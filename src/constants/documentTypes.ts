const SEAMANS_BOOK_DOCUMENT = 'seamans_book_document';
const PASSPORT_DOCUMENT = 'passport_document';
const SEAFARER_PHOTO = 'seafarer_profile_photo';
const BANK_ACCOUNT_DOCUMENT = 'bank_account_document';
const user_defined_document_type_id = 1;

const DELETE_DOCUMENT_URL = {
  [SEAFARER_PHOTO]: 'delete-seafarer-photo',
  [PASSPORT_DOCUMENT]: 'delete-passport-document',
  [SEAMANS_BOOK_DOCUMENT]: 'delete-seaman-book-document',
  [BANK_ACCOUNT_DOCUMENT]: 'delete-bank-account-document',
};

const findDocumentURL = (documentType) => {
  if (DELETE_DOCUMENT_URL[documentType]) return DELETE_DOCUMENT_URL[documentType];
  throw new Error('Document Type not found in DELETE_DOCUMENT_URL');
};

// Note: key in ALL_DOC_TYPES and DOC_FORM_IDS has to be the same
const ID_DOCUMENTS_TAB_DOC_TYPE_KEYS = ['PASSPORT', 'SEAMAN_BOOK', 'VISA', 'INDOS'];
const ID_DOCUMENTS_TAB_NEW_DOC_TYPE_KEYS = ['VISA', 'INDOS'];
const ENDORSEMENT_VERIFICATION_TAB_DOC_TYPE_KEYS = [
  'ENDORSEMENT',
  'DCE_VERIFICAITON',
  'VERIFICATION',
];
const OTHER_DOCUMENTS_TAB_DOC_TYPE_KEYS = [
  'MEDICAL',
  'DRUG_ALCOHOL_TEST',
  'CERTIFICATE_OF_COMPETENCY',
  'STCW',
  'EDUCATION',
  'APPRENTICESHIP',
  'TRAINING',
  'PRE_SEA_TRAINING',
  'CORRESPONDENCE_DETAILS',
  'OTHER_COURSE',
  'OTHER_DOCUMENT',
  'USER_DEFINED_DOCUMENT',
];
const ALL_DOC_TYPE_KEYS = [
  ...ID_DOCUMENTS_TAB_DOC_TYPE_KEYS,
  ...ENDORSEMENT_VERIFICATION_TAB_DOC_TYPE_KEYS,
  ...OTHER_DOCUMENTS_TAB_DOC_TYPE_KEYS,
];

const ALL_DOC_TYPES = {
  APPRENTICESHIP: 'apprenticeship',
  CERTIFICATE_OF_COMPETENCY: 'certificate_of_competency',
  DRUG_ALCOHOL_TEST: 'drug_alcohol_test',
  EDUCATION: 'education',
  ENDORSEMENT: 'endorsement',
  INDOS: 'indos',
  MEDICAL: 'medical',
  OTHER_COURSE: 'other_course',
  OTHER_DOCUMENT: 'other_document',
  PRE_SEA_TRAINING: 'pre_sea_training',
  STCW: 'stcw',
  TRAINING: 'training',
  VERIFICATION: 'verification',
  VISA: 'visa',
  PASSPORT: 'passport',
  SEAMAN_BOOK: 'seaman_book',
  DCE_VERIFICAITON: 'dce_verification',
  CORRESPONDENCE_DETAILS: 'correspondence_details',
  USER_DEFINED_DOCUMENT: 'user_defined_document',
  SUPPORTING_DOCUMENT: 'training_requirement_supporting_document',
  SUPERINTENDENT_REPORT: 'training_requirement_superintendent_report',
};

const DOC_FORM_IDS = {
  VISA: 1,
  INDOS: 2,
  ENDORSEMENT: 3,
  VERIFICATION: 4,
  MEDICAL: 5,
  DRUG_ALCOHOL_TEST: 6,
  CERTIFICATE_OF_COMPETENCY: 7,
  STCW: 8,
  EDUCATION: 9,
  APPRENTICESHIP: 10,
  TRAINING: 11,
  PRE_SEA_TRAINING: 12,
  OTHER_COURSE: 13,
  OTHER_DOCUMENT: 14,
  USER_DEFINED_DOCUMENT: 15,
  SEAMAN_BOOK: 16,
  PASSPORT: 17,
  DCE_VERIFICAITON: 18,
  CORRESPONDENCE_DETAILS: 19,
};

const DOC_FORM_ID_TO_DB_DOC_TYPE = {
  [DOC_FORM_IDS.VISA]: 'visa',
  [DOC_FORM_IDS.INDOS]: 'indos',
  [DOC_FORM_IDS.ENDORSEMENT]: 'endorsement',
  [DOC_FORM_IDS.VERIFICATION]: 'verification',
  [DOC_FORM_IDS.MEDICAL]: 'medical',
  [DOC_FORM_IDS.DRUG_ALCOHOL_TEST]: 'drug_alcohol_test',
  [DOC_FORM_IDS.CERTIFICATE_OF_COMPETENCY]: 'certificate_of_competency',
  [DOC_FORM_IDS.STCW]: 'stcw',
  [DOC_FORM_IDS.EDUCATION]: 'education',
  [DOC_FORM_IDS.APPRENTICESHIP]: 'apprenticeship',
  [DOC_FORM_IDS.TRAINING]: 'training',
  [DOC_FORM_IDS.PRE_SEA_TRAINING]: 'pre_sea_training',
  [DOC_FORM_IDS.OTHER_COURSE]: 'other_course',
  [DOC_FORM_IDS.OTHER_DOCUMENT]: 'other_document',
  [DOC_FORM_IDS.USER_DEFINED_DOCUMENT]: 'user_defined_document',
  [DOC_FORM_IDS.DCE_VERIFICAITON]: 'dce_verification',
  [DOC_FORM_IDS.CORRESPONDENCE_DETAILS]: 'correspondence_details',
};

const DOC_NAMES = {
  VISA: 'Visa',
  INDOS: 'Indian Database of Seafarers Number (INDos)',
  ENDORSEMENT: 'Endorsement',
  VERIFICATION: 'Document Verification',
  MEDICAL: 'Medical',
  DRUG_ALCOHOL_TEST: 'Drug and Alcohol Test',
  CERTIFICATE_OF_COMPETENCY: 'Certification of Competency',
  STCW: 'STCW',
  EDUCATION: 'Education',
  APPRENTICESHIP: 'Apprenticeship',
  TRAINING: 'Training',
  PRE_SEA_TRAINING: 'Pre Sea Training',
  OTHER_COURSE: 'Other Courses',
  OTHER_DOCUMENT: 'Other Documents',
  USER_DEFINED_DOCUMENT: 'User Defined Document',
  SEAMAN_BOOK: 'Seaman’s Book',
  PASSPORT: 'Passport',
  DCE_VERIFICAITON: 'DCE Verification',
  CORRESPONDENCE_DETAILS: 'Correspondence Details',
};

export {
  SEAMANS_BOOK_DOCUMENT,
  PASSPORT_DOCUMENT,
  SEAFARER_PHOTO,
  BANK_ACCOUNT_DOCUMENT,
  DELETE_DOCUMENT_URL,
  findDocumentURL,
  ID_DOCUMENTS_TAB_DOC_TYPE_KEYS,
  ID_DOCUMENTS_TAB_NEW_DOC_TYPE_KEYS,
  ENDORSEMENT_VERIFICATION_TAB_DOC_TYPE_KEYS,
  OTHER_DOCUMENTS_TAB_DOC_TYPE_KEYS,
  ALL_DOC_TYPE_KEYS,
  ALL_DOC_TYPES,
  DOC_FORM_ID_TO_DB_DOC_TYPE,
  DOC_NAMES,
  DOC_FORM_IDS,
  user_defined_document_type_id,
};
