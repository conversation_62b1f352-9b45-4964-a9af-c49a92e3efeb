export const HOURLY = 'Hourly';
export const MONTHLY = 'Monthly';
export const ALLOWED_PAYHEAD_CATEGORY_FOR_WAGES = [
  'Standard Earnings',
  'Fixed Deductions',
  'Accumulated Earnings',
];
export const WAGES_STATUS_PENDING = 'pending';
export const WAGES_STATUS_APPLIED = 'applied';
export const WAGES_STATUS_CANCELLED = 'cancelled';
export const WAGES_STATUS_OBSOLETE = 'obsolete';
export const WAGES_STATUS: any = {
  [WAGES_STATUS_PENDING]: {
    name: 'Pending',
    color: 'yellow',
  },
  [WAGES_STATUS_APPLIED]: {
    name: 'Applied',
    color: 'green',
  },
  [WAGES_STATUS_CANCELLED]: {
    name: 'Cancelled',
    color: 'red',
  },
};
export const PAYHEAD_TYPE_ALLOWANCE = 'Allowance';
export const DEFAULT_CURRENCY_UNIT = 'USD';
