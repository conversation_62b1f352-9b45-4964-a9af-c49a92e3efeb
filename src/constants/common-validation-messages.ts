const EXCEED_MAXIMUM_LENGTH = 'Exceed Maximum Length';
const VESSEL_NAME = 'Vessel is required';
const TRAINING_NEED = 'Training needs is required';
const RECOMMENDATION_INCOMPLETE_TRAINING_REQ =
  'Recommending this seafarer is not possible because the seafarer still has incomplete training requirements';
const RECOMMENDATION_INVESTIGATION_INCOMPLETE_TRAINING_REQ =
  'Recommending this seafarer is not possible because the seafarer still has incomplete investigation of training requirements';
const TRAINING_IMPARTED = 'Training imparted is required';

export {
  EXCEED_MAXIMUM_LENGTH,
  VESSEL_NAME,
  TRAINING_NEED,
  TRAINING_IMPARTED,
  R<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_INCOMPLETE_TRAINING_REQ,
  REC<PERSON><PERSON><PERSON><PERSON>ION_INVESTIGATION_INCOMPLETE_TRAINING_REQ,
};
