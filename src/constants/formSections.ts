const BASIC_LINK = 'basic';
const CONTACT_DETAILS_LINK = 'contact-details';
const GENERAL_DETAILS_LINK = 'general-details';
const BANK_ACCOUNT_LINK = 'bank-accounts';
const PERSONAL_PARTICULARS_LINK = 'personal-particulars';
const EXPERIENCE_LINK = 'experience';

const RANK_OFFICE_SECTION = 'RANK_OFFICE_SECTION';
const PERSONAL_DETAILS_SECTION = 'PERSONAL_DETAILS_SECTION';
const PASSPORT_SECTION = 'PASSPORT_SECTION';
const SEAMAN_BOOK_SECTION = 'SEAMAN_BOOK_SECTION';
const CONTACT_SECTION = 'CONTACT_SECTION';
const FAMILY_MEMBER_SECTION = 'FAMILY_MEMBER_SECTION';

export const DEFAULT_EMPTY_VALUE_FIELD = '- - -';

const BASIC_ERRORS = {
  rank_id: {
    message: 'Please select Rank (Basic)',
    link: BASIC_LINK,
    subSection: RANK_OFFICE_SECTION,
  },
  office_id: {
    message: 'Please select Reporting Office (Basic)',
    link: BASIC_LINK,
    subSection: RANK_OFFICE_SECTION,
  },
};

const PERSONAL_DETAILS_ERRORS = {
  first_name: {
    message: 'Please enter First Name (Basic / Personal Details)',
    link: BASIC_LINK,
    subSection: PERSONAL_DETAILS_SECTION,
  },
  last_name: {
    message: 'Please enter Last Name (Basic / Personal Details)',
    link: BASIC_LINK,
    subSection: PERSONAL_DETAILS_SECTION,
  },
  gender: {
    message: 'Please select Gender (Basic / Personal Details)',
    link: BASIC_LINK,
    subSection: PERSONAL_DETAILS_SECTION,
  },
  date_of_birth: {
    message: 'Please enter Date of Birth (Basic / Personal Details)',
    link: BASIC_LINK,
    subSection: PERSONAL_DETAILS_SECTION,
  },
  place_of_birth: {
    message: 'Please enter Place of Birth (Basic / Personal Details)',
    link: BASIC_LINK,
    subSection: PERSONAL_DETAILS_SECTION,
  },
  country_of_birth_id: {
    message: 'Please select Country of Birth (Basic / Personal Details)',
    link: BASIC_LINK,
    subSection: PERSONAL_DETAILS_SECTION,
  },
  nationality_id: {
    message: 'Please select Nationality (Basic / Personal Details)',
    link: BASIC_LINK,
    subSection: PERSONAL_DETAILS_SECTION,
  },
};

const PASSPORT_ERRORS = {
  passport_number: {
    message: 'Please enter a valid Passport Number (Basic / Passport Details)',
    link: BASIC_LINK,
    subSection: PASSPORT_SECTION,
  },
  passport_file: {
    message: 'Please provide a copy of Passport (Basic / Passport Details)',
    link: BASIC_LINK,
    subSection: PASSPORT_SECTION,
  },
  passport_country_id: {
    message: 'Please enter Country of Issue (Basic / Passport Details)',
    link: BASIC_LINK,
    subSection: PASSPORT_SECTION,
  },
  passport_date_of_expiry: {
    message: 'Please enter Date of Expiry (Basic / Passport Details)',
    link: BASIC_LINK,
    subSection: PASSPORT_SECTION,
  },
  passport_date_of_issue: {
    message: 'Please enter Date of Issue (Basic / Passport Details)',
    link: BASIC_LINK,
  },
  passport_place_of_issue: {
    message: 'Please enter Place of Issue (Basic / Passport Details)',
    link: BASIC_LINK,
    subSection: PASSPORT_SECTION,
  },
};

const SEAMAN_BOOK_ERRORS = {
  seaman_book_country_id: {
    message: "Please select Country for Seaman's Book (Basic / Seaman's Book)",
    link: BASIC_LINK,
    subSection: SEAMAN_BOOK_SECTION,
  },
  seaman_book_number: {
    message: "Please enter a valid Seaman Book Number (Basic / Seaman's Book)",
    link: BASIC_LINK,
    subSection: SEAMAN_BOOK_SECTION,
  },
  seaman_book_file: {
    message: "Please provide a copy of Seaman's Book (Basic / Seaman's Book)",
    link: BASIC_LINK,
    subSection: SEAMAN_BOOK_SECTION,
  },
  seaman_book_port_of_issue: {
    message: "Please enter Port of Issue (Basic / Seaman's Book)",
    link: BASIC_LINK,
    subSection: SEAMAN_BOOK_SECTION,
  },
  seaman_book_date_of_expiry: {
    message: "Please enter Date of Expiry (Basic / Seaman's Book)",
    link: BASIC_LINK,
    subSection: SEAMAN_BOOK_SECTION,
  },
  seaman_book_date_of_issue: {
    message: "Please enter Date of Issue (Basic / Seaman's Book)",
    link: BASIC_LINK,
    subSection: SEAMAN_BOOK_SECTION,
  },
  seaman_book_place_of_issue: {
    message: "Please enter Place of Issue (Basic / Seaman's Book)",
    link: BASIC_LINK,
    subSection: SEAMAN_BOOK_SECTION,
  },
};

const CONTACT_ERRORS = {
  telephone_numbers: {
    message: 'Please enter a valid telephone number (Contact Details / Contact Details)',
    link: CONTACT_DETAILS_LINK,
    subSection: CONTACT_SECTION,
  },
  mobile_numbers: {
    message: 'Please enter a valid mobile number (Contact Details / Contact Details)',
    link: CONTACT_DETAILS_LINK,
    subSection: CONTACT_SECTION,
  },
  email_addresses: {
    message: 'Please enter a valid email address (Contact Details / Contact Details)',
    link: CONTACT_DETAILS_LINK,
    subSection: CONTACT_SECTION,
  },
};

const BANK_ACCOUNT_ERRORS = {
  account_holder_first_name: {
    message: 'Please enter Account Holder First Name (Bank Accounts / Bank Account)',
    link: BANK_ACCOUNT_LINK,
  },
  account_holder_last_name: {
    message: 'Please enter Account Holder Last Name (Bank Accounts / Bank Account)',
    link: BANK_ACCOUNT_LINK,
  },
  account_holder_date_of_birth: {
    message: 'Please enter Account Holder Date of Birth (Bank Accounts / Bank Account)',
    link: BANK_ACCOUNT_LINK,
  },
  account_holder_gender: {
    message: 'Please enter Account Holder Gender (Bank Accounts / Bank Account)',
    link: BANK_ACCOUNT_LINK,
  },
  account_holder_nationality_id: {
    message: 'Please select Account Holder Nationality (Bank Accounts / Bank Account)',
    link: BANK_ACCOUNT_LINK,
  },
  relationship_with_beneficiary: {
    message: 'Please enter Relationship with Beneficiary (Bank Accounts / Bank Account)',
    link: BANK_ACCOUNT_LINK,
  },
  // Bank Account / Bank Details
  bank_name: {
    message: 'Please enter Bank Name (Bank Accounts / Bank Account Details)',
    link: BANK_ACCOUNT_LINK,
  },
  bank_account_number: {
    message: 'Please enter Bank Account Number (Bank Accounts / Bank Account Details)',
    link: BANK_ACCOUNT_LINK,
  },
  bank_address_country_id: {
    message: 'Please select Bank Country (Bank Accounts / Bank Account Details)',
    link: BANK_ACCOUNT_LINK,
  },
  bank_address_address1: {
    message: 'Please enter State / Province / District (Bank Accounts / Bank Account Details)',
    link: BANK_ACCOUNT_LINK,
  },
  bank_address_address2: {
    message: 'Please enter Village / Town / City (Bank Accounts / Bank Account Details)',
    link: BANK_ACCOUNT_LINK,
  },
  bank_address_address3: {
    message: 'Please enter Building Name / Estate (Bank Accounts / Bank Account Details)',
    link: BANK_ACCOUNT_LINK,
  },
  bank_address_address4: {
    message: 'Please enter Flat / Room / House Number/ Street (Bank Accounts / Bank Account Details)',
    link: BANK_ACCOUNT_LINK,
  },
  ifsc_number: {
    message: 'Please enter IFSC Number (Bank Accounts / Bank Account Details)',
    link: BANK_ACCOUNT_LINK,
  },
  swift_code: {
    message: 'Please enter SWIFT Code (Bank Accounts / Bank Account Details)',
    link: BANK_ACCOUNT_LINK,
  },
  bank_account_file: {
    message: 'Please provide a copy of Bank Account (Bank Accounts / Bank Account Details)',
    link: BASIC_LINK,
  },
  fcnr_months: {
    message: 'Please select months (Bank Accounts / Bank Account Details)',
    link: BANK_ACCOUNT_LINK,
  },
};

const FAMILY_MEMBER_ERRORS = {
  family_member_telephone: {
    message: 'Next of Kin Invalid Telephone Number',
    link: PERSONAL_PARTICULARS_LINK,
    subSection: FAMILY_MEMBER_SECTION,
  },
  family_member_mobilephone: {
    message: 'Next of Kin Invalid Mobile Number',
    link: PERSONAL_PARTICULARS_LINK,
    subSection: FAMILY_MEMBER_SECTION,
  },
  family_member_email: {
    message: 'Next of Kin Invalid email',
    link: PERSONAL_PARTICULARS_LINK,
    subSection: FAMILY_MEMBER_SECTION,
  },
};

const ERROR_MAPPINGS = {
  ...BASIC_ERRORS,
  ...PERSONAL_DETAILS_ERRORS,
  ...PASSPORT_ERRORS,
  ...SEAMAN_BOOK_ERRORS,
  ...CONTACT_ERRORS,
  ...BANK_ACCOUNT_ERRORS,
  ...FAMILY_MEMBER_ERRORS,
  // file: 'Please provide a copy',
};

const RANK_VALUES = {
  SUPY : 'SUPY'
}

export {
  BASIC_LINK,
  CONTACT_DETAILS_LINK,
  GENERAL_DETAILS_LINK,
  BANK_ACCOUNT_LINK,
  PERSONAL_PARTICULARS_LINK,
  EXPERIENCE_LINK,
  // For error mappings
  ERROR_MAPPINGS,
  // For scrolling section mapping purpose
  RANK_OFFICE_SECTION,
  PERSONAL_DETAILS_SECTION,
  PASSPORT_SECTION,
  SEAMAN_BOOK_SECTION,
  CONTACT_SECTION,
  FAMILY_MEMBER_SECTION,
  RANK_VALUES
};
