// common labels for the form
const LABEL_FILE_SIZE_AND_EXTENSION = '(pdf, jpg, png, max size 12MB)';
const LABEL_FILE_SIZE_5MB_AND_EXTENSION = '(pdf, jpg, png, max size 5MB)'; // should be removed after all document can support 12MB

export const COMMON_LABELS = {
  LABEL_FILE_SIZE_AND_EXTENSION,
  LABEL_FILE_SIZE_5MB_AND_EXTENSION,
};

// common messages of the form validation and pop up warnings/alerts
const MESSAGE_FILE_SIZE_EXCEED = 'File Size exceeded 12MB';
const MESSAGE_FILE_SIZE_EXCEED_5MB = 'File Size exceeded 5MB'; // should be removed after all document can support 12MB
const MESSAGE_FILE_EXTENSION_NOT_SUPPORT = 'File Extension is not supported';

export const COMMON_MESSAGES = {
  MESSAGE_FILE_SIZE_EXCEED,
  MESSAGE_FILE_SIZE_EXCEED_5MB,
  MESSAGE_FILE_EXTENSION_NOT_SUPPORT,
};
