/**
 * Using data-testid in Enzyme has some a little bit of gotchas.
 *
 * Here are some examples
 *
 * How to find data-testid using Enzyme?
 *
 * 1. wrapper.find(`[data-testid='tesdid-name']`).hostNodes();
 *
 * Some reference
 *
 * https://github.com/enzymejs/enzyme/issues/836
 * https://medium.com/trabe/use-enzyme-jest-snapshots-to-test-data-testid-attributes-in-react-components-769ab37442a9
 */

// Form Steps
export const BASIC_TAB = 'basic-tab';
export const CONTACT_DETAILS_TAB = 'contact-details-tab';
export const GENERAL_DETAILS_TAB = 'general-details-tab';
export const BANK_ACCOUNT_TAB = 'bank-account-tab';
export const PERSONAL_PARTICULARS_TAB = 'personal-particulars-tab';
export const EXPERIENCE_TAB = 'experience-tab';

// Basic Step
export const RANK_FIELD = 'form-rank-field';
export const REPORTING_OFFICE_FIELD = 'form-reporting-office-field';

// Basic Step Personal Details
export const FIRST_NAME_FIELD = 'form-first-name-field';
export const MIDDLE_NAME_FIELD = 'form-middle-name-field';
export const LAST_NAME_FIELD = 'form-last-name-field';

export const DATE_OF_BIRTH_FIELD = 'form-date-of-birth-field';
export const GENDER_FIELD = 'form-gender-field';
export const PLACE_OF_BIRTH_FIELD = 'form-place-of-birth-field';

export const COUNTRY_OF_BIRTH_FIELD = 'form-country-of-birth-field';
export const NATIONALITY_FIELD = 'form-nationality-field';

export const BASIC_FORM_TESTID_FIELDS = [
  FIRST_NAME_FIELD,
  MIDDLE_NAME_FIELD,
  LAST_NAME_FIELD,
  DATE_OF_BIRTH_FIELD,
  GENDER_FIELD,
  PLACE_OF_BIRTH_FIELD,
  COUNTRY_OF_BIRTH_FIELD,
  NATIONALITY_FIELD,
];
