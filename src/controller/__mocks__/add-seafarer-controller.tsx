import { getMockedSeafarerResponse } from '../../test/resources/seafarer-mock-data';
import dropdowndata from '../../test/resources/drop-down-data.json';
import portsdata from '../../test/resources/ports-map.json';
import bankNameResponse from '../../test/resources/bank-name-data.json';

export const mockGetEmptySeafarer = jest.fn().mockImplementation(() =>
  Promise.resolve({
    data: {},
    status: 200,
  }),
);

export const mockLoadSeafarer = jest
  .fn()
  .mockImplementation(() => Promise.resolve(getMockedSeafarerResponse()));

export const mockLoadDropdownData = jest
  .fn()
  .mockImplementation(() => Promise.resolve(dropdowndata));

export const mockLoadSeafarerReportingOfficeDropDownData = jest
  .fn()
  .mockImplementation(() => Promise.resolve(dropdowndata.offices));
export const mockLoadBankNameData = jest
  .fn()
  .mockImplementation(() => Promise.resolve(bankNameResponse));

export const mockLoadManningAgentDropDownData = jest
  .fn()
  .mockImplementation(() => Promise.resolve(dropdowndata.manningAgents));

export const mockLoadPortsOfIssueForSeamanBookMap = jest
  .fn()
  .mockImplementation(() => Promise.resolve(portsdata));

const mock = jest.fn().mockImplementation(() => {
  return {
    getEmptySeafarer: mockGetEmptySeafarer,
    loadSeafarer: mockLoadSeafarer,
    loadDropDownData: mockLoadDropdownData,
    loadSeafarerReportingOfficeDropDownData: mockLoadSeafarerReportingOfficeDropDownData,
    loadManningAgentDropDownData: mockLoadManningAgentDropDownData,
    loadPortsOfIssueForSeamanBookMap: mockLoadPortsOfIssueForSeamanBookMap,
    loadBankNames: mockLoadBankNameData,
  };
});

export default mock;
