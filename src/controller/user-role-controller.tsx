import { screeningApprovalGroups } from '../model/constants';

const COMPLIANCE_EMPLOYEE_GROUP = '/Department/Compliance/Employee';
const COMPLIANCE_SUPERVISOR_GROUP = '/Department/Compliance/Supervisor';

const EXTERNAL_SCREENING_USERS_EMPLOYEE_GROUP = '/Department/ExternalScreeningUsers/Employee';
const EXTERNAL_SCREENING_USERS_SUPERVISOR_GROUP = '/Department/ExternalScreeningUsers/Supervisor';
const FPD_GROUPS = [
  '/Department/Fleet Personnel/SeniorManagement',
  '/Department/Fleet Personnel/SeafarerManagers',
  '/Department/Fleet Personnel/Management',
  '/Department/Fleet Personnel/ManningAgents',
];

const ROLE = {
  VIEW_SEAFARER: 'seafarer|view|general',
  VIEW_SEAFARER_CONTACT_DETAILS: 'seafarer|view|contact',
  CASE_REPORT_UPLOAD: 'seafarer|screening|case-report-upload',
  DOCUMENTS_UPLOAD: 'seafarer|screening|documents-upload',
  SCREENING_VIEW: 'seafarer|screening|view',
  SCREENING_APPROVE: 'seafarer|screening|final-approve',
  ADD_SEAFARER: 'seafarer|add',
  EDIT_SEAFARER: 'seafarer|edit|basic',
  REAPPLY_SCREENING: 'seafarer|screening|reapply',
  FORWARD_SCREENING: 'seafarer|screening|forward',
  EDIT_PERSONAL_DETAILS: 'seafarer|edit|personal-details',
  EDIT_PASSPORT: 'seafarer|edit|passport',
  EDIT_SEAMANS_BOOK: 'seafarer|edit|seamans-book',
  EDIT_GENERAL_DETAILS: 'seafarer|edit|general-details',
  EDIT_RANK: 'seafarer|edit|rank',
  EDIT_REPORTING_OFFICE: 'seafarer|edit|reporting-office',
  EDIT_PERSONAL_PARTICULARS: 'seafarer|edit|personal-particulars',
  EDIT_CONTACT_DETAILS: 'seafarer|edit|contact-details',
  EDIT_DUPLICATE_HKID: 'seafarer|edit|duplicate-hkid',
  VIEW_BANK_ACCOUNT: 'seafarer|view|bank-account',
  EDIT_BANK_ACCOUNT: 'seafarer|edit|bank-account',
  HIDDEN_PERSONAL_PARTICULARS: 'seafarer|hidden|personal-particulars',
  VIEW_FAMILY_MEMBERS: 'seafarer|view|family-members',
  EDIT_STATUS: 'seafarer|edit|status',
  EDIT_STATUS_BLACKLIST: 'seafarer|edit|status|blacklisted',
  EDIT_STATUS_BLACKLISTED_TO_ON_LEAVE: 'seafarer|edit|status|blacklisted-to-on-leave',
  SEAFARER_USER_MANAGE: 'seafarer|user|manage',
  EDIT_SEAFARER_DOCUMENT: 'seafarer|edit|document',
  EDIT_SEAFARER_EXPERIENCE: 'seafarer|edit|experience',
  VIEW_CREW_LIST: 'seafarer|view|crew-list',
  EDIT_SEAFARER_EXPERIENCE_VESSEL: 'seafarer|edit|experience-vessel',
  EDIT_SEAFARER_WAGES: 'seafarer|edit|wages',
  VIEW_REPORT_MODELLER: 'sf|rt|md|v',
  EDIT_REPORT_MODELLER: 'sf|rt|md|e',
  EDIT_TRAVEL: 'sf|send-pii',
  EDIT_DAILY_NEWS: 'news|e',
  RECOMMEND: 'sf|r2',
  RECOMMEND_APPROVAL_TOP_RANKS: 'sf|r2|at',
  RECOMMEND_APPROVAL_LOW_RANKS: 'sf|r2|al',
  RECOMMEND_DEVIATION_APPROVAL_TOP_RANKS: 'sf|r2|atwd',
  RECOMMEND_DEVIATION_APPROVAL_LOW_RANKS: 'sf|r2|alwd',
  VIEW_REPORT_APPROVAL: 'sf|rt|ap|v',
  VIEW_REPORT_SIGNED_ON: 'sf|rt|so|v',
  VIEW_REPORT_DG_SHIPPING: 'sf|rt|dg|v',
  EXPORT_TO_EXCEL: 'seafarer|export|excel',
  VIEW_SUPT_APPRAISAL: 'survey|view|supt-appraisal',
  VIEW_MASTER_APPRAISAL: 'seafarer|view|master-appraisal',
  VIEW_DEBRIEFING: 'survey|view|debriefing',
  REPLACE_CREW_LIST: 'seafarer|replace|crew-list',
  GEN_APPOINTMENT_LETTER: 'sf|appt|gen',
  DG_SHIPPING_EXPORT_TO_EXCEL: 'sf|rt|dg|e',
  EDIT_CREW_PLANNER_SEAFARER: 'sf|cp|e',
  EDIT_CREW_PLANNER_VESSEL: 'sf|pm|e',
  VIEW_TRAINING: 'sf|tr|v',
  ADD_TRAINING: 'sf|tr|c',
  EDIT_TRAINING: 'sf|tr|e',
  EDIT_SUPT_TRAINING: 'sf|tr|es',
  VESSEL_VIEW_DRY: 'vessel|view|dry',
  VESSEL_VIEW_TANKER: 'vessel|view|tanker',
  CREW_PLANNER_MANAGE: 'sf|cp|m',
  VIEW_SEAFARER_TRAINING: 'seafarer|view|training',
  EDIT_SEAFARER_TRAINING: 'seafarer|edit|training',
  VIEW_CREW_PLANNER_SEAFARER: 'sf|cp|pm|viewonly',
  SEAFARER_PREPARATION: 'sf|r4',
  ROLE_SEAFARER_SUPER_USER: 'sf|su',
  SEAFARER_R4_VIEW: 'sf|r4|v',
};

class UserRoleController {
  async getConfig(kc) {
    return {
      seafarer: {
        screening: {
          caseReportUpload: kc.realmAccess.roles.includes(ROLE.CASE_REPORT_UPLOAD),
          documentsUpload: kc.realmAccess.roles.includes(ROLE.DOCUMENTS_UPLOAD),
          view: kc.realmAccess.roles.includes(ROLE.SCREENING_VIEW),
          reapply: kc.realmAccess.roles.includes(ROLE.REAPPLY_SCREENING),
          forward: kc.realmAccess.roles.includes(ROLE.FORWARD_SCREENING),
          approve: kc.realmAccess.roles.includes(ROLE.SCREENING_APPROVE),
        },
        addSeafarer: kc.realmAccess.roles.includes(ROLE.ADD_SEAFARER),
        editSeafarer: kc.realmAccess.roles.includes(ROLE.EDIT_SEAFARER),
        replaceCrewList: kc.realmAccess.roles.includes(ROLE.REPLACE_CREW_LIST),
        add: {
          training: kc.realmAccess.roles.includes(ROLE.ADD_TRAINING),
        },
        edit: {
          personalDetails: kc.realmAccess.roles.includes(ROLE.EDIT_PERSONAL_DETAILS),
          passport: kc.realmAccess.roles.includes(ROLE.EDIT_PASSPORT),
          seamansBook: kc.realmAccess.roles.includes(ROLE.EDIT_SEAMANS_BOOK),
          generalDetails: kc.realmAccess.roles.includes(ROLE.EDIT_GENERAL_DETAILS),
          rank: kc.realmAccess.roles.includes(ROLE.EDIT_RANK),
          reportingOffice: kc.realmAccess.roles.includes(ROLE.EDIT_REPORTING_OFFICE),
          personalParticulars: kc.realmAccess.roles.includes(ROLE.EDIT_PERSONAL_PARTICULARS),
          contactDetails: kc.realmAccess.roles.includes(ROLE.EDIT_CONTACT_DETAILS),
          duplicateHKID: kc.realmAccess.roles.includes(ROLE.EDIT_DUPLICATE_HKID),
          status: kc.realmAccess.roles.includes(ROLE.EDIT_STATUS),
          statusBlacklistedToOnLeave: kc.realmAccess.roles.includes(
            ROLE.EDIT_STATUS_BLACKLISTED_TO_ON_LEAVE,
          ),
          statusBlacklist: kc.realmAccess.roles.includes(ROLE.EDIT_STATUS_BLACKLIST),
          bankAccount: kc.realmAccess.roles.includes(ROLE.EDIT_BANK_ACCOUNT),
          seafarerDocument: kc.realmAccess.roles.includes(ROLE.EDIT_SEAFARER_DOCUMENT),
          seafarerExperience: kc.realmAccess.roles.includes(ROLE.EDIT_SEAFARER_EXPERIENCE),
          seafarerExperienceVessel: kc.realmAccess.roles.includes(
            ROLE.EDIT_SEAFARER_EXPERIENCE_VESSEL,
          ),
          wages: kc.realmAccess.roles.includes(ROLE.EDIT_SEAFARER_WAGES),
          reportModeller: kc.realmAccess.roles.includes(ROLE.EDIT_REPORT_MODELLER),
          travel: kc.realmAccess.roles.includes(ROLE.EDIT_TRAVEL),
          dailyNews: kc.realmAccess.roles.includes(ROLE.EDIT_DAILY_NEWS),
          recommendation: kc.realmAccess.roles.includes(ROLE.RECOMMEND),
          recommendationApproval: kc.realmAccess.roles.includes(ROLE.RECOMMEND_APPROVAL_TOP_RANKS),
          recommendationApprovalExceptTopRanks: kc.realmAccess.roles.includes(
            ROLE.RECOMMEND_APPROVAL_LOW_RANKS,
          ),
          recommendationDeviationApproval: kc.realmAccess.roles.includes(
            ROLE.RECOMMEND_DEVIATION_APPROVAL_TOP_RANKS,
          ),
          recommendationDeviationApprovalExceptTopRanks: kc.realmAccess.roles.includes(
            ROLE.RECOMMEND_DEVIATION_APPROVAL_LOW_RANKS,
          ),
          seafarerExportToExcel: kc.realmAccess.roles.includes(ROLE.EXPORT_TO_EXCEL),
          seafarerDgShippingExportToExcel: kc.realmAccess.roles.includes(
            ROLE.DG_SHIPPING_EXPORT_TO_EXCEL,
          ),
          training: kc.realmAccess.roles.includes(ROLE.EDIT_TRAINING),
          suptTraining: kc.realmAccess.roles.includes(ROLE.EDIT_SUPT_TRAINING),
          crewPlannerSeafarer: kc.realmAccess.roles.includes(ROLE.EDIT_CREW_PLANNER_SEAFARER),
          crewPlannerVessel: kc.realmAccess.roles.includes(ROLE.EDIT_CREW_PLANNER_VESSEL),
          contractEndDate: kc.realmAccess.roles.includes(ROLE.SEAFARER_PREPARATION),
          seafarerSuperUser: kc.realmAccess.roles.includes(ROLE.ROLE_SEAFARER_SUPER_USER),
        },
        generate: {
          appointmentLetter: kc.realmAccess.roles.includes(ROLE.GEN_APPOINTMENT_LETTER),
        },
        hidden: {
          bankAccount: kc.realmAccess.roles.includes(ROLE.HIDDEN_BANK_ACCOUNT),
          personalParticulars: kc.realmAccess.roles.includes(ROLE.HIDDEN_PERSONAL_PARTICULARS),
          // contactDetails: !kc.realmAccess.roles.includes(ROLE.VIEW_SEAFARER_CONTACT_DETAILS),
          familyMembers: !kc.realmAccess.roles.includes(ROLE.VIEW_FAMILY_MEMBERS),
        },
        view: {
          general: kc.realmAccess.roles.includes(ROLE.VIEW_SEAFARER),
          bankAccount: kc.realmAccess.roles.includes(ROLE.VIEW_BANK_ACCOUNT),
          crewList: kc.realmAccess.roles.includes(ROLE.VIEW_CREW_LIST),
          reportModeller: kc.realmAccess.roles.includes(ROLE.VIEW_REPORT_MODELLER),
          reportApproval: kc.realmAccess.roles.includes(ROLE.VIEW_REPORT_APPROVAL),
          reportSignedOn: kc.realmAccess.roles.includes(ROLE.VIEW_REPORT_SIGNED_ON),
          reportSignedOff: kc.realmAccess.roles.includes(ROLE.SEAFARER_PREPARATION),
          reportDgShipping: kc.realmAccess.roles.includes(ROLE.VIEW_REPORT_DG_SHIPPING),
          viewSuptAppraisal: kc.realmAccess.roles.includes(ROLE.VIEW_SUPT_APPRAISAL),
          viewMasterAppraisal: kc.realmAccess.roles.includes(ROLE.VIEW_MASTER_APPRAISAL),
          viewDebriefing: kc.realmAccess.roles.includes(ROLE.VIEW_DEBRIEFING),
          crewPlannerSeafarer: kc.realmAccess.roles.includes(ROLE.VIEW_CREW_PLANNER_SEAFARER),
          viewTraining: kc.realmAccess.roles.includes(ROLE.VIEW_TRAINING),
          viewContractDetails: kc.realmAccess.roles.includes(ROLE.SEAFARER_R4_VIEW),
        },
        user: {
          manage: kc.realmAccess.roles.includes(ROLE.SEAFARER_USER_MANAGE),
        },
        manage: {
          crewPlanner: kc.realmAccess.roles.includes(ROLE.CREW_PLANNER_MANAGE),
        },
        training: {
          edit: kc.realmAccess.roles.includes(ROLE.EDIT_SEAFARER_TRAINING),
          view: kc.realmAccess.roles.includes(ROLE.VIEW_SEAFARER_TRAINING),
        },
      },
      vessel: {
        view: {
          dryVessel: kc.realmAccess.roles.includes(ROLE.VESSEL_VIEW_DRY),
          tankerVessel: kc.realmAccess.roles.includes(ROLE.VESSEL_VIEW_TANKER),
        },
      },
      screeningGroup: this.getScreeningGroup(kc.tokenParsed.group),
      shipPartyId: kc.idTokenParsed.ship_party_id,
      shipPartyType: kc.idTokenParsed.ship_party_type ?? '',
      techGroup: this.getTechGroups(kc.tokenParsed.group),
    };
  }

  getScreeningGroup = (kcGroups) => {
    const isFpdGroupEmployee = () => FPD_GROUPS.some((group) => kcGroups.indexOf(group) !== -1);

    if (kcGroups.includes(COMPLIANCE_EMPLOYEE_GROUP))
      return screeningApprovalGroups.COMPLIANCE_EMPLOYEE;
    if (kcGroups.includes(COMPLIANCE_SUPERVISOR_GROUP))
      return screeningApprovalGroups.COMPLIANCE_SUPERVISOR;
    if (kcGroups.includes(EXTERNAL_SCREENING_USERS_EMPLOYEE_GROUP))
      return screeningApprovalGroups.COMPLIANCE_EMPLOYEE;
    if (kcGroups.includes(EXTERNAL_SCREENING_USERS_SUPERVISOR_GROUP))
      return screeningApprovalGroups.COMPLIANCE_SUPERVISOR;
    if (isFpdGroupEmployee()) return screeningApprovalGroups.FPD_MANNING;
    return null;
  };

  getTechGroups = (kcGroups) => {
    const techGroups = kcGroups
      .filter((group) => group.startsWith('/Tech Group/'))
      .flatMap((techGroup) => techGroup.split('/Tech Group/').filter(Boolean));
    return techGroups.length > 0 ? techGroups : null;
  };
}

export default UserRoleController;
