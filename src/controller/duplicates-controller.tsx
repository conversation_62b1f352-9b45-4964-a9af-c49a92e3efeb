import seafarerService from '../service/seafarer-service';

class DuplicatesController {
  pasportDuplicates = {};
  seamansBookDuplicates = {};
  personalDetailsDuplicates = {};

  // Passport search
  async loadSeafarersByPassportNumber(passportNumber, seafarer) {
    const cachedValue = this.getCachedPassportsResult(passportNumber);
    if (cachedValue) {
      return cachedValue;
    }

    const result = await seafarerService.getPassportDuplicates(passportNumber);
    if (result && result.data.results.length > 0) {
      //NOTE : This condition is for allowing duplicate seafarers to enter same passport  number
      if (
        seafarer?.parent_hkid === result.data.results[0].hkid ||
        seafarer?.hkid === result.data.results[0].parent_hkid ||
        seafarer?.parent_hkid === result.data.results[0].parent_hkid
      ) {
        return [];
      } else {
        const duplicateData = { ...result.data.results[0], number: passportNumber };
        this.pasportDuplicates[passportNumber] = [duplicateData];
        return [duplicateData];
      }
    }

    return [];
  }

  getCachedPassportsResult(passportNumber) {
    const cachedValue = this.pasportDuplicates[passportNumber];
    if (cachedValue) {
      return cachedValue;
    }
    return null;
  }

  // Seaman's Book search
  async loadSeafarersBySeamansBookNumber(seamansBookNumber, seafarer) {
    const cachedValue = this.getCachedSeamansBooksResult(seamansBookNumber);
    if (cachedValue) {
      return cachedValue;
    }

    const result = await seafarerService.getSeamansBookDuplicates(seamansBookNumber);
    if (result && result.data.results.length > 0) {
      //NOTE : This condition is for allowing duplicate seafarers to enter same seaman book number
      if (
        seafarer?.parent_hkid === result.data.results[0].hkid ||
        seafarer?.hkid === result.data.results[0].parent_hkid ||
        seafarer?.parent_hkid === result.data.results[0].parent_hkid
      ) {
        return [];
      } else {
        const duplicateData = { ...result.data.results[0], number: seamansBookNumber };
        this.seamansBookDuplicates[seamansBookNumber] = [duplicateData];
        return [duplicateData];
      }
    }

    return [];
  }

  getCachedSeamansBooksResult(seamansBookNumber) {
    const cachedValue = this.seamansBookDuplicates[seamansBookNumber];
    if (cachedValue) {
      return cachedValue;
    }
    return null;
  }

  // Personal Details search
  async loadSeafarersByPersonalDetails(personalDetails, seafarer) {
    if (!this.isPersonalDetailsValid(personalDetails)) {
      return null;
    }

    const cachedValue = this.getCachedPersonalDetailsResult(personalDetails);
    if (cachedValue) {
      return cachedValue;
    }

    const result = await seafarerService.getPersonalDetailsDuplicates(personalDetails);
    if (result && result.data.results.length > 0) {
      if (
        seafarer?.parent_hkid === result.data.results[0].hkid ||
        seafarer?.hkid === result.data.results[0].parent_hkid ||
        seafarer?.parent_hkid === result.data.results[0].parent_hkid
      ) {
        return [];
      } else {
        const key = this.personalDetailsAsString(personalDetails);
        this.personalDetailsDuplicates[key] = result.data.results;
        return result.data.results;
      }
    }

    return [];
  }

  getCachedPersonalDetailsResult(personalDetails) {
    const key = this.personalDetailsAsString(personalDetails);
    const cachedValue = this.personalDetailsDuplicates[key];
    if (cachedValue) {
      return cachedValue;
    }
    return null;
  }

  personalDetailsAsString(personalDetails) {
    return JSON.stringify(personalDetails);
  }

  isPersonalDetailsValid(personalDetails) {
    const isFirstNameEmpty = this.isEmpty(personalDetails.first_name);
    const isLastNameEmpty = this.isEmpty(personalDetails.last_name);
    const isBirthDayEmpty = this.isEmpty(personalDetails.date_of_birth);

    if (isFirstNameEmpty || isLastNameEmpty || isBirthDayEmpty) {
      return false;
    }

    return true;
  }

  isEmpty(string) {
    const str = string ? string.trim() : null;
    return !str || 0 === str.length;
  }
}

export default DuplicatesController;
