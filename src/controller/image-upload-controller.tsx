import seafarerService from '../service/seafarer-service';
import download from 'downloadjs';
import { ALL_DOC_TYPES } from '../constants/documentTypes';

class ImageUploadController {
  async uploadPassportImage(passport) {
    const file = passport?.file ?? undefined;
    if (file === undefined) {
      return undefined;
    }
    const passportId = passport.id ?? undefined;
    if (passportId === undefined) {
      return undefined;
    }

    let result = undefined;

    try {
      const formData = new FormData();
      formData.append('passport', file);
      const response = await seafarerService.uploadPassportImage(formData, passportId);
      result = response && response.length > 0 ? response[0] : undefined;
    } catch (error) {
      console.error(error);
    }

    return result;
  }

  async downloadPassportImage(document) {
    try {
      const response = await seafarerService.downloadPassportImage(document.id, document.mime);
      return response;
    } catch (error) {
      console.error(error);
    }
    return undefined;
  }

  async deleteDocument(id, documentType) {
    try {
      const response = await seafarerService.deleteDocument(id, documentType);
      return response.data;
    } catch (error) {
      console.error(error);
    }
    return undefined;
  }

  async uploadSeamansBookImage(seamanBook) {
    const file = seamanBook?.file ?? undefined;
    if (file === undefined) {
      return undefined;
    }
    const seamanBookId = seamanBook.id ?? undefined;
    if (seamanBookId === undefined) {
      return undefined;
    }

    let result = undefined;

    try {
      const formData = new FormData();
      formData.append('seaman_book', file);
      const response = await seafarerService.uploadSeamanBookImage(formData, seamanBookId);
      result = response && response.length > 0 ? response[0] : undefined;
    } catch (error) {
      console.error(error);
    }

    return result;
  }

  async downloadSeamanBookImage(document) {
    try {
      const response = await seafarerService.downloadSeamanBookImage(document.id, document.mime);
      return response;
    } catch (error) {
      return error;
    }
  }

  async uploadSeafarerImage(photo) {
    let result = undefined;

    try {
      const formData = new FormData();
      formData.append('photo', photo);
      const response = await seafarerService.uploadSeafarerImage(
        formData,
        photo.seafarer_person_id,
      );
      result = response && response.length > 0 ? response[0] : undefined;
    } catch (error) {
      console.error(error);
    }

    return result;
  }

  arrayBufferToBase64(buffer) {
    let binary = '';
    const bytes = new Uint8Array(buffer);
    const len = bytes.byteLength;
    for (let i = 0; i < len; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return window.btoa(binary);
  }

  async downloadSeafarerImage(document) {
    try {
      const response = await seafarerService.downloadSeafarerImage(document.id, document.mime);
      return response.data;
    } catch (error) {
      console.error(error);
    }
    return undefined;
  }

  async uploadBankAccountImage(bankAccount) {
    const file = bankAccount?.file;
    if (!file || !bankAccount.id) {
      return null;
    }
    let result = undefined;

    try {
      const formData = new FormData();
      formData.append('bank_account', file);
      const response = await seafarerService.uploadBankAccountImage(formData, bankAccount.id);
      result = response && response.length > 0 ? response[0] : undefined;
    } catch (error) {
      console.error(error);
    }

    return result;
  }

  async downloadBankAccountImage(document) {
    try {
      const response = await seafarerService.downloadBankAccountImage(document.id, document.mime);
      return response.data;
    } catch (error) {
      console.error(error);
    }
    return undefined;
  }

  async downloadFile(documentType, document, fileName) {
    try {
      let file = undefined;
      let type = undefined;
      if (documentType === 'passport') {
        const response = await this.downloadPassportImage(document);
        file = response.data;
        type = response.headers['content-type'];
      }

      if (documentType === 'seamans_book') {
        const response = await this.downloadSeamanBookImage(document);
        file = response.data;
        type = response.headers['content-type'];
      }

      if (documentType === 'screening') {
        file = await this.downloadScreeningDocument(document);
      }
      if (documentType === 'bank_account') {
        file = await this.downloadBankAccountImage(document);
      }

      if (!file && Object.values(ALL_DOC_TYPES).includes(documentType)) {
        const response = await seafarerService.downloadSeafarerDocument(document.id);
        file = response.data;
        type = response.headers['content-type'];
      }

      if (file) {
        if (documentType !== 'screening' && documentType !== 'bank_account') {
          const updatedFile = new Blob([file], { type: type });
          const fileURL = URL.createObjectURL(updatedFile);
          window.open(fileURL);
        } else {
          download(file, fileName ?? document.name);
        }
        return { status: 200 };
      }
    } catch (error) {
      console.log(error);
      return error;
    }
  }

  async uploadCaseReportImages(approvalId, files) {
    if (files === undefined || approvalId === undefined || files.length === 0) {
      return undefined;
    }

    try {
      await Promise.all(
        files.map((file) => {
          const formData = new FormData();

          const documentType = file.document_type;
          delete file.document_type;
          delete file.localId;
          formData.append(documentType, file);
          return seafarerService.uploadCaseReportImages(formData, approvalId);
        }),
      );
    } catch (error) {
      console.error(error);
    }

    return undefined;
  }

  async downloadScreeningDocument(document) {
    try {
      const response = await seafarerService.downloadScreeningDocument(document.id, document.mime);
      return response.data;
    } catch (error) {
      console.error(error);
    }
    return undefined;
  }
}

export default ImageUploadController;
