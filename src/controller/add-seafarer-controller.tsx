import _, { isString, cloneDeep } from 'lodash';

import seafarerService from '../service/seafarer-service';
import * as referenceService from '../service/reference-service';
import ImageController from './image-upload-controller';
import { v4 as uuid } from 'uuid';
import { filterByInvalidContact } from '../util/view-utils';
import { isValidDate, populateContactNumbers } from '../util/form-utils';
import {
  TELEPHONE,
  MOBILE,
  EMAIL,
  PHONE_VALIDATION,
  EMAIL_VALIDATION,
} from '../constants/contactTypes';
import {
  BANK_ACCOUNT_DOCUMENT,
  PASSPORT_DOCUMENT,
  SEAFARER_PHOTO,
  SEAMANS_BOOK_DOCUMENT,
} from '../constants/documentTypes';

import { genLocalStorageControlFuncs } from '../util/local-storage-helper';
import { removeSpaces } from '../util/string-utils';

const removeDeprecatedLocalStorageData = async () => {
  //double checked this 4 keys is used only by paris2-web-seafarer
  localStorage.removeItem('LocalDropDownData');
  localStorage.removeItem('LocalPortsData');
  localStorage.removeItem('LocalPortsOfIssueForSeamanBookData');
  localStorage.removeItem('DropDownDataCachedTime');
};

const validateDOBforBankDetails = (seafaerData) => {
  const { bank_accounts } = seafaerData.seafarer_person;
  if (!bank_accounts || bank_accounts.length === 0) return;
  bank_accounts.forEach((i) => {
    if (
      _.has(i, 'account_holder_date_of_birth') &&
      !_.isNull(i.account_holder_date_of_birth) &&
      !isValidDate(i.account_holder_date_of_birth)
    ) {
      throw new Error('Payload has Invalid Date of Birth');
    }
  });
};

const createSeafarer = async (changes, canViewContacts) => {
  const cleanedChanges = cleanSeafarer(changes, canViewContacts);
  validateDOBforBankDetails(cleanedChanges);
  const { data = {} } = await seafarerService.createSeafarer(cleanedChanges);
  return data;
};

const patchSeafarer = async (id, changes, canViewContacts) => {
  const cleanedChanges = cleanSeafarer(changes, canViewContacts);
  validateDOBforBankDetails(cleanedChanges);
  const { data = {} } = await seafarerService.patchSeafarer(id, cleanedChanges);
  return data;
};

const cleanDocumentFields = (documentList) => {
  return documentList.map((document) => {
    // common fields for both passport and seaman book
    if (document.date_of_issue === '') {
      document.date_of_issue = null;
    }
    if (document.date_of_expiry === '') {
      document.date_of_expiry = null;
    }

    // field of passport
    if (document.place_of_issue === '') {
      document.place_of_issue = null;
    }
    return document;
  });
};

const cleanSeafarer = (seafarer, canViewContacts) => {
  const cleanedChanges = removeEmptyStrings(seafarer);
  const cleanedPerson = removeEmptyStrings(cleanedChanges.seafarer_person);

  const currentFamilyMembers = cleanedChanges.seafarer_person.family_members ?? [];
  const cleanedFamilyMembers = currentFamilyMembers.map((item) => {
    if (seafarer.id && !canViewContacts) {
      delete item.address;
      delete item.address_id;
      delete item.telephone;
      delete item.mobilephone;
      delete item.email;
    }
    return removeEmptyStrings(item);
  });
  delete cleanedChanges.crew_planning;
  delete cleanedPerson.seafarer_screening;
  delete cleanedPerson.seafarer_status_history;
  cleanedPerson.family_members = [...cleanedFamilyMembers];
  cleanedPerson.passports = cleanDocumentFields([...cleanedChanges.seafarer_person.passports]);
  cleanedPerson.seaman_books = cleanDocumentFields([
    ...cleanedChanges.seafarer_person.seaman_books,
  ]);
  cleanedChanges.seafarer_person = { ...cleanedPerson };
  return cleanedChanges;
};

const removeEmptyStrings = (dictionary) => {
  if (dictionary === null || dictionary === undefined) {
    return dictionary;
  }
  Object.keys(dictionary).forEach(function (key) {
    const value = dictionary[key];
    if (isString(value)) {
      const trimedValue = value.trim();
      if (trimedValue === '') {
        dictionary[key] = null;
      }
    }
    if (value === null || value === undefined) {
      delete dictionary[key];
    }
    if (key === 'localId') {
      delete dictionary[key];
    }
  });
  return dictionary;
};

const getEmptySeafarer = () => {
  const item = {
    seafarer_person: {
      passports: [{ localId: uuid() }],
      seaman_books: [{ is_original: true, localId: uuid() }],
      bank_accounts: [{}],
      first_name: '',
      last_name: '',
      middle_name: '',
      telephone_numbers: [{ localId: uuid(), contact_type: 'telephone_number', contact: '' }],
      mobile_numbers: [{ localId: uuid(), contact_type: 'mobile_number', contact: '' }],
      email_addresses: [{ localId: uuid(), contact_type: 'email', contact: '' }],
      addresses: [{ localId: uuid() }],
      family_members: [{ localId: uuid() }],
      marital_status: 'single',
      place_of_birth: '',
    },
  };

  return { ...item };
};

const loadSeafarer = async (id) => {
  const { data: seafarer = getEmptySeafarer() } = await seafarerService.getSeafarer(id, true);
  if (seafarer.seafarer_person) {
    delete seafarer.seafarer_person.created_by_user_info;
    delete seafarer.seafarer_person.updated_by_user_info;
  }
  return seafarer;
};

const gender = [
  { id: 1, value: 'male' },
  { id: 2, value: 'female' },
];

const bool = [
  { id: 1, value: 'yes' },
  { id: 2, value: 'no' },
];

const dataQuality = [
  {
    label: 'Urgent Correction',
    color: 'red',
    name: 'mandatory_data_missing',
  },
  {
    label: 'No Urgent Correction',
    color: 'orange',
    name: 'data_invalid',
  },
  {
    label: 'Data is OK',
    color: 'green',
    name: 'clean',
  },
];

/**
 * Note: should not call this directly
 * should call AddSeafarerController.loadDropDownData() to make sure cached value in LocalStorage is used/invalidated
 **/
const _loadDropDownData = async () => {
  const result = await Promise.all([
    seafarerService.getSeafarerDropDownData(),
    seafarerService.getSeafarerDocumentDropdown(),
    referenceService.getVisaRegionDropDownData(),
  ]);
  const dropDownsData = {
    ...result[0],
    ...result[1],
    ...result[2],
  };
  const sortData = (type) => {
    return dropDownsData[type]
      ? dropDownsData[type].sort((a, b) => (a.value.toLowerCase() > b.value.toLowerCase() ? 1 : -1))
      : [];
  };

  dropDownsData['nationalities'] = sortData('nationalities');
  dropDownsData['ranks'] = sortData('ranks');
  dropDownsData['bool'] = bool;
  dropDownsData['gender'] = gender;
  dropDownsData['dataQuality'] = dataQuality;
  dropDownsData['manningAgents'] = sortData('manningAgents');
  dropDownsData['visaRegions'] = dropDownsData['visaRegions']
    .map((ele) => {
      return ele.name;
    })
    .sort((a, b) => (a.toLowerCase() > b.toLowerCase() ? 1 : -1));
  dropDownsData['cocCertificates'] = sortData('cocCertificates');
  dropDownsData['endorsements'] = sortData('endorsements');
  dropDownsData['institutes'] = sortData('institutes');
  dropDownsData['medicalCertificates'] = sortData('medicalCertificates');
  dropDownsData['otherCourseTypes'] = sortData('otherCourseTypes');
  dropDownsData['otherDocumentsTypes'] = sortData('otherDocumentsTypes');
  dropDownsData['preSeaTrainingCourses'] = sortData('preSeaTrainingCourses');
  dropDownsData['stcwLicences'] = sortData('stcwLicences');

  return dropDownsData;
};

const imageController = new ImageController();

class AddSeafarerController {
  constructor({ isEditBankAccountDetails } = {}) {
    this.isEditBankAccountDetails = isEditBankAccountDetails;
  }
  seafarerId = null;
  seafarerChanges = {};
  initialValues = {};

  getEmptySeafarer = getEmptySeafarer;

  //Deprecated: no usage as of ********
  async onLoadPage(seafarerId) {
    this.seafarerId = seafarerId ? parseInt(seafarerId) : null;
    let seafarer = { ...getEmptySeafarer() };

    if (this.seafarerId) {
      seafarer = await loadSeafarer(this.seafarerId);
    }

    // go through this.loadDropDownData() to make sure cached value in LocalStorage is used/invalidated
    const dropDownData = await this.loadDropDownData();
    return {
      seafarer,
      dropDownData,
    };
  }

  async loadSeafarer(seafarerId) {
    this.seafarerId = seafarerId ? parseInt(seafarerId) : null;
    let seafarer = { ...getEmptySeafarer() };

    if (this.seafarerId) {
      seafarer = await loadSeafarer(this.seafarerId);
      if (Object.keys(seafarer).length > 0) {
        seafarer = this.cleanBankAddress(seafarer);

        if (seafarer.seafarer_person.family_members) {
          this.getInvalidFamilyContacts(seafarer);
        }

        if (seafarer.seafarer_person.seafarer_contacts) {
          seafarer = this.mapContactFields(seafarer);
        }

        this.prepareFilesForValidation(seafarer);
      }
      return seafarer;
    }

    return seafarer;
  }

  prepareFilesForValidation(seafarer) {
    const person = seafarer.seafarer_person ?? {};
    const passports = person.passports ?? [];
    passports.forEach((passport) => {
      if (passport.document && passport.document.length > 0) {
        passport.file = {};
      }
    });

    const seamanBooks = person.seaman_books ?? [];
    seamanBooks.forEach((seamanBook) => {
      if (seamanBook.document && seamanBook.document.length > 0) {
        seamanBook.file = {};
      }
    });
  }

  cleanBankAddress(seafarer) {
    const person = seafarer.seafarer_person || {};
    const bank_accounts = person.bank_accounts || [];

    seafarer.seafarer_person.bank_accounts = bank_accounts.map((b) => {
      if (b.bank_address === null || b.bank_address === undefined) b.bank_address = {};
      return b;
    });

    return seafarer;
  }

  getInvalidContacts(seafarer) {
    const person = seafarer.seafarer_person || {};
    const contacts = person.seafarer_contacts || [];

    const telephones = contacts.filter(filterByInvalidContact(TELEPHONE, PHONE_VALIDATION));
    const mobiles = contacts.filter(filterByInvalidContact(MOBILE, PHONE_VALIDATION));
    const emails = contacts.filter(filterByInvalidContact(EMAIL, EMAIL_VALIDATION));

    const joinedContacts = [...telephones, ...mobiles, ...emails];

    if (joinedContacts.length > 0) {
      if (!seafarer.seafarer_person.invalid_data) seafarer.seafarer_person.invalid_data = {};

      seafarer.seafarer_person.invalid_data.contacts = { telephones, mobiles, emails };
    }
  }

  getInvalidFamilyContacts(seafarer) {
    const person = seafarer.seafarer_person || {};
    const familyMembers = person.family_members || [];

    if (familyMembers.length === 0) return;

    const invalidData = {};

    const fm = familyMembers[0];

    if (fm.telephone && !PHONE_VALIDATION.test(fm.telephone)) invalidData.telephone = fm.telephone;
    if (fm.mobilephone && !PHONE_VALIDATION.test(fm.mobilephone))
      invalidData.mobilephone = fm.mobilephone;
    if (fm.email && !PHONE_VALIDATION.test(fm.email)) invalidData.email = fm.email;

    if (Object.keys(invalidData).length > 0) {
      if (!seafarer.seafarer_person.invalid_data) seafarer.seafarer_person.invalid_data = {};

      seafarer.seafarer_person.invalid_data.family_members = invalidData;
    }
  }

  mapContactFields(seafarer) {
    const contacts = seafarer.seafarer_person.seafarer_contacts || [];

    const telephone_numbers = [];
    const mobile_numbers = [];
    const email_addresses = [];

    contacts.map((contactData) => {
      const { contact, contact_type, id } = contactData;
      if (contact_type === TELEPHONE) populateContactNumbers(telephone_numbers, contactData);
      if (contact_type === MOBILE) populateContactNumbers(mobile_numbers, contactData);
      if (contact_type === EMAIL) email_addresses.push({ id, contact, contact_type });
      return contactData;
    });

    this.getInvalidContacts(seafarer);

    seafarer.seafarer_person.telephone_numbers = telephone_numbers;
    seafarer.seafarer_person.mobile_numbers = mobile_numbers;
    seafarer.seafarer_person.email_addresses = email_addresses;

    return seafarer;
  }

  dropdownDataLocalStorageControl = genLocalStorageControlFuncs(['LocalDropDownData']);
  async loadDropDownData() {
    //temp code to clean deprecated data in LocalStorage
    removeDeprecatedLocalStorageData();

    const cacheKey = `LocalDropDownData`;
    const isCacheInvalidated =
      await this.dropdownDataLocalStorageControl.removeLocalStorageItemsIfExpired();

    if (!isCacheInvalidated) {
      const cachedValue = this.dropdownDataLocalStorageControl.getLocalStorageItem(cacheKey);

      if (cachedValue === undefined) {
        throw Error('cachedValue in LocalDropDownData should not be undefined');
      }

      return JSON.parse(cachedValue);
    } else {
      const response = await _loadDropDownData();
      this.dropdownDataLocalStorageControl.setLocalStorageItem(cacheKey, JSON.stringify(response));
      return response;
    }
  }

  async loadBankNames() {
    const response = await seafarerService.getBankNames();
    return response;
  }

  async loadSeafarerReportingOfficeDropDownData() {
    const response = await seafarerService.getSeafarerReportingOfficeDropDownData();
    return response;
  }

  async loadManningAgentDropDownData() {
    const response = await seafarerService.getSeafarerDropDownData('?values=manningAgents');
    return response?.manningAgents;
  }

  portsOfIssueForSeamanBookLocalStorageControl = genLocalStorageControlFuncs([
    'LocalPortsOfIssueForSeamanBookData',
  ]);
  async loadPortsOfIssueForSeamanBook(countryCode) {
    //temp code to clean deprecated data in LocalStorage
    removeDeprecatedLocalStorageData();

    this.portsOfIssueForSeamanBookLocalStorageControl.removeLocalStorageItemsIfExpired();

    const cacheKey = `LocalPortsOfIssueForSeamanBookData`;
    const cachedValue =
      this.portsOfIssueForSeamanBookLocalStorageControl.getLocalStorageItem(cacheKey);
    let cachedPortsMap = {};
    if (cachedValue) {
      cachedPortsMap = JSON.parse(cachedValue);
      if (cachedPortsMap[countryCode]) {
        return cachedPortsMap[countryCode];
      }
    }
    const { ports } = await referenceService.getPortsOfIssueForSeamanBook(countryCode);
    if (!Array.isArray(ports)) return [];
    const portsMap = typeof cachedPortsMap === 'object' ? cachedPortsMap : {};
    portsMap[countryCode] = ports.map(({ name }) => name).filter((name) => name);
    this.portsOfIssueForSeamanBookLocalStorageControl.setLocalStorageItem(
      cacheKey,
      JSON.stringify(portsMap),
    );

    return portsMap[countryCode];
  }

  async loadPortsOfIssueForSeamanBookMap(countryCodes) {
    const results = await Promise.all(
      countryCodes.map((code) => this.loadPortsOfIssueForSeamanBook(code)),
    );
    return results.reduce(
      (map, result, index) => ({
        [countryCodes[index]]: result,
        ...map,
      }),
      {},
    );
  }

  difference(changed, initial, parentKey) {
    if (!initial) {
      return _.cloneDeep(changed);
    }
    if (_.isArray(changed)) {
      return changed
        .map((ele) => {
          const initialArr = initial.find((obj) => {
            return obj.id === ele.id;
          });
          return this.difference(ele, initialArr, parentKey);
        })
        .filter((diffValue) => !_.isEmpty(diffValue));
    }
    let diff = {};
    for (const [key, val] of Object.entries(changed)) {
      // number is mandatory for the sync logic
      if (_.isObject(val)) {
        parentKey = key;
        diff[key] = this.difference(val, initial[key], parentKey);
      } else if (key === 'id') {
        diff[key] = val;
      } else if (val !== initial[key]) {
        diff[key] = val;
      }
    }
    return diff;
  }

  async onSubmitSeafarer() {
    // 1 get all not upladed files yet
    /**
     * gen doc_path for passport and seaman book first,
     * as now we map the seafarer.seafarer_person.passport & seafarer.seafarer_person.seaman_books with doc_path
     *
     * use doc_path bcause
     * 1. number is not present in patch body if it is not updated (and cannot make it always present to avoid uncesssary validation/permission check)
     * 2. id is not present in patch body if the passport is new
     *
     * For bank account, will keep useing bank account number to map (bank account number is mandatory if file is uploaded)
     **/
    //skip ice_experience_doc
    const cleanedSeafarerChanges = { ...this.seafarerChanges };
    delete cleanedSeafarerChanges.ice_experience_doc;

    const seafarerChangesWithDocPaths = this.genDocPathForFileToUpload(cleanedSeafarerChanges);
    const passportFiles = this.getPassportFilesForUploading(seafarerChangesWithDocPaths);

    const seamansBookFiles = this.getSeamanBooksFilesForUploading(seafarerChangesWithDocPaths);

    const bankAccountFiles = this.getBankAccountFilesForUploading(seafarerChangesWithDocPaths);

    const photosFile = this.getSeafarerImageForUploading(seafarerChangesWithDocPaths);

    let photoIdToRemove = null;

    if (this.seafarerId && seafarerChangesWithDocPaths.seafarer_person.photoIdToRemove) {
      photoIdToRemove = seafarerChangesWithDocPaths.seafarer_person.photoIdToRemove;
    }

    const { seamansBookDocIdToRemove, passportDocIdToRemove, bankAccountDocIdToRemove } =
      this.getListOfDocumentsToRemove({ ...seafarerChangesWithDocPaths });

    // 2 submit seafarer
    const cleanedSeafarer = this.getCleanedSeafarer(seafarerChangesWithDocPaths);
    const initialCleanedSeafarer = this.getCleanedSeafarer(this.initialValues, {
      useOriginalContacts: true,
    });
    const diff = this.difference(cleanedSeafarer, initialCleanedSeafarer);

    const canViewContacts = this.initialValues.can_view_contacts;
    if (this.isEditBankAccountDetails) {
      diff.isEditBankAccountDetails = this.isEditBankAccountDetails;
    }
    const updatedSeafarer = await this.uploadSeafarer(diff, canViewContacts);

    // 3 get updated seafarer object // updatedSeafarer
    // 4 map not uploaded yet images with created id's

    // This API is for mapping the updatedSeafarer.passports with passportFiles, just to get the passport.id
    const passportsToUpload = this.preparePassportsForImageUploading(
      updatedSeafarer,
      passportFiles,
    );

    const uploadPromises = [];

    // 5 submit images one by one
    if (passportsToUpload && passportsToUpload.length > 0) {
      for (const passportFile of passportsToUpload) {
        if (passportFile) {
          uploadPromises.push(imageController.uploadPassportImage(passportFile));
        }
      }
    }

    // 6 map seaman books objects with not yet uploaded files
    const seamanBooksToUpload = this.prepareSeamanBooksForImageUploading(
      updatedSeafarer,
      seamansBookFiles,
    );

    // submit images for seaman books
    if (seamanBooksToUpload && seamanBooksToUpload.length > 0) {
      for (const seamanBookFile of seamanBooksToUpload) {
        if (seamanBookFile) {
          uploadPromises.push(imageController.uploadSeamansBookImage(seamanBookFile));
        }
      }
    }

    const photosToUpload = this.preparePhotosForImageUploading(updatedSeafarer, photosFile);

    // submit images for seaman books
    if (photosToUpload && photosToUpload.length > 0) {
      const photo = photosToUpload[0];
      uploadPromises.push(imageController.uploadSeafarerImage(photo));
    }

    // 6 map seaman books objects with not yet uploaded files
    const bankAccountsToUpload = this.prepareBankAccountsForImageUploading(
      cleanedSeafarer,
      bankAccountFiles,
    );

    // submit images for bank accounts
    if (bankAccountsToUpload && bankAccountsToUpload.length > 0) {
      for (const bankFile of bankAccountsToUpload) {
        if (bankFile) {
          uploadPromises.push(imageController.uploadBankAccountImage(bankFile));
        }
      }
    }

    await Promise.all(uploadPromises);

    const deletePromises = [];

    for (const sbDocId of seamansBookDocIdToRemove) {
      deletePromises.push(imageController.deleteDocument(sbDocId, SEAMANS_BOOK_DOCUMENT));
    }

    for (const docId of passportDocIdToRemove) {
      deletePromises.push(imageController.deleteDocument(docId, PASSPORT_DOCUMENT));
    }

    for (const docId of bankAccountDocIdToRemove) {
      deletePromises.push(imageController.deleteDocument(docId, BANK_ACCOUNT_DOCUMENT));
    }

    if (photoIdToRemove !== null && photosToUpload.length === 0) {
      deletePromises.push(imageController.deleteDocument(photoIdToRemove, SEAFARER_PHOTO));
    }

    await Promise.all(deletePromises);

    return { id: updatedSeafarer.id, seafarerPersonId: updatedSeafarer.seafarer_person_id };
  }

  getListOfDocumentsToRemove(seafarer) {
    const person = seafarer.seafarer_person || {};

    const seamansBook = person.seaman_books || [];
    const seamansBookDocIdToRemove = this.getDocumentToRemove(seamansBook);

    const passports = person.passports || [];
    const passportDocIdToRemove = this.getDocumentToRemove(passports);

    const bankAccounts = person.bank_accounts || [];
    const bankAccountDocIdToRemove = this.getDocumentToRemove(bankAccounts);

    return { seamansBookDocIdToRemove, passportDocIdToRemove, bankAccountDocIdToRemove };
  }

  getDocumentToRemove(documents) {
    const docsIdToRemove = documents
      .filter((d) => d.idToRemove && !d.file)
      .map((d) => d.idToRemove);
    return docsIdToRemove;
  }

  async uploadSeafarer(seafarer, canViewContacts) {
    if (this.seafarerId === null) {
      return await createSeafarer(seafarer, canViewContacts);
    } else {
      return await patchSeafarer(this.seafarerId, seafarer, canViewContacts);
    }
  }

  getPassportFilesForUploading(seafarer) {
    const files = [];
    const person = seafarer.seafarer_person ?? {};
    const passports = person.passports ?? [];

    passports.forEach((passport) => {
      const file = passport.file;
      if (file && file instanceof File) {
        // use doc_path for mapping to corresponding passport obj to file object after seafaer is created/patched through backend API
        file.doc_path = passport.doc_path;
        files.push(file);
      }
    });

    return files;
  }

  getSeafarerImageForUploading(seafarer) {
    const files = [];
    const person = seafarer.seafarer_person ?? {};
    files.push(person.photo);
    return files;
  }

  getSeamanBooksFilesForUploading(seafarer) {
    const files = [];
    const person = seafarer.seafarer_person ?? {};
    const seamanBooks = person.seaman_books ?? [];

    seamanBooks.forEach((seamanBook) => {
      const file = seamanBook.file;
      if (file && file instanceof File) {
        // use doc_path for mapping to corresponding seafarer obj with file object after seafaer is created/patched through backend API
        file.doc_path = seamanBook.doc_path;
        files.push(file);
      }
    });

    return files;
  }

  getBankAccountFilesForUploading(seafarer) {
    const files = [];
    const person = seafarer.seafarer_person ?? {};
    const bankAccounts = person.bank_accounts ?? [];

    bankAccounts.forEach((account) => {
      const file = account.file;
      if (file) {
        file.bank_account_number = account.number;
        files.push(file);
      }
    });

    return files;
  }

  genDocPath(fileName) {
    return `/ui${new Date().getFullYear()}/${new Date().getTime()}_${removeSpaces(fileName)}`;
  }

  genDocPathForFileToUpload(seafarer) {
    const newSeafarer = cloneDeep(seafarer);
    const person = newSeafarer.seafarer_person ?? {};
    const passports = person.passports ?? [];
    const seamanBooks = person.seaman_books ?? [];
    const passportWithDocPath = passports.map((passport) => {
      if (passport.file && passport.file instanceof File) {
        passport.doc_path = this.genDocPath(passport.file.name);
      }

      return passport;
    });
    newSeafarer.seafarer_person.passports = passportWithDocPath;

    const seamanBooksWithDocPath = seamanBooks.map((seamanbook) => {
      if (seamanbook.file && seamanbook.file instanceof File) {
        seamanbook.doc_path = this.genDocPath(seamanbook.file.name);
      }

      return seamanbook;
    });
    newSeafarer.seafarer_person.seaman_books = seamanBooksWithDocPath;

    return newSeafarer;
  }

  getCleanedSeafarer(seafarer, options = {}) {
    const newSeafarer = cloneDeep(seafarer);
    const person = newSeafarer.seafarer_person ?? {};
    const passports = person.passports ?? [];
    const seamanBooks = person.seaman_books ?? [];
    const bankAccounts = person.bank_accounts ?? [];
    // Contact section
    const telephoneNumbers = person.telephone_numbers ?? [];
    const mobileNumbers = person.mobile_numbers ?? [];
    const emails = person.email_addresses ?? [];
    const addresses = person.addresses ?? [];
    const { photo } = person;

    if (options.useOriginalContacts) {
      const originalContactRecords = newSeafarer.seafarer_person.seafarer_contacts;
      if (originalContactRecords) {
        newSeafarer.seafarer_person.seafarer_contacts = originalContactRecords.map(
          (contactRecord) => {
            return {
              id: contactRecord.id,
              contact: contactRecord.contact,
              contact_type: contactRecord.contact_type,
            };
          },
        );
      } else {
        newSeafarer.seafarer_person.seafarer_contacts = [];
      }
    } else {
      const contactFields = [...telephoneNumbers, ...mobileNumbers, ...emails].filter((contact) => {
        delete contact.localId;
        if (contact.contact) return contact;
      });
      newSeafarer.seafarer_person.seafarer_contacts = contactFields;
    }

    const cleanedAddresses = addresses.filter((address) => {
      delete address.localId;
      if (Object.keys(address).length > 0) return address;
    });
    newSeafarer.seafarer_person.addresses = cleanedAddresses;

    const cleanedPassports = passports
      .map((passport) => {
        delete passport.file;
        delete passport.idToRemove;
        delete passport.localId;
        return passport;
      })
      .filter((item) => Object.keys(item).length > 0);
    newSeafarer.seafarer_person.passports = cleanedPassports;

    const cleanedSeamanBooks = seamanBooks
      .map((seamanBook) => {
        if (!seamanBook.is_original) seamanBook.is_original = false;
        delete seamanBook.file;
        delete seamanBook.idToRemove;
        delete seamanBook.localId;
        delete seamanBook.has_no_date_of_expiry;
        return seamanBook;
      })
      .filter((item) => Object.keys(item).length > 0);

    newSeafarer.seafarer_person.seaman_books = cleanedSeamanBooks;

    const cleanedBankAccounts = bankAccounts
      .map((account) => {
        if (account.account_holder_gender === '') account.account_holder_gender = null;
        delete account.file;
        delete account.idToRemove;
        delete account.localId;
        return account;
      })
      .filter((item) => Object.keys(item).length > 0);

    newSeafarer.seafarer_person.bank_accounts = cleanedBankAccounts;

    let newPhotoPath = person.photo_path ?? null;
    if (photo) {
      newPhotoPath = this.genDocPath(newSeafarer.seafarer_person.photo.name);
    } else if (!photo && !!newSeafarer.seafarer_person.photoIdToRemove) {
      newPhotoPath = '';
    }
    newSeafarer.seafarer_person.photo_path = newPhotoPath;
    delete newSeafarer.seafarer_person.photo;

    if (newSeafarer.seafarer_person.smoking && newSeafarer.seafarer_person.smoking === '')
      newSeafarer.seafarer_person.smoking = null;
    if (newSeafarer.seafarer_person.vegatarian && newSeafarer.seafarer_person.vegatarian === '')
      newSeafarer.seafarer_person.vegatarian = null;

    delete newSeafarer.seafarer_rank;
    delete newSeafarer.seafarer_reporting_office;
    delete newSeafarer.seafarer_manning_agent;
    delete newSeafarer.seafarer_person.telephone_numbers;
    delete newSeafarer.seafarer_person.mobile_numbers;
    delete newSeafarer.seafarer_person.email_addresses;
    delete newSeafarer.seafarer_person.invalid_data;
    delete newSeafarer.seafarer_person.country_of_birth;
    delete newSeafarer.seafarer_person.photoIdToRemove;

    if (seafarer.id && !seafarer.can_view_contacts) {
      delete newSeafarer.seafarer_person.seafarer_contacts;
      delete newSeafarer.seafarer_person.addresses;
    }

    return newSeafarer;
  }

  preparePassportsForImageUploading(seafarer, files) {
    const person = seafarer.seafarer_person ?? {};
    const passports = person.passports ?? [];

    const result = passports.map((passport) => {
      for (let i = 0; i < (files ?? []).length; i++) {
        const file = files[i];
        if (
          passport.doc_path !== undefined &&
          file.doc_path !== undefined &&
          passport.doc_path === file.doc_path
        ) {
          passport.file = file;
          passport.doc_path = passport.doc_path.replace(/ /g, '_');
          return passport;
        }
      }
    });

    return result;
  }

  prepareSeamanBooksForImageUploading(seafarer, files) {
    const person = seafarer.seafarer_person ?? {};
    const seamanBooks = person.seaman_books ?? [];

    const result = seamanBooks.map((seamanBook) => {
      for (let i = 0; i < (files ?? []).length; i++) {
        const file = files[i];
        if (
          seamanBook.doc_path !== undefined &&
          file.doc_path !== undefined &&
          seamanBook.doc_path === file.doc_path
        ) {
          seamanBook.file = file;
          seamanBook.doc_path = seamanBook.doc_path.replace(/ /g, '_');
          return seamanBook;
        }
      }
    });

    return result;
  }

  preparePhotosForImageUploading(seafarer, files) {
    if (files.length === 0 || files[0] === undefined) return [];
    const person = seafarer.seafarer_person ?? {};
    const results = files.map((p) => {
      p.seafarer_person_id = person.id;
      return p;
    });
    return results;
  }

  prepareBankAccountsForImageUploading(seafarer, files = []) {
    const person = seafarer.seafarer_person ?? {};
    const bankAccounts = person.bank_accounts ?? [];

    const result = bankAccounts.map((account) => {
      for (let i = 0; i < (files ?? []).length; i++) {
        const file = files[i];
        if (account.number === file.bank_account_number) {
          account.file = file;
          return account;
        }
      }
    });

    return result;
  }
}

export default AddSeafarerController;
