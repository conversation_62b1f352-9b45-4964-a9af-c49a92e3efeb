import * as wcoService from '../service/wco-service';
import * as seafarerService from '../service/seafarer-service';
import moment from 'moment';

const MAX_WCO_POLLING_COUNT = 15;

const formatDate = (dateText) => (dateText ? moment(dateText).format('D MMM YYYY') : '');

const SECONDARDARY_FIELDS_MAP = {
  gender: {
    typeId: 'SFCT_1',
  },
  dateOfBirth: {
    typeId: 'SFCT_2',
    isDateTime: true,
  },
  countryLocation: {
    typeId: 'SFCT_3',
  },
  placeOfBirth: {
    typeId: 'SFCT_4',
  },
  nationality: {
    typeId: 'SFCT_5',
  },
};

class WcoScreeningDetailsController {
  wcoCase = null;
  closed = false;

  parseSecondaryFields = (secondaryFieldResults) => {
    return Object.entries(SECONDARDARY_FIELDS_MAP).reduce(
      (map, [key, { typeId, isDateTime = false }]) => {
        const data = secondaryFieldResults.find((result) => result.typeId === typeId);
        if (!data?.field) {
          return {
            [key]: {
              value: null,
              submittedValue: null,
            },
            ...map,
          };
        }
        const value = isDateTime ? data.field.dateTimeValue : data.field.value;
        const submittedValue = isDateTime
          ? formatDate(data.submittedDateTimeValue)
          : data.submittedValue;
        return {
          [key]: {
            value,
            submittedValue,
          },
          ...map,
        };
      },
      {},
    );
  };

  parseWcoResults = (wcoCaseData) => {
    let parsedResult = [];
    if (wcoCaseData?.result?.result) {
      parsedResult = JSON.parse(wcoCaseData.result.result);
    }

    const results = parsedResult.map(
      ({
        primaryName,
        category,
        matchStrength,
        providerType,
        secondaryFieldResults,
        referenceId,
        matchedTerm,
        submittedTerm,
      }) => {
        const name = primaryName;
        const fields = this.parseSecondaryFields(secondaryFieldResults);

        const fieldValues = {
          name: matchedTerm,
          ...Object.entries(fields).reduce(
            (map, [key, field]) => ({
              ...map,
              [key]: field.value,
            }),
            {},
          ),
        };
        const submittedValues = {
          name: submittedTerm,
          ...Object.entries(fields).reduce(
            (map, [key, field]) => ({
              ...map,
              [key]: field.submittedValue,
            }),
            {},
          ),
        };
        return {
          matchStrength,
          name,
          category,
          providerType,
          referenceId,
          ...fieldValues,
          submittedValues,
        };
      },
    );
    return results;
  };

  parseWcoCaseData = (wcoCaseData) => {
    const { id, wco_case_system_id, created_at, status, wco_case_id} = wcoCaseData;
    const results = this.parseWcoResults(wcoCaseData);
    const firstItem = results.length > 0 ? results[0] : {};
    const submittedValues = firstItem.submittedValues ?? {};
    const wcoCase = {
      id,
      wco_case_id: wco_case_id,
      status: status ?? 'not_screened',
      created_at: moment(created_at).format('D MMM YYYY h:mm'),
      wcoCaseSystemId: wco_case_system_id,
      results,
      submittedValues,
    };
    return wcoCase;
  };

  loadCase = async (seafarerId, seafarerPersonId) => {
    this.closed = false;
    let { data: wcoCaseData } = await wcoService.getWcoCase(seafarerPersonId);

    let hasWcoCaseData = wcoCaseData.status === 'success' || wcoCaseData.status === 'error';

    if (!hasWcoCaseData) {
      await wcoService.requestScreening(seafarerPersonId);

      let finished = false;
      let pollCount = 0;
      while (!finished && pollCount < MAX_WCO_POLLING_COUNT && !this.closed) {
        ({ data: wcoCaseData } = await wcoService.getWcoCase(seafarerPersonId));
        pollCount++;
        finished = wcoCaseData.status === 'success' || wcoCaseData.status === 'error';
        if (!finished) {
          await new Promise((resolve) => setTimeout(resolve, 3000));
        }
      }

      console.log('wcoCaseData newly fetched: ', wcoCaseData);
    } else {
      console.log(
        `Successfully read WCO case data for seafarerPersonId: ${seafarerPersonId} from DB`,
      );
    }

    this.wcoCase = this.parseWcoCaseData(wcoCaseData);
    if (Object.keys(this.wcoCase.submittedValues).length === 0) {
      const { data: seafarer } = await seafarerService.getSeafarer(seafarerId);
      const seafarerPerson = seafarer.seafarer_person;
      const countryLocation =
        seafarerPerson.passports.length > 0 && seafarerPerson.passports[0].country
          ? seafarerPerson.passports[0].country.alpha3_code
          : null;
      this.wcoCase.submittedValues = {
        name: seafarerPerson.name ?? (seafarerPerson.first_name + ' ' + seafarerPerson.last_name),
        gender: seafarerPerson.gender,
        nationality: seafarerPerson.nationality ? seafarerPerson.nationality.alpha3_code : '',
        countryLocation,
        dateOfBirth: formatDate(seafarerPerson.date_of_birth),
        placeOfBirth: seafarerPerson.country_of_birth
          ? seafarerPerson.country_of_birth.alpha3_code
          : '',
      };
    }
    return this.wcoCase;
  };

  close = () => {
    this.closed = true;
  };
}

export default WcoScreeningDetailsController;
