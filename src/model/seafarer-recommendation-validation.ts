import { EMAIL_VALIDATION } from '../constants/contactTypes';
import * as yup from 'yup';

export const seafarerValidationSchema = () => {
  return yup.object({
    vesselName: yup.string().required('Please select a vessel'),
    wages: yup
      .number()
      .min(0, 'Wages must be greater than or equal to 0')
      .required('Wages must be greater than or equal to 0'),
    rank: yup.string().required('Please select a rank'),
    expectedSignOnDate: yup.string().required('Please select expected sign on date'),
    email: yup
      .string()
      .matches(EMAIL_VALIDATION, 'Invalid email address !')
      .required('Please input a valid email'),
    remark: yup.string().max(300, 'Remarks must be less than 301 characters').nullable(),
  });
};

export const supyValidationSchema = () => {
  return yup.object({
    vesselName: yup.string().required('Please select a vessel'),
    expectedSignOnDate: yup.string().required('Please select expected sign on date'),
    remark: yup.string().max(300, 'Remarks must be less than 301 characters').nullable(),
  });
};
