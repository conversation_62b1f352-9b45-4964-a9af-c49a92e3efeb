import * as yup from 'yup';
import moment from 'moment';

import {
  PHONE_VALIDATION,
  MULTI_PHONE_VALIDATION,
  EMAIL_VALIDATION,
  PASSPORT_NUMBER_VALIDATION,
  SEAMAN_BOOK_NUMBER_VALIDATION,
  MULTI_EMAIL_VALIDATION,
} from '../constants/contactTypes';

import { ERROR_MAPPINGS, RANK_VALUES } from '../constants/formSections';

import * as COMMON_VALIDATION_MSG from '../constants/common-validation-messages';

function validateRecommendedDate(value) {
  const currentDate = moment();
  const recommentedDate = moment(value, 'YYYY-MM-DD', true);
  if (recommentedDate.isAfter(currentDate, 'day')) {
    return false;
  }
  return true;
}

function isValidDate(value) {
  if (value) {
    const parseDate = moment(value).format('D MMM YYYY');
    return moment(parseDate, 'D MMM YYYY', true).isValid();
  }
  return true;
}

function isWithInRange(value) {
  if (value) {
    const parseDate = moment(value).format('D MMM YYYY');
    return moment(parseDate, 'D MMM YYYY').isBetween('1900-01-01', '2100-12-31', undefined, '[]');
  }
  return true;
}

const phoneSchema = (errorMessage) =>
  yup.object().shape({
    contact: yup.string().matches(PHONE_VALIDATION, errorMessage).nullable(),
    contact_type: yup.string().nullable(),
  });

const phoneField = (errorMessage) =>
  yup.string().matches(PHONE_VALIDATION, errorMessage).nullable();

const multiPhoneField = (errorMessage) =>
  yup.string().matches(MULTI_PHONE_VALIDATION, errorMessage).nullable();

const getLatestDocExpiryDate = (docList) => {
  const dateList = docList.map((p) => {
    return new Date(p.date_of_expiry ? p.date_of_expiry : 0).getTime();
  });
  const maxDate = Math.max(...dateList);
  return maxDate;
};

yup.addMethod(yup.string, 'validateIfscCode', function (args) {
  const { selectedBank, message } = args;
  return this.test('test-ifsc-digit', message, function (value) {
    const {
      path,
      createError,
      options: { index },
    } = this;
    if (selectedBank.length && selectedBank[index] && selectedBank[index].neft_prefix) {
      const len = selectedBank[index].neft_prefix.length;
      if (
        value &&
        value.substring(0, len) === selectedBank[index].neft_prefix &&
        value.length === 11
      ) {
        return true;
      } else {
        createError({ path, message });
      }
    } else {
      return true;
    }
  });
});

yup.addMethod(yup.string, 'validateAccountNumberLength', function (args) {
  const { selectedBank } = args;
  return this.test('test-account-number', function (value) {
    const {
      path,
      createError,
      options: { index },
    } = this;
    if (selectedBank.length && value) {
      value = value.trim();
      if (selectedBank[index] && !selectedBank[index].account_digit) {
        if (value && value.length <= 35) {
          return true;
        } else {
          return createError({ path, message: 'Incorrect account number' });
        }
      }
      const splitAccountDigit = selectedBank[index]?.account_digit.split(',');
      let isEqual = false;
      splitAccountDigit?.forEach((digit) => {
        if (value && +digit === value.length) {
          isEqual = true;
        } else {
          isEqual = false;
        }
      });
      return isEqual ? true : createError({ path, message: 'Incorrect account number' });
    } else {
      return true;
    }
  });
});

yup.addMethod(yup.string, 'checkIsIfscRequired', function (selectedBank) {
  return this.test('is-required', function (value) {
    const {
      createError,
      path,
      options: { index },
    } = this;
    if (selectedBank.length && selectedBank[index] && selectedBank[index].neft_prefix && !value) {
      return createError({ path, message: ERROR_MAPPINGS['ifsc_number'].message });
    } else {
      return true;
    }
  });
});

yup.addMethod(yup.string, 'checkAccountNumberWithOtherParameters', function (args) {
  const { selectedBank } = args;
  return this.test('check-special-character', function (value) {
    const {
      path,
      createError,
      options: { index },
    } = this;
    if (selectedBank.length && value) {
      value = value.trim();
      let isError = true;
      if (selectedBank[index] && !selectedBank[index].is_other_chars_allowed) {
        if (!/^[^*|":<>[\]{}`\\()';@&$]+$/.test(value)) {
          return createError({ path, message: 'Special characters are not allowed ' });
        } else {
          isError = false;
        }
      }
      if (
        selectedBank[index] &&
        !selectedBank[index].is_letters_allowed &&
        selectedBank[index].account_digit
      ) {
        if (!/^\d+$/.test(value)) {
          return createError({ path, message: 'Not allowed letters' });
        } else {
          isError = false;
        }
      }
      if (!isError) {
        return true;
      }
    } else {
      return true;
    }
  });
});

const bankAccountField = () => {
  return yup.string().when(['$selectedBank'], (selectedBank) => {
    return yup
      .string()
      .nullable()
      .max(255, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH)
      .validateAccountNumberLength({ selectedBank })
      .checkAccountNumberWithOtherParameters({ selectedBank });
  });
};

const swiftCodeField = () => {
  return yup.string().when(['$dropDownData', 'bank_name'], (dropDownData, bank_name) => {
    return yup.string().nullable().max(255, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH);
  });
};

const ifscField = () => {
  return yup.string().when(['$selectedBank'], (selectedBank) => {
    return yup
      .string()
      .nullable()
      .checkIsIfscRequired(selectedBank)
      .validateIfscCode({ message: 'Incorrect ifsc code', selectedBank });
  });
};

const bankAddressSchema = yup.object().shape({
  country_id: yup.number().typeError(ERROR_MAPPINGS['bank_address_country_id'].message).nullable(),
  postal_zip_code: yup.string().nullable().max(255, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH),
  address1: yup.string().nullable().max(255, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH),
  address2: yup.string().nullable().max(255, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH),
  address3: yup.string().nullable().max(255, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH),
  address4: yup.string().nullable().max(255, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH),
});

// should be removed after all document can support 12MB
const _validateFileSizeAndType = function (fileObj) {
  return validateFileSizeAndType(fileObj, 5000000);
};

const validateFileSizeAndType = function (fileObj, checkSize = ********) {
  // object exist but is not a File type, that means it is uploaded previously
  if (typeof fileObj === 'object' && !(fileObj instanceof File)) {
    return true;
  }
  if (fileObj && fileObj.size > checkSize) {
    return this.createError({
      path: this.path,
      message: 'File too large',
    });
  }
  if (fileObj && !['image/png', 'image/jpeg', 'application/pdf'].includes(fileObj.type)) {
    return this.createError({
      path: this.path,
      message: 'Unsupported Format',
    });
  }

  return true;
};

export const getSeamanBooksSchema = (shouldValidateAll = false, isDetailsPage = false) => {
  return yup
    .array()
    .compact(function (v, idx, arr) {
      //  arr.length <= 1: hack by mutating the passport value, insert a property isLatest to determine the passport to be validated
      if (shouldValidateAll || arr.length <= 1) {
        v.isLatest = true;
      } else {
        const expiryDateTime = new Date(v.date_of_expiry);
        const latestDate = getLatestDocExpiryDate(arr);
        if (latestDate === 0) {
          v.isLatest = true;
        }
        v.isLatest = latestDate === expiryDateTime.getTime();
      }
      return false;
    })
    .of(
      yup.object({
        isLatest: yup.boolean(),
        is_original: yup.boolean().nullable().default(false),
        number: yup
          .string()
          .when(['isLatest', '$value', '$dropDownData'], (isLatest, value, dropDownData) => {
            const rankName = dropDownData?.ranks?.find((i) => i.id === value.rank_id)?.value;
            if (isLatest && rankName !== RANK_VALUES.SUPY) {
              return yup
                .string()
                .nullable()
                .matches(
                  SEAMAN_BOOK_NUMBER_VALIDATION,
                  ERROR_MAPPINGS['seaman_book_number'].message,
                )
                .required(ERROR_MAPPINGS['seaman_book_number'].message);
            } else {
              return yup.string().nullable();
            }
          }),
        date_of_issue: yup
          .date()
          .when(['isLatest', '$value', '$dropDownData'], (isLatest, value, dropDownData) => {
            const rankName = dropDownData?.ranks?.find((i) => i.id === value.rank_id)?.value;
            if (isLatest && rankName !== RANK_VALUES.SUPY) {
              return yup
                .date()
                .nullable()
                .required('Please enter Date of Issue')
                .test('isValidDate', 'Invalid Date of Issue format', isValidDate)
                .test('isWithRange', 'Invalid Date of Issue format', isWithInRange);
            } else {
              return yup
                .date()
                .nullable()
                .test('isValidDate', 'Invalid Date of Issue format', isValidDate)
                .test('isWithRange', 'Invalid Date of Issue format', isWithInRange);
            }
          }),
        date_of_expiry: yup
          .date()
          .when(
            ['isLatest', 'date_of_issue', '$value', '$dropDownData', 'has_no_date_of_expiry'],
            (isLatest, date_of_issue, value, dropDownData, has_no_date_of_expiry) => {
              if (has_no_date_of_expiry) {
                return yup.date().nullable();
              }
              const rankName = dropDownData?.ranks?.find((i) => i.id === value.rank_id)?.value;
              if (isLatest && rankName != RANK_VALUES.SUPY)
                return date_of_issue
                  ? yup
                      .date()
                      .nullable()
                      .min(
                        yup.ref('date_of_issue'),
                        'Date of Expiry should be later than Date of Issue',
                      )
                      .required('Please enter Date of Expiry')
                      .test('isValidDate', 'Invalid Date of Expiry format', isValidDate)
                      .test('isWithRange', 'Invalid Date of Expiry format', isWithInRange)
                  : yup
                      .date()
                      .nullable()
                      .required('Please enter Date of Expiry')
                      .test('isValidDate', 'Invalid Date of Expiry format', isValidDate)
                      .test('isWithRange', 'Invalid Date of Expiry format', isWithInRange);
              else return yup.date().nullable();
            },
          ),
        country_id: yup
          .number()
          .when(['isLatest', '$value', '$dropDownData'], (isLatest, value, dropDownData) => {
            const rankName = dropDownData?.ranks?.find((i) => i.id === value.rank_id)?.value;
            if (isLatest && rankName !== RANK_VALUES.SUPY) {
              return yup
                .number()
                .nullable()
                .typeError(ERROR_MAPPINGS['seaman_book_country_id'].message)
                .required(ERROR_MAPPINGS['seaman_book_country_id'].message);
            } else {
              return yup.number().nullable();
            }
          }),
        port_of_issue: yup
          .string()
          .when(['isLatest', '$value', '$dropDownData'], (isLatest, value, dropDownData) => {
            const rankName = dropDownData?.ranks?.find((i) => i.id === value.rank_id)?.value;
            if (isLatest && rankName !== RANK_VALUES.SUPY) {
              return yup
                .string()
                .nullable()
                .required(ERROR_MAPPINGS['seaman_book_port_of_issue'].message);
            } else {
              return yup.string().nullable();
            }
          }),
        file: yup
          .mixed()
          .when(['isLatest', '$value', '$dropDownData'], (isLatest, value, dropDownData) => {
            const rankName = dropDownData?.ranks?.find((i) => i.id === value.rank_id)?.value;
            if (isLatest && rankName !== RANK_VALUES.SUPY) {
              return yup
                .mixed()
                .nullable()
                .required('A file is required')
                .test('fileSizeAndFormat', 'File not valid', validateFileSizeAndType);
            } else {
              return yup
                .mixed()
                .nullable()
                .test('fileSizeAndFormat', 'File not valid', validateFileSizeAndType);
            }
          }),
        doc_path: isDetailsPage
          ? yup.mixed().nullable().required(ERROR_MAPPINGS['seaman_book_file'].message)
          : yup.mixed().nullable(),
      }),
    );
};

const seafarerSchema = yup.object({
  rank_id: yup
    .number()
    .typeError(ERROR_MAPPINGS['rank_id'].message)
    .nullable()
    .required(ERROR_MAPPINGS['rank_id'].message),
  office_id: yup
    .number()
    .typeError(ERROR_MAPPINGS['office_id'].message)
    .nullable()
    .required(ERROR_MAPPINGS['office_id'].message),
  manning_agent_id: yup.number().nullable(),
  availability_remarks: yup
    .string()
    .nullable()
    .max(255, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH),
  additional_experience: yup
    .string()
    .nullable()
    .max(255, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH),
  cargo_experience: yup.string().nullable().max(255, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH),
  seafarer_person: yup.object().shape({
    first_name: yup
      .string()
      .nullable()
      .required('Please enter First Name')
      .max(255, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH),
    last_name: yup
      .string()
      .nullable()
      .required('Please enter Last Name')
      .max(255, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH),
    middle_name: yup.string().nullable().max(255, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH),
    gender: yup.string().nullable().required(ERROR_MAPPINGS['gender'].message),
    date_of_birth: yup
      .date()
      .nullable()
      .required('Please enter Date of Birth')
      .test('isValidDate', 'Invalid Date of Birth format', isValidDate)
      .test('isWithRange', 'Invalid Date of Birth format', isWithInRange),
    place_of_birth: yup
      .string()
      .nullable()
      .required('Please enter Place of Birth')
      .max(255, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH),
    country_of_birth_id: yup
      .number()
      .typeError(ERROR_MAPPINGS['country_of_birth_id'].message)
      .nullable()
      .required(ERROR_MAPPINGS['country_of_birth_id'].message),
    nationality_id: yup
      .number()
      .typeError(ERROR_MAPPINGS['nationality_id'].message)
      .nullable()
      .required(ERROR_MAPPINGS['nationality_id'].message),
    passports: yup
      .array()
      .compact(function (v, idx, arr) {
        // hack by mutating the passport value, insert a property isLatest to determine the passport to be validated
        if (arr.length <= 1) {
          v.isLatest = true;
        } else {
          const expiryDateTime = new Date(v.date_of_expiry);
          const latestDate = getLatestDocExpiryDate(arr);
          if (latestDate === 0) {
            v.isLatest = true;
          }
          v.isLatest = latestDate === expiryDateTime.getTime();
        }
        return false;
      })
      .of(
        yup.object({
          isLatest: yup.boolean(),
          number: yup.string().when('isLatest', {
            is: true,
            then: yup
              .string()
              .matches(PASSPORT_NUMBER_VALIDATION, ERROR_MAPPINGS['passport_number'].message)
              .required(ERROR_MAPPINGS['passport_number'].message),
            otherwise: yup.string().nullable(),
          }),
          date_of_issue: yup.date().when('isLatest', {
            is: true,
            then: yup
              .date()
              .nullable()
              .required('Please enter Date of Issue')
              .test('isValidDate', 'Invalid Date of Issue format', isValidDate)
              .test('isWithRange', 'Invalid Date of Issue format', isWithInRange),
            otherwise: yup.date().nullable(),
          }),
          date_of_expiry: yup
            .date()
            .when(['isLatest', 'date_of_issue'], (isLatest, date_of_issue) => {
              if (isLatest) {
                return date_of_issue
                  ? yup
                      .date()
                      .nullable()
                      .min(
                        yup.ref('date_of_issue'),
                        'Date of Expiry should be later than Date of Issue',
                      )
                      .required('Please enter Date of Expiry')
                      .test('isValidDate', 'Invalid Date of Expiry format', isValidDate)
                      .test('isWithRange', 'Invalid Date of Expiry format', isWithInRange)
                  : yup
                      .date()
                      .nullable()
                      .required('Please enter Date of Expiry')
                      .test('isValidDate', 'Invalid Date of Expiry format', isValidDate)
                      .test('isWithRange', 'Invalid Date of Expiry format', isWithInRange);
              } else
                return yup
                  .date()
                  .nullable()
                  .test('isValidDate', 'Invalid Date of Expiry format', isValidDate)
                  .test('isWithRange', 'Invalid Date of Expiry format', isWithInRange);
            }),
          country_id: yup.number().when('isLatest', {
            is: true,
            then: yup
              .number()
              .typeError(ERROR_MAPPINGS['passport_country_id'].message)
              .nullable()
              .required(ERROR_MAPPINGS['passport_country_id'].message),
            otherwise: yup.number().nullable(),
          }),
          place_of_issue: yup.string().when('isLatest', {
            is: true,
            then: yup
              .string()
              .nullable()
              .required(ERROR_MAPPINGS['passport_place_of_issue'].message),
            otherwise: yup.string().nullable(),
          }),
          file: yup.mixed().when('isLatest', {
            is: true,
            then: yup
              .mixed()
              .required('A file is required')
              .test('fileSizeAndFormat', 'File not valid', validateFileSizeAndType),
            otherwise: yup
              .mixed()
              .nullable()
              .test('fileSizeAndFormat', 'File not valid', validateFileSizeAndType),
          }),
          doc_path: yup.mixed().when('isLatest', {
            is: true,
            then: yup.mixed().nullable().required(ERROR_MAPPINGS['passport_file'].message),
            otherwise: yup.mixed().nullable(),
          }),
        }),
      ),
    seaman_books: getSeamanBooksSchema(false, false),
    email_addresses: yup
      .array()
      .of(
        yup.object().shape({
          contact: yup
            .string()
            .required('Please enter email')
            .matches(EMAIL_VALIDATION, 'Invalid email.'),
          contact_type: yup.string().nullable(),
        }),
      )
      .min(1, 'Please enter atleast one email'),
    seafarer_contacts: yup.array().of(
      yup.object().shape({
        contact_type: yup.string().nullable(),
        contact: yup.string().when('contact_type', (contentType) => {
          if (contentType == 'email')
            return yup
              .string()
              .matches(EMAIL_VALIDATION, ERROR_MAPPINGS['email_addresses'])
              .nullable();
          else if (contentType == 'mobile_number')
            return yup
              .string()
              .matches(PHONE_VALIDATION, ERROR_MAPPINGS['mobile_numbers'])
              .nullable();
          else
            return yup
              .string()
              .matches(PHONE_VALIDATION, ERROR_MAPPINGS['telephone_numbers'])
              .nullable();
        }),
      }),
    ),
    mobile_numbers: yup.array().of(phoneSchema(ERROR_MAPPINGS['mobile_numbers'])),
    telephone_numbers: yup.array().of(phoneSchema(ERROR_MAPPINGS['telephone_numbers'])),
    addresses: yup.array().of(
      yup.object().shape({
        postal_zip_code: yup
          .string()
          .nullable()
          .max(255, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH),
        country_id: yup.number().nullable(),
        state: yup.string().nullable().max(255, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH),
        city: yup.string().nullable().max(255, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH),
        building: yup.string().nullable().max(255, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH),
        other_address: yup
          .string()
          .nullable()
          .max(255, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH),
      }),
    ),
    bank_accounts: yup.array().of(
      yup.object().shape({
        seafarer_is_account_holder: yup.boolean().nullable(),
        account_holder_first_name: yup
          .string()
          .nullable()
          .required('Please enter first name')
          .max(255, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH),
        account_holder_last_name: yup
          .string()
          .nullable()
          .max(255, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH),
        account_holder_middle_name: yup
          .string()
          .nullable()
          .max(255, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH),
        account_holder_gender: yup.string().nullable(),
        account_holder_date_of_birth: yup
          .date()
          .nullable()
          .test('isValidDate', 'Invalid Date of Birth format', isValidDate)
          .test('isWithRange', 'Invalid Date of Birth format', isWithInRange),
        account_holder_nationality_id: yup
          .number()
          .typeError(ERROR_MAPPINGS['account_holder_nationality_id'].message)
          .nullable(),
        relationship_with_beneficiary: yup
          .string()
          .when('seafarer_is_account_holder', (seafarer_is_account_holder) => {
            if (seafarer_is_account_holder == 'false') {
              return yup
                .string()
                .nullable()
                .required(ERROR_MAPPINGS['relationship_with_beneficiary'].message)
                .max(255, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH);
            } else {
              return yup.string().nullable().max(255, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH);
            }
          }),
        account_holder_address: bankAddressSchema.nullable(),
        bank_name: yup.string().when('bank_address', (bank_address) => {
          if (bank_address?.country_id) {
            return yup
              .string()
              .required(ERROR_MAPPINGS['bank_name'].message)
              .max(255, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH);
          } else {
            return yup.string().nullable();
          }
        }),
        number: bankAccountField(),
        bank_address: bankAddressSchema.nullable(),
        ifsc_number: ifscField(),
        swift_code: swiftCodeField(),
        iban_number: yup.string().nullable().max(255, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH),
        account_type: yup.string().nullable(),
        fcnr_months: yup
          .string()
          .nullable()
          .when('account_type', (account_type, schema) => {
            if (account_type === 'fcnr') {
              return schema.required('Please select month');
            } else {
              return schema;
            }
          }),
        file: yup
          .mixed()
          .nullable()
          .test('fileSizeAndFormat', 'File not valid', _validateFileSizeAndType),
      }),
    ),

    height: yup.number().nullable().max(9999999, 'Height is too large'),
    weight: yup.number().nullable().max(9999999, 'Weight is too large'),
    overall_size: yup.number().nullable().max(9999999, 'Overall Size is too large'),
    tshirt_size: yup.number().nullable().max(9999999, 'T-shirt Size is too large'),
    jacket_size: yup.number().nullable().max(9999999, 'Jacket Size is too large'),
    shoe_size: yup.number().nullable().max(9999999, 'Shoe Size is too large'),
    nearest_airport: yup.string().nullable().max(255, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH),
    surname_of_spouse: yup
      .string()
      .nullable()
      .max(255, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH),
    name_of_spouse: yup.string().nullable().max(255, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH),
    children_names: yup.string().nullable().max(255, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH),
    number_of_children: yup.number().nullable().max(9999999, 'Number of Children is too large'),
    family_members: yup
      .array()
      .of(
        yup.object().shape({
          surname: yup.string().nullable().max(255, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH),
          name: yup.string().nullable().max(255, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH),
          relationship: yup
            .string()
            .nullable()
            .max(255, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH),
          telephone: multiPhoneField('Next of Kin Invalid Telephone Number.'),
          mobilephone: multiPhoneField('Next of Kin Invalid Mobile Number.'),
          email: yup
            .string()
            .matches(MULTI_EMAIL_VALIDATION, 'Next of Kin Invalid email.')
            .nullable(),
          address: yup
            .object()
            .nullable()
            .shape({
              postal_zip_code: yup
                .string()
                .nullable()
                .max(255, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH),
              country_id: yup.number().nullable(),
              address1: yup
                .string()
                .nullable()
                .max(255, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH),
              address2: yup
                .string()
                .nullable()
                .max(255, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH),
              address3: yup
                .string()
                .nullable()
                .max(255, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH),
              address4: yup
                .string()
                .nullable()
                .max(255, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH),
            }),
          percentage: yup
            .number()
            .nullable()
            .min(1, 'Percentage should be greater than 0')
            .max(100, 'Percentage should be less than 100'),
        }),
    )
      .test({
        name: 'percentageSum',
        test: function test(value) {
          console.log('value', value, this);
          if (!value) return true;
          const totalPercentage = value.reduce((acc, member) => {
            return acc + (member.percentage || 0);
          }, 0);
          if (totalPercentage > 100) {
            return this.createError({
              message: 'Total percentage of all family members should not exceed 100',
            });
          }
          return true;
        },
      }),
    photo: yup
      .mixed()
      .nullable()
      .test('fileSizeAndFormat', 'File not valid', _validateFileSizeAndType),
  }),
});

export const seafarerDocTypeVisaSchema = yup.object({
  type_of_visa: yup.string().nullable().required('Type of Visa is required'),
  visa_region: yup.string().nullable().required('Country is required'),
  number: yup.string().nullable().required('Number is required'),
  date_of_issue: yup
    .date()
    .nullable()
    .test('isValidDate', 'Invalid Date of Issue format', isValidDate)
    .test('isWithRange', 'Invalid Date of Issue format', isWithInRange),
  date_of_expiry: yup
    .date()
    .nullable()
    .min(yup.ref('date_of_issue'), 'Date of Expiry should be later than Date of Issue')
    .test('isValidDate', 'Invalid Date of Expiry format', isValidDate)
    .test('isWithRange', 'Invalid Date of Expiry format', isWithInRange),
  issuing_authority: yup.string().nullable().required('Issuing Authority is required'),
  rejected: yup.string().nullable(),
  file: yup
    .mixed()
    .required('Copy of Visa is required')
    .test('fileSizeAndFormat', 'File not valid', validateFileSizeAndType),
});

export const seafarerDocTypeINDoSSchema = yup.object({
  issued_by: yup.string().nullable(),
  certificate_no: yup.string().nullable().required('Certificate No. is required'),
  date_of_issue: yup
    .date()
    .nullable()
    .test('isValidDate', 'Invalid Date of Issue format', isValidDate)
    .test('isWithRange', 'Invalid Date of Issue format', isWithInRange),
  date_of_expiry: yup
    .date()
    .nullable()
    .min(yup.ref('date_of_issue'), 'Date of Expiry should be later than Date of Issue')
    .test('isValidDate', 'Invalid Date of Expiry format', isValidDate)
    .test('isWithRange', 'Invalid Date of Expiry format', isWithInRange),
  file: yup.mixed().nullable().test('fileSizeAndFormat', 'File not valid', validateFileSizeAndType),
});

export const seafarerDocTypeEndorsementSchema = yup.object({
  endorsement_id: yup.string().nullable().required('Type of Endorsement is required'),
  issued_by: yup.string().nullable(),
  certificate_no: yup.string().nullable(),
  date_of_issue: yup
    .date()
    .nullable()
    .test('isValidDate', 'Invalid Date of Issue format', isValidDate)
    .test('isWithRange', 'Invalid Date of Issue format', isWithInRange),
  date_of_expiry: yup
    .date()
    .nullable()
    .min(yup.ref('date_of_issue'), 'Date of Expiry should be later than Date of Issue')
    .test('isValidDate', 'Invalid Date of Expiry format', isValidDate)
    .test('isWithRange', 'Invalid Date of Expiry format', isWithInRange),
  file: yup.mixed().nullable().test('fileSizeAndFormat', 'File not valid', validateFileSizeAndType),
});

export const seafarerDocTypeVerificationSchema = yup.object({
  type_of_verification: yup.string().nullable().required('Type of Verification is required'),
  date_of_issue: yup
    .date()
    .nullable()
    .test('isValidDate', 'Invalid Date of Issue format', isValidDate)
    .test('isWithRange', 'Invalid Date of Issue format', isWithInRange),
  date_of_expiry: yup
    .date()
    .nullable()
    .min(yup.ref('date_of_issue'), 'Date of Expiry should be later than Date of Issue')
    .test('isValidDate', 'Invalid Date of Expiry format', isValidDate)
    .test('isWithRange', 'Invalid Date of Expiry format', isWithInRange),
  file: yup.mixed().nullable().test('fileSizeAndFormat', 'File not valid', validateFileSizeAndType),
});

export const seafarerDocTypeDceVerificationSchema = yup.object({
  type_of_verification: yup.string().nullable().required('Type of Verification is required'),
  date_of_issue: yup
    .date()
    .nullable()
    .test('isValidDate', 'Invalid Date of Issue format', isValidDate)
    .test('isWithRange', 'Invalid Date of Issue format', isWithInRange),
  date_of_expiry: yup
    .date()
    .nullable()
    .min(yup.ref('date_of_issue'), 'Date of Expiry should be later than Date of Issue')
    .test('isValidDate', 'Invalid Date of Expiry format', isValidDate)
    .test('isWithRange', 'Invalid Date of Expiry format', isWithInRange),
  file: yup.mixed().nullable().test('fileSizeAndFormat', 'File not valid', validateFileSizeAndType),
});

export const seafarerDocTypeDrugAndAlcoholTestSchema = yup.object({
  vessel: yup.string().nullable().required('Vessel is required'),
  date_of_test: yup
    .date()
    .nullable()
    .test('isValidDate', 'Invalid Date of Test format', isValidDate)
    .test('isWithRange', 'Invalid Date of Test format', isWithInRange),
  tester: yup.string().nullable(),
  is_result_failed: yup.bool().nullable(),
  date_of_expiry: yup
    .date()
    .nullable()
    .test('isValidDate', 'Invalid Date of Expiry format', isValidDate)
    .test('isWithRange', 'Invalid Date of Expiry format', isWithInRange)
    .min(yup.ref('date_of_test'), 'Date of Expiry should be later than Date of Issue'),
  file: yup.mixed().nullable().test('fileSizeAndFormat', 'File not valid', validateFileSizeAndType),
});

export const seafarerDocTypeMedicalSchema = yup.object({
  medical_certificate_id: yup.string().nullable().required('Certificate is required'),
  date_of_issue: yup
    .date()
    .nullable()
    .test('isValidDate', 'Invalid Date of Issue format', isValidDate)
    .test('isWithRange', 'Invalid Date of Issue format', isWithInRange),
  issued_by: yup.string().nullable(),
  certificate_no: yup.string().nullable(),
  date_of_expiry: yup
    .date()
    .nullable()
    .min(yup.ref('date_of_issue'), 'Date of Expiry should be later than Date of Issue')
    .test('isValidDate', 'Invalid Date of Expiry format', isValidDate)
    .test('isWithRange', 'Invalid Date of Expiry format', isWithInRange),
  file: yup.mixed().nullable().test('fileSizeAndFormat', 'File not valid', validateFileSizeAndType),
});

export const seafarerDocTypeEducationSchema = yup.object({
  qualification: yup.string().nullable().required('Qualification is required'),
  class: yup.string().nullable(),
  institute: yup.string().nullable(),
  pass_date: yup.string().nullable(),
  file: yup.mixed().nullable().test('fileSizeAndFormat', 'File not valid', validateFileSizeAndType),
});

export const seafarerDocTypeSTCWSchema = yup.object({
  stcw_licence_id: yup.string().nullable().required('Type of STCW is required'),
  issued_by: yup.string().nullable(),
  certificate_no: yup.string().nullable(),
  date_of_issue: yup
    .date()
    .nullable()
    .test('isValidDate', 'Invalid Date of Issue format', isValidDate)
    .test('isWithRange', 'Invalid Date of Issue format', isWithInRange),
  date_of_expiry: yup
    .date()
    .nullable()
    .min(yup.ref('date_of_issue'), 'Date of Expiry should be later than Date of Issue')
    .test('isValidDate', 'Invalid Date of Expiry format', isValidDate)
    .test('isWithRange', 'Invalid Date of Expiry format', isWithInRange),
  file: yup.mixed().nullable().test('fileSizeAndFormat', 'File not valid', validateFileSizeAndType),
});

export const seafarerDocTypeApprenticeshipSchema = yup.object({
  apprenticeship: yup.string().nullable().required('Type of Apprenticeship is required'),
  start_date: yup
    .date()
    .nullable()
    .test('isValidDate', 'Invalid Start Date format', isValidDate)
    .test('isWithRange', 'Invalid Start Date format', isWithInRange),
  end_date: yup
    .date()
    .nullable()
    .min(yup.ref('start_date'), 'Date of Expiry should be later than Date of Issue')
    .test('isValidDate', 'Invalid End Date format', isValidDate)
    .test('isWithRange', 'Invalid End Date format', isWithInRange),
  file: yup.mixed().nullable().test('fileSizeAndFormat', 'File not valid', validateFileSizeAndType),
});

export const seafarerDocTypeTrainingSchema = yup.object({
  course: yup.string().nullable().required('Type of Training is required'),
  institute: yup.string().nullable(),
  grade: yup.string().nullable(),
  start_date: yup
    .date()
    .nullable()
    .test('isValidDate', 'Invalid Start Date format', isValidDate)
    .test('isWithRange', 'Invalid Start Date format', isWithInRange),
  end_date: yup
    .date()
    .nullable()
    .min(yup.ref('start_date'), 'Date of Expiry should be later than Date of Issue')
    .test('isValidDate', 'Invalid End Date format', isValidDate)
    .test('isWithRange', 'Invalid End Date format', isWithInRange),
  file: yup.mixed().nullable().test('fileSizeAndFormat', 'File not valid', validateFileSizeAndType),
});

export const seafarerDocTypePreSeaTrainingSchema = yup.object({
  pre_sea_training_course_id: yup
    .string()
    .nullable()
    .required('Type of Pre Sea Course is required'),
  institute_id: yup.string().nullable().required('Institute is required'),
  grade: yup.string().nullable(),
  start_date: yup
    .date()
    .nullable()
    .test('isValidDate', 'Invalid Start Date format', isValidDate)
    .test('isWithRange', 'Invalid Start Date format', isWithInRange),
  end_date: yup
    .date()
    .nullable()
    .min(yup.ref('start_date'), 'Date of Expiry should be later than Date of Issue')
    .test('isValidDate', 'Invalid End Date format', isValidDate)
    .test('isWithRange', 'Invalid End Date format', isWithInRange),
  file: yup.mixed().nullable().test('fileSizeAndFormat', 'File not valid', validateFileSizeAndType),
});

export const seafarerDocTypeOtherCoursesSchema = yup.object({
  other_course_type_id: yup.string().nullable().required('Type of Other Courses is required'),
  course_title: yup.string().nullable(),
  certificate_no: yup.string().nullable(),
  institute: yup.string().nullable(),
  date_of_issue: yup
    .date()
    .nullable()
    .test('isValidDate', 'Invalid Date of Issue format', isValidDate)
    .test('isWithRange', 'Invalid Date of Issue format', isWithInRange),
  date_of_expiry: yup
    .date()
    .nullable()
    .min(yup.ref('date_of_issue'), 'Date of Expiry should be later than Date of Issue')
    .test('isValidDate', 'Invalid Date of Expiry format', isValidDate)
    .test('isWithRange', 'Invalid Date of Expiry format', isWithInRange),
  file: yup.mixed().nullable().test('fileSizeAndFormat', 'File not valid', validateFileSizeAndType),
});

export const seafarerDocTypeOtherDocumentsSchema = yup.object({
  other_document_type_id: yup.string().nullable().required('Type of Document is required'),
  issued_by: yup.string().nullable(),
  certificate_no: yup.string().nullable(),
  date_of_issue: yup
    .date()
    .nullable()
    .test('isValidDate', 'Invalid Date of Issue format', isValidDate)
    .test('isWithRange', 'Invalid Date of Issue format', isWithInRange),
  date_of_expiry: yup
    .date()
    .nullable()
    .min(yup.ref('date_of_issue'), 'Date of Expiry should be later than Date of Issue')
    .test('isValidDate', 'Invalid Date of Expiry format', isValidDate)
    .test('isWithRange', 'Invalid Date of Expiry format', isWithInRange),
  file: yup.mixed().nullable().test('fileSizeAndFormat', 'File not valid', validateFileSizeAndType),
});

export const seafarerDocTypeCertificationOfCompetencySchema = yup.object({
  country_id: yup.string().nullable().required('Country Name is required'),
  coc_certificate_id: yup.string().nullable().required('Certificate is required'),
  certificate_no: yup.string().nullable(),
  date_of_issue: yup
    .date()
    .nullable()
    .test('isValidDate', 'Invalid Date of Issue format', isValidDate)
    .test('isWithRange', 'Invalid Date of Issue format', isWithInRange),
  date_of_expiry: yup
    .date()
    .nullable()
    .min(yup.ref('date_of_issue'), 'Date of Expiry should be later than Date of Issue')
    .test('isValidDate', 'Invalid Date of Expiry format', isValidDate)
    .test('isWithRange', 'Invalid Date of Expiry format', isWithInRange),
  file: yup.mixed().nullable().test('fileSizeAndFormat', 'File not valid', validateFileSizeAndType),
});

export const seafarerDocTypeCorrespondenceDetailsSchema = yup.object({
  type_of_correspondence_details: yup.string().nullable(),
  date_of_issue: yup
    .date()
    .nullable()
    .test('isValidDate', 'Invalid Date of Issue format', isValidDate)
    .test('isWithRange', 'Invalid Date of Issue format', isWithInRange),
  file: yup.mixed().nullable().test('fileSizeAndFormat', 'File not valid', validateFileSizeAndType),
});

export const seafarerDocTypeUserDefinedDocumentSchema = yup.object({
  user_defined_document_type_id: yup
    .string()
    .nullable()
    .required('Type of User Defined Document is required'),
  short_remark: yup.string().nullable(),
  date_of_issue: yup
    .date()
    .nullable()
    .test('isValidDate', 'Invalid Date of Issue format', isValidDate)
    .test('isWithRange', 'Invalid Date of Issue format', isWithInRange),
  date_of_expiry: yup
    .date()
    .nullable()
    .min(yup.ref('date_of_issue'), 'Date of Expiry should be later than Date of Issue')
    .test('isValidDate', 'Invalid Date of Expiry format', isValidDate)
    .test('isWithRange', 'Invalid Date of Expiry format', isWithInRange),
  file: yup.mixed().nullable().test('fileSizeAndFormat', 'File not valid', validateFileSizeAndType),
});

export const seafarerWagesUpdateSchema = (
  payheadsData,
  isEffectiveDateRequired,
  isEmailDisabled,
  newRank = 0,
) => {
  const res = payheadsData.map((e) => {
    const keyName = `payhead_${e.id}`;
    const result = [
      keyName.toString(),
      yup
        .number()
        .nullable()
        .min(0, 'Amount cannot not be negative')
        .max(9999999.99, 'Amount cannot be more than 9999999.99')
        .test('validAmount', 'Please enter the amount with at most 2 decimal places', (value) =>
          /^(\d+|\d+\.\d{0,2})$/.exec(value + ''),
        ),
    ];
    return result;
  });

  let effective_date;
  if (isEffectiveDateRequired) {
    effective_date = yup
      .date()
      .required('Effective date is required to update wages.')
      .min(
        moment().startOf('month').toDate(),
        'Effective date must be future/past date of current month',
      )
      .test('isValidDate', 'Invalid Effective Date format', isValidDate)
      .test('isWithRange', 'Invalid Effective Date format', isWithInRange);
  } else {
    effective_date = yup
      .date()
      .nullable()
      .test('isValidDate', 'Invalid Effective Date format', isValidDate)
      .test('isWithRange', 'Invalid Effective Date format', isWithInRange);
  }

  let email;
  if (!isEmailDisabled) {
    email = yup
      .string()
      .nullable()
      .matches(EMAIL_VALIDATION, 'Please enter a valid email address.')
      .required('Email address is required to set wages.');
  } else {
    email = yup.string().nullable();
  }

  const payheadValidator = Object.fromEntries(res);
  if (payheadValidator) {
    const schemaObj = {
      effective_date,
      email,
      promotion: yup.boolean().nullable(),
      remarks: yup.string().nullable(),
      new_rank: yup
        .number()
        .nullable()
        .when('promotion', {
          is: true,
          then: yup
            .number()
            .required('New rank is required for promotion.')
            .notOneOf([newRank], 'New rank cannot be the same as current rank.'),
        }),
      new_contract_end_date: yup
        .date()
        .nullable()
        .when('promotion', {
          is: true,
          then: yup
            .date()
            .required('New contract end date is required for promotion.')
            .min(
              yup.ref('effective_date'),
              'New contract end date cannot be less than Effective date',
            )
            .test('isValidDate', 'Invalid New contract end date format', isValidDate)
            .test('isWithRange', 'Invalid New contract end date format', isWithInRange),
        }),
      ...payheadValidator,
    };
    const schema = yup.object().shape(schemaObj);
    return schema;
  }
};

export const vesselEditPlanFormSchema = yup.object({
  vessel_plan_details: yup.array().of(
    yup.object().shape({
      planned_number: yup.number().min(0, 'Number cannot be negative'),
      planned_wages: yup
        .number()
        .nullable()
        .min(0, 'Amount cannot not be negative')
        .max(9999999.99, 'Amount cannot be more than 9999999.99')
        .test('validAmount', 'Please enter the amount with at most 2 decimal places', (value) =>
          /^(\d+|\d+\.\d{0,2})$/.exec(value + ''),
        ),
      planned_nationality: yup.number().nullable(),
    }),
  ),
});

export const seafarerPreJoiningEditSchema = (payheadsData) => {
  const amountValidator = yup
    .number()
    .nullable()
    .min(0, 'Amount cannot not be negative')
    .max(9999999.99, 'Amount cannot be more than 9999999.99')
    .test('validAmount', 'Please enter the amount with at most 2 decimal places', (value) =>
      /^(\d+|\d+\.\d{0,2})$/.exec(value + ''),
    );
  const res = payheadsData.map((e) => {
    const keyName = `amount_${e.id}`;
    const result = [keyName.toString(), amountValidator];
    return result;
  });

  const payheadValidator = Object.fromEntries(res);

  if (payheadValidator) {
    const schemaObj = {
      monthly_allotment: amountValidator,
      first_allotment: amountValidator,
      ...payheadValidator,
    };
    const schema = yup.object().shape(schemaObj);
    return schema;
  }
};

export const seafarerTrainingSchema = (isEdit) => {
  let trainingImparted;
  if (isEdit) {
    trainingImparted = yup
      .string()
      .nullable()
      .required(COMMON_VALIDATION_MSG.TRAINING_IMPARTED)
      .max(250, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH);
  } else {
    trainingImparted = yup.string().nullable();
  }
  let schema = yup.object({
    date_of_issue: yup
      .date()
      .nullable()
      .required('Date Training Recommended is required')
      .test('isValidDate', 'Invalid Date of Issue format', isValidDate)
      .test('isWithRange', 'Invalid Date of Issue format', isWithInRange)
      .test(
        'validateRecommendedDate',
        'Date Training Recommended must be past date or current date',
        validateRecommendedDate,
      ),
    deadline: yup
      .date()
      .nullable()
      .required('Deadline is required')
      .test(
        'validateCompletedDate',
        'Deadline date cannot be before the recommended date.',
        function (value) {
          const { date_of_issue } = this.parent;
          const deadlineDate = moment(value, 'YYYY-MM-DD', true);
          const recommendedDate = moment(date_of_issue, 'YYYY-MM-DD', true);
          if (!deadlineDate.isSameOrAfter(recommendedDate, 'day')) {
            return false;
          }
          return true;
        },
      )
      .test('isValidDate', 'Invalid Deadline date format', isValidDate)
      .test('isWithRange', 'Invalid Deadline date format', isWithInRange),
    vessel_ownership_id: yup.string().nullable().required(COMMON_VALIDATION_MSG.VESSEL_NAME),
    completed_date: yup
      .date()
      .nullable()
      .test('isValidDate', 'Invalid Date of completed format', isValidDate)
      .test('isWithRange', 'Invalid Date of completed format', isWithInRange)
      .test(
        'validateCompletedDate',
        'Completed date cannot be before the recommended date.',
        function (value) {
          if (isEdit) {
            const { date_of_issue } = this.parent;
            const completedDate = moment(value, 'YYYY-MM-DD', true);
            const recommendedDate = moment(date_of_issue, 'YYYY-MM-DD', true);
            if (!completedDate.isSameOrAfter(recommendedDate, 'day')) {
              return false;
            }
          }
          return true;
        },
      ),
    training_needs: yup
      .string()
      .nullable()
      .required(COMMON_VALIDATION_MSG.TRAINING_NEED)
      .max(250, COMMON_VALIDATION_MSG.EXCEED_MAXIMUM_LENGTH),
    training_imparted: trainingImparted,
    supporting_document: yup
      .mixed()
      .test('fileSizeAndFormat', 'File not valid', validateFileSizeAndType),
    superintendent_report: yup
      .mixed()
      .test('fileSizeAndFormat', 'File not valid', validateFileSizeAndType),
  });

  return schema;
};

export const addSimulatedPortSchema = yup.object({
  country_name: yup.string().required('Country is required'),
  port_id: yup.string().required('Port is required'),
  date_of_arrival: yup
    .date()
    .nullable()
    .required('Date Estimated Date of Arrival is required')
    .min(
      moment().startOf('day').toDate(),
      'Estimated Date of Arrival must be future date of current date',
    )
    .test('isValidDate', 'Invalid Date of Issue format', isValidDate)
    .test('isWithRange', 'Invalid Date of Issue format', isWithInRange),
});

export const updateContractEndDateSchema = yup.object({
  remarks: yup.string().nullable(),
  contract_end_date: yup
    .date()
    .required('Contract End Date is required')
    .min(moment().startOf('day').toDate(), 'Contract End Date must be future date of current date')
    .test('isValidDate', 'Invalid Date of Issue format', isValidDate)
    .test('isWithRange', 'Invalid Date of Issue format', isWithInRange),
});

export const setContractDetailsModalSchema = (canAccessBackDate) => {
  return yup.object({
    expected_contract_start_date: yup
      .date()
      .required('Expected Contract Start Date is required')
      .min(
        canAccessBackDate ? moment().startOf('day').subtract(1, 'month').toDate() : moment().startOf('day').toDate(), 
        canAccessBackDate ? 'Expected Contract Start Date can only be one month back' : 'Expected Contract Start Date must be future date of current date'
      )
      .test('isValidDate', 'Invalid Date of Issue format', isValidDate)
      .test('isWithRange', 'Invalid Date of Issue format', isWithInRange),
    contract_length: yup.number().required('Contract Length is required'),
    repatriation_country_name: yup.string().required('Repatriation Country is required'),
    repatriation_port_id: yup.number().required('Repatriation Port is required'),
    embarkation_country_name: yup.string().required('Embarkation Country is required'),
    embarkation_port_id: yup.number().required('Embarkation Port is required'),
    nominee: yup.number().required('Nominee is required'),
  })
};

export default seafarerSchema;
