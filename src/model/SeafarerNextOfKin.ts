import { valueOrDash, capitalizeArgs, Dash, getAddress } from './utils';

const SeafarerNextOfKin = (family_member, seafarerContactDetailsHidden) => {
  const name = valueOrDash(family_member, ['name'], true);
  const surName = valueOrDash(family_member, ['surname'], true);

  const fullName = capitalizeArgs(name, surName) || Dash;

  const kinAddress = family_member.address || {};
  const address = getAddress(kinAddress);
  const percentage = family_member.percentage ? family_member.percentage + '%' : Dash;
  const seafarerNextOfKinData = [
    {
      label: 'Name of Next of Kin',
      value: fullName,
    },
    {
      label: 'Relationship',
      value: valueOrDash(family_member, ['relationship'], true),
    },
    {
      label: 'Telephone No.',
      value: valueOrDash(family_member, ['telephone']),
      phoneAutoFormat: true,
    },
    {
      label: 'Mobile No.',
      value: valueOrDash(family_member, ['mobilephone']),
      phoneAutoFormat: true,
    },
    {
      label: 'Email Address',
      value: valueOrDash(family_member, ['email']),
      emailType: true,
    },
    {
      label: 'Address of Next of Kin',
      value: address,
    },
    {
      label: 'Percentage',
      value: percentage,
    },
  ];

  return !seafarerContactDetailsHidden ? seafarerNextOfKinData : seafarerNextOfKinData.splice(0, 2);
};

export default SeafarerNextOfKin;
