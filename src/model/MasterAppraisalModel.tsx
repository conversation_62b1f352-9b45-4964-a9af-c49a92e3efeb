import React from 'react';
import { OverlayTrigger, Tooltip } from 'react-bootstrap';
import { dateAsString, stringAsDate, Dash } from '../model/utils';
import VesselNameLink from '../component/CrewList/VesselNameLink';
import { showPassFailColorClass, showGradeInText, getGradeColor } from '../util/appraisal';
import { MASTER_APPRAISAL_GRADE_SCALE } from '../constants/appraisal';
import { toNumber } from 'lodash';
// eslint-disable-next-line no-unused-vars
import { MasterAppraisalGetResponse } from '../types/masterAppraisal';

const MasterAppraisalModel = (masterDetail: MasterAppraisalGetResponse, dropDownData: any) => {
  const combinedGradeString = (average_score: string | number) => {
    let score_string = average_score + ' (' + showGradeInText(toNumber(average_score)) + ')';
    return score_string;
  };

  const renderTooltip = (props: any) => (
    <Tooltip id="appraisal-tooltip" {...props}>
      <table>
        <tbody>
          {MASTER_APPRAISAL_GRADE_SCALE.map((i) => {
            return (
              <tr key={i.id}>
                <td className="text-left">{i.label}</td>
                <td className="text-left pl-3">{i.range}</td>
              </tr>
            );
          })}
        </tbody>
      </table>
    </Tooltip>
  );

  const getRankValue = (rankId: number) => {
    return dropDownData?.ranks.find((element: { id: any }) => element.id == rankId)?.value;
  };

  return {
    primaryLeft: [
      {
        label: 'Vessel',
        value: (
          <VesselNameLink
            ownershipId={masterDetail?.vessel_ownership_id}
            vesselName={masterDetail?.vessel_name}
            eventTracker={() => {}}
            className="pl-0 master-appraisal-vessel-name"
          />
        ),
      },
      {
        label: 'Rank',
        value: getRankValue(masterDetail?.rank_id) ?? Dash,
      },
      {
        label: 'Submitted Date',
        value: dateAsString(stringAsDate(masterDetail?.created_at)) ?? Dash,
      },
      {
        label: 'Period of Appraisal',
        value:
          masterDetail?.start_date && masterDetail?.end_date
            ? dateAsString(stringAsDate(masterDetail.start_date)) +
              ' - ' +
              dateAsString(stringAsDate(masterDetail.end_date))
            : Dash,
      },
      {
        label: 'Occasion for Report',
        value: masterDetail?.master_appraisal_reason ?? Dash,
      },
      {
        label: 'Training Needs',
        value: <div className="text-wrap">{masterDetail?.training_needs ?? Dash}</div>,
      },
    ],
    primaryRight: [
      {
        label: 'Overall Grade',
        value: (
          <OverlayTrigger
            placement="bottom"
            delay={{ show: 250, hide: 400 }}
            overlay={renderTooltip}
          >
            <div
              className={`supt-appraisal-score ${getGradeColor(
                masterDetail?.overall_grade,
              )} font-weight-bold col-6 pl-0`}
            >
              {combinedGradeString(masterDetail?.overall_grade) ?? Dash}
            </div>
          </OverlayTrigger>
        ),
      },
      {
        label: 'Promotion Recommended',
        value: (
          <div
            className={`${showPassFailColorClass(
              masterDetail?.is_promotion_recommended,
            )} font-weight-bold`}
          >
            {masterDetail?.is_promotion_recommended ? 'Yes' : 'No'}
          </div>
        ),
      },
      {
        label: 'Work Necessary for Promotion',
        value: masterDetail?.promotion_requirement ?? Dash,
      },
      {
        label: 'Recommended Rank',
        value: getRankValue(masterDetail?.new_rank_id) ?? Dash,
      },
      {
        label: 'Drinking Status',
        value: masterDetail?.master_appraisal_drinking_status ?? Dash,
      },
    ],
    primaryLeftPdfVersion: [
      {
        label: 'Vessel',
        value: masterDetail?.vessel_name ?? Dash,
      },
      {
        label: 'Rank',
        value: getRankValue(masterDetail?.rank_id) ?? Dash,
      },
      {
        label: 'Submitted Date',
        value: dateAsString(stringAsDate(masterDetail?.created_at)) ?? Dash,
      },
      {
        label: 'Period of Appraisal',
        value:
          masterDetail?.start_date && masterDetail?.end_date
            ? dateAsString(stringAsDate(masterDetail.start_date)) +
              ' - ' +
              dateAsString(stringAsDate(masterDetail.end_date))
            : Dash,
      },
      {
        label: 'Occasion for Report',
        value: masterDetail?.master_appraisal_reason ?? Dash,
      },
      {
        label: 'Training Needs',
        value: <div className="text-wrap">{masterDetail?.training_needs ?? Dash}</div>,
      },
    ],
    primaryRightPdfVersion: [
      {
        label: 'Overall Grade',
        value: (
          <div
            className={`supt-appraisal-score ${getGradeColor(
              masterDetail?.overall_grade,
            )} font-weight-bold`}
          >
            {combinedGradeString(masterDetail?.overall_grade) ?? Dash}
          </div>
        ),
      },
      {
        label: 'Promotion Recommended',
        value: (
          <div
            className={`${showPassFailColorClass(
              masterDetail?.is_promotion_recommended,
            )} font-weight-bold`}
          >
            {masterDetail?.is_promotion_recommended ? 'Yes' : 'No'}
          </div>
        ),
      },
      {
        label: 'Work Necessary for Promotion',
        value: masterDetail?.promotion_requirement ?? Dash,
      },
      {
        label: 'Recommended Rank',
        value: getRankValue(masterDetail?.new_rank_id) ?? Dash,
      },
      {
        label: 'Drinking Status',
        value: masterDetail?.master_appraisal_drinking_status ?? Dash,
      },
    ],
    secondaryLeft: [
      {
        label: 'Master',
        value: masterDetail?.master_name ?? Dash,
      },
      {
        label: "Master's Comment",
        value: masterDetail?.master_comment ?? Dash,
      },
    ],
    secondaryRight: [
      {
        label: 'Head of Department',
        value: masterDetail?.head_name ?? Dash,
      },
      {
        label: "Head of Department's Comment",
        value: masterDetail?.head_comment ?? Dash,
      },
      {
        label: "Crew's Comment",
        value: masterDetail?.crew_comment ?? Dash,
      },
    ],
    gradingTable: [
      {
        label: 'Outstanding',
        value: '20',
      },
      {
        label: 'Very Good',
        value: '16 - 19',
      },
      {
        label: 'Good',
        value: '12 - 15',
      },
      {
        label: 'Fair',
        value: '8 - 11',
      },
      {
        label: 'Poor',
        value: '4 - 7',
      },
      {
        label: 'Very Poor',
        value: '0 - 3',
      },
    ],
  };
};

export default MasterAppraisalModel;
