import { valueOrDash, dateOrDash } from './utils';

const SeafarerPassport = (passport) => {
  let fileName = '- - -';
  const document =
    passport.document && passport.document.length > 0 ? passport.document[0] : undefined;
  const country = passport.country ? passport.country.value : undefined;

  if (document) {
    const extension = passport.doc_path ? passport.doc_path.split('.').pop(): '';
    if (country) {
      fileName = `${country}_Passport.${extension}`;
    } else {
      fileName = `Passport.${extension}`;
    }
  }

  return [
    {
      label: 'Passport No.',
      value: valueOrDash(passport, ['number']),
    },
    {
      label: 'Date of Issue',
      value: dateOrDash(passport, ['date_of_issue']),
    },
    {
      label: 'Date of Expiry',
      value: dateOrDash(passport, ['date_of_expiry']),
    },
    {
      label: 'Place of Issue',
      value: valueOrDash(passport, ['place_of_issue'], true),
    },
    {
      label: 'Country of Issue',
      value: valueOrDash(passport, ['country', 'value']),
    },
    {
      label: 'Document',
      value: fileName,
      document: document,
      documentType: 'passport',
    },
  ];
};

export default SeafarerPassport;
