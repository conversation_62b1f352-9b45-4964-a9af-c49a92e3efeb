import React from 'react';
import { ExpiryDateStyling } from '../component/common/ExpiryDateStyling';
import { dateAsString, stringAsDate } from '../model/utils';


export const Medical = [
    {
        Header: 'Issued By',
        id: 'issued-by',
        accessor: function accessor(row) { return (
            <div>
                {(row?.seafarer_doc_medical?.issued_by || row?.seafarer_doc_drug_alcohol_test?.tester) ?? '---'}
            </div>
        )},
        disableSortBy: true,
        maxWidth: 100,
    },
    {
        Header: 'Certificate No./Result',
        id: 'certificate-no',
        accessor: function accessor(row) { return (
            <div>
                {row?.seafarer_doc_medical?.certificate_no || (row?.seafarer_doc_drug_alcohol_test && (row.seafarer_doc_drug_alcohol_test?.is_result_failed ? 'Failed' : 'Passed'))}
            </div>
        )},
        disableSortBy: true,
        maxWidth: 100,
    },
    {
        Header: 'Date of Issue/Test',
        id: 'date-of-issue',
        accessor: function accessor(row) { return (
            <div>
                {(row?.seafarer_doc_medical?.date_of_issue || row?.seafarer_doc_drug_alcohol_test?.date_of_test) ? dateAsString(dateAsString(row?.seafarer_doc_medical?.date_of_issue || row?.seafarer_doc_drug_alcohol_test?.date_of_test)) : ''}
            </div>
        )},
        disableSortBy: true,
        maxWidth: 100,
    },
    {
        Header: 'Date of Expiry',
        id: 'date-of-expiry',
        accessor: function accessor(row) { return (
            <div>
                {row?.seafarer_doc_medical?.date_of_expiry || row?.seafarer_doc_drug_alcohol_test?.date_of_expiry ? <ExpiryDateStyling value={row?.seafarer_doc_medical?.date_of_expiry || row?.seafarer_doc_drug_alcohol_test?.date_of_expiry} /> : '---'}
            </div>
        )},
        disableSortBy: true,
        maxWidth: 100,
    },

];

export const CertificatesOfCompentency = [
    {
        Header: 'Certificate No',
        id: 'certificate-no',
        accessor: function accessor(row) { return (
            <div className={row?.seafarer_doc_certificate_of_competency?.is_original ? 'font-weight-bold' : ''}>               
                {row?.seafarer_doc_certificate_of_competency?.certificate_no}
            </div>
        )},
        disableSortBy: true,
        maxWidth: 100,
        order: 3
    },
    {
        Header: 'Date of Issue',
        id: 'date-of-issue',
        accessor: function accessor(row) { return (
            <div className={row?.seafarer_doc_certificate_of_competency?.is_original ? 'font-weight-bold' : ''}>               
                {row?.seafarer_doc_certificate_of_competency?.date_of_issue ? dateAsString(stringAsDate(row.seafarer_doc_certificate_of_competency.date_of_issue)) : ''}
            </div>
        )},
        disableSortBy: true,
        maxWidth: 100,
        order: 4
    },
    {
        Header: 'Date of Expiry',
        id: 'date-of-expiry',
        accessor: function accessor(row) { return (
            <div>
                {row?.seafarer_doc_certificate_of_competency?.date_of_expiry ? <ExpiryDateStyling value={row.seafarer_doc_certificate_of_competency.date_of_expiry} /> : '---'}
            </div>
        )},
        disableSortBy: true,
        maxWidth: 100,
        order: 5
    },
];

export const STCW = [
    {
        Header: 'Issued By',
        id: 'issued ',
        accessor: function accessor(row) { return (
            <div>
                {row?.seafarer_doc_stcw?.issued_by}
            </div>
        )},
        disableSortBy: true,
        maxWidth: 100,
    },
    {
        Header: 'Certificate No',
        id: 'certificate-no',
        accessor: function accessor(row) { return (
            <div>
                {row?.seafarer_doc_stcw?.certificate_no}
            </div>
        )},
        disableSortBy: true,
        maxWidth: 100,
    },
    {
        Header: 'Date of Issue',
        id: 'date-of-issue',
        accessor: function accessor(row) { return (
            <div>
                {row?.seafarer_doc_stcw?.date_of_issue ? dateAsString(stringAsDate(row.seafarer_doc_stcw.date_of_issue)) : ''}
            </div>
        )},
        disableSortBy: true,
        maxWidth: 100,
    },
    {
        Header: 'Date of Expiry',
        id: 'date-of-expiry',
        accessor: function accessor(row) { return (
            <div>
                {row?.seafarer_doc_stcw?.date_of_expiry ? <ExpiryDateStyling value={row.seafarer_doc_stcw.date_of_expiry} /> : '---'}
            </div>
        )},
        disableSortBy: true,
        maxWidth: 100
    },
];

export const Education = [
    {
        Header: 'Qualification',
        id: 'qualification',
        accessor: function accessor(row) { return (
            <div>
                {row?.seafarer_doc_education?.qualification}
            </div>
        )},
        disableSortBy: true,
        maxWidth: 100,
    },
    {
        Header: 'Institute',
        id: 'institute ',
        accessor: function accessor(row) { return (
            <div>
                {row?.seafarer_doc_education?.institute}
            </div>
        )},
        disableSortBy: true,
        maxWidth: 100,
    },
    {
        Header: 'Pass Date',
        id: 'pass-date',
        accessor: function accessor(row) { return (
            <div>
                {row?.seafarer_doc_education?.pass_date ? dateAsString(stringAsDate(row.seafarer_doc_education.pass_date)) : ''}
            </div>
        )},
        disableSortBy: true,
        maxWidth: 100,
    },
    {
        Header: 'class',
        id: 'class',
        accessor: function accessor(row) { return (
            <div>
                {row?.seafarer_doc_education?.class}
            </div>
        )},
        disableSortBy: true,
        maxWidth: 100,
    }
];

export const Apprenticeship = [
    {
        Header: 'Apprenticeship',
        id: 'apprenticeship',
        accessor: function accessor(row) { return (
            <div>
                {row?.seafarer_doc_apprenticeship?.apprenticeship}
            </div>
        )},
        disableSortBy: true,
        maxWidth: 100,
    },
    {
        Header: 'Start Date',
        id: 'start-date ',
        accessor: function accessor(row) { return (
            <div>
                {dateAsString(stringAsDate(row?.seafarer_doc_apprenticeship?.start_date)) ?? ''}
            </div>
        )},
        disableSortBy: true,
        maxWidth: 100,
    },
    {
        Header: 'End Date',
        id: 'end-date',
        accessor: function accessor(row) { return (
            <div>
                {dateAsString(stringAsDate(row?.seafarer_doc_apprenticeship?.end_date))}
            </div>
        )},
        disableSortBy: true,
        maxWidth: 100,
    }
];

export const Training = [
    {
        Header: 'Start Date',
        id: 'start-date ',
        accessor: function accessor(row) { return (
            <div>
                {(row?.seafarer_doc_training?.start_date || row?.seafarer_doc_pre_sea_training?.start_date) ? dateAsString(stringAsDate(row?.seafarer_doc_training?.start_date || row?.seafarer_doc_pre_sea_training?.start_date)) : ''}
            </div>
        )},
        disableSortBy: true,
        maxWidth: 100
    },
    {
        Header: 'End Date',
        id: 'end-date',
        accessor: function accessor(row) { return (
            <div>
                {(row?.seafarer_doc_training?.end_date || row?.seafarer_doc_pre_sea_training?.end_date) ? dateAsString(stringAsDate(row?.seafarer_doc_training?.end_date || row?.seafarer_doc_pre_sea_training?.end_date)) : ''}
            </div>
        )},
        disableSortBy: true,
        maxWidth: 100
    },
    {
        Header: 'Grade',
        id: 'grade',
        accessor: function accessor(row) { return (
            <div>
                {row?.seafarer_doc_training?.grade || row?.seafarer_doc_pre_sea_training?.grade}
            </div>
        )},
        disableSortBy: true,
        maxWidth: 100
    },

];

export const OtherCourses = [
    {
        Header: 'Course',
        id: 'course',
        disableSortBy: true,
        accessor: function accessor(row) { return (
            <div>
                {row?.seafarer_doc_other_course?.course_title}
            </div>
        )},
        maxWidth: 100
    },
    {
        Header: 'Institute',
        id: 'institute',
        accessor: function accessor(row) { return (
            <div>
                {row?.seafarer_doc_other_course?.institute}
            </div>
        )},
        disableSortBy: true,
        maxWidth: 100
    },
    {
        Header: 'Certificate No.',
        id: 'certificate-no',
        accessor: function accessor(row) { return (
            <div>
                {row?.seafarer_doc_other_course?.certificate_no}
            </div>
        )},
        disableSortBy: true,
        maxWidth: 100
    },
    {
        Header: 'Date of Issue',
        id: 'date-of-issue',
        accessor: function accessor(row) { return (
            <div>
                {row?.seafarer_doc_other_course?.date_of_issue ? dateAsString(stringAsDate(row.seafarer_doc_other_course.date_of_issue)) : ''}
            </div>
        )},
        disableSortBy: true,
        maxWidth: 100
    },
    {
        Header: 'Date of Expiry',
        id: 'date-of-expiry',
        accessor: function accessor(row) { return (
            <div>
                {dateAsString(stringAsDate(row?.seafarer_doc_other_course?.date_of_expiry))}
            </div>
        )},
        disableSortBy: true,
        maxWidth: 100
    }
];

export const OtherDocuments = [
    {
        Header: 'Issued By',
        id: 'issued-by',
        accessor: function accessor(row) { return (
            <div>
                {row?.seafarer_doc_other_document?.issued_by}
            </div>
        )},
        disableSortBy: true,
        maxWidth: 100
    },
    {
        Header: 'Certificate No',
        id: 'certificate-no',
        accessor: function accessor(row) { return (
            <div>
                {row?.seafarer_doc_other_document?.certificate_no}
            </div>
        )},
        disableSortBy: true,
        maxWidth: 100
    },
    {
        Header: 'Date of Issue',
        id: 'date-of-issue',
        accessor: function accessor(row) { return (
            <div>
                {row?.seafarer_doc_other_document?.date_of_issue ? dateAsString(stringAsDate(row.seafarer_doc_other_document.date_of_issue)) : ''}
            </div>
        )},
        disableSortBy: true,
        maxWidth: 100
    },
    {
        Header: 'Date of Expiry',
        id: 'date-of-expiry',
        accessor: function accessor(row) { return (
            <div>
                {row?.seafarer_doc_other_document?.date_of_expiry ? <ExpiryDateStyling value={row.seafarer_doc_other_document.date_of_expiry} /> : '---'}
            </div>
        )},
        disableSortBy: true,
        maxWidth: 100
    }
];

export const CorrespondenceDetails = [
    {
        Header: 'Type of Correspondence Details',
        id: 'type_of_correspondence_details',
        accessor: function accessor(row) { return (
            <div>
                {row?.seafarer_doc_correspondence_details?.type_of_correspondence_details}
            </div>
        )},
        disableSortBy: true,
        maxWidth: 100,
    },
    {
        Header: 'Date of Issue',
        id: 'date-of-issue',
        accessor: function accessor(row) {return (
            <div>
                {dateAsString(stringAsDate(row?.seafarer_doc_correspondence_details?.date_of_issue)) ?? ''}
            </div>
        )},
        disableSortBy: true,
        maxWidth: 100,
    },
];

export const UserDefinedDocs = [
  {
    Header: 'Date',
    id: 'date',
    accessor: function accessor(row) {
      return (
        <div>
          {dateAsString(stringAsDate(row?.seafarer_doc_user_defined_document?.date_of_issue)) ?? ''}
        </div>
      );
    },
    disableSortBy: true,
    maxWidth: 100,
  },
  {
    Header: 'Short Remark',
    id: 'short-remark',
    accessor: function accessor(row) {
      return <div>{row?.seafarer_doc_user_defined_document?.short_remark}</div>;
    },
    disableSortBy: true,
    maxWidth: 100,
  },
  {
    Header: 'Date of Expiry',
    id: 'date-of-expiry',
    accessor: function accessor(row) {
      return (
        <div>
          {dateAsString(stringAsDate(row?.seafarer_doc_user_defined_document?.date_of_expiry)) ??
            ''}
        </div>
      );
    },
    disableSortBy: true,
    maxWidth: 100,
  },
];