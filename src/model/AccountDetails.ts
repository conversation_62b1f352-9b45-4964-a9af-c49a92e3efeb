import { valueOrDash, dateOrDash, capitalizeFirstLetter, capitalizeArgs, Dash } from './utils';

const AccountDetails = (data) => {
  const getAddress = (addressObj) => {
    if (Object.keys(addressObj).length === 0) return '- - -';

    const address =
      capitalizeArgs(
        addressObj.address1,
        addressObj.address2,
        addressObj.address3,
        addressObj.address4,
        addressObj.postal_zip_code,
      ) || Dash;

    return address;
  };

  const primaryPayrollAccount = valueOrDash(data, ['is_primary_payroll_account']) ? 'Yes' : 'No';

  const firstName = valueOrDash(data, ['account_holder_first_name'], true);
  const middleName = valueOrDash(data, ['account_holder_middle_name'], true);
  const lastName = valueOrDash(data, ['account_holder_last_name'], true);
  const fullName = capitalizeArgs(firstName, middleName, lastName) || Dash;

  const kinAddress = data?.bank_address || {};
  const bankAddress = getAddress(kinAddress);

  const kAcctHolderAddress = data?.account_holder_address || {};
  const acctHolderAddress = getAddress(kAcctHolderAddress);

  let fileName = '- - -';
  const document = data?.document && data?.document.length > 0 ? data?.document[0] : undefined;

  if (document) {
    fileName = document.name;
  }

  const defaultDetailsList = [
    {
      label: 'Bank Name',
      value: valueOrDash(data, ['bank_name']),
      isSectionHeader: true,
    },
    {
      label: 'Primary Payroll Account',
      value: primaryPayrollAccount,
    },
    {
      label: 'Account No.',
      value: valueOrDash(data, ['number']),
    },
    {
      label: 'Bank Address',
      value: bankAddress,
    },
    {
      label: 'IFSC Number',
      value: valueOrDash(data, ['ifsc_number']),
    },
    {
      label: 'SWIFT Code',
      value: valueOrDash(data, ['swift_code']),
    },
    {
      label: 'Account Type',
      value: valueOrDash(data, ['account_type']),
    },
    {
      label: 'IBAN Number',
      value: valueOrDash(data, ['iban_number']),
    },
    {
      label: 'Account Holder Name',
      value: fullName,
    },
    {
      label: 'Gender',
      value: capitalizeFirstLetter(valueOrDash(data, ['account_holder_gender'])),
    },
    {
      label: 'Date of Birth',
      value: dateOrDash(data, ['account_holder_date_of_birth']),
    },
    {
      label: 'Place of Birth',
      value: valueOrDash(data, ['account_holder_birth_place']),
    },
    {
      label: 'Account Holder Contact number-1',
      value: valueOrDash(data, ['contact_number_1']),
    },
    {
      label: 'Account Holder Contact number-2',
      value: valueOrDash(data, ['contact_number_2']),
    },

    {
      label: 'Intermediary Bank SWIFT Code',
      value: valueOrDash(data, ['bank_swift_code']),
    },
    {
      label: 'Intermediary Bank Name',
      value: valueOrDash(data, ['intermediary_bank_name']),
    },
    {
      label: 'Intermediary Bank Address',
      value: valueOrDash(data, ['intermediary_bank_address']),
    },

    {
      label: 'Intermediary Bank Account Number',
      value: valueOrDash(data, ['intermediary_bank_account_number']),
    },

    {
      label: 'Special Remittance instructions',
      value: valueOrDash(data, ['special_remittance_instructions']),
    },
    {
      label: 'Remarks',
      value: valueOrDash(data, ['remarks']),
    },
    {
      label: 'Nationality',
      value: valueOrDash(data, ['account_holder_nationality', 'value']),
    },
    {
      label: 'Relationship with Beneficiary',
      value: valueOrDash(data, ['relationship_with_beneficiary']),
    },
    {
      label: 'Address',
      value: acctHolderAddress,
    },
    {
      label: 'Proof of Bank Account',
      value: fileName,
      document: document,
      documentType: 'bank_account',
    },
  ];

  if (
    valueOrDash(data, ['account_holder_nationality', 'value']) === 44 &&
    data.bank_address.country_id === 44
  ) {
    const nationalityIndex = defaultDetailsList.findIndex((data) => data.label === 'Nationality');
    defaultDetailsList[nationalityIndex + 1] = {
      label: 'CNAPS',
      value: valueOrDash(data, ['cnaps']),
    };
  }

  if (valueOrDash(data, ['account_type']) === 'fcnr') {
    const acountTypeIndex = defaultDetailsList.findIndex((data) => data.label === 'Account Type');
    defaultDetailsList[acountTypeIndex + 1] = {
      label: 'Number of months',
      value: valueOrDash(data, ['fcnr_months']),
    };
  }

  return defaultDetailsList;
};

export default AccountDetails;
