import { valueOrDash } from './utils';
const VesselParticularViewData = (vessel) => [
  {
    label: 'Life Boat Capacity',
    value: valueOrDash(vessel, ['life_boat_capacity']),
  },
  {
    label: 'Length O.A.',
    value: valueOrDash(vessel, ['length_oa']),
  },
  {
    label: 'Length BP',
    value: valueOrDash(vessel, ['length_bp']),
  },
  {
    label: 'Depth',
    value: valueOrDash(vessel, ['depth']),
  },
  {
    label: 'Breadth (extreme)',
    value: valueOrDash(vessel, ['breadth_extreme']),
  },
  {
    label: 'Summer Draft',
    value: valueOrDash(vessel, ['summer_dwt']),
  },
  {
    label: 'Summer FWT',
    value: '',
  },
  {
    label: 'International GRT',
    value: valueOrDash(vessel, ['international_grt']),
  },
  {
    label: 'International NRT',
    value: valueOrDash(vessel, ['international_nrt']),
  },
  {
    label: 'Service Speed',
    value: valueOrDash(vessel, ['service_speed']),
  },
];

export default VesselParticularViewData;
