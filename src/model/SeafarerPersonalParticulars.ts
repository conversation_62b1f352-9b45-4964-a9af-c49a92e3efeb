import { valueOrDash, Dash, capitalizeArgs, capitalizeFirstLetter } from './utils';

const SmokerMapping = {
  yes: 'Smoker',
  no: 'Non Smoker',
};

const SeafarerPersonalParticulars = (seafarer) => {
  let height = valueOrDash(seafarer, ['seafarer_person', 'height']);
  if (height !== Dash) {
    height += ' cm';
  }

  let weight = valueOrDash(seafarer, ['seafarer_person', 'weight']);
  if (weight !== Dash) {
    weight += ' kg';
  }

  let shoeSize = valueOrDash(seafarer, ['seafarer_person', 'shoe_size']);
  if (shoeSize !== Dash) {
    shoeSize = 'USA ' + shoeSize;
  }

  let smoking = valueOrDash(seafarer, ['seafarer_person', 'smoking']);
  if (smoking !== Dash) {
    smoking = SmokerMapping[smoking];
  }

  const spouseFirstName = valueOrDash(seafarer, ['seafarer_person', 'name_of_spouse'], true);
  const spouseLastName = valueOrDash(seafarer, ['seafarer_person', 'surname_of_spouse'], true);
  const maritalStatus = seafarer?.seafarer_person?.marital_status ? capitalizeFirstLetter(seafarer.seafarer_person.marital_status) : 'Single';

  const spouseFullName = capitalizeArgs(spouseFirstName, spouseLastName) || Dash;

  const result = [
    {
      label: 'Height',
      value: height,
    },
    {
      label: 'Weight',
      value: weight,
    },
    {
      label: 'Overall Size',
      value: valueOrDash(seafarer, ['seafarer_person', 'overall_size']),
    },
    {
      label: 'T-shirt Size',
      value: valueOrDash(seafarer, ['seafarer_person', 'tshirt_size']),
    },
    {
      label: 'Jacket Size',
      value: valueOrDash(seafarer, ['seafarer_person', 'jacket_size']),
    },
    {
      label: 'Shoe Size',
      value: shoeSize,
    },
    {
      label: 'Smoking',
      value: smoking,
    },
    {
      label: 'Vegetarian',
      value: valueOrDash(seafarer, ['seafarer_person', 'vegetarian'], true),
    },
    {
      label: 'Nearest Airport',
      value: valueOrDash(seafarer, ['seafarer_person', 'nearest_airport'], true),
    },
    {
      label: 'Marital Status',
      value: maritalStatus,
    },
    {
      label: 'Name of Spouse',
      value: spouseFullName,
    },
    {
      label: 'Number of Children',
      value: valueOrDash(seafarer, ['seafarer_person', 'number_of_children']),
    },
    {
      label: `Children's Name & DOB`,
      value: valueOrDash(seafarer, ['seafarer_person', 'children_names']),
    },
  ];
  return result;
};

export default SeafarerPersonalParticulars;
