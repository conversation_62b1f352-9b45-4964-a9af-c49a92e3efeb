import { v4 as uuid } from 'uuid';
import { valueOrDash, dateOrDash, stringAsDate } from '../model/utils';

export const seafarerStatus = {
  seafarer_account_status: 'initialized',
  seafarer_journey_status: 'new_applicant',
  seafarer_exam_status: '',
};

export function getSeafarerModel(data) {
  const seafarerPerson = data['seafarer_person'] ?? {};
  const passports = seafarerPerson['passports'] ?? [];
  const seamanBooks = seafarerPerson['seaman_books'] ?? [];

  const passportModel = (data) => {
    const expiryDate = dateOrDash(data, ['date_of_expiry']);
    const issueDate = dateOrDash(data, ['date_of_issue']);

    return {
      localId: uuid(),
      number: valueOrDash(data, ['number']),
      date_of_issue: issueDate,
      date_of_expiry: expiryDate,
      country_id: data.country_id,
      nationality_id: data.nationality_id,
    };
  };

  const seamanBookModel = (data) => {
    const expiryDate = dateOrDash(data, ['date_of_expiry']);
    const issueDate = dateOrDash(data, ['date_of_issue']);

    return {
      localId: uuid(),
      number: valueOrDash(data, ['number']),
      date_of_issue: issueDate,
      date_of_expiry: expiryDate,
      country_id: data.country_id,
      port_of_issue: data.port_of_issue,
    };
  };

  const possportData = passports.map((value) => {
    return passportModel(value);
  });

  const seamanBookData = seamanBooks.map((value) => {
    return seamanBookModel(value);
  });

  const dateOfBirth = dateOrDash(seafarerPerson, ['date_of_birth']);
  const createdAt = dateOrDash(seafarerPerson, ['created_at']);

  const id = valueOrDash(data, ['id']);
  const seafarerId = valueOrDash(seafarerPerson, ['id']);
  const gender = valueOrDash(seafarerPerson, ['gender']);

  const firstName = valueOrDash(seafarerPerson, ['first_name']);
  const lastName = valueOrDash(seafarerPerson, ['last_name']);
  const middleName = valueOrDash(seafarerPerson, ['middle_name']);
  const fullName = `${firstName} ${lastName}`;

  const createdby = valueOrDash(seafarerPerson, ['created_by']);

  const result = {
    general: {
      id: id,
      current_rank: 'Master',
      full_name: fullName,
      first_name: firstName,
      middle_name: middleName,
      last_name: lastName,
      date_of_birth: dateOfBirth,
      place_of_birth: seafarerPerson.place_of_birth,
      gender: gender,
      createdAt: createdAt,
      createdBy: createdby,
      seafarerId: seafarerId,
    },
    passports: possportData,
    seamanBooks: seamanBookData,
  };
  return result;
}

export function convertSeafarerModelToRequestFormat(seafarer) {
  const general = seafarer.general ?? {};
  const passports = seafarer.passports ?? [];
  const seamanBooks = seafarer.seamanBooks ?? [];

  const passportData = passports.map((passport) => {
    return {
      number: passport.number,
      date_of_issue: passport.date_of_issue,
      date_of_expiry: passport.date_of_expiry,
      country_id: passport.country_id,
      nationality_id: passport.nationality_id,
    };
  });

  const seamanBooksData = seamanBooks.map((seamanBook) => {
    return {
      number: seamanBook.number,
      date_of_issue: seamanBook.date_of_issue,
      date_of_expiry: seamanBook.date_of_expiry,
      country_id: seamanBook.country_id,
      port_of_issue: seamanBook.port_of_issue,
    };
  });

  return {
    seafarer_person: {
      screening_status: 'under_screening',
      first_name: general.first_name,
      last_name: general.first_name,
      date_of_birth: stringAsDate(general.date_of_birth),
      gender: general.gender,
      nationality_id: passportData[0] ? passportData[0].nationality_id : '',
      country_id: passportData[0] ? passportData[0].country_id : '',
      address: 'Hong Kong, Kennedy Town',
      place_of_birth: 'Hong Kong',
      passports: passportData,
      seaman_books: seamanBooksData,
      status: seafarerStatus,
    },
  };
}

export function getEmptySeafarer() {
  return {
    general: getEmptyGeneral(),
    passports: [getEmptyPassport()],
    seamanBooks: [getEmptySeamansBook()],
  };
}

export function getEmptyGeneral() {
  return {
    current_rank: '',
    full_name: '',
    first_name: '',
    middle_name: '',
    last_name: '',
    date_of_birth: '',
    gender: '',
  };
}

export function getEmptyPassport() {
  return {
    number: '',
    date_of_issue: '',
    date_of_expiry: '',
    issuing_country: '',
    place_of_issue: '',
  };
}

export function getEmptySeamansBook() {
  return {
    localId: uuid(),
    number: '',
    country: '',
    port_of_issue: '',
    date_of_issue: '',
    date_of_expiry: '',
  };
}
