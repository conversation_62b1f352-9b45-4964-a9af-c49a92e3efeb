import * as yup from 'yup';
import { isValidXml10 } from '@src/util/string-utils';

export const initialValues = {
  date: new Date(),
  categoryId: undefined,
  headline: '',
  story: '',
};

export const validationSchema = () => {
  return yup.object({
    date: yup.string().required('Please select a date'),
    categoryId: yup.number().required('Please select a category'),
    headline: yup
      .string()
      .max(200)
      .required('Please enter a headline')
      .test('is-xml10', 'Contains invalid characters or symbols', (value: string) =>
        isValidXml10(value),
      ),
    story: yup
      .string()
      .max(4000)
      .required('Please enter story content')
      .test('is-xml10', 'Contains invalid characters or symbols', (value: string) =>
        isValidXml10(value),
      ),
  });
};
