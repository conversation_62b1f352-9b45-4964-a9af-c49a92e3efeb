import { valueOrDash, Dash } from './utils';

const VesselContactViewData = (vessel) => {
  const phones = valueOrDash(vessel, ['phones']);
  const emails = valueOrDash(vessel, ['emails']);

  const phoneRows =
    phones == Dash
      ? []
      : phones.map(function (phone) {
          return {
            label: valueOrDash(phone, ['phone_type', 'value']),
            value: valueOrDash(phone, ['phone_number']),
          };
        });

  const emailRows =
    emails == Dash
      ? []
      : emails.map(function (email) {
          return {
            label: valueOrDash(email, ['email_type', 'value']),
            value: valueOrDash(email, ['email']),
          };
        });

  return phoneRows.concat(emailRows);
};

export default VesselContactViewData;
