import { valueOrDash, dateOrDash } from './utils';

const getFieldInLatestContactLog = (seafarer, field) => {
  const latestContactLog = seafarer.seafarer_contact_log?.filter((scl) => scl.is_latest === true);
  const fieldValue = latestContactLog?.[0]?.[field];
  const result = fieldValue !== undefined ? { [field]: fieldValue } : null;
  return result;
};

const SeafarerGeneralDetails = (seafarer) => [
  {
    key: 'hkid',
    label: 'Seafarer ID (HKID)',
    value: valueOrDash(seafarer, ['hkid']),
  },
  {
    label: 'First Name',
    value: valueOrDash(seafarer, ['seafarer_person', 'first_name'], true, true),
  },
  {
    label: 'Middle Name',
    value: valueOrDash(seafarer, ['seafarer_person', 'middle_name'], true),
  },
  {
    label: 'Last Name',
    value: valueOrDash(seafarer, ['seafarer_person', 'last_name'], true),
  },
  {
    label: 'Gender',
    value: valueOrDash(seafarer, ['seafarer_person', 'gender'], true),
  },
  {
    label: 'Date of Birth',
    value: dateOrDash(seafarer, ['seafarer_person', 'date_of_birth']),
  },
  {
    label: 'Place of Birth',
    value: valueOrDash(seafarer, ['seafarer_person', 'place_of_birth'], true),
  },
  {
    label: 'Country of Birth',
    value: valueOrDash(seafarer, ['seafarer_person', 'country_of_birth', 'value']),
  },
  {
    label: 'Nationality',
    value: valueOrDash(seafarer, ['seafarer_person', 'nationality', 'value']),
  },
  {
    label: 'Rank',
    value: valueOrDash(seafarer, ['seafarer_rank', 'value']),
  },
  {
    label: 'Reporting Office',
    value: valueOrDash(seafarer, ['seafarer_reporting_office', 'value']),
  },
  {
    label: 'Manning Agency',
    value: valueOrDash(seafarer, ['seafarer_manning_agent', 'value']),
  },
  {
    label: 'Availability Date',
    value: dateOrDash(getFieldInLatestContactLog(seafarer, 'availability_date'), [
      'availability_date',
    ]),
  },
  {
    label: 'Availability Remark',
    value: valueOrDash(
      getFieldInLatestContactLog(seafarer, 'availability_remarks'),
      ['availability_remarks'],
      true,
    ),
  },
];

export default SeafarerGeneralDetails;
