import { screeningStatus, seafarerStatus } from './constants';
export const CONTRACT_EXPIRY_EVENT_KEY = 'contract-expiry';
export const AVAILABLE_SEAFARERS_EVENT_KEY = 'available-seafarers';

export const ListTabData = [
  {
    eventKey: `${screeningStatus.ALL}`,
    tabName: 'All',
    dataTestId: 'fml-all',
  },
  {
    eventKey: `${screeningStatus.PASSED}`,
    tabName: 'Approved Seafarers',
    dataTestId: 'fml-approved-seafarers',
  },
  {
    eventKey: `${screeningStatus.UNDER_SCREENING}`,
    tabName: 'In Screening',
    dataTestId: 'fml-in-screening',
  },
  {
    eventKey: `${screeningStatus.REJECTED}`,
    tabName: 'Screening Rejected',
    dataTestId: 'fml-screening-rejected',
  },
  {
    eventKey: `${screeningStatus.ARCHIVED}`,
    tabName: 'Archived Seafarers',
    dataTestId: 'fml-archived-seafarers',
  },
  {
    eventKey: CONTRACT_EXPIRY_EVENT_KEY,
    tabName: 'Contract Expiry',
    dataTestId: 'fml-contract-expiry',
  },
  {
    eventKey: AVAILABLE_SEAFARERS_EVENT_KEY,
    tabName: 'Available Seafarers',
    dataTestId: 'fml-available-seafarers',
  },
];

export const DetailPageTabData = [
  {
    eventKey: 'general',
    tabName: 'General',
    dataTestId: 'fml-detail-general',
  },
  {
    eventKey: 'screening',
    tabName: 'Screening Details',
    dataTestId: 'fml-detail-screening',
  },
  {
    eventKey: 'id-documents',
    tabName: 'ID Documents',
    dataTestId: 'fml-detail-id-documents',
  },
  {
    eventKey: 'endorsement',
    tabName: 'Endorsement & Verification',
    dataTestId: 'fml-detail-endorsement-vertification',
  },
  {
    eventKey: 'other-documents',
    tabName: 'Other Documents',
    dataTestId: 'fml-detail-others-documents',
  },
  {
    eventKey: 'account-details',
    tabName: 'Bank Accounts',
    dataTestId: 'fml-detail-bank-accounts',
  },
  {
    eventKey: 'availability',
    tabName: 'Availability',
    dataTestId: 'fml-detail-availability',
  },
  {
    eventKey: 'pre-joining',
    tabName: 'Pre-Joining',
  },
  {
    eventKey: 'experience',
    tabName: 'Experience',
    dataTestId: 'fml-detail-experience',
  },
  {
    eventKey: 'appraisals',
    tabName: 'Appraisals',
  },
  {
    eventKey: 'status-history',
    tabName: 'Status History',
    dataTestId: 'fml-detail-status-history',
  },
  {
    eventKey: 'training-courses',
    tabName: 'Training Courses',
    dataTestId:'fml-detail-training-courses',
  },
];

export const SeafarerReportData = [
  {
    eventKey: 'approval-report',
    tabName: 'Approval Report',
  },
  {
    eventKey: 'signed-on',
    tabName: 'Signed On',
  },
  {
    eventKey: 'signed-off',
    tabName: 'Signed Off',
  },
  {
    eventKey: 'dg-shipping-list',
    tabName: 'DG Shipping List',
  },
  {
    eventKey: 'modeller',
    tabName: 'Modeller',
  },
];

export const CREW_PLANNER_TAB_LIST = [
  {
    eventKey: 'seafarers-to-relieve',
    tabName: 'Seafarers to Relieve',
  },
  {
    eventKey: 'available-seafarers',
    tabName: 'Available Seafarers',
  },
];

export const CrewListTabData = [
  {
    eventKey: seafarerStatus.SIGNED_ON,
    tabName: 'Signed On',
  },
  {
    eventKey: seafarerStatus.RECOMMENDED,
    tabName: 'Recommended',
  },
];

export const SEAFARER_PAGE = 'seafarer';
export const SEAFARER_REPORT_PAGE = 'seafarer-reports';

export const CREW_MANAGEMEMT_TAB_LIST = [
  {
    eventKey: 'schedule-crew-for-travel',
    tabName: 'Schedule Crew for Travel',
  },
];
export const SEAFARER_TRAINING_COURSES_PAGE = 'training-courses';
