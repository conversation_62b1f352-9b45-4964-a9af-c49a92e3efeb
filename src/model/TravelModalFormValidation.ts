import { EMAIL_VALIDATION } from '../constants/contactTypes';
import * as yup from 'yup';

export const message = `Dear Sir/Madam,
  
Please find the following seafarer information sent from PARIS 2.0 for travel arrangement.

Regards,
Fleet Management Limited`;

export const validationSchema = () => {
  return yup.object({
    subject: yup.string().required('Please add subject'),
    sendTo: yup.array().min(1, 'Please select atleast one agency'),
    cc: yup
      .string()
      .test('checkIfValidEmail', 'Please enter valid email address', function (value: string) {
        if (!value) {
          return true;
        }
        const emailArray = value.split(';');
        const doesInvalidEmailExist = emailArray.some((email) => !EMAIL_VALIDATION.test(email));
        if (doesInvalidEmailExist) {
          return false;
        }
        return true;
      }),
    messageContent: yup.string().required('Please enter an appropriate message content'),
  });
};
