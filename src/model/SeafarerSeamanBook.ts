import { valueOrDash, dateOrDash } from './utils';

const SeafarerSeamanBook = (seaman_book) => {
  let fileName = '- - -';
  const document =
    seaman_book.document && seaman_book.document.length > 0 ? seaman_book.document[0] : undefined;
  const country = seaman_book.country ? seaman_book.country.value : undefined;

  if (document) {
    const extension = seaman_book.doc_path ? seaman_book.doc_path.split('.').pop() : '';
    if (country) {
      fileName = `${country}_Seamans_Book.${extension}`;
    } else {
      fileName = `Seamans_Book.${extension}`;
    }
  }

  const isOriginal = seaman_book?.is_original ? 'Yes' : 'No';

  return [
    {
      label: 'Seaman’s Book No.',
      value: valueOrDash(seaman_book, ['number']),
    },
    {
      label: 'Country',
      value: valueOrDash(seaman_book, ['country', 'value']),
    },
    {
      label: 'Port of Issue',
      value: valueOrDash(seaman_book, ['port_of_issue'], true),
    },
    {
      label: 'Date of Issue',
      value: dateOrDash(seaman_book, ['date_of_issue']),
    },
    {
      label: 'Date of Expiry',
      value: dateOrDash(seaman_book, ['date_of_expiry']),
    },
    {
      label: 'Document',
      value: fileName,
      document: document,
      documentType: 'seamans_book',
    },
    {
      label: 'Is Original',
      value: isOriginal,
    },
  ];
};

export default SeafarerSeamanBook;
