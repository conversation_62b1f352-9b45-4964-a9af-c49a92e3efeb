import { valueOrDash, getDurationStrByDateRanges } from './utils';

export const SeafarerExperienceDetails = (seafarer, vesselExperience) => {
  let framoExp = '---';
  if (seafarer.framo_experience !== null) {
    framoExp = seafarer.framo_experience ? 'Yes' : 'No';
  }
  const withIceExperience = seafarer.ice_conditions_experience ? 'Yes' : 'No';

  return [
    {
      key: 'with_ice_experience',
      label: 'Ice/Polar Code Sea Service',
      value: withIceExperience,
    },
    {
      key: 'framo_experience',
      label: 'Framo Experience',
      value: framoExp,
    },
    {
      key: 'cargo_experience',
      label: 'Cargo Handling Experience',
      value: valueOrDash(seafarer, ['cargo_experience']),
    },
    {
      key: 'additional_experience',
      label: 'Additional Experience',
      value: valueOrDash(seafarer, ['additional_experience']),
    },
    {
      key: 'total_fml_experience',
      label: 'Total Experience with FML',
      value: SeafarerTotalFMLExperience(vesselExperience)
    },
  ];
};

export const SeafarerTotalFMLExperience = (vesselExperience) => {
  const fmlExperiences = vesselExperience.filter(
    (experience) => experience.vessel_ref_id !== 0 && experience.vessel_ref_id !== null,
  );
  const fmlExperienceDateRanges = fmlExperiences.map((experience) => {
    return { fromDateISOString: experience.start_date, toDateISOString: experience.end_date };
  });
  return getDurationStrByDateRanges(fmlExperienceDateRanges)
}

export const SeafarerExperienceByVesselType = (vesselExperience) => {
  let vesselType = {};
  vesselExperience.forEach((vessel) => {
    if (vesselType[vessel.vessel_type]) {
      vesselType[vessel.vessel_type].value.push({fromDateISOString: vessel.start_date, toDateISOString: vessel.end_date})
    } else {
      vesselType[vessel.vessel_type] = {
        key: vessel.vessel_type,
        label: vessel.vessel_type,
        value: [{fromDateISOString: vessel.start_date, toDateISOString: vessel.end_date}]
      };
    }
  });
  return Object.values(vesselType).map((ele) => {
    return {
      ...ele,
      value: getDurationStrByDateRanges(ele.value)
    };
  });
};

export const SeafarerExperienceByRank = (vesselExperience) => {
  let vesselType = {};
  vesselExperience.forEach((vessel) => {
    if (vesselType[vessel.rank.value]) {
      vesselType[vessel.rank.value].value.push({fromDateISOString: vessel.start_date, toDateISOString: vessel.end_date})
    } else {
      vesselType[vessel.rank.value] = {
        key: vessel.rank.value,
        label: vessel.rank.value,
        value: [{fromDateISOString: vessel.start_date, toDateISOString: vessel.end_date}]
      };
    }
  });
  return Object.values(vesselType).map((ele) => {
    return {
      ...ele,
      value: getDurationStrByDateRanges(ele.value)
    };
  });
};

export const SeafarerExperienceByVesselTypeAndRank = (vesselExperience) => {
  let vesselType = {};
  vesselExperience.forEach((vessel) => {
    const type = `${vessel.vessel_type} / ${vessel.rank.value}`;
    if (vesselType[type]) {
      vesselType[type].value.push({fromDateISOString: vessel.start_date, toDateISOString: vessel.end_date})
    } else {
      vesselType[type] = {
        key: type,
        label: type,
        value: [{fromDateISOString: vessel.start_date, toDateISOString: vessel.end_date}]
      };
    }
  });
  return Object.values(vesselType).map((ele) => {
    return {
      ...ele,
      value: getDurationStrByDateRanges(ele.value),
    };
  });
};
