export interface AppointmentLetterWages {
  desc: string;
  currency: string;
  amount: string;
  order: number;
};

export interface AppointmentLetterNextOfKin {
  name?: string;
  surname?: string;
  relationship?: string;
  telephone?: string;
  mobilePhone?: string;
  address?: string;
  percentage?: number;
}

export interface AppointmentLetterData {
  seafarer: {
    hkid: string;
    fullName: string;
    passport: string;
    seamanBook: string;
    dateOfBirth: string;
    placeOfBirth: string;
    countryOfBirth: string;
    isFilipino: boolean;
    nextOfKin: AppointmentLetterNextOfKin[];
    maritalStatus: string;
    spouseSurname: string;
    spouseName: string;
    child: string;
  };
  seafarer_status_history: {
    vessel: string;
    rank: string; // e.g. MASTER (MSTR)
    repatriationPort: string;
    expectedStartDate: string;
    expectedEndDate: string;
  };
  homeAllotment?: { // For Filipino seafarer only
    currency: string;
    firstAllotment: number;
    monthlyAllotment: number;
  };
  remuneration: AppointmentLetterWages[];
  earnings: AppointmentLetterWages[];
  deductions: AppointmentLetterWages[];
  owner: {
    id: number;
    name: string;
    isZeroAlcohol: boolean;
  };
  registeredOwner: {
    id: number;
    name: string;
  };
  reportingOffice: {
    id: number;
    name: string;
    shipPartyType: number;
    address: string;
    country: string;
    reportingOfficeName: string;
    authorizedRepresentativeRank: string;
    authorizedRepresentative: string;
    principalCompany: string;
  };
  vesselManager: {
    name: string;
    address: string;
  };
  vessel: {
    imoNumber: number;
  };
}
