/* eslint-disable no-undef */

import { Seafarer } from './seafarerInterfaces';
import { VesselOwnershipDetails } from './vesselService';

export interface PrejoiningTabProps {
  seafarer: Seafarer;
  setIsEditPreJoiningEnabled: (x: boolean) => void;
  roleConfig: any;
  eventTracker: (type: string, value: string) => {};
  activeVesselData: VesselOwnershipDetails[];
  setHasHistoryChanged: (x: number) => void;
}
