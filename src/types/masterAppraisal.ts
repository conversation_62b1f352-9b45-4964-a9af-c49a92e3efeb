/* eslint-disable no-undef */
import GA4React from 'ga-4-react';
export interface MasterAppraisalQueryResponse {
  pagination: {
    totalCount: number;
    count: number;
    offset: number;
    limit: number;
    orderBy: string;
  };
  results: MasterAppraisalResult[];
}

export interface MasterAppraisalResult {
  id: number;
  created_at: string;
  updated_at: string;
  ref_id: number;
  rank_id: number;
  rank: {
    id: number;
    value: string;
    unit: string;
    ref_id: number;
    sortpriority: number;
  };
  seafarer_id: number;
  vessel_ownership_id: number;
  vessel_name: string;
  master_comment: string;
  head_comment: string;
  crew_comment: string;
  overall_grade: number;
  is_promotion_recommended: boolean;
  new_rank_id: number;
  promotion_requirement: string;
  training_needs: string;
  start_date: string;
  end_date: string;
  master_id: number;
  head_id: number;
  master_name: string;
  head_name: string;
  created_by_hash: string;
  updated_by_hash: string;
  ga4react?: GA4React;
}

export interface MasterAppraisalGetResponse {
  id: number;
  ref_id: number;
  rank_id: number;
  seafarer_id: number;
  vessel_ownership_id: number;
  vessel_name: string;
  master_comment: string;
  head_comment: string;
  crew_comment: string;
  overall_grade: number;
  is_promotion_recommended: boolean;
  new_rank_id: number;
  promotion_requirement: string;
  training_needs: string;
  master_appraisal_reason: string;
  master_appraisal_drinking_status: string;
  master_appraisal_result: {
    id: number;
    master_appraisal_id: number;
    master_appraisal_question_id: number;
    master_appraisal_question: {
      id: number;
      ref_id: number;
      value: string;
      unit: string;
      sortpriority: number;
    };
    result: number;
    ref_id: number;
    created_at: string;
    updated_at: string;
    created_by: string;
    updated_by: string;
  }[];
  start_date: string;
  end_date: string;
  master_id: number;
  head_id: number;
  master_name: string;
  head_name: string;
  created_at: string;
  updated_at: string;
  created_by_hash: string;
  updated_by_hash: string;
  rank: {
    id: number;
    value: string;
    unit: string;
    ref_id: number;
    sortpriority: number;
  };
}
