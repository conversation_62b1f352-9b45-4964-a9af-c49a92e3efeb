import { RouteComponentProps } from 'react-router-dom';

/* eslint-disable no-undef */
export interface CopyVesselPlanProps {
  history: RouteComponentProps['history'];
  vesselOwnerShipId: number | string;
  vesselDropdownData: { id: number; value: string }[];
  refreshTableData: () => void;
  eventTracker: (type: string, value: string) => void;
  roleConfig: any;
}

export interface VesselDropDownProps {
  dropDownValues: { id: number; value: string }[];
  onChange: (x: any) => void;
}
