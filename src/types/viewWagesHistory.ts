/* eslint-disable no-undef */
import { RouteComponentProps } from 'react-router-dom';
export interface WagesDropDownValue {
  id: number;
  effectiveDate: string;
  totalSalary: number;
}

export interface WagesDropDownProps {
  dropDownValues: WagesDropDownValue[];
  selectedValue: WagesDropDownValue;
  onInputChange: (eventKey: WagesDropDownValue) => void;
  eventTracker: (type: string, value: string) => void;
  wagesUnit?: string;
}

export interface ViewWagesHistoryModalProps {
  seafarer: any;
  history: RouteComponentProps['history'];
  eventTracker: (type: string, value: string) => void;
}
