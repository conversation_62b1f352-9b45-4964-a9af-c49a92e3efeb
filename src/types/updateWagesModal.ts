/* eslint-disable no-undef */
import { RouteComponentProps } from 'react-router-dom';
import { Seafarer } from './seafarerInterfaces';
import { VesselOwnershipDetails } from './vesselService';
export interface UpdateWagesModalProps {
  setShowUpdateWagesModal: (value: boolean) => {};
  seafarer: Seafarer;
  history: RouteComponentProps['history'];
  showUpdateWagesModal: boolean;
  refreshDetailsPageData: () => void;
  eventTracker: (type: string, value: string) => void;
  roleConfig: any;
  activeVesselData: VesselOwnershipDetails[];
}
