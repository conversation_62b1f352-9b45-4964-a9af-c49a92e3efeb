import { SeafarerContacts, SeafarerStatusHistory } from './seafarerInterfaces';

/* eslint-disable no-undef */
export interface Payhead {
  id: number;
  head_name: string;
  type: string;
  nature: string;
  category: string;
  is_display_input_sheet: boolean;
  default_value: number;
  place_entered: string;
  is_add_to_basic_wages: boolean;
  is_add_to_total_wages: boolean;
  display_order: number;
  status: number;
}

export interface SeafarerWagesDetails {
  id: number;
  seafarer_wages_id: number;
  payhead_id: number;
  amount: number;
  amount_unit: string;
  ref_id: number;
  created_at: Date;
  updated_at: Date;
  created_by_hash: string;
  last_updated_by_hash: string;
  payhead: Payhead;
}

export interface SeafarerWagesDetailsPayload {
  id?: number;
  payhead_id: number;
  amount: number;
}

export interface SeafarerWages {
  id: number;
  amount_total: number;
  amount_basic: number;
  status: string;
  ref_id: number;
  remarks: string;
  is_history: boolean;
  amount_unit: string;
  seafarer_wages_details: SeafarerWagesDetails[];
  seafarer_promotion?: SeafarerPromotion[];
  seafarer_status_history: SeafarerStatusHistory;
  effective_date: Date;
  created_at: Date;
  updated_at: Date;
  created_by: string;
  last_updated_by: string;
}

export interface SeafarerPromotion {
  is_promotion: boolean;
}

export interface SeafarerWagesPayload {
  id?: number;
  effective_date?: string;
  remarks?: string;
  seafarer_promotion?: SeafarerPromotionPayload;
  seafarer_wages_details?: SeafarerWagesDetailsPayload[];
  seafarer_contacts?: SeafarerContacts;
  amount_unit?: string;
}

export interface SeafarerPromotionPayload {
  new_rank_id: number;
  new_contract_end_date: Date;
  is_promotion: boolean;
}

export interface SeafarerJoiningSpendingsPayload {
  id?: number;
  payhead_id: number;
  amount: number;
  remarks: string;
}

export interface SeafarerAllotmentDetailsPayload {
  id?: number;
  monthly_allotment: number;
  first_allotment: number;
}

export interface SeafarerPreJoiningPayload {
  seafarer_allotment?: SeafarerAllotmentDetailsPayload;
  seafarer_joining_spendings: SeafarerJoiningSpendingsPayload[];
}
