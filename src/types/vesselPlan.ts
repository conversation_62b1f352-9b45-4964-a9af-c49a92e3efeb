/* eslint-disable no-undef */
export interface GetVesselPlanApiRes {
  id: number;
  copy_from_id: null | number;
  vessel_ownership_id: number;
  vessel_tech_group: string;
  vessel_name: string;
  vessel_ref_id: number;
  planned_number: number;
  planned_wages: number;
  planned_wages_unit: string;
  actual_wages: number;
  seafarer_report_modeller_details: {
    seafarer_report_modeller_id: number;
    seafarer_id: number;
    rank_id: number;
    rank: {
      id: number;
      value: string;
      unit: string;
      ref_id: number;
      sortpriority: number;
    };
    planned_number: number;
    planned_wages: number;
    planned_nationality_id: number;
    planned_nationality: {
      id: number;
      alpha2_code: string;
      alpha3_code: string;
      value: string;
      ref_id: null | number;
    };
    actual_wages: number;
    actual_number: number;
    actual_nationality_id: number;
    actual_nationality: {
      id: number;
      alpha2_code: string;
      alpha3_code: string;
      value: string;
      ref_id: null | number;
    };
  }[];
}
