import GA4React from 'ga-4-react';
import { SeafarerTrainingRequirementDocument } from './seafarerInterfaces';

/* eslint-disable no-undef */
export interface TrainingRequirementResponse {
  pagination: {
    totalCount: number;
    count: number;
    offset: number;
    limit: number;
    orderBy: string;
  };
  results: TrainingRequirementResult[];
}

export interface TrainingRequirementResult {
  id: number;
  seafarer_id: number;
  recommended_date: string;
  vessel_ownership_id: number | null;
  vessel_name: string;
  deadline_date: string;
  completed_date: string | null;
  training_needs: string;
  training_imparted: string;
  completed_date_created_at: string | null;
  type: 0 | 1;
  ref_id: number;
  supt_appraisal_id: number | null;
  master_appraisal_id: number | null;
  created_by_hash: string;
  updated_by_hash: string;
  created_at: string;
  updated_at: string;
  seafarer_training_requirement_document: SeafarerTrainingRequirementDocument[];
  ga4react?: GA4React;
}
