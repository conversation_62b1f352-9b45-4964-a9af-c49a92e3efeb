/* eslint-disable no-undef */
import GA4React from 'ga-4-react';

export interface DebriefingQueryResponse {
  pagination: {
    totalCount: number;
    offset: number;
    limit: number;
    orderBy: string;
  };
  results: DebriefingResult[];
}

interface SurveyParticipant {
  id: number;
  user_id: number;
  seafarer_id: null | number;
  first_name: string;
  middle_name: string;
  last_name: string;
  created_by_hash: string;
  updated_by_hash: string;
  created_at: string;
  updated_at: string;
}

interface SurveyParticipationDebriefing {
  id: number;
  survey_participantion_id: number;
  seafarer_id: number;
  first_name: string;
  middle_name: string;
  last_name: string;
  hkid: number;
  office_id: number;
  seafarer_status_history_id: number;
  average_score: number;
  rank_id: number;
  rank_value?: string;
  rank_unit?: string;
  vessel_ownership_id: number;
  vessel_id: number;
  'seafarer_rank.value'?: string;
  vessel_ownership_access: {
    created_at: string;
    id: number;
    tech_group: string;
    updated_at: string;
    vessel_name: string;
  };
  vessel_tech_group: string;
  created_at: string;
  updated_at: string;
  created_by_hash: string;
  updated_by_hash: string;
}

export interface DebriefingResult {
  id: number;
  survey_id: number;
  survey_participant_id: number;
  ref_id: number;
  status: string;
  due_date: string;
  completed_at: string;
  created_by_hash: string;
  updated_by_hash: string;
  created_at: string;
  updated_at: string;
  survey_participant: SurveyParticipant;
  survey_participation_debriefing: SurveyParticipationDebriefing;
  access_control: {
    is_editable: boolean;
  };
  ga4react?: GA4React;
}
