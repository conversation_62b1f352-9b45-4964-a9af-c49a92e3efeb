/* eslint-disable no-undef */
import React from 'react';
export interface Filter {
  type: FilterType;
  subtype: any;
  defaultTab?: string;
}

export interface FilterType {
  type: string;
  name: string;
  section: string;
  inputType: string;
  queryType: string;
  queryKey: string;
  isSearchable?: boolean;
  notMultiple?: boolean;
  validTabs?: string[];
}

export interface PaginateData {
  pageSize: number;
  pageIndex: number;
  sortBy: {
    id: string;
    desc: boolean;
  }[];
  offset?: number;
}

export interface GetApiResponse {
  pagination: {
    totalCount: number;
    count: number;
    offset: string;
    limit: string;
    orderBy: string;
  };
  summary: ModellerReportSummary;
  results: ModellerReportResults[];
}

export interface ModellerReportResults {
  id: number;
  copy_from_id: null | number;
  vessel_ownership_id: number;
  vessel_tech_group: string;
  vessel_name: string;
  vessel_ref_id: number;
  planned_number: number;
  actual_number: number;
  planned_wages: number;
  actual_wages: number;
  created_at: string;
  updated_at: string;
  created_by_hash: string;
  last_updated_by_hash: string;
  keyword?: string;
}

export interface ModellerReportSummary {
  planned_total_number: number;
  actual_total_number: number;
  planned_wages: number;
  actual_wages: number;
}

export interface FooterProps {
  columns: {
    Header: string | JSX.Element;
    target_id: string;
    value: string | JSX.Element;
    width: number;
  }[];
  rightOffset?: number;
  tableRef?: React.MutableRefObject<null>;
}

export const seafarerScreeningStatusReports = Object.freeze({
  under_screening: 'Pending',
  not_started: 'Pending',
  rejected: 'Rejected',
  passed: 'Approved',
});

export const approvalStatusClassName = Object.freeze({
  Approved: 'green',
  Pending: 'yellow',
  Rejected: 'red',
});

export enum recommendationStatus {
  WITH_DEVIATION = 'With Deviation',
  WITHOUT_DEVIATION = 'Without Deviation',
}
export enum approvalStatus {
  APPROVED = 'Approved',
  REJECTED = 'Rejected',
  PENDING = 'Pending',
}

export const reportFilterKeys = Object.freeze({
  tech_group: 'tech_group',
  vessel: 'vessel',
  reports_vessel: 'reports_vessel',
  vessel_with_id: 'vessel_with_id',
  reports_ranks: 'reports_ranks',
  reports_nationalities: 'reports_nationalities',
  recommended: 'recommended',
  approval_status: 'approval_status',
  recommend_date: 'recommend_date',
  approval_date: 'approval_date',
  report_owners: 'report_owners',
  reporting_offices: 'reporting_offices',
  date_of_joining: 'date_of_joining',
  sign_off_date: 'sign_off_date',
  commencement_of_contract: 'commencement_of_contract',
  actual_number: 'actual_number',
  actual_wages: 'actual_wages',
  planned_number: 'planned_number',
  planned_wages: 'planned_wages',
  ownership_details: 'ownership_details',
  sign_on: 'sign_on',
  sign_off: 'sign_off',
  planned_wages_unit: 'planned_wages_unit',
});

export const reportApiPayloadFilterKeys = Object.freeze({
  reports_vessel: 'vessel',
  reports_ranks: 'rank',
  reports_nationalities: 'nationality',
  recommended: 'recommended',
  approval_status: 'journeyStatus',
  reporting_offices: 'reportingOffice',
  date_of_joining: 'statusDate',
  tech_group: 'vessel_tech_group',
  actual_number: 'actual_number',
  actual_wages: 'actual_wages',
  planned_number: 'planned_number',
  planned_wages: 'planned_wages',
  vessel: 'vessel_name',
  commencement_of_contract: 'statusDate',
  sign_on: 'signOnDate',
  sign_off: 'signOffDate',
  recommend_date: 'recommendedDate',
  approval_date: 'approvedDate',
});

export enum reportTabNames {
  MODELLER = 'modeller',
  APPROVAL_REPORT = 'approval-report',
  SIGNED_ON = 'signed-on',
  SIGNED_OFF = 'signed-off',
  DG_SHIPPING_LIST = 'dg-shipping-list',
}

export enum reportTabNamesFilter {
  'modeller' = 'MODELLER_REPORT',
  'approval-report' = 'APPROVAL_REPORT',
  'signed-on' = 'SIGNED_ON_REPORT',
  'signed-off' = 'SIGNED_OFF_REPORT',
  'dg-shipping-list' = 'DG_SHIPPING_REPORT',
}
