/* eslint-disable no-undef */
export interface Seafarer {
  additional_experience: string;
  can_view_contacts: boolean;
  cargo_experience: string;
  created_at: Date;
  data_quality: string;
  framo_experience: boolean;
  hkid: number;
  ice_conditions_experience: boolean;
  id: number;
  is_only_worked_fml: boolean;
  is_parent: boolean;
  manning_agent_id: number;
  not_to_be_employed: boolean;
  not_to_be_employed_reason: string;
  office_id: number;
  parent_hkid: number;
  rank_id: number;
  ref_id: number;
  seafarer_contact_log: SeafarerContactLog[];
  seafarer_experience: SeafarerExperience[];
  seafarer_manning_agent: string;
  seafarer_person: Seafarer<PERSON>erson;
  seafarer_person_id: number;
  seafarer_rank: SeafarerRank;
  seafarer_reporting_office: SeafarerReportingOffice;
  show_cargo_experience: boolean;
  show_fml_experience: boolean;
  updated_at: Date;
  with_fml_vessel_experience: boolean;
}

export interface SeafarerContactLog {
  availability_date: Date;
  availability_remarks: string;
  contact_date: Date;
  contact_mode: string;
  created_at: Date;
  created_by_hash: string;
  docs_in_hand: boolean;
  general_remarks: string;
  id: number;
  is_latest: boolean;
  next_contact_date: Date;
  seafarer_id: number;
  updated_at: Date;
}

export interface SeafarerExperience {
  brake_horse_power: number;
  created_at: Date;
  created_by_hash: string;
  created_by_p1_email: string;
  deadweight_gross_registered_tonnage: string;
  deadweight_tonnage: number;
  deleted_at: Date;
  end_date: Date;
  engine_sub_type: string;
  engine_type: string;
  id: number;
  is_from_crew_assignment: boolean;
  is_latest: boolean;
  is_latest_from_crew_assignment: boolean;
  last_updated_by_hash: string;
  owner_name: string;
  ownership_id: number;
  paris1_ref_id: number;
  rank_id: number;
  salary: number;
  seafarer_id: number;
  signoff_port_name: string;
  signoff_reason_id: number;
  start_date: Date;
  status_history_id: number;
  updated_at: Date;
  vessel_name: string;
  vessel_ownership_id: number;
  vessel_ref_id: number;
  vessel_tech_group: string;
  vessel_type: string;
}

export interface SeafarerReportingOffice {
  id: number;
  ref_id: number;
  ship_party_id: number;
  unit: string;
  value: string;
}

export interface SeafarerRank {
  id: number;
  ref_id: number;
  sortpriority: number;
  unit: string;
  value: string;
}

export interface SeafarerStatusHistory {
  id: number;
  seafarer_person_id: number;
  seafarer_account_status: string;
  seafarer_journey_status: string;
  seafarer_exam_status: string;
  rank_id: number;
  vessel_name: string;
  vessel_ref_id: number;
  created_by_hash: string;
  created_by: string;
  seafarer_journey_remarks: string;
  seafarer_exam_remarks: string;
  status_date: Date;
  vessel_ownership_id: number;
  sign_off_date: Date;
  expected_contract_end_date: Date;
  embarkation_port: string;
  repatriation_port: string;
  vessel_tech_group: string;
  vessel_type: string;
  replaced_by_id: number;
  is_p1_history: boolean;
  seafarer_wages_id: number;
  expected_contract_start_date: Date;
  created_at: Date;
  updated_at: Date;
  created_at_p1: Date;
  updated_at_p1: Date;
  paris1_ref_id: number;
  is_current_status: boolean;
  seafarer_allotment_id: number;
  vessel_id: number | null;
  recommended_wages: number | string;
  recommended_wages_unit: string;
  recommended_with_deviation?: boolean;
  no_recommendation_permission?: boolean;
  has_travel_with_fleet?: boolean;
  nominee?: number | null;
  repatriation_port_id?: number | null;
  embarkation_port_id?: number | null;
  contract_length?: number | null;
}

export interface SeafarerPerson {
  id: number;
  current_account_status: string;
  current_journey_status: string;
  current_exam_status: string;
  created_by_hash: string;
  created_at: Date;
  updated_at: Date;
  screening_status: string;
  created_by: string;
  created_by_username: string;
  updated_by: string;
  updated_by_username: string;
  first_name: string;
  middle_name: string;
  last_name: string;
  date_of_birth: Date;
  gender: string;
  place_of_birth: string;
  country_of_birth_id: number;
  nationality_id: number;
  height: number;
  weight: number;
  overall_size: number;
  tshirt_size: number;
  jacket_size: number;
  shoe_size: number;
  smoking: string;
  vegetarian: string;
  nearest_airport: string;
  marital_status: string;
  surname_of_spouse: string;
  name_of_spouse: string;
  number_of_children: number;
  children_names: string;
  photo_path: string;
  updated_by_hash: string;
  updated_at_p1: Date;
  created_at_p1: Date;
  country_of_birth: string;
  nationality: SeafarerNationality;
  seafarer: Seafarer[];
  bank_accounts: SeafarerBankAccount[];
  passports: SeafarerPassport[];
  photos: [];
  seaman_books: [];
  seafarer_contacts: SeafarerContacts[];
  addresses: SeafarerContactAddress[];
  family_members: [];
  seafarer_account_status: SeafarerAccountStatus;
  seafarer_status_history: SeafarerStatusHistory[];
  seafarer_document: SeafarerDocument[];
  created_by_user_info: {};
  updated_by_user_info: {};
}

export interface SeafarerPassport {
  id: number;
  created_at: Date;
  updated_at: Date;
  seafarer_person_id: number;
  number: string;
  place_of_issue: string;
  country_id: number;
  date_of_issue: Date;
  date_of_expiry: Date;
  ref_id: number;
  doc_path: string;
  country: string;
  document: SeafarerPassportDocument;
}

export interface SeafarerPassportDocument {
  id: number;
  created_at: Date;
  updated_at: Date;
  seafarer_passport_id: number;
  name: string;
  mime: string;
}

export interface SeafarerDocument {
  id: number;
  seafarer_person_id: number;
  type: string;
  ref_id: number;
  doc_path: string;
  is_deleted: boolean;
  created_at: string;
  updated_at: string;
  created_by_hash: string;
  updated_by_hash: string;
}

export interface SeafarerTrainingRequirementDocument {
  id: number;
  seafarer_document_id: number;
  seafarer_training_requirement_id: number;
  created_at: string;
  updated_at: string;
  seafarer_document: SeafarerDocument;
}

export interface SeafarerAccountStatus {
  id: number;
  seafarer_person_id: number;
  status: string;
  created_by: string;
  p1_user_email: string;
  created_at: Date;
  updated_at: Date;
}

export interface SeafarerContactAddress {
  id: number;
  created_at: Date;
  updated_at: Date;
  seafarer_person_id: number;
  postal_zip_code: string;
  country_id: number;
  state: string;
  city: string;
  building: string;
  other_address: string;
  country: string;
}

export interface SeafarerContacts {
  id?: number;
  created_at?: Date;
  updated_at?: Date;
  seafarer_person_id?: number;
  contact: string;
  contact_type: string;
}

export interface SeafarerNationality {
  id: number;
  alpha2_code: string;
  alpha3_code: string;
  value: string;
  ref_id: number;
}

export interface SeafarerBankAccount {
  id: number;
  created_at: string;
  updated_at: string;
  seafarer_person_id: number;
  is_primary_payroll_account: boolean;
  seafarer_is_account_holder: boolean;
  relationship_with_beneficiary: string;
  account_holder_first_name: string;
  account_holder_middle_name: string;
  account_holder_last_name: string;
  account_holder_date_of_birth: string;
  account_holder_gender: string;
  account_holder_nationality_id: string | number;
  account_holder_address_id: number;
  number: string;
  bank_name: string;
  bank_address_id: number;
  ifsc_number: string;
  swift_code: string | number;
  iban_number: string | number;
  cnaps: string | number;
  account_type: string;
  fcnr_months: number;
  account_holder_birth_place: string;
  account_holder_contact_1: string;
  account_holder_contact_2: string;
  intermediary_swift_code: string | number;
  intermediary_bank_name: string;
  intermediary_bank_address: string;
  intermediary_bank_account: string | number;
  special_remittence_instrcution: string;
  remarks: string;
  pay_mode: string;
  account_holder_nationality: string;
  account_holder_address: {
    id: number;
    created_at: string;
    updated_at: string;
    postal_zip_code: string | number;
    country_id: string | number;
    address1: string;
    address2: string;
    address3: string;
    address4: string;
  };
  bank_address: {
    id: number;
    created_at: string;
    updated_at: string;
    postal_zip_code: number | string;
    country_id: number;
    address1: string;
    address2: string;
    address3: string;
    address4: string;
  };
  document: any[];
}
export interface SeafarerReportModellerDetails {
  id?: number;
  seafarer_report_modeller_id: number;
  seafarer_id: number;
  rank_id: number;
  rank: SeafarerRank;
  planned_number: number;
  planned_wages: number;
  planned_nationality_id: number | null;
  planned_nationality: SeafarerNationality;
  actual_wages: number | null;
  actual_number: number | null;
  actual_nationality_id: number | null;
  actual_nationality: SeafarerNationality;
  created_at: Date;
  updated_at: Date;
  created_by_hash: string;
  last_updated_by_hash: string;
}

export interface SeafarerReportModeller {
  copy_from_id: number | null;
  vessel_ownership_id: number;
  vessel_tech_group: string;
  vessel_name: string;
  vessel_ref_id: number;
  planned_number: number;
  planned_wages: number;
  actual_wages: number | null;
  actual_number: number | null;
  seafarer_report_modeller_details: SeafarerReportModellerDetails[];
  created_at: Date;
  updated_at: Date;
  created_by_hash: string;
  last_updated_by_hash: string;
  planned_wages_unit: string;
}

export interface SeafarerReportModellerDetailsErrors {
  vessel_plan_details: {
    id: number;
    planned_number: number | null;
    planned_wages: number | null;
  }[];
}
