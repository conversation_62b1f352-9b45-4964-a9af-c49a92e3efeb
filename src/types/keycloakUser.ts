/* eslint-disable camelcase */
export type RealmAccess = {
  roles: string[];
};

export type UserInfo = {
  name: string;
  firstName: string;
  lastName: string;
  user_id: string;
  seafarer_id: number;
  realm_access: RealmAccess;
  user_name_hash?: string;
  rank?: string;
  email_verified?: string;
  is_nova_onboarded?: boolean;
  tc_nova_version?: number;
  is_user_onboarded?: boolean;
  preferred_username?: string;
  given_name?: string;
  azure_ad_upn?: string;
  department?: string;
  family_name?: string;
  email?: string;
  group?: string[];
  assigned_vessel_id: string;
};
