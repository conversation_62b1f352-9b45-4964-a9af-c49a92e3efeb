/* eslint-disable no-undef */

export interface Filter {
  type: FilterType;
  subtype: any;
  defaultTab?: string;
}

export interface FilterType {
  type: string;
  name: string;
  section: string;
  inputType: string;
  queryType: string;
  queryKey: string;
  isSearchable?: boolean;
  notMultiple?: boolean;
  validTabs?: string[];
}

export interface PaginateData {
  pageSize: number;
  pageIndex: number;
  sortBy: {
    id: string;
    desc: boolean;
  }[];
}

export interface SeafarerTrainingCourse {
  rowindex: number;
  user: string;
  training_category: string;
  sub_category: string;
  course_name: string;
  required_course: string;
  status: string;
  document_type: string;
  passed_test: string;
  score: string;
  date_completed: string;
  activityname: string;
}
