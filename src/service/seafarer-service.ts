import { qsDefaultOptions } from '@src/util';
import QueryString from 'qs';
import { CancelTokenSource } from 'axios';
import { OPERATION_CANCELLED_ERROR_TEXT } from '@src/constants/crewPlanner';
import { findDocumentURL } from '../constants/documentTypes';
import UserService from './user-service';
import httpService from './http-service';
import { toast } from 'react-toastify';

const {
  VESSEL_HOST,
  SEAFARER_HOST,
  MEDIA_HOST,
  CREW_ASSIGNMENT_HOST,
  KEYCLOAK_API_HOST,
  SHIP_PARTY_HOST,
} = process.env;

export const createHeaders = async () => {
  const token = await UserService.getToken();
  return {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${token}`,
  };
};

export const getSeafarerDropDownData = async (query = '') => {
  const { data } = await httpService
    .getAxiosClient()
    .get(`${SEAFARER_HOST}/get-lookup-data${query}`, {
      headers: await createHeaders(),
    });
  return data;
};

export const getSeafarerReportingOfficeDropDownData = async () => {
  const { data } = await httpService
    .getAxiosClient()
    .get(`${SEAFARER_HOST}/get-reporting-offices`, {
      headers: await createHeaders(),
    });
  return data;
};

export const getTechGroupDropDown = async () => {
  const { data } = await httpService
    .getAxiosClient()
    .get(`${KEYCLOAK_API_HOST}/vessel/get-tech-group`, {
      headers: await createHeaders(),
    });
  return data;
};

export const getSeafarer = async (seafarerId, includeDerivedValue = false, values = '') => {
  const response = await httpService
    .getAxiosClient()
    .get(`${SEAFARER_HOST}/${seafarerId}${values}`, {
      headers: await createHeaders(),
    });

  if (includeDerivedValue) {
    const person = response.data.seafarer_person;
    if (person.seaman_books) {
      person.seaman_books = person.seaman_books.map((i) => {
        i.has_no_date_of_expiry = true;
        if (i.date_of_expiry) {
          i.has_no_date_of_expiry = false;
        }
        return i;
      });
    }
  }
  return response;
};

export const getSeafarerWages = async (seafarerId, query = '') => {
  return httpService.getAxiosClient().get(`${CREW_ASSIGNMENT_HOST}/${seafarerId}/wages${query}`, {
    headers: await createHeaders(),
  });
};

export const getPayheads = async () => {
  return httpService.getAxiosClient().get(`${CREW_ASSIGNMENT_HOST}/payheads`, {
    headers: await createHeaders(),
  });
};

export const getSefarerWagesHistory = async (seafarerId, statusHistoryId) => {
  return httpService
    .getAxiosClient()
    .get(
      `${CREW_ASSIGNMENT_HOST}/${seafarerId}/wages?seafarer_status_history_id=${statusHistoryId}&with_history=true&status=obsolete%7Capplied`,
      {
        headers: await createHeaders(),
      },
    );
};

export const createSeafarerWages = async (seafarerId, seafarerStatusHistoryId, seafarerWages) => {
  return httpService
    .getAxiosClient()
    .post(
      `${CREW_ASSIGNMENT_HOST}/${seafarerId}/wages?seafarer_status_history_id=${seafarerStatusHistoryId}`,
      seafarerWages,
      {
        headers: await createHeaders(),
      },
    );
};

export const patchSeafarerWages = async (
  seafarerId,
  seafarerStatusHistoryId,
  seafarerWagesId,
  payload,
) => {
  return httpService
    .getAxiosClient()
    .patch(
      `${CREW_ASSIGNMENT_HOST}/${seafarerId}/wages/${seafarerWagesId}?seafarer_status_history_id=${seafarerStatusHistoryId}`,
      payload,
      {
        headers: await createHeaders(),
      },
    );
};

export const deleteSeafarerWages = async (seafarerId, seafarerWagesId) => {
  return httpService
    .getAxiosClient()
    .delete(`${CREW_ASSIGNMENT_HOST}/${seafarerId}/wages/${seafarerWagesId}`, {
      headers: await createHeaders(),
    });
};

export const getSeafarerPreJoiningDetails = async (seafarerId, query = {}) => {
  return httpService.getAxiosClient().get(`${CREW_ASSIGNMENT_HOST}/${seafarerId}/pre-joining`, {
    headers: await createHeaders(),
    params: query,
  });
};

export const createSeafarerPreJoiningDetails = async (
  seafarerId,
  seafarerStatusHistoryId,
  seafarerPreJoiningPayload,
) => {
  return httpService
    .getAxiosClient()
    .post(
      `${CREW_ASSIGNMENT_HOST}/${seafarerId}/pre-joining?seafarer_status_history_id=${seafarerStatusHistoryId}`,
      seafarerPreJoiningPayload,
      {
        headers: await createHeaders(),
      },
    );
};

export const patchSeafarerPreJoiningDetails = async (
  seafarerId,
  seafarerStatusHistoryId,
  seafarerPreJoiningPayload,
) => {
  return httpService
    .getAxiosClient()
    .patch(
      `${CREW_ASSIGNMENT_HOST}/${seafarerId}/pre-joining?seafarer_status_history_id=${seafarerStatusHistoryId}`,
      seafarerPreJoiningPayload,
      {
        headers: await createHeaders(),
      },
    );
};

export const getSeafarerExperience = async (seafarerId) => {
  return httpService.getAxiosClient().get(`${SEAFARER_HOST}/experience/${seafarerId}`, {
    headers: await createHeaders(),
  });
};

export const createSeafarerExp = async (exp) => {
  return httpService.getAxiosClient().post(`${SEAFARER_HOST}/experience/create`, exp, {
    headers: await createHeaders(),
  });
};

export const patchSeafarerExp = async (expId, exp) => {
  return httpService.getAxiosClient().patch(`${SEAFARER_HOST}/experience/edit/${expId}`, exp, {
    headers: await createHeaders(),
  });
};

export const deleteSeafarerExp = async (expId) => {
  return httpService.getAxiosClient().delete(`${SEAFARER_HOST}/experience/delete/${expId}`, {
    headers: await createHeaders(),
  });
};

let cancelToken: CancelTokenSource;
export const getSeafarers = async (activeTab, query = '', relations = true) => {
  console.log('get data for active tab', activeTab);
  if (typeof cancelToken !== typeof undefined) {
    cancelToken.cancel(OPERATION_CANCELLED_ERROR_TEXT);
  }
  cancelToken = httpService.axios.CancelToken.source();
  const withRelations = relations ? 'withRelations=true' : '';
  const statusParam = activeTab ? `screening_status=${activeTab}` : '';
  const fixedQuery = query
    .split('&')
    .filter(
      (q) =>
        !q.startsWith('state=') &&
        !q.startsWith('code=') &&
        !q.startsWith('session_state=') &&
        !q.startsWith('redirect'),
    )
    .join('&');
  const queryParams = [statusParam, withRelations, fixedQuery].join('&');
  return httpService.getAxiosClient().get(`${SEAFARER_HOST}/query?${queryParams}`, {
    headers: await createHeaders(),
    cancelToken: cancelToken.token,
  });
};

export const getSeafarerListUsernames = async (activeTab, query = '') => {
  console.log('get data for active tab', activeTab);
  if (typeof cancelToken !== typeof undefined) {
    cancelToken.cancel(OPERATION_CANCELLED_ERROR_TEXT);
  }
  cancelToken = httpService.axios.CancelToken.source();
  const withRelations = 'withRelations=false';
  const statusParam = `screening_status=${activeTab}`;
  const fixedQuery = query
    .split('&')
    .filter(
      (q) =>
        !q.startsWith('state=') &&
        !q.startsWith('code=') &&
        !q.startsWith('session_state=') &&
        !q.startsWith('redirect'),
    )
    .join('&');
  const fields = ['f=created_by_hash', 'f=updated_by_hash'].join('&');
  const withDecryptUsernames = 'withDecryptUsernames=true';
  const queryParams = [statusParam, withRelations, fixedQuery, fields, withDecryptUsernames].join(
    '&',
  );
  return httpService.getAxiosClient().get(`${SEAFARER_HOST}/query?${queryParams}`, {
    headers: await createHeaders(),
    cancelToken: cancelToken.token,
  });
};

export const getSeafarerFieldsData = async (seafarerId, fields) => {
  console.log('## get seafarer fields data for id ', seafarerId);
  const fieldsParam = fields.map((field) => `f=${field}`).join('&');
  const queryParam = `id=${seafarerId}&${fieldsParam}`;
  return httpService.getAxiosClient().get(`${SEAFARER_HOST}/query?${queryParam}`, {
    headers: await createHeaders(),
  });
};

export const getSeafarerByHKID = async (hkid) => {
  const queryParam = `hkid=${hkid}`;
  return httpService.getAxiosClient().get(`${SEAFARER_HOST}/query?${queryParam}`, {
    headers: await createHeaders(),
  });
};

export const createSeafarer = async (seafarer) => {
  return httpService.getAxiosClient().post(`${SEAFARER_HOST}/create`, seafarer, {
    headers: await createHeaders(),
  });
};

export const patchSeafarer = async (id, seafarer) => {
  return httpService.getAxiosClient().post(`${SEAFARER_HOST}/${id}/patch`, seafarer, {
    headers: await createHeaders(),
  });
};

// Images
export const getPassportUploadLink = async (passportId) => {
  const headers = await createHeaders();
  return httpService
    .getAxiosClient()
    .get(`${SEAFARER_HOST}/get-passport-upload-link/${passportId}`, {
      headers,
    });
};

export const savePassportMetadata = async (passportId) => {
  const headers = await createHeaders();
  return httpService.getAxiosClient().post(
    `${SEAFARER_HOST}/save-passport-metadata/${passportId}`,
    {},
    {
      headers,
    },
  );
};

export const uploadPassportImage = async (image, passportId) => {
  const urlResult = await getPassportUploadLink(passportId);
  const fileData = image.get('passport');
  console.log(fileData);
  await uploadFileWithUrl(urlResult.data.url, fileData);
  return savePassportMetadata(passportId);
};

export const getSeamanBookUploadLink = async (seamanbookId) => {
  const headers = await createHeaders();
  return httpService
    .getAxiosClient()
    .get(`${SEAFARER_HOST}/get-seamanbook-upload-link/${seamanbookId}`, {
      headers,
    });
};

export const saveSeamanBookMetadata = async (seamanbookId) => {
  const headers = await createHeaders();
  return httpService.getAxiosClient().post(
    `${SEAFARER_HOST}/save-seamanbook-metadata/${seamanbookId}`,
    {},
    {
      headers,
    },
  );
};

export const uploadSeamanBookImage = async (image, seamanbookId) => {
  const urlResult = await getSeamanBookUploadLink(seamanbookId);
  const fileData = image.get('seaman_book');
  await uploadFileWithUrl(urlResult.data.url, fileData);
  return saveSeamanBookMetadata(seamanbookId);
};

export const uploadBankAccountImage = async (image, accountId) => {
  const headers = await createHeaders();
  headers['Content-Type'] = 'multipart/form-data';
  return httpService
    .getAxiosClient()
    .post(`${SEAFARER_HOST}/upload-bank-account-document/${accountId}`, image, {
      headers,
    });
};

export const uploadSeafarerImage = async (image, accountId) => {
  const headers = await createHeaders();
  headers['Content-Type'] = 'multipart/form-data';
  return httpService
    .getAxiosClient()
    .post(`${SEAFARER_HOST}/upload-seafarer-photo/${accountId}`, image, {
      headers,
    });
};

export const deleteDocument = async (id, documentType) => {
  const headers = await createHeaders();
  headers['Content-Type'] = 'multipart/form-data';
  const documentUrl = findDocumentURL(documentType);
  return httpService.getAxiosClient().delete(`${SEAFARER_HOST}/${documentUrl}/${id}`, {
    headers,
  });
};

export const getPassportDownloadLink = async (document_id) => {
  const headers = await createHeaders();
  return httpService
    .getAxiosClient()
    .get(`${SEAFARER_HOST}/get-passport-download-link/${document_id}`, {
      headers,
    });
};

export const downloadPassportImage = async (id, mime) => {
  const urlResult = await getPassportDownloadLink(id);
  const path = urlResult.data.url;
  const headers = {};
  headers.Accept = mime ?? 'image/jpeg, image/png, application/pdf';
  return httpService.getAxiosClient().get(path, {
    headers,
    responseType: 'arraybuffer',
  });
};

export const getSeafarerDocumentDownloadLink = async (document_id) => {
  const headers = await createHeaders();
  return httpService
    .getAxiosClient()
    .get(`${SEAFARER_HOST}/seafarer_document/${document_id}/get-download-link`, {
      headers,
    });
};

export const downloadSeafarerDocument = async (document_id) => {
  const urlResult = await getSeafarerDocumentDownloadLink(document_id);
  const headers = {};
  headers.Accept = 'image/jpeg, image/png, application/pdf';
  const path = urlResult.data.url;
  return httpService.getAxiosClient().get(path, {
    headers,
    responseType: 'arraybuffer',
  });
};

export const getSeafarerDocumentsList = async (seafarer_person_id, query) => {
  return httpService
    .getAxiosClient()
    .get(`${SEAFARER_HOST}/seafarer_documents/${seafarer_person_id}${query}`, {
      headers: await createHeaders(),
    });
};

export const getSeamanBookDownloadLink = async (document_id) => {
  const headers = await createHeaders();
  return httpService
    .getAxiosClient()
    .get(`${SEAFARER_HOST}/get-seamanbook-download-link/${document_id}`, {
      headers,
    });
};

export const downloadSeamanBookImage = async (id, mime) => {
  const urlResult = await getSeamanBookDownloadLink(id);
  const path = urlResult.data.url;
  const headers = {};
  headers.Accept = mime ?? 'image/jpeg, image/png, application/pdf';
  return httpService.getAxiosClient().get(path, {
    headers,
    responseType: 'arraybuffer',
  });
};

export const downloadSeafarerImage = async (id, mime) => {
  const headers = await createHeaders();
  headers.Accept = mime ?? 'image/jpeg, image/png, application/pdf';
  return httpService.getAxiosClient().get(`${SEAFARER_HOST}/download-seafarer-photo/${id}`, {
    headers,
    responseType: 'arraybuffer',
  });
};

export const downloadBankAccountImage = async (id, mime) => {
  const headers = await createHeaders();
  headers.Accept = mime ?? 'image/jpeg, image/png, application/pdf';
  return httpService.getAxiosClient().get(`${SEAFARER_HOST}/download-bank-account-document/${id}`, {
    headers,
    responseType: 'arraybuffer',
  });
};

export const uploadCaseReportImages = async (images, approvalId) => {
  const headers = await createHeaders();
  headers['Content-Type'] = 'multipart/form-data';
  return httpService
    .getAxiosClient()
    .post(`${SEAFARER_HOST}/save-screening-docs/${approvalId}`, images, {
      headers,
    });
};

export const downloadScreeningDocument = async (id, mime) => {
  const headers = await createHeaders();
  headers.Accept = mime ?? 'image/jpeg, image/png, application/pdf';
  return httpService.getAxiosClient().get(`${SEAFARER_HOST}/download-screening-doc/${id}`, {
    headers,
    responseType: 'arraybuffer',
  });
};

// Duplicates
export const getPassportDuplicates = async (passportNumber) => {
  if (passportNumber === undefined || passportNumber === null || passportNumber === '') {
    return null;
  }
  const fields = 'f=hkid&f=seafarer_rank.value&f=seafarer_reporting_office.value&f=parent_hkid';
  return httpService
    .getAxiosClient()
    .get(`${SEAFARER_HOST}/query?seafarer_person:passports.number=${passportNumber}&${fields}`, {
      headers: await createHeaders(),
    });
};

export const getSeamansBookDuplicates = async (seamansBookNumber) => {
  if (seamansBookNumber === undefined || seamansBookNumber === null || seamansBookNumber === '') {
    return null;
  }
  const fields = 'f=hkid&f=seafarer_rank.value&f=seafarer_reporting_office.value&f=parent_hkid';
  return httpService
    .getAxiosClient()
    .get(
      `${SEAFARER_HOST}/query?seafarer_person:seaman_books.number=${encodeURIComponent(
        seamansBookNumber,
      )}&${fields}`,
      {
        headers: await createHeaders(),
      },
    );
};

export const getPersonalDetailsDuplicates = async (personalDetails) => {
  if (personalDetails === undefined || personalDetails === null) {
    return null;
  }

  const fields = 'f=hkid&f=seafarer_rank.value&f=seafarer_reporting_office.value&f=parent_hkid';

  const firstName = personalDetails.first_name ?? '';
  const lastName = personalDetails.last_name ?? '';
  const date = personalDetails.date_of_birth ?? '';

  return httpService
    .getAxiosClient()
    .get(
      `${SEAFARER_HOST}/query?first_name=${firstName}&last_name=${lastName}&date_of_birth=${date}&${fields}`,
      {
        headers: await createHeaders(),
      },
    );
};

let getParentSeafarerToken: CancelTokenSource;
export const getParentSeafarerDetails = async (hkid) => {
  if (hkid === undefined || hkid === null) {
    return null;
  }
  if (typeof getParentSeafarerToken !== typeof undefined) {
    getParentSeafarerToken.cancel(OPERATION_CANCELLED_ERROR_TEXT);
  }
  getParentSeafarerToken = httpService.axios.CancelToken.source();
  const fields = 'withRelations=true';
  return httpService.getAxiosClient().get(`${SEAFARER_HOST}/query?hkid=${hkid}&${fields}`, {
    headers: await createHeaders(),
    cancelToken: getParentSeafarerToken.token,
  });
};

export const patchParentHKID = async (reqObj) => {
  return httpService.getAxiosClient().put(`${SEAFARER_HOST}/${reqObj.id}/patch-parent`, reqObj, {
    headers: await createHeaders(),
  });
};

export const requestDownloadUrl = async (images) => {
  return httpService.getAxiosClient().post(`${MEDIA_HOST}/request-download-url`, images, {
    headers: await createHeaders(),
  });
};

export const requestUploadUrl = async (images) => {
  return httpService.getAxiosClient().post(`${MEDIA_HOST}/request-upload-url`, images, {
    headers: await createHeaders(),
  });
};

export const isAnyInputPending = async (id) => {
  return httpService.getAxiosClient().get(`${VESSEL_HOST}/is-input-pending/${id}`, {
    headers: await createHeaders(),
  });
};

export const getLastPosition = async (imo) => {
  return httpService.getAxiosClient().get(`${VESSEL_HOST}/get-last-position/${imo}`, {
    headers: await createHeaders(),
  });
};

export const applyPendingStatus = async (id) => {
  return httpService.getAxiosClient().post(
    `${VESSEL_HOST}/apply-pending-status/`,
    { vessel_id: id },
    {
      headers: await createHeaders(),
    },
  );
};

export const getChildHKID = async (seafarerPersonId) => {
  return httpService.getAxiosClient().get(`${SEAFARER_HOST}/get-child-hkids/${seafarerPersonId}`, {
    headers: await createHeaders(),
  });
};

export const getSeafarerStatus = async (id) => {
  return httpService.getAxiosClient().get(`${SEAFARER_HOST}/status/${id}`, {
    headers: await createHeaders(),
  });
};
export const updateSeafarerStatus = async (seafarerPersonId, payload) => {
  return httpService
    .getAxiosClient()
    .post(`${SEAFARER_HOST}/update-seafarer-status/${seafarerPersonId}`, payload, {
      headers: await createHeaders(),
    });
};
export const getVesselOwnershipId = async (ref_id) => {
  return httpService.getAxiosClient().get(`${VESSEL_HOST}/ownership-id/${ref_id}`, {
    headers: await createHeaders(),
  });
};

export const getExperienceRankHistory = async (id) => {
  return httpService.getAxiosClient().get(`${SEAFARER_HOST}/get-rank-history/${id}`, {
    headers: await createHeaders(),
  });
};

export const queryGraphql = async (query) => {
  const { data } = await httpService.getAxiosClient().post(`${VESSEL_HOST}/query-graphql`, query, {
    headers: await createHeaders(),
  });
  return data;
};

export const getShipPartyOwnerList = async () => {
  const { data } = await httpService
    .getAxiosClient()
    .get(`${CREW_ASSIGNMENT_HOST}/ship-party-owner-list`, {
      headers: await createHeaders(),
    });
  return data;
};

export const getShipPartyVesselManagerByRefId = async (ref_id) => {
  const { data } = await httpService
    .getAxiosClient()
    .get(`${CREW_ASSIGNMENT_HOST}/ship-party-vessel-manager?paris1_ref_id=${ref_id}`, {
      headers: await createHeaders(),
    });
  return data;
};

export const getShipPartyData = async (offset, limit) => {
  const { data } = await httpService
    .getAxiosClient()
    .get(`${SHIP_PARTY_HOST}/query?ship_party_type_id=1&limit=${limit}&offset=${offset}`, {
      headers: await createHeaders(),
    });
  return data;
};

export const getShipPartyDetailsById = async (id) => {
  const { data } = await httpService.getAxiosClient().get(`${SHIP_PARTY_HOST}/${id}`, {
    headers: await createHeaders(),
  });
  return data;
};

export const getShipPartyById = async (id) => {
  const { data } = await httpService
    .getAxiosClient()
    .get(`${CREW_ASSIGNMENT_HOST}/ship-party/${id}`, {
      headers: await createHeaders(),
    });
  return data;
};

export const getDropDownDataFromVessel = async (params = '') => {
  return queryGraphql({
    query: `{
      vesselTypes { id, value }
      miscEngines  { id, value }
      ${params}
    }
    `,
  });
};

export const getSeafarerDocumentList = async (id, params = '') => {
  return httpService.getAxiosClient().get(`${SEAFARER_HOST}/seafarer_documents/${id}${params}`, {
    headers: await createHeaders(),
  });
};

export const uploadFileWithUrl = async (url, file) => {
  return httpService.getAxiosClient().put(url, file, {
    headers: {
      'Content-Type': file.type,
    },
  });
};

export const getSeafarerDocumentUploadLink = async (payload) => {
  const headers = await createHeaders();
  return httpService
    .getAxiosClient()
    .post(`${SEAFARER_HOST}/seafarer_document/get-upload-link`, payload, {
      headers,
    });
};

export const createSeafarerDocument = async (payload) => {
  const docData = JSON.parse(payload.get('body'));
  const fileData = payload.get('document');

  let createDocumentParams;
  if (fileData !== 'undefined') {
    const createLinkParams = {
      ...docData,
      fileName: fileData.name,
    };
    const urlResult = await getSeafarerDocumentUploadLink(createLinkParams);
    await uploadFileWithUrl(urlResult.data.url, fileData);

    createDocumentParams = {
      ...docData,
      filePath: urlResult.data.filePath,
    };
  } else {
    createDocumentParams = {
      ...docData,
    };
  }

  return httpService
    .getAxiosClient()
    .post(`${SEAFARER_HOST}/seafarer_document`, createDocumentParams, {
      headers: await createHeaders(),
    });
};

export const patchSeafarerDocument = async (docId, payload) => {
  const docData = JSON.parse(payload.get('body'));
  const fileData = payload.get('document');

  const patchDocumentParams = {
    ...docData,
  };

  if (docData.hasFileChange) {
    const patchLinkParams = {
      ...docData,
      fileName: fileData.name,
    };

    const urlResult = await getSeafarerDocumentUploadLink(patchLinkParams);
    await uploadFileWithUrl(urlResult.data.url, fileData);

    patchDocumentParams.filePath = urlResult.data.filePath;
  }

  return httpService
    .getAxiosClient()
    .post(`${SEAFARER_HOST}/seafarer_document/${docId}`, patchDocumentParams, {
      headers: await createHeaders(),
    });
};

export const getSeafarerDocument = async (docId) => {
  const headers = await createHeaders();
  return httpService.getAxiosClient().get(`${SEAFARER_HOST}/seafarer_document/${docId}`, {
    headers,
  });
};

export const getSeafarerDocumentDropdown = async (query = '') => {
  const { data } = await httpService
    .getAxiosClient()
    .get(`${SEAFARER_HOST}/seafarer-document-dropdown${query}`, {
      headers: await createHeaders(),
    });
  return data;
};

export const deleteSeafarerDocument = async (id, user_defined_document_type_id = null) => {
  const headers = await createHeaders();
  return httpService.getAxiosClient().delete(`${SEAFARER_HOST}/seafarer_document/${id}/${user_defined_document_type_id}`, {
    headers,
  });
};

export const getContactLog = async (seafarerId) => {
  return httpService.getAxiosClient().get(`${SEAFARER_HOST}/contact-log/${seafarerId}`, {
    headers: await createHeaders(),
  });
};

export const createContactLog = async (seafarerId, payload) => {
  return httpService.getAxiosClient().post(`${SEAFARER_HOST}/contact-log/${seafarerId}`, payload, {
    headers: await createHeaders(),
  });
};
export const getCrewList = async (params) => {
  const queryParams = `${params}&withRelations=true`;
  return httpService.getAxiosClient().get(`${CREW_ASSIGNMENT_HOST}/crew-list?${queryParams}`, {
    headers: await createHeaders(),
  });
};

export const getRecommendedCrewList = async (params) => {
  const queryParams = `${params}&withRelations=true`;
  return httpService
    .getAxiosClient()
    .get(`${CREW_ASSIGNMENT_HOST}/recommended-crew-list?${queryParams}`, {
      headers: await createHeaders(),
    });
};

let availableSeafarersCancelToken: CancelTokenSource;
export const getAvailableSeafarers = async (queryParams, withRelations = true) => {
  if (typeof availableSeafarersCancelToken !== typeof undefined) {
    availableSeafarersCancelToken.cancel(OPERATION_CANCELLED_ERROR_TEXT);
  }
  const queryParamsObject = new URLSearchParams(queryParams);
  queryParamsObject.append('withRelations', withRelations.toString());
  availableSeafarersCancelToken = httpService.axios.CancelToken.source();
  return httpService
    .getAxiosClient()
    .get(`${CREW_ASSIGNMENT_HOST}/query-available-seafarers?${queryParamsObject.toString()}`, {
      headers: await createHeaders(),
      cancelToken: availableSeafarersCancelToken.token,
    });
};

let relieverSeafarerCancelToken: CancelTokenSource;
export const getRelieverSeafarers = async (queryParams, withRelations = true) => {
  if (typeof relieverSeafarerCancelToken !== typeof undefined) {
    relieverSeafarerCancelToken.cancel(OPERATION_CANCELLED_ERROR_TEXT);
  }
  const queryParamsObject = new URLSearchParams(queryParams);
  queryParamsObject.append('withRelations', withRelations.toString());
  relieverSeafarerCancelToken = httpService.axios.CancelToken.source();
  return httpService
    .getAxiosClient()
    .get(`${CREW_ASSIGNMENT_HOST}/relievers?${queryParamsObject.toString()}`, {
      headers: await createHeaders(),
      cancelToken: relieverSeafarerCancelToken.token,
    });
};

let contractExpirySeafarerToken: CancelTokenSource;
export const getContractExpirySeafarers = async (queryParams, iscancelTokenRequired = true) => {
  if (iscancelTokenRequired && typeof contractExpirySeafarerToken !== typeof undefined) {
    contractExpirySeafarerToken.cancel(OPERATION_CANCELLED_ERROR_TEXT);
  }
  contractExpirySeafarerToken = httpService.axios.CancelToken.source();
  return httpService
    .getAxiosClient()
    .get(`${CREW_ASSIGNMENT_HOST}/contract-expiry?${queryParams}`, {
      headers: await createHeaders(),
      cancelToken: contractExpirySeafarerToken.token,
    });
};
interface GetMissingRanksParams {
  rank?: string | string[];
  vessel_type?: string;
  tech_group?: string;
  vessel_ownership_id?: number | number[];
  owner_name?: string;
  crew_planning_status?: string | string[];
}

let missingPersonnelToken: CancelTokenSource;
export const getMissingRanks = async (
  queryParams: GetMissingRanksParams,
  iscancelTokenRequired = true,
) => {
  if (iscancelTokenRequired && typeof missingPersonnelToken !== typeof undefined) {
    missingPersonnelToken.cancel(OPERATION_CANCELLED_ERROR_TEXT);
  }
  missingPersonnelToken = httpService.axios.CancelToken.source();
  return httpService.getAxiosClient().get(`${CREW_ASSIGNMENT_HOST}/missing-rank`, {
    params: queryParams,
    paramsSerializer: (params) => QueryString.stringify(params, qsDefaultOptions),
    headers: await createHeaders(),
    cancelToken: missingPersonnelToken.token,
  });
};
let missingPersonnelCountToken: CancelTokenSource;
export const getMissingRanksCount = async (
  queryParams: GetMissingRanksParams,
  iscancelTokenRequired = true,
) => {
  if (iscancelTokenRequired && typeof missingPersonnelCountToken !== typeof undefined) {
    missingPersonnelCountToken.cancel(OPERATION_CANCELLED_ERROR_TEXT);
  }
  missingPersonnelCountToken = httpService.axios.CancelToken.source();
  const response = await httpService
    .getAxiosClient()
    .get(`${CREW_ASSIGNMENT_HOST}/missing-rank/count`, {
      params: queryParams,
      paramsSerializer: (params) => QueryString.stringify(params, qsDefaultOptions),
      headers: await createHeaders(),
      cancelToken: missingPersonnelCountToken.token,
    });
  return response.data?.total_count;
};
interface GetAdditionalCrewRequest {
  rank_id?: string;
  vessel_type?: string;
  tech_group?: string;
  vessel_ownership_id?: string;
  crew_planning_status?: string;
}

export const getAdditionalCrewRequest = async (queryParams: GetAdditionalCrewRequest) => {
  return httpService
    .getAxiosClient()
    .get(`${CREW_ASSIGNMENT_HOST}/crew-planner/additional-request`, {
      params: queryParams,
      headers: await createHeaders(),
    });
};
export const getSignOffSeafarers = async (queryParams) => {
  return httpService
    .getAxiosClient()
    .get(`${CREW_ASSIGNMENT_HOST}/contract-expiry?${queryParams}`, {
      headers: await createHeaders(),
    });
};

export const getSeafarerStatusHistoryByPersonID = async (personId: number, query = {}) => {
  return httpService.getAxiosClient().get(`${SEAFARER_HOST}/status/${personId}`, {
    params: query,
    headers: await createHeaders(),
  });
};

export const getBankNames = async () => {
  return httpService.getAxiosClient().get(`${SEAFARER_HOST}/banks`, {
    headers: await createHeaders(),
  });
};

export const getRecommendedChecks = async (seafarerId) => {
  return httpService
    .getAxiosClient()
    .get(`${CREW_ASSIGNMENT_HOST}/recommendations/seafarer/${seafarerId}/checks`, {
      headers: await createHeaders(),
    });
};
export const getRecommendedCheckList = async () => {
  return httpService.getAxiosClient().get(`${CREW_ASSIGNMENT_HOST}/recommendations/checklist`, {
    headers: await createHeaders(),
  });
};

export const createRecommendation = async (data) => {
  return httpService.getAxiosClient().post(`${CREW_ASSIGNMENT_HOST}/recommendations`, data, {
    headers: await createHeaders(),
  });
};

export const getRecommendedCheckListById = async (id) => {
  return httpService
    .getAxiosClient()
    .get(`${CREW_ASSIGNMENT_HOST}/recommendations/${id}/checklist`, {
      headers: await createHeaders(),
    });
};

export const getScreeningStatus = async (seafarerId) => {
  return httpService
    .getAxiosClient()
    .get(`${CREW_ASSIGNMENT_HOST}/seafarer/${seafarerId}/screening`, {
      headers: await createHeaders(),
    });
};

export const approveRejectRecommendation = async (data, recommendationId) => {
  return httpService
    .getAxiosClient()
    .patch(`${CREW_ASSIGNMENT_HOST}/recommendations/${recommendationId}/status`, data, {
      headers: await createHeaders(),
    });
};

export const getSeafarerBioDataById = async (id) => {
  return httpService.getAxiosClient().get(`${CREW_ASSIGNMENT_HOST}/seafarer/${id}/biodata`, {
    headers: await createHeaders(),
  });
};
export const getOcimfData = async (payload, seafarerId) => {
  return httpService
    .getAxiosClient()
    .post(`${CREW_ASSIGNMENT_HOST}/seafarer/${seafarerId}/ocimf`, payload, {
      headers: await createHeaders(),
    });
};

export const getMasterAppraisalList = async (params) => {
  return httpService.getAxiosClient().get(`${SEAFARER_HOST}/master-appraisal/query?${params}`, {
    headers: await createHeaders(),
  });
};

export const getTrainingReqListBySeafarerId = async (seafarerId) => {
  return httpService
    .getAxiosClient()
    .get(`${CREW_ASSIGNMENT_HOST}/getTrainingRequirementList/${seafarerId}`, {
      headers: await createHeaders(),
    });
};

export const getTrainingReqDetailsByTrainingReqId = async (trainingRequirementId) => {
  return httpService
    .getAxiosClient()
    .get(`${CREW_ASSIGNMENT_HOST}/training-requirement/${trainingRequirementId}`, {
      headers: await createHeaders(),
    });
};

export const getMasterAppraisal = async (masterAppraisalId) => {
  return httpService
    .getAxiosClient()
    .get(`${SEAFARER_HOST}/master-appraisal/${masterAppraisalId}`, {
      headers: await createHeaders(),
    });
};

export const getPlanningAction = async (payload) => {
  return httpService
    .getAxiosClient()
    .post(`${CREW_ASSIGNMENT_HOST}/crew-planner/get-planning-action`, payload, {
      headers: await createHeaders(),
    });
};

export const createTrainingRequirement = async (seafarerId, payload) => {
  return httpService
    .getAxiosClient()
    .post(`${CREW_ASSIGNMENT_HOST}/create-training-requirement/${seafarerId}`, payload, {
      headers: await createHeaders(),
    });
};

export const editTrainingRequirement = async (seafarerId, trainingReqId, payload) => {
  return httpService
    .getAxiosClient()
    .patch(
      `${CREW_ASSIGNMENT_HOST}/${seafarerId}/edit-training-requirement/${trainingReqId}`,
      payload,
      {
        headers: await createHeaders(),
      },
    );
};

export const getVesselBudgetKpi = async (vesselId: number) => {
  return httpService
    .getAxiosClient()
    .get(`${CREW_ASSIGNMENT_HOST}/crew-planner/vessel-budget-kpi/${vesselId}`, {
      headers: await createHeaders(),
    });
};

export const checkRingfencing = async (relieverId, shipPartyId) => {
  return httpService
    .getAxiosClient()
    .get(
      `${CREW_ASSIGNMENT_HOST}/crew-planner/check-ringfencing?ship_party_id=${shipPartyId}&reliever_id=${relieverId}`,
      {
        headers: await createHeaders(),
      },
    );
};

export const cancelAdditionalRequest = async (id: number) => {
  return httpService.getAxiosClient().patch(
    `${CREW_ASSIGNMENT_HOST}/crew-planner/cancel-additional-request/${id}`,
    {},
    {
      headers: await createHeaders(),
    },
  );
};

export const getPlannerRanks = async () => {
  return httpService.getAxiosClient().get(`${CREW_ASSIGNMENT_HOST}/crew-planner/ranks`, {
    headers: await createHeaders(),
  });
};

let suggestedSeafarersCancelToken: CancelTokenSource;
export const getSuggestedSeafarers = async (queryParams) => {
  if (typeof suggestedSeafarersCancelToken !== typeof undefined) {
    suggestedSeafarersCancelToken.cancel(OPERATION_CANCELLED_ERROR_TEXT);
  }
  suggestedSeafarersCancelToken = httpService.axios.CancelToken.source();
  return httpService.getAxiosClient().post(
    `${CREW_ASSIGNMENT_HOST}/crew-planner/generate-suggested-seafarers?${queryParams}`,
    {},
    {
      headers: await createHeaders(),
      cancelToken: suggestedSeafarersCancelToken.token,
    },
  );
};

export async function getRelieverSeafarersWithPagination(queryParams) {
  // Create query parameters for data and count
  const queryParamsForData = new URLSearchParams(queryParams);
  const queryParamsForCount = new URLSearchParams(queryParams);

  // Adjust query parameters for count and exclude data
  queryParamsForData.set('exclude_total_count', 'true');
  queryParamsForCount.set('limit', '0');
  queryParamsForCount.set('offset', '0');

  // Execute both requests concurrently
  const [availableSeafarersData, availableSeafarersPagination] = await Promise.all([
    getRelieverSeafarers(queryParamsForData.toString()),
    getRelieverSeafarers(queryParamsForCount.toString(), false),
  ]);

  availableSeafarersData.data.pagination.totalCount =
    availableSeafarersPagination.data.pagination.totalCount;
  return availableSeafarersData;
}
export const getCrewPlanningScore = async (payload) => {
  return httpService
    .getAxiosClient()
    .post(`${CREW_ASSIGNMENT_HOST}/crew-planner/get-crew-planning-score`, payload, {
      headers: await createHeaders(),
    });
};

export const patchPeriodOnBoard = async (data: {
  seafarer_id: string;
  expected_contract_end_date: string;
  seafarer_journey_remarks?: string;
}) => {
  return httpService.getAxiosClient().patch(`${CREW_ASSIGNMENT_HOST}/period-onboard`, data, {
    headers: await createHeaders(),
  });
};

export const setSeafarerToTravel = async (data: { seafarer_id: string; status_date: string }) => {
  return httpService.getAxiosClient().post(`${CREW_ASSIGNMENT_HOST}/travel`, data, {
    headers: await createHeaders(),
  });
};

export const cancelTravelPlan = async (data: { seafarer_id: string }) => {
  return httpService.getAxiosClient().post(`${CREW_ASSIGNMENT_HOST}/cancel-travel`, data, {
    headers: await createHeaders(),
  });
};

export const createContractDetails = async (data: {
  seafarer_id: string;
  expected_contract_start_date: string;
  contract_length: number;
  repatriation_port_id: number;
  repatriation_port: string;
  embarkation_port_id: number;
  embarkation_port: string;
  nominee: number;
}) => {
  return httpService.getAxiosClient().post(`${CREW_ASSIGNMENT_HOST}/contract-details`, data, {
    headers: await createHeaders(),
  });
};

export const getOcimfMatrix = async (vesselId: number) => {
  return httpService.getAxiosClient().get(`${SEAFARER_HOST}/ocimf/matrix/${vesselId}`, {
    headers: await createHeaders(),
  }).catch(e => {
    console.log(e);
    toast.error('Flag is not assigned for this vessel.')
  });
};

export default {
  getSeafarer,
  getSeafarers,
  getSeafarerListUsernames,
  createSeafarer,
  patchSeafarer,
  getSeafarerDropDownData,
  getSeafarerReportingOfficeDropDownData,
  uploadPassportImage,
  uploadSeamanBookImage,
  uploadBankAccountImage,
  downloadPassportImage,
  downloadSeamanBookImage,
  downloadScreeningDocument,
  downloadBankAccountImage,
  uploadCaseReportImages,
  getPassportDuplicates,
  getSeamansBookDuplicates,
  getPersonalDetailsDuplicates,
  requestDownloadUrl,
  requestUploadUrl,
  uploadSeafarerImage,
  downloadSeafarerImage,
  createHeaders,
  isAnyInputPending,
  getLastPosition,
  applyPendingStatus,
  getSeafarerFieldsData,
  deleteDocument,
  getParentSeafarerDetails,
  patchParentHKID,
  getChildHKID,
  getSeafarerStatus,
  updateSeafarerStatus,
  getVesselOwnershipId,
  getSeafarerExperience,
  getExperienceRankHistory,
  createSeafarerExp,
  patchSeafarerExp,
  deleteSeafarerExp,
  getDropDownDataFromVessel,
  downloadSeafarerDocument,
  getSeafarerDocumentsList,
  getSeafarerDocumentList,
  getSeafarerDocument,
  createSeafarerDocument,
  getSeafarerDocumentDropdown,
  deleteSeafarerDocument,
  getContactLog,
  createContactLog,
  getCrewList,
  getAvailableSeafarers,
  getContractExpirySeafarers,
  getTechGroupDropDown,
  getSignOffSeafarers,
  createSeafarerWages,
  deleteSeafarerWages,
  getSeafarerPreJoiningDetails,
  getSeafarerStatusHistoryByPersonID,
  getSefarerWagesHistory,
  getBankNames,
  getRecommendedChecks,
  getRecommendedCheckList,
  createRecommendation,
  getRecommendedCheckListById,
  getScreeningStatus,
  approveRejectRecommendation,
  getSeafarerBioDataById,
  getOcimfData,
  queryGraphql,
  getShipPartyOwnerList,
  getShipPartyData,
  getShipPartyDetailsById,
  getShipPartyVesselManagerByRefId,
  getMasterAppraisalList,
  getMasterAppraisal,
  getSeafarerByHKID,
  getShipPartyById,
  getSeafarerWages,
  getTrainingReqListBySeafarerId,
  createTrainingRequirement,
  getTrainingReqDetailsByTrainingReqId,
  getMissingRanks,
  getPlanningAction,
  getVesselBudgetKpi,
  getAdditionalCrewRequest,
  checkRingfencing,
  getSuggestedSeafarers,
  cancelAdditionalRequest,
  getPlannerRanks,
  getRelieverSeafarersWithPagination,
  getRelieverSeafarers,
  getMissingRanksCount,
  patchPeriodOnBoard,
  setSeafarerToTravel,
  cancelTravelPlan,
  createContractDetails,
  getRecommendedCrewList,
  getOcimfMatrix
};
