import auth from '@paris2/auth';
import httpService from './http-service';

const { CREW_ASSIGNMENT_HOST } = process.env;

const { getToken } = auth;

export const createHeaders = async () => {
  const token = await getToken();
  return {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${token}`,
  };
};

export const getTravelAgencyList = async () => {
  return httpService.getAxiosClient().get(`${CREW_ASSIGNMENT_HOST}/travel/agencies`, {
    headers: await createHeaders(),
  });
};

export const sendTravelInformation = async (payload) => {
  return httpService.getAxiosClient().post(`${CREW_ASSIGNMENT_HOST}/travel/send-pii-data`, payload, {
    headers: await createHeaders(),
  });
};
