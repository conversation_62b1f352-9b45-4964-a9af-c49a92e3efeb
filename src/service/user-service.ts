import auth from '@paris2/auth';
import AES from 'crypto-js/aes';
import Utf8 from 'crypto-js/enc-utf8';
import { UserInfo } from '../types/keycloakUser';

import httpService from './http-service';

const { KEYCLOAK_API_HOST } = process.env;

const { init, getToken, keycloak } = auth;

const USER_INFO_CACHE_KEY_PREFIX = 'user-info-cache';

export const createHeaders = async () => {
  const token = await getToken();
  return {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${token}`,
  };
};

const getCachedUserInfo = (userNameHash, secret) => {
  const cachedUserInfoString = localStorage.getItem(
    `${USER_INFO_CACHE_KEY_PREFIX}-${userNameHash}`,
  );
  if (!cachedUserInfoString) return null;
  try {
    const decryptedUserInfoString = AES.decrypt(cachedUserInfoString, secret).toString(Utf8);
    const cachedUserInfo = JSON.parse(decryptedUserInfoString);
    return cachedUserInfo;
  } catch (e) {
    return null;
  }
};

const setCachedUserInfo = (userNameHash, userInfo, secret) => {
  const encryptedUserInfo = AES.encrypt(JSON.stringify(userInfo), secret).toString();
  localStorage.setItem(`${USER_INFO_CACHE_KEY_PREFIX}-${userNameHash}`, encryptedUserInfo);
};

export const getSeafarerUserAccount = async (seafarer_id) => {
  return httpService
    .getAxiosClient()
    .get(`${KEYCLOAK_API_HOST}/users-with-attr?seafarer_id=${seafarer_id}`, {
      headers: await createHeaders(),
    });
};

export const createKeycloakAccount = async (payload) => {
  return httpService.getAxiosClient().post(`${KEYCLOAK_API_HOST}/user/create`, payload, {
    headers: await createHeaders(),
  });
};

export const updateUserAccount = async (user_email, payload) => {
  return httpService
    .getAxiosClient()
    .patch(`${KEYCLOAK_API_HOST}/user/${user_email}/update`, payload, {
      headers: await createHeaders(),
    });
};

export const resendEmailInviteToUser = async (userID, payload) => {
  return httpService
    .getAxiosClient()
    .patch(`${KEYCLOAK_API_HOST}/resend-email-invite/${userID}`, payload, {
      headers: await createHeaders(),
    });
};

export const getUserInfo = async (userNameHash) => {
  const jwtToken = await getToken();
  const secret = jwtToken.substr(0, 12);
  const cachedUserInfo = getCachedUserInfo(userNameHash, secret);
  if (cachedUserInfo) {
    return cachedUserInfo;
  }
  try {
    const {
      data: { userInfo },
    } = await httpService
      .getAxiosClient()
      .get(`${KEYCLOAK_API_HOST}/user/${encodeURIComponent(userNameHash)}?decryptUserName=true`, {
        headers: await createHeaders(),
      });
    setCachedUserInfo(userNameHash, userInfo, secret);
    return userInfo;
  } catch (e) {
    console.error(e);
    return null;
  }
};

export const getCurrentUserInfo = (): UserInfo | null => keycloak.tokenParsed ?? null;

export default {
  init,
  getToken,
  getUserInfo,
};
