import httpService from './http-service';
import UserService from './user-service';

const { REFERENCE_HOST } = process.env;

export const createHeaders = async () => {
  const token = await UserService.getToken();
  return {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${token}`,
  };
};

export const getPorts = async (countryCode, type = null) => {
  const params = type ? `countryCode=${countryCode}&type=${type}` : `countryCode=${countryCode}`;
  const { data } = await httpService
    .getAxiosClient()
    .get(`${REFERENCE_HOST}/ports?${params}`, {
      headers: await createHeaders(),
    });
  return data;
};

export const getCountries = async () => {
  const { data } = await httpService.getAxiosClient().get(`${REFERENCE_HOST}/countries`, {
    headers: await createHeaders(),
  });
  return data;
};

export const getCountryByCode = async (countryCode) => {
  const { data } = await httpService.getAxiosClient().get(`${REFERENCE_HOST}/country/${countryCode}`, {
    headers: await createHeaders(),
  });
  return data;
};

export const getPortById = async (portId) => {
  const { data } = await httpService
    .getAxiosClient()
    .get(`${REFERENCE_HOST}/port/${portId}`, {
      headers: await createHeaders(),
    });
  return data;
};

export const getPortsOfIssueForSeamanBook = async (countryCode) => {
  const { data } = await httpService
    .getAxiosClient()
    .get(`${REFERENCE_HOST}/ports-of-issue-for-seaman-book?countryCode=${countryCode}`, {
      headers: await createHeaders(),
    });
  return data;
};

export const getVisaRegionDropDownData = async () => {
  const { data } = await httpService.getAxiosClient().get(`${REFERENCE_HOST}/visa-regions`, {
    headers: await createHeaders(),
  });
  return data;
};
