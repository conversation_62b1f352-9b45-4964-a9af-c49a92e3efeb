import httpService from './http-service';
import { createHeaders } from './seafarer-service';

const { SEAFARER_HOST } = process.env;

export const getScreeningData = async (seafarerPersonId) => {
  console.log('Get screening data for seafarer person with id ', seafarerPersonId);
  return httpService
    .getAxiosClient()
    .get(`${SEAFARER_HOST}/get-screening-approvals/${seafarerPersonId}`, {
      headers: await createHeaders(),
    });
};

export const updateScreeningData = async (approvalId, approvalStatus, remarks, seafarerId) => {
  console.log(
    `updating screening approval with status ${approvalStatus} for approval record with id: ${approvalId}`,
  );

  const REMARKS_LIMIT = 1000;
  const stripedRemarks =
    remarks && remarks.length > REMARKS_LIMIT
      ? remarks.substring(0, REMARKS_LIMIT).trim()
      : remarks;

  const requestJson = {
    id: parseInt(approvalId),
    approval_status: approvalStatus,
    remarks: stripedRemarks,
    seafarer_id: parseInt(seafarerId),
  };
  console.log(`updating`, requestJson);
  return httpService
    .getAxiosClient()
    .post(`${SEAFARER_HOST}/update-screening-approval`, requestJson, {
      headers: await createHeaders(),
    });
};

export default {
  getScreeningData,
  updateScreeningData,
};
