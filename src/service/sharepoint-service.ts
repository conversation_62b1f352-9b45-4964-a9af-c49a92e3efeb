/* eslint-disable arrow-body-style */
/* eslint-disable @typescript-eslint/indent */
/* eslint-disable no-irregular-whitespace */
import auth from '@paris2/auth';

import axios from 'axios';

const { getToken } = auth;

const { TABLEAU_PROXY_HOST, SITE_NAME } = process.env;

const createHeaders = async () => {
  const token = await getToken();
  return {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${token}`,
  };
};

/**
 * GET RSS feed for site https://www.fleetship.com
 */
export const getSharepointData = async (siteID: number) => {
  return axios.get(`${TABLEAU_PROXY_HOST}/get-sp-data/${SITE_NAME}/Site Pages/${siteID}`, {
    headers: await createHeaders(),
  });
};
