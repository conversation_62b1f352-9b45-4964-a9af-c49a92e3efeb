import httpService from './http-service';

const { LMS_HOST, LMS_HOST_TOKEN } = process.env;
const getToken = () =>
  httpService
    .getAxiosClient()
    .get(
      `${LMS_HOST}/webservice/rest/server.php?wstoken=${LMS_HOST_TOKEN}&wsfunction=tool_token_get_token&moodlewsrestformat=json&idtype=id&idvalue=14&service=FMLAPI`,
      {
        headers: {
          'Content-Type': 'application/json',
        },
      },
    );

export const getTrainingCourses = async (seafarerhkid: number, queryParams: string) => {
  const response = await getToken();
  const token = response.data.token;
  return httpService
    .getAxiosClient()
    .get(
      `${LMS_HOST}/webservice/rest/server.php?moodlewsrestformat=json&wstoken=${token}&wsfunction=getTrainingstatus_param&hkid=${seafarerhkid}${queryParams}`,
      {
        headers: {
          'Content-Type': 'application/json',
        },
      },
    );
};

export default { getTrainingCourses };
