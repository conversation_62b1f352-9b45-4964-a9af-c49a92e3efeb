import { createHeaders } from './user-service';
import httpService from './http-service';
const { SEAFARER_SURVEY_HOST } = process.env;

export const getSuptAppraisalList = async (params: string) => {
  return httpService.getAxiosClient().get(`${SEAFARER_SURVEY_HOST}/supt-appraisal/query${params}`, {
    headers: await createHeaders(),
  });
};

export const getDebriefingList = async (params: string) => {
  return httpService.getAxiosClient().get(`${SEAFARER_SURVEY_HOST}/debriefing/query${params}`, {
    headers: await createHeaders(),
  });
};

export default { getSuptAppraisalList, getDebriefingList };
