import { createHeaders } from './user-service';
import httpService from './http-service';
import { queryGraphql } from './seafarer-service';

const { VESSEL_HOST } = process.env;

function buildParams(data) {
  const params = new URLSearchParams();

  Object.entries(data).forEach(([key, value]) => {
    if (Array.isArray(value)) {
      value.forEach((value) => params.append(key, value.toString()));
    } else {
      params.append(key, value.toString());
    }
  });

  return params.toString();
}

export const getVessels = async () => {
  return httpService.getAxiosClient().get(`${VESSEL_HOST}/ownership-names`, {
    headers: await createHeaders(),
  });
};

export const getVesselList = async (statusParam, fields, offset) => {
  const queryParams = [statusParam, fields, offset].join('&');
  return httpService.getAxiosClient().get(`${VESSEL_HOST}/query?${queryParams}`, {
    headers: await createHeaders(),
  });
};

export const getVesselById = async (vessel_id) => {
  return httpService.getAxiosClient().get(`${VESSEL_HOST}/${vessel_id}`, {
    headers: await createHeaders(),
  });
};

export const getVesselOwnershipById = async (ownership_id) => {
  return httpService.getAxiosClient().get(`${VESSEL_HOST}/ownership/${ownership_id}`, {
    headers: await createHeaders(),
  });
};

export const getAllVessels = async () => {
  return httpService
    .getAxiosClient()
    .get(`${VESSEL_HOST}/ownerships?status=active&f=name&f=id&f=vessel.id&f=vessel_type.type&limit=1500`, {
      headers: await createHeaders(),
    });
};

export const queryVesselOwnership = async (
  queryParams = 'order=created_at+desc&status=active&f=name&f=id&f=vessel.id&flatten=true',
) => {
  const { data } = await httpService
    .getAxiosClient()
    .get(`${VESSEL_HOST}/ownerships?${queryParams}`, {
      headers: await createHeaders(),
    });
  return data?.results;
};

// Using V2 API
export const getVesselV2Ownerships = async () => {
  return getVesselV2Dropdown(
    'order=name asc&status=active&status=pending_handover&status=draft&f=[vessel_ownership.vessel_account_code_new,vessel_ownership.vessel_id,emails.email,vessel_ownership.flag_date,vessel_ownership.wages_treatment,vessel_ownership.wages_accumulation,vessel.status,vessel_ownership.owner_start_date,vessel_ownership.owner_end_date,vessel_ownership.registered_owner_start_date,vessel_ownership.registered_owner_end_date,fleet_staff.tech_group,vessel.ref_id,misc_registered_owner.id,misc_registered_owner.value,misc_registered_owner.ship_party_id,owner.id,owner.value,owner.ship_party_id,vessel.id,vessel_type.value,vessel_type.type,misc_currency.id,misc_currency.value]',
  );
};
export const getVesselOwnershipV2 = async (param: string) => {
  return httpService.getAxiosClient().get(`${VESSEL_HOST}/v2/ownerships?order=name asc&${param}`, {
    headers: await createHeaders(),
  });
};

export const getVesselV2Dropdown = async (param) => {
  return httpService.getAxiosClient().get(`${VESSEL_HOST}/v2/ownerships?${param}`, {
    headers: await createHeaders(),
  });
};

export const getVesselOwnershipList = async () => {
  const params = {
    f: ['vessel.id', 'vessel.name', 'vessel.ref_id', 'misc_currency.id', 'misc_currency.value'],
    order: 'ownership_id desc',
  };
  const queryParams = buildParams(params);
  const { data } = await getVesselV2Dropdown(queryParams);
  if (!data?.results?.length) return [];

  // Transform data
  return data.results
    .filter((item) => item.name)
    .map((item) => ({
      ownership_name: item.name,
      ownership_id: item.id,
      vessel_p1_ref_id: item?.handed_over_ref_id || item?.vessel?.ref_id,
      vessel_id: item?.vessel?.id,
      misc_currency: item?.vessel?.misc_currency,
    }));
};

export const getVesselV2OwnershipById = async (ownership_id) => {
  return httpService.getAxiosClient().get(`${VESSEL_HOST}/v2/ownerships/${ownership_id}`, {
    headers: await createHeaders(),
  });
};

export const getManagedVesselDropDownDataFromVessel = async () => {
  return queryGraphql({
    query: `{
      vesselTypes { id, value }
      owners  { id, value }
    }
    `,
  });
};

export const getVesselItinerary = async (
  ownershipId: string,
  dateRange: string,
  limit: number | undefined = undefined,
  pageNo: number | undefined = undefined,
) => {
  const queryString = new URLSearchParams();
  if (Number.isInteger(limit)) queryString.append('limit', limit);
  if (Number.isInteger(pageNo)) queryString.append('offset', pageNo);
  if (dateRange) queryString.append('estimated_arrival', dateRange);
  queryString.append('orderBy', 'estimated_arrival desc');
  return httpService
    .getAxiosClient()
    .get(`${VESSEL_HOST}/itinerary/${ownershipId}?${queryString.toString()}`, {
      headers: await createHeaders(),
    });
};

export const getVesselType = async () => {
  return httpService.getAxiosClient().get(`${VESSEL_HOST}/vessel-type`, {
    headers: await createHeaders(),
  });
};

export default {
  getVessels,
  getVesselList,
  getVesselOwnershipById,
  getAllVessels,
  queryVesselOwnership,
  getVesselOwnershipList,
  getVesselById,
  getVesselOwnershipV2,
  getVesselItinerary,
  getVesselV2Dropdown,
  getVesselV2OwnershipById,
  getVesselV2Ownerships,
  getVesselType,
};
