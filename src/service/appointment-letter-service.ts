import { jsPDF } from 'jspdf';
import moment from 'moment';
import {
  AppointmentLetterData,
  AppointmentLetterWages,
  AppointmentLetterNextOfKin,
} from '../types/appointmentLetter';
import {
  toRomanNumber,
  dateAsString,
  formatCurrency,
  capitalizeArgs,
  getPhoneNumber,
  capitalizeArgsWithSeparator,
} from '../model/utils';
import appointmentLetterResource from '../resources/appointment_letter.json';
import { numberToWords } from '../util/view-utils';
import { PdfUtils } from '../util/pdf-utils';
import { DEFAULT_CURRENCY_UNIT, HOURLY } from '@src/constants/seafarer-wages';

const generate = (props: AppointmentLetterData) => {
  const {
    seafarer,
    seafarer_status_history,
    remuneration,
    homeAllotment,
    earnings,
    deductions,
    owner,
    registeredOwner,
    reportingOffice,
    vesselManager,
    vessel,
  } = props;
  const today = dateAsString(moment());
  const { reportingOfficeName } = reportingOffice;
  const pdf = new jsPDF('portrait', 'pt', 'a4', false);
  const pdfUtils = new PdfUtils(pdf, 40, 30, 'helvetica', 10, 20, 'small');
  const {
    maxWidth,
    fonts,
    fontSize,
    marginY,
    lineHeight,
    cellHeight,
    compactCellHeight,
    startX,
    endX,
    textOptions,
    contentStartY,
    titleStartY,
  } = pdfUtils;
  const isFilipino = seafarer.isFilipino;

  const renderContractSignedText = (pageYPosition: number) => {
    const signedText = `This Contract has been signed at ________________________ on **${today}**.`;
    pdf.setFontSize(fontSize);
    pdf.setFont(fonts, 'normal');
    return pdfUtils.generateParagraphsWithBold(signedText, pageYPosition + marginY, fontSize);
  };

  const processWages = (wages: AppointmentLetterWages[], showPm = false, hasTotalWages = false) => {
    const wagesArray = wages
      .filter((wage: AppointmentLetterWages) => wage.amount && wage.amount !== '0.00')
      .filter((wage: AppointmentLetterWages) => !hasTotalWages || wage.desc !== 'Total') // remove total if it exists
      .map((wage: AppointmentLetterWages, index) => {
        const { desc, currency, amount, nature } = wage;
        const romanNumberOrder = `${toRomanNumber(index + 1)}.`;
        let unit = 'p.m.';
        if (nature === HOURLY) {
          unit = 'p.h.';
        }
        return showPm
          ? [romanNumberOrder, desc, currency.toUpperCase(), `${formatCurrency(amount)}`, unit]
          : [romanNumberOrder, desc, currency.toUpperCase(), `${formatCurrency(amount)}`];
      });
    let sumUpAmount = 0;
    if (
      hasTotalWages &&
      wages.find((wage) => wage.desc === 'Total' && wage.amount && wage.amount !== '0.00')
    ) {
      sumUpAmount = parseFloat(wages.find((wage) => wage.desc === 'Total').amount);
    } else {
      sumUpAmount = wages.reduce((total, wage) => {
        const amount = parseFloat(wage.amount);
        return total + amount;
      }, 0);
    }
    if (showPm) {
      wagesArray.push(['', '', 'Total', `${formatCurrency(sumUpAmount)}`, 'p.m.']);
    } else {
      wagesArray.push(['', '', 'Total', `${formatCurrency(sumUpAmount)}`]);
    }
    return wagesArray;
  };

  const insertSeafarerEmploymentAgreement = () => {
    let yPosition = contentStartY;
    const seafarerEmploymentTitle = 'Seafarer Employment Agreement (SEA)';
    pdfUtils.addPageTitle(seafarerEmploymentTitle);
    // dear sir
    const greetingText = 'Dear Sir/Madam,';
    const dateText = `Date: ${today}`;
    pdf.setFontSize(fontSize);
    pdf.setFont(fonts, 'normal');
    pdf.text(greetingText, startX, yPosition, { align: 'left' });
    pdf.setFont(fonts, 'bold');
    pdf.text(dateText, endX, yPosition, { align: 'right' });
    pdf.setFont(fonts, 'normal');

    // on behalf of
    const paragraphText = `We, **${reportingOfficeName}**, acting on behalf ${
      reportingOffice.principalCompany
        ? `of **${reportingOffice.principalCompany}**, who in turn are agents`
        : ''
    } of **${registeredOwner.name}**, the owner of the vessel **${
      seafarer_status_history.vessel
    }**, are pleased to offer you on behalf of **${registeredOwner.name}** the post of **${
      seafarer_status_history.rank
    }** for employment on board their vessel **${
      seafarer_status_history.vessel
    }** (with IMO number **${
      vessel.imoNumber
    }**) (the "**Vessel**") on the following terms and conditions:`;
    yPosition += lineHeight + marginY;
    const behalfEndY = pdfUtils.generateParagraphsWithBold(paragraphText, yPosition, fontSize);

    // point 1 - 4
    const point1Text = `That you have read, agreed and accepted the Service Terms & Conditions for Seafarers, and the applicable Collective Bargaining
     Agreement (CBA)_______________ for the Vessel (if any) which are applicable and form a part of this Seafarer Employment Agreement. Annual leave,
     repatriation and termination are covered under the CBA and/or relevant service terms and conditions of employment.  Any subsequent changes, if made in
     future to the Service Terms & Conditions for Seafarers or CBA shall be duly notified and will be applicable to you.`;
    yPosition = behalfEndY + marginY;
    const point2Text = isFilipino
      ? `Your service tenure shall commence from the date of departure from the international airport in ${reportingOffice.country}. Boarding / lodging expenses, if any, during transit period in the country of your residence shall be for your own account.`
      : `Your service tenure shall commence from the date of departure from the international airport in ${reportingOffice.country}.`;
    const point3Text = `That your remuneration will be as follows:`;
    const remunerationsStartY = pdfUtils.generateParagraphsWihPoints(
      [point1Text, point2Text, point3Text],
      yPosition,
      fontSize,
    );

    // remunerations
    const remunerations = processWages(remuneration, true, true);
    const remunerationsCellWidth = [
      maxWidth * 0.1,
      maxWidth * 0.5,
      maxWidth * 0.1,
      maxWidth * 0.2,
      maxWidth * 0.1,
    ];
    const commencementStartY = pdfUtils.generateTable(
      remunerations,
      remunerationsCellWidth,
      cellHeight,
      startX,
      remunerationsStartY,
      undefined,
      (row, col) => col === 3,
      (row, col) => (col === 3 ? 'right' : undefined),
    );

    // 5. commencement
    const commencementDateText = dateAsString(seafarer_status_history.expectedStartDate);
    // 6. tenure
    const expectedStartDate = new Date(seafarer_status_history.expectedStartDate);
    const expectedEndDate = new Date(seafarer_status_history.expectedEndDate);
    const tenureMonths = Math.round(
      (expectedEndDate.getTime() - expectedStartDate.getTime()) / (1000 * 60 * 60 * 24 * 30),
    );
    const tenureMonthsText = numberToWords(tenureMonths);
    const tenureMonthsFormatted = tenureMonths.toString().padStart(2, '0');
    const tenureMonthsHumanReadable = `${tenureMonthsText} (${tenureMonthsFormatted})`;
    const restPoints = [
      `Subject to clause 2, the date of commencement of your tenure is **${commencementDateText}**. It has been mutually agreed that the period of your tenure shall **${tenureMonthsHumanReadable} Months ± 1 month** at operational convenience.`,
      `On completion of your tenure you will be repatriated to **${seafarer_status_history.repatriationPort}**`,
      `You are recruited by the Owners of the Vessel i.e. **${registeredOwner.name}** (the “**Vessel Owner**”) and will become their employee for the duration for which you are employed with effect from the commencement of your tenure.`,
      `**${vesselManager.name}**, having its registered office at **${vesselManager.address}**, is considered as the Ship Owner only for the limited purpose of the application of the Maritime Labour Convention as per clause 1(j) of Article II of the Maritime Labour Convention, 2006  (the “**Vessel Manager**”).`,
      'You will be required to sign and abide by the rules and regulations of the articles of the vessel as applicable.',
      'Annex A and Annex B hereto form a part of this Seafarer Employment Agreement.',
    ];
    const requireToSignText =
      '\nPlease sign and return the copy of this Seafarer Employment Agreement, as a confirmation of your acceptance.';
    const confirmationStartY = pdfUtils.generateParagraphsWihPoints(
      restPoints,
      commencementStartY + marginY,
      fontSize,
      startX,
      4,
    );
    let pageYPosition = confirmationStartY;
    pdf.addPage();
    pageYPosition = contentStartY;
    // 10. confirmation
    pageYPosition = pdfUtils.generateParagraphs([requireToSignText], pageYPosition, fontSize);

    // 11. signature
    const signatureTableData = [
      ['Yours Faithfully,', ''],
      [`${reportingOfficeName}`, 'I agree to the above'],
      ['as agents only', ''],
      ['', ''],
      ['________________________________', '________________________________'],
      [`Name: ${reportingOffice?.authorizedRepresentative}`, `Name: ${seafarer.fullName}`],
      [
        `Designation: ${reportingOffice?.authorizedRepresentativeRank}`,
        `Rank: ${seafarer_status_history.rank}`,
      ],
    ];
    const birthPlaceText =
      capitalizeArgsWithSeparator(
        [
          capitalizeArgsWithSeparator(seafarer?.placeOfBirth?.toLowerCase()?.split(' '), ' '),
          seafarer?.countryOfBirth?.toLowerCase(),
        ].flatMap((item) => item?.split(',')),
        ', ',
      ) ?? '';
    const seafarerProfileTableData = [
      ['', 'P.P. No:', seafarer.passport],
      ['', 'S.Bk. no:', seafarer.seamanBook],
      ['Original: Seafarer', 'DOB: ', dateAsString(new Date(seafarer.dateOfBirth))],
      ['Original: Office', 'POB:', ''],
      ['CC: Master', '', ''],
      [''],
    ];

    const seafarerProfileCellWidth = [maxWidth * 0.5, maxWidth * 0.11, maxWidth * 0.39];
    const signatureCellWidth = [maxWidth * 0.5, maxWidth * 0.5];
    pageYPosition = pdfUtils.generateTable(
      signatureTableData,
      signatureCellWidth,
      compactCellHeight,
      startX,
      pageYPosition,
      undefined,
      (row) => row === 5 || row === 6,
    );
    pageYPosition += marginY;
    const birthPlaceTextYPosition = pageYPosition + cellHeight * 3;
    pageYPosition = pdfUtils.generateTable(
      seafarerProfileTableData,
      seafarerProfileCellWidth,
      cellHeight,
      startX,
      pageYPosition,
      (row, col) => false,
      (row, col) => col === 2,
      undefined,
    );
    pdfUtils.generateParagraphsWithBold(
      `**${birthPlaceText}**`,
      birthPlaceTextYPosition,
      fontSize,
      maxWidth * 0.69,
      false,
    );
    renderContractSignedText(pageYPosition);

    if (isFilipino) {
      pdf.addPage();
      const currencyText = homeAllotment?.currency?.toUpperCase() ?? DEFAULT_CURRENCY_UNIT;
      const allotmentFirstMonth = moment(seafarer_status_history.expectedStartDate).format(
        'MMMM YYYY',
      );
      const allotmentCommencingMonth = moment(seafarer_status_history.expectedStartDate)
        .add(1, 'month')
        .format('MMMM YYYY');

      const homeAllotmentTitle = 'Home Allotment';
      pdfUtils.addPageTitle(homeAllotmentTitle);
      pdf.setFontSize(fontSize);
      pdf.setFont(fonts, 'normal');
      const homeAllotmentText = `Your first month (joining month) allotment to be deducted on **${allotmentFirstMonth}**. Portage Bill is determined to be **${currencyText} ${formatCurrency(
        homeAllotment?.firstAllotment,
      )}**. Commencing with month of **${allotmentCommencingMonth}**, your allotment deduction on the ship's Portage Bill is fixed at  **${currencyText} ${formatCurrency(
        homeAllotment?.monthlyAllotment,
      )}** per month.`;
      pageYPosition = pdfUtils.generateParagraphsWithBold(
        homeAllotmentText,
        contentStartY,
        fontSize,
      );
      const requireSignText =
        'You will be required to sign and abide by the rules and regulations of the articles of the vessel as applicable.';
      pageYPosition = pdfUtils.generateParagraphs(
        requireSignText,
        pageYPosition + marginY,
        fontSize,
      );

      // 11. signature
      pageYPosition = pdfUtils.generateTable(
        signatureTableData,
        signatureCellWidth,
        compactCellHeight,
        startX,
        pageYPosition,
        undefined,
        (row, col) => row === 5 || row === 6
      );
      pageYPosition += marginY;
      const birthPlaceTextYPositionForFilipino = pageYPosition + cellHeight * 3;
      pageYPosition = pdfUtils.generateTable(
        seafarerProfileTableData,
        seafarerProfileCellWidth,
        cellHeight,
        startX,
        pageYPosition,
        (row, col) => false,
        (row, col) => col === 2,
        undefined,
      );
      pdfUtils.generateParagraphsWithBold(
        `**${birthPlaceText}**`,
        birthPlaceTextYPositionForFilipino,
        fontSize,
        maxWidth * 0.69,
        false,
      );
      renderContractSignedText(pageYPosition);
    }
  };

  const insertEarningsAndDeductions = () => {
    //heading
    pdf.addPage();
    pdfUtils.addPageTitle('EARNINGS AND DEDUCTIONS');

    //Credited Section
    const earningsText = `A. The following will be credited to your account in this month's portage bill`;
    pdf.setFontSize(fontSize);
    pdf.setFont(fonts, 'normal');
    pdf.text(earningsText, startX, contentStartY);
    const earningsTextDimensions = pdf.getTextDimensions(earningsText, { maxWidth });
    const earningsStartY = contentStartY + earningsTextDimensions.h + marginY;

    // earnings
    const earningsData = processWages(earnings, false, false);
    const earningsTableWidth = [maxWidth * 0.1, maxWidth * 0.5, maxWidth * 0.1, maxWidth * 0.2];
    const deductionStartY = pdfUtils.generateTable(
      earningsData,
      earningsTableWidth,
      cellHeight,
      startX,
      earningsStartY,
      (row, col) => false,
      (row, col) => col === 3,
      (row, col) => (col === 3 ? 'right' : 'left'),
    );

    //Debited Section
    const deductionText = `B. The following will be debited from your account in this month's portage bill`;
    pdf.setFont(fonts, 'normal');
    pdf.text(deductionText, startX, deductionStartY + marginY);
    const deductionTextDimensions = pdf.getTextDimensions(deductionText, { maxWidth });
    const deductionTableStartY = deductionStartY + deductionTextDimensions.h + marginY * 2;
    const deductionsData = processWages(deductions, false, false);
    let textStartY = pdfUtils.generateTable(
      deductionsData,
      earningsTableWidth,
      cellHeight,
      startX,
      deductionTableStartY,
      (row, col) => false,
      (row, col) => col === 3,
      (row, col) => (col === 3 ? 'right' : 'left'),
    );

    pdf.setFont(fonts, 'bold');
    pdf.text(
      'No wages, allowances or dues other than those mentioned in this Seafarer Employment Agreement are payable to you.',
      startX,
      textStartY + marginY,
      textOptions,
    );

    //Contracting parties secction
    pdf.setFont(fonts, 'normal');
    textStartY += lineHeight * 6;
    pdf.text('Contracting Parties'.toUpperCase(), startX, textStartY);

    textStartY += lineHeight * 6;
    const signatureTableData = [
      // Seafarer Signature, Margin, Designation Signature
      ['', '', ''],
      ['SEAFARER', '', `Name: ${reportingOffice?.authorizedRepresentative}`],
      ['', '', `${reportingOffice?.authorizedRepresentativeRank}`],
    ];
    const signatureCellWidth = [maxWidth * 0.4, maxWidth * 0.1, maxWidth * 0.4];
    pdfUtils.generateTable(
      signatureTableData,
      signatureCellWidth,
      compactCellHeight,
      startX,
      textStartY,
      (row, col) => row === 0 && (col == 0 || col == 2),
    );

    // Seafarer Profile
    textStartY += lineHeight * 6;
    const seafarerProfileTableData = [
      ['NAME:', seafarer.fullName, '', reportingOfficeName],
      ['HKID:', seafarer.hkid, '', 'as agents only'],
      ['RANK:', seafarer_status_history.rank, '', ''],
      ['VESSEL:', seafarer_status_history.vessel, '', ''],
      ['DOB:', `${dateAsString(new Date(seafarer.dateOfBirth))}`, '', ''],
      ['POB:', `${seafarer.placeOfBirth}; ${seafarer.countryOfBirth}`, '', ''],
      [''],
    ];
    const seafarerProfileCellWidth = [
      maxWidth * 0.11,
      maxWidth * 0.34,
      maxWidth * 0.05,
      maxWidth * 0.4,
    ];
    textStartY = pdfUtils.generateTable(
      seafarerProfileTableData,
      seafarerProfileCellWidth,
      cellHeight,
      startX,
      textStartY,
      (row, col) => false,
      (row, col) => col % 2 !== 0,
    );
    renderContractSignedText(textStartY);
  };

  const insertAnnex = () => {
    let annexYPosition = contentStartY;

    const {
      drugAndAlcoholPolicy,
      healthAndSafetyPolicy,
      tradeCompliancePolicy,
      pollutionPrevention,
      personalDataAndConfidentiality,
    } = appointmentLetterResource;

    const insertAnnexPoints = (points: string[], x = startX) => {
      annexYPosition += marginY;
      pdf.setFontSize(fontSize);
      pdf.setFont(fonts, 'normal');
      annexYPosition = pdfUtils.generateParagraphsWihPoints(
        points,
        annexYPosition,
        fontSize,
        x + 10,
      );
    };

    const insertAnnexPageOne = () => {
      pdf.addPage();
      pdfUtils.addPageTitle('ANNEX A TO THE SEAFARER EMPLOYMENT AGREEMENT'.toUpperCase());
      annexYPosition = contentStartY + lineHeight;
      const introText =
        'By executing the Seafarer Employment Agreement, the seafarer undertakes to abide with the following:';
      annexYPosition = pdfUtils.generateParagraphs([introText], annexYPosition, fontSize);
      annexYPosition += lineHeight;
      // Annex Section A
      pdf.setFontSize(fontSize + 2);
      pdf.setFont(fonts, 'bold');
      pdf.text(drugAndAlcoholPolicy.title, startX, annexYPosition);

      annexYPosition += lineHeight;
      const pointsA = owner.isZeroAlcohol
        ? drugAndAlcoholPolicy.pointsZeroAlcohol
        : drugAndAlcoholPolicy.pointsDefault;
      insertAnnexPoints(pointsA);

      // Annex Section B
      annexYPosition += lineHeight * 2;
      pdf.setFontSize(fontSize + 2);
      pdf.setFont(fonts, 'bold');
      pdf.text(healthAndSafetyPolicy.title, startX, annexYPosition);

      annexYPosition += lineHeight;
      pdf.setFontSize(fontSize);
      pdf.setFont(fonts, 'normal');
      const pointsB = healthAndSafetyPolicy.points;
      insertAnnexPoints(pointsB);
    };

    const insertAnnexPageTwo = () => {
      pdf.addPage();
      // Annex Section C
      annexYPosition = contentStartY; // reset the Y Position
      pdf.setFontSize(fontSize + 2);
      pdf.setFont(fonts, 'bold');
      pdf.text(tradeCompliancePolicy.title, startX, annexYPosition);

      annexYPosition += lineHeight;
      pdf.setFontSize(fontSize);
      pdf.setFont(fonts, 'normal');
      const pointsC = tradeCompliancePolicy.points;
      insertAnnexPoints(pointsC);

      // Annex Section D
      annexYPosition += lineHeight * 2; // reset the Y Position
      pdf.setFontSize(fontSize + 2);
      pdf.setFont(fonts, 'bold');
      pdf.text(pollutionPrevention.title, startX, annexYPosition);

      annexYPosition += lineHeight + marginY;
      pdf.setFontSize(fontSize);
      pdf.setFont(fonts, 'normal');
      pdf.text(pollutionPrevention.intro, startX, annexYPosition);

      annexYPosition += lineHeight;
      const pointsD = pollutionPrevention.points;
      insertAnnexPoints(pointsD);
    };
    const insertAnnexPageThree = () => {
      pdf.addPage();

      // Annex Section E
      annexYPosition = contentStartY; // reset the Y Position
      pdf.setFontSize(fontSize + 2);
      pdf.setFont(fonts, 'bold');
      pdf.text(personalDataAndConfidentiality.title, startX, annexYPosition);

      annexYPosition += lineHeight;
      pdf.setFontSize(fontSize);
      pdf.setFont(fonts, 'normal');
      const pointsE = personalDataAndConfidentiality.points;
      insertAnnexPoints(pointsE);

      // Signature
      annexYPosition += lineHeight * 3;
      pdf.text('Contracting Parties'.toUpperCase(), startX, annexYPosition);

      annexYPosition += lineHeight * 6;
      const signatureTableData = [
        ['', '', ''],
        ['Seafarer Signature', '', `${reportingOffice?.authorizedRepresentative}`],
        ['', '', `${reportingOffice?.authorizedRepresentativeRank}`],
      ];
      const signatureCellWidth = [maxWidth * 0.4, maxWidth * 0.1, maxWidth * 0.4];

      pdfUtils.generateTable(
        signatureTableData,
        signatureCellWidth,
        compactCellHeight,
        startX,
        annexYPosition,
        (row, col) => row === 0 && (col == 0 || col == 2),
      );

      // Seafarer Profile
      annexYPosition += lineHeight * 6;
      const seafarerProfileTableData = [
        ['NAME: ', seafarer.fullName, '', reportingOfficeName],
        ['HKID: ', seafarer.hkid, '', 'as agents only'],
        ['RANK: ', seafarer_status_history.rank, '', ''],
        ['VESSEL: ', seafarer_status_history.vessel, '', ''],
        ['DOB:', `${dateAsString(new Date(seafarer.dateOfBirth))}`, '', ''],
        ['POB:', `${seafarer.placeOfBirth}; ${seafarer.countryOfBirth}`, '', ''],
        [''],
      ];
      const seafarerProfileCellWidth = [
        maxWidth * 0.11,
        maxWidth * 0.34,
        maxWidth * 0.05,
        maxWidth * 0.4,
      ];

      annexYPosition = pdfUtils.generateTable(
        seafarerProfileTableData,
        seafarerProfileCellWidth,
        cellHeight,
        startX,
        annexYPosition,
        (row, col) => false,
        (row, col) => {
          if (col === 0) return false;
          if (row === 4 && col === 3) return false; // PLACE
          return true;
        },
      );
      renderContractSignedText(annexYPosition);
    };

    insertAnnexPageOne();
    insertAnnexPageTwo();
    insertAnnexPageThree();
  };

  const insertNextOfKin = () => {
    pdf.addPage();
    const nextOfKin = appointmentLetterResource.nextOfKinDeclaration;
    pdfUtils.addPageTitle(nextOfKin.title);
    pdfUtils.addPageTitle(nextOfKin.subTitle, titleStartY + cellHeight * 2);
    let nextOfKinYPosition = contentStartY + cellHeight * 3;

    const tableData = [
      ['NAME:', seafarer.fullName, '', ''],
      ['RANK:', seafarer_status_history.rank, 'HKID:', `${seafarer.hkid}`],
      ['VESSEL JOINING:', seafarer_status_history.vessel, '', ''],
    ];
    const cellWidth = [maxWidth * 0.3, maxWidth * 0.4, maxWidth * 0.15, maxWidth * 0.15];
    const infoTableEndYPosition = pdfUtils.generateTable(
      tableData,
      cellWidth,
      cellHeight,
      startX,
      nextOfKinYPosition,
      undefined,
      (row, col) => col % 2 !== 0,
    );

    nextOfKinYPosition = infoTableEndYPosition + marginY * 2;
    const introYPosition = pdfUtils.generateNormalParagraph(nextOfKin.intro, nextOfKinYPosition);

    const nextOfKinList = seafarer.nextOfKin.filter((kin) =>
      Object.values(kin).some((value) => value),
    );

    const getTelOrMobile = (kin: AppointmentLetterNextOfKin) => {
      return kin.mobilePhone && kin.telephone
        ? `${getPhoneNumber(kin.mobilePhone)}, ${getPhoneNumber(kin.telephone)}`
        : kin.mobilePhone ?? kin.telephone ?? '';
    };

    nextOfKinYPosition = introYPosition + marginY * 2;
    let nextOfKinTableY;
    if (nextOfKinList.length) {
      const nextOfKinTbody = nextOfKinList.map((kin, index) => {
        const kinAddress = kin.address?.replace(/\r?\n/g, ' ');
        const fullName = capitalizeArgs(kin.name, kin.surname);
        return [
          ['Sl. No.', `${index + 1}`],
          ['Name:', `${fullName}`],
          ['Tel:', `${getTelOrMobile(kin)}`],
          ['Relation:', `${kin.relationship}`],
          ['Percentage:', `${kin.percentage}`],
          ['Address:', `${kinAddress}`],
          ['', ''],
        ];
      });
      const nextOfKinCellWidth = [maxWidth * 0.15, maxWidth * 0.75];
      nextOfKinTableY = pdfUtils.generateTable(
        nextOfKinTbody.flat(),
        nextOfKinCellWidth,
        cellHeight,
        startX,
        nextOfKinYPosition,
        undefined,
        (row, col) => col % 2 !== 0,
        (row, col) => 'left',
        (row, col) => (row % 7 === 5 && col === 1 ? 3 : 1), // col span as address is too long
      );
    } else {
      const tableHeader = ['Sl. No.', 'Name, Address, tel, fax', 'Relation', 'Percentage'];
      const nextOfKinTbody = Array.from({ length: 6 }, () => ['', '', '', '']);
      const nextOfKinCellWidth = [maxWidth * 0.1, maxWidth * 0.6, maxWidth * 0.15, maxWidth * 0.15];
      const nextOfKinTableData = [tableHeader, ...nextOfKinTbody];
      nextOfKinTableY = pdfUtils.generateTable(
        nextOfKinTableData,
        nextOfKinCellWidth,
        cellHeight,
        startX,
        nextOfKinYPosition,
        (row, col) => row > 0,
      );
    }

    // spouse and children
    pdf.line(startX, nextOfKinTableY, maxWidth + startX, nextOfKinTableY);
    const spouseChildrenTableData = [
      ['Information Regarding Spouse and Children'],
      ["Spouse's Info:", `${capitalizeArgs(seafarer.spouseName, seafarer.spouseSurname)}`],
      ["Children's Name & DOB:", `${seafarer.child}`],
    ];
    const spouseChildrenCellWidth = [maxWidth * 0.25, maxWidth * 0.75];
    nextOfKinYPosition = nextOfKinTableY + marginY * 2;
    pdfUtils.generateTable(
      spouseChildrenTableData,
      spouseChildrenCellWidth,
      cellHeight,
      startX,
      nextOfKinYPosition,
      undefined,
      (row, col) => row === 0,
      undefined,
      (row, col) => (row === 0 && col === 0 ? 2 : 1),
    );
    const spouseChildrenDimensions = pdf.getTextDimensions(seafarer.child, {
      maxWidth: maxWidth * 0.75,
    });

    const signatureTableData = [
      ['Signature:', '', '', '', ''],
      ['Seafarer:', seafarer.fullName, '', '', ''],
      ['Witness:', '', '', '', '(MANAGER)'],
      ['Witness:', '', '', '', '(PERSONNEL OFFICER)'],
      [''],
    ];
    const signatureCellWidth = [
      maxWidth * 0.15,
      maxWidth * 0.3,
      maxWidth * 0.1,
      maxWidth * 0.15,
      maxWidth * 0.3,
    ];
    nextOfKinYPosition += spouseChildrenDimensions.h + cellHeight + marginY * 6;
    nextOfKinYPosition = pdfUtils.generateTable(
      signatureTableData,
      signatureCellWidth,
      cellHeight,
      startX,
      nextOfKinYPosition,
      (row, col) => {
        if (row === 0 && (col === 1)) {
          return true;
        }
        if (row === 2 && col === 1) {
          return true;
        }
        if (row === 3 && col === 1) {
          return true;
        }
        return false;
      },
      (row, col) => row === 1 && col === 1,
    );
    renderContractSignedText(nextOfKinYPosition);
  };

  const insertAntiBribery = () => {
    if (owner.id !== 15 && owner.id !== 411) return; // Owner name: Spar
    pdf.addPage();
    const { antiBriberyPolicySpar } = appointmentLetterResource;
    pdfUtils.addPageTitle(antiBriberyPolicySpar.title);
    addBriberyPoints(antiBriberyPolicySpar.points);
  };

  const addBriberyPoints = (points) => {
    let yPosition = contentStartY;
    const point1EndY = pdfUtils.generateNormalParagraph(points[0], yPosition, fontSize);
    yPosition = point1EndY + marginY;
    // point 2
    pdf.setFontSize(fontSize);
    let lines = pdf.splitTextToSize(points[1], maxWidth - marginY, textOptions);
    lines.forEach((line) => {
      if (line.includes('lengthy custodial sentence')) {
        pdf.setFont(fonts, 'bold');
        pdf.text(line, startX, yPosition);
        // Calculate the width of the text to draw the line
        let lineWidth = (pdf.getStringUnitWidth(line) * fontSize) / pdf.internal.scaleFactor;
        pdf.line(startX, yPosition + 1, startX + lineWidth, yPosition + 1); // Underline the text
        yPosition += lineHeight;
      } else if (line.includes('summary dismissal, personal liability')) {
        const normalText = line.substr(0, line.indexOf('summary'));
        const boldText = line.substr(line.indexOf('summary'));
        const boldTextStartX =
          (pdf.getStringUnitWidth(normalText) * fontSize) / pdf.internal.scaleFactor + startX + 2;
        pdf.setFont(fonts, 'normal');
        pdf.text(normalText, startX, yPosition);
        pdf.setFont(fonts, 'bold');
        pdf.text(boldText, boldTextStartX, yPosition);
        let lineWidth = (pdf.getStringUnitWidth(boldText) * fontSize) / pdf.internal.scaleFactor;
        pdf.line(boldTextStartX, yPosition + 1, boldTextStartX + lineWidth, yPosition + 1); // Underline the text
        yPosition += lineHeight;
      } else {
        pdf.setFont(fonts, 'normal');
        pdf.text(line, startX, yPosition);
        yPosition += lineHeight;
      }
    });

    pdfUtils.generateParagraphs(points.slice(2), yPosition + marginY, fontSize);
  };

  const insertUndertaking = () => {
    let pageYPosition = titleStartY;
    const seafarerName = seafarer?.fullName?.replace(/\*/g, '');
    const points = [
      `My family and I, **${seafarerName}**, agree and accept that ${reportingOfficeName}'s, vessel’s manager’s and owner’s obligations towards me, **${seafarerName}**,
      are limited to complying with the Seafarer’s Employment Agreement, and the obligations specifically imposed on them under the Maritime Labour Convention
      or the corresponding national laws and regulations enacted for the welfare, health and safety of the seafarers by the Flag of the Vessel and ${reportingOfficeName}'s country.`,

      `My family and I have considered the inherent challenges and risks associated with sailing and working on the vessel, and we hereby unequivocally agree to
      the extent legally permissible, to fully and absolutely WAIVE, RELEASE, AND DISCHARGE ${reportingOfficeName}, Vessel’s manager and owner from any and all liabilities not
      specifically mentioned in Clause 1, including but not limited to any exposure towards liability arising from the Seafarer’s death, disability or personal
      injury which is higher than that provided for in the applicable law, the Service Terms and Contract and/or the Collective Bargaining Agreement.`,

      `My family and I hereby undertake that, save to the extent of obtaining legal remedies before a forum having proper jurisdiction to adjudicate any dispute,
      We shall not and shall ensure that the persons associated with us also shall not at any time engage in any form of conduct, or make any statements or representations,
      whether in writing or verbal, that disparage or otherwise impair and/or harm the reputation, goodwill or commercial interests of ${reportingOfficeName},
       vessel’s manager and vessel’s owner.`,
    ];

    pdf.addPage();
    pdf.setFontSize(fontSize + 3);
    pdf.setFont(fonts, 'bold');
    pdf.text('Undertaking:', startX, pageYPosition);
    pageYPosition += lineHeight * 2;
    pdf.setFontSize(fontSize);
    pdf.setFont(fonts, 'normal');

    pageYPosition = pdfUtils.generateParagraphsWihPoints(
      points,
      pageYPosition,
      fontSize,
      startX + 10,
    );

    let signatureStartY = pageYPosition + lineHeight * 2;
    const signatureTableData = [
      ['________________________________', ''],
      ['Seafarer', ''],
    ];
    const signatureCellWidth = [maxWidth * 0.4, maxWidth * 0.1];
    signatureStartY = pdfUtils.generateTable(
      signatureTableData,
      signatureCellWidth,
      cellHeight,
      startX,
      signatureStartY,
    );
    signatureStartY += marginY;
    const seafarerProfileTableData = [
      ['Name:', seafarer.fullName],
      ['HKID: ', seafarer.hkid],
      [''],
    ];
    const seafarerProfileCellWidth = [35, maxWidth * 0.5];
    signatureStartY = pdfUtils.generateTable(
      seafarerProfileTableData,
      seafarerProfileCellWidth,
      cellHeight,
      startX,
      signatureStartY,
      (row, col) => false,
      (row, col) => col === 1,
    );
    renderContractSignedText(signatureStartY);
  };

  const generatePdf = () => {
    insertSeafarerEmploymentAgreement();
    insertEarningsAndDeductions();
    insertAnnex();
    insertNextOfKin();
    insertAntiBribery();
    insertUndertaking();
    pdfUtils.addHeaderFooter(reportingOfficeName, reportingOffice?.address);
    pdf.save(`appointment_letter_${seafarer.hkid}.pdf`);
  };

  generatePdf();
};

export default {
  generate,
};
