import httpService from './http-service';
import { createHeaders } from './seafarer-service';

const { SEAFARER_HOST } = process.env;

/**
 * Get /seafarer/wco-case/{seafarer_person_id}
 * @param {*} seafarerPersonId
 */
export async function getWcoCase(seafarerPersonId) {
  return httpService.getAxiosClient().get(`${SEAFARER_HOST}/wco-case/${seafarerPersonId}`, {
    headers: await createHeaders(),
  });
}

/**
 * Post /seafarer/wco-case/{seafarer_person_id}/request-screening
 * @param {*} seafarerPersonId
 */
export async function requestScreening(seafarerPersonId) {
  return httpService
    .getAxiosClient()
    .post(`${SEAFARER_HOST}/wco-case/${seafarerPersonId}/request-screening`, null, {
      headers: await createHeaders(),
    });
}

/**
 * Get /seafarer/wco-case/{seafarer_person_id}/results
 * @param {*} seafarerPersonId
 */
export async function getScreeningResults(seafarerPersonId) {
  return httpService.getAxiosClient().get(`${SEAFARER_HOST}/wco-case/${seafarerPersonId}/results`, {
    headers: await createHeaders(),
  });
}
