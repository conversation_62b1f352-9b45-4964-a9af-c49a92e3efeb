import { OPERATION_CANCELLED_ERROR_TEXT } from '@src/constants/crewPlanner';
import httpService from './http-service';
import UserService from './user-service';

const { CREW_ASSIGNMENT_HOST } = process.env;

export const createHeaders = async () => {
  const token = await UserService.getToken();
  return {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${token}`,
  };
};

let cancelToken;

export const getVesselPlanByOwnershipId = async (vesselOwnershipId) => {
  return httpService
    .getAxiosClient()
    .get(`${CREW_ASSIGNMENT_HOST}/modeller-report/${vesselOwnershipId}`, {
      headers: await createHeaders(),
    });
};

export const getModellerReports = async (queryParams) => {
  if (typeof cancelToken != typeof undefined) {
    cancelToken.cancel(OPERATION_CANCELLED_ERROR_TEXT);
  }
  cancelToken = httpService.axios.CancelToken.source();
  return httpService
    .getAxiosClient()
    .get(`${CREW_ASSIGNMENT_HOST}/modeller-report/query?${queryParams}`, {
      headers: await createHeaders(),
      cancelToken: cancelToken.token,
    });
};

export const getSeafarerReports = async (queryParams = '') => {
  if (typeof cancelToken != typeof undefined) {
    cancelToken.cancel(OPERATION_CANCELLED_ERROR_TEXT);
  }
  cancelToken = httpService.axios.CancelToken.source();
  return httpService
    .getAxiosClient()
    .get(`${CREW_ASSIGNMENT_HOST}/crew-list-reports/query?${queryParams}`, {
      headers: await createHeaders(),
      cancelToken: cancelToken.token,
    });
};

export const copyVesselPlanByOwnershipId = async (vesselOwnershipId, copyFromVesselOwnershipId) => {
  return httpService.getAxiosClient().post(
    `${CREW_ASSIGNMENT_HOST}/modeller-report/${vesselOwnershipId}?copy_from_vessel_ownership_id=${copyFromVesselOwnershipId}`,
    {},
    {
      headers: await createHeaders(),
    },
  );
};

export const patchVesselPlan = async (vesselOwnershipId, vesselPlanPayload) => {
  return httpService
    .getAxiosClient()
    .patch(`${CREW_ASSIGNMENT_HOST}/modeller-report/${vesselOwnershipId}`, vesselPlanPayload, {
      headers: await createHeaders(),
    });
};

export const seafarerReportsExportToExcel = async (payload) => {
  return httpService
    .getAxiosClient()
    .post(`${CREW_ASSIGNMENT_HOST}/crew-list-reports/export`, payload, {
      headers: await createHeaders(),
    });
};

export default {
  getVesselPlanByOwnershipId,
  getModellerReports,
  getSeafarerReports,
  copyVesselPlanByOwnershipId,
  patchVesselPlan,
  seafarerReportsExportToExcel,
};
