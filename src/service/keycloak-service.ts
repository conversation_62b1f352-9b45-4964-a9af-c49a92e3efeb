import QueryString from 'qs';
import httpService from './http-service';
import UserService from './user-service';
import { qsDefaultOptions } from '@src/util';

const { KEYCLOAK_API_HOST } = process.env;

export const createHeaders = async () => {
  const token = await UserService.getToken();
  return {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${token}`,
  };
};

export const getTechGroupList = async () => {
  const response = await httpService
    .getAxiosClient()
    .get(`${KEYCLOAK_API_HOST}/vessel/get-vessel-fml-staffs/tech_group`, {
      headers: await createHeaders(),
    });
  return response.data.map((staff, index) => ({
    id: staff.id ?? index + 1,
    value: staff.full_name,
  }));
};
export const getUsers = async (queryParams: Object) => {
  const response = await httpService.getAxiosClient().get(`${KEYCLOAK_API_HOST}/users`, {
    headers: await createHeaders(),
    params: queryParams,
    paramsSerializer: (params) => QueryString.stringify(params, qsDefaultOptions),
  });
  return response?.data?.users ?? [];
};

export default {
  getTechGroupList,
  getUsers,
};
