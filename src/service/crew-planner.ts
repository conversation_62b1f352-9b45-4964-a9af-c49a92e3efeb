import httpService from './http-service';
import UserService from './user-service';

const { CREW_ASSIGNMENT_HOST } = process.env;

export const createHeaders = async () => {
  const token = await UserService.getToken();
  return {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${token}`,
  };
};

export const updateCrewPlan = async (data) => {
  return httpService.getAxiosClient().patch(`${CREW_ASSIGNMENT_HOST}/crew-planner/plan`, data, {
    headers: await createHeaders(),
  });
};
export interface ManageRankGroupArgs {
  user_id: string;
  user_role: string;
  user_name: string;
  rankgroup_id?: number[];
  is_delete_user?: boolean;
}
export interface MangeRankGroupResponse {
  message: string;
}
export const updateManageRankGroup = async (
  data: ManageRankGroupArgs,
): Promise<MangeRankGroupResponse> => {
  const response = await httpService
    .getAxiosClient()
    .post(`${CREW_ASSIGNMENT_HOST}/crew-planner/manageRankGroup`, data, {
      headers: await createHeaders(),
    });
  return response?.data ?? {};
};

export const createCrewPlan = async (crewPlan) => {
  return httpService.getAxiosClient().post(`${CREW_ASSIGNMENT_HOST}/crew-planner/plan`, crewPlan, {
    headers: await createHeaders(),
  });
};

export const plannerRemark = async (crewPlan) => {
  return httpService
    .getAxiosClient()
    .post(`${CREW_ASSIGNMENT_HOST}/crew-planner/remarks`, crewPlan, {
      headers: await createHeaders(),
    });
};

export const addSimulateItinerary = async (data: {
  vessel_id: number;
  ownership_id: number;
  port_id: number;
  date: string;
  is_active: boolean;
}) => {
  return httpService
    .getAxiosClient()
    .post(`${CREW_ASSIGNMENT_HOST}/crew-planner/simulate-itinerary`, data, {
      headers: await createHeaders(),
    });
};
export const patchSimulateItinerary = async (data: {
  id: number;
  port_id?: number;
  date?: string;
  is_active?: boolean;
}) => {
  return httpService
    .getAxiosClient()
    .patch(`${CREW_ASSIGNMENT_HOST}/crew-planner/simulate-itinerary`, data, {
      headers: await createHeaders(),
    });
};

export const getSimulateItinerary = async (vesselId: number, dates: string = '') => {
  const queryParams = new URLSearchParams();
  if (dates) {
    queryParams.append('date', dates);
  }
  return httpService
    .getAxiosClient()
    .get(
      `${CREW_ASSIGNMENT_HOST}/crew-planner/simulate-itinerary/${vesselId}?${queryParams.toString()}`,
      {
        headers: await createHeaders(),
      },
    );
};

export const addAdditionalCrew = async (data: { rank_id: number; ownership_id: number }) => {
  return httpService
    .getAxiosClient()
    .post(`${CREW_ASSIGNMENT_HOST}/crew-planner/additional-request`, data, {
      headers: await createHeaders(),
    });
};

export const getPlannerList = async () => {
  const response = await httpService
    .getAxiosClient()
    .get(`${CREW_ASSIGNMENT_HOST}/crew-planner/planner-list`, {
      headers: await createHeaders(),
    });
  return response?.data ?? [];
};
export const queryCrewPlan = async (
  itineraryIds: number[] | string[],
  simulateItinerary: number[] | string[],
  vesselOwnershipIds: number[] | string[],
  fromCreatedAt: string | null,
  toCreatedAt: string | null,
  planningStatus: number[] | string[],
) => {
  const queryParams = new URLSearchParams();
  if (itineraryIds?.length > 0) {
    queryParams.append('itineraryIds', itineraryIds?.join(','));
  }
  if (simulateItinerary?.length > 0) {
    queryParams.append('simulatedItineraryIds', simulateItinerary?.join(','));
  }
  if (vesselOwnershipIds?.length > 0) {
    queryParams.append('ownershipIds', vesselOwnershipIds?.join(','));
  }
  if (fromCreatedAt && toCreatedAt) {
    queryParams.append('fromCreatedAt', fromCreatedAt);
    queryParams.append('toCreatedAt', toCreatedAt);
  }
  if (planningStatus) {
    queryParams.append('planningStatus', planningStatus);
  }
  const response = await httpService
    .getAxiosClient()
    .get(`${CREW_ASSIGNMENT_HOST}/crew-planner/query-crew-plan?${queryParams.toString()}`, {
      headers: await createHeaders(),
    });
  return response?.data ?? [];
};
export default {
  updateCrewPlan,
  createCrewPlan,
  plannerRemark,
  getSimulateItinerary,
  addAdditionalCrew,
  updateManageRankGroup,
  getPlannerList,
  queryCrewPlan,
};
