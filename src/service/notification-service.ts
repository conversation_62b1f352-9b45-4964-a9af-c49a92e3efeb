import { AxiosResponse } from 'axios';
import { createHeaders } from './user-service';
import httpService from './http-service';

const { NOTIFICATION_HOST } = process.env;

export interface UserDetails {
  username: string;
  email: string;
}

interface EmailContent {
  subject: string | null;
  message: string;
}

export interface NotificationMessagePayload {
  users: UserDetails[];
  message: string;
  link: string;
  icon?: string;
  emailContent: EmailContent;
  ccAddress?: string[];
}

export const parseMessageToHTML = (message: string) => {
  const words = message.split('\n');
  return `
  <html>
    <body>
      ${words.join('<br/>')}
      <br/>
    </body>
  </html>
  `;
};

const sendEmailNotificationMessage = async (
  payload: NotificationMessagePayload,
): Promise<AxiosResponse<any>> =>
  httpService.getAxiosClient().post(`${NOTIFICATION_HOST}/api/notify`, payload, {
    headers: await createHeaders(),
  });

export { sendEmailNotificationMessage };
