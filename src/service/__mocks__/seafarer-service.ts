import dropdowndata from '../../test/resources/drop-down-data.json';
import bankNameResponse from '../../test/resources/bank-name-data.json';
import { getShipPartyDataResponse, getTechGroupDropDownResponse } from '../../test/resources/getMockedDropDownData';
import { masterAppraisalQueryApiResponse } from '../../test/resources/master-appraisal-mockdata';
import { trainingRequirementsApiResponse } from '../../test/resources/training-requirements-mockdata';
import { seafarerBiodataMockResponse } from '../../test/resources/seafarer-bio-data';
import { getMockedSeafarerResponse } from '@src/test/resources/seafarer-mock-data';

// seafarer with matches
const wcoCaseWithmatches = require('../../test/resources/wco-case-with-matches.json');

// seafarer with no match
const wcoCaseWithNoMatch = require('../../test/resources/wco-case-with-no-matches.json');

// seafarer with screening error
const wcoCaseWithError = require('../../test/resources/wco-case-with-error.json');

const allWcoCases = [wcoCaseWithmatches, wcoCaseWithNoMatch, wcoCaseWithError];

const mockGetSeafarerFn = jest.fn().mockImplementation(async (seafarerId) => {
  const { seafarer } = allWcoCases.find(({ seafarer: { id } }) => id === seafarerId);
  return seafarer;
});

export const getSeafarerDropDownData = jest
  .fn()
  .mockImplementation(() => Promise.resolve(dropdowndata));
export const getSeafarerReportingOfficeDropDownData = jest
  .fn()
  .mockImplementation(() => Promise.resolve(dropdowndata.offices));
export const getBankNames = jest.fn().mockImplementation(() => Promise.resolve(bankNameResponse));
export const getTechGroupDropDown = jest.fn().mockResolvedValue(getTechGroupDropDownResponse());
export const getSeafarer = mockGetSeafarerFn;

export const getCrewList = jest.fn().mockImplementation(() => Promise.resolve({}));
export const getMissingRanks = jest.fn().mockImplementation(() => Promise.resolve({}));
export const getVesselBudgetKpi = jest.fn().mockImplementation(() => Promise.resolve({}));
export const getAdditionalCrewRequest = jest.fn().mockImplementation(() => Promise.resolve({}));
export const getSeafarerWages = jest.fn().mockImplementation(() => Promise.resolve({}));
export const getScreeningStatus = jest.fn().mockImplementation(() => Promise.resolve({}));
export const getSeafarerStatusHistoryByPersonID = jest
  .fn()
  .mockImplementation(() => Promise.resolve({}));
export const createSeafarerPreJoiningDetails = jest
  .fn()
  .mockImplementation(() => Promise.resolve({}));
export const patchSeafarerPreJoiningDetails = jest
  .fn()
  .mockImplementation(() => Promise.resolve({}));
export const getSeafarerPreJoiningDetails = jest.fn().mockImplementation(() => Promise.resolve({}));
export const patchSeafarerWages = jest.fn().mockImplementation(() => Promise.resolve({}));
export const getDropDownDataFromVessel = jest.fn().mockImplementation(() => Promise.resolve({}));
export const getSefarerWagesHistory = jest.fn().mockImplementation(() => Promise.resolve({}));
export const createHeaders = jest.fn().mockImplementation(() => Promise.resolve({}));
export const getSeafarerBioDataById = jest
  .fn()
  .mockImplementation(() => Promise.resolve({ data: seafarerBiodataMockResponse }));

export const downloadSeafarerImage = jest.fn().mockImplementation(() => Promise.resolve({}));
export const getRelieverSeafarersWithPagination = jest
  .fn()
  .mockImplementation(() => Promise.resolve({}));
export const queryGraphql = jest.fn().mockImplementation(() => Promise.resolve([]));
export const getSeafarerDocumentDropdown = jest.fn().mockResolvedValue(dropdowndata);
export const getMasterAppraisalList = jest.fn().mockImplementation(() =>
  Promise.resolve({
    status: 200,
    data: masterAppraisalQueryApiResponse,
  }),
);
export const getTrainingReqListBySeafarerId = jest.fn().mockImplementation(() =>
  Promise.resolve({
    status: 200,
    data: trainingRequirementsApiResponse,
  }),
);
export const getSeafarerFieldsData = jest.fn().mockImplementation(() =>
  Promise.resolve({
    data: {
      results: [getMockedSeafarerResponse()],
    },
  }),
);
export const getSeafarerStatus = jest.fn().mockImplementation(() => Promise.resolve({ data: [] }));
export const getChildHKID = jest.fn().mockImplementation(() => Promise.resolve({ data: [] }));
export const getShipPartyOwnerList = jest.fn().mockResolvedValue(getShipPartyDataResponse());

export default {
  getSeafarerDropDownData,
  getSeafarerReportingOfficeDropDownData,
  getBankNames,
  getSeafarer,
  getCrewList,
  getMissingRanks,
  getVesselBudgetKpi,
  getAdditionalCrewRequest,
  getSeafarerWages,
  getSeafarerStatusHistoryByPersonID,
  getSeafarerPreJoiningDetails,
  patchSeafarerWages,
  getDropDownDataFromVessel,
  patchSeafarerPreJoiningDetails,
  createHeaders,
  downloadSeafarerImage,
  getRelieverSeafarersWithPagination,
  getTechGroupDropDown,
  getSeafarerBioDataById,
  queryGraphql,
  getSeafarerDocumentDropdown,
  getTrainingReqListBySeafarerId,
  getMasterAppraisalList,
  getSeafarerFieldsData,
  getSeafarerStatus,
  getChildHKID,
  getShipPartyOwnerList,
};
