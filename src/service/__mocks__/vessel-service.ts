import { vesselListDropdown } from '@src/test/resources/vessel-experience';
import {
  dropdownData,
  mockItineraryData,
  mockVesselData,
} from '../../test/resources/managed-vessels';
import { ActiveVesselData } from '../../test/resources/vessel-ownership-data';

export const queryVesselOwnership = jest
  .fn()
  .mockImplementation(() => Promise.resolve(ActiveVesselData.results));

export const getManagedVesselDropDownDataFromVessel = jest
  .fn()
  .mockImplementation(() => Promise.resolve(dropdownData));

export const getVesselOwnershipV2 = jest
  .fn()
  .mockImplementation(() => Promise.resolve(mockVesselData));

export const getVesselItinerary = jest
  .fn()
  .mockImplementation(() => Promise.resolve(mockItineraryData));
export const getVesselV2Dropdown = jest.fn(() => Promise.resolve(mockVesselData));
export const getVesselList = jest
  .fn()
  .mockImplementation(() => Promise.resolve({ data: vesselListDropdown }));
export const getVesselOwnershipList = jest.fn().mockImplementation(() => Promise.resolve([]));

export default {
  queryVesselOwnership,
  getManagedVesselDropDownDataFromVessel,
  getVesselOwnershipV2,
  getVesselItinerary,
  getVesselList,
  getVesselV2Dropdown,
  getVesselOwnershipList,
};
