// seafarer with matches
const wcoCaseWithmatches = require('../../test/resources/wco-case-with-matches.json');

// seafarer with no match
const wcoCaseWithNoMatch = require('../../test/resources/wco-case-with-no-matches.json');

// seafarer with screening error
const wcoCaseWithError = require('../../test/resources/wco-case-with-error.json');

const allWcoCases = [wcoCaseWithmatches, wcoCaseWithNoMatch, wcoCaseWithError];

const mockGetWcoCaseFn = jest.fn().mockImplementation(async (seafarerPersonId) => {
  const { wco_case } = allWcoCases.find(
    ({
      seafarer: {
        seafarer_person: { id },
      },
    }) => id === seafarerPersonId,
  );
  return wco_case;
});

const mockRequestScreening = jest.fn().mockImplementation(async (seafarerPersonId) => {
  return {
    data: {},
  };
});

export const getWcoCase = mockGetWcoCaseFn;
export const requestScreening = mockRequestScreening;
