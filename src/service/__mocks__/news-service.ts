export default {
  init: jest.fn(),
  getToken: jest.fn(),
};

const categoriesResponse = {
  pagination: {
    totalCount: 1,
    offset: 0,
    orderBy: 'catagory ASC',
  },
  response: [
    {
      deleted: false,
      deletedOn: null,
      deletedBy: null,
      createdOn: '2022-12-13T07:46:43.053Z',
      modifiedOn: null,
      id: 1,
      category: 'sample1',
      status: 1,
      createdBy: 'SYSTEM',
      modifiedBy: null,
    },
    {
      deleted: false,
      deletedOn: null,
      deletedBy: null,
      createdOn: '2022-12-13T07:46:43.053Z',
      modifiedOn: null,
      id: 2,
      category: 'sample2',
      status: 1,
      createdBy: 'SYSTEM',
      modifiedBy: null,
    },
    {
      deleted: false,
      deletedOn: null,
      deletedBy: null,
      createdOn: '2022-12-13T07:46:43.053Z',
      modifiedOn: null,
      id: 3,
      category: 'sample3',
      status: 1,
      createdBy: 'SYSTEM',
      modifiedBy: null,
    },
  ],
};

export const getCategoryList = jest
  .fn()
  .mockImplementation(() => Promise.resolve({ data: categoriesResponse }));

const newsResponse = {
  pagination: {
    totalCount: 15,
    offset: 0,
    limit: 2,
    orderBy: 'newsDate DESC',
  },
  response: [
    {
      deleted: false,
      deletedOn: null,
      deletedBy: null,
      createdOn: '2022-12-16T05:31:31.482Z',
      modifiedOn: '2022-12-16T05:31:31.482Z',
      id: 16,
      newsDate: '2022-12-16T00:00:00.000Z',
      newsCategoryId: 1,
      status: 1,
      headline: 'vijay travels',
      story: 'uttrakhand',
      createdBy: 'Ashish Kaushik',
      modifiedBy: 'Ashish Kaushik',
    },
    {
      deleted: false,
      deletedOn: null,
      deletedBy: null,
      createdOn: '2022-12-16T04:57:11.987Z',
      modifiedOn: '2022-12-16T04:57:11.987Z',
      id: 15,
      newsDate: '2022-12-16T00:00:00.000Z',
      newsCategoryId: 1,
      status: 1,
      headline: 'q latest edited',
      story: 'ml',
      createdBy: 'Ashish Kaushik',
      modifiedBy: 'Ashish Kaushik',
    },
  ],
};

export const getNews = jest.fn().mockImplementation(() => Promise.resolve({ data: newsResponse }));

export const addNews = jest
  .fn()
  .mockImplementation((payload) => Promise.resolve({ data: payload }));

export const editNews = jest
  .fn()
  .mockImplementation((id, payload) => Promise.resolve({ data: payload }));
