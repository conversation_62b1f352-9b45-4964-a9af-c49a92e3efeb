import auth from '@paris2/auth';
import httpService from './http-service';

const { CREW_ASSIGNMENT_HOST } = process.env;
const { getToken } = auth;

const createOrderBy = (sortBy) => `${sortBy[0].id} ${sortBy[0].desc ? 'DESC' : 'ASC'}`;

export const createHeaders = async () => {
  const token = await getToken();
  return {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${token}`,
  };
};

export const getCategoryList = async () => {
  return httpService.getAxiosClient().get(`${CREW_ASSIGNMENT_HOST}/news/category`, {
    headers: await createHeaders(),
  });
};

export const getNews = async (queryParam) => {
  const { pageIndex, pageSize } = queryParam;
  const limitParam = `limit=${pageSize || 10}`;
  const offset = `offset=${pageIndex * pageSize || 0}`;
  let queryParams = [limitParam, offset].join('&');
  return httpService.getAxiosClient().get(`${CREW_ASSIGNMENT_HOST}/news?${queryParams}`, {
    headers: await createHeaders(),
  });
};

export const addNews = async (payload) => {
  return httpService.getAxiosClient().post(`${CREW_ASSIGNMENT_HOST}/news`, payload, {
    headers: await createHeaders(),
  });
};

export const editNews = async (id, payload) => {
  return httpService.getAxiosClient().patch(`${CREW_ASSIGNMENT_HOST}/news/${id}`, payload, {
    headers: await createHeaders(),
  });
};

export default {
  getCategoryList,
  getNews,
  addNews,
  editNews,
};
