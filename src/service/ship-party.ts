import UserService from './user-service';
import httpService from './http-service';

const { SHIP_PARTY_HOST } = process.env;

export const createHeaders = async () => {
  const token = await UserService.getToken();
  return {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${token}`,
  };
};

export const getShipPartyData = async (params: string) => {
  return httpService.getAxiosClient().get(`${SHIP_PARTY_HOST}/query?${params}`, {
    headers: await createHeaders(),
  });
};

export default {
  getShipPartyData,
};
