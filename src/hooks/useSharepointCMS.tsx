import { useCallback, useEffect, useState } from 'react';
import parse from 'html-react-parser';
import { parseHTML } from '../util/sharepoint';
import * as sharePointService from '../service/sharepoint-service';

const SHAREPOINT_CMS_MAP = {
  'seafarer-status-instructions': 17,
  'general-faq': 18,
};

const getCmsSiteID = (name) => {
  if (!SHAREPOINT_CMS_MAP[name]) return SHAREPOINT_CMS_MAP['general-faq'];
  return SHAREPOINT_CMS_MAP[name];
};

function useSharepointCMS(path) {
  const [faqContent, setFaqContent] = useState(null);

  const getFaqContent = useCallback(async () => {
    const siteID = getCmsSiteID(path);
    const value = await sharePointService.getSharepointData(siteID);
    const richText = parse(value.data.data.CanvasContent1);
    if (richText?.props?.children) {
      parseHTML(richText?.props?.children);
    }
    setFaqContent(richText);
  }, [path]);

  useEffect(() => {
    if (faqContent !== null) return;
    getFaqContent();
  }, [setFaqContent, faqContent]);

  return faqContent;
}

export { useSharepointCMS };
