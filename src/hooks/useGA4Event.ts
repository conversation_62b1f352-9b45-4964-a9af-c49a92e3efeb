import { useGA4React } from 'ga-4-react';
import { toString } from 'lodash';

const useGA4Event = () => {
  const ga4react = useGA4React();
  const eventTracker = (action: string, label: string, category: string) => {
    try {
      ga4react?.event(action, toString(label), category, false);
    } catch (error) {
      console.log('error on google analytics', error);
    }
  };
  return { eventTracker };
};

export default useGA4Event;
