import { useCallback, useEffect, useState } from 'react';

type ServiceFunction<T, A extends any[]> = (...args: A) => Promise<T>;

export interface UseRequestOptions<T, A extends any[]> {
  lazy?: boolean;
  initialData?: T;
  onError?: (error: Error) => void;
  onComplete?: (data: T | null) => void;
  initialArgs?: A;
}

interface UseRequestReturn<T, A extends any[]> {
  data: T | null;
  error: Error | null;
  loading: boolean;
  refetch: (...args: A) => Promise<T | null>;
}

function useRequest<T, A extends any[]>(
  serviceFunction: ServiceFunction<T, A>,
  options: UseRequestOptions<T, A> = {},
): UseRequestReturn<T, A> {
  const { lazy = false, initialData = null, onError, onComplete, initialArgs = [] as A } = options;

  const [data, setData] = useState<T | null>(initialData);
  const [error, setError] = useState<Error | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(!lazy);

  const fetchData = useCallback(
    async (...fetchArgs: any) => {
      setIsLoading(true);
      setError(null);

      try {
        // Use fetchArgs if provided, otherwise use initialArgs
        const finalArgs = fetchArgs.length > 0 ? fetchArgs : initialArgs;
        const response = await serviceFunction(...finalArgs);
        setData(response);
        if (onComplete) onComplete(response);
        return response;
      } catch (err) {
        setError(err as Error);
        if (onError) onError(err as Error);
      } finally {
        setIsLoading(false);
      }
      return null;
    },
    [serviceFunction, onError, onComplete],
  );

  useEffect(() => {
    if (!lazy) {
      fetchData();
    }
  }, [lazy, fetchData]);

  return { data, error, loading: isLoading, refetch: fetchData };
}

export default useRequest;
