import useRequest, { UseRequestOptions } from './useRequest';

type ServiceFunction<T, A extends any[]> = (...args: A) => Promise<T>;

interface UseRequestReturn<T, A extends any[]> {
  data: T | null;
  error: Error | null;
  loading: boolean;
  refetch: (...args: A) => Promise<T | null>;
}

function useLazyRequest<T, A extends any[]>(
  serviceFunction: ServiceFunction<T, A>,
  options: UseRequestOptions<T, A> = {},
): [(...args: A) => Promise<T | null>, UseRequestReturn<T, A>] {
  const { data, error, loading, refetch } = useRequest(serviceFunction, {
    lazy: true,
    ...options,
  });

  const returnProps: UseRequestReturn<T, A> = {
    data,
    error,
    loading,
    refetch,
  };

  return [refetch, returnProps];
}

export default useLazyRequest;
