import React, { useCallback, useEffect, useState } from 'react';

const useMultiApiLoader = (apiCalls) => {
  const [loadingStates, setLoadingStates] = useState(
    Object.keys(apiCalls).reduce((acc, key) => {
      acc[key] = true;
      return acc;
    }, {}),
  );
  const [errorStates, setErrorStates] = useState(
    Object.keys(apiCalls).reduce((acc, key) => {
      acc[key] = null;
      return acc;
    }, {}),
  );
  const [dataStates, setDataStates] = useState({});

  const fetchData = useCallback(async () => {
    setLoadingStates(
      Object.keys(apiCalls).reduce((acc, key) => {
        acc[key] = true;
        return acc;
      }, {}),
    );
    setErrorStates(
      Object.keys(apiCalls).reduce((acc, key) => {
        acc[key] = null;
        return acc;
      }, {}),
    );
    Object.values(apiCalls).forEach((callObj, index) => {
      const key = Object.keys(apiCalls)[index];
      callObj
        .call(...(callObj.args ?? []))
        .then((res) => {
          if (typeof callObj.responseHandler === 'function') {
            res = callObj.responseHandler(res);
          }
          setDataStates((prev) => ({ ...prev, [key]: res }));
        })
        .catch((err) => {
          setErrorStates((prev) => ({ ...prev, [key]: true }));
          setLoadingStates((prev) => ({ ...prev, [key]: false }));
        })
        .finally(() => {
          setLoadingStates((prev) => ({ ...prev, [key]: false }));
        });
    });
  }, [apiCalls]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return { loadingStates, errorStates, dataStates };
};

export default useMultiApiLoader;
