const { PARIS_ONE_HOST } = process.env;

export const goToParis1 = (ref_id, shipPartyId) => {
  if (ref_id) {
    const link = shipPartyId ? '/FMLLoginKeycloak?targeturl=/fml' : '';
    window.open(
      `${PARIS_ONE_HOST}/${link}/fml/PARIS?display=crewoverview&crewid=${ref_id}`,
      '_blank',
    );
  } else {
    window.open(PARIS_ONE_HOST, '_blank');
  }
};

export const goToParis1SeafarerExp = (ref_id, shipPartyId) => {
  if (ref_id) {
    const link = shipPartyId ? '/FMLLoginKeycloak?targeturl=/fml' : '';
    window.open(
      `${PARIS_ONE_HOST}/fml/${link}/PARIS?display=experience&crewid=${ref_id}`,
      '_blank',
    );
  } else {
    window.open(PARIS_ONE_HOST, '_blank');
  }
};

export const goToParis1SeafarerDocument = (ref_id, shipPartyId) => {
  if (ref_id) {
    const link = shipPartyId ? '/FMLLoginKeycloak?targeturl=/fml' : '';
    window.open(`${PARIS_ONE_HOST}/fml/${link}/PARIS?display=documents&crewid=${ref_id}`, '_blank');
  } else {
    window.open(PARIS_ONE_HOST, '_blank');
  }
};

export const goToParis1SeafarerRecommend = (ref_id, shipPartyId) => {
  if (ref_id) {
    const link = shipPartyId ? '/FMLLoginKeycloak?targeturl=/fml' : '';
    window.open(
      `${PARIS_ONE_HOST}/fml/${link}/PARIS?display=assignment&pitype=assign&crewid=${ref_id}`,
      '_blank',
    );
  } else {
    window.open(PARIS_ONE_HOST, '_blank');
  }
};
