const filterPages = (visiblePages, totalPages) =>
  visiblePages.filter((page) => page <= totalPages);

const getVisiblePages = (page, total) => {
  if (total < 7) {
    return filterPages([1, 2, 3, 4, 5, 6], total);
  }
  if (page % 5 >= 0 && page > 4 && page + 2 < total) {
    return [1, page - 1, page, page + 1, total];
  }
  if (page % 5 >= 0 && page > 4 && page + 2 >= total) {
    return [1, total - 3, total - 2, total - 1, total];
  }
  return [1, 2, 3, 4, 5, total];
};

const generateQueryParams = (ship_party_type_id, ship_party_name, paginateParams) => {
  const { pageSize, pageIndex, sortBy } = paginateParams;
  let queryParams = '';
  if (pageSize) queryParams += `&limit=${pageSize}`;
  if (pageIndex) queryParams += `&offset=${pageIndex}`;
  if (ship_party_type_id !== 0) queryParams += `&ship_party_type_id=${ship_party_type_id}`;
  if (ship_party_name) queryParams += `&name=${ship_party_name}`;
  if (sortBy.length > 0 && sortBy[0].id) queryParams += `&orderBy=${sortBy[0].id} ${sortBy[0].desc ? 'desc' : 'asc'}`;

  return queryParams;
};

export { filterPages, getVisiblePages, generateQueryParams };
