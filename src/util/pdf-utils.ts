import { jsPDF } from 'jspdf';

function getMarginY(tableSize: string) {
  switch (tableSize) {
    case 'small':
      return 10;
    case 'middle':
      return 20;
    case 'large':
      return 30;
    default:
      return 0;
  }
}

export class PdfUtils {
  public pdf: jsPDF;
  public startY: number;
  public startX: number;
  public fonts: string;
  public marginY: number;
  public fontSize: number;
  public lineHeight: number;
  public cellHeight: number;
  public compactCellHeight: number;
  public maxWidth: number;
  public endX: number;
  public textOptions: {
    maxWidth: number;
    align: 'justify' | 'center' | 'left' | 'right' | undefined;
  };
  public titleStartY: number;
  public contentStartY: number;
  public footerStartY: number;

  constructor(
    pdf: jsPDF,
    startX: number,
    startY: number,
    fonts: string,
    fontSize: number,
    cellHeight: number,
    tableSize: 'small' | 'middle' | 'large',
    marginRight: number = 50,
  ) {
    this.pdf = pdf;
    this.startY = startY;
    this.startX = startX;
    this.fonts = fonts || 'helvetica';
    this.marginY = getMarginY(tableSize);
    this.fontSize = fontSize || 10;
    this.lineHeight = this.fontSize * 1.2 || 12;
    this.cellHeight = cellHeight || 20;
    this.compactCellHeight = this.cellHeight * 0.8;
    this.maxWidth = this.pdf.internal.pageSize.width - this.startX - marginRight;
    this.textOptions = {
      align: 'justify',
      maxWidth: this.maxWidth - marginRight,
    };
    this.endX = this.startX + this.maxWidth;
    this.titleStartY = this.startY + this.lineHeight + this.cellHeight * 1.5;
    this.contentStartY = this.startY + this.lineHeight + this.cellHeight * 3;
    this.footerStartY = pdf.internal.pageSize.height - this.cellHeight;
  }

  public addPageTitle = (title: string, startY = this.titleStartY) => {
    this.pdf.setFontSize(16);
    this.pdf.setFont(this.fonts, 'bold');
    this.pdf.text(title, this.pdf.internal.pageSize.getWidth() / 2, startY, {
      align: 'center',
    });
  };

  public addHeaderFooter = (headerText: string, footerText: string) => {
    const pageCount = this.pdf.internal.getNumberOfPages();
    for (let i = 1; i <= pageCount; i++) {
      this.pdf.setPage(i);
      this.pdf.setFontSize(12);
      this.pdf.setFont(this.fonts, 'bold');
      this.pdf.text(headerText, this.startX, this.startY); // header
      this.pdf.setFontSize(6);
      this.pdf.setFont(this.fonts, 'normal');
      this.pdf.text(footerText, this.startX, this.footerStartY); // footer
      this.pdf.text(`Page ${i} of ${pageCount}`, this.endX, this.footerStartY, { align: 'right' });
    }
  };

  public addBulletPoint = (startX: number, startY: number, char: string = "\x35") => {
    this.pdf.setFontSize(10);
    this.pdf.setFont("Zapfdingbats");
    this.pdf.text(char, startX, startY)
  };

  public generateTable(
    tableData: (string | undefined)[][],
    cellWidth: number[],
    cellHeight: number = this.cellHeight,
    startXPosition: number = this.startX,
    startYPosition: number = this.startY,
    underlineCallback?: (row: number, col: number) => boolean | undefined,
    boldCallback?: (row: number, col: number) => boolean | undefined,
    alignCallback?: (row: number, col: number) => 'left' | 'right' | 'center' | undefined,
    colSpanCallback?: (row: number, col: number) => number | undefined,
    fontSize: number = this.fontSize,
  ): number {
    let cellYPosition = startYPosition;
    let hasPageBreak = false;
    let resettedRow = 0;
    for (let row = 0; row < tableData.length; row++) {
      if (cellYPosition + cellHeight * 2 > this.footerStartY) {
        this.pdf.addPage();
        hasPageBreak = true;
        resettedRow = 0;
        cellYPosition = this.contentStartY;
      } else {
        cellYPosition = hasPageBreak
          ? this.contentStartY + resettedRow * cellHeight
          : startYPosition + row * cellHeight;
      }
      if (hasPageBreak) {
        resettedRow++;
      } else {
        resettedRow = 0;
      }
      for (let col = 0; col < tableData[row].length; col++) {
        const cellText = tableData[row][col];
        const cellXPosition = Math.min(
          startXPosition + cellWidth.slice(0, col).reduce((sum, width) => sum + width, 0),
          this.endX - cellWidth[col],
        );
        this.pdf.setFontSize(fontSize);
        this.pdf.setFont(this.fonts, boldCallback?.(row, col) ? 'bold' : 'normal');
        const alignment = alignCallback ? alignCallback(row, col) : 'left';
        const colSpan = colSpanCallback?.(row, col);
        if (colSpan && colSpan > 1) {
          const colSpanWidth = cellWidth
            .slice(col, col + colSpan)
            .reduce((sum, width) => sum + width, 0);
          this.pdf.text(cellText, cellXPosition, cellYPosition, {
            align: alignment,
            maxWidth: colSpanWidth,
          });
          col += colSpan - 1;
        } else if (alignment === 'center') {
          this.pdf.text(cellText, cellXPosition + cellWidth[col] / 2, cellYPosition, {
            align: 'center',
          });
        } else if (alignment === 'right') {
          this.pdf.text(cellText, cellXPosition + cellWidth[col] - 12, cellYPosition, {
            align: 'right',
          });
        } else {
          this.pdf.text(cellText, cellXPosition, cellYPosition, { align: 'left' });
        }
        if (underlineCallback?.(row, col)) {
          this.pdf.setLineWidth(0.2);
          this.pdf.line(
            startXPosition + cellWidth.slice(0, col).reduce((sum, width) => sum + width, 0),
            cellYPosition + 2,
            startXPosition + cellWidth.slice(0, col + 1).reduce((sum, width) => sum + width, 0),
            cellYPosition + 2,
          );
        }
      }
    }
    return cellYPosition + this.marginY;
  }

  public generateParagraphsWihPoints = (
    texts: string | string[],
    startYPosition: number,
    fontSize: number = this.fontSize,
    startXPosition: number = this.startX,
    pointStartCount: number = 1,
    justifyLineText: boolean = true,
  ) => {
    let index = pointStartCount;
    const paragraphs = Array.isArray(texts) ? texts : [texts];

    let currentYPosition = startYPosition;
    // Processing text with subpoints
    const subPointRegex = /^\s{3,}/;
    paragraphs.forEach((paragraph) => {
      const pointChar = index + '.';
      this.pdf.setFontSize(fontSize);
      this.pdf.setFont(this.fonts, 'normal');
        const isSubPoint = subPointRegex.test(paragraph);
        const currentIndent = isSubPoint ? startXPosition + 20 : startXPosition;
        if (isSubPoint) {
          this.addBulletPoint(currentIndent, currentYPosition);
        } else {
          this.pdf.text(pointChar, currentIndent, currentYPosition)
        }
      const formattedString  = paragraph.replace(/[\r\n]+/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();
      const paragraphStartX = currentIndent + 12;
      const startY = this.generateNormalAndBoldText(formattedString, currentYPosition, fontSize, paragraphStartX, justifyLineText);
      currentYPosition = startY + this.marginY;
      index ++;
    });
    return currentYPosition;
  };
  public generateParagraphsWithBold = (
    texts: string | string[],
    startYPosition: number,
    fontSize: number = this.fontSize,
    startXPosition: number = this.startX,
    justifyLineText: boolean = true,
  ) => {
    const paragraphs = Array.isArray(texts) ? texts : [texts];
    const formattedParagraphs = paragraphs.map((point) =>
      point
        .replace(/[\r\n]+/g, ' ')
        .replace(/\s+/g, ' ')
        .trim(),
    );
    let currentYPosition = startYPosition;
    formattedParagraphs.forEach((paragraph) => {

      const startY = this.generateNormalAndBoldText(paragraph, currentYPosition, fontSize, startXPosition, justifyLineText);
      currentYPosition = startY + this.marginY;
    });
    return currentYPosition;
  };

  private justifyTextLine = (
    words: Array<{ text: string; isBold: boolean }>,
    fontSize: number,
    maxWidth: number,
    leftMargin: number,
    startY: number,
    isLastLine: boolean = false,
  ): void => {
    let cursorX = leftMargin;
    const lineWidth = words.reduce((width, part) => {
      this.pdf.setFont(this.fonts, part.isBold ? 'bold' : 'normal');
      return (
        width + (this.pdf.getStringUnitWidth(part.text) * fontSize) / this.pdf.internal.scaleFactor
      );
    }, 0);
    // Calculate extra space to distribute between words
    const extraSpace = maxWidth - lineWidth;
    const spaces = words.filter((part) => part.text.trim() !== '').length - 1;
    const spaceAdjustment = spaces > 0 && !isLastLine ? extraSpace / spaces : 0;

    words.forEach((part, index) => {
      this.pdf.setFont(this.fonts, part.isBold ? 'bold' : 'normal');
      this.pdf.text(part.text, cursorX, startY);
      cursorX +=
        (this.pdf.getStringUnitWidth(part.text) * fontSize) / this.pdf.internal.scaleFactor +
        (index < words.length - 1 ? spaceAdjustment : 0);
    });
  };

  public generateNormalParagraph = (
    paragraphText: string,
    startYPosition: number,
    fontSize: number = this.fontSize,
    startXPosition: number = this.startX,
    fontWeight: string = 'normal'
  ): number => {
    const pageWidth = this.pdf.internal.pageSize.width;
    const leftMargin = startXPosition;
    const rightMargin = this.startX;
    const maxWidth = pageWidth - leftMargin - rightMargin; // Usable width

    this.pdf.setFontSize(fontSize);
    this.pdf.setFont(this.fonts, fontWeight);
    let startY = startYPosition;

    const textLines = this.pdf.splitTextToSize(paragraphText, maxWidth);

    textLines.forEach((line, index) => {
      // Add a new page if needed
      if (startY + this.lineHeight > this.footerStartY) {
        this.pdf.addPage();
        startY = this.contentStartY;
      }

      // For the last line or single-word lines
      if (index === textLines.length - 1 || line.split(' ').length === 1) {
        this.pdf.text(line, leftMargin, startY, { align: 'left' });
      } else {
        const words = line.split(' ').map((word) => ({ text: word, bold: fontWeight === 'bold' }))
        this.justifyTextLine(words, fontSize, maxWidth, leftMargin, startY);
      }

      startY += this.lineHeight;
    });
    return startY;
  };

  public generateParagraphs = (
    text: string | string[],
    startYPosition: number,
    fontSize: number = this.fontSize,
    startXPosition: number = this.startX,
  ): number => {
    const paragraphs = Array.isArray(text) ? text : [text];
    let currentYPosition = startYPosition;
    for (let paragraphText of paragraphs) {
      const startY = this.generateNormalParagraph(
        paragraphText,
        currentYPosition,
        fontSize,
        startXPosition,
      );
      currentYPosition = startY + this.marginY;
    }
    return currentYPosition;
  };

  public generateNormalAndBoldText = (
    paragraphText: string,
    startYPosition: number,
    fontSize: number = this.fontSize,
    startXPosition: number = this.startX,
    justifyLineText: boolean = true,
  ): number => {
    const pageWidth = this.pdf.internal.pageSize.width;
    const leftMargin = startXPosition;
    const rightMargin = this.startX;
    const maxWidth = pageWidth - leftMargin - rightMargin;
    let startY = startYPosition;

    const textParts = this.parseTextForFormatting(paragraphText);
    const lines = this.splitTextWithDynamicWidth(textParts, maxWidth, fontSize);

    lines.forEach((line, index) => {
      if (startY + this.lineHeight > this.footerStartY) {
        this.pdf.addPage();
        startY = this.contentStartY;
      }

      const isLastLine = index === lines.length - 1;
      this.renderLineWithFormatting(line, fontSize, leftMargin, startY, maxWidth, isLastLine, justifyLineText);
      startY += this.lineHeight;
    });

    return startY;
  };

  private splitTextWithDynamicWidth = (
    textParts: Array<{ text: string; isBold: boolean }>,
    maxWidth: number,
    fontSize: number,
  ): Array<Array<{ text: string; isBold: boolean }>> => {
    const lines: Array<Array<{ text: string; isBold: boolean }>> = [];
    let currentLine: Array<{ text: string; isBold: boolean }> = [];
    let currentLineWidth = 0;

    textParts.forEach((part) => {
      this.pdf.setFont(this.fonts, part.isBold ? 'bold' : 'normal');
      const words = part.text.split(' ');

      words.forEach((word, index) => {
        const spaceWidth =
          currentLine.length > 0
            ? (this.pdf.getStringUnitWidth(' ') * fontSize) / this.pdf.internal.scaleFactor
            : 0;
        const wordWidth =
          (this.pdf.getStringUnitWidth(word) * fontSize) / this.pdf.internal.scaleFactor;

        if (currentLineWidth + wordWidth + spaceWidth > maxWidth) {
          // Line is full
          lines.push(currentLine);
          currentLine = [];
          currentLineWidth = 0;
        }

        if (wordWidth > maxWidth) {
          // Handle overly long words (split the word)
          let remainingWord = word;
          while (
            (this.pdf.getStringUnitWidth(remainingWord) * fontSize) /
              this.pdf.internal.scaleFactor >
            maxWidth
          ) {
            let chunk = '';
            for (let word of remainingWord) {
              const chunkWidth =
                (this.pdf.getStringUnitWidth(chunk + word) * fontSize) /
                this.pdf.internal.scaleFactor;
              if (chunkWidth > maxWidth) break;
              chunk += word;
            }
            for (let word of remainingWord) {
              const chunkWidth =
                (this.pdf.getStringUnitWidth(chunk + word) * fontSize) /
                this.pdf.internal.scaleFactor;
              if (chunkWidth > maxWidth) break;
              chunk += word;
            }
            currentLine.push({ text: chunk, isBold: part.isBold });
            lines.push(currentLine);
            currentLine = [];
            currentLineWidth = 0;
            remainingWord = remainingWord.slice(chunk.length);
          }
          currentLine.push({ text: remainingWord, isBold: part.isBold });
          currentLineWidth +=
            (this.pdf.getStringUnitWidth(remainingWord) * fontSize) / this.pdf.internal.scaleFactor;
        } else {
          // Normal case for smaller words
          currentLine.push({
            text: word + (index < words.length - 1 ? ' ' : ''), // Add space if not the last word
            isBold: part.isBold,
          });
          currentLineWidth += wordWidth + spaceWidth;
        }
      });
    });

    if (currentLine.length > 0) {
      lines.push(currentLine);
    }

    return lines;
  };

  private renderLineWithFormatting = (
    line: Array<{ text: string; isBold: boolean }>,
    fontSize: number,
    startX: number,
    startY: number,
    maxWidth: number,
    isLastLine: boolean = false,
    justifyLineText: boolean = true,
  ): void => {
    let cursorX = startX;
    const totalLineWidth = line.reduce((width, part) => {
      this.pdf.setFont(this.fonts, part.isBold ? 'bold' : 'normal');
      return (
        width + (this.pdf.getStringUnitWidth(part.text) * fontSize) / this.pdf.internal.scaleFactor
      );
    }, 0);

    const extraSpace = maxWidth - totalLineWidth;
    const spaces = line.filter((part) => part.text.trim() !== '').length - 1;
    const spaceAdjustment = spaces > 0 && !isLastLine && justifyLineText ? extraSpace / spaces : 0;

    line.forEach((part, index) => {
      this.pdf.setFont(this.fonts, part.isBold ? 'bold' : 'normal');
      this.pdf.text(part.text, cursorX, startY);
      cursorX +=
        (this.pdf.getStringUnitWidth(part.text) * fontSize) / this.pdf.internal.scaleFactor +
        (index < line.length - 1 ? spaceAdjustment : 0);
    });
  };

  // Parse bold text
  private parseTextForFormatting = (text: string): Array<{ text: string; isBold: boolean }> => {
    const parts: Array<{ text: string; isBold: boolean }> = [];
    const regex = /\*\*(.*?)\*\*/g;
    let lastIndex = 0;
    let match;

    while ((match = regex.exec(text)) !== null) {
      if (match.index > lastIndex) {
        parts.push({ text: text.slice(lastIndex, match.index), isBold: false });
      }
      parts.push({ text: match[1], isBold: true });
      lastIndex = regex.lastIndex;
    }

    if (lastIndex < text.length) {
      parts.push({ text: text.slice(lastIndex), isBold: false });
    }

    return parts;
  };
}
