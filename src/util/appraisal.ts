import React from 'react';

export const showColorClass = (averageScore: number) => {
  if (averageScore >= 80) {
    return 'exceed-expectation';
  }
  if (averageScore <= 79 && averageScore >= 60) {
    return 'meet-expectation';
  }
  if (averageScore < 60) {
    return 'below-expectation';
  }
};

export const getGradeColor = (averageScore: number) => {
  if (averageScore >= 12 && averageScore <= 20) {
    return 'font-green';
  }
  if (averageScore >= 8 && averageScore <= 11) {
    return 'font-orange';
  }
  if (averageScore >= 0 && averageScore <= 7) {
    return 'font-red';
  }
};

export const showPassFailColorClass = (isSuccess: boolean) => {
  if (isSuccess) {
    return 'font-green';
  } else {
    return 'font-red';
  }
};

export const showGradeInText = (averageScore: number) => {
  if (averageScore == 20) {
    return 'Outstanding';
  }
  if (averageScore >= 16 && averageScore <= 19) {
    return 'Very Good';
  }
  if (averageScore >= 12 && averageScore <= 15) {
    return 'Good';
  }
  if (averageScore >= 8 && averageScore <= 11) {
    return 'Fair';
  }
  if (averageScore >= 4 && averageScore <= 7) {
    return 'Poor';
  }
  if (averageScore >= 0 && averageScore <= 3) {
    return 'Very Poor';
  }
};

export const renderToolTipText = (averageScore: number) => {
  if (averageScore >= 80) {
    return 'Exceed Expectation';
  }
  if (averageScore <= 79 && averageScore >= 60) {
    return 'Meet Expectation';
  }
  if (averageScore < 60) {
    return 'Below Expectation';
  }
};

export const getMasterAppaisalGradeColorClass = (grade: number) => {
  if (grade >= 12) {
    return 'exceed-expectation font-weight-bold';
  }
  if (grade < 12 && grade >= 8) {
    return 'meet-expectation font-weight-bold';
  }
  if (grade < 8) {
    return 'below-expectation font-weight-bold';
  }
};

export const getAppaisalToolTipText = () => {
  return `Green: Exceed Expectations\r\nOrange: Meets Expectations\r\nRed: Below Expectations`;
};

export const isSubmittedColor = (debriefingStatus: string) =>
  debriefingStatus === 'done' || debriefingStatus === 'pending_office_comment'
    ? 'exceed-expectation'
    : 'below-expectation';
