import _ from 'lodash';
import XLSX from 'xlsx';
import { formatDate, formatValue } from './view-utils';

interface Column {
  Header: string;
  name?: string;
  type: string;
  value?: any;
  handleExportOptionalFn?: any;
}

interface Data {
  jsonData: any;
  columns: Column[];
  colStartIndex?: number;
}

export const exportTableToExcel = (data: Data[], fileName: string, mainTitle?: string) => {
  const wb = XLSX.utils.book_new();
  const worksheet = XLSX.utils.json_to_sheet([[]]);
  let maxColLen = 0;
  data.forEach((i) => {
    const colLength = i.columns.length + (i?.colStartIndex ?? 0);
    if (maxColLen < colLength) maxColLen = colLength;
  });
  XLSX.utils.book_append_sheet(wb, worksheet, 'Sheet1');
  const wscols = _.fill(Array(maxColLen), { wch: 25 });

  worksheet['!cols'] = wscols;
  let row_index = 1;
  if (mainTitle) {
    const cell_address = { c: 0, r: row_index++ };
    const cell_ref = XLSX.utils.encode_cell(cell_address);
    XLSX.utils.sheet_add_aoa(worksheet, [[mainTitle]], { origin: cell_ref });
    row_index++;
  }
  data.forEach((element) => {
    const colIndexOffset = element.colStartIndex ?? 0;
    element.columns.forEach((column, index) => {
      const cell_address = { c: index + colIndexOffset, r: row_index };
      const cell_ref = XLSX.utils.encode_cell(cell_address);
      XLSX.utils.sheet_add_aoa(worksheet, [[column.Header]], { origin: cell_ref });
    });
    row_index++;

    element.jsonData?.forEach((item: any) => {
      element.columns.forEach((column, c_index) => {
        const cell_address_Data = { c: c_index + colIndexOffset, r: row_index };
        const cell_ref_Data = XLSX.utils.encode_cell(cell_address_Data);
        let cellValue = column?.handleExportOptionalFn
          ? column.handleExportOptionalFn(column.value ?? _.get(item, `${column.name}`))
          : column.value ?? _.get(item, `${column.name}`);

        XLSX.utils.sheet_add_aoa(
          worksheet,
          [[column.type === 'date' ? formatDate(cellValue) : formatValue(cellValue)]],
          {
            origin: cell_ref_Data,
          },
        );
      });
      row_index++;
    });
    row_index++;
  });

  XLSX.writeFile(wb, `${fileName}.xlsx`);
};

const REQUIRED_FIELDS = ['Header', 'type'];

export const extractValidColumns = (columns: any) => {
  return columns
    .filter(
      (e: any) => _.intersection(_.keys(e), REQUIRED_FIELDS).length === REQUIRED_FIELDS.length,
    )
    .map((column: any) =>
      _.pick(column, [...REQUIRED_FIELDS, 'value', 'name', 'handleExportOptionalFn']),
    );
};
