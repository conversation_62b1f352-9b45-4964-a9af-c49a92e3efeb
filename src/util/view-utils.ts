import { shipPartyType } from '../model/constants';
import _ from 'lodash';
import moment from 'moment-timezone';
import { DEFAULT_EMPTY_VALUE_FIELD } from '../constants/formSections';
import { DEFAULT_CURRENCY_UNIT } from '@src/constants/seafarer-wages';
import { capitalizeFirstLetter } from '@src/model/utils';

export const getShortDate = (dateValue) => {
  const d = new Date(dateValue);

  const dateString =
    d.toLocaleString('en-US', { day: '2-digit' }) +
    ' ' +
    d.toLocaleString('en-US', { month: 'short' }) +
    ', ' +
    d.getFullYear();
  return dateString;
};

export const sortOnGivenOrder = (a, order, key) => {
  const map = order.reduce((r, v, i) => {
    r[v] = i;
    return r;
}, {});
  return a.sort((a, b) => map[a[key]] - map[b[key]]);
};

export function filterByExcludedKeys(object, keysToExclude) {
  return Object.keys(object)
    .filter((key) => !keysToExclude.includes(key))
    .reduce((obj, key) => {
      obj[key] = object[key];
      return obj;
    }, {});
}

export function filterByKeys(object, keysToInclude) {
  if (!object) {
    return {};
  }

  return Object.keys(object)
    .filter((key) => keysToInclude.includes(key))
    .reduce((obj, key) => {
      obj[key] = object[key];
      return obj;
    }, {});
}

export function filterByContactType(type) {
  return function (contact) {
    return contact.contact_type === type;
  };
}

export const joinContacts = (contacts) => contacts.map((c) => c.contact).join(',');

export function filterByInvalidContact(type, regex) {
  return function (c) {
    return c.contact_type === type && !regex.test(c.contact);
  };
}

export const scrollToSection = (ref) => {
  if (!ref.current) return;
  const measurement = ref.current.getBoundingClientRect();
  const heightOffset = -60; // height of navbar
  const y = measurement.top + window.scrollY + heightOffset;

  return window.scrollTo({ top: y, behavior: 'smooth' });
};

export const checkUserBelongToSameShipParty = (offices, seafarer, keycloak) => {
  let isSameShipPartyUser = true;
  if (seafarer?.id && keycloak.shipPartyType == shipPartyType.MANNING_AGENT) {
    _.find(offices, ({ id, ship_party_id }) => {
      if (id === seafarer.office_id) {
        isSameShipPartyUser = (ship_party_id === keycloak.shipPartyId);
      }
    });
  }
  return isSameShipPartyUser;
};

export const formatOrdinalDate = (ordinalDate, format) => {
  if (!ordinalDate) return DEFAULT_EMPTY_VALUE_FIELD;
  if (typeof ordinalDate === 'string') {
    return moment(parseInt(ordinalDate)).format(format);
  }
  return ordinalDate;
};

//Comparer Function
export function getSortOrder(prop) {
  return function (a, b) {
    if (a[prop] > b[prop]) {
      return 1;
    } else if (a[prop] < b[prop]) {
      return -1;
    }
    return 0;
  };
}

export const formatValue = (data, defaultValue = '- - -') => {
  return data || defaultValue;
};

export const formatDate = (date, resultFormat = 'DD MMM YYYY', defaultValue = '- - -') => {
  if (moment(date).isValid()) {
    return moment(date).parseZone(date).format(resultFormat);
  }
  return defaultValue;
};

export const getStatusHistoryWithLatestVesselOwnershipData = (activeVesselData, statusHistory) => {
  const latestVesselData = activeVesselData.find((i) => i?.vessel_id === statusHistory?.vessel_id);
  const statusHistoryWithLatestOwnershipData = latestVesselData
    ? {
        ...statusHistory,
        vessel_ownership_id: latestVesselData?.id,
        vessel_name: latestVesselData?.name,
      }
    : statusHistory;
  return statusHistoryWithLatestOwnershipData;
};

export const twoDigitFloat = (input) => {
  if (input !== null && input !== undefined) {
    let num = input.toString();
    if (num.indexOf('.') !== -1 && num.search(/.00$/g) === -1) {
      num = num.slice(0, num.indexOf('.') + 3);
      return Number(num).toFixed(2);
    } else {
      return Number(input).toFixed(2);
    }
  }
};

export const numberToWords = (num) => {
  const units = ['', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine'];
  const tens = ['', 'Ten', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety'];
  const teens = ['Ten', 'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen', 'Seventeen', 'Eighteen', 'Nineteen'];

  const convertToWords = (num) => {
    if (num < 10) {
      return units[num];
    } else if (num < 20) {
      return teens[num - 10];
    } else if (num < 100) {
      const ten = Math.floor(num / 10);
      const unit = num % 10;
      return `${tens[ten]} ${units[unit]}`;
    }
    return '';
  };

  const numString = num.toString();
  const numLength = numString.length;

  if (numLength === 1 || numLength === 2) {
    return convertToWords(num);
  }

  return '';
}
export const numberWithCommas = (x) => {
  return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

export const formatAmount = (amount,unit) => {
   if (!amount) return amount;
   const currencyUnit = unit?.toUpperCase() || DEFAULT_CURRENCY_UNIT;
  return currencyUnit + ' ' + amount;
}
export const columnSortIconName = (column) => {
  if (!column.isSorted) {
    return 'sort-off';
  }
  if (column.isSortedDesc) {
    return 'sort-ascending';
  }
  return 'sort-descending';
};

// on click it cycles to the next sorting order
export const columnSortEventName = (column) => {
  if (!column.isSorted) {
    return `ASC - ${column.render('Header')}`;
  }
  if (column.isSortedDesc) {
    return `None - ${column.render('Header')}`;
  }
  return `Desc - ${column.render('Header')}`;
};

export const getOcimfPillColor = (val: string) => {
  switch (val) {
    case 'green':
      return { color: 'white', background: '#28A747' };
    case 'yellow':
      return { color: 'black', background: '#FFC107' };
    case 'orange':
      return { color: 'white', background: '#F08100' };
    case 'red':
      return { color: 'white', background: '#D41B56' };
  }
};

export const RANKS_MAPPING = {
  MASTER: 'MASTER',
  CO: 'CHIEF OFFICER',
  '2O': '2ND OFFICER',
  '3O': '3RD OFFICER',
  CE: 'CHIEF ENGINEER',
  '2E': '2ND ENGINEER',
  '3E': '3RD ENGINEER',
  '4E': '4TH ENGINEER',
};

export const getRanksForTooltip = (commaSeparatedRanks: string) => {
  const ranks = commaSeparatedRanks?.split(',');
  return `${RANKS_MAPPING[ranks[0]]} + ${RANKS_MAPPING[ranks[1]]}`;
};

export const getTypeOfCompliance = (key: string) => {
  return `"Exp. in ${capitalizeFirstLetter(key?.replace('_', ' '))}" fails OCIMF Compliance`;
};

export default {
  getShortDate,
  sortOnGivenOrder,
  filterByExcludedKeys,
  filterByContactType,
  filterByInvalidContact,
  joinContacts,
  scrollToSection,
  checkUserBelongToSameShipParty,
  formatValue,
  formatAmount,
  formatDate,
  getSortOrder,
  getStatusHistoryWithLatestVesselOwnershipData,
  twoDigitFloat,
  numberToWords,
  numberWithCommas,
};
