import moment from 'moment-timezone';
import { searchType } from '../model/constants';
import { filterByExcludedKeys, filterByKeys } from './view-utils';
import _ from 'lodash';

function getBasicFormErrors(errors) {
  const excludedSeafarerPersonKeys = [
    'family_members',
    'bank_accounts',
    'mobile_numbers',
    'telephone_numbers',
    'email_addresses',
    'addresses',
    'nearest_airport',
    'photo',
    'surname_of_spouse',
    'name_of_spouse',
    'children_names',
    'height',
    'weight',
    'number_of_children',
    'overall_size',
    'tshirt_size',
    'jacket_size',
    'shoe_size',
  ];
  const person = errors.seafarer_person || {};
  const filteredPersonErrorKeys = filterByExcludedKeys(person, excludedSeafarerPersonKeys);

  const filteredErrorKeys = filterByExcludedKeys(errors, [
    'seafarer_person',
    'availability_remarks',
    'additional_experience',
    'cargo_experience',
  ]);

  return { ...filteredErrorKeys, ...filteredPersonErrorKeys };
}

function hasBasicFormErrors(errors) {
  const contactErrors = getBasicFormErrors(errors);
  return Object.keys(contactErrors).length > 0;
}

function getContactErrors(errors) {
  const contacts = ['telephone_numbers', 'email_addresses', 'mobile_numbers'];
  const person = errors.seafarer_person || {};
  const filteredContactErrorKeys = filterByKeys(person, contacts);

  return filteredContactErrorKeys;
}

function hasContactErrors(errors) {
  const contactErrors = getContactErrors(errors);
  return Object.keys(contactErrors).length > 0;
}

function getBankErrors(errors) {
  const bankFields = [
    'account_holder_first_name',
    'account_holder_last_name',
    'account_holder_date_of_birth',
    'account_holder_gender',
    'account_holder_date_of_birth',
    'account_holder_nationality_id',
    'relationship_with_beneficiary',
    'bank_name',
    'bank_account_number',
    'bank_address_country_id',
    'bank_address_address1',
    'bank_address_address2',
    'bank_address_address3',
    'bank_address_address4',
    'ifsc_number',
    'swift_code',
    'bank_account_file',
    'fcnr_months',
  ];
  const bankAccounts = errors.seafarer_person?.bank_accounts ?? [];
  const bankAccount = bankAccounts.length ? bankAccounts[0] : {};
  const filteredbankErrorKeys = filterByKeys(bankAccount, bankFields);

  return filteredbankErrorKeys;
}

function hasBankErrors(errors) {
  const bankErrors = getBankErrors(errors);
  return Object.keys(bankErrors).length > 0;
}

function getFamilyMemberContactError(errors) {
  const contacts = ['telephone', 'mobilephone', 'email'];
  const person = errors.seafarer_person || {};
  const familyMembers = person.family_members || {};
  const firstMember = familyMembers[0] || {};
  const filteredContactErrorKeys = filterByKeys(firstMember, contacts);

  return filteredContactErrorKeys;
}

function getPersonalParticularError(errors) {
  const personalParticularFields = [
    'nearest_airport',
    'photo',
    'surname_of_spouse',
    'name_of_spouse',
    'children_names',
    'height',
    'weight',
    'number_of_children',
    'overall_size',
    'tshirt_size',
    'jacket_size',
    'shoe_size',
  ];
  const seafarerPerson = errors.seafarer_person;
  const filteredErrorKeys = filterByKeys(seafarerPerson, personalParticularFields);

  return filteredErrorKeys;
}

function hasPersonalParticularError(errors) {
  const contactErrors = getFamilyMemberContactError(errors);
  const personalParticularErrors = getPersonalParticularError(errors);
  const allPersonalErrors = { ...personalParticularErrors, ...contactErrors };
  return Object.keys(allPersonalErrors).length > 0;
}

const getDigits = (str) => str.replace(/\D/g, '');

const populateContactNumbers = (fields, contactData) => {
  const { contact: number, contact_type, id } = contactData;
  const contact = getDigits(number);
  fields.push({ id, contact, contact_type });
};

const getDuplicateReason = (currentSearchType) => {
  switch (currentSearchType) {
    case searchType.PASSPORT:
      return 'Passport Number';
    case searchType.SEAMANS_BOOK:
      return 'Seaman’s Book Number';
    case searchType.PERSONAL_DETAILS:
      return 'Personal Details';
    default:
      return 'information';
  }
};

const getDuplicateNumbers = (cachedDuplicates, documents) => {
  const numbers = documents.map((doc) => doc.number);
  const existingPassportDuplicates = cachedDuplicates.filter((cd) => {
    return numbers.indexOf(cd.number) !== -1;
  });

  return existingPassportDuplicates;
};

const doesNotContainDocNumber = (documents, number) => {
  const index = documents.findIndex((doc) => doc.number === number);
  return index === -1;
};

const isValidDate = (value) => {
  if (moment(value).isValid() && !_.isNumber(value)) {
    return true;
  }
  return false;
};

const objectDifference = (obj1, obj2) => {
  const result = {};
  if (Object.is(obj1, obj2)) {
    return undefined;
  }
  if (!obj2 || typeof obj2 !== 'object') {
    return obj2;
  }
  Object.keys(obj1 || {})
    .concat(Object.keys(obj2 || {}))
    .forEach((key) => {
      if (obj2[key] !== obj1[key] && !Object.is(obj1[key], obj2[key])) {
        result[key] = obj2[key];
      }
      if (typeof obj2[key] === 'object' && typeof obj1[key] === 'object') {
        const value = objectDifference(obj1[key], obj2[key]);
        if (value !== undefined) {
          result[key] = value;
        }
      }
    });
  return result;
};

const differenceArraysOfObjects = (firstArray, secondArray) => {
  if (firstArray.length !== secondArray.length) {
    return [];
  }
  const response = [];
  firstArray.forEach((e, i) => {
    const f = secondArray[i];
    if (typeof e === 'object') {
      if (typeof f === 'object') {
        const diff = objectDifference(e, f);
        if (diff) {
          response.push(diff);
        }
      }
    }
  });
  return response.filter((e) => !_.isEmpty(e));
};

export {
  getBasicFormErrors,
  getContactErrors,
  hasContactErrors,
  hasBankErrors,
  getBankErrors,
  getDigits,
  populateContactNumbers,
  getFamilyMemberContactError,
  hasPersonalParticularError,
  hasBasicFormErrors,
  getDuplicateReason,
  getDuplicateNumbers,
  doesNotContainDocNumber,
  isValidDate,
  objectDifference,
  differenceArraysOfObjects,
};
