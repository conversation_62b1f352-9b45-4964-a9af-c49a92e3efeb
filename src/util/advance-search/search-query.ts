import searchTypes, {
  QUERY_TYPE_LIKE,
  QUERY_TYPE_MATCH,
  QUERY_TYPE_RANGE,
  QUERY_TYPE_LESS_THAN,
  QUERY_TYPE_MORE_THAN,
} from './search-types';
import moment from 'moment';
import {
  SEAFARER_STATUS_FIELDS,
  EXPERIENCE_IN_YEARS_FIELDS,
  seafarerStatus,
  screeningFilterKeys,
} from '../../model/constants';
import { SEAFARER_PAGE } from '../../model/TabData';
import _ from 'lodash';
import { reportFilterKeys, approvalStatus, reportTabNames } from '../../types/seafarerReports';
import { getScreenForColumnValue } from '../../component/seafarerList/MenuList';

export const getSearchTypes = searchTypes;

export function generateQuickSearch(quickSearchParams) {
  const params = [];
  for (const [key, value] of Object.entries(quickSearchParams)) {
    if (value) params.push(`${key}=${encodeURIComponent(value)}`);
  }

  const joinedQueryParams = params.join('&');
  return joinedQueryParams;
}

export const addSortOrPaginateParams = (sortPaginateData, queryParams) => {
  const { pageSize, pageIndex, sortBy, offset } = sortPaginateData;
  const offsetValue = offset || 0;
  if (pageSize) queryParams += `&limit=${pageSize}`;
  queryParams += pageIndex ? `&offset=${pageIndex}` : `&offset=${offsetValue}`;
  if (sortBy.length > 0 && sortBy[0].id)
    queryParams += `&orderBy=${sortBy[0].id} ${sortBy[0].desc ? 'desc' : 'asc'}`;
  return queryParams;
};

export const addSortOrPaginateParamsForReports = (sortPaginateData, queryParams) => {
  const { pageSize, pageIndex, sortBy } = sortPaginateData;
  if (pageSize) queryParams += `&limit=${pageSize}`;
  if (pageIndex > -1) queryParams += `&offset=${pageIndex * pageSize}`;
  if (sortBy.length > 0 && sortBy[0].id)
    queryParams += `&orderBy=${sortBy[0].id} ${sortBy[0].desc ? 'desc' : 'asc'}`;
  return queryParams;
};

export const getDateQuery = (item) => {
  let min = item.subtype?.startDate
    ? moment(item.subtype?.startDate).format('YYYY-MM-DD')
    : 'Invalid date';
  let max = item.subtype?.endDate
    ? moment(item.subtype?.endDate).format('YYYY-MM-DD')
    : 'Invalid date';
  if (max === 'Invalid date' && min === 'Invalid date') return;
  if (max === 'Invalid date') max = '';
  if (min === 'Invalid date') min = '';
  return { min, max };
};

export const getValueFromCriteriaItem = (item, currentTab = null) => {
  let value = '';
  if (
    item.type.inputType === 'dropdown' &&
    Array.isArray(item.subtype) &&
    item.subtype?.filter((i) => i.id === 0).length
  )
    // in order to skipp filter with value All
    return null;
  switch (item.type.inputType) {
    case 'dropdown':
      if (item.type.type === 'vessel_size') {
        value = (item.subtype.length ? item.subtype : [])
          .map((type) => `${type.min},${type.max}`)
          .join('|');
        break;
      }
      if (item.type.type === 'with_fml_vessel_experience' || item.type.type === 'docs_in_hand') {
        if (item.subtype.length && item.subtype[0].value === 'Yes') {
          value = 'true';
        }
        if (item.subtype.length && item.subtype[0].value === 'No') {
          value = 'false';
        }
        break;
      }
      if (item.type.type === 'years_of_long_service_type') {
        if (item.subtype.length && item.subtype[0].value === 'No Gaps') {
          value = 'true';
        }
        if (item.subtype.length && item.subtype[0].value === 'With Gaps') {
          value = 'false';
        }
        break;
      }
      if (item.type.type === 'owner' && item.subtype.length) {
        value = item.subtype.map((subtype) => subtype.ownership_ids).join(',');
        break;
      }
      if (item.type.type === 'last_contact_date' && item.subtype && item.subtype.length) {
        const selectedOption = item.subtype[0].id;
        const today = moment();
        let startDate, endDate;

        switch (selectedOption) {
          case 1: // Contacted in Last 5 days
            startDate = today.clone().subtract(5, 'days');
            endDate = today;
            value = `${startDate.format('YYYY-MM-DD')},${endDate.format('YYYY-MM-DD')}`;
            break;
          case 2: // Contacted in Last 15 days
            startDate = today.clone().subtract(15, 'days');
            endDate = today;
            value = `${startDate.format('YYYY-MM-DD')},${endDate.format('YYYY-MM-DD')}`;
            break;
          case 3: // Contacted more than last 30 days
            endDate = today.clone().subtract(30, 'days');
            value = `,${endDate.format('YYYY-MM-DD')}`;
            break;
        }
        break;
      }
      if (item.type.type === reportFilterKeys.approval_status && item.subtype.length) {
        value = item.subtype
          .map((subtype) => {
            if (subtype.value === approvalStatus.APPROVED) {
              return seafarerStatus.CREW_ASSIGNMENT_APPROVED;
            } else if (subtype.value === approvalStatus.REJECTED) {
              return seafarerStatus.CREW_ASSIGNMENT_REJECTED;
            } else {
              return seafarerStatus.CREW_ASSIGNMENT_PENDING;
            }
          })
          .join('|');
        break;
      }
      if (
        [
          reportFilterKeys.reports_ranks,
          reportFilterKeys.reports_nationalities,
          reportFilterKeys.reports_vessel,
          reportFilterKeys.vessel_with_id,
          reportFilterKeys.report_owners,
          reportFilterKeys.reporting_offices,
          screeningFilterKeys.SCREENING_FOR,
          'vessel',
        ].includes(item.type.type)
      ) {
        if (item.subtype.length) {
          if (item.type.validTabs?.includes('modeller')) {
            value = item.subtype.map((subtype) => subtype.value).join('|');
          } else {
            value = item.subtype.map((subtype) => (currentTab === reportTabNames.SIGNED_OFF && item.type.type === reportFilterKeys.vessel_with_id) ? subtype.ref_id : subtype.id).join('|');
          }
        }
        break;
      }
      if (SEAFARER_STATUS_FIELDS.includes(item.type.type))
        value = (item.subtype.length ? item.subtype : []).map((type) => type.id).join('|');
      else value = (item.subtype.length ? item.subtype : []).map((type) => type.value).join('|');
      break;
    case 'text':
      value = item.subtype;
      break;
    case 'number_range': {
      const min = item.subtype.min;
      const max = item.subtype.max;
      value = { min, max };
      break;
    }
    case 'date': {
      const dateQueryObj = getDateQuery(item);
      if (dateQueryObj?.min && dateQueryObj?.max) {
        value = { min: dateQueryObj.min, max: dateQueryObj.max };
      }
      break;
    }
    case 'year': {
      const year = moment(item.subtype).format('YYYY');
      value = { min: `${year}-01-01`, max: `${year}-12-31` };
      break;
    }
    case 'checkbox': {
      value = (item.subtype.length ? item.subtype : [])
        .filter((type) => type.checked === true)
        .map((type) => type.name)
        .join('|');
      break;
    }
    case 'date_single': {
      value = item.subtype ? moment(item.subtype).format('YYYY-MM-DD') : '';
      break;
    }
    default:
      value = item.subtype ?? '';
  }
  return value;
};

export const putValueToCriteriaItem = (item, value) => {
  if (typeof item.type.inputType === 'object') {
    item.subtype = value;
    return item;
  }

  let startDate;
  let endDate;
  switch (item.type.inputType) {
    case 'dropdown':
    case 'checkbox':
      item.subtype = { value };
      break;
    case 'text':
      item.subtype = value;
      if (EXPERIENCE_IN_YEARS_FIELDS.includes(item.type.type)) {
        item.subtype = parseInt(value).toString();
      }
      break;
    case 'number_range':
      item.subtype = {
        ...value,
      };
      break;
    case 'date':
      startDate = moment(value.min).toDate();
      endDate = moment(value.max).toDate();
      if (startDate.toString() === 'Invalid Date' && endDate.toString() === 'Invalid Date') break;
      else if (startDate.toString() === 'Invalid Date') item.subtype = { endDate };
      else if (endDate.toString() === 'Invalid Date') item.subtype = { startDate };
      else item.subtype = { startDate, endDate };
      break;
    case 'year':
      item.subtype = moment(value.min).toDate();
      break;
    case 'date_single':
      item.subtype = moment(value).toDate();
      break;
    default:
      item.subtype = value;
  }
  return item;
};

export const getQueryItem = (item, currentTab = null) => {
  let queryItem = '';
  const {
    type: { queryType, queryKey },
  } = item;

  // get value
  let value = getValueFromCriteriaItem(item, currentTab);

  // push query items
  switch (queryType) {
    case QUERY_TYPE_LIKE:
      queryItem = {
        key: queryKey,
        value: value ?? '',
      };
      break;
    case QUERY_TYPE_RANGE:
      if (value) {
        queryItem = {
          key: `${queryKey}`,
          value: `${value.min},${value.max}`,
        };
      }
      break;
    case QUERY_TYPE_LESS_THAN:
      if (value) {
        queryItem = {
          key: `${queryKey}`,
          value: `,${value}`,
        };
      }
      break;
    case QUERY_TYPE_MORE_THAN:
      if (value) {
        queryItem = {
          key: `${queryKey}`,
          value: `${value},`,
        };
      }
      break;
    case QUERY_TYPE_MATCH:
    default:
      queryItem = value && {
        key: queryKey,
        value,
      };
  }
  // value && // in order to skip filters with value All
  if (queryItem) {
    return queryItem;
  }
};

export const mapSearchCriteriaToQueryString = (searchCriteria, currentTab = null) => {
  const query = searchCriteria
    .reduce((arr, item) => {
      const queryItems = [];
      if (typeof item.type.type === 'object') {
        const items = item.type.type
          .map((e, i) => {
            let newItem = _.cloneDeep(item);
            newItem.subtype = item.subtype[e];
            newItem.type.inputType = item.type.inputType[i];
            newItem.type.queryType = item.type.queryType[i];
            newItem.type.queryKey = item.type.queryKey[i];
            newItem.type.type = e;

            return newItem;
          })
          .filter((e) => e.subtype.length !== 0);
        if (items?.length > 0) {
          items.map((e) => {
            const queryItem = getQueryItem(e, currentTab);
            if (queryItem) {
              queryItems.push({ ...queryItem });
            }
          });
        }
      } else {
        const queryItem = getQueryItem(item, currentTab);
        if (queryItem) {
          queryItems.push({ ...queryItem });
        }
      }
      return [...arr, ...queryItems];
    }, [])
    .map((item) => {
      const { key, value } = item;
      return `${key}=${encodeURIComponent(value)}`;
    })
    .join('&');

  return query;
};

const createRangeItem = ({ value, searchType, key }) => {
  const valuePair = value.split(',');
  return {
    searchType,
    key,
    value: {
      min: valuePair[0],
      max: valuePair[1],
    },
  };
};

export const mapQueryStringToSearchCriteria = (
  queryString,
  dropDownData,
  tab = null,
  page = SEAFARER_PAGE,
) => {
  const criteria = queryString
    .split('&')
    .map((param) => decodeURIComponent(param).split('='))
    .reduce((arr, [key, value]) => {
      if (!key) return arr;
      const searchTypesForTab = tab
        ? getSearchTypes(page).filter(
            (i) => i.validTabs === undefined || i.validTabs.includes(tab),
          )
        : getSearchTypes(page);

      const searchType = searchTypesForTab.find((type) => {
        return type.queryKey === key || type.queryKey.includes(key);
      });
      if (!searchType) return arr;
      const existingSearchTypeArr = arr.find((e) => e.searchType === searchType) ?? null;
      if (typeof searchType.queryKey === 'object' && !existingSearchTypeArr) {
        const newValue = Object.fromEntries(
          searchType.queryKey.map((e) => {
            if (e === key) {
              const type = searchType.type[searchType.queryKey.indexOf(key)];
              return [type, value];
            } else {
              const type = searchType.type[searchType.queryKey.indexOf(e)];
              return [type, ''];
            }
          }),
        );
        const keyArr = [key];
        return [...arr, { searchType, key: keyArr, value: newValue }];
      }
      if (existingSearchTypeArr) {
        const newArr = arr.map((e) => {
          if (e.searchType === existingSearchTypeArr.searchType) {
            if (typeof e.key === 'object') {
              e.key.push(key);
            }
            if (typeof e.value === 'object') {
              e.value = Object.fromEntries(
                Object.keys(e.value).map((f) => {
                  const type = searchType.type[searchType.queryKey.indexOf(key)];
                  if (f === type) {
                    return [[f], value];
                  } else {
                    return [[f], e.value[f]];
                  }
                }),
              );
            }
            return e;
          } else {
            return e;
          }
        });
        return [...newArr];
      }
      const { queryType } = searchType;
      if (queryType === QUERY_TYPE_RANGE) {
        return [...arr, createRangeItem({ value, searchType, key })];
      }
      return [...arr, { searchType, key, value }];
    }, [])
    .map(({ searchType, value }) => {
      const item = {
        type: searchType,
      };
      putValueToCriteriaItem(item, value);
      if (typeof searchType.inputType === 'object') {
        searchType.inputType.map((e) => {
          const type = searchType.type[searchType.inputType.indexOf(e)];
          if ((e === 'dropdown' || e === 'checkbox') && dropDownData) {
            const dropDownOptions = dropDownData[type];
            if (!dropDownOptions && !SEAFARER_STATUS_FIELDS.includes(type) && e !== 'checkbox')
              return item;
            // Using '|' as delimiter to seperate multiple dropdown values
            item.subtype[type] = item.subtype[type].split('|').map((val) => {
              if (SEAFARER_STATUS_FIELDS.includes(type)) return { id: val, value: val };
              if (e === 'checkbox') return { name: val, checked: true };
              if (type === 'vessel_size') {
                const minVal = parseInt(val.split(',')?.[0]) ?? '';
                const data = dropDownOptions.find(({ min }) => min === minVal);
                if (!data) return dropDownOptions.find(({ id }) => id === 0);
                return data;
              }
              if (type === 'with_fml_vessel_experience' || type === 'docs_in_hand') {
                let flag = '';
                if (val === 'true') {
                  flag = 'Yes';
                } else {
                  flag = 'No';
                }
                const data = dropDownOptions.find(({ value }) => value === flag);
                if (!data) return dropDownOptions.find(({ id }) => id === 0);
                return data;
              }
              if (type === 'years_of_long_service_type') {
                let flag = '';
                if (val === 'false') {
                  flag = 'With Gaps';
                } else {
                  flag = 'No Gaps';
                }
                const data = dropDownOptions.find(({ value }) => value === flag);
                if (!data) return dropDownOptions.find(({ id }) => id === 0);
                return data;
              }
              const data = dropDownOptions.find(({ value }) => value === val);
              if (!data) return val;
              return { id: data.id, value: data.value };
            });
          }
        });
        return item;
      }
      if (
        (searchType.inputType === 'dropdown' || searchType.inputType === 'checkbox') &&
        dropDownData
      ) {
        const dropDownOptions = dropDownData[searchType.type];
        if (
          !dropDownOptions &&
          !SEAFARER_STATUS_FIELDS.includes(searchType.type) &&
          searchType.inputType !== 'checkbox'
        )
          return item;
        // Using '|' as delimiter to seperate multiple dropdown values
        item.subtype = item.subtype.value.split('|').map((val) => {
          if (SEAFARER_STATUS_FIELDS.includes(searchType.type)) return { id: val, value: val };
          if (screeningFilterKeys.SCREENING_FOR === searchType.type)
            return { id: val, value: getScreenForColumnValue(val) };
          if (searchType.inputType === 'checkbox') return { name: val, checked: true };
          if (searchType.type === 'vessel_size') {
            const minVal = parseInt(val.split(',')?.[0]) ?? '';
            const data = dropDownOptions.find(({ min }) => min === minVal);
            if (!data) return dropDownOptions.find(({ id }) => id === 0);
            return data;
          }
          if (searchType.type === 'with_fml_vessel_experience' || searchType.type === 'docs_in_hand') {
            let flag = '';
            if (val === 'true') {
              flag = 'Yes';
            } else {
              flag = 'No';
            }
            const data = dropDownOptions.find(({ value }) => value === flag);
            if (!data) return dropDownOptions.find(({ id }) => id === 0);
            return data;
          }
          if (searchType.type === reportFilterKeys.approval_status) {
            let approvalStatusValue = '';
            if (val === seafarerStatus.CREW_ASSIGNMENT_APPROVED) {
              approvalStatusValue = approvalStatus.APPROVED;
            } else if (val === seafarerStatus.CREW_ASSIGNMENT_REJECTED) {
              approvalStatusValue = approvalStatus.REJECTED;
            } else {
              approvalStatusValue = approvalStatus.PENDING;
            }
            const data = dropDownOptions.find(({ value }) => value === approvalStatusValue);
            if (!data) return dropDownOptions.find(({ id }) => id === 0);
            return data;
          }

          if (
            [
              reportFilterKeys.reports_vessel,
              reportFilterKeys.reports_ranks,
              reportFilterKeys.reports_nationalities,
              reportFilterKeys.reporting_offices,
            ].includes(searchType.type)
          ) {
            const data = dropDownOptions.find(({ id }) => id === parseInt(val));
            if (!data) return val;
            return { id: data.id, value: data.value };
          }

          const data = dropDownOptions.find(({ value }) => value === val);
          if (!data) return val;
          return { id: data.id, value: data.value };
        });
      }
      return item;
    });
  return criteria;
};
