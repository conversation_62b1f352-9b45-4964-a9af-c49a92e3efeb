import { SEAFARER_PAGE, SEAFARER_REPORT_PAGE, SEAFARER_TRAINING_COURSES_PAGE } from '../../model/TabData';
import {
  ON_LEAVE_SEAFARER,
  SEAFARERS_TO_RELIEVE,
} from '../../constants/crewPlanner';
import { screeningFilterKeys, screeningStatus, AVAILABLE_SEAFARERS, CONTRACT_EXPIRY } from '../../model/constants';
import { reportFilterKeys, reportTabNames } from '../../types/seafarerReports';

export const QUERY_TYPE_LIKE = 'like';
export const QUERY_TYPE_MATCH = 'match';
export const QUERY_TYPE_RANGE = 'range';
export const QUERY_TYPE_LESS_THAN = 'lessThan';
export const QUERY_TYPE_MORE_THAN = 'moreThan';

export const recommendedDateQueryKey = 'recommendedDate';
export const approvalDateQueryKey = 'approvedDate';
export const statusDateQueryKey = 'statusDate';
export const joiningDateQueryKey = 'dateOfJoining';
export const dateOfCommencementKey = 'contractStartDate';
export const dateOfSignOnKey = 'signOnDate';
export const dateOfSignOffKey = 'signOffDate';
export const recommendedStatusKey = 'recommendedStatus';
export const accountingCurrency = 'accountingCurrency';

export const approvalGroupKey = 'approvalGroup';
export const pendingApprovalGroupKey = 'pendingApprovalGroup';
export const commonApprovalGroupKey = 'seafarer_person:seafarer_screening.approval_group';

const ALL_EXCEPT_AVAILABLE_SEAFARERS = [
  screeningStatus.ALL,
  screeningStatus.PASSED,
  screeningStatus.UNDER_SCREENING,
  screeningStatus.REJECTED,
  screeningStatus.ARCHIVED,
  CONTRACT_EXPIRY,
  SEAFARERS_TO_RELIEVE,
  ON_LEAVE_SEAFARER,
];

const SEAFARER_SEARCH_TYPES = [
  {
    type: 'first_name',
    name: 'First Name',
    section: '',
    inputType: 'text',
    queryType: QUERY_TYPE_LIKE,
    queryKey: 'seafarer_person.first_name',
    validTabs: ALL_EXCEPT_AVAILABLE_SEAFARERS,
  },
  {
    type: 'middle_name',
    name: 'Middle Name',
    section: '',
    inputType: 'text',
    queryType: QUERY_TYPE_LIKE,
    queryKey: 'seafarer_person.middle_name',
    validTabs: ALL_EXCEPT_AVAILABLE_SEAFARERS,
  },
  {
    type: 'last_name',
    name: 'Last Name',
    section: '',
    inputType: 'text',
    queryType: QUERY_TYPE_LIKE,
    queryKey: 'seafarer_person.last_name',
    validTabs: ALL_EXCEPT_AVAILABLE_SEAFARERS,
  },
  {
    type: 'dataQuality',
    name: 'Quality',
    section: '',
    inputType: 'checkbox',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'data_quality',
    validTabs: ALL_EXCEPT_AVAILABLE_SEAFARERS,
  },
  {
    type: 'hkid',
    name: 'Seafarer ID (HKID)',
    section: '',
    inputType: 'number',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'hkid',
    inputProps: {
      min: 0,
      pattern: '\\d*',
      inputMode: 'numeric',
    },
    validTabs: ALL_EXCEPT_AVAILABLE_SEAFARERS,
  },
  {
    type: 'nationalities',
    name: 'Nationality',
    section: '',
    inputType: 'dropdown',
    isSearchable: true,
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'seafarer_person:nationality.value',
  },
  {
    type: 'ranks',
    name: 'Rank',
    section: '',
    inputType: 'dropdown',
    isSearchable: true,
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'seafarer_rank.value',
    validTabs: [
      screeningStatus.ALL,
      screeningStatus.PASSED,
      screeningStatus.UNDER_SCREENING,
      screeningStatus.REJECTED,
      screeningStatus.ARCHIVED,
    ],
  },
  {
    type: 'offices',
    name: 'Reporting Office',
    section: '',
    inputType: 'dropdown',
    isSearchable: true,
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'seafarer_reporting_office.value',
  },
  {
    type: 'place_of_birth',
    name: 'Place of Birth',
    section: '',
    inputType: 'text',
    queryType: QUERY_TYPE_LIKE,
    queryKey: 'seafarer_person.place_of_birth',
    validTabs: ALL_EXCEPT_AVAILABLE_SEAFARERS,
  },
  {
    type: 'date',
    name: 'Date of Birth',
    section: '',
    inputType: 'date',
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'seafarer_person.date_of_birth',
  },
  {
    type: 'account_status',
    name: 'Account Status',
    section: '',
    inputType: 'dropdown',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'seafarer_person.current_account_status',
    validTabs: ALL_EXCEPT_AVAILABLE_SEAFARERS,
  },
  {
    type: 'journey_status',
    name: 'Journey Status',
    section: '',
    inputType: 'dropdown',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'seafarer_person.current_journey_status',
    validTabs: [
      screeningStatus.ALL,
      screeningStatus.PASSED,
      screeningStatus.UNDER_SCREENING,
      screeningStatus.REJECTED,
      screeningStatus.ARCHIVED,
      ON_LEAVE_SEAFARER,
    ],
  },
  {
    type: 'exam_status',
    name: 'Examination',
    section: '',
    inputType: 'dropdown',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'seafarer_person.current_exam_status',
    validTabs: ALL_EXCEPT_AVAILABLE_SEAFARERS,
  },
  {
    type: 'gender',
    name: 'Gender',
    section: '',
    inputType: 'dropdown',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'gender',
  },
  {
    type: 'countries',
    name: 'Country of birth',
    section: '',
    inputType: 'dropdown',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'seafarer_person:country_of_birth.value',
    validTabs: ALL_EXCEPT_AVAILABLE_SEAFARERS,
  },
  {
    type: 'passport_number',
    name: 'Passport number',
    section: '',
    inputType: 'text',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'seafarer_person:passports.number',
    validTabs: ALL_EXCEPT_AVAILABLE_SEAFARERS,
  },
  {
    type: 'date_of_passport_expiry',
    name: 'Passport Expiry',
    section: '',
    inputType: 'date',
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'seafarer_person:passports.date_of_expiry',
    validTabs: [AVAILABLE_SEAFARERS],
  },
  {
    type: 'date_of_seaman_book_expiry',
    name: "Seamen’s Book Expiry",
    section: '',
    inputType: 'date',
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'seafarer_person:seaman_books.date_of_expiry',
    validTabs: [AVAILABLE_SEAFARERS],
  },
  {
    type: 'date_of_us_visa_expiry',
    name: 'US Visa Expiry',
    section: '',
    inputType: 'date',
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'seafarer_person:seafarer_document:seafarer_doc_visa.date_of_expiry',
    validTabs: [AVAILABLE_SEAFARERS],
  },
  {
    type: 'date_of_yellow_fever_expiry',
    name: 'Yellow Fever',
    section: '',
    inputType: 'date',
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'seafarer_person:seafarer_document:seafarer_doc_other_document.date_of_expiry',
    validTabs: [AVAILABLE_SEAFARERS],
  },
  {
    type: 'seafarer_age',
    name: 'Age',
    section: '',
    inputType: 'dropdown',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'seafarer_age',
    notMultiple: true,
    validTabs: [AVAILABLE_SEAFARERS],
  },
  {
    type: 'seamans_book_number',
    name: "Seaman's book number",
    section: '',
    inputType: 'text',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'seafarer_person:seaman_books.number',
    validTabs: ALL_EXCEPT_AVAILABLE_SEAFARERS,
  },
  {
    type: 'contact_date',
    name: 'Latest Contact Date',
    section: '',
    inputType: 'date',
    queryType: QUERY_TYPE_RANGE,
    queryIdentifier: 'contact_date',
    queryKey: 'seafarer_contact_log.contact_date',
    validTabs: ALL_EXCEPT_AVAILABLE_SEAFARERS,
  },
  {
    type: 'last_contact_date',
    name: 'Last Contacted',
    section: '',
    inputType: 'dropdown',
    notMultiple: true,
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'seafarer_contact_log.contact_date',
    queryIdentifier: 'last_contact_date',
    validTabs: [
      AVAILABLE_SEAFARERS
    ],
  },
  {
    type: 'availability_date',
    name: 'Availability date',
    section: '',
    inputType: 'date',
    validTabs: ALL_EXCEPT_AVAILABLE_SEAFARERS,
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'seafarer_contact_log.availability_date',
    queryIdentifier: 'availability_date',
  },
  {
    type: 'next_contact_date',
    name: 'Next Contact Date',
    section: '',
    inputType: 'date',
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'seafarer_contact_log.next_contact_date',
  },
  {
    type: 'docs_in_hand',
    name: 'Documents in Hand',
    section: '',
    inputType: 'dropdown',
    notMultiple: true,
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'seafarer_contact_log.docs_in_hand',
  },
  {
    type: 'data_of_contract_expry',
    name: 'Date of Contract Expiry',
    section: '',
    inputType: 'date',
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'seafarer_person:seafarer_status_history.expected_contract_end_date',
    validTabs: [CONTRACT_EXPIRY, SEAFARERS_TO_RELIEVE],
  },
  {
    type: 'sign_on_ranks',
    name: 'Rank',
    section: '',
    inputType: 'dropdown',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'seafarer_person:seafarer_status_history:seafarer_rank.value',
    validTabs: [CONTRACT_EXPIRY],
  },
  {
    type: 'tech_group',
    name: 'Tech Group',
    section: '',
    inputType: 'dropdown',
    isSearchable: true,
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'seafarer_person:seafarer_status_history.vessel_tech_group',
    validTabs: [CONTRACT_EXPIRY, SEAFARERS_TO_RELIEVE],
  },
  {
    type: 'available_date',
    name: 'Available Date',
    section: '',
    inputType: 'date_single',
    queryType: QUERY_TYPE_MORE_THAN,
    queryKey: 'seafarer_contact_log.availability_date',
    queryIdentifier: 'available_date',
    validTabs: [AVAILABLE_SEAFARERS, ON_LEAVE_SEAFARER],
  },
  {
    type: 'with_fml_vessel_experience',
    name: 'Has FML vessel experience',
    section: '',
    inputType: 'dropdown',
    notMultiple: true,
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'with_fml_vessel_experience',
    validTabs: [ON_LEAVE_SEAFARER],
  },
  {
    type: 'target_vessel_type',
    name: 'Prev. Vessel Type',
    section: '',
    inputType: 'dropdown',
    isSearchable: true,
    notMultiple: true,
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'target_vessel_type.value',
    validTabs: [AVAILABLE_SEAFARERS, ON_LEAVE_SEAFARER],
  },
  {
    type: 'target_rank',
    name: 'Rank',
    section: '',
    inputType: 'dropdown',
    isSearchable: true,
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'target_rank.value',
    validTabs: [AVAILABLE_SEAFARERS],
  },
  {
    type: 'duration_with_company',
    name: 'Exp. with FML',
    section: '',
    inputType: 'text',
    queryType: QUERY_TYPE_MORE_THAN,
    queryKey: 'duration_with_company',
    validTabs: [AVAILABLE_SEAFARERS, ON_LEAVE_SEAFARER],
  },
  {
    type: 'engine_type',
    name: 'Engine Type',
    section: '',
    inputType: 'dropdown',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'seafarer_experience.engine_type',
    validTabs: [ON_LEAVE_SEAFARER],
  },
  {
    type: 'duration_in_target_rank',
    name: 'Exp. in Rank',
    section: '',
    inputType: 'text',
    queryType: QUERY_TYPE_MORE_THAN,
    queryKey: 'duration_in_target_rank',
    validTabs: [AVAILABLE_SEAFARERS, ON_LEAVE_SEAFARER],
  },
  {
    type: 'vessel_size',
    name: 'Vessel Size (DWT)',
    section: '',
    inputType: 'dropdown',
    notMultiple: true,
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'seafarer_experience.deadweight_tonnage',
    validTabs: [ON_LEAVE_SEAFARER],
  },
  {
    type: 'duration_on_target_vessel_type',
    name: 'Exp. in Vessel Type',
    section: '',
    inputType: 'text',
    queryType: QUERY_TYPE_MORE_THAN,
    queryKey: 'duration_on_target_vessel_type',
    validTabs: [AVAILABLE_SEAFARERS, ON_LEAVE_SEAFARER],
  },
  {
    type: 'previous_vessel',
    name: 'Previous Vessel',
    section: '',
    inputType: 'dropdown',
    isSearchable: true,
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'seafarer_experience.vessel_name',
    validTabs: [AVAILABLE_SEAFARERS, ON_LEAVE_SEAFARER],
  },
  {
    type: 'previous_tech_group',
    name: 'Previous Group',
    section: '',
    inputType: 'dropdown',
    isSearchable: true,
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'seafarer_experience.vessel_tech_group',
    validTabs: [AVAILABLE_SEAFARERS, ON_LEAVE_SEAFARER],
  },
  {
    type: 'previous_rank',
    name: 'Previous Rank',
    section: '',
    inputType: 'dropdown',
    isSearchable: true,
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'seafarer_experience:rank.value',
    validTabs: [ON_LEAVE_SEAFARER],
  },
  {
    type: 'plan_status',
    name: 'Plan Status',
    section: '',
    inputType: 'dropdown',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'crew_plan_seafarer_journey_status',
    validTabs: [ON_LEAVE_SEAFARER],
  },
  {
    type: 'planned_vessel',
    name: 'Planned Vessel',
    section: '',
    inputType: 'dropdown',
    isSearchable: true,
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'crew_plan_vessel_name',
    queryIdentifier: 'planned_vessel',
    validTabs: [ON_LEAVE_SEAFARER],
  },
  {
    type: 'planned_vessel',
    name: 'Planned Vessel',
    section: '',
    inputType: 'dropdown',
    isSearchable: true,
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'crew_plan_vessel_name',
    queryIdentifier: 'recommended_vessel',
    validTabs: [AVAILABLE_SEAFARERS],
  },
  {
    type: 'sign_off_date',
    name: 'Sign Off Date',
    section: '',
    inputType: 'date',
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'seafarer_experience.end_date',
    validTabs: [AVAILABLE_SEAFARERS, ON_LEAVE_SEAFARER],
  },
  {
    type: 'duration_on_all_vessel_type',
    name: 'Exp. in All Vessel Type',
    section: '',
    inputType: 'text',
    queryType: QUERY_TYPE_MORE_THAN,
    queryKey: 'duration_on_all_vessel_type',
    validTabs: [AVAILABLE_SEAFARERS, ON_LEAVE_SEAFARER],
  },
  {
    type: ['years_of_long_service', 'years_of_long_service_type'],
    name: 'Years of long service',
    section: '',
    inputType: ['text', 'dropdown'],
    queryType: [QUERY_TYPE_MATCH, QUERY_TYPE_MATCH],
    queryKey: ['years_of_long_service', 'without_gaps'],
    notMultiple: true,
    validTabs: [
      screeningStatus.ALL,
      screeningStatus.PASSED,
      screeningStatus.UNDER_SCREENING,
      screeningStatus.REJECTED,
      screeningStatus.ARCHIVED,
    ],
  },
  {
    type: screeningFilterKeys.SCREENING_FOR,
    name: 'Screening for',
    section: '',
    inputType: 'dropdown',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'seafarer_person:seafarer_screening.screening_for',
    validTabs: [screeningStatus.UNDER_SCREENING, screeningStatus.REJECTED],
  },
  {
    type: screeningFilterKeys.PENDING_APPROVAL_GROUP,
    name: 'Pending approval group',
    section: '',
    inputType: 'dropdown',
    queryType: QUERY_TYPE_MATCH,
    queryKey: pendingApprovalGroupKey,
    validTabs: [screeningStatus.UNDER_SCREENING],
  },
  {
    type: screeningFilterKeys.PENDING_APPROVAL_GROUP,
    name: 'Approval group',
    section: '',
    inputType: 'dropdown',
    queryType: QUERY_TYPE_MATCH,
    queryKey: approvalGroupKey,
    validTabs: [screeningStatus.REJECTED],
  },
  {
    type: screeningFilterKeys.SCREENING_SUBMITTED_DATE_AND_TIME,
    name: 'Screening submitted date and time',
    section: '',
    inputType: 'date',
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'seafarer_person:seafarer_screening.created_at',
    validTabs: [screeningStatus.UNDER_SCREENING, screeningStatus.REJECTED],
  },
  {
    type: 'vessel_type',
    name: 'Vessel Type',
    section: '',
    inputType: 'dropdown',
    isSearchable: true,
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'vessel_type',
    validTabs: [SEAFARERS_TO_RELIEVE],
  },
  {
    type: 'vessel',
    name: 'Vessel',
    section: '',
    inputType: 'dropdown',
    isSearchable: true,
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'seafarer_person:seafarer_status_history.vessel_id',
    validTabs: [CONTRACT_EXPIRY, SEAFARERS_TO_RELIEVE],
  },
  {
    type: 'owner',
    name: 'Owner',
    section: '',
    inputType: 'dropdown',
    isSearchable: true,
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'vessel_ownership_id',
    validTabs: [CONTRACT_EXPIRY, SEAFARERS_TO_RELIEVE],
  },
  {
    type: 'previous_owner',
    name: 'Previous Owner',
    section: '',
    inputType: 'dropdown',
    isSearchable: true,
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'seafarer_experience.owner_name',
    validTabs: [AVAILABLE_SEAFARERS],
  },
];

const SEAFARER_REPORT_SEARCH_TYPES = [
  {
    type: 'tech_group',
    name: 'Tech Group',
    section: '',
    inputType: 'dropdown',
    isSearchable: true,
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'vessel_tech_group',
    validTabs: [reportTabNames.MODELLER],
  },
  {
    type: 'vessel',
    name: 'Vessels',
    section: '',
    inputType: 'dropdown',
    isSearchable: true,
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'vessel_name',
    validTabs: [reportTabNames.MODELLER],
  },
  {
    type: 'planned_number',
    name: 'Planned Number',
    section: '',
    inputType: 'number_range',
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'planned_number',
    validTabs: [reportTabNames.MODELLER],
  },
  {
    type: 'planned_wages',
    name: 'Planned Wages',
    section: '',
    inputType: 'number_range',
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'planned_wages',
    validTabs: [reportTabNames.MODELLER],
  },
  {
    type: 'actual_number',
    name: 'Actual Number',
    section: '',
    inputType: 'number_range',
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'actual_number',
    validTabs: [reportTabNames.MODELLER],
  },
  {
    type: 'actual_wages',
    name: 'Actual Wages',
    section: '',
    inputType: 'number_range',
    queryType: QUERY_TYPE_RANGE,
    queryKey: 'actual_wages',
    validTabs: [reportTabNames.MODELLER],
  },
  {
    type: reportFilterKeys.recommend_date,
    name: 'Recommended Date',
    section: '',
    inputType: 'date',
    queryType: QUERY_TYPE_RANGE,
    queryKey: recommendedDateQueryKey,
    validTabs: [reportTabNames.APPROVAL_REPORT],
  },
  {
    type: reportFilterKeys.approval_date,
    name: 'Approval Date',
    section: '',
    inputType: 'date',
    queryType: QUERY_TYPE_RANGE,
    queryKey: approvalDateQueryKey,
    validTabs: [reportTabNames.APPROVAL_REPORT],
  },
  {
    type: reportFilterKeys.recommended,
    name: 'Recommended',
    section: '',
    inputType: 'dropdown',
    isSearchable: true,
    queryType: QUERY_TYPE_MATCH,
    queryKey: recommendedStatusKey,
    validTabs: [reportTabNames.APPROVAL_REPORT],
  },
  {
    type: reportFilterKeys.approval_status,
    name: 'Approval Status',
    section: '',
    inputType: 'dropdown',
    isSearchable: true,
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'journeyStatus',
    validTabs: [reportTabNames.APPROVAL_REPORT],
  },
  {
    type: reportFilterKeys.reports_ranks,
    name: 'Rank',
    section: '',
    inputType: 'dropdown',
    isSearchable: true,
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'rank',
    validTabs: [reportTabNames.APPROVAL_REPORT],
  },
  {
    type: reportFilterKeys.date_of_joining,
    name: 'Date of Joining',
    section: '',
    inputType: 'date',
    queryType: QUERY_TYPE_RANGE,
    queryKey: joiningDateQueryKey,
    validTabs: [reportTabNames.SIGNED_ON],
  },
  {
    type: reportFilterKeys.sign_off_date,
    name: 'Sign Off Date',
    section: '',
    inputType: 'date',
    queryType: QUERY_TYPE_RANGE,
    queryKey: statusDateQueryKey,
    validTabs: [reportTabNames.SIGNED_OFF],
  },
  {
    type: reportFilterKeys.reports_nationalities,
    name: 'Nationality',
    section: '',
    inputType: 'dropdown',
    isSearchable: true,
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'nationality',
    validTabs: [
      reportTabNames.APPROVAL_REPORT,
      reportTabNames.SIGNED_ON,
      reportTabNames.SIGNED_OFF,
    ],
  },
  {
    type: reportFilterKeys.reporting_offices,
    name: 'Reporting Office',
    section: '',
    inputType: 'dropdown',
    isSearchable: true,
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'reportingOffice',
    validTabs: [reportTabNames.SIGNED_ON, reportTabNames.SIGNED_OFF],
  },
  {
    type: reportFilterKeys.report_owners,
    name: 'Owner',
    section: '',
    inputType: 'dropdown',
    isSearchable: true,
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'owner',
    validTabs: [reportTabNames.SIGNED_ON, reportTabNames.SIGNED_OFF],
  },
  {
    type: reportFilterKeys.vessel_with_id,
    name: 'Vessels',
    section: '',
    inputType: 'dropdown',
    isSearchable: true,
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'vessel',
    validTabs: [reportTabNames.SIGNED_ON, reportTabNames.SIGNED_OFF],
  },
  {
    type: reportFilterKeys.reports_vessel,
    name: 'Vessels',
    section: '',
    inputType: 'dropdown',
    isSearchable: true,
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'vessel',
    validTabs: [reportTabNames.APPROVAL_REPORT],
  },
  {
    type: reportFilterKeys.commencement_of_contract,
    name: 'Commencement of Contract',
    section: '',
    inputType: 'date',
    queryType: QUERY_TYPE_RANGE,
    queryKey: dateOfCommencementKey,
    validTabs: [reportTabNames.DG_SHIPPING_LIST],
  },
  {
    type: reportFilterKeys.sign_on,
    name: 'Sign-On',
    section: '',
    inputType: 'date',
    queryType: QUERY_TYPE_RANGE,
    queryKey: dateOfSignOnKey,
    validTabs: [reportTabNames.DG_SHIPPING_LIST],
  },
  {
    type: reportFilterKeys.sign_off,
    name: 'Sign-Off',
    section: '',
    inputType: 'date',
    queryType: QUERY_TYPE_RANGE,
    queryKey: dateOfSignOffKey,
    validTabs: [reportTabNames.DG_SHIPPING_LIST],
  },
  {
    type: reportFilterKeys.planned_wages_unit,
    name: 'Accounting Currency',
    section: '',
    inputType: 'switch',
    queryType: QUERY_TYPE_MATCH,
    queryKey: reportFilterKeys.planned_wages_unit,
    validTabs: [reportTabNames.MODELLER],
  },
];
const SEAFARER_TRAINING_COURSES_SEARCH_TYPES = [
  {
    type: 'status',
    name: 'Course Status',
    section: '',
    inputType: 'dropdown',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'status',
    notMultiple: true,
  },
  {
    type: 'is_required',
    name: 'Importance',
    section: '',
    inputType: 'dropdown',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'is_required',
    notMultiple: true,
  },
  {
    type: 'is_passed',
    name: 'Test Result',
    section: '',
    inputType: 'dropdown',
    queryType: QUERY_TYPE_MATCH,
    queryKey: 'is_passed',
    notMultiple: true,
  },
];

export default (page = SEAFARER_PAGE) => {
  if (page === SEAFARER_PAGE) return SEAFARER_SEARCH_TYPES;
  if (page === SEAFARER_REPORT_PAGE) return SEAFARER_REPORT_SEARCH_TYPES;
  if (page === SEAFARER_TRAINING_COURSES_PAGE) return SEAFARER_TRAINING_COURSES_SEARCH_TYPES;
  return [];
};
