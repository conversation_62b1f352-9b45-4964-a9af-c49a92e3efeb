export const removeSpaces = (fileName) =>
  fileName ? fileName.replace(/\s{2,}/g, ' ').replace(/ /g, '_') : '';

function containsInvalidEscapes(str: string): boolean {
  return /\\u[0-9A-Fa-f]{4}/.test(str) || /\\x[0-9A-Fa-f]{2}/.test(str);
}

function isAllowedControl(cp: number): boolean {
  return cp === 0x9 || cp === 0xa || cp === 0xd;
}

function isValidRange(cp: number): boolean {
  return (
    (cp >= 0x20 && cp <= 0xd7ff) ||
    (cp >= 0xe000 && cp <= 0xfffd) ||
    (cp >= 0x10000 && cp <= 0x10ffff)
  );
}

function isDisallowedControl(cp: number): boolean {
  return cp < 0x20;
}

function isUnpairedSurrogate(cp: number): boolean {
  return cp >= 0xd800 && cp <= 0xdfff;
}

function isNonCharacter(cp: number): boolean {
  // eslint-disable-next-line no-bitwise
  return (cp >= 0xfdd0 && cp <= 0xfdef) || (cp & 0xfffe) === 0xfffe;
}

function isEmoji(cp: number): boolean {
  const emojiRanges: Array<[number, number]> = [
    [0x1f300, 0x1f5ff], // Misc Symbols and Pictographs
    [0x1f600, 0x1f64f], // Emoticons
    [0x1f680, 0x1f6ff], // Transport & Map Symbols
    [0x1f700, 0x1f77f], // Alchemical Symbols
    [0x1f780, 0x1f7ff], // Geometric Shapes Extended
    [0x1f800, 0x1f8ff], // Supplemental Arrows-C
    [0x1f900, 0x1f9ff], // Supplemental Symbols and Pictographs
    [0x1fa00, 0x1faff], // Symbols and Pictographs Extended-A
    [0x2600, 0x26ff], // Misc symbols (including ♬, ☀️)
    [0x2700, 0x27bf], // Dingbats (includes many emoji-like symbols)
  ];
  return emojiRanges.some(([start, end]) => cp >= start && cp <= end);
}

function isValidCodePoint(cp: number): boolean {
  return (
    !isEmoji(cp) &&
    (isAllowedControl(cp) ||
      isValidRange(cp) ||
      (!isDisallowedControl(cp) && !isUnpairedSurrogate(cp) && !isNonCharacter(cp)))
  );
}

/**
 * Validates if a string contains only characters allowed in XML 1.0,
 * and excludes emoji characters.
 */
export function isValidXml10(str: string): boolean {
  if (typeof str !== 'string') return false;
  if (containsInvalidEscapes(str)) return false;

  let i = 0;
  while (i < str.length) {
    const cp = str.codePointAt(i);
    if (cp === undefined || !isValidCodePoint(cp)) return false;
    i += cp > 0xffff ? 2 : 1;
  }

  return true;
}
