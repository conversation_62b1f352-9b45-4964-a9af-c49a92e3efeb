export const getDateFromTimestamp = (date) => {
  const dateStamp = new Date(date);
  const month = getShortMonth(dateStamp.getMonth() + 1);
  //returns dd mmm yyyy
  return `${dateStamp.getDate()} ${month} ${dateStamp.getFullYear()}`;
};

export const getShortMonth = (monthNum: number) => {
  const date = new Date();
  date.setMonth(monthNum - 1);
  //returns mmm
  return date.toLocaleString('en-US', {
    month: 'short',
  });
};

export const convertTZ = (date, tzString) => {
  return (typeof date === 'string' ? new Date(date) : date)?.toLocaleString('en-US', {
    timeZone: tzString,
  });
};
