export const getSeafarerStatus = (jsonData, status) => {
  if (status && jsonData[status]) {
    return jsonData[status].name;
  }
  return '---';
};

export const replaceEmptyWithDashes = (value: string | null | undefined): string => {
    return value || '---';
};

export const formatYesNo = (value: boolean | string | null | undefined): string => {
    if (value === null || value === undefined) {
        return '---';
    }

    if (typeof value === 'boolean') {
        return value ? 'Yes' : 'No';
    }

    return value.toString().toLowerCase() === 'yes' ? 'Yes' : 'No';
};
