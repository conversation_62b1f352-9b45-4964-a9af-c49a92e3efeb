import { seafarerStatus } from '../../model/constants';
import { ON_LEAVE_SEAFARER, SEAFARERS_TO_RELIEVE } from '../../constants/crewPlanner';
import SEARCH_TYPES from '../advance-search/search-types';

const defaultFilters = (key, response = null) => {
  let filters = [];

  if (key === SEAFARERS_TO_RELIEVE) {
    filters = [];
  }

  if (key === ON_LEAVE_SEAFARER) {
    filters = [
      {
        type: SEARCH_TYPES().filter((i) => i.type === 'account_status')[0],
        subtype: [
          {
            id: 'active',
            value: 'Active',
          },
        ],
        defaultTab: ON_LEAVE_SEAFARER,
      },
      {
        type: SEARCH_TYPES().filter((i) => i.type === 'journey_status')[0],
        subtype: [
          {
            id: seafarerStatus.ON_LEAVE,
            value: 'On Leave',
          },
        ],
        defaultTab: ON_LEAVE_SEAFARER,
      },
    ];
  }

  return filters;
};

export default defaultFilters;

export const INITIAL_LOAD_FILTERS = [
  {
    type: SEARCH_TYPES().filter((i) => i.type === 'hkid')[0],
    subtype: '',
  },
];
