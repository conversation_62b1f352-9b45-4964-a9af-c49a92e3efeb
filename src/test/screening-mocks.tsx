import seafarerService from '../service/seafarer-service';
import screeningApprovalService from '../service/screening-service';

const seafarerData = (hkid = null) => {
  return {
    results: [
      {
        first_name: '<PERSON><PERSON><PERSON>',
        last_name: '<PERSON><PERSON>',
        id: 1,
        hkid: hkid,
        'seafarer_person.id': 13,
      },
    ],
  };
};

export const getScreeningJson = (status, remarks, group, withDocs) => {
  const docs = withDocs
    ? [
      {
        id: 1,
        name: 'file1.png',
        approval_id: 1,
        created_at: '2020-09-14T07:52:07.445Z',
        updated_at: '2020-09-14T07:52:07.445Z',
        mime: 'image/png',
        document_type: 'case_report',
      },
    ]
    : [];
  return {
    data: [
      {
        id: '1',
        seafarer_person_id: 13,
        created_at: '2020-08-31T06:18:42.697Z',
        updated_at: '2020-09-01T03:39:12.302Z',
        approval_group: group,
        approver_name: 'test.user',
        approval_status: status,
        remarks: remarks,
        document: docs,
      },
    ],
  };
};

export function mockSeafarerlDataCall(hkid = null) {
  seafarerService.getSeafarerFieldsData = jest
    .fn()
    .mockImplementation(() => Promise.resolve({ data: seafarerData(hkid) }));
}

export function mockScreeningDataCall(
  status = 'pending',
  remarks = null,
  group = 'Compliance Employee',
  withDocs = true,
) {
  screeningApprovalService.getScreeningData = jest
    .fn()
    .mockImplementation(() => Promise.resolve(getScreeningJson(status, remarks, group, withDocs)));
}

export function mockScreeningDataWithForwardedCall() {
  const json = [
    {
      id: '3',
      seafarer_person_id: 14,
      created_at: '2020-08-31T04:18:42.697Z',
      updated_at: '2020-09-01T04:39:12.302Z',
      approval_group: 'Compliance Employee',
      approver_name: 'test.user',
      approval_status: 'forwarded',
      remarks: 'some remarks',
      document: [],
    },
    {
      id: '2',
      seafarer_person_id: 13,
      created_at: '2020-09-01T03:39:12.302Z',
      updated_at: '2020-09-01T03:39:13.302Z',
      approval_group: 'Compliance Supervisor',
      approver_name: null,
      approval_status: 'pending',
      remarks: null,
      document: [],
    },
  ];

  screeningApprovalService.getScreeningData = jest
    .fn()
    .mockImplementation(() => Promise.resolve({ data: json }));
}

export function mockScreeningDataWithFPDCall() {
  const json = [
    {
      id: '1',
      seafarer_person_id: 13,
      created_at: '2020-08-31T01:18:42.697Z',
      updated_at: '2020-09-01T03:39:12.302Z',
      approval_group: 'Compliance Employee',
      approver_name: 'test.user1',
      approval_status: 'rejected',
      remarks: 'some remarks',
      document: [],
    },
    {
      id: '2',
      seafarer_person_id: 13,
      created_at: '2020-09-01T01:18:42.697Z',
      updated_at: '2020-09-03T03:39:13.302Z',
      approval_group: 'Compliance Employee',
      approver_name: 'test.user1',
      approval_status: 'forwarded',
      remarks: 'some remarks',
      document: [],
    },
    {
      id: '2',
      seafarer_person_id: 13,
      created_at: '2020-09-03T03:39:12.302Z',
      updated_at: '2020-09-10T03:39:14.302Z',
      approval_group: 'Compliance Supervisor',
      approver_name: 'test.user2',
      approval_status: 'rejected',
      remarks: 'some remarks',
      document: [],
    },
    {
      id: '3',
      seafarer_person_id: 13,
      created_at: '2020-09-10T03:39:12.302Z',
      updated_at: '2020-09-10T03:39:15.302Z',
      approval_group: 'Fleet Personnel',
      approver_name: null,
      approval_status: 'pending',
      remarks: null,
      document: [],
    },
  ];

  screeningApprovalService.getScreeningData = jest
    .fn()
    .mockImplementation(() => Promise.resolve({ data: json }));
}

export function getButtonsForApprovalStatus(screeningPage) {
  const firstSectionRow = screeningPage.find('table tbody tr').at(1);
  const allButtons = firstSectionRow.find('Button');
  return allButtons;
}

export function statusHistoryMockData() {
  const json = [
    {
      id: 1528279,
      seafarer_person_id: 29920,
      seafarer_account_status: 'archived',
      seafarer_journey_status: 'blacklisted',
      seafarer_exam_status: null,
      rank_id: 6,
      vessel_name: null,
      vessel_ref_id: null,
      created_by_hash: '***',
      created_by: 'Someone 1',
      seafarer_journey_remarks: 'blacklist',
      seafarer_exam_remarks: null,
      created_at: '2021-11-24T10:56:06.994Z',
      updated_at: '2021-11-24T10:56:06.994Z',
      created_at_p1: '2021-11-24T10:56:06.994Z',
      updated_at_p1: '2021-11-24T10:56:06.994Z',
      status_date: '2021-11-25T10:56:06.994Z',
      paris1_ref_id: null,
      is_current_status: true,
    },
    {
      id: 1528256,
      seafarer_person_id: 29920,
      seafarer_account_status: 'active',
      seafarer_journey_status: 'on_leave',
      seafarer_exam_status: 'ready_with_class_4_certificate_of_competency',
      rank_id: 6,
      vessel_name: null,
      vessel_ref_id: null,
      created_by_hash: '***',
      created_by: 'Some one 2',
      seafarer_journey_remarks: 'blacklist',
      seafarer_exam_remarks: null,
      created_at: '2021-11-24T10:55:04.580Z',
      updated_at: '2021-11-24T10:55:04.580Z',
      created_at_p1: '2021-11-24T10:55:04.580Z',
      updated_at_p1: '2021-11-24T10:55:04.580Z',
      status_date: '2021-11-24T10:55:04.580Z',
      paris1_ref_id: null,
      is_current_status: false,
    },
  ];
  return json;

}
export const experienceRankHistoryMockData = () => {
  const json = [
    {
      start_date: "1997-03-05T00:00:00.000Z",
      last_updated_by: null,
      value: "MM"
    },
    {
      start_date: "1989-03-15T00:00:00.000Z",
      last_updated_by: null,
      value: "OILER"
    }
  ]
  return json;
}
export const experienceMockData = () => {
  const json = [
    {
      id: 5,
      seafarer_id: 20009,
      ownership_id: null,
      rank_id: 16,
      vessel_name: "Spar Eight",
      vessel_type: "Bulk Carrier",
      deadweight_tonnage: 36227,
      deadweight_gross_registered_tonnage: "D",
      engine_type: "SULZER RLB",
      engine_sub_type: null,
      brake_horse_power: 13050,
      start_date: "1998-11-05T00:00:00.000Z",
      end_date: "1999-09-15T00:00:00.000Z",
      owner_name: "Spar Shipping AS",
      signoff_port_name: null,
      signoff_reason_id: null,
      created_by_hash: null,
      last_updated_by: null,
      created_at: "2003-07-03T05:53:02.000Z",
      updated_at: "2003-07-03T05:53:02.000Z",
      salary: 0,
      value: "MM",
      unit: "MM",
      ref_id: 2000352,
      signoff_reason: null,
      rank: {
        id: 16,
        value: "MM",
        unit: "MM",
        ref_id: 2000352
      }
    },
    {
      id: 4,
      seafarer_id: 20009,
      ownership_id: null,
      rank_id: 16,
      vessel_name: "Spar Topaz",
      vessel_type: "Bulk Carrier",
      deadweight_tonnage: 38455,
      deadweight_gross_registered_tonnage: "D",
      engine_type: "SULZER RTA",
      engine_sub_type: null,
      brake_horse_power: 9700,
      start_date: "2000-04-03T00:00:00.000Z",
      end_date: "2001-01-28T00:00:00.000Z",
      owner_name: "Spar Shipping AS",
      signoff_port_name: null,
      signoff_reason_id: null,
      created_by_hash: null,
      last_updated_by: null,
      created_at: "2003-07-03T05:53:02.000Z",
      updated_at: "2003-07-03T05:53:02.000Z",
      salary: 0,
      value: "MM",
      unit: "MM",
      ref_id: 2000352,
      signoff_reason: null,
      rank: {
        id: 16,
        value: "MM",
        unit: "MM",
        ref_id: 2000352
      }
    },
    {
      id: 3,
      seafarer_id: 20009,
      ownership_id: null,
      rank_id: 16,
      vessel_name: "M.V. Noble Empress",
      vessel_type: "Bulk Carrier",
      deadweight_tonnage: 23781,
      deadweight_gross_registered_tonnage: "D",
      engine_type: "B&W LGA",
      engine_sub_type: null,
      brake_horse_power: 10500,
      start_date: "2001-08-17T00:00:00.000Z",
      end_date: "2002-07-16T00:00:00.000Z",
      owner_name: "NOBLE CHARTERING LIMITED",
      signoff_port_name: null,
      signoff_reason_id: 1,
      created_by_hash: null,
      last_updated_by: null,
      created_at: "2002-07-19T01:28:53.000Z",
      updated_at: "2002-07-19T01:28:53.000Z",
      salary: 0,
      value: "MM",
      unit: "MM",
      ref_id: 2000352,
      signoff_reason: {
        id: 1,
        created_at: "2021-11-11T03:45:44.930Z",
        updated_at: "2021-11-11T03:45:44.930Z",
        value: "Completion of Contract",
        unit: "A",
        ref_id: 2000616
      },
      rank: {
        id: 16,
        value: "MM",
        unit: "MM",
        ref_id: 2000352
      }
    },
    {
      id: 2,
      seafarer_id: 20009,
      ownership_id: null,
      rank_id: 16,
      vessel_name: "Spar Three",
      vessel_type: "Bulk Carrier",
      deadweight_tonnage: 35451,
      deadweight_gross_registered_tonnage: "D",
      engine_type: "SULZER RLB",
      engine_sub_type: null,
      brake_horse_power: 11850,
      start_date: "2003-02-19T00:00:00.000Z",
      end_date: "2003-12-08T00:00:00.000Z",
      owner_name: "Spar Shipping AS",
      signoff_port_name: null,
      signoff_reason_id: 1,
      created_by_hash: null,
      last_updated_by: null,
      created_at: "2003-12-09T07:18:02.000Z",
      updated_at: "2003-12-09T07:18:02.000Z",
      salary: 0,
      value: "MM",
      unit: "MM",
      ref_id: 2000352,
      signoff_reason: {
        id: 1,
        created_at: "2021-11-11T03:45:44.930Z",
        updated_at: "2021-11-11T03:45:44.930Z",
        value: "Completion of Contract",
        unit: "A",
        ref_id: 2000616
      },
      rank: {
        id: 16,
        value: "MM",
        unit: "MM",
        ref_id: 2000352
      }
    },
    {
      id: 1,
      seafarer_id: 20009,
      ownership_id: null,
      rank_id: 16,
      vessel_name: "Pioneer Sky",
      vessel_type: "Bulk Carrier",
      deadweight_tonnage: 1,
      deadweight_gross_registered_tonnage: "D",
      engine_type: null,
      engine_sub_type: null,
      brake_horse_power: 1,
      start_date: "2004-06-22T00:00:00.000Z",
      end_date: "2005-04-25T00:00:00.000Z",
      owner_name: "Interunity Management Corp. S.A.",
      signoff_port_name: null,
      signoff_reason_id: null,
      created_by_hash: null,
      last_updated_by: null,
      created_at: "2005-04-28T02:44:27.000Z",
      updated_at: "2005-04-28T02:44:27.000Z",
      salary: 802,
      value: "MM",
      unit: "MM",
      ref_id: 2000352,
      signoff_reason: null,
      rank: {
        id: 16,
        value: "MM",
        unit: "MM",
        ref_id: 2000352
      }
    }
  ]
  return json;
}
export default {
  mockSeafarerlDataCall,
  mockScreeningDataCall,
  getButtonsForApprovalStatus,
  getScreeningJson,
  statusHistoryMockData,
  experienceRankHistoryMockData,
  experienceMockData
};
