import React from 'react';
import { MemoryRouter, Route } from 'react-router-dom';
import '@testing-library/jest-dom';
import moment from 'moment-timezone';
import { act, fireEvent, render, screen, waitFor } from '@testing-library/react';
import * as seafarerService from '../../../service/seafarer-service';
import {
  wagesApiResponseWithUserNotSignedOn,
  wagesApiResponseWithWagesDetails,
} from '../../resources/wages-api-response-with-wages-details';
import UpdateWagesModal from '../../../component/UpdateWages/UpdateWagesModal';
import { seafarerGetResponse } from '../../resources/document-response';
import dropdowndata from '../../resources/drop-down-data.json';
import { ActiveVesselData } from '../../resources/vessel-ownership-data';

jest.mock('../../../styleGuide');
jest.mock('../../../service/user-service');
jest.mock('../../../service/seafarer-service');
const seafarer = seafarerGetResponse.data;
let setShowUpdateWagesModal;
const seafarerId = seafarer.id;
const showUpdateWagesModal = true;
const history = {
  location: {
    pathname: '',
  },
  goBack: () => {},
};
const refreshDetailsPageData = () => jest.fn();
const eventTracker = () => jest.fn();
const activeVesselData = ActiveVesselData.results;

const renderUpdateWagesModal = (
  seafarer,
  setShowUpdateWagesModal,
  history,
  showUpdateWagesModal,
) => {
  return act(async () => {
    render(
      <MemoryRouter initialEntries={[`/seafarer/details/${seafarerId}/pre-joining/wages/add`]}>
        <Route exact path="/seafarer/details/:seafarerId?/pre-joining/wages/add">
          <UpdateWagesModal
            setShowUpdateWagesModal={setShowUpdateWagesModal}
            seafarer={seafarer}
            history={history}
            showUpdateWagesModal={showUpdateWagesModal}
            refreshDetailsPageData={refreshDetailsPageData}
            eventTracker={eventTracker}
            roleConfig={{
              seafarer: {
                edit: {
                  wages: true,
                },
              },
            }}
            activeVesselData={activeVesselData}
          />
        </Route>
      </MemoryRouter>,
    );
  });
};

const renderPageWithoutRole = (
  seafarer,
  setShowUpdateWagesModal,
  history,
  showUpdateWagesModal,
) => {
  return act(async () => {
    render(
      <MemoryRouter initialEntries={[`/seafarer/details/${seafarerId}/pre-joining/wages/add`]}>
        <Route exact path="/seafarer/details/:seafarerId?/pre-joining/wages/add">
          <UpdateWagesModal
            setShowUpdateWagesModal={setShowUpdateWagesModal}
            seafarer={seafarer}
            history={history}
            showUpdateWagesModal={showUpdateWagesModal}
            refreshDetailsPageData={refreshDetailsPageData}
            roleConfig={{}}
            activeVesselData={activeVesselData}
          />
        </Route>
      </MemoryRouter>,
    );
  });
};

beforeAll(async () => {
  jest.spyOn(seafarerService, 'getSeafarerWages').mockImplementation(() =>
    Promise.resolve({
      status: 200,
      data: wagesApiResponseWithWagesDetails,
    }),
  );
  jest.spyOn(seafarerService, 'getSeafarerDropDownData').mockImplementation(() =>
    Promise.resolve({
      status: 200,
      ranks: dropdowndata.ranks,
    }),
  );
  jest.spyOn(seafarerService, 'patchSeafarerWages').mockImplementation((payload) =>
    Promise.resolve({
      status: 200,
      data: payload,
    }),
  );
  setShowUpdateWagesModal = jest.fn().mockImplementation(() => {});
});

describe('Render UpdateWagesModal for creating and updating wages', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('Should validate fail when amounts in the number fields have more than 2 digits post decimal', async () => {
    await renderUpdateWagesModal(seafarer, setShowUpdateWagesModal, history, showUpdateWagesModal);
    await act(async () => {
      const allInputs = screen.getAllByRole('textbox');
      const numberInput = allInputs.find((input) => input.name === 'effective_date');
      const oldDate = moment().startOf('month').toDate();
      if (numberInput) {
        fireEvent.change(numberInput, { target: { value: oldDate } });
      }
    });

    await act(async () => {
      const allInputs = screen.getAllByRole('spinbutton');
      const payhead = allInputs.find((input) => input.name === 'payhead_2');
      if (payhead) {
        fireEvent.change(payhead, { target: { value: '2.3445' } });
      }
    });

    await act(async () => {
      const save = screen.getByRole('button', { name: /save/i });
      if (save) {
        fireEvent.click(save);
      }
    });
    await waitFor(() => {
      expect(
        screen.getByText('Please enter the amount with at most 2 decimal places'),
      ).toBeInTheDocument();
    });
  });

  it('Should validate fail on effective date field if effective date is in the past and not in current month', async () => {
    await renderUpdateWagesModal(seafarer, setShowUpdateWagesModal, history, showUpdateWagesModal);
    await act(async () => {
      const allInputs = screen.getAllByRole('textbox');
      const numberInput = allInputs.find((input) => input.name === 'effective_date');
      const oldDate = moment().startOf('month').subtract(1, 'day').toDate();
      if (numberInput) {
        fireEvent.change(numberInput, { target: { value: oldDate } });
      }
    });

    await act(async () => {
      const save = screen.getByRole('button', { name: /save/i });
      if (save) {
        fireEvent.click(save);
      }
    });
    await waitFor(() => {
      expect(
        screen.getByText('Effective date must be future/past date of current month'),
      ).toBeInTheDocument();
    });
  });

  it('Should validate pass on effective date field if effective date is in the future or a past date in current month', async () => {
    await renderUpdateWagesModal(seafarer, setShowUpdateWagesModal, history, showUpdateWagesModal);
    await act(async () => {
      const allInputs = screen.getAllByRole('textbox');
      const numberInput = allInputs.find((input) => input.name === 'effective_date');
      const oldDate = moment().startOf('month').toDate();
      if (numberInput) {
        fireEvent.change(numberInput, { target: { value: oldDate } });
      }
    });

    await act(async () => {
      const save = screen.getByRole('button', { name: /save/i });
      if (save) {
        fireEvent.click(save);
      }
    });
    await waitFor(() => {
      expect(
        screen.queryByText('Effective date must be future/past date of current month'),
      ).not.toBeInTheDocument();
    });
  });

  it('Should validate patch body to only include fields that were updated in UI', async () => {
    await renderUpdateWagesModal(seafarer, setShowUpdateWagesModal, history, showUpdateWagesModal);

    const newDate = moment().startOf('month').toDate();
    await act(async () => {
      const allInputs = screen.getAllByRole('textbox');
      const numberInput = allInputs.find((input) => input.name === 'effective_date');
      if (numberInput) {
        fireEvent.change(numberInput, { target: { value: newDate } });
      }
    });
    await act(async () => {
      const allInputs = screen.getAllByRole('spinbutton');
      const payhead2 = allInputs.find((input) => input.name === 'payhead_2');
      if (payhead2) {
        fireEvent.change(payhead2, { target: { value: '1045' } });
      }
      const payhead3 = allInputs.find((input) => input.name === 'payhead_3');
      if (payhead3) {
        fireEvent.change(payhead3, { target: { value: '500' } });
      }
    });

    const patchSpy = jest.spyOn(seafarerService, 'patchSeafarerWages');

    const seafarerStatusHistoryId = wagesApiResponseWithWagesDetails[0].seafarer_status_history.id;
    const wagesId = wagesApiResponseWithWagesDetails[0].id;
    const payload = {
      amount_unit: 'usd',
      effective_date: moment(newDate).format('YYYY-MM-DD'),
      seafarer_wages_details: [
        {
          id: 2,
          payhead_id: 2,
          amount: 1045,
        },
        {
          id: 3,
          payhead_id: 3,
          amount: 500,
        },
      ],
    };

    await waitFor(() => {
      const save = screen.getByRole('button', { name: /save/i });
      if (save) {
        fireEvent.click(save);
      }
    });
    expect(patchSpy).toHaveBeenCalledWith(
      seafarerId.toString(),
      seafarerStatusHistoryId,
      wagesId,
      payload,
    );
  });

  it('Should disable new_rank and new_contract_end_date when promotion field is not checked', async () => {
    let checkboxInput: HTMLElement;
    await renderUpdateWagesModal(seafarer, setShowUpdateWagesModal, history, showUpdateWagesModal);
    await waitFor(() => {
      const allInputs = screen.getAllByRole('checkbox');
      checkboxInput = allInputs.find((input) => input.name === 'promotion');
    });
    await act(async () => {
      if (checkboxInput) {
        fireEvent.change(checkboxInput, { target: { checked: false } });
      }
    });
    expect(checkboxInput?.checked).toBe(false);
    expect(screen.getByTestId('new_rank')).toBeDisabled();
    expect(screen.getByTestId('fml-update-wages-modal-new-contract-end-date')).toBeDisabled();
  });

  it('Should enable new_rank and new_contract_end_date when promotion field is checked', async () => {
    await renderUpdateWagesModal(seafarer, setShowUpdateWagesModal, history, showUpdateWagesModal);
    await act(async () => {
      const allInputs = screen.getAllByRole('checkbox');
      const checkboxInput = allInputs.find((input) => input.name === 'promotion');
      if (checkboxInput) {
        fireEvent.change(checkboxInput, { target: { checked: true } });
      }
      expect(checkboxInput?.checked).toBe(true);
      expect(screen.getByTestId('fml-update-wages-modal-effective-date')).not.toBeDisabled();
    });
  });

  it('Should display error if new contract end date field is less than effective date', async () => {
    await renderUpdateWagesModal(seafarer, setShowUpdateWagesModal, history, showUpdateWagesModal);
    await act(async () => {
      const allCheckBox = screen.getAllByRole('checkbox');
      const checkboxInput = allCheckBox.find((input) => input.name === 'promotion');
      if (checkboxInput) {
        fireEvent.change(checkboxInput, { target: { checked: true } });
      }
      expect(checkboxInput?.checked).toBe(true);
      const allInputs = screen.getAllByRole('textbox');
      const effectiveDate = allInputs.find((input) => input.name === 'effective_date');
      const newEffectiveDate = moment().add(2, 'month').toDate();
      if (effectiveDate) {
        fireEvent.change(effectiveDate, { target: { value: newEffectiveDate } });
      }
    });
    await act(async () => {
      const contractEndDate = screen.getByTestId('fml-update-wages-modal-new-contract-end-date');
      const newContractDate = moment().add(1, 'month').toDate();
      if (contractEndDate) {
        fireEvent.change(contractEndDate, { target: { value: newContractDate } });
      }
    });
    await act(async () => {
      const save = screen.getByRole('button', { name: /save/i });
      if (save) {
        fireEvent.click(save);
      }
    });

    const errorMsg = await waitFor(() =>
      screen.queryByText('New contract end date cannot be less than Effective date'),
    );
    expect(errorMsg).not.toBeInTheDocument();
  });

  it('Should disable promotion checkbox when seafarer_status_history.seafarer_journey_status not "signed_on"', async () => {
    jest.spyOn(seafarerService, 'getSeafarerWages').mockImplementation(() =>
      Promise.resolve({
        status: 200,
        data: wagesApiResponseWithUserNotSignedOn,
      }),
    );
    await renderUpdateWagesModal(seafarer, setShowUpdateWagesModal, history, showUpdateWagesModal);
    await waitFor(() => {
      const allInputs = screen.getAllByRole('checkbox');
      const promotion = allInputs.find((input) => input.name === 'promotion');
      expect(promotion).toBeDisabled();
    });
  });

  it('Should disable Effective date when seafarer_status_history.seafarer_journey_status not "signed_on"', async () => {
    jest.spyOn(seafarerService, 'getSeafarerWages').mockImplementation(() =>
      Promise.resolve({
        status: 200,
        data: wagesApiResponseWithUserNotSignedOn,
      }),
    );
    await renderUpdateWagesModal(seafarer, setShowUpdateWagesModal, history, showUpdateWagesModal);
    await waitFor(() => {
      const allInputs = screen.getAllByRole('textbox');
      const effectiveDate = allInputs.find((input) => input.name === 'effective_date');
      expect(effectiveDate).toBeDisabled();
    });
  });
  it('should show 403 page when seafarer|edit|wages role is absent', async () => {
    await renderPageWithoutRole();
    expect(screen.getByText('403')).toBeInTheDocument();
  });
});
