import React from 'react';
import NavigationBar from '../../../component/SeafarerReport/NavigationBar/NavigationBar';
import { render, cleanup, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { MemoryRouter, Route } from 'react-router-dom';

describe('NavigationBar component', () => {
  afterEach(cleanup);
  it('Should route to modeller report page when clicked on vessel name', async () => {
    render(
      <MemoryRouter initialEntries={[`/seafarer-reports/modeller/2`]}>
        <Route exact path="/seafarer-reports/modeller/:vesselOwnerShipId">
          <NavigationBar vesselName="Sagami" vesselOwnerShipId={'2'} isEdit={true} />
        </Route>
      </MemoryRouter>,
    );
    expect(screen.getByText('Sagami').closest('a')).toHaveAttribute(
      'href',
      '/seafarer-reports/modeller/2',
    );
  });
  it('Should route to modeller report page when clicked seafarer modeller', async () => {
    render(
      <MemoryRouter initialEntries={[`/seafarer-reports/modeller/2`]}>
        <Route exact path="/seafarer-reports/modeller/:vesselOwnerShipId">
          <NavigationBar vesselName="Sagami" vesselOwnerShipId={'2'} isEdit={true} />
        </Route>
      </MemoryRouter>,
    );
    expect(screen.getByText('Seafarer Reports • Modeller').closest('a')).toHaveAttribute(
      'href',
      '/seafarer-reports/modeller',
    );
  });
});
