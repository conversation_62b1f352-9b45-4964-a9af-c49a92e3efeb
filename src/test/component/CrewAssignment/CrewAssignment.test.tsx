import React from 'react';
import { MemoryRouter, Route } from 'react-router-dom';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { seafarerStatus } from '@src/model/constants';
import { SUPY } from '@src/constants/recommendation';
import { cloneDeep } from 'lodash';
import ViewCrewAssignmentPlan from '../../../component/CrewAssignment/ViewCrewAssignmentPlan';
import * as seafarerService from '../../../service/seafarer-service';
import { seafarerGetResponse } from '../../resources/document-response';
import {
  wagesApiResponseWithWagesDetails,
  wagesApiResponseWithWagesDetailsAndIsPromotion,
} from '../../resources/wages-api-response-with-wages-details';
import { getSeafarerStatusHistoryByPersonIDMockResponse } from '../../resources/seafarer-status-history';
import { WAGES_STATUS_APPLIED } from '../../../constants/seafarer-wages';
import { ActiveVesselData } from '../../resources/vessel-ownership-data';

jest.mock('../../../styleGuide');
jest.mock('../../../service/user-service');
jest.mock('../../../service/seafarer-service');

const seafarer = seafarerGetResponse.data;
const seafarerId = seafarer.id;
const activeVesselData = ActiveVesselData.results;
const dropDownData = {
  ranks: [
    { id: 6, value: 'CHIEF ENGINEER' },

    { id: 14, value: 'MASTER' },
  ],
};

const roleConfig = {
  seafarer: {
    edit: {
      recommendation: true,
      recommendationApproval: true,
      recommendationApprovalExceptTopRanks: true,
      recommendationDeviationApproval: true,
      recommendationDeviationApprovalExceptTopRanks: true,
      seafarerSuperUser: true,
    },
  },
};

const renderViewCrewAssignmentPlan = (seafarer) => (
  <MemoryRouter initialEntries={[`/seafarer/details/${seafarerId}/general`]}>
    <Route exact path="/seafarer/details/:seafarerId?/general">
      <ViewCrewAssignmentPlan
        seafarer={seafarer}
        activeVesselData={activeVesselData}
        dropdownData={dropDownData}
        roleConfig={roleConfig}
        plansList={getSeafarerStatusHistoryByPersonIDMockResponse()}
        setError={() => {}}
        eventTracker={jest.fn()}
      />
    </Route>
  </MemoryRouter>
);

const mockGetScreeningStatus = async (status, screen) => {
  jest.spyOn(seafarerService, 'getScreeningStatus').mockImplementation(() =>
    Promise.resolve({
      status: 200,
      data: {
        error: false,
      },
    }),
  );

  await waitFor(() => {
    const approveBtn = screen.getByTestId('approve-button');
    fireEvent.click(approveBtn);
  });
};

beforeEach(async () => {
  jest.spyOn(seafarerService, 'getSeafarerStatusHistoryByPersonID').mockImplementation(() =>
    Promise.resolve({
      status: 200,
      data: getSeafarerStatusHistoryByPersonIDMockResponse(),
    }),
  );
  jest.spyOn(seafarerService, 'getSeafarerWages').mockImplementation(() =>
    Promise.resolve({
      status: 200,
      data: wagesApiResponseWithWagesDetails,
    }),
  );
});

describe('View Crew Assignment Plan in General Tab', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('Should be able to render multiple Crew Assigment plans', async () => {
    render(renderViewCrewAssignmentPlan(seafarer));
    await waitFor(() => {
      expect(screen.getByText('Crew Assignment Plan 4')).toBeInTheDocument();
      expect(screen.getByText('Crew Assignment Plan 3')).toBeInTheDocument();
      expect(screen.getByText('Crew Assignment Plan 2')).toBeInTheDocument();
      expect(screen.getByText('Crew Assignment Plan 1')).toBeInTheDocument();
    });
  });
  it('Should contains relavent fields when seafarer is not signed_on', async () => {
    jest.spyOn(seafarerService, 'getSeafarerStatusHistoryByPersonID').mockImplementation(() =>
      Promise.resolve({
        status: 200,
        data: getSeafarerStatusHistoryByPersonIDMockResponse().filter(
          (i) => i.seafarer_journey_status === 'crew_assignment_approved',
        ),
      }),
    );
    render(renderViewCrewAssignmentPlan(seafarer));

    await waitFor(() => {
      expect(screen.getAllByText('Date')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Vessel')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Effective Date')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Status')[0]).toBeInTheDocument();
      expect(screen.getAllByText('By')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Wages')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Remark')[0]).toBeInTheDocument();
    });
  });
  it('Should contains relavent fields when seafarer is signed_on with no wages update', async () => {
    jest.spyOn(seafarerService, 'getSeafarerStatusHistoryByPersonID').mockImplementation(() =>
      Promise.resolve({
        status: 200,
        data: getSeafarerStatusHistoryByPersonIDMockResponse().filter(
          (i) => i.seafarer_journey_status === 'signed_on',
        ),
      }),
    );
    const wagesApiResponseWithWagesDetailsWithoutPromotion = {
      ...wagesApiResponseWithWagesDetails.filter((i) => i.status === WAGES_STATUS_APPLIED)[0],
      seafarer_promotion: [],
    };
    jest.spyOn(seafarerService, 'getSeafarerWages').mockImplementation(() =>
      Promise.resolve({
        status: 200,
        data: [wagesApiResponseWithWagesDetailsWithoutPromotion], // wagesApiResponseWithWagesDetails.filter((i) => i.status === WAGES_STATUS_APPLIED),
      }),
    );
    render(renderViewCrewAssignmentPlan(seafarer));

    await waitFor(() => {
      expect(screen.getAllByText('Date')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Vessel')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Status')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Rank')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Wages')[0]).toBeInTheDocument();
    });
  });
  it('Should contains relavent fields when seafarer is signed_on with wages update', async () => {
    jest.spyOn(seafarerService, 'getSeafarerStatusHistoryByPersonID').mockImplementation(() =>
      Promise.resolve({
        status: 200,
        data: getSeafarerStatusHistoryByPersonIDMockResponse().filter(
          (i) => i.seafarer_journey_status === 'signed_on',
        ),
      }),
    );
    jest.spyOn(seafarerService, 'getSeafarerWages').mockImplementation(() =>
      Promise.resolve({
        status: 200,
        data: wagesApiResponseWithWagesDetailsAndIsPromotion,
      }),
    );
    render(renderViewCrewAssignmentPlan(seafarer));

    await waitFor(() => {
      expect(screen.getAllByText('Date')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Vessel')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Status')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Rank')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Wages')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Effective Date')[0]).toBeInTheDocument();
      expect(screen.getAllByText('New Contract End Date')[0]).toBeInTheDocument();
      expect(screen.getAllByText('New Wages')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Wages and Promotion Status')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Action')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Last Created Date')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Last Edited By')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Remark')[0]).toBeInTheDocument();

      expect(screen.getAllByText('Update Wages and Promotion')[0]).toBeInTheDocument();
    });
  });
  it('Should not contain promotion related fields when only wages are updated', async () => {
    jest.spyOn(seafarerService, 'getSeafarerStatusHistoryByPersonID').mockImplementation(() =>
      Promise.resolve({
        status: 200,
        data: getSeafarerStatusHistoryByPersonIDMockResponse().filter(
          (i) => i.seafarer_journey_status === 'signed_on',
        ),
      }),
    );
    jest.spyOn(seafarerService, 'getSeafarerWages').mockImplementation(() =>
      Promise.resolve({
        status: 200,
        data: wagesApiResponseWithWagesDetails.map((i) => {
          if (i.is_history === false) {
            return { ...i, seafarer_promotion: [] };
          }
          return i;
        }),
      }),
    );
    render(renderViewCrewAssignmentPlan(seafarer));

    await waitFor(() => {
      expect(screen.getAllByText('Date')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Vessel')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Status')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Rank')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Wages')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Effective Date')[0]).toBeInTheDocument();
      expect(screen.getAllByText('New Wages')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Wages and Promotion Status')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Action')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Last Created Date')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Last Edited By')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Remark')[0]).toBeInTheDocument();
    });
  });
  it('Should not display action field when update wages status is not pending', async () => {
    jest.spyOn(seafarerService, 'getSeafarerStatusHistoryByPersonID').mockImplementation(() =>
      Promise.resolve({
        status: 200,
        data: getSeafarerStatusHistoryByPersonIDMockResponse().filter(
          (i) => i.seafarer_journey_status === 'signed_on',
        ),
      }),
    );
    jest.spyOn(seafarerService, 'getSeafarerWages').mockImplementation(() =>
      Promise.resolve({
        status: 200,
        // data: wagesApiResponseWithWagesDetails,
        data: wagesApiResponseWithWagesDetailsAndIsPromotion.map((i) => {
          if (i.is_history === false) {
            return { ...i, status: 'applied' };
          }
          return i;
        }),
      }),
    );
    render(renderViewCrewAssignmentPlan(seafarer));

    await waitFor(() => {
      expect(screen.getAllByText('Date')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Vessel')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Status')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Rank')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Wages')[0]).toBeInTheDocument();
      expect(screen.getAllByText('New Rank')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Effective Date')[0]).toBeInTheDocument();
      expect(screen.getAllByText('New Contract End Date')[0]).toBeInTheDocument();
      expect(screen.getAllByText('New Wages')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Wages and Promotion Status')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Last Created Date')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Last Edited By')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Remark')[0]).toBeInTheDocument();
    });
  });

  it('should click event fire on click approval button in case recommendation', async () => {
    render(renderViewCrewAssignmentPlan(seafarer));
    await waitFor(() => {
      mockGetScreeningStatus('recommended', screen);
      const modal = screen.getAllByTestId('recommendation-approval-modal');
      expect(modal[0]).toBeTruthy();
    });
  });

  it('should change event fire on wages field', async () => {
    render(renderViewCrewAssignmentPlan(seafarer));
    await waitFor(() => {
      mockGetScreeningStatus('recommended', screen);
      const wagesField = screen.getByTestId('wages');
      fireEvent.change(wagesField, { target: { value: '123' } });
      expect(wagesField.value).toEqual('123');
    });
  });

  it('should change event fire on remarks field', async () => {
    render(renderViewCrewAssignmentPlan(seafarer));
    await waitFor(() => {
      mockGetScreeningStatus('recommended', screen);
      const remarks = screen.getByTestId('remarks');
      fireEvent.change(remarks, { target: { value: 'hi' } });
      expect(remarks.value).toEqual('hi');
    });
  });

  it('should keypress event fire on wages field', async () => {
    render(renderViewCrewAssignmentPlan(seafarer));
    await waitFor(() => {
      mockGetScreeningStatus('recommended', screen);
      expect(screen.getAllByText('Date')[0]).toBeInTheDocument();
      const wagesField = screen.getByTestId('wages');
      fireEvent.keyPress(wagesField, { key: 'e', code: 'KeyE', charCode: 69 });
    });
  });

  it('Should contains set to travel button(enabled) when wages and contract details are set and seafarer status is crew assignment approved, ', async () => {
    jest.spyOn(seafarerService, 'getSeafarerStatusHistoryByPersonID').mockImplementation(() =>
      Promise.resolve({
        status: 200,
        data: getSeafarerStatusHistoryByPersonIDMockResponse().filter(
          (i) => i.seafarer_journey_status === seafarerStatus.CREW_ASSIGNMENT_APPROVED,
        ),
      }),
    );
    const wagesApiResponseWithWagesDetailsWithoutPromotion = {
      ...wagesApiResponseWithWagesDetails.filter((i) => i.status === WAGES_STATUS_APPLIED)[0],
      seafarer_promotion: [],
    };
    jest.spyOn(seafarerService, 'getSeafarerWages').mockImplementation(() =>
      Promise.resolve({
        status: 200,
        data: [wagesApiResponseWithWagesDetailsWithoutPromotion], // wagesApiResponseWithWagesDetails.filter((i) => i.status === WAGES_STATUS_APPLIED),
      }),
    );
    render(renderViewCrewAssignmentPlan(seafarer));
    let button;
    await waitFor(() => {
      button = screen.getByTestId('set-to-travel-button');
      expect(button).toBeEnabled();
      expect(button).toBeInTheDocument();
    });
    fireEvent.click(button);
    await waitFor(() => {
      expect(screen.getByText('Select Travel Date')).toBeInTheDocument();
    });
  });
  it('Should contains set to travel button(disable) when wages or contract details are not set or  seafarer status is  not crew assignment approved, ', async () => {
    jest.spyOn(seafarerService, 'getSeafarerStatusHistoryByPersonID').mockImplementation(() =>
      Promise.resolve({
        status: 200,
        data: getSeafarerStatusHistoryByPersonIDMockResponse().filter(
          (i) => i.seafarer_journey_status === seafarerStatus.CREW_ASSIGNMENT_APPROVED,
        ),
      }),
    );
    jest.spyOn(seafarerService, 'getSeafarerWages').mockImplementation(() =>
      Promise.resolve({
        status: 200,
        data: [], // wagesApiResponseWithWagesDetails.filter((i) => i.status === WAGES_STATUS_APPLIED),
      }),
    );
    render(renderViewCrewAssignmentPlan(seafarer));
    await waitFor(() => {
      const button = screen.getByTestId('set-to-travel-button');

      expect(button).not.toBeEnabled();
      expect(button).toBeInTheDocument();
    });
  });
  it('Should contains set to travel button(enabled) when seafarer status is crew assignment approved and rank is SUPY', async () => {
    jest.spyOn(seafarerService, 'getSeafarerStatusHistoryByPersonID').mockImplementation(() =>
      Promise.resolve({
        status: 200,
        data: getSeafarerStatusHistoryByPersonIDMockResponse().filter(
          (i) => i.seafarer_journey_status === seafarerStatus.CREW_ASSIGNMENT_APPROVED,
        ),
      }),
    );
    jest.spyOn(seafarerService, 'getSeafarerWages').mockImplementation(() =>
      Promise.resolve({
        status: 200,
        data: [], // wagesApiResponseWithWagesDetails.filter((i) => i.status === WAGES_STATUS_APPLIED),
      }),
    );
    const clonedSeafarer = cloneDeep(seafarer);
    clonedSeafarer.seafarer_rank.value = SUPY;
    render(renderViewCrewAssignmentPlan(clonedSeafarer));

    await waitFor(() => {
      const button = screen.getByTestId('set-to-travel-button');
      expect(button).toBeEnabled();
      expect(button).toBeInTheDocument();
    });
  });
});
