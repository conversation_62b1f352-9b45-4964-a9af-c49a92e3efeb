import { STATUS_COLORS } from '@src/model/constants';
import {
  getVesselType,
  getVesselIdFromSeafarer,
  getSeafarerRank,
  getSeafarerVesselId,
  getSeafarerVesselOwnershipId,
  checkMissingPersonnel,
  checkAdditionalRequest,
  getSelelectRanks,
  getScoringColor,
  getScoreDifferenceColor,
} from '../../../pages/CrewPlanner/utils';
import {
  DOCUMENT_SCORE_GRADE,
} from '@src/constants/crewPlanner';

jest.mock('../../../service/user-service');
jest.mock('../../../styleGuide');
describe('Utility Functions', () => {
  describe('getVesselType', () => {
    it('should return vessel_type value when missing personnel', () => {
      const seafarer = { isMissingPersonnel: true, vessel_type: { value: 'Cargo' } };
      expect(getVesselType(seafarer)).toBe('Cargo');
    });

    it('should return vessel_type when additional request', () => {
      const seafarer = { isAdditionalRequest: true, vessel_type: 'Tanker' };
      expect(getVesselType(seafarer)).toBe('Tanker');
    });

    it('should return vessel_type from seafarer_person history', () => {
      const seafarer = {
        seafarer_person: {
          seafarer_status_history: [{ vessel_type: 'Fishing' }],
        },
      };
      expect(getVesselType(seafarer)).toBe('Fishing');
    });

    it('should return empty string if no vessel_type found', () => {
      const seafarer = {};
      expect(getVesselType(seafarer)).toBe("");
    });
  });

  describe('getVesselIdFromSeafarer', () => {
    it('should return vessel_id when missing personnel or additional request', () => {
      const seafarer = { isMissingPersonnel: true, vessel_id: '123' };
      expect(getVesselIdFromSeafarer(seafarer)).toBe('123');
    });

    it('should return vessel_id from seafarer_person history', () => {
      const seafarer = {
        seafarer_person: {
          seafarer_status_history: [{ vessel_id: '456' }],
        },
      };
      expect(getVesselIdFromSeafarer(seafarer)).toBe('456');
    });

    it('should return empty string if no vessel_id found', () => {
      const seafarer = {};
      expect(getVesselIdFromSeafarer(seafarer)).toBe("");
    });
  });

  describe('getSeafarerRank', () => {
    it('should return rank when missing personnel', () => {
      const seafarer = { isMissingPersonnel: true, rank: 'Captain' };
      expect(getSeafarerRank(seafarer)).toBe('Captain');
    });

    it('should return rank value when additional request', () => {
      const seafarer = { isAdditionalRequest: true, rank: { value: 'First Mate' } };
      expect(getSeafarerRank(seafarer)).toBe('First Mate');
    });

    it('should return seafarer_rank value from history', () => {
      const seafarer = {
        seafarer_person: {
          seafarer_status_history: [{ seafarer_rank: { value: 'Chief Engineer' } }],
        },
      };
      expect(getSeafarerRank(seafarer)).toBe('Chief Engineer');
    });

    it('should return empty string if no rank found', () => {
      const seafarer = {};
      expect(getSeafarerRank(seafarer)).toBe('');
    });
  });

  describe('getSeafarerVesselId', () => {
    it('should return vessel_id from history if present', () => {
      const seafarer = {
        seafarer_person: {
          seafarer_status_history: [{ vessel_id: '789' }],
        },
        vessel_id: '001',
      };
      expect(getSeafarerVesselId(seafarer)).toBe('789');
    });

    it('should return vessel_id from the seafarer object if history is absent', () => {
      const seafarer = { vessel_id: '001' };
      expect(getSeafarerVesselId(seafarer)).toBe('001');
    });

    it('should return empty string if no vessel_id found', () => {
      const seafarer = {};
      expect(getSeafarerVesselId(seafarer)).toBe(undefined);
    });
  });

  describe('getSeafarerVesselOwnershipId', () => {
    it('should return vessel_ownership_id from history if present', () => {
      const seafarer = {
        seafarer_person: {
          seafarer_status_history: [{ vessel_ownership_id: '111' }],
        },
        vessel_ownership_id: '222',
      };
      expect(getSeafarerVesselOwnershipId(seafarer)).toBe('111');
    });

    it('should return vessel_ownership_id from the seafarer object if history is absent', () => {
      const seafarer = { vessel_ownership_id: '222' };
      expect(getSeafarerVesselOwnershipId(seafarer)).toBe('222');
    });

    it('should return undefined if no vessel_ownership_id found', () => {
      const seafarer = {};
      expect(getSeafarerVesselOwnershipId(seafarer)).toBeUndefined();
    });
  });

  describe('checkMissingPersonnel', () => {
    it('should return true if missing personnel condition is met', () => {
      const seafarer = { isMissingPersonnel: true };
      expect(checkMissingPersonnel(seafarer)).toBe(true);
    });

    it('should return false if not missing personnel', () => {
      const seafarer = { isMissingPersonnel: false };
      expect(checkMissingPersonnel(seafarer)).toBe(false);
    });
  });

  describe('checkAdditionalRequest', () => {
    it('should return true if additional request condition is met', () => {
      const seafarer = { isAdditionalRequest: true };
      expect(checkAdditionalRequest(seafarer)).toBe(true);
    });

    it('should return false if not an additional request', () => {
      const seafarer = { isAdditionalRequest: false };
      expect(checkAdditionalRequest(seafarer)).toBe(false);
    });
  });

  describe('getSelelectRanks', () => {
    it('should return ranks from historyRankKey', () => {
      const apiQueryObj = {
        'seafarer_person:seafarer_status_history:seafarer_rank.value': 'Rank1|Rank2',
      };
      expect(getSelelectRanks(apiQueryObj)).toEqual(['Rank1', 'Rank2']);
    });

    it('should return ranks from target_rank.value', () => {
      const apiQueryObj = { 'target_rank.value': 'Rank3,Rank4' };
      expect(getSelelectRanks(apiQueryObj)).toEqual(['Rank3', 'Rank4']);
    });

    it('should return empty array if no ranks found', () => {
      const apiQueryObj = {};
      expect(getSelelectRanks(apiQueryObj)).toEqual([]);
    });
  });

  describe('getScoringColor', () => {
    it('should return correct class for Fail score', () => {
      expect(getScoringColor(DOCUMENT_SCORE_GRADE.Fail)).toBe(
        'average-score-wrapper average-score-red',
      );
    });

    it('should return correct class for Pass score', () => {
      expect(getScoringColor(DOCUMENT_SCORE_GRADE.Pass)).toBe(
        'average-score-wrapper average-score-green',
      );
    });

    it('should return correct class for scores', () => {
      expect(getScoringColor(90)).toBe('average-score-wrapper average-score-green');
      expect(getScoringColor(75)).toBe('average-score-wrapper average-score-green-light');
      expect(getScoringColor(65)).toBe('average-score-wrapper average-score-yellow');
      expect(getScoringColor(55)).toBe('average-score-wrapper average-score-orange');
      expect(getScoringColor(45)).toBe('average-score-wrapper average-score-red');
    });
  });

  describe('getScoreDifferenceColor', () => {
    it('should return the correct color for score A', () => {
      expect(getScoreDifferenceColor('A')).toBe(`${STATUS_COLORS.GREEN}-box-planning-relieve`);
    });

    it('should return the correct color for score B', () => {
      expect(getScoreDifferenceColor('B')).toBe(`${STATUS_COLORS.GREEN}-box-planning-relieve`);
    });

    it('should return the correct color for score C', () => {
      expect(getScoreDifferenceColor('C')).toBe(`${STATUS_COLORS.ORANGE}-box-planning-relieve`);
    });

    it('should return the correct color for score D', () => {
      expect(getScoreDifferenceColor('D')).toBe(`${STATUS_COLORS.ORANGE}-box-planning-relieve`);
    });

    it('should return the correct color for score F', () => {
      expect(getScoreDifferenceColor('F')).toBe(`${STATUS_COLORS.RED}-box-planning-relieve`);
    });

    it('should return default color for unknown score', () => {
      expect(getScoreDifferenceColor('X')).toBe('onboarding-with-joint-experience');
    });
  });
});
