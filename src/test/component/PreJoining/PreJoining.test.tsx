import React from 'react';
import { render, fireEvent } from '@testing-library/react';
import { createMemoryHistory } from 'history';
import PreJoiningTabModel from '../../../component/PreJoining/PreJoiningModel';
import '@testing-library/jest-dom';


describe('PreJoiningTabModel', () => {
  const mockData = {
    seafarer_status_history: {
      vessel_name: 'Test Vessel',
      vessel_ownership_id: 123,
      recommended_wages: 5000,
      recommended_wages_unit: 'usd',
    },
    seafarer_wages: {
      amount_total: 5500,
      amount_unit: 'USD',
      amount_basic: 3000,
    },
    seafarer_allotment: {
      monthly_allotment: 1000,
      first_allotment: 1500,
    },
    bank_name: 'Test Bank',
    account_type: 'Savings',
    fcnr_months: 12,
    seafarer_id: 456,
  };

  const eventTrackerMock = jest.fn();
  const history = createMemoryHistory();

  it('renders vessel name button and navigates on click', () => {
    const model = PreJoiningTabModel(mockData, false, history, eventTrackerMock);
    const { getByText } = render(model.contract_details[0].value);

    const vesselButton = getByText('Test Vessel');
    fireEvent.click(vesselButton);

    expect(history.location.pathname).toBe('/vessel/ownership/details/123');
  });

  it('displays correct currency unit for wages details', () => {
    const model = PreJoiningTabModel(mockData, false, history, eventTrackerMock);
    const { getByText } = render(<div>{model.wages_details[0].value}</div>);

    expect(getByText((content, element) => content.includes('USD 3000'))).toBeInTheDocument();
  });

  it('displays recommended wages with different styling when total differs', () => {
    const model = PreJoiningTabModel(mockData, false, history, eventTrackerMock);
    const { container } = render(<div>{model.wages_details[2].value}</div>);
    
    expect(container.querySelector('.font-weight-bold.font-red')).toBeTruthy();
  });

  it.skip('handles disabled Set Wages button and tracks event on click', () => {
    const model = PreJoiningTabModel(mockData, true, history, eventTrackerMock);
    const { getByRole } = render(<div>{model.wages_details[3].value}</div>);
    
    const setWagesButton = getByRole('button', { name: 'Set Wages' });
    expect(setWagesButton).toBeDisabled();
  });

  it('renders beneficiary details correctly', () => {
    const model = PreJoiningTabModel(mockData, false, history, eventTrackerMock);
    const { getByText } = render(<div>{model.beneficiary_details[0].value}</div>);

    expect(getByText('Test Bank')).toBeInTheDocument();
  });
  
  it('displays default currency unit if no unit is provided in data', () => {
    const model = PreJoiningTabModel(
      { ...mockData, seafarer_wages: { amount_basic: 2000 } },
      false,
      history,
      eventTrackerMock
    );
    const { getByText } = render(<div>{model.wages_details[0].value}</div>);
    expect(getByText((content, element) => content.includes('USD 2000'))).toBeInTheDocument();
  });
});
