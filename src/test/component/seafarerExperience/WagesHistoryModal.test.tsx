import React from 'react';
import { MemoryRouter, Route } from 'react-router-dom';
import { fireEvent, render, screen, waitFor, within } from '@testing-library/react';
import WagesHistoryModal from '../../../component/seafarerExperience/WagesHistoryModal';
import * as seafarerService from '../../../service/seafarer-service';
import { seafarerGetResponse } from '../../resources/document-response';
import { wagesApiResponseWithHistoryQueryParams } from '../../resources/wages-api-response-with-wages-details';
import { formatAmount } from '@src/util/view-utils';

jest.mock('../../../styleGuide');
jest.mock('../../../service/user-service');

const seafarer = seafarerGetResponse.data;
const history = {
  location: {
    pathname: '',
  },
  goBack: () => {},
};
const seafarerId = 60715;
const seafarerStatusHistoryId = 933992;

const renderWagesHistoryModal = (seafarer, history) => (
  <MemoryRouter
    initialEntries={[
      `/seafarer/details/${seafarerId}/experience/556420/wages-history?seafarer_status_history_id=${seafarerStatusHistoryId}`,
    ]}
  >
    <Route exact path="/seafarer/details/:seafarerId/experience/:experienceId/wages-history">
      <WagesHistoryModal seafarer={seafarer} history={history} />
    </Route>
  </MemoryRouter>
);

beforeAll(async () => {
  jest.spyOn(seafarerService, 'getSefarerWagesHistory').mockImplementation(() =>
    Promise.resolve({
      status: 200,
      data: wagesApiResponseWithHistoryQueryParams,
    }),
  );
});

describe('Wages History Modal test', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('Should Render wages history modal with wages data filled based on response', async () => {
    render(renderWagesHistoryModal(seafarer, history));
    await waitFor(() => {
      const payHeadAmountFields = screen.getAllByRole('spinbutton');
      const expectedWagesData = wagesApiResponseWithHistoryQueryParams.find((i) => i.id === 3239);
      if (expectedWagesData) {
        const amountData = payHeadAmountFields.map((i) => {
          const actualValue = i.value;
          const expectedValue = expectedWagesData.seafarer_wages_details.find(
            (e) => e.payhead_id == Number(i.id),
          )?.amount;
          return { id: i.id, actualValue, expectedValue };
        });
        expect(
          amountData.map((i) => {
            return { id: i.id, amount: i.actualValue };
          }),
        ).toEqual(
          amountData.map((i) => {
            return { id: i.id, amount: i.expectedValue };
          }),
        );
        const totalSalary = screen.getByTestId('wages-history-modal-total-salary');
        expect(totalSalary.textContent).toEqual(formatAmount(expectedWagesData.amount_total, expectedWagesData.amount_unit));
        const basicSalary = screen.getByTestId('wages-history-modal-basic-salary');
        expect(basicSalary.textContent).toEqual(formatAmount(expectedWagesData.amount_basic,expectedWagesData.amount_unit));
      }
    });
  });

  it('Should refresh amount values when effective date is changed from the dropdown', async () => {
    render(renderWagesHistoryModal(seafarer, history));
    const seafarerWagesId = 55;
    await waitFor(() => {
      const dropdown = screen.getByTestId('wages-history-modal-effective-date-dropdown');
      const button = within(dropdown).getByRole('button');
      fireEvent.click(button);
    });
    await waitFor(() => {
      const dropdownItem = screen.getByTestId(`dropdown-item-${seafarerWagesId}`);
      fireEvent.click(dropdownItem);
    });
    await waitFor(() => {
      const payHeadAmountFields = screen.getAllByRole('spinbutton');
      const expectedWagesData = wagesApiResponseWithHistoryQueryParams.find(
        (i) => i.id === seafarerWagesId,
      );
      if (expectedWagesData) {
        const amountData = payHeadAmountFields.map((i) => {
          const actualValue = i.value;
          const expectedValue = expectedWagesData.seafarer_wages_details.find(
            (e) => e.payhead_id == Number(i.id),
          )?.amount;
          return { id: i.id, actualValue, expectedValue };
        });
        expect(
          amountData.map((i) => {
            return { id: i.id, amount: i.actualValue };
          }),
        ).toEqual(
          amountData.map((i) => {
            return { id: i.id, amount: i.expectedValue };
          }),
        );
        const totalSalary = screen.getByTestId('wages-history-modal-total-salary');
        expect(totalSalary.textContent).toEqual(formatAmount(expectedWagesData.amount_total, expectedWagesData.amount_unit));
        const basicSalary = screen.getByTestId('wages-history-modal-basic-salary');
        expect(basicSalary.textContent).toEqual(formatAmount(expectedWagesData.amount_basic, expectedWagesData.amount_unit));
      }
    });
  });
});
