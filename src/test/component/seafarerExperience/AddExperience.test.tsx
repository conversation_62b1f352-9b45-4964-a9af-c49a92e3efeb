import React from 'react';
import '@testing-library/jest-dom';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import AddExperience from '../../../component/seafarerExperience/AddExperience';
import * as vesselService from '../../../service/vessel-service';
import * as seafarerService from '../../../service/seafarer-service';
import {
  vesselTypeAndMiscDropdown,
  vesselListDropdown,
  lookupDropdown,
} from '../../resources/vessel-experience';

jest.mock('../../../styleGuide');
jest.mock('../../../service/user-service');
jest.mock('../../../service/seafarer-service');
jest.mock('../../../service/vessel-service');

const renderAddExperienceModalForCreate = () => <AddExperience />;

const renderEditExperienceModalForCreate = (seafarerId, expEdit) => (
  <AddExperience seafarer_id={seafarerId} expToEdit={expEdit} canEditVesselDetails={false} />
);

describe('Render AddExperience for adding document and check error message', () => {
  beforeAll(async () => {
    jest
      .spyOn(vesselService, 'getVesselList')
      .mockImplementation(() => Promise.resolve({ data: vesselListDropdown }));
    jest.spyOn(seafarerService, 'getSeafarerDropDownData').mockImplementation(() => lookupDropdown);
    jest
      .spyOn(seafarerService, 'getDropDownDataFromVessel')
      .mockImplementation(() => Promise.resolve({ ...vesselTypeAndMiscDropdown }));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should show vessel exprience form inputs', async () => {
    render(renderAddExperienceModalForCreate());
    await waitFor(() => {
      expect(screen.getByText('Company Vessel')).toBeInTheDocument();
      expect(screen.getByText('Yes')).toBeInTheDocument();
      expect(screen.getByText('No')).toBeInTheDocument();
      expect(screen.getByText('Vessel Name*')).toBeInTheDocument();
      expect(screen.getByText('Vessel Type*')).toBeInTheDocument();
      expect(screen.getByText('Owner')).toBeInTheDocument();
      expect(screen.getByText('Engine Type')).toBeInTheDocument();
      expect(screen.getByText('Engine Model')).toBeInTheDocument();
      expect(screen.getByText('Brake Horsepower (BHP)')).toBeInTheDocument();
      expect(screen.getByText('DWT/GRT')).toBeInTheDocument();
      expect(screen.getByText('DWT')).toBeInTheDocument();
      expect(screen.getByText('GRT')).toBeInTheDocument();
      expect(screen.getByText('Rank*')).toBeInTheDocument();
      expect(screen.getByText('Salary')).toBeInTheDocument();
      expect(screen.getByText('Start Date*')).toBeInTheDocument();
      expect(screen.getByText('End Date*')).toBeInTheDocument();
      expect(screen.getByText('Sign Off Reason')).toBeInTheDocument();
    });
  });

  it('should render vessel exprience form and required fields error check', async () => {
    render(renderAddExperienceModalForCreate());

    await waitFor(() => {
      const cancelButton = screen.getByRole('button', {
        name: /add/i,
      });
      fireEvent.click(cancelButton);
    });
    await waitFor(() => {
      expect(screen.getByText('Vessel Name is required')).toBeInTheDocument();
      expect(screen.getByText('Vessel Type is required')).toBeInTheDocument();
      expect(screen.getByText('Rank is required')).toBeInTheDocument();
      expect(screen.getByText('Start Date is required')).toBeInTheDocument();
      expect(screen.getByText('End Date is required')).toBeInTheDocument();
    });
  });

  it('should render vessel experience form for edit', async () => {
    render(
      renderEditExperienceModalForCreate(1, {
        id: 64,
        seafarer_id: 367,
        ownership_id: null,
        rank_id: 114,
        vessel_name: 'Spar Eight',
        vessel_type: 'Oil Tanker',
        deadweight_tonnage: 2,
        deadweight_gross_registered_tonnage: null,
        engine_type: 'SULZER RTA',
        engine_sub_type: null,
        brake_horse_power: 7600,
        start_date: '2022-01-26T00:00:00.000Z',
        end_date: '2023-01-26T00:00:00.000Z',
        owner_name: 'Spar Shipping AS',
        signoff_port_name: null,
        signoff_reason_id: null,
        vessel_ref_id: null,
        created_by_hash: 'Jo** Ba**',
        created_by_p1_email: '<EMAIL>',
        last_updated_by_hash: 'Jo** Ba**',
        deleted_at: null,
        paris1_ref_id: 42,
        salary: 2000,
        is_from_crew_assignment: false,
        created_at: '2001-09-25T05:52:44.000Z',
        updated_at: '2001-09-25T05:52:44.000Z',
        value: 'ENGINE CADET',
        unit: 'ENG. CDT',
        ref_id: 2000347,
        signoff_reason: null,
        rank: {
          id: 114,
          value: 'ENGINE CADET',
          unit: 'ENG. CDT',
          ref_id: 2000347,
        },
      }),
    );

    await waitFor(() => {
      const allTextbox = screen.getAllByRole('textbox');
      const allSpinButtons = screen.getAllByRole('spinbutton');
      const vessenNameInput = allTextbox.find((input) => input.name === 'vessel_name');
      expect(vessenNameInput?.value).toEqual('Spar Eight');

      const brakeHorsePowerInput = allSpinButtons.find(
        (input) => input.name === 'brake_horse_power',
      );
      expect(brakeHorsePowerInput?.value).toEqual('7600');

      const salaryInput = allSpinButtons.find((input) => input.name === 'salary');
      expect(salaryInput?.value).toEqual('2000');

      const startDateInput = allTextbox.find((input) => input.name === 'start_date');
      expect(startDateInput?.value).toEqual('26 Jan 2022');

      const endDateInput = allTextbox.find((input) => input.name === 'end_date');
      expect(endDateInput?.value).toEqual('26 Jan 2023');

      expect(
        screen.getByRole('button', {
          name: /cancel/i,
        }),
      ).toBeInTheDocument();
      expect(
        screen.getByRole('button', {
          name: /save/i,
        }),
      ).toBeInTheDocument();
    });
  });

  it('should render vessel experience form for edit, show disabled feilds', async () => {
    render(
      renderEditExperienceModalForCreate(1, {
        id: 64,
        seafarer_id: 367,
        ownership_id: null,
        rank_id: 114,
        vessel_name: 'Spar Eight',
        vessel_type: 'Oil Tanker',
        deadweight_tonnage: 2,
        deadweight_gross_registered_tonnage: null,
        engine_type: 'SULZER RTA',
        engine_sub_type: null,
        brake_horse_power: 7600,
        start_date: '2022-01-26T00:00:00.000Z',
        end_date: '2023-01-26T00:00:00.000Z',
        owner_name: 'Spar Shipping AS',
        signoff_port_name: null,
        signoff_reason_id: null,
        vessel_ref_id: 1000,
        created_by_hash: 'Jo** Ba**',
        created_by_p1_email: '<EMAIL>',
        last_updated_by_hash: 'Jo** Ba**',
        deleted_at: null,
        paris1_ref_id: 42,
        salary: 2000,
        is_from_crew_assignment: false,
        created_at: '2001-09-25T05:52:44.000Z',
        updated_at: '2001-09-25T05:52:44.000Z',
        value: 'ENGINE CADET',
        unit: 'ENG. CDT',
        ref_id: 2000347,
        signoff_reason: null,
        rank: {
          id: 114,
          value: 'ENGINE CADET',
          unit: 'ENG. CDT',
          ref_id: 2000347,
        },
      }),
    );

    await waitFor(() => {
      const allTextbox = screen.getAllByRole('textbox');
      const allSpinButtons = screen.getAllByRole('spinbutton');
      const allRadioButton = screen.getAllByRole('radio');

      const vesselCheckInput = allRadioButton.find((input) => input.name === 'is_company_vessel');
      expect(vesselCheckInput?.value).toEqual('true');
      expect(vesselCheckInput).toBeDisabled();

      const vesselNameInput = allTextbox.find((input) => input.name === 'owner_name');
      expect(vesselNameInput?.value).toEqual('Spar Shipping AS');
      expect(vesselNameInput).toBeDisabled();

      const brakeHorsePowerInput = allSpinButtons.find(
        (input) => input.name === 'brake_horse_power',
      );
      expect(brakeHorsePowerInput?.value).toEqual('7600');
      expect(brakeHorsePowerInput).toBeDisabled();

      const salaryInput = allSpinButtons.find((input) => input.name === 'salary');
      expect(salaryInput?.value).toEqual('2000');

      const startDateInput = allTextbox.find((input) => input.name === 'start_date');
      expect(startDateInput?.value).toEqual('26 Jan 2022');

      const endDateInput = allTextbox.find((input) => input.name === 'end_date');
      expect(endDateInput?.value).toEqual('26 Jan 2023');

      expect(
        screen.getByRole('button', {
          name: /cancel/i,
        }),
      ).toBeInTheDocument();
      expect(
        screen.getByRole('button', {
          name: /save/i,
        }),
      ).toBeInTheDocument();
    });
  });
});
