import React from 'react';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import ScreeningModalView from '../../../component/screening/ScreeningModalView';
import { updateWrapper } from '../../../setupTests';
import screeningApprovalService from '../../../service/screening-service';
import { mockUploadCaseReportImages } from '../../../controller/image-upload-controller';

jest.mock('../../../styleGuide');
jest.mock('../../../service/user-service');
jest.mock('../../../controller/image-upload-controller');

const seafarerId = 1;
const approvalId = 1;
let mockApprovalUpdateFn;

const renderModal = (actionType: string) => (
  <ScreeningModalView
    show
    closeScreeningModal={jest.fn()}
    seafarerId={seafarerId}
    approvalId={approvalId}
    setLoadingFor={jest.fn()}
    renderTable={jest.fn()}
    actionType={actionType}
  />
);

function clickOnModelActionButton(screen, actionName) {
  const modelButtons = screen.getByRole('button', {
    name: actionName,
  });
  fireEvent.click(modelButtons);
}

describe('<ScreeningModalView />', () => {
  beforeAll(async () => {
    mockApprovalUpdateFn = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ status: 200, data: 1 }));
    screeningApprovalService.updateScreeningData = mockApprovalUpdateFn;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it.skip('should call api for screening approval update with approved status, on click of confirm button', async () => {
    const renderedView = renderModal('approve');

    clickOnModelActionButton(renderedView, 'Confirm');
    await updateWrapper(renderedView);
    expect(mockApprovalUpdateFn).toHaveBeenCalledWith(1, 'approved', null, 1);
    expect(mockUploadCaseReportImages).toHaveBeenCalledTimes(1);
  });

  it('should call api for screening reject update with rejected status, on click of confirm button', async () => {
    render(renderModal('reject'));
    const remarks = screen.getByRole('textbox');
    fireEvent.change(remarks, { target: { value: 'Rejected' } });
    clickOnModelActionButton(screen, 'Confirm');

    await waitFor(() => {
      expect(mockApprovalUpdateFn).toHaveBeenCalledWith(1, 'rejected', 'Rejected', 1);
      expect(mockUploadCaseReportImages).toHaveBeenCalledTimes(0);
    });
  });

  it('should call api for screening rework with pending status, on click of confirm button', async () => {
    render(renderModal('rework'));
    clickOnModelActionButton(screen, 'Confirm');

    await waitFor(() => {
      expect(mockApprovalUpdateFn).toHaveBeenCalledWith(1, 'pending', null, 1);
      expect(mockUploadCaseReportImages).toHaveBeenCalledTimes(0);
    });
  });

  it.skip('should call api for screening forward with forwarded status, on click of confirm button', async () => {
    const renderedView = renderModal('forward');
    clickOnModelActionButton(renderedView, 'Confirm');
    await updateWrapper(renderedView);

    expect(mockApprovalUpdateFn).toHaveBeenCalledWith(1, 'forwarded', null, 1);
    expect(mockUploadCaseReportImages).toHaveBeenCalledTimes(1);
  });

  it('should call api for reapply with reapplied status, on click of confirm button', async () => {
    render(renderModal('reapply'));
    clickOnModelActionButton(screen, 'Confirm');

    await waitFor(() => {
      expect(mockApprovalUpdateFn).toHaveBeenCalledWith(1, 'reapplied', null, 1);
      expect(mockUploadCaseReportImages).toHaveBeenCalledTimes(0);
    });
  });
});
