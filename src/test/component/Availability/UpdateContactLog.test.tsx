import React from 'react';
import _ from 'lodash';
import '@testing-library/jest-dom';
import moment from 'moment-timezone';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import UpdateContactLog from '../../../component/Availability/UpdateContactLog';
import seafarerService from '../../../service/seafarer-service';
import { getContactLogResponse } from '../../resources/contact-log-response';

jest.mock('../../../styleGuide');
jest.mock('../../../service/user-service');
const seafarerIdWithContactLog = 37025;
const seafarerIdWithoutContactLog = 38161;

const renderUpdateContactLogModalWithData = (contactLog, seafarerId) => (
  <UpdateContactLog
    seafarerId={seafarerId}
    contactsLog={contactLog}
    cancelButtonHandler={() => {}}
  />
);

const renderUpdateContactLogModalWithoutData = (seafarerId) => (
  <UpdateContactLog seafarerId={seafarerId} contactsLog={[]} cancelButtonHandler={() => {}} />
);

beforeAll(async () => {
  seafarerService.getContactLog = jest.fn().mockImplementation(() =>
    Promise.resolve({
      status: 200,
      data: getContactLogResponse,
    }),
  );
});

describe('Render UpdateContactLog for adding document and check error message', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should show modal window with passing contact log', async () => {
    const { data } = await seafarerService.getContactLog();
    const contactLogs = _.orderBy(
      data,
      [(contactLogObj) => new Date(contactLogObj.created_at)],
      ['desc'],
    );

    render(renderUpdateContactLogModalWithData(contactLogs, seafarerIdWithContactLog));

    expect(screen.getByText('Availability Date')).toBeInTheDocument();
    expect(screen.getByText('Documents in hand')).toBeInTheDocument();
    expect(screen.getByText('Yes, documents in hand')).toBeInTheDocument();
    expect(screen.getByText('Availability Remark')).toBeInTheDocument();
    expect(screen.getByText('General Remark')).toBeInTheDocument();
    expect(screen.getByText('Contact Date*')).toBeInTheDocument();
    expect(screen.getByText('Contact Mode')).toBeInTheDocument();
    expect(screen.getByText('Next Contact Date')).toBeInTheDocument();
    const expectedValuesObj = contactLogs.find((contactslog) => contactslog?.is_latest);

    const allTextbox = screen.getAllByRole('textbox');
    const dateFormat = 'DD MMM YYYY';
    const valuesObj = {
      availability_date: moment
        .utc((allTextbox.find((e) => e.name === 'availability_date') as HTMLInputElement).value, dateFormat)
        .toISOString(),
      contact_date: moment
        .utc((allTextbox.find((e) => e.name === 'contact_date') as HTMLInputElement).value, dateFormat)
        .toISOString(),
      next_contact_date: moment
        .utc((allTextbox.find((e) => e.name === 'next_contact_date') as HTMLInputElement).value, dateFormat)
        .toISOString(),
      contact_mode: screen.getByTestId('contact-mode').value,
      availability_remarks: (
        allTextbox.find((e) => e.name === 'availability_remarks') as HTMLInputElement
      ).value,
      general_remarks: (allTextbox.find((e) => e.name === 'general_remarks') as HTMLInputElement)
        .value,
    };
    expect(expectedValuesObj).toMatchObject(valuesObj);
  });

  it('should show modal window without passing contact log', async () => {
    render(renderUpdateContactLogModalWithoutData(seafarerIdWithoutContactLog));

    expect(screen.getByText('Availability Date')).toBeInTheDocument();
    expect(screen.getByText('Documents in hand')).toBeInTheDocument();
    expect(screen.getByText('Yes, documents in hand')).toBeInTheDocument();
    expect(screen.getByText('Availability Remark')).toBeInTheDocument();
    expect(screen.getByText('General Remark')).toBeInTheDocument();
    expect(screen.getByText('Contact Date*')).toBeInTheDocument();
    expect(screen.getByText('Contact Mode')).toBeInTheDocument();
    expect(screen.getByText('Next Contact Date')).toBeInTheDocument();

    const allTextbox = screen.getAllByRole('textbox');

    const valuesObj = {
      availability_date: moment
        .utc((allTextbox.find((e) => e.name === 'availability_date') as HTMLInputElement).value)
        .toISOString(),
      contact_date: moment
        .utc((allTextbox.find((e) => e.name === 'contact_date') as HTMLInputElement).value)
        .toISOString(),
      next_contact_date: moment
        .utc((allTextbox.find((e) => e.name === 'next_contact_date') as HTMLInputElement).value)
        .toISOString(),
      availability_remarks: (
        allTextbox.find((e) => e.name === 'availability_remarks') as HTMLInputElement
      ).value,
      general_remarks: (allTextbox.find((e) => e.name === 'general_remarks') as HTMLInputElement)
        .value,
    };

    const expectedValuesObj = {
      availability_date: null,
      contact_date: null,
      next_contact_date: null,
      availability_remarks: '',
      general_remarks: '',
    };

    expect(valuesObj).toMatchObject(expectedValuesObj);
  });

  it('should have availability date later than contact date', async () => {
    const { data } = await seafarerService.getContactLog();
    const contactLogs = _.orderBy(
      data,
      [(contactLogObj) => new Date(contactLogObj.created_at)],
      ['desc'],
    );

    render(renderUpdateContactLogModalWithData(contactLogs, seafarerIdWithContactLog));

    const allTextbox = screen.getAllByRole('textbox');
    const availabilityInput = allTextbox.find((input) => input.name === 'availability_date');
    const contactDateInput = allTextbox.find((input) => input.name === 'contact_date');

    let availability_date = availabilityInput?.value;
    let contact_date = contactDateInput?.value;

    let flag = false;
    availability_date = moment(availability_date, 'DD MMM YYYY');
    contact_date = moment(contact_date, 'DD MMM YYYY');

    if (availability_date.isAfter(contact_date)) flag = true;

    expect(flag).toBeTruthy();
  });

  it('should render UpdateContactLog Modal and required fields error check', async () => {
    render(renderUpdateContactLogModalWithoutData(seafarerIdWithoutContactLog));

    const allTextbox = screen.getAllByRole('textbox');
    const availabilityInput = allTextbox.find((input) => input.name === 'availability_remarks');
    fireEvent.change(availabilityInput, { target: { value: 'sometext' } });

    waitFor(() => {
      const submitBtn = screen.getByRole('button', { name: /save/i });
      fireEvent.click(submitBtn);
    });
    waitFor(() => {
      expect('Contact Date is required').toBeInTheDocument();
    });
  });
});
