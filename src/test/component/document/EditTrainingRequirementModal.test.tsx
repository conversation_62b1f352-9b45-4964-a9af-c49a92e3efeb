import React from 'react';
import { render, fireEvent, screen, act, waitFor } from '@testing-library/react';
import * as COMMON_VALIDATION_MSG from '../../../constants/common-validation-messages';
import EditTrainingRequirementModal from '../../../component/document/EditTrainingRequirementModal';
import seafarerService from '../../../service/seafarer-service';
import '@testing-library/jest-dom';
import { cloneDeep } from 'lodash';

jest.mock('../../../styleGuide');
jest.mock('../../../service/user-service');
jest.mock('react-router', () => ({
  useParams: () => ({
    seafarerId: 5,
    trainingReqId: 63,
  }),
}));
const mockHistory = {
  push: jest.fn(),
  location: {
    pathname: '/seafarer/details/:seafarerId/appraisals/training-req/:trainingReqId/edit',
  },
};

afterEach(() => {
  jest.clearAllMocks();
});

const roleConfig = {
  seafarer: {
    edit: {
      training: true,
      suptTraining: true,
    },
  },
};

const trainingDetails = {
  status: 200,
  data: {
    id: 63,
    seafarer_id: 5,
    recommended_date: '2023-06-25T00:00:00.000Z',
    vessel_ownership_id: 1705,
    vessel_name: 'Acer Arrow (MMPL)',
    deadline_date: '2024-07-25T00:00:00.000Z',
    completed_date: '2023-07-28T00:00:00.000Z',
    completed_date_created_at: null,
    training_needs: '5',
    training_imparted: '5',
    ref_id: null,
    supt_appraisal_id: null,
    master_appraisal_id: null,
    created_by_hash: 'm4dN8stUtPFLN2ho3SkPvKZIGjcojmc7HUNHW4Uw4LM=',
    updated_by_hash: 'm4dN8stUtPFLN2ho3SkPvKZIGjcojmc7HUNHW4Uw4LM=',
    created_at: '2023-07-25T09:27:38.181Z',
    updated_at: '2023-07-30T04:24:17.910Z',
    seafarer_training_requirement_document: [
      {
        id: 42,
        seafarer_document_id: 3612840,
        seafarer_training_requirement_id: 63,
        created_at: '2023-07-28T12:21:52.822Z',
        updated_at: '2023-07-28T12:21:52.822Z',
        seafarer_document: {
          id: 3612840,
          seafarer_person_id: 5,
          type: 'training_requirement_supporting_document',
          ref_id: null,
          doc_path: '/ui2023/seafarer/202307260528407_dummy.pdf',
          is_deleted: true,
          created_at: '2023-07-28T12:21:52.800Z',
          updated_at: '2023-07-30T04:24:17.879Z',
          created_by_hash: 'm4dN8stUtPFLN2ho3SkPvKZIGjcojmc7HUNHW4Uw4LM=',
          updated_by_hash: 'm4dN8stUtPFLN2ho3SkPvKZIGjcojmc7HUNHW4Uw4LM=',
        },
      },
      {
        id: 43,
        seafarer_document_id: 3612841,
        seafarer_training_requirement_id: 63,
        created_at: '2023-07-28T12:21:52.822Z',
        updated_at: '2023-07-28T12:21:52.822Z',
        seafarer_document: {
          id: 3612841,
          seafarer_person_id: 5,
          type: 'training_requirement_superintendent_report',
          ref_id: null,
          doc_path: '/ui2023/seafarer/202307260528408_dummy.pdf',
          is_deleted: true,
          created_at: '2023-07-28T12:21:52.800Z',
          updated_at: '2023-07-30T04:24:17.879Z',
          created_by_hash: 'm4dN8stUtPFLN2ho3SkPvKZIGjcojmc7HUNHW4Uw4LM=',
          updated_by_hash: 'm4dN8stUtPFLN2ho3SkPvKZIGjcojmc7HUNHW4Uw4LM=',
        },
      },
      {
        id: 44,
        seafarer_document_id: 3612842,
        seafarer_training_requirement_id: 63,
        created_at: '2023-07-29T06:48:22.884Z',
        updated_at: '2023-07-29T06:48:22.884Z',
        seafarer_document: {
          id: 3612842,
          seafarer_person_id: 5,
          type: 'training_requirement_supporting_document',
          ref_id: null,
          doc_path: '/ui2023/seafarer/202307260528407_dummy.pdf',
          is_deleted: false,
          created_at: '2023-07-29T06:48:22.866Z',
          updated_at: '2023-07-29T06:48:22.866Z',
          created_by_hash: 'm4dN8stUtPFLN2ho3SkPvKZIGjcojmc7HUNHW4Uw4LM=',
          updated_by_hash: 'm4dN8stUtPFLN2ho3SkPvKZIGjcojmc7HUNHW4Uw4LM=',
        },
      },
      {
        id: 45,
        seafarer_document_id: 3612843,
        seafarer_training_requirement_id: 63,
        created_at: '2023-07-29T06:48:22.884Z',
        updated_at: '2023-07-29T06:48:22.884Z',
        seafarer_document: {
          id: 3612843,
          seafarer_person_id: 5,
          type: 'training_requirement_superintendent_report',
          ref_id: null,
          doc_path: '/ui2023/seafarer/202307260528408_dummy.pdf',
          is_deleted: false,
          created_at: '2023-07-29T06:48:22.866Z',
          updated_at: '2023-07-29T06:48:22.866Z',
          created_by_hash: 'm4dN8stUtPFLN2ho3SkPvKZIGjcojmc7HUNHW4Uw4LM=',
          updated_by_hash: 'm4dN8stUtPFLN2ho3SkPvKZIGjcojmc7HUNHW4Uw4LM=',
        },
      },
      {
        id: 46,
        seafarer_document_id: 3612844,
        seafarer_training_requirement_id: 63,
        created_at: '2023-07-29T06:50:38.842Z',
        updated_at: '2023-07-29T06:50:38.842Z',
        seafarer_document: {
          id: 3612844,
          seafarer_person_id: 5,
          type: 'training_requirement_supporting_document',
          ref_id: null,
          doc_path: '/ui2023/seafarer/202307260528407_dummy.pdf',
          is_deleted: false,
          created_at: '2023-07-29T06:50:38.831Z',
          updated_at: '2023-07-29T06:50:38.831Z',
          created_by_hash: 'm4dN8stUtPFLN2ho3SkPvKZIGjcojmc7HUNHW4Uw4LM=',
          updated_by_hash: 'm4dN8stUtPFLN2ho3SkPvKZIGjcojmc7HUNHW4Uw4LM=',
        },
      },
      {
        id: 47,
        seafarer_document_id: 3612845,
        seafarer_training_requirement_id: 63,
        created_at: '2023-07-29T06:50:38.842Z',
        updated_at: '2023-07-29T06:50:38.842Z',
        seafarer_document: {
          id: 3612845,
          seafarer_person_id: 5,
          type: 'training_requirement_superintendent_report',
          ref_id: null,
          doc_path: '/ui2023/seafarer/202307260528408_dummy.pdf',
          is_deleted: false,
          created_at: '2023-07-29T06:50:38.831Z',
          updated_at: '2023-07-29T06:50:38.831Z',
          created_by_hash: 'm4dN8stUtPFLN2ho3SkPvKZIGjcojmc7HUNHW4Uw4LM=',
          updated_by_hash: 'm4dN8stUtPFLN2ho3SkPvKZIGjcojmc7HUNHW4Uw4LM=',
        },
      },
      {
        id: 48,
        seafarer_document_id: 3612846,
        seafarer_training_requirement_id: 63,
        created_at: '2023-07-29T08:44:46.289Z',
        updated_at: '2023-07-29T08:44:46.289Z',
        seafarer_document: {
          id: 3612846,
          seafarer_person_id: 5,
          type: 'training_requirement_superintendent_report',
          ref_id: null,
          doc_path: '/ui2023/seafarer/202307290844385_download_(7).pdf',
          is_deleted: false,
          created_at: '2023-07-29T08:44:46.260Z',
          updated_at: '2023-07-29T08:44:46.260Z',
          created_by_hash: 'm4dN8stUtPFLN2ho3SkPvKZIGjcojmc7HUNHW4Uw4LM=',
          updated_by_hash: 'm4dN8stUtPFLN2ho3SkPvKZIGjcojmc7HUNHW4Uw4LM=',
        },
      },
      {
        id: 49,
        seafarer_document_id: 3612847,
        seafarer_training_requirement_id: 63,
        created_at: '2023-07-29T08:44:46.289Z',
        updated_at: '2023-07-29T08:44:46.289Z',
        seafarer_document: {
          id: 3612847,
          seafarer_person_id: 5,
          type: 'training_requirement_supporting_document',
          ref_id: null,
          doc_path: '/ui2023/seafarer/202307290844415_download_(5).pdf',
          is_deleted: false,
          created_at: '2023-07-29T08:44:46.260Z',
          updated_at: '2023-07-29T08:44:46.260Z',
          created_by_hash: 'm4dN8stUtPFLN2ho3SkPvKZIGjcojmc7HUNHW4Uw4LM=',
          updated_by_hash: 'm4dN8stUtPFLN2ho3SkPvKZIGjcojmc7HUNHW4Uw4LM=',
        },
      },
      {
        id: 50,
        seafarer_document_id: 3612848,
        seafarer_training_requirement_id: 63,
        created_at: '2023-07-29T08:54:56.299Z',
        updated_at: '2023-07-29T08:54:56.299Z',
        seafarer_document: {
          id: 3612848,
          seafarer_person_id: 5,
          type: 'training_requirement_superintendent_report',
          ref_id: null,
          doc_path: '/ui2023/seafarer/202307290854494_download_(26).pdf',
          is_deleted: false,
          created_at: '2023-07-29T08:54:56.263Z',
          updated_at: '2023-07-29T08:54:56.263Z',
          created_by_hash: 'm4dN8stUtPFLN2ho3SkPvKZIGjcojmc7HUNHW4Uw4LM=',
          updated_by_hash: 'm4dN8stUtPFLN2ho3SkPvKZIGjcojmc7HUNHW4Uw4LM=',
        },
      },
      {
        id: 51,
        seafarer_document_id: 3612849,
        seafarer_training_requirement_id: 63,
        created_at: '2023-07-29T09:03:33.662Z',
        updated_at: '2023-07-29T09:03:33.662Z',
        seafarer_document: {
          id: 3612849,
          seafarer_person_id: 5,
          type: 'training_requirement_superintendent_report',
          ref_id: null,
          doc_path: '/ui2023/seafarer/202307290903276_download_(5).pdf',
          is_deleted: false,
          created_at: '2023-07-29T09:03:33.638Z',
          updated_at: '2023-07-29T09:03:33.638Z',
          created_by_hash: 'm4dN8stUtPFLN2ho3SkPvKZIGjcojmc7HUNHW4Uw4LM=',
          updated_by_hash: 'm4dN8stUtPFLN2ho3SkPvKZIGjcojmc7HUNHW4Uw4LM=',
        },
      },
      {
        id: 66,
        seafarer_document_id: 3612864,
        seafarer_training_requirement_id: 63,
        created_at: '2023-07-30T04:24:17.890Z',
        updated_at: '2023-07-30T04:24:17.890Z',
        seafarer_document: {
          id: 3612864,
          seafarer_person_id: 5,
          type: 'training_requirement_supporting_document',
          ref_id: null,
          doc_path: '/ui2023/seafarer/202307300423449_download_(6).pdf',
          is_deleted: false,
          created_at: '2023-07-30T04:24:17.867Z',
          updated_at: '2023-07-30T04:24:17.867Z',
          created_by_hash: 'm4dN8stUtPFLN2ho3SkPvKZIGjcojmc7HUNHW4Uw4LM=',
          updated_by_hash: 'm4dN8stUtPFLN2ho3SkPvKZIGjcojmc7HUNHW4Uw4LM=',
        },
      },
      {
        id: 67,
        seafarer_document_id: 3612865,
        seafarer_training_requirement_id: 63,
        created_at: '2023-07-30T04:24:17.890Z',
        updated_at: '2023-07-30T04:24:17.890Z',
        seafarer_document: {
          id: 3612865,
          seafarer_person_id: 5,
          type: 'training_requirement_superintendent_report',
          ref_id: null,
          doc_path: '/ui2023/seafarer/202307300423449_download_(4).pdf',
          is_deleted: false,
          created_at: '2023-07-30T04:24:17.867Z',
          updated_at: '2023-07-30T04:24:17.867Z',
          created_by_hash: 'm4dN8stUtPFLN2ho3SkPvKZIGjcojmc7HUNHW4Uw4LM=',
          updated_by_hash: 'm4dN8stUtPFLN2ho3SkPvKZIGjcojmc7HUNHW4Uw4LM=',
        },
      },
    ],
  },
};

const trainingDetailsWithCompletedCreatedAt = {
  status: 200,
  data: {
    id: 63,
    seafarer_id: 5,
    recommended_date: '2023-06-25T00:00:00.000Z',
    vessel_ownership_id: 1705,
    vessel_name: 'Acer Arrow (MMPL)',
    deadline_date: '2024-07-25T00:00:00.000Z',
    completed_date: '2023-07-28T00:00:00.000Z',
    completed_date_created_at: '2023-07-28T00:00:00.000Z',
    training_needs: '5',
    training_imparted: '5',
    ref_id: null,
    supt_appraisal_id: null,
    master_appraisal_id: null,
    created_by_hash: 'm4dN8stUtPFLN2ho3SkPvKZIGjcojmc7HUNHW4Uw4LM=',
    updated_by_hash: 'm4dN8stUtPFLN2ho3SkPvKZIGjcojmc7HUNHW4Uw4LM=',
    created_at: '2023-07-25T09:27:38.181Z',
    updated_at: '2023-07-30T04:24:17.910Z',
    seafarer_training_requirement_document: [],
  },
};

const trainingWithCompletedCreatedAtBefore15Days = {
  status: 200,
  data: {
    id: 63,
    seafarer_id: 5,
    recommended_date: '2023-06-25T00:00:00.000Z',
    vessel_ownership_id: 1705,
    vessel_name: 'Acer Arrow (MMPL)',
    deadline_date: '2024-07-25T00:00:00.000Z',
    completed_date: '2023-07-28T00:00:00.000Z',
    completed_date_created_at: new Date().toISOString(),
    training_needs: '5',
    training_imparted: '5',
    ref_id: null,
    supt_appraisal_id: null,
    master_appraisal_id: null,
    created_by_hash: 'm4dN8stUtPFLN2ho3SkPvKZIGjcojmc7HUNHW4Uw4LM=',
    updated_by_hash: 'm4dN8stUtPFLN2ho3SkPvKZIGjcojmc7HUNHW4Uw4LM=',
    created_at: '2023-07-25T09:27:38.181Z',
    updated_at: '2023-07-30T04:24:17.910Z',
    seafarer_training_requirement_document: [],
  },
};

jest.mock('../../../service/vessel-service', () => ({
  getVesselV2Dropdown: jest.fn(() =>
    Promise.resolve({ data: { results: [{ id: 1, name: 'test' }] } }),
  ),
}));
jest.mock('../../../service/seafarer-service', () => ({
  getTrainingReqDetailsByTrainingReqId: jest.fn(() =>
    Promise.resolve({
      status: 200,
      data: {
        id: 63,
        seafarer_id: 5,
        recommended_date: '2023-06-25T00:00:00.000Z',
        vessel_ownership_id: 1705,
        vessel_name: 'Acer Arrow (MMPL)',
        deadline_date: '2024-07-25T00:00:00.000Z',
        completed_date: '2023-07-28T00:00:00.000Z',
        training_needs: '5',
        training_imparted: '5',
        ref_id: null,
        supt_appraisal_id: null,
        master_appraisal_id: null,
        created_by_hash: 'm4dN8stUtPFLN2ho3SkPvKZIGjcojmc7HUNHW4Uw4LM=',
        updated_by_hash: 'm4dN8stUtPFLN2ho3SkPvKZIGjcojmc7HUNHW4Uw4LM=',
        created_at: '2023-07-25T09:27:38.181Z',
        updated_at: '2023-07-30T04:24:17.910Z',
        seafarer_training_requirement_document: [
          {
            id: 42,
            seafarer_document_id: 3612840,
            seafarer_training_requirement_id: 63,
            created_at: '2023-07-28T12:21:52.822Z',
            updated_at: '2023-07-28T12:21:52.822Z',
            seafarer_document: {
              id: 3612840,
              seafarer_person_id: 5,
              type: 'training_requirement_supporting_document',
              ref_id: null,
              doc_path: '/ui2023/seafarer/202307260528407_dummy.pdf',
              is_deleted: false,
              created_at: '2023-07-28T12:21:52.800Z',
              updated_at: '2023-07-30T04:24:17.879Z',
              created_by_hash: 'm4dN8stUtPFLN2ho3SkPvKZIGjcojmc7HUNHW4Uw4LM=',
              updated_by_hash: 'm4dN8stUtPFLN2ho3SkPvKZIGjcojmc7HUNHW4Uw4LM=',
            },
          },
          {
            id: 43,
            seafarer_document_id: 3612841,
            seafarer_training_requirement_id: 63,
            created_at: '2023-07-28T12:21:52.822Z',
            updated_at: '2023-07-28T12:21:52.822Z',
            seafarer_document: {
              id: 3612841,
              seafarer_person_id: 5,
              type: 'training_requirement_superintendent_report',
              ref_id: null,
              doc_path: '/ui2023/seafarer/202307260528408_dummy.pdf',
              is_deleted: false,
              created_at: '2023-07-28T12:21:52.800Z',
              updated_at: '2023-07-30T04:24:17.879Z',
              created_by_hash: 'm4dN8stUtPFLN2ho3SkPvKZIGjcojmc7HUNHW4Uw4LM=',
              updated_by_hash: 'm4dN8stUtPFLN2ho3SkPvKZIGjcojmc7HUNHW4Uw4LM=',
            },
          },
        ],
      },
    }),
  ),
  editTrainingRequirement: jest.fn(() => Promise.resolve({ status: 200, data: {} })),
}));

describe('EditTrainingRequirementModal', () => {
  const renderTrainingReqModal = (userRoleConfig) => {
    const editTrainingReqModal = render(
      <EditTrainingRequirementModal
        history={mockHistory}
        callTrainingRequirement={() => {}}
        seafarerPersonId={trainingDetails.data.seafarer_id}
        roleConfig={userRoleConfig}
        isInvestigationTrainingReq={false}
      />,
    );
    return editTrainingReqModal;
  };

  test('training requirement modal opens', async () => {
    const { getByText } = renderTrainingReqModal(roleConfig);
    await waitFor(() => {
      expect(getByText(/Edit Training Requirement/i)).toBeInTheDocument();
    });
  });

  test('render edit form fields correctly', async () => {
    const { getByText } = renderTrainingReqModal(roleConfig);
    await waitFor(() => {
      expect(getByText(/Date Training Recommended*/i)).toBeInTheDocument();
      expect(getByText(/Deadline*/i)).toBeInTheDocument();
      expect(getByText(/Vessel*/i)).toBeInTheDocument();
      expect(getByText(/Date Completed/i)).toBeInTheDocument();
      expect(getByText(/Training Needs*/i)).toBeInTheDocument();
      expect(getByText(/Training Imparted*/i)).toBeInTheDocument();
      expect(getByText(/Supporting Document/i)).toBeInTheDocument();
      expect(getByText(/Superintendent Report/i)).toBeInTheDocument();
    });
  });

  test('should show validation error if mandatory fields are empty', async () => {
    let trainingData = cloneDeep(trainingDetails);
    trainingData.data.training_needs = '';
    trainingData.data.training_imparted = '';
    seafarerService.getTrainingReqDetailsByTrainingReqId = jest
      .fn()
      .mockImplementation(() => Promise.resolve(trainingData));

    const { getByText } = renderTrainingReqModal(roleConfig);

    await waitFor(async () => {
      const btn = screen.getByText('Save');
      await act(async () => {
        fireEvent.click(btn);
      });
    });
    await waitFor(async () => {
      expect(getByText(COMMON_VALIDATION_MSG.TRAINING_NEED)).toBeInTheDocument();
      expect(getByText(COMMON_VALIDATION_MSG.TRAINING_IMPARTED)).toBeInTheDocument();
    });
  });

  test('should not show validation error if mandatory fields are filled', async () => {
    const { getByText, getByTitle } = renderTrainingReqModal(roleConfig);
    await waitFor(async () => {
      const btn = getByText('Save');
      const trainingNeeds = getByTitle('training_needs');
      const trainingImparted = getByTitle('training_imparted');
      await act(async () => {
        fireEvent.change(trainingNeeds, { target: { value: 'testing' } });
        fireEvent.change(trainingImparted, { target: { value: 'testing' } });
        fireEvent.click(btn);
      });
    });
    await waitFor(() => {
      expect(() => getByText(COMMON_VALIDATION_MSG.TRAINING_NEED)).toThrow();
      expect(() => getByText(COMMON_VALIDATION_MSG.TRAINING_IMPARTED)).toThrow();
    });
  });

  test('If user has edit training req role access then certian fields - recommended date, deadline date, vessel dropdown, training needs should be enabled', async () => {
    let userRoleConfig = cloneDeep(roleConfig);
    userRoleConfig.seafarer.edit.suptTraining = false;
    const { getByTitle, getByTestId } = renderTrainingReqModal(userRoleConfig);
    await waitFor(async () => {
      const vesselDropdown = getByTestId('vessel_ownership_id');
      const recommendedDateBtn = getByTestId('training-req-date-of-issue');
      const deadlineDateBtn = getByTestId('training-req-deadline');
      const trainingNeeds = getByTitle('training_needs');

      expect(vesselDropdown).toBeEnabled();
      expect(recommendedDateBtn).toBeEnabled();
      expect(deadlineDateBtn).toBeEnabled();
      expect(trainingNeeds).toBeEnabled();
    });
  });

  test('If supt. id is present then certian fields - recommended date, deadline date, vessel dropdown, training needs should be disabled', async () => {
    let trainingData = cloneDeep(trainingDetails);
    trainingData.data.supt_appraisal_id = 11;
    seafarerService.getTrainingReqDetailsByTrainingReqId = jest
      .fn()
      .mockImplementation(() => Promise.resolve(trainingData));

    const { getByTitle, getByTestId } = renderTrainingReqModal(roleConfig);

    await waitFor(async () => {
      const vesselDropdown = getByTestId('vessel_ownership_id');
      const recommendedDateBtn = getByTestId('training-req-date-of-issue');
      const deadlineDateBtn = getByTestId('training-req-deadline');
      const trainingNeeds = getByTitle('training_needs');

      expect(vesselDropdown).toBeDisabled();
      expect(recommendedDateBtn).toBeDisabled();
      expect(deadlineDateBtn).toBeDisabled();
      expect(trainingNeeds).toBeDisabled();
    });
  });

  test('If master appraisal id is present then certian fields - recommended date, deadline date, vessel dropdown, training needs should be disabled', async () => {
    let trainingData = cloneDeep(trainingDetails);
    trainingData.data.master_appraisal_id = 11;
    seafarerService.getTrainingReqDetailsByTrainingReqId = jest
      .fn()
      .mockImplementation(() => Promise.resolve(trainingData));

    const { getByTitle, getByTestId } = renderTrainingReqModal(roleConfig);

    await waitFor(async () => {
      const vesselDropdown = getByTestId('vessel_ownership_id');
      const recommendedDateBtn = getByTestId('training-req-date-of-issue');
      const deadlineDateBtn = getByTestId('training-req-deadline');
      const trainingNeeds = getByTitle('training_needs');

      expect(vesselDropdown).toBeDisabled();
      expect(recommendedDateBtn).toBeDisabled();
      expect(deadlineDateBtn).toBeDisabled();
      expect(trainingNeeds).toBeDisabled();
    });
  });

  test('If user has only edit supt training req role access then certian fields - recommended date, deadline date, vessel dropdown, training needs should be disabled', async () => {
    let userRoleConfig = cloneDeep(roleConfig);
    userRoleConfig.seafarer.edit.training = false;
    const { getByTitle, getByTestId } = renderTrainingReqModal(userRoleConfig);
    await waitFor(async () => {
      const vesselDropdown = getByTestId('vessel_ownership_id');
      const recommendedDateBtn = getByTestId('training-req-date-of-issue');
      const deadlineDateBtn = getByTestId('training-req-deadline');
      const trainingNeeds = getByTitle('training_needs');

      expect(vesselDropdown).toBeDisabled();
      expect(recommendedDateBtn).toBeDisabled();
      expect(deadlineDateBtn).toBeDisabled();
      expect(trainingNeeds).toBeDisabled();
    });
  });

  test('Save button should be disable 15days after the Training End date was updated', async () => {
    let trainingData = cloneDeep(trainingDetailsWithCompletedCreatedAt);
    seafarerService.getTrainingReqDetailsByTrainingReqId = jest
      .fn()
      .mockImplementation(() => Promise.resolve(trainingData));

    renderTrainingReqModal(roleConfig);

    await waitFor(async () => {
      const saveButton = screen.getByText('Save');
      expect(saveButton).toBeDisabled();
    });
  });
  test('Save button should be enabled 15days within the Training End date was updated', async () => {
    let trainingData = cloneDeep(trainingWithCompletedCreatedAtBefore15Days);
    seafarerService.getTrainingReqDetailsByTrainingReqId = jest
      .fn()
      .mockImplementation(() => Promise.resolve(trainingData));

    renderTrainingReqModal(roleConfig);

    await waitFor(async () => {
      const saveButton = screen.getByText('Save');
      expect(saveButton).not.toBeDisabled();
    });
  });
  test('Save button should be enable before update training end date', async () => {
    let trainingData = cloneDeep(trainingDetails);
    seafarerService.getTrainingReqDetailsByTrainingReqId = jest
      .fn()
      .mockImplementation(() => Promise.resolve(trainingData));

    renderTrainingReqModal(roleConfig);

    await waitFor(async () => {
        const saveButton = screen.getByText('Save');
        expect(saveButton).not.toBeDisabled();
    });
  });
});
