import React from 'react';
import { render, fireEvent, screen, act, waitFor } from '@testing-library/react';
import AddTrainingRequirementModal from '../../../component/document/AddTrainingRequirementModal';
import * as COMMON_VALIDATION_MSG from '../../../constants/common-validation-messages';

import '@testing-library/jest-dom';

jest.mock('../../../styleGuide');
jest.mock('../../../service/user-service');
jest.mock('react-router', () => ({
  useParams: () => ({
    seafarerId: 123,
  }),
}));
jest.mock('../../../service/vessel-service', () => ({
  getVesselV2Dropdown: jest.fn(() =>
    Promise.resolve({ data: { results: [{ id: 1, name: 'test' }] } }),
  ),
}));

describe('AddTrainingRequirementModal', () => {
  const renderTrainingReqModal = ({
    setShowAddTrainingRequirementModal = setShowAddTrainingRequirementModal,
    isEdit = false,
    isFromMasterAppraisal = false,
  }) => {
    return render(
      <AddTrainingRequirementModal
        setShowAddTrainingRequirementModal={setShowAddTrainingRequirementModal}
        isEdit={isEdit}
        isFromMasterAppraisal={isFromMasterAppraisal}
      />,
    );
  };

  test('training requirement modal opens', async () => {
    const { getByText } = renderTrainingReqModal({ setShowAddTrainingRequirementModal: true });
    await waitFor(() => {
      expect(getByText(/Add Training Requirement/i)).toBeInTheDocument();
    });
  });

  test('renders form fields correctly', async () => {
    const { getByText } = renderTrainingReqModal({ setShowAddTrainingRequirementModal: true });
    await waitFor(() => {
      expect(getByText(/Date Training Recommended*/i)).toBeInTheDocument();
      expect(getByText(/Deadline*/i)).toBeInTheDocument();
      expect(getByText(/Vessel*/i)).toBeInTheDocument();
      expect(getByText(/Date Completed/i)).toBeInTheDocument();
      expect(getByText(/Training Needs*/i)).toBeInTheDocument();
      expect(getByText(/Training Imparted/i)).toBeInTheDocument();
      expect(getByText(/Supporting Document/i)).toBeInTheDocument();
      expect(getByText(/Superintendent Report/i)).toBeInTheDocument();
    });
  });

  test('certian fields like, completed date, training imparted, supporting doc and supt report should be disabled', async () => {
    const { getByPlaceholderText, getAllByText, getByTitle } = renderTrainingReqModal({
      setShowAddTrainingRequirementModal: true,
    });

    await waitFor(() => {
      const uploadButton = getAllByText(/Upload/i);
      const dateOfCompletedButton = getByPlaceholderText(/Select date of Completed/i);
      expect(getByTitle('Training Imparted')).toBeDisabled();
      expect(uploadButton[0]).toBeDisabled();
      expect(uploadButton[1]).toBeDisabled();
      expect(dateOfCompletedButton).toBeDisabled();
    });
  });

  test('should show validation error if mandatory fields are empty', async () => {
    await act(async () => {
      renderTrainingReqModal({
        setShowAddTrainingRequirementModal: true,
      });
    });
    await act(async () => {
      fireEvent.click(screen.getByText('Save'));
    });

    await waitFor(() => {
      expect(screen.getByText(COMMON_VALIDATION_MSG.VESSEL_NAME)).toBeInTheDocument();
      expect(screen.getByText(COMMON_VALIDATION_MSG.TRAINING_NEED)).toBeInTheDocument();
    });
  });

  test('should not show validation error if mandatory fields are filled', async () => {
    await act(async () => {
      renderTrainingReqModal({
        setShowAddTrainingRequirementModal: true,
      });
    });
    await act(async () => {
      fireEvent.change(screen.getByTitle('training_needs'), { target: { value: 'new comment' } });
      fireEvent.click(screen.getByText('Save'));
    });
    await waitFor(() => {
      expect(() => screen.getByText(COMMON_VALIDATION_MSG.TRAINING_NEED)).toThrow();
    });
  });
});
