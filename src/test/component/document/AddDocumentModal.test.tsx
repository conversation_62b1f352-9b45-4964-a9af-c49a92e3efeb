import React from 'react';
import { MemoryRouter } from 'react-router-dom';
import '@testing-library/jest-dom';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import AddDocumentModal from '../../../component/document/AddDocumentModal';
import dropdowndata from '../../resources/drop-down-data.json';
import * as vesselService from '../../../service/vessel-service';
import vesselDropdownData from '../../resources/vessel-drop-down.json';

import seafarerService from '../../../service/seafarer-service';
import * as documentTypes from '../../../constants/documentTypes';
import * as mockResponse from '../../resources/document-response';

jest.mock('../../../styleGuide');
jest.mock('../../../service/user-service');

const renderAddDocumentModalForCreate = (targetId) => (
  <MemoryRouter>
    <AddDocumentModal
      setShowDocumentModal={() => {}}
      targetId={targetId}
      dropdownData={dropdowndata}
      onSubmitCallback={undefined}
      seafarerPersonId={undefined}
      docId={undefined}
      history={undefined}
      disableDocumentTypeDropdown={undefined}
      overrideOnSubmitRedirectPath={undefined}
      onSubmit={undefined}
      eventTracker={undefined}
    />
  </MemoryRouter>
);

const renderAddDocumentModalForEdit = (targetFormId, docId) => (
  <MemoryRouter>
    <AddDocumentModal
      setShowDocumentModal={() => {}}
      targetId={targetFormId}
      docId={docId}
      dropdownData={dropdowndata}
      onSubmitCallback={undefined}
      seafarerPersonId={undefined}
      history={undefined}
      disableDocumentTypeDropdown={undefined}
      overrideOnSubmitRedirectPath={undefined}
      onSubmit={undefined}
      eventTracker={undefined}
    />
  </MemoryRouter>
);

describe('Render AddDocumentModal for adding document and check error message', () => {
  beforeAll(async () => {
    jest.spyOn(vesselService, 'getVessels').mockResolvedValue({
      status: 200,
      data: vesselDropdownData,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should show indos form inputs', async () => {
    render(renderAddDocumentModalForCreate(documentTypes.DOC_FORM_IDS.INDOS));

    expect(screen.getByText(/document type\*/i)).toBeInTheDocument();
    expect(screen.getByText(/issued by/i)).toBeInTheDocument();
    expect(screen.getByText(/certificate no\.\*/i)).toBeInTheDocument();
    expect(screen.getByText(/date of issue/i)).toBeInTheDocument();
    expect(screen.getByText(/date of expiry/i)).toBeInTheDocument();
    expect(screen.getByText(/document \(pdf, jpg, png, max size 12mb\)/i)).toBeInTheDocument();
  });

  it('should show confirmation popup on change in indos form', async () => {
    render(renderAddDocumentModalForCreate(documentTypes.DOC_FORM_IDS.INDOS));
    const certNoInput = screen
      .getAllByRole('textbox')
      .find((input) => input.name === 'certificate_no');
    if (certNoInput) {
      fireEvent.change(certNoInput, { target: { value: 'cert' } });
    }
    const cancelButton = screen.getByRole('button', {
      name: /cancel/i,
    });
    fireEvent.click(cancelButton);
    await waitFor(() => {
      const type = screen.getByText('Are you sure?');
      expect(type).toBeInTheDocument();
    });
  });

  it('should show confirmation popup on change in visa form', async () => {
    render(renderAddDocumentModalForCreate(documentTypes.DOC_FORM_IDS.VISA));
    const visaType = screen.getAllByRole('textbox').find((input) => input.name === 'type_of_visa');
    if (visaType) {
      fireEvent.change(visaType, { target: { value: 'cert' } });
    }
    const cancelButton = screen.getByRole('button', {
      name: /cancel/i,
    });
    fireEvent.click(cancelButton);
    await waitFor(() => {
      const type = screen.getByText('Are you sure?');
      expect(type).toBeInTheDocument();
    });
  });

  it('should show visa form inputs', async () => {
    render(renderAddDocumentModalForCreate(documentTypes.DOC_FORM_IDS.VISA));

    expect(screen.getByText(/document type\*/i)).toBeInTheDocument();
    expect(screen.getByText(/type of visa\*/i)).toBeInTheDocument();
    expect(screen.getByText(/country\*/i)).toBeInTheDocument();
    expect(screen.getByText(/number\*/i)).toBeInTheDocument();
    expect(screen.getByText(/date of issue/i)).toBeInTheDocument();
    expect(screen.getByText(/date of expiry/i)).toBeInTheDocument();
    expect(screen.getByText(/issuing authority/i)).toBeInTheDocument();
    expect(screen.getByText(/rejected/i)).toBeInTheDocument();
    expect(
      screen.getByText(/upload copy of visa\* \(pdf, jpg, png, max size 12mb\)/i),
    ).toBeInTheDocument();
  });

  it('should render Visa form and required fields error check', async () => {
    render(renderAddDocumentModalForCreate(documentTypes.DOC_FORM_IDS.VISA));
    const submitBtn = screen.getByRole('button', {
      name: /confirm/i,
    });
    fireEvent.click(submitBtn);
    await waitFor(() => {
      expect(screen.getByText(/type of visa is required/i)).toBeInTheDocument();
      expect(screen.getByText(/country is required/i)).toBeInTheDocument();
      expect(screen.getByText(/number is required/i)).toBeInTheDocument();
      expect(screen.getByText(/issuing authority is required/i)).toBeInTheDocument();
      expect(screen.getByText(/please provide a copy of visa\./i)).toBeInTheDocument();
    });
  });

  it('should show Endorsement form inputs', async () => {
    render(renderAddDocumentModalForCreate(documentTypes.DOC_FORM_IDS.ENDORSEMENT));

    expect(screen.getByText(/document type\*/i)).toBeInTheDocument();
    expect(screen.getByText(/type of endorsement\*/i)).toBeInTheDocument();
    expect(screen.getByText(/issued by/i)).toBeInTheDocument();
    expect(screen.getByText(/certificate no\./i)).toBeInTheDocument();
    expect(screen.getByText(/date of issue/i)).toBeInTheDocument();
    expect(screen.getByText(/date of expiry/i)).toBeInTheDocument();
    expect(screen.getByText(/document \(pdf, jpg, png, max size 12mb\)/i)).toBeInTheDocument();
  });

  it('should render Endorsement type dropdown', async () => {
    render(renderAddDocumentModalForCreate(documentTypes.DOC_FORM_IDS.ENDORSEMENT));
    const submitBtn = screen.getByTestId('endorsement-type');
    fireEvent.click(submitBtn);
    await waitFor(() => {
      expect(
        screen.getByRole('option', {
          name: /dangerous cargo endorsement \(chemical\)/i,
        }),
      ).toBeInTheDocument();
      expect(
        screen.getByRole('option', {
          name: /dangerous cargo endorsement \(gas\)/i,
        }),
      ).toBeInTheDocument();
      expect(
        screen.getByRole('option', {
          name: /dangerous cargo endorsement \(oil\)/i,
        }),
      ).toBeInTheDocument();
      expect(
        screen.getByRole('option', {
          name: /gmdss endorsement/i,
        }),
      ).toBeInTheDocument();
      expect(
        screen.getByRole('option', {
          name: /igf - advance level cop/i,
        }),
      ).toBeInTheDocument();
      expect(
        screen.getByRole('option', {
          name: /igf - basic cop/i,
        }),
      ).toBeInTheDocument();
    });
  });

  it('should render Endorsement form and required fields error check', async () => {
    render(renderAddDocumentModalForCreate(documentTypes.DOC_FORM_IDS.ENDORSEMENT));
    const submitBtn = screen.getByRole('button', {
      name: /confirm/i,
    });
    fireEvent.click(submitBtn);
    await waitFor(() => {
      expect(screen.getByText(/type of endorsement is required/i)).toBeInTheDocument();
    });
  });

  it('should show Document Verification form inputs', async () => {
    render(renderAddDocumentModalForCreate(documentTypes.DOC_FORM_IDS.VERIFICATION));

    expect(screen.getByText(/document type\*/i)).toBeInTheDocument();
    expect(screen.getByText(/type of Verification\*/i)).toBeInTheDocument();
    expect(screen.getByText(/date of issue/i)).toBeInTheDocument();
    expect(screen.getByText(/date of expiry/i)).toBeInTheDocument();
    expect(screen.getByText(/document \(pdf, jpg, png, max size 12mb\)/i)).toBeInTheDocument();
  });

  it('should render Document Verification form and required fields error check', async () => {
    render(renderAddDocumentModalForCreate(documentTypes.DOC_FORM_IDS.VERIFICATION));
    const submitBtn = screen.getByRole('button', {
      name: /confirm/i,
    });
    fireEvent.click(submitBtn);
    await waitFor(() => {
      expect(screen.getByText(/type of verification is required/i)).toBeInTheDocument();
    });
  });

  it('should show DCE Verification form inputs', async () => {
    render(renderAddDocumentModalForCreate(documentTypes.DOC_FORM_IDS.DCE_VERIFICAITON));

    expect(screen.getByText(/document type\*/i)).toBeInTheDocument();
    expect(screen.getByText(/type of Verification\*/i)).toBeInTheDocument();
    expect(screen.getByText(/date of issue/i)).toBeInTheDocument();
    expect(screen.getByText(/date of expiry/i)).toBeInTheDocument();
    expect(screen.getByText(/document \(pdf, jpg, png, max size 12mb\)/i)).toBeInTheDocument();
  });

  it('should render DCE Verification form and required fields error check', async () => {
    render(renderAddDocumentModalForCreate(documentTypes.DOC_FORM_IDS.DCE_VERIFICAITON));
    const submitBtn = screen.getByRole('button', {
      name: /confirm/i,
    });
    fireEvent.click(submitBtn);
    await waitFor(() => {
      expect(screen.getByText(/type of verification is required/i)).toBeInTheDocument();
    });
  });

  it('should show Apprenticeship form inputs', async () => {
    render(renderAddDocumentModalForCreate(documentTypes.DOC_FORM_IDS.APPRENTICESHIP));

    expect(screen.getByText(/document type\*/i)).toBeInTheDocument();
    expect(screen.getByText(/apprenticeship\*/i)).toBeInTheDocument();
    expect(screen.getByText(/start date/i)).toBeInTheDocument();
    expect(screen.getByText(/end date/i)).toBeInTheDocument();
    expect(screen.getByText(/document \(pdf, jpg, png, max size 12mb\)/i)).toBeInTheDocument();
  });

  it('should render Apprenticeship form and required fields error check', async () => {
    render(renderAddDocumentModalForCreate(documentTypes.DOC_FORM_IDS.APPRENTICESHIP));
    const submitBtn = screen.getByRole('button', {
      name: /confirm/i,
    });
    fireEvent.click(submitBtn);
    await waitFor(() => {
      expect(screen.getByText(/type of apprenticeship is required/i)).toBeInTheDocument();
    });
  });

  it('should show Drug And Alcohol Test form inputs', async () => {
    render(renderAddDocumentModalForCreate(documentTypes.DOC_FORM_IDS.DRUG_ALCOHOL_TEST));

    await waitFor(() => {
      expect(screen.getByText(/document type\*/i)).toBeInTheDocument();
      expect(screen.getByText(/vessel\*/i)).toBeInTheDocument();
      expect(screen.getByText(/tester/i)).toBeInTheDocument();
      expect(screen.getByText(/result/i)).toBeInTheDocument();
      expect(screen.getByText(/date of test/i)).toBeInTheDocument();
      expect(screen.getByText(/date of expiry/i)).toBeInTheDocument();
      expect(screen.getByText(/document \(pdf, jpg, png, max size 12mb\)/i)).toBeInTheDocument();
    });
  });

  it('should render Drug And Alcohol Test form and required fields error check', async () => {
    render(renderAddDocumentModalForCreate(documentTypes.DOC_FORM_IDS.DRUG_ALCOHOL_TEST));
    await waitFor(() => {
      const submitBtn = screen.getByRole('button', {
        name: /confirm/i,
      });
      fireEvent.click(submitBtn);
    });
    await waitFor(() => {
      expect(screen.getByText(/vessel is required/i)).toBeInTheDocument();
    });
  });

  it('should show Education form inputs', async () => {
    render(renderAddDocumentModalForCreate(documentTypes.DOC_FORM_IDS.EDUCATION));

    expect(screen.getByText(/document type\*/i)).toBeInTheDocument();
    expect(screen.getByText(/qualification\*/i)).toBeInTheDocument();
    expect(screen.getByText(/institute/i)).toBeInTheDocument();
    expect(screen.getByText(/class/i)).toBeInTheDocument();
    expect(screen.getByText(/pass date/i)).toBeInTheDocument();
    expect(screen.getByText(/document \(pdf, jpg, png, max size 12mb\)/i)).toBeInTheDocument();
  });

  it('should render Education form and required fields error check', async () => {
    render(renderAddDocumentModalForCreate(documentTypes.DOC_FORM_IDS.EDUCATION));
    await waitFor(() => {
      const submitBtn = screen.getByRole('button', {
        name: /confirm/i,
      });
      fireEvent.click(submitBtn);
    });
    await waitFor(() => {
      expect(screen.getByText(/qualification is required/i)).toBeInTheDocument();
    });
  });

  it('should show Medical form inputs', async () => {
    render(renderAddDocumentModalForCreate(documentTypes.DOC_FORM_IDS.MEDICAL));

    expect(screen.getByText(/document type\*/i)).toBeInTheDocument();
    expect(screen.getByText(/medical certificates\*/i)).toBeInTheDocument();
    expect(screen.getByText(/issued by/i)).toBeInTheDocument();
    expect(screen.getByText(/certificate no\./i)).toBeInTheDocument();
    expect(screen.getByText(/date of issue/i)).toBeInTheDocument();
    expect(screen.getByText(/date of expiry/i)).toBeInTheDocument();
    expect(screen.getByText(/document \(pdf, jpg, png, max size 12mb\)/i)).toBeInTheDocument();
  });

  it('should render Medical form and required fields error check', async () => {
    render(renderAddDocumentModalForCreate(documentTypes.DOC_FORM_IDS.MEDICAL));
    const submitBtn = screen.getByRole('button', {
      name: /confirm/i,
    });
    fireEvent.click(submitBtn);

    await waitFor(() => {
      expect(screen.getByText(/certificate is required/i)).toBeInTheDocument();
    });
  });

  it('should show STCW form inputs', async () => {
    render(renderAddDocumentModalForCreate(documentTypes.DOC_FORM_IDS.STCW));

    expect(screen.getByText(/document type\*/i)).toBeInTheDocument();
    expect(screen.getByText(/type of stcw\*/i)).toBeInTheDocument();
    expect(screen.getByText(/issued by/i)).toBeInTheDocument();
    expect(screen.getByText(/certificate no\./i)).toBeInTheDocument();
    expect(screen.getByText(/date of issue/i)).toBeInTheDocument();
    expect(screen.getByText(/date of expiry/i)).toBeInTheDocument();
    expect(screen.getByText(/document \(pdf, jpg, png, max size 12mb\)/i)).toBeInTheDocument();
  });

  it('should render STCW form and required fields error check', async () => {
    render(renderAddDocumentModalForCreate(documentTypes.DOC_FORM_IDS.STCW));
    const submitBtn = screen.getByRole('button', {
      name: /confirm/i,
    });
    fireEvent.click(submitBtn);

    await waitFor(() => {
      expect(screen.getByText(/type of stcw is required/i)).toBeInTheDocument();
    });
  });

  it('should show Training form inputs', async () => {
    render(renderAddDocumentModalForCreate(documentTypes.DOC_FORM_IDS.TRAINING));

    expect(screen.getByText(/document type\*/i)).toBeInTheDocument();
    expect(screen.getByText(/course\*/i)).toBeInTheDocument();
    expect(screen.getByText(/institute/i)).toBeInTheDocument();
    expect(screen.getByText(/grade/i)).toBeInTheDocument();
    expect(screen.getByText(/start date/i)).toBeInTheDocument();
    expect(screen.getByText(/end date/i)).toBeInTheDocument();
    expect(screen.getByText(/document \(pdf, jpg, png, max size 12mb\)/i)).toBeInTheDocument();
  });

  it('should render Training form and required fields error check', async () => {
    render(renderAddDocumentModalForCreate(documentTypes.DOC_FORM_IDS.TRAINING));
    const submitBtn = screen.getByRole('button', {
      name: /confirm/i,
    });
    fireEvent.click(submitBtn);

    await waitFor(() => {
      expect(screen.getByText(/type of training is required/i)).toBeInTheDocument();
    });
  });

  it('should show Pre Sea Training form inputs', async () => {
    render(renderAddDocumentModalForCreate(12));

    expect(screen.getByText(/document type\*/i)).toBeInTheDocument();
    expect(screen.getByText(/course\*/i)).toBeInTheDocument();
    expect(screen.getByText(/institute\*/i)).toBeInTheDocument();
    expect(screen.getByText(/grade/i)).toBeInTheDocument();
    expect(screen.getByText(/start date/i)).toBeInTheDocument();
    expect(screen.getByText(/end date/i)).toBeInTheDocument();
    expect(screen.getByText(/document \(pdf, jpg, png, max size 12mb\)/i)).toBeInTheDocument();
  });

  it('should render Pre Sea Training form and required fields error check', async () => {
    render(renderAddDocumentModalForCreate(12));
    const submitBtn = screen.getByRole('button', {
      name: /confirm/i,
    });
    fireEvent.click(submitBtn);

    await waitFor(() => {
      expect(screen.getByText(/type of pre sea course is required/i)).toBeInTheDocument();
      expect(screen.getByText(/institute is required/i)).toBeInTheDocument();
    });
  });

  it('should show Other Course form inputs', async () => {
    render(renderAddDocumentModalForCreate(13));

    expect(screen.getByText(/document type\*/i)).toBeInTheDocument();
    expect(screen.getByText(/other courses type\*/i)).toBeInTheDocument();
    expect(screen.getByText(/institute/i)).toBeInTheDocument();
    expect(screen.getByText(/course title/i)).toBeInTheDocument();
    expect(screen.getByText(/certificate no\./i)).toBeInTheDocument();
    expect(screen.getByText(/date of issue/i)).toBeInTheDocument();
    expect(screen.getByText(/date of expiry/i)).toBeInTheDocument();
    expect(screen.getByText(/document \(pdf, jpg, png, max size 12mb\)/i)).toBeInTheDocument();
  });

  it('should show Other Document form inputs', async () => {
    render(renderAddDocumentModalForCreate(14));

    expect(screen.getByText(/document type\*/i)).toBeInTheDocument();
    expect(screen.getByText(/other documents\*/i)).toBeInTheDocument();
    expect(screen.getByText(/issued by/i)).toBeInTheDocument();
    expect(screen.getByText(/certificate no\./i)).toBeInTheDocument();
    expect(screen.getByText(/date of issue/i)).toBeInTheDocument();
    expect(screen.getByText(/date of expiry/i)).toBeInTheDocument();
    expect(screen.getByText(/document \(pdf, jpg, png, max size 12mb\)/i)).toBeInTheDocument();
  });
  it('should render Other Document  form and required fields error check', async () => {
    render(renderAddDocumentModalForCreate(14));
    const submitBtn = screen.getByRole('button', {
      name: /confirm/i,
    });
    fireEvent.click(submitBtn);

    await waitFor(() => {
      expect(screen.getByText(/type of document is required/i)).toBeInTheDocument();
    });
  });

  it('should show Certificate of Competency form inputs', async () => {
    render(renderAddDocumentModalForCreate(7));
    expect(screen.getByText(/document type\*/i)).toBeInTheDocument();
    expect(screen.getByText(/flags\*/i)).toBeInTheDocument();
    expect(screen.getByText(/certificate\*/i)).toBeInTheDocument();
    expect(screen.getByText(/certificate no\./i)).toBeInTheDocument();
    expect(screen.getByText(/date of issue/i)).toBeInTheDocument();
    expect(screen.getByText(/date of expiry/i)).toBeInTheDocument();
    expect(screen.getByText(/is national/i)).toBeInTheDocument();
    expect(screen.getByText(/document \(pdf, jpg, png, max size 12mb\)/i)).toBeInTheDocument();
  });
  it('should render Certificate of Competency form and required fields error check', async () => {
    render(renderAddDocumentModalForCreate(7));
    const submitBtn = screen.getByRole('button', {
      name: /confirm/i,
    });
    fireEvent.click(submitBtn);

    await waitFor(() => {
      expect(screen.getByText(/country name is required/i)).toBeInTheDocument();
      expect(screen.getByText(/certificate is required/i)).toBeInTheDocument();
    });
  });
  // Commented as all forms are implemented
  // it('should render other form and required fields check', async () => {
  //   let renderedView = await renderAddDocumentModalForCreate(15);
  //   let type = renderedView.find({ id: 'under-construction' });
  //   expect(type.exists()).toEqual(true);
  // });
});

describe('Render AddDocumentModal for editing document', () => {
  beforeAll(async () => {
    // mock service
    jest.setTimeout(100000000);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should render visa form for edit', async () => {
    seafarerService.getSeafarerDocument = jest
      .fn()
      .mockImplementation(() => Promise.resolve(mockResponse.visaDocumentGetResponse));
    render(renderAddDocumentModalForEdit(documentTypes.DOC_FORM_IDS.VISA, 1234));
    await waitFor(() => {
      const docTypeDropdown = screen.getByTestId('doc-type-dropdown');
      expect(docTypeDropdown.value).toEqual(documentTypes.DOC_FORM_IDS.VISA.toString());
      expect(docTypeDropdown.disabled).toEqual(true);
    });

    // number: 'M 95**',
    // date_of_issue: '2015-08-08T00:00:00.000Z',
    // date_of_expiry: '2025-03-29T00:00:00.000Z',
    await waitFor(() => {
      const allInputs = screen.getAllByRole('textbox');
      const numberInput = allInputs.find((input) => input.name === 'number');
      expect(numberInput.value).toEqual('M 95**');
      const dateOfIssueInput = allInputs.find((input) => input.name === 'date_of_issue');
      expect(dateOfIssueInput.value).toEqual('8 Aug 2015');
      const dateOfExpiryInput = allInputs.find((input) => input.name === 'date_of_expiry');
      expect(dateOfExpiryInput.value).toEqual('29 Mar 2025');
    });
  });

  it('should render indos form for edit', async () => {
    seafarerService.getSeafarerDocument = jest
      .fn()
      .mockImplementation(() => Promise.resolve(mockResponse.indosDocumentGetResponse));

    render(renderAddDocumentModalForEdit(documentTypes.DOC_FORM_IDS.INDOS, 1234));

    await waitFor(() => {
      const docTypeDropdown = screen.getByTestId('doc-type-dropdown');
      expect(docTypeDropdown.value).toEqual(documentTypes.DOC_FORM_IDS.INDOS.toString());
      expect(docTypeDropdown.disabled).toEqual(true);

      const allInputs = screen.getAllByRole('textbox');
      const numberInput = allInputs.find((input) => input.name === 'issued_by');
      expect(numberInput.value).toEqual('test');
      const dateOfIssueInput = allInputs.find((input) => input.name === 'date_of_issue');
      expect(dateOfIssueInput.value).toEqual('26 Jan 2022');
      const dateOfExpiryInput = allInputs.find((input) => input.name === 'date_of_expiry');
      expect(dateOfExpiryInput.value).toEqual('26 Jan 2023');
    });
  });

  it('should render verification form for edit', async () => {
    seafarerService.getSeafarerDocument = jest
      .fn()
      .mockImplementation(() => Promise.resolve(mockResponse.verificationDocumentGetResponse));

    render(renderAddDocumentModalForEdit(documentTypes.DOC_FORM_IDS.VERIFICATION, 1234));

    await waitFor(() => {
      const docTypeDropdown = screen.getByTestId('doc-type-dropdown');
      expect(docTypeDropdown.value).toEqual(documentTypes.DOC_FORM_IDS.VERIFICATION.toString());
      expect(docTypeDropdown.disabled).toEqual(true);

      const allInputs = screen.getAllByRole('textbox');
      const dateOfIssueInput = allInputs.find((input) => input.name === 'date_of_issue');
      expect(dateOfIssueInput.value).toEqual('27 Jan 2017');
      const dateOfExpiryInput = allInputs.find((input) => input.name === 'date_of_expiry');
      expect(dateOfExpiryInput.value).toEqual('');
    });
  });

  it('should render endorsement form for edit', async () => {
    seafarerService.getSeafarerDocument = jest
      .fn()
      .mockImplementation(() => Promise.resolve(mockResponse.endorsementDocumentGetResponse));

    render(renderAddDocumentModalForEdit(documentTypes.DOC_FORM_IDS.ENDORSEMENT, 1234));

    await waitFor(() => {
      const docTypeDropdown = screen.getByTestId('doc-type-dropdown');
      expect(docTypeDropdown.value).toEqual(documentTypes.DOC_FORM_IDS.ENDORSEMENT.toString());
      expect(docTypeDropdown.disabled).toEqual(true);

      const allInputs = screen.getAllByRole('textbox');
      const dateOfIssueInput = allInputs.find((input) => input.name === 'date_of_issue');
      expect(dateOfIssueInput.value).toEqual('');
      const dateOfExpiryInput = allInputs.find((input) => input.name === 'date_of_expiry');
      expect(dateOfExpiryInput.value).toEqual('');
    });
  });
  it('test for confirm modal popup in Visa Form edit', async () => {
    seafarerService.getSeafarerDocument = jest
      .fn()
      .mockImplementation(() => Promise.resolve(mockResponse.visaDocumentGetResponse));

    render(renderAddDocumentModalForEdit(documentTypes.DOC_FORM_IDS.VISA, 1234));

    await waitFor(() => {
      const certNoInput = screen.getAllByRole('textbox').find((input) => input.name === 'number');
      if (certNoInput) {
        fireEvent.change(certNoInput, { target: { value: '***' } });
      }
    });
    const cancelButton = screen.getByRole('button', {
      name: /cancel/i,
    });
    fireEvent.click(cancelButton);
    await waitFor(() => {
      const type = screen.getByText('Are you sure?');
      expect(type).toBeInTheDocument();
    });
  });

  it('test for confirm modal popup in verification form edit', async () => {
    seafarerService.getSeafarerDocument = jest
      .fn()
      .mockImplementation(() => Promise.resolve(mockResponse.verificationDocumentGetResponse));

    render(renderAddDocumentModalForEdit(documentTypes.DOC_FORM_IDS.VERIFICATION, 1234));
    await waitFor(() => {
      const certNoInput = screen
        .getAllByRole('textbox')
        .find((input) => input.name === 'type_of_verification');
      if (certNoInput) {
        fireEvent.change(certNoInput, { target: { value: 'typetestverification' } });
      }
    });
    const cancelButton = screen.getByRole('button', {
      name: /cancel/i,
    });
    fireEvent.click(cancelButton);
    await waitFor(() => {
      const type = screen.getByText('Are you sure?');
      expect(type).toBeInTheDocument();
    });
  });
});
