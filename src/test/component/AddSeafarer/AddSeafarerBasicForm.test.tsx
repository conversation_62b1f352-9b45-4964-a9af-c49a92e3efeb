import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import AddSeafarerBasicForm from '../../../component/AddSeafarer/AddSeafarerBasicForm';
import dropdowndata from '../../resources/drop-down-data.json';
import portsdata from '../../resources/ports-map.json';
import { getMockedSeafarerResponse } from '../../resources/seafarer-mock-data';
import seafarerService from '../../../service/seafarer-service';
import { BASIC_FORM_TESTID_FIELDS } from '../../../constants/test-id/seafarer-form-test-id';

jest.mock('../../../styleGuide');
jest.mock('../../../service/user-service');
jest.mock('../../../service/seafarer-service');

const renderAddSeafarerForm = (seafarer) => (
  <AddSeafarerBasicForm
    seafarer={seafarer}
    dropDownData={dropdowndata}
    errors={{}}
    handleBlur={jest.fn()}
    onSeafarerChange={jest.fn()}
    portsMap={portsdata}
    ref={{ basicRef: null, personalDetailsRef: null, passportRef: null, seamanBookRef: null }}
    cacheDuplicates={jest.fn()}
    cachedDuplicates={{
      passport: {},
      seamansBook: {},
      personalDetails: {},
    }}
    roleConfig={{
      seafarer: {
        edit: {
          personalDetails: true,
          passport: true,
          seamansBook: true,
        },
        create: {
          rank: true,
        },
      },
    }}
  />
);

beforeAll(async () => {
  seafarerService.getPersonalDetailsDuplicates = jest
    .fn()
    .mockImplementation(() => Promise.resolve({ status: 200, data: [] }));
});

describe('AddSeafarerBasicForm', () => {
  const seafarer = getMockedSeafarerResponse();
  it('should have sections', async () => {
    const { container } = render(renderAddSeafarerForm(seafarer));
    const expectedElements = ['Personal Details', 'Passport Details', 'Seaman’s Book'];
    const elements = await waitFor(() =>
      container.getElementsByClassName('add_seafarer_page__section-title'),
    );
    const result = Array.from(elements).map((element) => element.textContent);
    expect(result).toEqual(expectedElements);
  });

  it('should have form inputs ', async () => {
    const expectedInputs = [
      'Rank*',
      'Reporting Office*',
      'Manning Agency',
      'First Name*',
      'Middle Name',
      'Last Name*',
      'Date of Birth*',
      'Gender*',
      'Place of Birth*',
      'Country of Birth*',
      'Nationality*',
      'Upload copy of passport* (pdf, jpg, png, max size 12MB)',
      'Passport Number*',
      'Place of Issue*',
      'Country of Issue*',
      'Date of Issue*',
      'Date of Expiry*',
      'Upload copy of passport* (pdf, jpg, png, max size 12MB)',
      'Passport Number*',
      'Place of Issue*',
      'Country of Issue*',
      'Date of Issue*',
      'Date of Expiry*',
      'Upload copy of Seaman’s Book* (pdf, jpg, png, max size 12MB)',
      'Is National',
      'Seaman’s Book Number*',
      'Country*',
      'Port of Issue*',
      'Date of Issue*',
      'Date of Expiry*',
      'Upload copy of Seaman’s Book* (pdf, jpg, png, max size 12MB)',
      'Is National',
      'Seaman’s Book Number*',
      'Country*',
      'Port of Issue*',
      'Date of Issue*',
      'Date of Expiry*',
    ];
    const { container } = render(renderAddSeafarerForm(seafarer));
    const elements = await waitFor(() => container.getElementsByClassName('form-label'));
    const result = Array.from(elements).map((element) => element.textContent);
    expect(result).toEqual(expectedInputs);
  });

  it('should have 9 date pickers', async () => {
    const { container } = render(renderAddSeafarerForm(seafarer));
    const elements = await waitFor(() =>
      container.getElementsByClassName('react-datepicker__input-container'),
    );
    expect(Array.from(elements).length).toEqual(9);
  });

  it('Should populate test ID accordingly', async () => {
    render(renderAddSeafarerForm(seafarer));
    // Check if testID is properly populated
    BASIC_FORM_TESTID_FIELDS.forEach((testID) => {
      const field = screen.getAllByTestId(`${testID}`);
      expect(field.length).toEqual(1);
    });
  });

  it('should not disable the rank field if current journey status is on_leave', async () => {
    const { getByTestId } = render(renderAddSeafarerForm(seafarer));
    const dropdown = getByTestId('form-rank-field') as HTMLInputElement;
    expect(dropdown.disabled).toBe(false);
  });

  it('should disable the rank field if current journey status is signed_on', async () => {
    const signedOnSeafarer = {
      ...seafarer,
      seafarer_person: {
        ...seafarer.seafarer_person,
        current_journey_status: 'signed_on',
      },
    };
    const { getByTestId } = render(renderAddSeafarerForm(signedOnSeafarer));
    const dropdown = getByTestId('form-rank-field') as HTMLInputElement;
    expect(dropdown.disabled).toBe(true);
  });
});
