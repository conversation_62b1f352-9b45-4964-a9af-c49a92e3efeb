import React from 'react';
import { RenderResult, render, waitFor } from '@testing-library/react';
import AddSeafarerExperienceForm from '../../../component/AddSeafarer/AddSeafarerExperienceForm';
import dropdowndata from '../../resources/drop-down-data.json';
import * as mockResponse from '../../resources/document-response';
import seafarerService from '../../../service/seafarer-service';

jest.mock('../../../styleGuide');
jest.mock('../../../service/user-service');

beforeAll(async () => {
  seafarerService.getPersonalDetailsDuplicates = jest
    .fn()
    .mockImplementation(() => Promise.resolve({ status: 200, data: [] }));
});

describe('AddSeafarerExperienceForm', () => {
  const roleConfigData = {
    seafarer: {
      screening: {
        view: true,
      },
      create: {},
      edit: {
        duplicateHKID: false,
        seafarerExperience: true,
      },
      view: {
        general: true,
        bankAccount: true,
        hkid: true,
      },
      hidden: {
        contactDetails: false,
      },
    },
  };

  describe('Seafarer with no ice experience', () => {
    let renderedView: RenderResult<typeof import('@testing-library/dom/types/queries')>;
    beforeEach(async () => {
      const seafarer = mockResponse.seafarerGetResponse.data;
      renderedView = render(
        <AddSeafarerExperienceForm
          seafarer={seafarer}
          dropDownData={dropdowndata}
          handleBlur={jest.fn()}
          onSeafarerChange={jest.fn()}
          roleConfig={roleConfigData}
        />,
      );
    });

    it('should have upload button if seafarer.ice_conditions_experience is undefined/false', async () => {
      const uploadBtn = await waitFor(() =>
        renderedView.getAllByTestId('upload-ice-experience-btn'),
      );
      const editBtn = await waitFor(() => renderedView.queryAllByTestId('edit-ice-experience-btn'));
      expect(uploadBtn).toHaveLength(1);
      expect(editBtn).toHaveLength(0);
    });

    it('should enable the fields in experience tab', async () => {
      const framoExpField = await waitFor(
        () => renderedView.getByTestId('framo_experience_field') as HTMLInputElement,
      );
      expect(framoExpField.disabled).toBe(false);

      const showFmlExpField = await waitFor(
        () => renderedView.getByTestId('show_fml_experience_field') as HTMLInputElement,
      );
      expect(showFmlExpField.disabled).toBe(false);

      const showCargoExpField = await waitFor(
        () => renderedView.getByTestId('show_cargo_experience') as HTMLInputElement,
      );
      expect(showCargoExpField.disabled).toBe(false);

      const additionalExpTextArea = await waitFor(
        () => renderedView.getByTestId('additional-experience-text-area') as HTMLInputElement,
      );
      expect(additionalExpTextArea.disabled).toBe(false);

      const cargoHandlingExpField = await waitFor(
        () => renderedView.getByTestId('cargo_handling_experience_field') as HTMLInputElement,
      );
      expect(cargoHandlingExpField.disabled).toBe(false);
    });
  });

  describe('Seafarer with ice experience', () => {
    let renderedView: RenderResult<typeof import('@testing-library/dom/types/queries')>;
    beforeEach(async () => {
      const seafarerWithIceExperience = mockResponse.seafarerWithIceExperienceGetResponse.data;

      renderedView = render(
        <AddSeafarerExperienceForm
          seafarer={seafarerWithIceExperience}
          dropDownData={dropdowndata}
          handleBlur={jest.fn()}
          onSeafarerChange={jest.fn()}
          roleConfig={roleConfigData}
        />,
      );
    });

    it('should have edit button if seafarer.ice_conditions_experience is true', async () => {
      const uploadBtn = await waitFor(() =>
        renderedView.queryAllByTestId('upload-ice-experience-btn'),
      );
      const editBtn = await waitFor(() => renderedView.queryAllByTestId('edit-ice-experience-btn'));
      expect(uploadBtn).toHaveLength(0);
      expect(editBtn).toHaveLength(1);
    });
  });

  const roleConfigDataWithoutExpEdit = {
    seafarer: {
      screening: {
        view: true,
      },
      create: {},
      edit: {
        duplicateHKID: false,
        seafarerExperience: false,
      },
      view: {
        general: true,
        bankAccount: true,
        hkid: true,
      },
      hidden: {
        contactDetails: false,
      },
    },
  };

  describe('Seafarer without ice experience and user has no edit experience permission', () => {
    let renderedView: RenderResult<typeof import('@testing-library/dom/types/queries')>;
    beforeEach(async () => {
      const seafarer = mockResponse.seafarerGetResponse.data;

      renderedView = render(
        <AddSeafarerExperienceForm
          seafarer={seafarer}
          dropDownData={dropdowndata}
          handleBlur={jest.fn()}
          onSeafarerChange={jest.fn()}
          roleConfig={roleConfigDataWithoutExpEdit}
        />,
      );
    });

    it('should have no edit or upload button', async () => {
      const uploadBtn = await waitFor(() =>
        renderedView.queryAllByTestId('upload-ice-experience-btn'),
      );
      const editBtn = await waitFor(() => renderedView.queryAllByTestId('edit-ice-experience-btn'));
      expect(uploadBtn).toHaveLength(0);
      expect(editBtn).toHaveLength(0);
    });

    it('should disable the fields in experience tab', async () => {
      const framoExpField = await waitFor(
        () => renderedView.getByTestId('framo_experience_field') as HTMLInputElement,
      );
      expect(framoExpField.disabled).toBe(true);

      const showFmlExpField = await waitFor(
        () => renderedView.getByTestId('show_fml_experience_field') as HTMLInputElement,
      );
      expect(showFmlExpField.disabled).toBe(true);

      const showCargoExpField = await waitFor(
        () => renderedView.getByTestId('show_cargo_experience') as HTMLInputElement,
      );
      expect(showCargoExpField.disabled).toBe(true);

      const additionalExpTextArea = await waitFor(
        () => renderedView.getByTestId('additional-experience-text-area') as HTMLInputElement,
      );
      expect(additionalExpTextArea.disabled).toBe(true);

      const cargoHandlingExpField = await waitFor(
        () => renderedView.getByTestId('cargo_handling_experience_field') as HTMLInputElement,
      );
      expect(cargoHandlingExpField.disabled).toBe(true);
    });
  });

  describe('Seafarer with ice experience and user has no edit experience permission', () => {
    let renderedView: RenderResult<typeof import('@testing-library/dom/types/queries')>;
    beforeEach(async () => {
      const seafarerWithIceExperience = mockResponse.seafarerWithIceExperienceGetResponse.data;

      renderedView = render(
        <AddSeafarerExperienceForm
          seafarer={seafarerWithIceExperience}
          dropDownData={dropdowndata}
          handleBlur={jest.fn()}
          onSeafarerChange={jest.fn()}
          roleConfig={roleConfigDataWithoutExpEdit}
        />,
      );
    });

    it('should have no edit or upload button', async () => {
      const uploadBtn = await waitFor(() =>
        renderedView.queryAllByTestId('upload-ice-experience-btn'),
      );
      const editBtn = await waitFor(() => renderedView.queryAllByTestId('edit-ice-experience-btn'));
      expect(uploadBtn).toHaveLength(0);
      expect(editBtn).toHaveLength(0);
    });

    it('should disable the fields in experience tab', async () => {
      const framoExpField = await waitFor(
        () => renderedView.getByTestId('framo_experience_field') as HTMLInputElement,
      );
      expect(framoExpField.disabled).toBe(true);

      const showFmlExpField = await waitFor(
        () => renderedView.getByTestId('show_fml_experience_field') as HTMLInputElement,
      );
      expect(showFmlExpField.disabled).toBe(true);

      const showCargoExpField = await waitFor(
        () => renderedView.getByTestId('show_cargo_experience') as HTMLInputElement,
      );
      expect(showCargoExpField.disabled).toBe(true);

      const additionalExpTextArea = await waitFor(
        () => renderedView.getByTestId('additional-experience-text-area') as HTMLInputElement,
      );
      expect(additionalExpTextArea.disabled).toBe(true);

      const cargoHandlingExpField = await waitFor(
        () => renderedView.getByTestId('cargo_handling_experience_field') as HTMLInputElement,
      );
      expect(cargoHandlingExpField.disabled).toBe(true);
    });
  });
});
