import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import ConfirmClubModal from '../../../component/CrewPlanner/ConfirmClubModal';

describe('ConfirmClubModal', () => {
  const handleCloseConfirm = jest.fn();
  const handleConfirmClub = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders correctly when show is true', () => {
    render(
      <ConfirmClubModal
        show={true}
        handleCloseConfirm={handleCloseConfirm}
        handleConfirmClub={handleConfirmClub}
      />
    );

    expect(screen.getByText('Confirm Clubbing')).toBeInTheDocument();
    expect(screen.getByText('By confirming to club, you will be redirected to the Reliever Seafarer Profile where you can set the travel status')).toBeInTheDocument();
    expect(screen.getByText('Cancel')).toBeInTheDocument();
    expect(screen.getByText('Confirm')).toBeInTheDocument();
  });

  test('does not render when show is false', () => {
    render(
      <ConfirmClubModal
        show={false}
        handleCloseConfirm={handleCloseConfirm}
        handleConfirmClub={handleConfirmClub}
      />
    );

    expect(screen.queryByText('Confirm Clubbing')).not.toBeInTheDocument();
  });

  test('calls handleCloseConfirm when Cancel button is clicked', () => {
    render(
      <ConfirmClubModal
        show={true}
        handleCloseConfirm={handleCloseConfirm}
        handleConfirmClub={handleConfirmClub}
      />
    );

    fireEvent.click(screen.getByText('Cancel'));
    expect(handleCloseConfirm).toHaveBeenCalled();
  });

  test('calls handleConfirmClub when Confirm button is clicked', () => {
    render(
      <ConfirmClubModal
        show={true}
        handleCloseConfirm={handleCloseConfirm}
        handleConfirmClub={handleConfirmClub}
      />
    );

    fireEvent.click(screen.getByText('Confirm'));
    expect(handleConfirmClub).toHaveBeenCalled();
  });

  test('calls handleCloseConfirm when modal is closed', () => {
    render(
      <ConfirmClubModal
        show={true}
        handleCloseConfirm={handleCloseConfirm}
        handleConfirmClub={handleConfirmClub}
      />
    );

    fireEvent.click(screen.getByRole('button', { name: /Cancel/i }));
    expect(handleCloseConfirm).toHaveBeenCalled();
  });
});
