import React from 'react';
import { EndOfContractStyling } from '../../../component/common/EndOfContractStyling';
import { render, cleanup } from '@testing-library/react';
import '@testing-library/jest-dom';
import { DateTime, Duration } from 'luxon';

afterEach(cleanup);

it('Red color for Contract over ', () => {
  const data = DateTime.now().minus(Duration.fromObject({ days: 2}));
  const { getByTestId } = render(<EndOfContractStyling value={data} />);
  expect(getByTestId('EndOfContractStyling_span')).toHaveClass('font-red');
});

it('Yellow color for Contract will expire in next _30 Days', () => {
  const data = DateTime.now().plus(Duration.fromObject({ days: 20 }));
  const { getByTestId } = render(<EndOfContractStyling value={data} />);
  expect(getByTestId('EndOfContractStyling_span')).toHaveClass('font-yellow');
});

it('Green color for Contract will expire in next _31-60 days', () => {
  const data = DateTime.now().plus(Duration.fromObject({ days: 34 }));
  const { getByTestId } = render(<EndOfContractStyling value={data} />);
  expect(getByTestId('EndOfContractStyling_span')).toHaveClass('font-green');
});

it('Black color for Date will Pass in next _61 or more days', () => {
  const data = DateTime.now().plus(Duration.fromObject({ days: 80 }));
  const { getByTestId } = render(<EndOfContractStyling value={data} />);
  expect(getByTestId('EndOfContractStyling_span')).toHaveClass('font-black');
});
