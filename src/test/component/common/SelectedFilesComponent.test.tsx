import React from 'react';
import { render, screen } from '@testing-library/react';
import FileUploadComponents from '../../../component/common/FileUploadModalView';

const { SelectedFilesComponent } = FileUploadComponents;

jest.mock('../../../styleGuide');
jest.mock('../../../service/user-service');
jest.mock('../../../controller/image-upload-controller');

const renderSelectedFiledComponent = (roleConfig: {
  seafarer: { screening: { caseReportUpload?: boolean; documentsUpload?: boolean } };
  screeningGroup: string;
}) => (
  <SelectedFilesComponent
    roleConfig={roleConfig}
    files={['file1', 'file2']}
    onFileChange={jest.fn()}
    onRemoveFile={jest.fn()}
  />
);

describe('<SelectedFilesComponent />', () => {
  it('should not have case report document_type option for user having no case report upload role', async () => {
    const roleConfig = {
      seafarer: {
        screening: {
          caseReportUpload: false,
        },
      },
      screeningGroup: 'Compliance Employee',
    };
    render(renderSelectedFiledComponent(roleConfig));
    const caseReportOption = screen.queryByText('Case Report');

    expect(caseReportOption).toBeNull();
  });

  it('should have case report document_type option for user having case report upload role', async () => {
    const roleConfig = {
      seafarer: {
        screening: {
          caseReportUpload: true,
        },
      },
      screeningGroup: 'Compliance Employee',
    };
    render(renderSelectedFiledComponent(roleConfig));
    const caseReportOptions = screen.getAllByText('Case Report');

    caseReportOptions.forEach((option) => {
      expect(option.tagName).toBe('OPTION');
    });
  });

  it('should have all other documents type options available for user when user have document upload role', async () => {
    const roleConfig = {
      seafarer: {
        screening: {
          documentsUpload: true,
        },
      },
      screeningGroup: 'Compliance Employee',
    };
    render(renderSelectedFiledComponent(roleConfig));
    const selfDeclarationOptions = screen.getAllByText('Self Declaration');
    selfDeclarationOptions.forEach((option) => {
      expect(option.tagName).toBe('OPTION');
    });

    const policeDeclarationOptions = screen.getAllByText('Police Declaration');
    policeDeclarationOptions.forEach((option) => {
      expect(option.tagName).toBe('OPTION');
    });

    const otherScreeningDocumentOptions = screen.getAllByText('Other Screening Document');
    otherScreeningDocumentOptions.forEach((option) => {
      expect(option.tagName).toBe('OPTION');
    });
  });

  it('should not have all other documents type options available for user when user have no document upload role', async () => {
    const roleConfig = {
      seafarer: {
        screening: {
          documentsUpload: false,
        },
      },
      screeningGroup: 'Compliance Employee',
    };
    render(renderSelectedFiledComponent(roleConfig));

    const selfDeclarationOptions = screen.queryByText('Self Declaration');
    const policeDeclarationOptions = screen.queryByText('Police Declaration');
    const otherScreeningDocumentOptions = screen.queryByText('Other Screening Document');

    expect(selfDeclarationOptions).toBeNull();
    expect(policeDeclarationOptions).toBeNull();
    expect(otherScreeningDocumentOptions).toBeNull();
  });
});
