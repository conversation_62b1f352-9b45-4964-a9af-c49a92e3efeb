export const seafarerBiodataMockResponse = {
    "response": {
      "seafarerBioDetail": {
        "seafarer_person_id": 78246,
        "rank_id": 7,
        "seafarer_id": 78246,
        "cargo_experience": null,
        "seafarer_person": {
          "first_name": "<PERSON><PERSON><PERSON>",
          "middle_name": null,
          "last_name": "Do**",
          "date_of_birth": "1991-08-10T00:00:00.000Z",
          "place_of_birth": "<PERSON><PERSON><PERSON>,JHARKHAND",
          "height": 170,
          "weight": 65,
          "passports": [
            {
              "number": "Z6000",
              "date_of_issue": "2022-05-11T00:00:00.000Z",
              "date_of_expiry": "2032-05-10T00:00:00.000Z",
              "place_of_issue": "Ranchi",
              "country": {
                "id": 105,
                "alpha2_code": "IN",
                "alpha3_code": "IND",
                "value": "India",
                "numeric_code": 356
              },
              "document": [
                {
                  "id": 419947,
                  "created_at": "2022-06-29T05:52:15.290Z",
                  "updated_at": "2022-06-29T05:52:15.290Z",
                  "seafarer_passport_id": 485782,
                  "name": "1656482085372_passport_pdf.pdf",
                  "mime": "application/pdf"
                }
              ]
            }
          ],
          "seaman_books": [
            {
              "number": "MU77",
              "date_of_issue": "2013-05-30T00:00:00.000Z",
              "place_of_issue": "Mumbai (ex Bombay)",
              "country": {
                "id": 105,
                "alpha2_code": "IN",
                "alpha3_code": "IND",
                "value": "India",
                "numeric_code": 356
              },
              "document": [
                {
                  "id": 776037,
                  "created_at": "2022-03-30T14:00:04.932Z",
                  "updated_at": "2022-03-30T14:00:04.932Z",
                  "seafarer_seaman_book_id": 916923,
                  "name": "16** pd**",
                  "mime": null
                }
              ]
            }
          ],
          "nationality": {
            "id": 109,
            "alpha2_code": "IN",
            "alpha3_code": "IND",
            "value": "Indian",
            "ref_id": null
          }
        },
        "rank": "2ND ENGINEER",
        "seafarer_experience": [
          {
            "rank_id": 7,
            "vessel_name": "Spar Capella",
            "vessel_type": "Bulk Carrier",
            "deadweight_tonnage": 58018,
            "deadweight_gross_registered_tonnage": "D",
            "engine_type": "B&W",
            "brake_horse_power": 11828,
            "start_date": "2021-09-20T00:00:00.000Z",
            "end_date": "2022-03-28T00:00:00.000Z",
            "owner_name": "Spar Shipping AS",
            "vessel_ref_id": 4584,
            "rank": "2ND ENGINEER"
          },
          {
            "rank_id": 8,
            "vessel_name": "Spar Canis",
            "vessel_type": "Bulk Carrier",
            "deadweight_tonnage": 53208,
            "deadweight_gross_registered_tonnage": "D",
            "engine_type": "B&W",
            "brake_horse_power": 12889,
            "start_date": "2020-05-14T00:00:00.000Z",
            "end_date": "2021-01-17T00:00:00.000Z",
            "owner_name": "Spar Shipping AS",
            "vessel_ref_id": 4294,
            "rank": "3RD ENGINEER"
          },
          {
            "rank_id": 8,
            "vessel_name": "Spar Gemini",
            "vessel_type": "Bulk Carrier",
            "deadweight_tonnage": 52987,
            "deadweight_gross_registered_tonnage": "D",
            "engine_type": "B&W",
            "brake_horse_power": 12889,
            "start_date": "2019-07-28T00:00:00.000Z",
            "end_date": "2020-01-18T00:00:00.000Z",
            "owner_name": "Spar Shipping AS",
            "vessel_ref_id": 4332,
            "rank": "3RD ENGINEER"
          },
          {
            "rank_id": 8,
            "vessel_name": "Vishva Ekta",
            "vessel_type": "Bulk Carrier",
            "deadweight_tonnage": 0,
            "deadweight_gross_registered_tonnage": "D",
            "engine_type": "B&W",
            "brake_horse_power": 12900,
            "start_date": "2017-10-14T00:00:00.000Z",
            "end_date": "2018-02-02T00:00:00.000Z",
            "owner_name": "The SCI Ltd",
            "vessel_ref_id": 0,
            "rank": "3RD ENGINEER"
          },
          {
            "rank_id": 8,
            "vessel_name": "Vishva Nidhi",
            "vessel_type": "Bulk Carrier",
            "deadweight_tonnage": 0,
            "deadweight_gross_registered_tonnage": "D",
            "engine_type": "B&W",
            "brake_horse_power": 12710,
            "start_date": "2016-03-03T00:00:00.000Z",
            "end_date": "2016-10-15T00:00:00.000Z",
            "owner_name": "The SCI Ltd",
            "vessel_ref_id": 0,
            "rank": "3RD ENGINEER"
          },
          {
            "rank_id": 9,
            "vessel_name": "Vishva nidhi",
            "vessel_type": "Bulk Carrier",
            "deadweight_tonnage": 0,
            "deadweight_gross_registered_tonnage": "D",
            "engine_type": "B&W",
            "brake_horse_power": 12710,
            "start_date": "2016-01-16T00:00:00.000Z",
            "end_date": "2016-03-03T00:00:00.000Z",
            "owner_name": "The SCI Ltd",
            "vessel_ref_id": 0,
            "rank": "4TH ENGINEER"
          },
          {
            "rank_id": 38,
            "vessel_name": "Deah Shakti",
            "vessel_type": "Oil Tanker",
            "deadweight_tonnage": 0,
            "deadweight_gross_registered_tonnage": "D",
            "engine_type": "SULZER",
            "brake_horse_power": 20464,
            "start_date": "2013-09-11T00:00:00.000Z",
            "end_date": "2014-03-18T00:00:00.000Z",
            "owner_name": "The SCI Ltd",
            "vessel_ref_id": 0,
            "rank": "JUNIOR ENGINEER"
          }
        ]
      },
      "stcwCourses": [
        "30** ( Pa** 'B')",
        "SM** MF**/15**",
        "30** ( Pa** 'B')"
      ],
      "certificateOfCompetency": [
        {
          "coc_certificate_id": 6,
          "certificate_no": "95** 13**",
          "date_of_expiry": "2024-03-17T00:00:00.000Z",
          "certificate_name": "Class 2 (Motor)"
        }
      ],
      "currentRankExp": {
        "currentRankExperienceInDays": 189
      },
      "totalExperienceWithFleet": {
        "totalCompanyExpInDays": 611
      },
      "seafarerPhotoId": 414680
    }
  }