export const seafarerGetResponse = {
  status: 200,
  data: {
    seafarer_person_id: 37025,
    id: 37025,
    hkid: 1522,
    created_at: '2017-03-08T12:29:40.229Z',
    updated_at: '2020-03-13T12:25:41.750Z',
    ref_id: 619,
    rank_id: 1,
    office_id: 1,
    manning_agent_id: null,
    availability_date: '2019-01-04T00:00:00.000Z',
    availability_remarks: 'online',
    not_to_be_employed: false,
    not_to_be_employed_reason: null,
    framo_experience: false,
    ice_conditions_experience: null,
    show_cargo_experience: false,
    show_fml_experience: true,
    cargo_experience: 'General <PERSON>, Bulk,Container.',
    additional_experience: null,
    parent_hkid: null,
    is_parent: null,
    data_quality: 'mandatory_data_missing',
    seafarer_manning_agent: null,
    seafarer_person: {
      id: 37025,
      created_at: '2017-03-08T12:29:40.229Z',
      updated_at: '2022-01-21T02:41:59.413Z',
      screening_status: 'passed',
      created_by: 'PARIS1.0 - Sync',
      created_by_username: 'paris1',
      updated_by: null,
      updated_by_username: null,
      first_name: 'To**',
      middle_name: 'An**',
      last_name: 'Ca**',
      date_of_birth: '1953-10-19T00:00:00.000Z',
      gender: 'male',
      place_of_birth: 'Goa',
      country_of_birth_id: null,
      nationality_id: 105,
      height: 169,
      weight: 65,
      overall_size: 0,
      tshirt_size: 0,
      jacket_size: 0,
      shoe_size: 0,
      smoking: 'unknown',
      vegetarian: 'no',
      nearest_airport: null,
      marital_status: 'single',
      surname_of_spouse: null,
      name_of_spouse: null,
      number_of_children: 2,
      children_names: 'Mi** Ti** M Ca** 06 10 81 Mr Bo** Ca** 18 01 89',
      photo_path: '/ui2004/1112174032_Capt._Tome_-_Vincent_Thomas_Bridge_-_HKID1522',
      created_by_hash: 'hJolEflCGlFhFIaJHtvSXA==',
      updated_by_hash: null,
      current_account_status: null,
      current_journey_status: null,
      current_exam_status: null,
      updated_at_p1: '2020-03-13T20:25:41.750Z',
      created_at_p1: '2020-03-13T20:25:41.750Z',
      country_of_birth: null,
      nationality: {
        id: 105,
        alpha2_code: 'IN',
        alpha3_code: 'IND',
        value: 'Indian',
        ref_id: null,
      },
      bank_accounts: [
        {
          id: 24678,
          created_at: '2021-10-09T13:55:08.298Z',
          updated_at: '2021-10-09T13:55:08.298Z',
          seafarer_person_id: 37025,
          is_primary_payroll_account: false,
          seafarer_is_account_holder: true,
          relationship_with_beneficiary: 'SE**',
          account_holder_first_name: 'Ca** To** An** Ca**',
          account_holder_middle_name: null,
          account_holder_last_name: null,
          account_holder_date_of_birth: null,
          account_holder_gender: null,
          account_holder_nationality_id: null,
          account_holder_address_id: 54169,
          number: '00**',
          bank_name: 'HS**',
          bank_address_id: 54170,
          ifsc_number: null,
          swift_code: null,
          iban_number: null,
          account_holder_nationality: null,
          account_holder_address: {
            id: 54169,
            created_at: '2021-10-09T13:55:08.186Z',
            updated_at: '2021-10-09T13:55:08.186Z',
            postal_zip_code: null,
            country_id: null,
            address1: '22 LU** CO** 86 CO**',
            address2: 'DA** WE** MU** 40** ',
            address3: 'MA** IN**',
            address4: null,
          },
          bank_address: {
            id: 54170,
            created_at: '2021-10-09T13:55:08.186Z',
            updated_at: '2021-10-09T13:55:08.186Z',
            postal_zip_code: null,
            country_id: 103,
            address1: '52 60 MA** GA** RO**',
            address2: 'FO** MU** 40** 00**',
            address3: 'MA**',
            address4: null,
          },
          document: [],
        },
      ],
      passports: [
        {
          id: 42007,
          created_at: '2021-10-09T13:55:08.298Z',
          updated_at: '2021-10-09T13:55:08.298Z',
          seafarer_person_id: 37025,
          number: 'Z 23**',
          place_of_issue: 'Mu**',
          country_id: null,
          date_of_issue: '2012-12-18T00:00:00.000Z',
          date_of_expiry: '2022-07-21T00:00:00.000Z',
          ref_id: 7886,
          doc_path: '/ui2012/1339673140_tome.pdf',
          country: null,
          document: [
            {
              id: 16181,
              name: '1339673140_tome.pdf',
              created_at: '2021-10-09T13:55:08.738Z',
              updated_at: '2021-10-09T13:55:08.738Z',
              mime: null,
            },
          ],
        },
      ],
      photos: [
        {
          id: 31935,
          name: '1112174032_Capt._Tome_-_Vincent_Thomas_Bridge_-_HKID1522',
          created_at: '2021-10-09T13:55:09.137Z',
          updated_at: '2021-10-09T13:55:09.137Z',
          mime: null,
        },
      ],
      seaman_books: [
        {
          id: 66394,
          created_at: '2021-10-09T13:55:08.298Z',
          updated_at: '2021-10-09T13:55:08.298Z',
          seafarer_person_id: 37025,
          country_id: 103,
          number: 'A 76**',
          date_of_issue: '2010-11-20T00:00:00.000Z',
          date_of_expiry: '2024-09-06T00:00:00.000Z',
          is_original: true,
          port_of_issue: 'Bo**',
          ref_id: 10818,
          doc_path: '/ui2018/1521010352_tome_cdc.pdf',
          country: {
            id: 103,
            alpha2_code: 'IN',
            alpha3_code: 'IND',
            value: 'India',
            numeric_code: 356,
          },
          document: [
            {
              id: 30703,
              name: '1521010352_tome_cdc.pdf',
              created_at: '2021-10-09T13:55:08.899Z',
              updated_at: '2021-10-09T13:55:08.899Z',
              mime: null,
            },
          ],
        },
        {
          id: 66395,
          created_at: '2021-10-09T13:55:08.298Z',
          updated_at: '2021-10-09T13:55:08.298Z',
          seafarer_person_id: 37025,
          country_id: 170,
          number: 'CT** 63**',
          date_of_issue: '2013-01-12T00:00:00.000Z',
          date_of_expiry: '2015-08-19T00:00:00.000Z',
          is_original: false,
          port_of_issue: 'PA**',
          ref_id: 126678,
          doc_path: '/ui2013/1373287265_TOME_CARDOZO_PAN_LIC.pdf',
          country: {
            id: 170,
            alpha2_code: 'PA',
            alpha3_code: 'PAN',
            value: 'Panama',
            numeric_code: 591,
          },
          document: [
            {
              id: 30704,
              name: '1373287265_TOME_CARDOZO_PAN_LIC.pdf',
              created_at: '2021-10-09T13:55:09.026Z',
              updated_at: '2021-10-09T13:55:09.026Z',
              mime: null,
            },
          ],
        },
      ],
      seafarer_contacts: [
        {
          id: 70952,
          created_at: '2021-10-09T13:55:08.358Z',
          updated_at: '2021-10-09T13:55:08.358Z',
          seafarer_person_id: 37025,
          contact: '02**',
          contact_type: 'telephone_number',
        },
        {
          id: 70953,
          created_at: '2021-10-09T13:55:08.358Z',
          updated_at: '2021-10-09T13:55:08.358Z',
          seafarer_person_id: 37025,
          contact: '89**',
          contact_type: 'mobile_number',
        },
        {
          id: 70954,
          created_at: '2021-10-09T13:55:08.358Z',
          updated_at: '2021-10-09T13:55:08.358Z',
          seafarer_person_id: 37025,
          contact: 't***@h***',
          contact_type: 'email',
        },
        {
          id: 70950,
          created_at: '2021-09-09T13:55:08.358Z',
          updated_at: '2021-10-09T13:55:08.358Z',
          seafarer_person_id: 37025,
          contact: '<EMAIL>',
          contact_type: 'email',
        },
      ],
      addresses: [
        {
          id: 87165,
          created_at: '2021-10-09T13:55:08.412Z',
          updated_at: '2021-10-09T13:55:08.412Z',
          seafarer_person_id: 37025,
          postal_zip_code: '400018',
          country_id: null,
          state: null,
          city: 'Mumbai',
          building: null,
          other_address: '15** Fl** A 15** Th** Pa** Lo** Pa** Pa** Bu** Ma** Wo**',
          country: null,
        },
        {
          id: 87166,
          created_at: '2021-10-09T13:55:08.412Z',
          updated_at: '2021-10-09T13:55:08.412Z',
          seafarer_person_id: 37025,
          postal_zip_code: null,
          country_id: null,
          state: null,
          city: null,
          building: null,
          other_address: null,
          country: null,
        },
      ],
      family_members: [
        {
          id: 30930,
          created_at: '2021-10-09T13:55:08.466Z',
          updated_at: '2021-10-09T13:55:08.466Z',
          seafarer_person_id: 37025,
          name: 'Ms TI** M',
          surname: 'Ca**',
          relationship: 'Daughter',
          telephone: null,
          mobilephone: null,
          email: null,
          address_id: 54171,
          address: {
            id: 54171,
            created_at: '2021-10-09T13:55:08.186Z',
            updated_at: '2021-10-09T13:55:08.186Z',
            postal_zip_code: null,
            country_id: null,
            address1: null,
            address2: null,
            address3: null,
            address4: '22 Lu** Co** 86 Co** La** Da** W Mu** 40**',
          },
        },
      ],
      seafarer_account_status: [
        {
          id: 482,
          seafarer_person_id: 37025,
          status: 'active',
          created_by: 'M S  Na**',
          p1_user_email: '<EMAIL>',
          created_at: '2001-11-08T16:00:00.000Z',
          updated_at: '2001-11-08T16:00:00.000Z',
        },
      ],
      created_by_user_info: {},
      updated_by_user_info: null,
    },
    seafarer_rank: {
      id: 1,
      value: 'MASTER',
      unit: 'MSTR',
      ref_id: 2000336,
    },
    seafarer_reporting_office: {
      id: 1,
      value: 'Mumbai - Andheri',
      unit: 'BOM',
      ref_id: 2000947,
      ship_party_id: null,
    },
  },
};

export const seafarerWithIceExperienceGetResponse = {
  status: 200,
  data: {
    ...seafarerGetResponse.data,
    ice_conditions_experience: true,
  },
};

export const visaDocumentGetResponse = {
  status: 200,
  data: {
    id: 1243,
    seafarer_person_id: 37025,
    type: 'visa',
    ref_id: 44315,
    doc_path: '/ui2018/1519211744_Scan_(405).pdf',
    is_deleted: false,
    created_at: '2022-01-20T08:27:02.204Z',
    updated_at: '2022-01-20T08:27:02.204Z',
    created_by_hash: 'tdtxxGbm4xpNwj1Y2Oj2G46DW9xugJETeAtdDwn2dxc=',
    updated_by_hash: 'tdtxxGbm4xpNwj1Y2Oj2G46DW9xugJETeAtdDwn2dxc=',
    seafarer_document_id: 22912,
    type_of_visa: ' C1 D ',
    country_id: 236,
    number: 'M 95**',
    date_of_issue: '2015-08-08T00:00:00.000Z',
    date_of_expiry: '2025-03-29T00:00:00.000Z',
    issuing_authority: 'US Consulate, Mumbai',
    rejected: false,
  },
};

export const indosDocumentGetResponse = {
  status: 200,
  data: {
    id: 98,
    seafarer_person_id: 37025,
    type: 'indos',
    ref_id: null,
    doc_path: '/ui2022/seafarer/202201260838151_Screenshot%202021-08-10%20at%204.33.33%20PM.png',
    is_deleted: false,
    created_at: '2022-01-26T08:38:15.371Z',
    updated_at: '2022-01-26T08:38:15.371Z',
    created_by_hash: 'CDB9cacPynYoY51fv9TIQ4izsoQv69jh0UYfzpxXkQ0=',
    updated_by_hash: 'CDB9cacPynYoY51fv9TIQ4izsoQv69jh0UYfzpxXkQ0=',
    seafarer_document_id: 24270,
    issued_by: 'test',
    certificate_no: 'test',
    date_of_issue: '2022-01-26T00:00:00.000Z',
    date_of_expiry: '2023-01-26T00:00:00.000Z',
    is_original: true,
  },
};

export const verificationDocumentGetResponse = {
  status: 200,
  data: {
    id: 181,
    seafarer_person_id: 37025,
    type: 'verification',
    ref_id: 1484629,
    doc_path: '/ui2017/1485517172_coc_tome.pdf',
    is_deleted: false,
    created_at: '2022-01-20T08:27:03.213Z',
    updated_at: '2022-01-20T08:27:03.213Z',
    created_by_hash: 'Oc1qftV7FDzc3TTty/Wm7O1MQRl3awUL5U3lb55i20c=',
    updated_by_hash: 'Oc1qftV7FDzc3TTty/Wm7O1MQRl3awUL5U3lb55i20c=',
    seafarer_document_id: 22916,
    type_of_verification: 'Coc verification',
    date_of_issue: '2017-01-27T00:00:00.000Z',
    date_of_expiry: null,
  },
};

export const endorsementDocumentGetResponse = {
  status: 200,
  data: {
    id: 378,
    seafarer_person_id: 12298,
    type: 'endorsement',
    ref_id: null,
    doc_path: '/ui2022/seafarer/202201210240397_data.pdf',
    is_deleted: false,
    created_at: '2022-01-21T02:40:39.926Z',
    updated_at: '2022-01-21T02:40:39.926Z',
    created_by_hash: 'PMBnDKNjZvDATxvLHJiVYm+8fgyVXmQToYriY7kmxRU=',
    updated_by_hash: 'PMBnDKNjZvDATxvLHJiVYm+8fgyVXmQToYriY7kmxRU=',
    seafarer_document_id: 24165,
    endorsement_id: 1,
    issued_by: null,
    certificate_no: null,
    date_of_issue: null,
    date_of_expiry: null,
    is_original: false,
  },
};

export const medicalDocumentGetResponse = {
  status: 200,
  data: {
    id: 1181,
    seafarer_person_id: 37025,
    type: 'medical',
    ref_id: 2043858,
    doc_path: '/ui2019/1550564574_MX-M464N_20190219_163058.pdf',
    is_deleted: false,
    created_at: '2022-01-20T08:27:04.198Z',
    updated_at: '2022-01-20T08:27:04.198Z',
    created_by_hash: '/VrvD/IC45y61BCYzP/CUP7cpivUiwSE4aCqWadvHv0=',
    updated_by_hash: '/VrvD/IC45y61BCYzP/CUP7cpivUiwSE4aCqWadvHv0=',
    seafarer_document_id: 22918,
    medical_certificate_id: 1,
    issued_by: 'DR BHATIA',
    certificate_no: '1234',
    date_of_issue: '2019-02-01T00:00:00.000Z',
    date_of_expiry: '2020-01-31T00:00:00.000Z',
  },
};

export const drugAndAlcoholDocumentGetResponse = {
  status: 200,
  data: {
    id: 200,
    seafarer_person_id: 12298,
    type: 'drug_alcohol_test',
    ref_id: 10379,
    doc_path: '/ui2020/1580469959_Scan0005.pdf',
    is_deleted: false,
    created_at: '2022-01-20T06:20:52.102Z',
    updated_at: '2022-01-20T06:20:52.102Z',
    created_by_hash: 'Vdr+WVnU1SxqSZVlmiMdPQv01AYU+3f4S2ogqns03JI=',
    updated_by_hash: 'Vdr+WVnU1SxqSZVlmiMdPQv01AYU+3f4S2ogqns03JI=',
    seafarer_document_id: 14646,
    vessel_name: 'Spar Lynx',
    vessel_ref_id: 4225,
    tester: 'DRUG TEST',
    is_result_failed: false,
    date_of_test: '2019-12-13T00:00:00.000Z',
    date_of_expiry: null,
    is_original: false,
  },
};

//missing certificate of competency here

export const stcwDocumentGetResponse = {
  status: 200,
  data: {
    id: 7497,
    seafarer_person_id: 37025,
    type: 'stcw',
    ref_id: 4345,
    doc_path: '/ui2013/1368165545_tome_aff.pdf',
    is_deleted: false,
    created_at: '2022-01-20T08:27:07.133Z',
    updated_at: '2022-01-20T08:27:07.133Z',
    created_by_hash: '69C8+bQcRCCGbxjCg76SA3+ZDauD7OjGUR0pnP6CTOc=',
    updated_by_hash: '69C8+bQcRCCGbxjCg76SA3+ZDauD7OjGUR0pnP6CTOc=',
    seafarer_document_id: 22927,
    stcw_licence_id: 1,
    issued_by: 'Bombay',
    certificate_no: '1234',
    date_of_issue: '1984-12-08T00:00:00.000Z',
    date_of_expiry: null,
    is_original: null,
  },
};

//missing education here
//missing apprenticeship here
//missin training here
//missing pre-sea training here
//missing other course training here
//missing other document here

export const documentListResponses = {
  idDocumentTab: {
    status: 200,
    data: [
      {
        id: 22912,
        seafarer_person_id: 37025,
        type: 'visa',
        ref_id: 44315,
        doc_path: '/ui2018/1519211744_Scan_(405).pdf',
        is_deleted: false,
        created_at: '2022-01-20T08:27:02.161Z',
        updated_at: '2022-01-20T08:27:02.161Z',
        created_by_hash: 'tdtxxGbm4xpNwj1Y2Oj2G46DW9xugJETeAtdDwn2dxc=',
        updated_by_hash: 'tdtxxGbm4xpNwj1Y2Oj2G46DW9xugJETeAtdDwn2dxc=',
        seafarer_doc_medical: null,
        seafarer_doc_verification: null,
        seafarer_doc_visa: {
          id: 1243,
          seafarer_document_id: 22912,
          type_of_visa: ' C1 D ',
          country_id: 236,
          number: 'M 95**',
          date_of_issue: '2015-08-08T00:00:00.000Z',
          date_of_expiry: '2025-03-29T00:00:00.000Z',
          issuing_authority: 'US Consulate, Mumbai',
          rejected: false,
          created_at: '2022-01-20T08:27:02.204Z',
          updated_at: '2022-01-20T08:27:02.204Z',
        },
        seafarer_doc_education: null,
        seafarer_doc_apprenticeship: null,
        seafarer_doc_training: null,
        seafarer_doc_pre_sea_training: null,
        seafarer_doc_stcw: null,
        seafarer_doc_certificate_of_competency: null,
        seafarer_doc_drug_alcohol_test: null,
        seafarer_doc_endorsement: null,
        seafarer_doc_indos: null,
        seafarer_doc_other_course: null,
        seafarer_doc_other_document: null,
      },
      {
        id: 22913,
        seafarer_person_id: 37025,
        type: 'visa',
        ref_id: 321827,
        doc_path: '/ui2016/1459141927_TOME.pdf',
        is_deleted: false,
        created_at: '2022-01-20T08:27:02.247Z',
        updated_at: '2022-01-20T08:27:02.247Z',
        created_by_hash: 'BVHoQysDt9ZFZR6lqs9+UdaeBS+19qaQ9jw0hGeENC8=',
        updated_by_hash: 'BVHoQysDt9ZFZR6lqs9+UdaeBS+19qaQ9jw0hGeENC8=',
        seafarer_doc_medical: null,
        seafarer_doc_verification: null,
        seafarer_doc_visa: {
          id: 1244,
          seafarer_document_id: 22913,
          type_of_visa: 'C MU**',
          country_id: 234,
          number: '18** DA** ST**',
          date_of_issue: '2017-04-04T00:00:00.000Z',
          date_of_expiry: '2021-07-02T00:00:00.000Z',
          issuing_authority: 'UK CONSULATE MUMBAI',
          rejected: false,
          created_at: '2022-01-20T08:27:02.293Z',
          updated_at: '2022-01-20T08:27:02.293Z',
        },
        seafarer_doc_education: null,
        seafarer_doc_apprenticeship: null,
        seafarer_doc_training: null,
        seafarer_doc_pre_sea_training: null,
        seafarer_doc_stcw: null,
        seafarer_doc_certificate_of_competency: null,
        seafarer_doc_drug_alcohol_test: null,
        seafarer_doc_endorsement: null,
        seafarer_doc_indos: null,
        seafarer_doc_other_course: null,
        seafarer_doc_other_document: null,
      },
      {
        id: 22914,
        seafarer_person_id: 37025,
        type: 'visa',
        ref_id: 321834,
        doc_path: '/ui2016/1459142508_uk.pdf',
        is_deleted: false,
        created_at: '2022-01-20T08:27:02.353Z',
        updated_at: '2022-01-20T08:27:02.353Z',
        created_by_hash: 'xjTApDpdaptLapdH0N2tabQkzMiAXxVDyITRDYqa8RI=',
        updated_by_hash: 'xjTApDpdaptLapdH0N2tabQkzMiAXxVDyITRDYqa8RI=',
        seafarer_doc_medical: null,
        seafarer_doc_verification: null,
        seafarer_doc_visa: {
          id: 1245,
          seafarer_document_id: 22914,
          type_of_visa: 'C',
          country_id: 234,
          number: '01**',
          date_of_issue: '2016-12-12T00:00:00.000Z',
          date_of_expiry: '2022-07-28T00:00:00.000Z',
          issuing_authority: 'UK',
          rejected: false,
          created_at: '2022-01-20T08:27:02.396Z',
          updated_at: '2022-01-20T08:27:02.396Z',
        },
        seafarer_doc_education: null,
        seafarer_doc_apprenticeship: null,
        seafarer_doc_training: null,
        seafarer_doc_pre_sea_training: null,
        seafarer_doc_stcw: null,
        seafarer_doc_certificate_of_competency: null,
        seafarer_doc_drug_alcohol_test: null,
        seafarer_doc_endorsement: null,
        seafarer_doc_indos: null,
        seafarer_doc_other_course: null,
        seafarer_doc_other_document: null,
      },
      {
        id: 22915,
        seafarer_person_id: 37025,
        type: 'visa',
        ref_id: 383905,
        doc_path: null,
        is_deleted: false,
        created_at: '2022-01-20T08:27:02.440Z',
        updated_at: '2022-01-20T08:27:02.440Z',
        created_by_hash: 'tdtxxGbm4xpNwj1Y2Oj2G46DW9xugJETeAtdDwn2dxc=',
        updated_by_hash: 'tdtxxGbm4xpNwj1Y2Oj2G46DW9xugJETeAtdDwn2dxc=',
        seafarer_doc_medical: null,
        seafarer_doc_verification: null,
        seafarer_doc_visa: {
          id: 1246,
          seafarer_document_id: 22915,
          type_of_visa: 'MC** EG**',
          country_id: 13,
          number: null,
          date_of_issue: '2016-12-05T00:00:00.000Z',
          date_of_expiry: '2016-02-14T00:00:00.000Z',
          issuing_authority: 'GOVT OF AUSTRALIA',
          rejected: false,
          created_at: '2022-01-20T08:27:02.484Z',
          updated_at: '2022-01-20T08:27:02.484Z',
        },
        seafarer_doc_education: null,
        seafarer_doc_apprenticeship: null,
        seafarer_doc_training: null,
        seafarer_doc_pre_sea_training: null,
        seafarer_doc_stcw: null,
        seafarer_doc_certificate_of_competency: null,
        seafarer_doc_drug_alcohol_test: null,
        seafarer_doc_endorsement: null,
        seafarer_doc_indos: null,
        seafarer_doc_other_course: null,
        seafarer_doc_other_document: null,
      },
      {
        id: 24270,
        seafarer_person_id: 37025,
        type: 'indos',
        ref_id: null,
        doc_path:
          '/ui2022/seafarer/202201260838151_Screenshot%202021-08-10%20at%204.33.33%20PM.png',
        is_deleted: false,
        created_at: '2022-01-26T08:38:15.352Z',
        updated_at: '2022-01-26T08:38:15.352Z',
        created_by_hash: 'CDB9cacPynYoY51fv9TIQ4izsoQv69jh0UYfzpxXkQ0=',
        updated_by_hash: 'CDB9cacPynYoY51fv9TIQ4izsoQv69jh0UYfzpxXkQ0=',
        seafarer_doc_medical: null,
        seafarer_doc_verification: null,
        seafarer_doc_visa: null,
        seafarer_doc_education: null,
        seafarer_doc_apprenticeship: null,
        seafarer_doc_training: null,
        seafarer_doc_pre_sea_training: null,
        seafarer_doc_stcw: null,
        seafarer_doc_certificate_of_competency: null,
        seafarer_doc_drug_alcohol_test: null,
        seafarer_doc_endorsement: null,
        seafarer_doc_indos: {
          id: 98,
          seafarer_document_id: 24270,
          issued_by: 'test',
          certificate_no: 'test',
          date_of_issue: '2022-01-26T00:00:00.000Z',
          date_of_expiry: '2022-01-26T00:00:00.000Z',
          is_original: true,
          created_at: '2022-01-26T08:38:15.371Z',
          updated_at: '2022-01-26T08:38:15.371Z',
        },
        seafarer_doc_other_course: null,
        seafarer_doc_other_document: null,
      },
    ],
  },
  verificationAndEndorsementTab: {
    status: 200,
    data: [
      {
        id: 24158,
        seafarer_person_id: 37440,
        type: 'endorsement',
        ref_id: null,
        doc_path: '/ui2022/seafarer/202201210058153_random_face3.jpg',
        is_deleted: false,
        created_at: '2022-01-21T00:58:15.760Z',
        updated_at: '2022-01-21T00:58:15.760Z',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        seafarer_doc_medical: null,
        seafarer_doc_verification: null,
        seafarer_doc_visa: null,
        seafarer_doc_education: null,
        seafarer_doc_apprenticeship: null,
        seafarer_doc_training: null,
        seafarer_doc_pre_sea_training: null,
        seafarer_doc_stcw: null,
        seafarer_doc_certificate_of_competency: null,
        seafarer_doc_drug_alcohol_test: null,
        seafarer_doc_endorsement: {
          id: 377,
          seafarer_document_id: 24158,
          endorsement_id: 1,
          issued_by: 'brian',
          certificate_no: '1234',
          date_of_issue: '2022-01-31T00:00:00.000Z',
          date_of_expiry: '2022-02-12T00:00:00.000Z',
          is_original: true,
          created_at: '2022-01-21T00:58:15.775Z',
          updated_at: '2022-01-21T00:58:15.775Z',
        },
        seafarer_doc_indos: null,
        seafarer_doc_other_course: null,
        seafarer_doc_other_document: null,
      },
      {
        id: 24203,
        seafarer_person_id: 37440,
        type: 'verification',
        ref_id: null,
        doc_path: '/ui2022/seafarer/202201210532147_random_face2.jpg',
        is_deleted: false,
        created_at: '2022-01-21T05:32:14.869Z',
        updated_at: '2022-01-21T05:32:14.869Z',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        seafarer_doc_medical: null,
        seafarer_doc_verification: {
          id: 210,
          seafarer_document_id: 24203,
          type_of_verification: 'CDC Verification',
          date_of_issue: '2022-12-05T00:00:00.000Z',
          date_of_expiry: '2028-12-03T00:00:00.000Z',
          created_at: '2022-01-21T05:32:14.878Z',
          updated_at: '2022-01-21T05:32:14.878Z',
        },
        seafarer_doc_visa: null,
        seafarer_doc_education: null,
        seafarer_doc_apprenticeship: null,
        seafarer_doc_training: null,
        seafarer_doc_pre_sea_training: null,
        seafarer_doc_stcw: null,
        seafarer_doc_certificate_of_competency: null,
        seafarer_doc_drug_alcohol_test: null,
        seafarer_doc_endorsement: null,
        seafarer_doc_indos: null,
        seafarer_doc_other_course: null,
        seafarer_doc_other_document: null,
      },
      {
        id: 24204,
        seafarer_person_id: 37440,
        type: 'dce_verification',
        ref_id: null,
        doc_path: '/ui2022/seafarer/202201210532147_random_face2.jpg',
        is_deleted: false,
        created_at: '2022-01-21T05:32:14.869Z',
        updated_at: '2022-01-21T05:32:14.869Z',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        seafarer_doc_medical: null,
        seafarer_doc_verification: null,
        seafarer_doc_dce_verification: {
          id: 210,
          seafarer_document_id: 24204,
          type_of_verification: 'DCE Verification',
          date_of_issue: '2022-12-05T00:00:00.000Z',
          date_of_expiry: '2028-12-03T00:00:00.000Z',
          created_at: '2022-01-21T05:32:14.878Z',
          updated_at: '2022-01-21T05:32:14.878Z',
        },
        seafarer_doc_visa: null,
        seafarer_doc_education: null,
        seafarer_doc_apprenticeship: null,
        seafarer_doc_training: null,
        seafarer_doc_pre_sea_training: null,
        seafarer_doc_stcw: null,
        seafarer_doc_certificate_of_competency: null,
        seafarer_doc_drug_alcohol_test: null,
        seafarer_doc_endorsement: null,
        seafarer_doc_indos: null,
        seafarer_doc_other_course: null,
        seafarer_doc_other_document: null,
      },
    ],
  },
  otherDocumentsTab: {
    status: 200,
    data: [
      {
        id: 24462,
        seafarer_person_id: 37440,
        type: 'other_document',
        ref_id: 2899150,
        doc_path: '/ui2022/seafarer/202202090047332_face3.jpeg',
        is_deleted: false,
        created_at: '2022-02-09T00:47:33.326Z',
        updated_at: '2022-02-09T06:40:19.083Z',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        seafarer_doc_medical: null,
        seafarer_doc_verification: null,
        seafarer_doc_visa: null,
        seafarer_doc_education: null,
        seafarer_doc_apprenticeship: null,
        seafarer_doc_training: null,
        seafarer_doc_pre_sea_training: null,
        seafarer_doc_stcw: null,
        seafarer_doc_certificate_of_competency: null,
        seafarer_doc_drug_alcohol_test: null,
        seafarer_doc_endorsement: null,
        seafarer_doc_indos: null,
        seafarer_doc_other_course: null,
        seafarer_doc_other_document: {
          id: 3737,
          seafarer_document_id: 24462,
          other_document_type_id: 2,
          issued_by: 'brian',
          certificate_no: '1234',
          date_of_issue: null,
          date_of_expiry: null,
          is_original: false,
          created_at: '2022-02-09T06:40:19.076Z',
          updated_at: '2022-02-09T06:40:19.076Z',
        },
      },
      {
        id: 24448,
        seafarer_person_id: 37440,
        type: 'medical',
        ref_id: 2043858,
        doc_path: '/ui2022/seafarer/202202090044287_face1.jpeg',
        is_deleted: false,
        created_at: '2022-02-08T00:28:13.814Z',
        updated_at: '2022-02-09T06:44:16.529Z',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        seafarer_doc_medical: {
          id: 1333,
          seafarer_document_id: 24448,
          medical_certificate_id: 3,
          issued_by: 'updated issue by',
          certificate_no: '12347',
          date_of_issue: '2019-02-02T00:00:00.000Z',
          date_of_expiry: '2021-02-01T00:00:00.000Z',
          created_at: '2022-02-09T06:44:16.527Z',
          updated_at: '2022-02-09T06:44:16.527Z',
        },
        seafarer_doc_verification: null,
        seafarer_doc_visa: null,
        seafarer_doc_education: null,
        seafarer_doc_apprenticeship: null,
        seafarer_doc_training: null,
        seafarer_doc_pre_sea_training: null,
        seafarer_doc_stcw: null,
        seafarer_doc_certificate_of_competency: null,
        seafarer_doc_drug_alcohol_test: null,
        seafarer_doc_endorsement: null,
        seafarer_doc_indos: null,
        seafarer_doc_other_course: null,
        seafarer_doc_other_document: null,
      },
      {
        id: 24449,
        seafarer_person_id: 37440,
        type: 'medical',
        ref_id: null,
        doc_path: '/ui2022/seafarer/202202080124037_random_face2.jpg',
        is_deleted: false,
        created_at: '2022-02-08T00:28:26.753Z',
        updated_at: '2022-02-08T01:24:03.854Z',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        seafarer_doc_medical: {
          id: 1320,
          seafarer_document_id: 24449,
          medical_certificate_id: 1,
          issued_by: 'brian123',
          certificate_no: '1234',
          date_of_issue: null,
          date_of_expiry: null,
          created_at: '2022-02-08T01:24:03.850Z',
          updated_at: '2022-02-08T01:24:03.850Z',
        },
        seafarer_doc_verification: null,
        seafarer_doc_visa: null,
        seafarer_doc_education: null,
        seafarer_doc_apprenticeship: null,
        seafarer_doc_training: null,
        seafarer_doc_pre_sea_training: null,
        seafarer_doc_stcw: null,
        seafarer_doc_certificate_of_competency: null,
        seafarer_doc_drug_alcohol_test: null,
        seafarer_doc_endorsement: null,
        seafarer_doc_indos: null,
        seafarer_doc_other_course: null,
        seafarer_doc_other_document: null,
      },
      {
        id: 24452,
        seafarer_person_id: 37440,
        type: 'medical',
        ref_id: null,
        doc_path: '/ui2022/seafarer/202202080125059_random_face3.jpg',
        is_deleted: false,
        created_at: '2022-02-08T01:25:06.105Z',
        updated_at: '2022-02-08T01:25:06.105Z',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        seafarer_doc_medical: {
          id: 1321,
          seafarer_document_id: 24452,
          medical_certificate_id: 1,
          issued_by: null,
          certificate_no: null,
          date_of_issue: null,
          date_of_expiry: null,
          created_at: '2022-02-08T01:25:06.129Z',
          updated_at: '2022-02-08T01:25:06.129Z',
        },
        seafarer_doc_verification: null,
        seafarer_doc_visa: null,
        seafarer_doc_education: null,
        seafarer_doc_apprenticeship: null,
        seafarer_doc_training: null,
        seafarer_doc_pre_sea_training: null,
        seafarer_doc_stcw: null,
        seafarer_doc_certificate_of_competency: null,
        seafarer_doc_drug_alcohol_test: null,
        seafarer_doc_endorsement: null,
        seafarer_doc_indos: null,
        seafarer_doc_other_course: null,
        seafarer_doc_other_document: null,
      },
      {
        id: 24460,
        seafarer_person_id: 37440,
        type: 'medical',
        ref_id: null,
        doc_path: '/ui2022/seafarer/202202090045383_face2.jpeg',
        is_deleted: false,
        created_at: '2022-02-09T00:45:38.546Z',
        updated_at: '2022-02-09T00:45:38.546Z',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        seafarer_doc_medical: {
          id: 1324,
          seafarer_document_id: 24460,
          medical_certificate_id: 1,
          issued_by: 'brian',
          certificate_no: null,
          date_of_issue: null,
          date_of_expiry: null,
          created_at: '2022-02-09T00:45:38.563Z',
          updated_at: '2022-02-09T00:45:38.563Z',
        },
        seafarer_doc_verification: null,
        seafarer_doc_visa: null,
        seafarer_doc_education: null,
        seafarer_doc_apprenticeship: null,
        seafarer_doc_training: null,
        seafarer_doc_pre_sea_training: null,
        seafarer_doc_stcw: null,
        seafarer_doc_certificate_of_competency: null,
        seafarer_doc_drug_alcohol_test: null,
        seafarer_doc_endorsement: null,
        seafarer_doc_indos: null,
        seafarer_doc_other_course: null,
        seafarer_doc_other_document: null,
      },
      {
        id: 24463,
        seafarer_person_id: 37440,
        type: 'medical',
        ref_id: 2899151,
        doc_path: '/ui2022/seafarer/202202090052078_face1.jpeg',
        is_deleted: false,
        created_at: '2022-02-09T00:52:07.910Z',
        updated_at: '2022-02-09T00:52:08.070Z',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        seafarer_doc_medical: {
          id: 1325,
          seafarer_document_id: 24463,
          medical_certificate_id: 1,
          issued_by: 'brian1',
          certificate_no: '1234',
          date_of_issue: null,
          date_of_expiry: null,
          created_at: '2022-02-09T00:52:07.914Z',
          updated_at: '2022-02-09T00:52:07.914Z',
        },
        seafarer_doc_verification: null,
        seafarer_doc_visa: null,
        seafarer_doc_education: null,
        seafarer_doc_apprenticeship: null,
        seafarer_doc_training: null,
        seafarer_doc_pre_sea_training: null,
        seafarer_doc_stcw: null,
        seafarer_doc_certificate_of_competency: null,
        seafarer_doc_drug_alcohol_test: null,
        seafarer_doc_endorsement: null,
        seafarer_doc_indos: null,
        seafarer_doc_other_course: null,
        seafarer_doc_other_document: null,
      },
      {
        id: 24465,
        seafarer_person_id: 37440,
        type: 'medical',
        ref_id: null,
        doc_path: '/ui2022/seafarer/202202090054293_face1.jpeg',
        is_deleted: false,
        created_at: '2022-02-09T00:54:29.420Z',
        updated_at: '2022-02-09T00:54:29.420Z',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        seafarer_doc_medical: {
          id: 1327,
          seafarer_document_id: 24465,
          medical_certificate_id: 1,
          issued_by: 'brian4',
          certificate_no: null,
          date_of_issue: null,
          date_of_expiry: null,
          created_at: '2022-02-09T00:54:29.423Z',
          updated_at: '2022-02-09T00:54:29.423Z',
        },
        seafarer_doc_verification: null,
        seafarer_doc_visa: null,
        seafarer_doc_education: null,
        seafarer_doc_apprenticeship: null,
        seafarer_doc_training: null,
        seafarer_doc_pre_sea_training: null,
        seafarer_doc_stcw: null,
        seafarer_doc_certificate_of_competency: null,
        seafarer_doc_drug_alcohol_test: null,
        seafarer_doc_endorsement: null,
        seafarer_doc_indos: null,
        seafarer_doc_other_course: null,
        seafarer_doc_other_document: null,
      },
      {
        id: 24466,
        seafarer_person_id: 37440,
        type: 'medical',
        ref_id: null,
        doc_path: null,
        is_deleted: false,
        created_at: '2022-02-09T01:02:12.873Z',
        updated_at: '2022-02-09T01:02:12.873Z',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        seafarer_doc_medical: {
          id: 1328,
          seafarer_document_id: 24466,
          medical_certificate_id: 1,
          issued_by: null,
          certificate_no: null,
          date_of_issue: null,
          date_of_expiry: null,
          created_at: '2022-02-09T01:02:12.927Z',
          updated_at: '2022-02-09T01:02:12.927Z',
        },
        seafarer_doc_verification: null,
        seafarer_doc_visa: null,
        seafarer_doc_education: null,
        seafarer_doc_apprenticeship: null,
        seafarer_doc_training: null,
        seafarer_doc_pre_sea_training: null,
        seafarer_doc_stcw: null,
        seafarer_doc_certificate_of_competency: null,
        seafarer_doc_drug_alcohol_test: null,
        seafarer_doc_endorsement: null,
        seafarer_doc_indos: null,
        seafarer_doc_other_course: null,
        seafarer_doc_other_document: null,
      },
      {
        id: 24467,
        seafarer_person_id: 37440,
        type: 'medical',
        ref_id: null,
        doc_path: '/ui2022/seafarer/202202090104565_face3.jpeg',
        is_deleted: false,
        created_at: '2022-02-09T01:04:56.649Z',
        updated_at: '2022-02-09T01:04:56.649Z',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        seafarer_doc_medical: {
          id: 1329,
          seafarer_document_id: 24467,
          medical_certificate_id: 1,
          issued_by: null,
          certificate_no: null,
          date_of_issue: null,
          date_of_expiry: null,
          created_at: '2022-02-09T01:04:56.661Z',
          updated_at: '2022-02-09T01:04:56.661Z',
        },
        seafarer_doc_verification: null,
        seafarer_doc_visa: null,
        seafarer_doc_education: null,
        seafarer_doc_apprenticeship: null,
        seafarer_doc_training: null,
        seafarer_doc_pre_sea_training: null,
        seafarer_doc_stcw: null,
        seafarer_doc_certificate_of_competency: null,
        seafarer_doc_drug_alcohol_test: null,
        seafarer_doc_endorsement: null,
        seafarer_doc_indos: null,
        seafarer_doc_other_course: null,
        seafarer_doc_other_document: null,
      },
      {
        id: 24482,
        seafarer_person_id: 37440,
        type: 'other_course',
        ref_id: null,
        doc_path: '/ui2022/seafarer/202202090306147_face2.jpeg',
        is_deleted: false,
        created_at: '2022-02-09T01:30:02.639Z',
        updated_at: '2022-02-09T03:06:14.803Z',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        seafarer_doc_medical: null,
        seafarer_doc_verification: null,
        seafarer_doc_visa: null,
        seafarer_doc_education: null,
        seafarer_doc_apprenticeship: null,
        seafarer_doc_training: null,
        seafarer_doc_pre_sea_training: null,
        seafarer_doc_stcw: null,
        seafarer_doc_certificate_of_competency: null,
        seafarer_doc_drug_alcohol_test: null,
        seafarer_doc_endorsement: null,
        seafarer_doc_indos: null,
        seafarer_doc_other_course: {
          id: 6419,
          seafarer_document_id: 24482,
          other_course_type_id: 3,
          course_title: 'course name2',
          certificate_no: 'cert no 2',
          date_of_issue: '1993-12-18T00:00:00.000Z',
          date_of_expiry: '2022-02-11T00:00:00.000Z',
          created_at: '2022-02-09T03:06:14.800Z',
          updated_at: '2022-02-09T03:06:14.800Z',
          institute: 'inst2',
        },
        seafarer_doc_other_document: null,
      },
      {
        id: 24481,
        seafarer_person_id: 37440,
        type: 'pre_sea_training',
        ref_id: null,
        doc_path: '/ui2022/seafarer/202202090304591_face3.jpeg',
        is_deleted: false,
        created_at: '2022-02-09T01:29:24.239Z',
        updated_at: '2022-02-09T07:59:33.786Z',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        seafarer_doc_medical: null,
        seafarer_doc_verification: null,
        seafarer_doc_visa: null,
        seafarer_doc_education: null,
        seafarer_doc_apprenticeship: null,
        seafarer_doc_training: null,
        seafarer_doc_pre_sea_training: {
          id: 103,
          seafarer_document_id: 24481,
          pre_sea_training_course_id: 3,
          institute_id: 1,
          grade: null,
          start_date: '2022-02-11T00:00:00.000Z',
          end_date: '2022-02-12T00:00:00.000Z',
          created_at: '2022-02-09T07:59:33.784Z',
          updated_at: '2022-02-09T07:59:33.784Z',
        },
        seafarer_doc_stcw: null,
        seafarer_doc_certificate_of_competency: null,
        seafarer_doc_drug_alcohol_test: null,
        seafarer_doc_endorsement: null,
        seafarer_doc_indos: null,
        seafarer_doc_other_course: null,
        seafarer_doc_other_document: null,
      },
      {
        id: 24484,
        seafarer_person_id: 37440,
        type: 'other_document',
        ref_id: 2899161,
        doc_path: '/ui2022/seafarer/202202090516274_face2.jpeg',
        is_deleted: false,
        created_at: '2022-02-09T01:30:37.674Z',
        updated_at: '2022-02-09T05:16:27.601Z',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        seafarer_doc_medical: null,
        seafarer_doc_verification: null,
        seafarer_doc_visa: null,
        seafarer_doc_education: null,
        seafarer_doc_apprenticeship: null,
        seafarer_doc_training: null,
        seafarer_doc_pre_sea_training: null,
        seafarer_doc_stcw: null,
        seafarer_doc_certificate_of_competency: null,
        seafarer_doc_drug_alcohol_test: null,
        seafarer_doc_endorsement: null,
        seafarer_doc_indos: null,
        seafarer_doc_other_course: null,
        seafarer_doc_other_document: {
          id: 3736,
          seafarer_document_id: 24484,
          other_document_type_id: 1,
          issued_by: 'brian1',
          certificate_no: '12341',
          date_of_issue: '2022-02-08T00:00:00.000Z',
          date_of_expiry: '2022-02-10T00:00:00.000Z',
          is_original: false,
          created_at: '2022-02-09T05:16:27.585Z',
          updated_at: '2022-02-09T05:16:27.585Z',
        },
      },
      {
        id: 24480,
        seafarer_person_id: 37440,
        type: 'training',
        ref_id: null,
        doc_path: '/ui2022/seafarer/202202090304027_face2.jpeg',
        is_deleted: false,
        created_at: '2022-02-09T01:28:48.916Z',
        updated_at: '2022-02-09T07:56:00.080Z',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        seafarer_doc_medical: null,
        seafarer_doc_verification: null,
        seafarer_doc_visa: null,
        seafarer_doc_education: null,
        seafarer_doc_apprenticeship: null,
        seafarer_doc_training: {
          id: 459,
          seafarer_document_id: 24480,
          course: 'some course',
          institute: 'inst',
          grade: '3',
          start_date: '2022-02-10T00:00:00.000Z',
          end_date: '2022-02-11T00:00:00.000Z',
          created_at: '2022-02-09T07:56:00.077Z',
          updated_at: '2022-02-09T07:56:00.077Z',
        },
        seafarer_doc_pre_sea_training: null,
        seafarer_doc_stcw: null,
        seafarer_doc_certificate_of_competency: null,
        seafarer_doc_drug_alcohol_test: null,
        seafarer_doc_endorsement: null,
        seafarer_doc_indos: null,
        seafarer_doc_other_course: null,
        seafarer_doc_other_document: null,
      },
      {
        id: 24461,
        seafarer_person_id: 37440,
        type: 'certificate_of_competency',
        ref_id: null,
        doc_path: '/ui2022/seafarer/202202090252001_face1.jpeg',
        is_deleted: false,
        created_at: '2022-02-09T00:46:24.469Z',
        updated_at: '2022-02-09T07:54:56.617Z',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        seafarer_doc_medical: null,
        seafarer_doc_verification: null,
        seafarer_doc_visa: null,
        seafarer_doc_education: null,
        seafarer_doc_apprenticeship: null,
        seafarer_doc_training: null,
        seafarer_doc_pre_sea_training: null,
        seafarer_doc_stcw: null,
        seafarer_doc_certificate_of_competency: {
          id: 1168,
          seafarer_document_id: 24461,
          country_id: 4,
          coc_certificate_id: 3,
          certificate_no: null,
          date_of_issue: '2022-02-13T00:00:00.000Z',
          date_of_expiry: '2022-03-04T00:00:00.000Z',
          is_original: true,
          created_at: '2022-02-09T07:54:56.614Z',
          updated_at: '2022-02-09T07:54:56.613Z',
        },
        seafarer_doc_drug_alcohol_test: null,
        seafarer_doc_endorsement: null,
        seafarer_doc_indos: null,
        seafarer_doc_other_course: null,
        seafarer_doc_other_document: null,
      },
      {
        id: 24477,
        seafarer_person_id: 37440,
        type: 'education',
        ref_id: null,
        doc_path: '/ui2022/seafarer/202202090300442_face4.jpeg',
        is_deleted: false,
        created_at: '2022-02-09T01:27:28.409Z',
        updated_at: '2022-02-09T07:52:14.116Z',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        seafarer_doc_medical: null,
        seafarer_doc_verification: null,
        seafarer_doc_visa: null,
        seafarer_doc_education: {
          id: 754,
          seafarer_document_id: 24477,
          qualification: 'qual3',
          institute: 'Cochin Shipyard Limited, Kochi',
          class: null,
          pass_date: '2022-01-09T07:52:14.107Z',
          created_at: '2022-02-09T07:52:14.107Z',
          updated_at: '2022-02-09T07:52:14.107Z',
        },
        seafarer_doc_apprenticeship: null,
        seafarer_doc_training: null,
        seafarer_doc_pre_sea_training: null,
        seafarer_doc_stcw: null,
        seafarer_doc_certificate_of_competency: null,
        seafarer_doc_drug_alcohol_test: null,
        seafarer_doc_endorsement: null,
        seafarer_doc_indos: null,
        seafarer_doc_other_course: null,
        seafarer_doc_other_document: null,
      },
      {
        id: 24476,
        seafarer_person_id: 37440,
        type: 'education',
        ref_id: null,
        doc_path: null,
        is_deleted: false,
        created_at: '2022-02-09T01:26:54.472Z',
        updated_at: '2022-02-09T07:56:26.713Z',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        seafarer_doc_medical: null,
        seafarer_doc_verification: null,
        seafarer_doc_visa: null,
        seafarer_doc_education: {
          id: 755,
          seafarer_document_id: 24476,
          qualification: 'qual',
          institute: 'Acadamy of Marine Education & Training (AMET), Chennai',
          class: 'class',
          pass_date: null,
          created_at: '2022-02-09T07:56:26.709Z',
          updated_at: '2022-02-09T07:56:26.709Z',
        },
        seafarer_doc_apprenticeship: null,
        seafarer_doc_training: null,
        seafarer_doc_pre_sea_training: null,
        seafarer_doc_stcw: null,
        seafarer_doc_certificate_of_competency: null,
        seafarer_doc_drug_alcohol_test: null,
        seafarer_doc_endorsement: null,
        seafarer_doc_indos: null,
        seafarer_doc_other_course: null,
        seafarer_doc_other_document: null,
      },
      {
        id: 24493,
        seafarer_person_id: 37440,
        type: 'stcw',
        ref_id: null,
        doc_path: '/ui2022/seafarer/202202090259396_face2.jpeg',
        is_deleted: false,
        created_at: '2022-02-09T02:09:39.310Z',
        updated_at: '2022-02-09T07:56:33.926Z',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        seafarer_doc_medical: null,
        seafarer_doc_verification: null,
        seafarer_doc_visa: null,
        seafarer_doc_education: null,
        seafarer_doc_apprenticeship: null,
        seafarer_doc_training: null,
        seafarer_doc_pre_sea_training: null,
        seafarer_doc_stcw: {
          id: 7800,
          seafarer_document_id: 24493,
          stcw_licence_id: 2,
          issued_by: 'brian1',
          certificate_no: '12345',
          date_of_issue: '1993-12-17T00:00:00.000Z',
          date_of_expiry: '2021-12-04T00:00:00.000Z',
          is_original: false,
          created_at: '2022-02-09T07:56:33.924Z',
          updated_at: '2022-02-09T07:56:33.924Z',
        },
        seafarer_doc_certificate_of_competency: null,
        seafarer_doc_drug_alcohol_test: null,
        seafarer_doc_endorsement: null,
        seafarer_doc_indos: null,
        seafarer_doc_other_course: null,
        seafarer_doc_other_document: null,
      },
      {
        id: 24479,
        seafarer_person_id: 37440,
        type: 'apprenticeship',
        ref_id: 23160,
        doc_path: '/ui2022/seafarer/202202090301414_face3.jpeg',
        is_deleted: false,
        created_at: '2022-02-09T01:28:11.057Z',
        updated_at: '2022-02-09T11:11:29.247Z',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        seafarer_doc_medical: null,
        seafarer_doc_verification: null,
        seafarer_doc_visa: null,
        seafarer_doc_education: null,
        seafarer_doc_apprenticeship: {
          id: 346,
          seafarer_document_id: 24479,
          apprenticeship: 'app3',
          start_date: '2022-02-10T00:00:00.000Z',
          end_date: '2022-02-10T00:00:00.000Z',
          created_at: '2022-02-09T11:11:29.244Z',
          updated_at: '2022-02-09T11:11:29.244Z',
        },
        seafarer_doc_training: null,
        seafarer_doc_pre_sea_training: null,
        seafarer_doc_stcw: null,
        seafarer_doc_certificate_of_competency: null,
        seafarer_doc_drug_alcohol_test: null,
        seafarer_doc_endorsement: null,
        seafarer_doc_indos: null,
        seafarer_doc_other_course: null,
        seafarer_doc_other_document: null,
      },
      {
        id: 24464,
        seafarer_person_id: 37440,
        type: 'medical',
        ref_id: null,
        doc_path: '/ui2022/seafarer/202202090053571_face3.jpeg',
        is_deleted: false,
        created_at: '2022-02-09T00:53:57.213Z',
        updated_at: '2022-02-09T11:12:35.080Z',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        seafarer_doc_medical: {
          id: 1338,
          seafarer_document_id: 24464,
          medical_certificate_id: 3,
          issued_by: 'brian2',
          certificate_no: null,
          date_of_issue: null,
          date_of_expiry: null,
          created_at: '2022-02-09T11:12:35.078Z',
          updated_at: '2022-02-09T11:12:35.078Z',
        },
        seafarer_doc_verification: null,
        seafarer_doc_visa: null,
        seafarer_doc_education: null,
        seafarer_doc_apprenticeship: null,
        seafarer_doc_training: null,
        seafarer_doc_pre_sea_training: null,
        seafarer_doc_stcw: null,
        seafarer_doc_certificate_of_competency: null,
        seafarer_doc_drug_alcohol_test: null,
        seafarer_doc_endorsement: null,
        seafarer_doc_indos: null,
        seafarer_doc_other_course: null,
        seafarer_doc_other_document: null,
      },
      {
        id: 24545,
        seafarer_person_id: 37440,
        type: 'stcw',
        ref_id: null,
        doc_path: null,
        is_deleted: false,
        created_at: '2022-02-09T10:32:53.954Z',
        updated_at: '2022-02-09T10:32:53.954Z',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        seafarer_doc_medical: null,
        seafarer_doc_verification: null,
        seafarer_doc_visa: null,
        seafarer_doc_education: null,
        seafarer_doc_apprenticeship: null,
        seafarer_doc_training: null,
        seafarer_doc_pre_sea_training: null,
        seafarer_doc_stcw: {
          id: 7808,
          seafarer_document_id: 24545,
          stcw_licence_id: 6,
          issued_by: null,
          certificate_no: null,
          date_of_issue: null,
          date_of_expiry: null,
          is_original: false,
          created_at: '2022-02-09T10:32:53.975Z',
          updated_at: '2022-02-09T10:32:53.975Z',
        },
        seafarer_doc_certificate_of_competency: null,
        seafarer_doc_drug_alcohol_test: null,
        seafarer_doc_endorsement: null,
        seafarer_doc_indos: null,
        seafarer_doc_other_course: null,
        seafarer_doc_other_document: null,
      },
      {
        id: 24530,
        seafarer_person_id: 37440,
        type: 'drug_alcohol_test',
        ref_id: null,
        doc_path: null,
        is_deleted: false,
        created_at: '2022-02-09T08:12:29.722Z',
        updated_at: '2022-02-09T08:12:29.722Z',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        seafarer_doc_medical: null,
        seafarer_doc_verification: null,
        seafarer_doc_visa: null,
        seafarer_doc_education: null,
        seafarer_doc_apprenticeship: null,
        seafarer_doc_training: null,
        seafarer_doc_pre_sea_training: null,
        seafarer_doc_stcw: null,
        seafarer_doc_certificate_of_competency: null,
        seafarer_doc_drug_alcohol_test: {
          id: 375,
          seafarer_document_id: 24530,
          vessel_name: '1_Owner+RegisterOwner_withoutSplit',
          vessel_ref_id: 5636,
          tester: null,
          date_of_test: null,
          date_of_expiry: null,
          is_result_failed: null,
          is_original: false,
          created_at: '2022-02-09T08:12:29.741Z',
          updated_at: '2022-02-09T08:12:29.741Z',
        },
        seafarer_doc_endorsement: null,
        seafarer_doc_indos: null,
        seafarer_doc_other_course: null,
        seafarer_doc_other_document: null,
      },
      {
        id: 24532,
        seafarer_person_id: 37440,
        type: 'stcw',
        ref_id: null,
        doc_path: null,
        is_deleted: false,
        created_at: '2022-02-09T08:14:56.449Z',
        updated_at: '2022-02-09T08:14:56.449Z',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        seafarer_doc_medical: null,
        seafarer_doc_verification: null,
        seafarer_doc_visa: null,
        seafarer_doc_education: null,
        seafarer_doc_apprenticeship: null,
        seafarer_doc_training: null,
        seafarer_doc_pre_sea_training: null,
        seafarer_doc_stcw: {
          id: 7801,
          seafarer_document_id: 24532,
          stcw_licence_id: 4,
          issued_by: null,
          certificate_no: null,
          date_of_issue: null,
          date_of_expiry: null,
          is_original: false,
          created_at: '2022-02-09T08:14:56.456Z',
          updated_at: '2022-02-09T08:14:56.456Z',
        },
        seafarer_doc_certificate_of_competency: null,
        seafarer_doc_drug_alcohol_test: null,
        seafarer_doc_endorsement: null,
        seafarer_doc_indos: null,
        seafarer_doc_other_course: null,
        seafarer_doc_other_document: null,
      },
      {
        id: 24533,
        seafarer_person_id: 37440,
        type: 'stcw',
        ref_id: null,
        doc_path: null,
        is_deleted: false,
        created_at: '2022-02-09T08:15:29.186Z',
        updated_at: '2022-02-09T08:15:29.186Z',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        seafarer_doc_medical: null,
        seafarer_doc_verification: null,
        seafarer_doc_visa: null,
        seafarer_doc_education: null,
        seafarer_doc_apprenticeship: null,
        seafarer_doc_training: null,
        seafarer_doc_pre_sea_training: null,
        seafarer_doc_stcw: {
          id: 7802,
          seafarer_document_id: 24533,
          stcw_licence_id: 4,
          issued_by: null,
          certificate_no: null,
          date_of_issue: null,
          date_of_expiry: null,
          is_original: false,
          created_at: '2022-02-09T08:15:29.190Z',
          updated_at: '2022-02-09T08:15:29.190Z',
        },
        seafarer_doc_certificate_of_competency: null,
        seafarer_doc_drug_alcohol_test: null,
        seafarer_doc_endorsement: null,
        seafarer_doc_indos: null,
        seafarer_doc_other_course: null,
        seafarer_doc_other_document: null,
      },
      {
        id: 24534,
        seafarer_person_id: 37440,
        type: 'correspondence_details',
        ref_id: 804858,
        doc_path: null,
        is_deleted: false,
        created_at: '2022-02-27T15:11:13.782Z',
        updated_at: '2022-02-27T15:11:14.415Z',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        seafarer_doc_medical: null,
        seafarer_doc_verification: null,
        seafarer_doc_dce_verification: null,
        seafarer_doc_correspondence_details: {
          id: 365,
          seafarer_document_id: 24534,
          type_of_correspondence_details: '1',
          date_of_issue: '2022-12-05T00:00:00.000Z',
          created_at: '2022-02-27T15:11:13.801Z',
          updated_at: '2022-02-27T15:11:13.801Z',
        },
        seafarer_doc_visa: null,
        seafarer_doc_education: null,
        seafarer_doc_apprenticeship: null,
        seafarer_doc_training: null,
        seafarer_doc_pre_sea_training: null,
        seafarer_doc_stcw: null,
        seafarer_doc_certificate_of_competency: null,
        seafarer_doc_drug_alcohol_test: null,
        seafarer_doc_endorsement: null,
        seafarer_doc_indos: null,
        seafarer_doc_other_course: null,
        seafarer_doc_other_document: null,
        seafarer_doc_user_defined_document: null,
      },
    ],
  },
};

export const documentDropdownResponse = {
  status: 200,
  data: {
    countries: [
      {
        id: 2,
        value: 'Albania',
        alpha2_code: 'AL',
        alpha3_code: 'ALB',
      },
      {
        id: 3,
        value: 'Algeria',
        alpha2_code: 'DZ',
        alpha3_code: 'DZA',
      },
      {
        id: 4,
        value: 'American Samoa',
        alpha2_code: 'AS',
        alpha3_code: 'ASM',
      },
      {
        id: 5,
        value: 'Andorra',
        alpha2_code: 'AD',
        alpha3_code: 'AND',
      },
      {
        id: 6,
        value: 'Angola',
        alpha2_code: 'AO',
        alpha3_code: 'AGO',
      },
      {
        id: 7,
        value: 'Anguilla',
        alpha2_code: 'AI',
        alpha3_code: 'AIA',
      },
      {
        id: 8,
        value: 'Antarctica',
        alpha2_code: 'AQ',
        alpha3_code: 'ATA',
      },
      {
        id: 9,
        value: 'Antigua and Barbuda',
        alpha2_code: 'AG',
        alpha3_code: 'ATG',
      },
      {
        id: 10,
        value: 'Argentina',
        alpha2_code: 'AR',
        alpha3_code: 'ARG',
      },
      {
        id: 11,
        value: 'Armenia',
        alpha2_code: 'AM',
        alpha3_code: 'ARM',
      },
      {
        id: 12,
        value: 'Aruba',
        alpha2_code: 'AW',
        alpha3_code: 'ABW',
      },
      {
        id: 13,
        value: 'Australia',
        alpha2_code: 'AU',
        alpha3_code: 'AUS',
      },
      {
        id: 14,
        value: 'Austria',
        alpha2_code: 'AT',
        alpha3_code: 'AUT',
      },
      {
        id: 15,
        value: 'Azerbaijan',
        alpha2_code: 'AZ',
        alpha3_code: 'AZE',
      },
      {
        id: 16,
        value: 'Bahamas (the)',
        alpha2_code: 'BS',
        alpha3_code: 'BHS',
      },
      {
        id: 17,
        value: 'Bahrain',
        alpha2_code: 'BH',
        alpha3_code: 'BHR',
      },
      {
        id: 18,
        value: 'Bangladesh',
        alpha2_code: 'BD',
        alpha3_code: 'BGD',
      },
      {
        id: 19,
        value: 'Barbados',
        alpha2_code: 'BB',
        alpha3_code: 'BRB',
      },
      {
        id: 20,
        value: 'Belarus',
        alpha2_code: 'BY',
        alpha3_code: 'BLR',
      },
      {
        id: 21,
        value: 'Belgium',
        alpha2_code: 'BE',
        alpha3_code: 'BEL',
      },
      {
        id: 22,
        value: 'Belize',
        alpha2_code: 'BZ',
        alpha3_code: 'BLZ',
      },
      {
        id: 23,
        value: 'Benin',
        alpha2_code: 'BJ',
        alpha3_code: 'BEN',
      },
      {
        id: 24,
        value: 'Bermuda',
        alpha2_code: 'BM',
        alpha3_code: 'BMU',
      },
      {
        id: 25,
        value: 'Bhutan',
        alpha2_code: 'BT',
        alpha3_code: 'BTN',
      },
      {
        id: 26,
        value: 'Bolivia (Plurinational State of)',
        alpha2_code: 'BO',
        alpha3_code: 'BOL',
      },
      {
        id: 27,
        value: 'Bonaire, Sint Eustatius and Saba',
        alpha2_code: 'BQ',
        alpha3_code: 'BES',
      },
      {
        id: 28,
        value: 'Bosnia and Herzegovina',
        alpha2_code: 'BA',
        alpha3_code: 'BIH',
      },
      {
        id: 29,
        value: 'Botswana',
        alpha2_code: 'BW',
        alpha3_code: 'BWA',
      },
      {
        id: 30,
        value: 'Bouvet Island',
        alpha2_code: 'BV',
        alpha3_code: 'BVT',
      },
      {
        id: 31,
        value: 'Brazil',
        alpha2_code: 'BR',
        alpha3_code: 'BRA',
      },
      {
        id: 32,
        value: 'British Indian Ocean Territory (the)',
        alpha2_code: 'IO',
        alpha3_code: 'IOT',
      },
      {
        id: 33,
        value: 'Brunei Darussalam',
        alpha2_code: 'BN',
        alpha3_code: 'BRN',
      },
      {
        id: 34,
        value: 'Bulgaria',
        alpha2_code: 'BG',
        alpha3_code: 'BGR',
      },
      {
        id: 35,
        value: 'Burkina Faso',
        alpha2_code: 'BF',
        alpha3_code: 'BFA',
      },
      {
        id: 36,
        value: 'Burundi',
        alpha2_code: 'BI',
        alpha3_code: 'BDI',
      },
      {
        id: 37,
        value: 'Cabo Verde',
        alpha2_code: 'CV',
        alpha3_code: 'CPV',
      },
      {
        id: 38,
        value: 'Cambodia',
        alpha2_code: 'KH',
        alpha3_code: 'KHM',
      },
      {
        id: 39,
        value: 'Cameroon',
        alpha2_code: 'CM',
        alpha3_code: 'CMR',
      },
      {
        id: 40,
        value: 'Canada',
        alpha2_code: 'CA',
        alpha3_code: 'CAN',
      },
      {
        id: 41,
        value: 'Cayman Islands (the)',
        alpha2_code: 'KY',
        alpha3_code: 'CYM',
      },
      {
        id: 42,
        value: 'Central African Republic (the)',
        alpha2_code: 'CF',
        alpha3_code: 'CAF',
      },
      {
        id: 43,
        value: 'Chad',
        alpha2_code: 'TD',
        alpha3_code: 'TCD',
      },
      {
        id: 44,
        value: 'Chile',
        alpha2_code: 'CL',
        alpha3_code: 'CHL',
      },
      {
        id: 45,
        value: 'China',
        alpha2_code: 'CN',
        alpha3_code: 'CHN',
      },
      {
        id: 46,
        value: 'Christmas Island',
        alpha2_code: 'CX',
        alpha3_code: 'CXR',
      },
      {
        id: 47,
        value: 'Cocos (Keeling) Islands (the)',
        alpha2_code: 'CC',
        alpha3_code: 'CCK',
      },
      {
        id: 48,
        value: 'Colombia',
        alpha2_code: 'CO',
        alpha3_code: 'COL',
      },
      {
        id: 49,
        value: 'Comoros (the)',
        alpha2_code: 'KM',
        alpha3_code: 'COM',
      },
      {
        id: 50,
        value: 'Congo (the Democratic Republic of the)',
        alpha2_code: 'CD',
        alpha3_code: 'COD',
      },
      {
        id: 51,
        value: 'Congo (the)',
        alpha2_code: 'CG',
        alpha3_code: 'COG',
      },
      {
        id: 52,
        value: 'Cook Islands (the)',
        alpha2_code: 'CK',
        alpha3_code: 'COK',
      },
      {
        id: 53,
        value: 'Costa Rica',
        alpha2_code: 'CR',
        alpha3_code: 'CRI',
      },
      {
        id: 54,
        value: 'Croatia',
        alpha2_code: 'HR',
        alpha3_code: 'HRV',
      },
      {
        id: 55,
        value: 'Cuba',
        alpha2_code: 'CU',
        alpha3_code: 'CUB',
      },
      {
        id: 56,
        value: 'Curaçao',
        alpha2_code: 'CW',
        alpha3_code: 'CUW',
      },
      {
        id: 57,
        value: 'Cyprus',
        alpha2_code: 'CY',
        alpha3_code: 'CYP',
      },
      {
        id: 58,
        value: 'Czechia',
        alpha2_code: 'CZ',
        alpha3_code: 'CZE',
      },
      {
        id: 59,
        value: "Côte d'Ivoire",
        alpha2_code: 'CI',
        alpha3_code: 'CIV',
      },
      {
        id: 60,
        value: 'Denmark',
        alpha2_code: 'DK',
        alpha3_code: 'DNK',
      },
      {
        id: 61,
        value: 'Djibouti',
        alpha2_code: 'DJ',
        alpha3_code: 'DJI',
      },
      {
        id: 62,
        value: 'Dominica',
        alpha2_code: 'DM',
        alpha3_code: 'DMA',
      },
      {
        id: 63,
        value: 'Dominican Republic (the)',
        alpha2_code: 'DO',
        alpha3_code: 'DOM',
      },
      {
        id: 64,
        value: 'Ecuador',
        alpha2_code: 'EC',
        alpha3_code: 'ECU',
      },
      {
        id: 65,
        value: 'Egypt',
        alpha2_code: 'EG',
        alpha3_code: 'EGY',
      },
      {
        id: 66,
        value: 'El Salvador',
        alpha2_code: 'SV',
        alpha3_code: 'SLV',
      },
      {
        id: 67,
        value: 'Equatorial Guinea',
        alpha2_code: 'GQ',
        alpha3_code: 'GNQ',
      },
      {
        id: 68,
        value: 'Eritrea',
        alpha2_code: 'ER',
        alpha3_code: 'ERI',
      },
      {
        id: 69,
        value: 'Estonia',
        alpha2_code: 'EE',
        alpha3_code: 'EST',
      },
      {
        id: 70,
        value: 'Eswatini',
        alpha2_code: 'SZ',
        alpha3_code: 'SWZ',
      },
      {
        id: 71,
        value: 'Ethiopia',
        alpha2_code: 'ET',
        alpha3_code: 'ETH',
      },
      {
        id: 72,
        value: 'Falkland Islands (the) [Malvinas]',
        alpha2_code: 'FK',
        alpha3_code: 'FLK',
      },
      {
        id: 73,
        value: 'Faroe Islands (the)',
        alpha2_code: 'FO',
        alpha3_code: 'FRO',
      },
      {
        id: 74,
        value: 'Fiji',
        alpha2_code: 'FJ',
        alpha3_code: 'FJI',
      },
      {
        id: 75,
        value: 'Finland',
        alpha2_code: 'FI',
        alpha3_code: 'FIN',
      },
      {
        id: 76,
        value: 'France',
        alpha2_code: 'FR',
        alpha3_code: 'FRA',
      },
      {
        id: 77,
        value: 'French Guiana',
        alpha2_code: 'GF',
        alpha3_code: 'GUF',
      },
      {
        id: 78,
        value: 'French Polynesia',
        alpha2_code: 'PF',
        alpha3_code: 'PYF',
      },
      {
        id: 79,
        value: 'French Southern Territories (the)',
        alpha2_code: 'TF',
        alpha3_code: 'ATF',
      },
      {
        id: 80,
        value: 'Gabon',
        alpha2_code: 'GA',
        alpha3_code: 'GAB',
      },
      {
        id: 81,
        value: 'Gambia (the)',
        alpha2_code: 'GM',
        alpha3_code: 'GMB',
      },
      {
        id: 82,
        value: 'Georgia',
        alpha2_code: 'GE',
        alpha3_code: 'GEO',
      },
      {
        id: 83,
        value: 'Germany',
        alpha2_code: 'DE',
        alpha3_code: 'DEU',
      },
      {
        id: 84,
        value: 'Ghana',
        alpha2_code: 'GH',
        alpha3_code: 'GHA',
      },
      {
        id: 85,
        value: 'Gibraltar',
        alpha2_code: 'GI',
        alpha3_code: 'GIB',
      },
      {
        id: 86,
        value: 'Greece',
        alpha2_code: 'GR',
        alpha3_code: 'GRC',
      },
      {
        id: 87,
        value: 'Greenland',
        alpha2_code: 'GL',
        alpha3_code: 'GRL',
      },
      {
        id: 88,
        value: 'Grenada',
        alpha2_code: 'GD',
        alpha3_code: 'GRD',
      },
      {
        id: 89,
        value: 'Guadeloupe',
        alpha2_code: 'GP',
        alpha3_code: 'GLP',
      },
      {
        id: 90,
        value: 'Guam',
        alpha2_code: 'GU',
        alpha3_code: 'GUM',
      },
      {
        id: 91,
        value: 'Guatemala',
        alpha2_code: 'GT',
        alpha3_code: 'GTM',
      },
      {
        id: 92,
        value: 'Guernsey',
        alpha2_code: 'GG',
        alpha3_code: 'GGY',
      },
      {
        id: 93,
        value: 'Guinea',
        alpha2_code: 'GN',
        alpha3_code: 'GIN',
      },
      {
        id: 94,
        value: 'Guinea-Bissau',
        alpha2_code: 'GW',
        alpha3_code: 'GNB',
      },
      {
        id: 95,
        value: 'Guyana',
        alpha2_code: 'GY',
        alpha3_code: 'GUY',
      },
      {
        id: 96,
        value: 'Haiti',
        alpha2_code: 'HT',
        alpha3_code: 'HTI',
      },
      {
        id: 97,
        value: 'Heard Island and McDonald Islands',
        alpha2_code: 'HM',
        alpha3_code: 'HMD',
      },
      {
        id: 98,
        value: 'Holy See (the)',
        alpha2_code: 'VA',
        alpha3_code: 'VAT',
      },
      {
        id: 99,
        value: 'Honduras',
        alpha2_code: 'HN',
        alpha3_code: 'HND',
      },
      {
        id: 100,
        value: 'Hong Kong',
        alpha2_code: 'HK',
        alpha3_code: 'HKG',
      },
      {
        id: 101,
        value: 'Hungary',
        alpha2_code: 'HU',
        alpha3_code: 'HUN',
      },
      {
        id: 102,
        value: 'Iceland',
        alpha2_code: 'IS',
        alpha3_code: 'ISL',
      },
      {
        id: 103,
        value: 'India',
        alpha2_code: 'IN',
        alpha3_code: 'IND',
      },
      {
        id: 104,
        value: 'Indonesia',
        alpha2_code: 'ID',
        alpha3_code: 'IDN',
      },
      {
        id: 105,
        value: 'Iran (Islamic Republic of)',
        alpha2_code: 'IR',
        alpha3_code: 'IRN',
      },
      {
        id: 106,
        value: 'Iraq',
        alpha2_code: 'IQ',
        alpha3_code: 'IRQ',
      },
      {
        id: 107,
        value: 'Ireland',
        alpha2_code: 'IE',
        alpha3_code: 'IRL',
      },
      {
        id: 108,
        value: 'Isle of Man',
        alpha2_code: 'IM',
        alpha3_code: 'IMN',
      },
      {
        id: 109,
        value: 'Israel',
        alpha2_code: 'IL',
        alpha3_code: 'ISR',
      },
      {
        id: 110,
        value: 'Italy',
        alpha2_code: 'IT',
        alpha3_code: 'ITA',
      },
      {
        id: 111,
        value: 'Jamaica',
        alpha2_code: 'JM',
        alpha3_code: 'JAM',
      },
      {
        id: 112,
        value: 'Japan',
        alpha2_code: 'JP',
        alpha3_code: 'JPN',
      },
      {
        id: 113,
        value: 'Jersey',
        alpha2_code: 'JE',
        alpha3_code: 'JEY',
      },
      {
        id: 114,
        value: 'Jordan',
        alpha2_code: 'JO',
        alpha3_code: 'JOR',
      },
      {
        id: 115,
        value: 'Kazakhstan',
        alpha2_code: 'KZ',
        alpha3_code: 'KAZ',
      },
      {
        id: 116,
        value: 'Kenya',
        alpha2_code: 'KE',
        alpha3_code: 'KEN',
      },
      {
        id: 117,
        value: 'Kiribati',
        alpha2_code: 'KI',
        alpha3_code: 'KIR',
      },
      {
        id: 118,
        value: "Korea (the Democratic People's Republic of)",
        alpha2_code: 'KP',
        alpha3_code: 'PRK',
      },
      {
        id: 119,
        value: 'Korea (the Republic of)',
        alpha2_code: 'KR',
        alpha3_code: 'KOR',
      },
      {
        id: 120,
        value: 'Kuwait',
        alpha2_code: 'KW',
        alpha3_code: 'KWT',
      },
      {
        id: 121,
        value: 'Kyrgyzstan',
        alpha2_code: 'KG',
        alpha3_code: 'KGZ',
      },
      {
        id: 122,
        value: "Lao People's Democratic Republic (the)",
        alpha2_code: 'LA',
        alpha3_code: 'LAO',
      },
      {
        id: 123,
        value: 'Latvia',
        alpha2_code: 'LV',
        alpha3_code: 'LVA',
      },
      {
        id: 124,
        value: 'Lebanon',
        alpha2_code: 'LB',
        alpha3_code: 'LBN',
      },
      {
        id: 125,
        value: 'Lesotho',
        alpha2_code: 'LS',
        alpha3_code: 'LSO',
      },
      {
        id: 126,
        value: 'Liberia',
        alpha2_code: 'LR',
        alpha3_code: 'LBR',
      },
      {
        id: 127,
        value: 'Libya',
        alpha2_code: 'LY',
        alpha3_code: 'LBY',
      },
      {
        id: 128,
        value: 'Liechtenstein',
        alpha2_code: 'LI',
        alpha3_code: 'LIE',
      },
      {
        id: 129,
        value: 'Lithuania',
        alpha2_code: 'LT',
        alpha3_code: 'LTU',
      },
      {
        id: 130,
        value: 'Luxembourg',
        alpha2_code: 'LU',
        alpha3_code: 'LUX',
      },
      {
        id: 131,
        value: 'Macao',
        alpha2_code: 'MO',
        alpha3_code: 'MAC',
      },
      {
        id: 132,
        value: 'Madagascar',
        alpha2_code: 'MG',
        alpha3_code: 'MDG',
      },
      {
        id: 133,
        value: 'Malawi',
        alpha2_code: 'MW',
        alpha3_code: 'MWI',
      },
      {
        id: 134,
        value: 'Malaysia',
        alpha2_code: 'MY',
        alpha3_code: 'MYS',
      },
      {
        id: 135,
        value: 'Maldives',
        alpha2_code: 'MV',
        alpha3_code: 'MDV',
      },
      {
        id: 136,
        value: 'Mali',
        alpha2_code: 'ML',
        alpha3_code: 'MLI',
      },
      {
        id: 137,
        value: 'Malta',
        alpha2_code: 'MT',
        alpha3_code: 'MLT',
      },
      {
        id: 138,
        value: 'Marshall Islands (the)',
        alpha2_code: 'MH',
        alpha3_code: 'MHL',
      },
      {
        id: 139,
        value: 'Martinique',
        alpha2_code: 'MQ',
        alpha3_code: 'MTQ',
      },
      {
        id: 140,
        value: 'Mauritania',
        alpha2_code: 'MR',
        alpha3_code: 'MRT',
      },
      {
        id: 141,
        value: 'Mauritius',
        alpha2_code: 'MU',
        alpha3_code: 'MUS',
      },
      {
        id: 142,
        value: 'Mayotte',
        alpha2_code: 'YT',
        alpha3_code: 'MYT',
      },
      {
        id: 143,
        value: 'Mexico',
        alpha2_code: 'MX',
        alpha3_code: 'MEX',
      },
      {
        id: 144,
        value: 'Micronesia (Federated States of)',
        alpha2_code: 'FM',
        alpha3_code: 'FSM',
      },
      {
        id: 145,
        value: 'Moldova (the Republic of)',
        alpha2_code: 'MD',
        alpha3_code: 'MDA',
      },
      {
        id: 146,
        value: 'Monaco',
        alpha2_code: 'MC',
        alpha3_code: 'MCO',
      },
      {
        id: 147,
        value: 'Mongolia',
        alpha2_code: 'MN',
        alpha3_code: 'MNG',
      },
      {
        id: 148,
        value: 'Montenegro',
        alpha2_code: 'ME',
        alpha3_code: 'MNE',
      },
      {
        id: 149,
        value: 'Montserrat',
        alpha2_code: 'MS',
        alpha3_code: 'MSR',
      },
      {
        id: 150,
        value: 'Morocco',
        alpha2_code: 'MA',
        alpha3_code: 'MAR',
      },
      {
        id: 151,
        value: 'Mozambique',
        alpha2_code: 'MZ',
        alpha3_code: 'MOZ',
      },
      {
        id: 152,
        value: 'Myanmar',
        alpha2_code: 'MM',
        alpha3_code: 'MMR',
      },
      {
        id: 153,
        value: 'Namibia',
        alpha2_code: 'NA',
        alpha3_code: 'NAM',
      },
      {
        id: 154,
        value: 'Nauru',
        alpha2_code: 'NR',
        alpha3_code: 'NRU',
      },
      {
        id: 155,
        value: 'Nepal',
        alpha2_code: 'NP',
        alpha3_code: 'NPL',
      },
      {
        id: 156,
        value: 'Netherlands (the)',
        alpha2_code: 'NL',
        alpha3_code: 'NLD',
      },
      {
        id: 157,
        value: 'New Caledonia',
        alpha2_code: 'NC',
        alpha3_code: 'NCL',
      },
      {
        id: 158,
        value: 'New Zealand',
        alpha2_code: 'NZ',
        alpha3_code: 'NZL',
      },
      {
        id: 159,
        value: 'Nicaragua',
        alpha2_code: 'NI',
        alpha3_code: 'NIC',
      },
      {
        id: 160,
        value: 'Niger (the)',
        alpha2_code: 'NE',
        alpha3_code: 'NER',
      },
      {
        id: 161,
        value: 'Nigeria',
        alpha2_code: 'NG',
        alpha3_code: 'NGA',
      },
      {
        id: 162,
        value: 'Niue',
        alpha2_code: 'NU',
        alpha3_code: 'NIU',
      },
      {
        id: 163,
        value: 'Norfolk Island',
        alpha2_code: 'NF',
        alpha3_code: 'NFK',
      },
      {
        id: 164,
        value: 'Northern Mariana Islands (the)',
        alpha2_code: 'MP',
        alpha3_code: 'MNP',
      },
      {
        id: 165,
        value: 'Norway',
        alpha2_code: 'NO',
        alpha3_code: 'NOR',
      },
      {
        id: 166,
        value: 'Oman',
        alpha2_code: 'OM',
        alpha3_code: 'OMN',
      },
      {
        id: 167,
        value: 'Pakistan',
        alpha2_code: 'PK',
        alpha3_code: 'PAK',
      },
      {
        id: 168,
        value: 'Palau',
        alpha2_code: 'PW',
        alpha3_code: 'PLW',
      },
      {
        id: 169,
        value: 'Palestine, State of',
        alpha2_code: 'PS',
        alpha3_code: 'PSE',
      },
      {
        id: 170,
        value: 'Panama',
        alpha2_code: 'PA',
        alpha3_code: 'PAN',
      },
      {
        id: 171,
        value: 'Papua New Guinea',
        alpha2_code: 'PG',
        alpha3_code: 'PNG',
      },
      {
        id: 172,
        value: 'Paraguay',
        alpha2_code: 'PY',
        alpha3_code: 'PRY',
      },
      {
        id: 173,
        value: 'Peru',
        alpha2_code: 'PE',
        alpha3_code: 'PER',
      },
      {
        id: 174,
        value: 'Philippines (the)',
        alpha2_code: 'PH',
        alpha3_code: 'PHL',
      },
      {
        id: 175,
        value: 'Pitcairn',
        alpha2_code: 'PN',
        alpha3_code: 'PCN',
      },
      {
        id: 176,
        value: 'Poland',
        alpha2_code: 'PL',
        alpha3_code: 'POL',
      },
      {
        id: 177,
        value: 'Portugal',
        alpha2_code: 'PT',
        alpha3_code: 'PRT',
      },
      {
        id: 178,
        value: 'Puerto Rico',
        alpha2_code: 'PR',
        alpha3_code: 'PRI',
      },
      {
        id: 179,
        value: 'Qatar',
        alpha2_code: 'QA',
        alpha3_code: 'QAT',
      },
      {
        id: 180,
        value: 'Republic of North Macedonia',
        alpha2_code: 'MK',
        alpha3_code: 'MKD',
      },
      {
        id: 181,
        value: 'Romania',
        alpha2_code: 'RO',
        alpha3_code: 'ROU',
      },
      {
        id: 182,
        value: 'Russian Federation (the)',
        alpha2_code: 'RU',
        alpha3_code: 'RUS',
      },
      {
        id: 183,
        value: 'Rwanda',
        alpha2_code: 'RW',
        alpha3_code: 'RWA',
      },
      {
        id: 184,
        value: 'Réunion',
        alpha2_code: 'RE',
        alpha3_code: 'REU',
      },
      {
        id: 185,
        value: 'Saint Barthélemy',
        alpha2_code: 'BL',
        alpha3_code: 'BLM',
      },
      {
        id: 186,
        value: 'Saint Helena, Ascension and Tristan da Cunha',
        alpha2_code: 'SH',
        alpha3_code: 'SHN',
      },
      {
        id: 187,
        value: 'Saint Kitts and Nevis',
        alpha2_code: 'KN',
        alpha3_code: 'KNA',
      },
      {
        id: 188,
        value: 'Saint Lucia',
        alpha2_code: 'LC',
        alpha3_code: 'LCA',
      },
      {
        id: 189,
        value: 'Saint Martin (French part)',
        alpha2_code: 'MF',
        alpha3_code: 'MAF',
      },
      {
        id: 190,
        value: 'Saint Pierre and Miquelon',
        alpha2_code: 'PM',
        alpha3_code: 'SPM',
      },
      {
        id: 191,
        value: 'Saint Vincent and the Grenadines',
        alpha2_code: 'VC',
        alpha3_code: 'VCT',
      },
      {
        id: 192,
        value: 'Samoa',
        alpha2_code: 'WS',
        alpha3_code: 'WSM',
      },
      {
        id: 193,
        value: 'San Marino',
        alpha2_code: 'SM',
        alpha3_code: 'SMR',
      },
      {
        id: 194,
        value: 'Sao Tome and Principe',
        alpha2_code: 'ST',
        alpha3_code: 'STP',
      },
      {
        id: 195,
        value: 'Saudi Arabia',
        alpha2_code: 'SA',
        alpha3_code: 'SAU',
      },
      {
        id: 196,
        value: 'Senegal',
        alpha2_code: 'SN',
        alpha3_code: 'SEN',
      },
      {
        id: 197,
        value: 'Serbia',
        alpha2_code: 'RS',
        alpha3_code: 'SRB',
      },
      {
        id: 198,
        value: 'Seychelles',
        alpha2_code: 'SC',
        alpha3_code: 'SYC',
      },
      {
        id: 199,
        value: 'Sierra Leone',
        alpha2_code: 'SL',
        alpha3_code: 'SLE',
      },
      {
        id: 200,
        value: 'Singapore',
        alpha2_code: 'SG',
        alpha3_code: 'SGP',
      },
      {
        id: 201,
        value: 'Sint Maarten (Dutch part)',
        alpha2_code: 'SX',
        alpha3_code: 'SXM',
      },
      {
        id: 202,
        value: 'Slovakia',
        alpha2_code: 'SK',
        alpha3_code: 'SVK',
      },
      {
        id: 203,
        value: 'Slovenia',
        alpha2_code: 'SI',
        alpha3_code: 'SVN',
      },
      {
        id: 204,
        value: 'Solomon Islands',
        alpha2_code: 'SB',
        alpha3_code: 'SLB',
      },
      {
        id: 205,
        value: 'Somalia',
        alpha2_code: 'SO',
        alpha3_code: 'SOM',
      },
      {
        id: 206,
        value: 'South Africa',
        alpha2_code: 'ZA',
        alpha3_code: 'ZAF',
      },
      {
        id: 207,
        value: 'South Georgia and the South Sandwich Islands',
        alpha2_code: 'GS',
        alpha3_code: 'SGS',
      },
      {
        id: 208,
        value: 'South Sudan',
        alpha2_code: 'SS',
        alpha3_code: 'SSD',
      },
      {
        id: 209,
        value: 'Spain',
        alpha2_code: 'ES',
        alpha3_code: 'ESP',
      },
      {
        id: 210,
        value: 'Sri Lanka',
        alpha2_code: 'LK',
        alpha3_code: 'LKA',
      },
      {
        id: 211,
        value: 'Sudan (the)',
        alpha2_code: 'SD',
        alpha3_code: 'SDN',
      },
      {
        id: 212,
        value: 'Suriname',
        alpha2_code: 'SR',
        alpha3_code: 'SUR',
      },
      {
        id: 213,
        value: 'Svalbard and Jan Mayen',
        alpha2_code: 'SJ',
        alpha3_code: 'SJM',
      },
      {
        id: 214,
        value: 'Sweden',
        alpha2_code: 'SE',
        alpha3_code: 'SWE',
      },
      {
        id: 215,
        value: 'Switzerland',
        alpha2_code: 'CH',
        alpha3_code: 'CHE',
      },
      {
        id: 216,
        value: 'Syrian Arab Republic',
        alpha2_code: 'SY',
        alpha3_code: 'SYR',
      },
      {
        id: 217,
        value: 'Taiwan',
        alpha2_code: 'TW',
        alpha3_code: 'TWN',
      },
      {
        id: 218,
        value: 'Tajikistan',
        alpha2_code: 'TJ',
        alpha3_code: 'TJK',
      },
      {
        id: 219,
        value: 'Tanzania, United Republic of',
        alpha2_code: 'TZ',
        alpha3_code: 'TZA',
      },
      {
        id: 220,
        value: 'Thailand',
        alpha2_code: 'TH',
        alpha3_code: 'THA',
      },
      {
        id: 221,
        value: 'Timor-Leste',
        alpha2_code: 'TL',
        alpha3_code: 'TLS',
      },
      {
        id: 222,
        value: 'Togo',
        alpha2_code: 'TG',
        alpha3_code: 'TGO',
      },
      {
        id: 223,
        value: 'Tokelau',
        alpha2_code: 'TK',
        alpha3_code: 'TKL',
      },
      {
        id: 224,
        value: 'Tonga',
        alpha2_code: 'TO',
        alpha3_code: 'TON',
      },
      {
        id: 225,
        value: 'Trinidad and Tobago',
        alpha2_code: 'TT',
        alpha3_code: 'TTO',
      },
      {
        id: 226,
        value: 'Tunisia',
        alpha2_code: 'TN',
        alpha3_code: 'TUN',
      },
      {
        id: 227,
        value: 'Turkey',
        alpha2_code: 'TR',
        alpha3_code: 'TUR',
      },
      {
        id: 228,
        value: 'Turkmenistan',
        alpha2_code: 'TM',
        alpha3_code: 'TKM',
      },
      {
        id: 229,
        value: 'Turks and Caicos Islands (the)',
        alpha2_code: 'TC',
        alpha3_code: 'TCA',
      },
      {
        id: 230,
        value: 'Tuvalu',
        alpha2_code: 'TV',
        alpha3_code: 'TUV',
      },
      {
        id: 231,
        value: 'Uganda',
        alpha2_code: 'UG',
        alpha3_code: 'UGA',
      },
      {
        id: 232,
        value: 'Ukraine',
        alpha2_code: 'UA',
        alpha3_code: 'UKR',
      },
      {
        id: 233,
        value: 'United Arab Emirates (the)',
        alpha2_code: 'AE',
        alpha3_code: 'ARE',
      },
      {
        id: 234,
        value: 'United Kingdom of Great Britain and Northern Ireland (the)',
        alpha2_code: 'GB',
        alpha3_code: 'GBR',
      },
      {
        id: 235,
        value: 'United States Minor Outlying Islands (the)',
        alpha2_code: 'UM',
        alpha3_code: 'UMI',
      },
      {
        id: 236,
        value: 'United States of America (the)',
        alpha2_code: 'US',
        alpha3_code: 'USA',
      },
      {
        id: 237,
        value: 'Uruguay',
        alpha2_code: 'UY',
        alpha3_code: 'URY',
      },
      {
        id: 238,
        value: 'Uzbekistan',
        alpha2_code: 'UZ',
        alpha3_code: 'UZB',
      },
      {
        id: 239,
        value: 'Vanuatu',
        alpha2_code: 'VU',
        alpha3_code: 'VUT',
      },
      {
        id: 240,
        value: 'Venezuela (Bolivarian Republic of)',
        alpha2_code: 'VE',
        alpha3_code: 'VEN',
      },
      {
        id: 241,
        value: 'Viet Nam',
        alpha2_code: 'VN',
        alpha3_code: 'VNM',
      },
      {
        id: 242,
        value: 'Virgin Islands (British)',
        alpha2_code: 'VG',
        alpha3_code: 'VGB',
      },
      {
        id: 243,
        value: 'Virgin Islands (U.S.)',
        alpha2_code: 'VI',
        alpha3_code: 'VIR',
      },
      {
        id: 244,
        value: 'Wallis and Futuna',
        alpha2_code: 'WF',
        alpha3_code: 'WLF',
      },
      {
        id: 245,
        value: 'Western Sahara',
        alpha2_code: 'EH',
        alpha3_code: 'ESH',
      },
      {
        id: 246,
        value: 'Yemen',
        alpha2_code: 'YE',
        alpha3_code: 'YEM',
      },
      {
        id: 247,
        value: 'Zambia',
        alpha2_code: 'ZM',
        alpha3_code: 'ZMB',
      },
      {
        id: 248,
        value: 'Zimbabwe',
        alpha2_code: 'ZW',
        alpha3_code: 'ZWE',
      },
      {
        id: 249,
        value: 'Åland Islands',
        alpha2_code: 'AX',
        alpha3_code: 'ALA',
      },
      {
        id: 1,
        value: 'Afghanistan',
        alpha2_code: 'AF',
        alpha3_code: 'AFG',
      },
    ],
  },
};

export const lookupDataResponseData = {
  countries: [
    {
      id: 2,
      value: 'Albania',
      alpha2_code: 'AL',
      alpha3_code: 'ALB',
    },
    {
      id: 3,
      value: 'Algeria',
      alpha2_code: 'DZ',
      alpha3_code: 'DZA',
    },
    {
      id: 4,
      value: 'American Samoa',
      alpha2_code: 'AS',
      alpha3_code: 'ASM',
    },
    {
      id: 5,
      value: 'Andorra',
      alpha2_code: 'AD',
      alpha3_code: 'AND',
    },
    {
      id: 6,
      value: 'Angola',
      alpha2_code: 'AO',
      alpha3_code: 'AGO',
    },
    {
      id: 7,
      value: 'Anguilla',
      alpha2_code: 'AI',
      alpha3_code: 'AIA',
    },
    {
      id: 8,
      value: 'Antarctica',
      alpha2_code: 'AQ',
      alpha3_code: 'ATA',
    },
    {
      id: 9,
      value: 'Antigua and Barbuda',
      alpha2_code: 'AG',
      alpha3_code: 'ATG',
    },
    {
      id: 10,
      value: 'Argentina',
      alpha2_code: 'AR',
      alpha3_code: 'ARG',
    },
    {
      id: 11,
      value: 'Armenia',
      alpha2_code: 'AM',
      alpha3_code: 'ARM',
    },
    {
      id: 12,
      value: 'Aruba',
      alpha2_code: 'AW',
      alpha3_code: 'ABW',
    },
    {
      id: 13,
      value: 'Australia',
      alpha2_code: 'AU',
      alpha3_code: 'AUS',
    },
    {
      id: 14,
      value: 'Austria',
      alpha2_code: 'AT',
      alpha3_code: 'AUT',
    },
    {
      id: 15,
      value: 'Azerbaijan',
      alpha2_code: 'AZ',
      alpha3_code: 'AZE',
    },
    {
      id: 16,
      value: 'Bahamas (the)',
      alpha2_code: 'BS',
      alpha3_code: 'BHS',
    },
    {
      id: 17,
      value: 'Bahrain',
      alpha2_code: 'BH',
      alpha3_code: 'BHR',
    },
    {
      id: 18,
      value: 'Bangladesh',
      alpha2_code: 'BD',
      alpha3_code: 'BGD',
    },
    {
      id: 19,
      value: 'Barbados',
      alpha2_code: 'BB',
      alpha3_code: 'BRB',
    },
    {
      id: 20,
      value: 'Belarus',
      alpha2_code: 'BY',
      alpha3_code: 'BLR',
    },
    {
      id: 21,
      value: 'Belgium',
      alpha2_code: 'BE',
      alpha3_code: 'BEL',
    },
    {
      id: 22,
      value: 'Belize',
      alpha2_code: 'BZ',
      alpha3_code: 'BLZ',
    },
    {
      id: 23,
      value: 'Benin',
      alpha2_code: 'BJ',
      alpha3_code: 'BEN',
    },
    {
      id: 24,
      value: 'Bermuda',
      alpha2_code: 'BM',
      alpha3_code: 'BMU',
    },
    {
      id: 25,
      value: 'Bhutan',
      alpha2_code: 'BT',
      alpha3_code: 'BTN',
    },
    {
      id: 26,
      value: 'Bolivia (Plurinational State of)',
      alpha2_code: 'BO',
      alpha3_code: 'BOL',
    },
    {
      id: 27,
      value: 'Bonaire, Sint Eustatius and Saba',
      alpha2_code: 'BQ',
      alpha3_code: 'BES',
    },
    {
      id: 28,
      value: 'Bosnia and Herzegovina',
      alpha2_code: 'BA',
      alpha3_code: 'BIH',
    },
    {
      id: 29,
      value: 'Botswana',
      alpha2_code: 'BW',
      alpha3_code: 'BWA',
    },
    {
      id: 30,
      value: 'Bouvet Island',
      alpha2_code: 'BV',
      alpha3_code: 'BVT',
    },
    {
      id: 31,
      value: 'Brazil',
      alpha2_code: 'BR',
      alpha3_code: 'BRA',
    },
    {
      id: 32,
      value: 'British Indian Ocean Territory (the)',
      alpha2_code: 'IO',
      alpha3_code: 'IOT',
    },
    {
      id: 33,
      value: 'Brunei Darussalam',
      alpha2_code: 'BN',
      alpha3_code: 'BRN',
    },
    {
      id: 34,
      value: 'Bulgaria',
      alpha2_code: 'BG',
      alpha3_code: 'BGR',
    },
    {
      id: 35,
      value: 'Burkina Faso',
      alpha2_code: 'BF',
      alpha3_code: 'BFA',
    },
    {
      id: 36,
      value: 'Burundi',
      alpha2_code: 'BI',
      alpha3_code: 'BDI',
    },
    {
      id: 37,
      value: 'Cabo Verde',
      alpha2_code: 'CV',
      alpha3_code: 'CPV',
    },
    {
      id: 38,
      value: 'Cambodia',
      alpha2_code: 'KH',
      alpha3_code: 'KHM',
    },
    {
      id: 39,
      value: 'Cameroon',
      alpha2_code: 'CM',
      alpha3_code: 'CMR',
    },
    {
      id: 40,
      value: 'Canada',
      alpha2_code: 'CA',
      alpha3_code: 'CAN',
    },
    {
      id: 41,
      value: 'Cayman Islands (the)',
      alpha2_code: 'KY',
      alpha3_code: 'CYM',
    },
    {
      id: 42,
      value: 'Central African Republic (the)',
      alpha2_code: 'CF',
      alpha3_code: 'CAF',
    },
    {
      id: 43,
      value: 'Chad',
      alpha2_code: 'TD',
      alpha3_code: 'TCD',
    },
    {
      id: 44,
      value: 'Chile',
      alpha2_code: 'CL',
      alpha3_code: 'CHL',
    },
    {
      id: 45,
      value: 'China',
      alpha2_code: 'CN',
      alpha3_code: 'CHN',
    },
    {
      id: 46,
      value: 'Christmas Island',
      alpha2_code: 'CX',
      alpha3_code: 'CXR',
    },
    {
      id: 47,
      value: 'Cocos (Keeling) Islands (the)',
      alpha2_code: 'CC',
      alpha3_code: 'CCK',
    },
    {
      id: 48,
      value: 'Colombia',
      alpha2_code: 'CO',
      alpha3_code: 'COL',
    },
    {
      id: 49,
      value: 'Comoros (the)',
      alpha2_code: 'KM',
      alpha3_code: 'COM',
    },
    {
      id: 50,
      value: 'Congo (the Democratic Republic of the)',
      alpha2_code: 'CD',
      alpha3_code: 'COD',
    },
    {
      id: 51,
      value: 'Congo (the)',
      alpha2_code: 'CG',
      alpha3_code: 'COG',
    },
    {
      id: 52,
      value: 'Cook Islands (the)',
      alpha2_code: 'CK',
      alpha3_code: 'COK',
    },
    {
      id: 53,
      value: 'Costa Rica',
      alpha2_code: 'CR',
      alpha3_code: 'CRI',
    },
    {
      id: 54,
      value: 'Croatia',
      alpha2_code: 'HR',
      alpha3_code: 'HRV',
    },
    {
      id: 55,
      value: 'Cuba',
      alpha2_code: 'CU',
      alpha3_code: 'CUB',
    },
    {
      id: 56,
      value: 'Curaçao',
      alpha2_code: 'CW',
      alpha3_code: 'CUW',
    },
    {
      id: 57,
      value: 'Cyprus',
      alpha2_code: 'CY',
      alpha3_code: 'CYP',
    },
    {
      id: 58,
      value: 'Czechia',
      alpha2_code: 'CZ',
      alpha3_code: 'CZE',
    },
    {
      id: 59,
      value: "Côte d'Ivoire",
      alpha2_code: 'CI',
      alpha3_code: 'CIV',
    },
    {
      id: 60,
      value: 'Denmark',
      alpha2_code: 'DK',
      alpha3_code: 'DNK',
    },
    {
      id: 61,
      value: 'Djibouti',
      alpha2_code: 'DJ',
      alpha3_code: 'DJI',
    },
    {
      id: 62,
      value: 'Dominica',
      alpha2_code: 'DM',
      alpha3_code: 'DMA',
    },
    {
      id: 63,
      value: 'Dominican Republic (the)',
      alpha2_code: 'DO',
      alpha3_code: 'DOM',
    },
    {
      id: 64,
      value: 'Ecuador',
      alpha2_code: 'EC',
      alpha3_code: 'ECU',
    },
    {
      id: 65,
      value: 'Egypt',
      alpha2_code: 'EG',
      alpha3_code: 'EGY',
    },
    {
      id: 66,
      value: 'El Salvador',
      alpha2_code: 'SV',
      alpha3_code: 'SLV',
    },
    {
      id: 67,
      value: 'Equatorial Guinea',
      alpha2_code: 'GQ',
      alpha3_code: 'GNQ',
    },
    {
      id: 68,
      value: 'Eritrea',
      alpha2_code: 'ER',
      alpha3_code: 'ERI',
    },
    {
      id: 69,
      value: 'Estonia',
      alpha2_code: 'EE',
      alpha3_code: 'EST',
    },
    {
      id: 70,
      value: 'Eswatini',
      alpha2_code: 'SZ',
      alpha3_code: 'SWZ',
    },
    {
      id: 71,
      value: 'Ethiopia',
      alpha2_code: 'ET',
      alpha3_code: 'ETH',
    },
    {
      id: 72,
      value: 'Falkland Islands (the) [Malvinas]',
      alpha2_code: 'FK',
      alpha3_code: 'FLK',
    },
    {
      id: 73,
      value: 'Faroe Islands (the)',
      alpha2_code: 'FO',
      alpha3_code: 'FRO',
    },
    {
      id: 74,
      value: 'Fiji',
      alpha2_code: 'FJ',
      alpha3_code: 'FJI',
    },
    {
      id: 75,
      value: 'Finland',
      alpha2_code: 'FI',
      alpha3_code: 'FIN',
    },
    {
      id: 76,
      value: 'France',
      alpha2_code: 'FR',
      alpha3_code: 'FRA',
    },
    {
      id: 77,
      value: 'French Guiana',
      alpha2_code: 'GF',
      alpha3_code: 'GUF',
    },
    {
      id: 78,
      value: 'French Polynesia',
      alpha2_code: 'PF',
      alpha3_code: 'PYF',
    },
    {
      id: 79,
      value: 'French Southern Territories (the)',
      alpha2_code: 'TF',
      alpha3_code: 'ATF',
    },
    {
      id: 80,
      value: 'Gabon',
      alpha2_code: 'GA',
      alpha3_code: 'GAB',
    },
    {
      id: 81,
      value: 'Gambia (the)',
      alpha2_code: 'GM',
      alpha3_code: 'GMB',
    },
    {
      id: 82,
      value: 'Georgia',
      alpha2_code: 'GE',
      alpha3_code: 'GEO',
    },
    {
      id: 83,
      value: 'Germany',
      alpha2_code: 'DE',
      alpha3_code: 'DEU',
    },
    {
      id: 84,
      value: 'Ghana',
      alpha2_code: 'GH',
      alpha3_code: 'GHA',
    },
    {
      id: 85,
      value: 'Gibraltar',
      alpha2_code: 'GI',
      alpha3_code: 'GIB',
    },
    {
      id: 86,
      value: 'Greece',
      alpha2_code: 'GR',
      alpha3_code: 'GRC',
    },
    {
      id: 87,
      value: 'Greenland',
      alpha2_code: 'GL',
      alpha3_code: 'GRL',
    },
    {
      id: 88,
      value: 'Grenada',
      alpha2_code: 'GD',
      alpha3_code: 'GRD',
    },
    {
      id: 89,
      value: 'Guadeloupe',
      alpha2_code: 'GP',
      alpha3_code: 'GLP',
    },
    {
      id: 90,
      value: 'Guam',
      alpha2_code: 'GU',
      alpha3_code: 'GUM',
    },
    {
      id: 91,
      value: 'Guatemala',
      alpha2_code: 'GT',
      alpha3_code: 'GTM',
    },
    {
      id: 92,
      value: 'Guernsey',
      alpha2_code: 'GG',
      alpha3_code: 'GGY',
    },
    {
      id: 93,
      value: 'Guinea',
      alpha2_code: 'GN',
      alpha3_code: 'GIN',
    },
    {
      id: 94,
      value: 'Guinea-Bissau',
      alpha2_code: 'GW',
      alpha3_code: 'GNB',
    },
    {
      id: 95,
      value: 'Guyana',
      alpha2_code: 'GY',
      alpha3_code: 'GUY',
    },
    {
      id: 96,
      value: 'Haiti',
      alpha2_code: 'HT',
      alpha3_code: 'HTI',
    },
    {
      id: 97,
      value: 'Heard Island and McDonald Islands',
      alpha2_code: 'HM',
      alpha3_code: 'HMD',
    },
    {
      id: 98,
      value: 'Holy See (the)',
      alpha2_code: 'VA',
      alpha3_code: 'VAT',
    },
    {
      id: 99,
      value: 'Honduras',
      alpha2_code: 'HN',
      alpha3_code: 'HND',
    },
    {
      id: 100,
      value: 'Hong Kong',
      alpha2_code: 'HK',
      alpha3_code: 'HKG',
    },
    {
      id: 101,
      value: 'Hungary',
      alpha2_code: 'HU',
      alpha3_code: 'HUN',
    },
    {
      id: 102,
      value: 'Iceland',
      alpha2_code: 'IS',
      alpha3_code: 'ISL',
    },
    {
      id: 103,
      value: 'India',
      alpha2_code: 'IN',
      alpha3_code: 'IND',
    },
    {
      id: 104,
      value: 'Indonesia',
      alpha2_code: 'ID',
      alpha3_code: 'IDN',
    },
    {
      id: 105,
      value: 'Iran (Islamic Republic of)',
      alpha2_code: 'IR',
      alpha3_code: 'IRN',
    },
    {
      id: 106,
      value: 'Iraq',
      alpha2_code: 'IQ',
      alpha3_code: 'IRQ',
    },
    {
      id: 107,
      value: 'Ireland',
      alpha2_code: 'IE',
      alpha3_code: 'IRL',
    },
    {
      id: 108,
      value: 'Isle of Man',
      alpha2_code: 'IM',
      alpha3_code: 'IMN',
    },
    {
      id: 109,
      value: 'Israel',
      alpha2_code: 'IL',
      alpha3_code: 'ISR',
    },
    {
      id: 110,
      value: 'Italy',
      alpha2_code: 'IT',
      alpha3_code: 'ITA',
    },
    {
      id: 111,
      value: 'Jamaica',
      alpha2_code: 'JM',
      alpha3_code: 'JAM',
    },
    {
      id: 112,
      value: 'Japan',
      alpha2_code: 'JP',
      alpha3_code: 'JPN',
    },
    {
      id: 113,
      value: 'Jersey',
      alpha2_code: 'JE',
      alpha3_code: 'JEY',
    },
    {
      id: 114,
      value: 'Jordan',
      alpha2_code: 'JO',
      alpha3_code: 'JOR',
    },
    {
      id: 115,
      value: 'Kazakhstan',
      alpha2_code: 'KZ',
      alpha3_code: 'KAZ',
    },
    {
      id: 116,
      value: 'Kenya',
      alpha2_code: 'KE',
      alpha3_code: 'KEN',
    },
    {
      id: 117,
      value: 'Kiribati',
      alpha2_code: 'KI',
      alpha3_code: 'KIR',
    },
    {
      id: 118,
      value: "Korea (the Democratic People's Republic of)",
      alpha2_code: 'KP',
      alpha3_code: 'PRK',
    },
    {
      id: 119,
      value: 'Korea (the Republic of)',
      alpha2_code: 'KR',
      alpha3_code: 'KOR',
    },
    {
      id: 120,
      value: 'Kuwait',
      alpha2_code: 'KW',
      alpha3_code: 'KWT',
    },
    {
      id: 121,
      value: 'Kyrgyzstan',
      alpha2_code: 'KG',
      alpha3_code: 'KGZ',
    },
    {
      id: 122,
      value: "Lao People's Democratic Republic (the)",
      alpha2_code: 'LA',
      alpha3_code: 'LAO',
    },
    {
      id: 123,
      value: 'Latvia',
      alpha2_code: 'LV',
      alpha3_code: 'LVA',
    },
    {
      id: 124,
      value: 'Lebanon',
      alpha2_code: 'LB',
      alpha3_code: 'LBN',
    },
    {
      id: 125,
      value: 'Lesotho',
      alpha2_code: 'LS',
      alpha3_code: 'LSO',
    },
    {
      id: 126,
      value: 'Liberia',
      alpha2_code: 'LR',
      alpha3_code: 'LBR',
    },
    {
      id: 127,
      value: 'Libya',
      alpha2_code: 'LY',
      alpha3_code: 'LBY',
    },
    {
      id: 128,
      value: 'Liechtenstein',
      alpha2_code: 'LI',
      alpha3_code: 'LIE',
    },
    {
      id: 129,
      value: 'Lithuania',
      alpha2_code: 'LT',
      alpha3_code: 'LTU',
    },
    {
      id: 130,
      value: 'Luxembourg',
      alpha2_code: 'LU',
      alpha3_code: 'LUX',
    },
    {
      id: 131,
      value: 'Macao',
      alpha2_code: 'MO',
      alpha3_code: 'MAC',
    },
    {
      id: 132,
      value: 'Madagascar',
      alpha2_code: 'MG',
      alpha3_code: 'MDG',
    },
    {
      id: 133,
      value: 'Malawi',
      alpha2_code: 'MW',
      alpha3_code: 'MWI',
    },
    {
      id: 134,
      value: 'Malaysia',
      alpha2_code: 'MY',
      alpha3_code: 'MYS',
    },
    {
      id: 135,
      value: 'Maldives',
      alpha2_code: 'MV',
      alpha3_code: 'MDV',
    },
    {
      id: 136,
      value: 'Mali',
      alpha2_code: 'ML',
      alpha3_code: 'MLI',
    },
    {
      id: 137,
      value: 'Malta',
      alpha2_code: 'MT',
      alpha3_code: 'MLT',
    },
    {
      id: 138,
      value: 'Marshall Islands (the)',
      alpha2_code: 'MH',
      alpha3_code: 'MHL',
    },
    {
      id: 139,
      value: 'Martinique',
      alpha2_code: 'MQ',
      alpha3_code: 'MTQ',
    },
    {
      id: 140,
      value: 'Mauritania',
      alpha2_code: 'MR',
      alpha3_code: 'MRT',
    },
    {
      id: 141,
      value: 'Mauritius',
      alpha2_code: 'MU',
      alpha3_code: 'MUS',
    },
    {
      id: 142,
      value: 'Mayotte',
      alpha2_code: 'YT',
      alpha3_code: 'MYT',
    },
    {
      id: 143,
      value: 'Mexico',
      alpha2_code: 'MX',
      alpha3_code: 'MEX',
    },
    {
      id: 144,
      value: 'Micronesia (Federated States of)',
      alpha2_code: 'FM',
      alpha3_code: 'FSM',
    },
    {
      id: 145,
      value: 'Moldova (the Republic of)',
      alpha2_code: 'MD',
      alpha3_code: 'MDA',
    },
    {
      id: 146,
      value: 'Monaco',
      alpha2_code: 'MC',
      alpha3_code: 'MCO',
    },
    {
      id: 147,
      value: 'Mongolia',
      alpha2_code: 'MN',
      alpha3_code: 'MNG',
    },
    {
      id: 148,
      value: 'Montenegro',
      alpha2_code: 'ME',
      alpha3_code: 'MNE',
    },
    {
      id: 149,
      value: 'Montserrat',
      alpha2_code: 'MS',
      alpha3_code: 'MSR',
    },
    {
      id: 150,
      value: 'Morocco',
      alpha2_code: 'MA',
      alpha3_code: 'MAR',
    },
    {
      id: 151,
      value: 'Mozambique',
      alpha2_code: 'MZ',
      alpha3_code: 'MOZ',
    },
    {
      id: 152,
      value: 'Myanmar',
      alpha2_code: 'MM',
      alpha3_code: 'MMR',
    },
    {
      id: 153,
      value: 'Namibia',
      alpha2_code: 'NA',
      alpha3_code: 'NAM',
    },
    {
      id: 154,
      value: 'Nauru',
      alpha2_code: 'NR',
      alpha3_code: 'NRU',
    },
    {
      id: 155,
      value: 'Nepal',
      alpha2_code: 'NP',
      alpha3_code: 'NPL',
    },
    {
      id: 156,
      value: 'Netherlands (the)',
      alpha2_code: 'NL',
      alpha3_code: 'NLD',
    },
    {
      id: 157,
      value: 'New Caledonia',
      alpha2_code: 'NC',
      alpha3_code: 'NCL',
    },
    {
      id: 158,
      value: 'New Zealand',
      alpha2_code: 'NZ',
      alpha3_code: 'NZL',
    },
    {
      id: 159,
      value: 'Nicaragua',
      alpha2_code: 'NI',
      alpha3_code: 'NIC',
    },
    {
      id: 160,
      value: 'Niger (the)',
      alpha2_code: 'NE',
      alpha3_code: 'NER',
    },
    {
      id: 161,
      value: 'Nigeria',
      alpha2_code: 'NG',
      alpha3_code: 'NGA',
    },
    {
      id: 162,
      value: 'Niue',
      alpha2_code: 'NU',
      alpha3_code: 'NIU',
    },
    {
      id: 163,
      value: 'Norfolk Island',
      alpha2_code: 'NF',
      alpha3_code: 'NFK',
    },
    {
      id: 164,
      value: 'Northern Mariana Islands (the)',
      alpha2_code: 'MP',
      alpha3_code: 'MNP',
    },
    {
      id: 165,
      value: 'Norway',
      alpha2_code: 'NO',
      alpha3_code: 'NOR',
    },
    {
      id: 166,
      value: 'Oman',
      alpha2_code: 'OM',
      alpha3_code: 'OMN',
    },
    {
      id: 167,
      value: 'Pakistan',
      alpha2_code: 'PK',
      alpha3_code: 'PAK',
    },
    {
      id: 168,
      value: 'Palau',
      alpha2_code: 'PW',
      alpha3_code: 'PLW',
    },
    {
      id: 169,
      value: 'Palestine, State of',
      alpha2_code: 'PS',
      alpha3_code: 'PSE',
    },
    {
      id: 170,
      value: 'Panama',
      alpha2_code: 'PA',
      alpha3_code: 'PAN',
    },
    {
      id: 171,
      value: 'Papua New Guinea',
      alpha2_code: 'PG',
      alpha3_code: 'PNG',
    },
    {
      id: 172,
      value: 'Paraguay',
      alpha2_code: 'PY',
      alpha3_code: 'PRY',
    },
    {
      id: 173,
      value: 'Peru',
      alpha2_code: 'PE',
      alpha3_code: 'PER',
    },
    {
      id: 174,
      value: 'Philippines (the)',
      alpha2_code: 'PH',
      alpha3_code: 'PHL',
    },
    {
      id: 175,
      value: 'Pitcairn',
      alpha2_code: 'PN',
      alpha3_code: 'PCN',
    },
    {
      id: 176,
      value: 'Poland',
      alpha2_code: 'PL',
      alpha3_code: 'POL',
    },
    {
      id: 177,
      value: 'Portugal',
      alpha2_code: 'PT',
      alpha3_code: 'PRT',
    },
    {
      id: 178,
      value: 'Puerto Rico',
      alpha2_code: 'PR',
      alpha3_code: 'PRI',
    },
    {
      id: 179,
      value: 'Qatar',
      alpha2_code: 'QA',
      alpha3_code: 'QAT',
    },
    {
      id: 180,
      value: 'Republic of North Macedonia',
      alpha2_code: 'MK',
      alpha3_code: 'MKD',
    },
    {
      id: 181,
      value: 'Romania',
      alpha2_code: 'RO',
      alpha3_code: 'ROU',
    },
    {
      id: 182,
      value: 'Russian Federation (the)',
      alpha2_code: 'RU',
      alpha3_code: 'RUS',
    },
    {
      id: 183,
      value: 'Rwanda',
      alpha2_code: 'RW',
      alpha3_code: 'RWA',
    },
    {
      id: 184,
      value: 'Réunion',
      alpha2_code: 'RE',
      alpha3_code: 'REU',
    },
    {
      id: 185,
      value: 'Saint Barthélemy',
      alpha2_code: 'BL',
      alpha3_code: 'BLM',
    },
    {
      id: 186,
      value: 'Saint Helena, Ascension and Tristan da Cunha',
      alpha2_code: 'SH',
      alpha3_code: 'SHN',
    },
    {
      id: 187,
      value: 'Saint Kitts and Nevis',
      alpha2_code: 'KN',
      alpha3_code: 'KNA',
    },
    {
      id: 188,
      value: 'Saint Lucia',
      alpha2_code: 'LC',
      alpha3_code: 'LCA',
    },
    {
      id: 189,
      value: 'Saint Martin (French part)',
      alpha2_code: 'MF',
      alpha3_code: 'MAF',
    },
    {
      id: 190,
      value: 'Saint Pierre and Miquelon',
      alpha2_code: 'PM',
      alpha3_code: 'SPM',
    },
    {
      id: 191,
      value: 'Saint Vincent and the Grenadines',
      alpha2_code: 'VC',
      alpha3_code: 'VCT',
    },
    {
      id: 192,
      value: 'Samoa',
      alpha2_code: 'WS',
      alpha3_code: 'WSM',
    },
    {
      id: 193,
      value: 'San Marino',
      alpha2_code: 'SM',
      alpha3_code: 'SMR',
    },
    {
      id: 194,
      value: 'Sao Tome and Principe',
      alpha2_code: 'ST',
      alpha3_code: 'STP',
    },
    {
      id: 195,
      value: 'Saudi Arabia',
      alpha2_code: 'SA',
      alpha3_code: 'SAU',
    },
    {
      id: 196,
      value: 'Senegal',
      alpha2_code: 'SN',
      alpha3_code: 'SEN',
    },
    {
      id: 197,
      value: 'Serbia',
      alpha2_code: 'RS',
      alpha3_code: 'SRB',
    },
    {
      id: 198,
      value: 'Seychelles',
      alpha2_code: 'SC',
      alpha3_code: 'SYC',
    },
    {
      id: 199,
      value: 'Sierra Leone',
      alpha2_code: 'SL',
      alpha3_code: 'SLE',
    },
    {
      id: 200,
      value: 'Singapore',
      alpha2_code: 'SG',
      alpha3_code: 'SGP',
    },
    {
      id: 201,
      value: 'Sint Maarten (Dutch part)',
      alpha2_code: 'SX',
      alpha3_code: 'SXM',
    },
    {
      id: 202,
      value: 'Slovakia',
      alpha2_code: 'SK',
      alpha3_code: 'SVK',
    },
    {
      id: 203,
      value: 'Slovenia',
      alpha2_code: 'SI',
      alpha3_code: 'SVN',
    },
    {
      id: 204,
      value: 'Solomon Islands',
      alpha2_code: 'SB',
      alpha3_code: 'SLB',
    },
    {
      id: 205,
      value: 'Somalia',
      alpha2_code: 'SO',
      alpha3_code: 'SOM',
    },
    {
      id: 206,
      value: 'South Africa',
      alpha2_code: 'ZA',
      alpha3_code: 'ZAF',
    },
    {
      id: 207,
      value: 'South Georgia and the South Sandwich Islands',
      alpha2_code: 'GS',
      alpha3_code: 'SGS',
    },
    {
      id: 208,
      value: 'South Sudan',
      alpha2_code: 'SS',
      alpha3_code: 'SSD',
    },
    {
      id: 209,
      value: 'Spain',
      alpha2_code: 'ES',
      alpha3_code: 'ESP',
    },
    {
      id: 210,
      value: 'Sri Lanka',
      alpha2_code: 'LK',
      alpha3_code: 'LKA',
    },
    {
      id: 211,
      value: 'Sudan (the)',
      alpha2_code: 'SD',
      alpha3_code: 'SDN',
    },
    {
      id: 212,
      value: 'Suriname',
      alpha2_code: 'SR',
      alpha3_code: 'SUR',
    },
    {
      id: 213,
      value: 'Svalbard and Jan Mayen',
      alpha2_code: 'SJ',
      alpha3_code: 'SJM',
    },
    {
      id: 214,
      value: 'Sweden',
      alpha2_code: 'SE',
      alpha3_code: 'SWE',
    },
    {
      id: 215,
      value: 'Switzerland',
      alpha2_code: 'CH',
      alpha3_code: 'CHE',
    },
    {
      id: 216,
      value: 'Syrian Arab Republic',
      alpha2_code: 'SY',
      alpha3_code: 'SYR',
    },
    {
      id: 217,
      value: 'Taiwan',
      alpha2_code: 'TW',
      alpha3_code: 'TWN',
    },
    {
      id: 218,
      value: 'Tajikistan',
      alpha2_code: 'TJ',
      alpha3_code: 'TJK',
    },
    {
      id: 219,
      value: 'Tanzania, United Republic of',
      alpha2_code: 'TZ',
      alpha3_code: 'TZA',
    },
    {
      id: 220,
      value: 'Thailand',
      alpha2_code: 'TH',
      alpha3_code: 'THA',
    },
    {
      id: 221,
      value: 'Timor-Leste',
      alpha2_code: 'TL',
      alpha3_code: 'TLS',
    },
    {
      id: 222,
      value: 'Togo',
      alpha2_code: 'TG',
      alpha3_code: 'TGO',
    },
    {
      id: 223,
      value: 'Tokelau',
      alpha2_code: 'TK',
      alpha3_code: 'TKL',
    },
    {
      id: 224,
      value: 'Tonga',
      alpha2_code: 'TO',
      alpha3_code: 'TON',
    },
    {
      id: 225,
      value: 'Trinidad and Tobago',
      alpha2_code: 'TT',
      alpha3_code: 'TTO',
    },
    {
      id: 226,
      value: 'Tunisia',
      alpha2_code: 'TN',
      alpha3_code: 'TUN',
    },
    {
      id: 227,
      value: 'Turkey',
      alpha2_code: 'TR',
      alpha3_code: 'TUR',
    },
    {
      id: 228,
      value: 'Turkmenistan',
      alpha2_code: 'TM',
      alpha3_code: 'TKM',
    },
    {
      id: 229,
      value: 'Turks and Caicos Islands (the)',
      alpha2_code: 'TC',
      alpha3_code: 'TCA',
    },
    {
      id: 230,
      value: 'Tuvalu',
      alpha2_code: 'TV',
      alpha3_code: 'TUV',
    },
    {
      id: 231,
      value: 'Uganda',
      alpha2_code: 'UG',
      alpha3_code: 'UGA',
    },
    {
      id: 232,
      value: 'Ukraine',
      alpha2_code: 'UA',
      alpha3_code: 'UKR',
    },
    {
      id: 233,
      value: 'United Arab Emirates (the)',
      alpha2_code: 'AE',
      alpha3_code: 'ARE',
    },
    {
      id: 234,
      value: 'United Kingdom of Great Britain and Northern Ireland (the)',
      alpha2_code: 'GB',
      alpha3_code: 'GBR',
    },
    {
      id: 235,
      value: 'United States Minor Outlying Islands (the)',
      alpha2_code: 'UM',
      alpha3_code: 'UMI',
    },
    {
      id: 236,
      value: 'United States of America (the)',
      alpha2_code: 'US',
      alpha3_code: 'USA',
    },
    {
      id: 237,
      value: 'Uruguay',
      alpha2_code: 'UY',
      alpha3_code: 'URY',
    },
    {
      id: 238,
      value: 'Uzbekistan',
      alpha2_code: 'UZ',
      alpha3_code: 'UZB',
    },
    {
      id: 239,
      value: 'Vanuatu',
      alpha2_code: 'VU',
      alpha3_code: 'VUT',
    },
    {
      id: 240,
      value: 'Venezuela (Bolivarian Republic of)',
      alpha2_code: 'VE',
      alpha3_code: 'VEN',
    },
    {
      id: 241,
      value: 'Viet Nam',
      alpha2_code: 'VN',
      alpha3_code: 'VNM',
    },
    {
      id: 242,
      value: 'Virgin Islands (British)',
      alpha2_code: 'VG',
      alpha3_code: 'VGB',
    },
    {
      id: 243,
      value: 'Virgin Islands (U.S.)',
      alpha2_code: 'VI',
      alpha3_code: 'VIR',
    },
    {
      id: 244,
      value: 'Wallis and Futuna',
      alpha2_code: 'WF',
      alpha3_code: 'WLF',
    },
    {
      id: 245,
      value: 'Western Sahara',
      alpha2_code: 'EH',
      alpha3_code: 'ESH',
    },
    {
      id: 246,
      value: 'Yemen',
      alpha2_code: 'YE',
      alpha3_code: 'YEM',
    },
    {
      id: 247,
      value: 'Zambia',
      alpha2_code: 'ZM',
      alpha3_code: 'ZMB',
    },
    {
      id: 248,
      value: 'Zimbabwe',
      alpha2_code: 'ZW',
      alpha3_code: 'ZWE',
    },
    {
      id: 249,
      value: 'Åland Islands',
      alpha2_code: 'AX',
      alpha3_code: 'ALA',
    },
    {
      id: 1,
      value: 'Afghanistan',
      alpha2_code: 'AF',
      alpha3_code: 'AFG',
    },
  ],
  nationalities: [
    {
      id: 1,
      value: 'Aruban',
      alpha2_code: 'AW',
      alpha3_code: 'ABW',
    },
    {
      id: 2,
      value: 'Afghan',
      alpha2_code: 'AF',
      alpha3_code: 'AFG',
    },
    {
      id: 3,
      value: 'Angolan',
      alpha2_code: 'AO',
      alpha3_code: 'AGO',
    },
    {
      id: 4,
      value: 'Anguillan',
      alpha2_code: 'AI',
      alpha3_code: 'AIA',
    },
    {
      id: 5,
      value: 'Åland Island',
      alpha2_code: 'AX',
      alpha3_code: 'ALA',
    },
    {
      id: 6,
      value: 'Albanian',
      alpha2_code: 'AL',
      alpha3_code: 'ALB',
    },
    {
      id: 7,
      value: 'Andorran',
      alpha2_code: 'AD',
      alpha3_code: 'AND',
    },
    {
      id: 8,
      value: 'Emirati, Emirian, Emiri',
      alpha2_code: 'AE',
      alpha3_code: 'ARE',
    },
    {
      id: 9,
      value: 'Argentine',
      alpha2_code: 'AR',
      alpha3_code: 'ARG',
    },
    {
      id: 10,
      value: 'Armenian',
      alpha2_code: 'AM',
      alpha3_code: 'ARM',
    },
    {
      id: 11,
      value: 'American Samoan',
      alpha2_code: 'AS',
      alpha3_code: 'ASM',
    },
    {
      id: 12,
      value: 'Antarctic',
      alpha2_code: 'AQ',
      alpha3_code: 'ATA',
    },
    {
      id: 13,
      value: 'French Southern Territories',
      alpha2_code: 'TF',
      alpha3_code: 'ATF',
    },
    {
      id: 14,
      value: 'Antiguan or Barbudan',
      alpha2_code: 'AG',
      alpha3_code: 'ATG',
    },
    {
      id: 15,
      value: 'Australian',
      alpha2_code: 'AU',
      alpha3_code: 'AUS',
    },
    {
      id: 16,
      value: 'Austrian',
      alpha2_code: 'AT',
      alpha3_code: 'AUT',
    },
    {
      id: 17,
      value: 'Azerbaijani, Azeri',
      alpha2_code: 'AZ',
      alpha3_code: 'AZE',
    },
    {
      id: 18,
      value: 'Burundian',
      alpha2_code: 'BI',
      alpha3_code: 'BDI',
    },
    {
      id: 19,
      value: 'Belgian',
      alpha2_code: 'BE',
      alpha3_code: 'BEL',
    },
    {
      id: 20,
      value: 'Beninese, Beninois',
      alpha2_code: 'BJ',
      alpha3_code: 'BEN',
    },
    {
      id: 21,
      value: 'Bonaire',
      alpha2_code: 'BQ',
      alpha3_code: 'BES',
    },
    {
      id: 22,
      value: 'Burkinabé',
      alpha2_code: 'BF',
      alpha3_code: 'BFA',
    },
    {
      id: 23,
      value: 'Bangladeshi',
      alpha2_code: 'BD',
      alpha3_code: 'BGD',
    },
    {
      id: 24,
      value: 'Bulgarian',
      alpha2_code: 'BG',
      alpha3_code: 'BGR',
    },
    {
      id: 25,
      value: 'Bahraini',
      alpha2_code: 'BH',
      alpha3_code: 'BHR',
    },
    {
      id: 26,
      value: 'Bahamian',
      alpha2_code: 'BS',
      alpha3_code: 'BHS',
    },
    {
      id: 27,
      value: 'Bosnian or Herzegovinian',
      alpha2_code: 'BA',
      alpha3_code: 'BIH',
    },
    {
      id: 28,
      value: 'Barthélemois',
      alpha2_code: 'BL',
      alpha3_code: 'BLM',
    },
    {
      id: 29,
      value: 'Belarusian',
      alpha2_code: 'BY',
      alpha3_code: 'BLR',
    },
    {
      id: 30,
      value: 'Belizean',
      alpha2_code: 'BZ',
      alpha3_code: 'BLZ',
    },
    {
      id: 31,
      value: 'Bermudian, Bermudan',
      alpha2_code: 'BM',
      alpha3_code: 'BMU',
    },
    {
      id: 32,
      value: 'Bolivian',
      alpha2_code: 'BO',
      alpha3_code: 'BOL',
    },
    {
      id: 33,
      value: 'Brazilian',
      alpha2_code: 'BR',
      alpha3_code: 'BRA',
    },
    {
      id: 34,
      value: 'Barbadian',
      alpha2_code: 'BB',
      alpha3_code: 'BRB',
    },
    {
      id: 35,
      value: 'Bruneian',
      alpha2_code: 'BN',
      alpha3_code: 'BRN',
    },
    {
      id: 36,
      value: 'Bhutanese',
      alpha2_code: 'BT',
      alpha3_code: 'BTN',
    },
    {
      id: 37,
      value: 'Bouvet Island',
      alpha2_code: 'BV',
      alpha3_code: 'BVT',
    },
    {
      id: 38,
      value: 'Motswana, Botswanan',
      alpha2_code: 'BW',
      alpha3_code: 'BWA',
    },
    {
      id: 39,
      value: 'Central African',
      alpha2_code: 'CF',
      alpha3_code: 'CAF',
    },
    {
      id: 40,
      value: 'Canadian',
      alpha2_code: 'CA',
      alpha3_code: 'CAN',
    },
    {
      id: 41,
      value: 'Cocos Island',
      alpha2_code: 'CC',
      alpha3_code: 'CCK',
    },
    {
      id: 42,
      value: 'Swiss',
      alpha2_code: 'CH',
      alpha3_code: 'CHE',
    },
    {
      id: 43,
      value: 'Chilean',
      alpha2_code: 'CL',
      alpha3_code: 'CHL',
    },
    {
      id: 44,
      value: 'Chinese',
      alpha2_code: 'CN',
      alpha3_code: 'CHN',
    },
    {
      id: 45,
      value: 'Ivorian',
      alpha2_code: 'CI',
      alpha3_code: 'CIV',
    },
    {
      id: 46,
      value: 'Cameroonian',
      alpha2_code: 'CM',
      alpha3_code: 'CMR',
    },
    {
      id: 47,
      value: 'Congolese',
      alpha2_code: 'CD',
      alpha3_code: 'COD',
    },
    {
      id: 48,
      value: 'Congolese',
      alpha2_code: 'CG',
      alpha3_code: 'COG',
    },
    {
      id: 49,
      value: 'Cook Island',
      alpha2_code: 'CK',
      alpha3_code: 'COK',
    },
    {
      id: 50,
      value: 'Colombian',
      alpha2_code: 'CO',
      alpha3_code: 'COL',
    },
    {
      id: 51,
      value: 'Comoran, Comorian',
      alpha2_code: 'KM',
      alpha3_code: 'COM',
    },
    {
      id: 52,
      value: 'Cabo Verdean',
      alpha2_code: 'CV',
      alpha3_code: 'CPV',
    },
    {
      id: 53,
      value: 'Costa Rican',
      alpha2_code: 'CR',
      alpha3_code: 'CRI',
    },
    {
      id: 54,
      value: 'Cuban',
      alpha2_code: 'CU',
      alpha3_code: 'CUB',
    },
    {
      id: 55,
      value: 'Curaçaoan',
      alpha2_code: 'CW',
      alpha3_code: 'CUW',
    },
    {
      id: 56,
      value: 'Christmas Island',
      alpha2_code: 'CX',
      alpha3_code: 'CXR',
    },
    {
      id: 57,
      value: 'Caymanian',
      alpha2_code: 'KY',
      alpha3_code: 'CYM',
    },
    {
      id: 58,
      value: 'Cypriot',
      alpha2_code: 'CY',
      alpha3_code: 'CYP',
    },
    {
      id: 59,
      value: 'Czech',
      alpha2_code: 'CZ',
      alpha3_code: 'CZE',
    },
    {
      id: 60,
      value: 'German',
      alpha2_code: 'DE',
      alpha3_code: 'DEU',
    },
    {
      id: 61,
      value: 'Djiboutian',
      alpha2_code: 'DJ',
      alpha3_code: 'DJI',
    },
    {
      id: 62,
      value: 'Dominican',
      alpha2_code: 'DM',
      alpha3_code: 'DMA',
    },
    {
      id: 63,
      value: 'Danish',
      alpha2_code: 'DK',
      alpha3_code: 'DNK',
    },
    {
      id: 64,
      value: 'Dominican',
      alpha2_code: 'DO',
      alpha3_code: 'DOM',
    },
    {
      id: 65,
      value: 'Algerian',
      alpha2_code: 'DZ',
      alpha3_code: 'DZA',
    },
    {
      id: 66,
      value: 'Ecuadorian',
      alpha2_code: 'EC',
      alpha3_code: 'ECU',
    },
    {
      id: 67,
      value: 'Egyptian',
      alpha2_code: 'EG',
      alpha3_code: 'EGY',
    },
    {
      id: 68,
      value: 'Eritrean',
      alpha2_code: 'ER',
      alpha3_code: 'ERI',
    },
    {
      id: 69,
      value: 'Sahrawi, Sahrawian, Sahraouian',
      alpha2_code: 'EH',
      alpha3_code: 'ESH',
    },
    {
      id: 70,
      value: 'Spanish',
      alpha2_code: 'ES',
      alpha3_code: 'ESP',
    },
    {
      id: 71,
      value: 'Estonian',
      alpha2_code: 'EE',
      alpha3_code: 'EST',
    },
    {
      id: 72,
      value: 'Ethiopian',
      alpha2_code: 'ET',
      alpha3_code: 'ETH',
    },
    {
      id: 73,
      value: 'Finnish',
      alpha2_code: 'FI',
      alpha3_code: 'FIN',
    },
    {
      id: 74,
      value: 'Fijian',
      alpha2_code: 'FJ',
      alpha3_code: 'FJI',
    },
    {
      id: 75,
      value: 'Falkland Island',
      alpha2_code: 'FK',
      alpha3_code: 'FLK',
    },
    {
      id: 76,
      value: 'French',
      alpha2_code: 'FR',
      alpha3_code: 'FRA',
    },
    {
      id: 77,
      value: 'Faroese',
      alpha2_code: 'FO',
      alpha3_code: 'FRO',
    },
    {
      id: 78,
      value: 'Micronesian',
      alpha2_code: 'FM',
      alpha3_code: 'FSM',
    },
    {
      id: 79,
      value: 'Gabonese',
      alpha2_code: 'GA',
      alpha3_code: 'GAB',
    },
    {
      id: 80,
      value: 'British, UK',
      alpha2_code: 'GB',
      alpha3_code: 'GBR',
    },
    {
      id: 81,
      value: 'Georgian',
      alpha2_code: 'GE',
      alpha3_code: 'GEO',
    },
    {
      id: 82,
      value: 'Channel Island',
      alpha2_code: 'GG',
      alpha3_code: 'GGY',
    },
    {
      id: 83,
      value: 'Ghanaian',
      alpha2_code: 'GH',
      alpha3_code: 'GHA',
    },
    {
      id: 84,
      value: 'Gibraltar',
      alpha2_code: 'GI',
      alpha3_code: 'GIB',
    },
    {
      id: 85,
      value: 'Guinean',
      alpha2_code: 'GN',
      alpha3_code: 'GIN',
    },
    {
      id: 86,
      value: 'Guadeloupe',
      alpha2_code: 'GP',
      alpha3_code: 'GLP',
    },
    {
      id: 87,
      value: 'Gambian',
      alpha2_code: 'GM',
      alpha3_code: 'GMB',
    },
    {
      id: 88,
      value: 'Bissau-Guinean',
      alpha2_code: 'GW',
      alpha3_code: 'GNB',
    },
    {
      id: 89,
      value: 'Equatorial Guinean, Equatoguinean',
      alpha2_code: 'GQ',
      alpha3_code: 'GNQ',
    },
    {
      id: 90,
      value: 'Greek, Hellenic',
      alpha2_code: 'GR',
      alpha3_code: 'GRC',
    },
    {
      id: 91,
      value: 'Grenadian',
      alpha2_code: 'GD',
      alpha3_code: 'GRD',
    },
    {
      id: 92,
      value: 'Greenlandic',
      alpha2_code: 'GL',
      alpha3_code: 'GRL',
    },
    {
      id: 93,
      value: 'Guatemalan',
      alpha2_code: 'GT',
      alpha3_code: 'GTM',
    },
    {
      id: 94,
      value: 'French Guianese',
      alpha2_code: 'GF',
      alpha3_code: 'GUF',
    },
    {
      id: 95,
      value: 'Guamanian, Guambat',
      alpha2_code: 'GU',
      alpha3_code: 'GUM',
    },
    {
      id: 96,
      value: 'Guyanese',
      alpha2_code: 'GY',
      alpha3_code: 'GUY',
    },
    {
      id: 97,
      value: 'Hong Kong, Hong Kongese',
      alpha2_code: 'HK',
      alpha3_code: 'HKG',
    },
    {
      id: 98,
      value: 'Heard Island or McDonald Islands',
      alpha2_code: 'HM',
      alpha3_code: 'HMD',
    },
    {
      id: 99,
      value: 'Honduran',
      alpha2_code: 'HN',
      alpha3_code: 'HND',
    },
    {
      id: 100,
      value: 'Croatian',
      alpha2_code: 'HR',
      alpha3_code: 'HRV',
    },
    {
      id: 101,
      value: 'Haitian',
      alpha2_code: 'HT',
      alpha3_code: 'HTI',
    },
    {
      id: 102,
      value: 'Hungarian, Magyar',
      alpha2_code: 'HU',
      alpha3_code: 'HUN',
    },
    {
      id: 103,
      value: 'Indonesian',
      alpha2_code: 'ID',
      alpha3_code: 'IDN',
    },
    {
      id: 104,
      value: 'Manx',
      alpha2_code: 'IM',
      alpha3_code: 'IMN',
    },
    {
      id: 105,
      value: 'Indian',
      alpha2_code: 'IN',
      alpha3_code: 'IND',
    },
    {
      id: 106,
      value: 'BIOT',
      alpha2_code: 'IO',
      alpha3_code: 'IOT',
    },
    {
      id: 107,
      value: 'Irish',
      alpha2_code: 'IE',
      alpha3_code: 'IRL',
    },
    {
      id: 108,
      value: 'Iranian, Persian',
      alpha2_code: 'IR',
      alpha3_code: 'IRN',
    },
    {
      id: 109,
      value: 'Iraqi',
      alpha2_code: 'IQ',
      alpha3_code: 'IRQ',
    },
    {
      id: 110,
      value: 'Icelandic',
      alpha2_code: 'IS',
      alpha3_code: 'ISL',
    },
    {
      id: 111,
      value: 'Israeli',
      alpha2_code: 'IL',
      alpha3_code: 'ISR',
    },
    {
      id: 112,
      value: 'Italian',
      alpha2_code: 'IT',
      alpha3_code: 'ITA',
    },
    {
      id: 113,
      value: 'Jamaican',
      alpha2_code: 'JM',
      alpha3_code: 'JAM',
    },
    {
      id: 114,
      value: 'Channel Island',
      alpha2_code: 'JE',
      alpha3_code: 'JEY',
    },
    {
      id: 115,
      value: 'Jordanian',
      alpha2_code: 'JO',
      alpha3_code: 'JOR',
    },
    {
      id: 116,
      value: 'Japanese',
      alpha2_code: 'JP',
      alpha3_code: 'JPN',
    },
    {
      id: 117,
      value: 'Kazakhstani, Kazakh',
      alpha2_code: 'KZ',
      alpha3_code: 'KAZ',
    },
    {
      id: 118,
      value: 'Kenyan',
      alpha2_code: 'KE',
      alpha3_code: 'KEN',
    },
    {
      id: 119,
      value: 'Kyrgyzstani, Kyrgyz, Kirgiz, Kirghiz',
      alpha2_code: 'KG',
      alpha3_code: 'KGZ',
    },
    {
      id: 120,
      value: 'Cambodian',
      alpha2_code: 'KH',
      alpha3_code: 'KHM',
    },
    {
      id: 121,
      value: 'I-Kiribati',
      alpha2_code: 'KI',
      alpha3_code: 'KIR',
    },
    {
      id: 122,
      value: 'Kittitian or Nevisian',
      alpha2_code: 'KN',
      alpha3_code: 'KNA',
    },
    {
      id: 123,
      value: 'South Korean',
      alpha2_code: 'KR',
      alpha3_code: 'KOR',
    },
    {
      id: 124,
      value: 'Kuwaiti',
      alpha2_code: 'KW',
      alpha3_code: 'KWT',
    },
    {
      id: 125,
      value: 'Lao, Laotian',
      alpha2_code: 'LA',
      alpha3_code: 'LAO',
    },
    {
      id: 126,
      value: 'Lebanese',
      alpha2_code: 'LB',
      alpha3_code: 'LBN',
    },
    {
      id: 127,
      value: 'Liberian',
      alpha2_code: 'LR',
      alpha3_code: 'LBR',
    },
    {
      id: 128,
      value: 'Libyan',
      alpha2_code: 'LY',
      alpha3_code: 'LBY',
    },
    {
      id: 129,
      value: 'Saint Lucian',
      alpha2_code: 'LC',
      alpha3_code: 'LCA',
    },
    {
      id: 130,
      value: 'Liechtenstein',
      alpha2_code: 'LI',
      alpha3_code: 'LIE',
    },
    {
      id: 131,
      value: 'Sri Lankan',
      alpha2_code: 'LK',
      alpha3_code: 'LKA',
    },
    {
      id: 132,
      value: 'Basotho',
      alpha2_code: 'LS',
      alpha3_code: 'LSO',
    },
    {
      id: 133,
      value: 'Lithuanian',
      alpha2_code: 'LT',
      alpha3_code: 'LTU',
    },
    {
      id: 134,
      value: 'Luxembourg, Luxembourgish',
      alpha2_code: 'LU',
      alpha3_code: 'LUX',
    },
    {
      id: 135,
      value: 'Latvian',
      alpha2_code: 'LV',
      alpha3_code: 'LVA',
    },
    {
      id: 136,
      value: 'Macanese, Chinese',
      alpha2_code: 'MO',
      alpha3_code: 'MAC',
    },
    {
      id: 137,
      value: 'Saint-Martinoise',
      alpha2_code: 'MF',
      alpha3_code: 'MAF',
    },
    {
      id: 138,
      value: 'Moroccan',
      alpha2_code: 'MA',
      alpha3_code: 'MAR',
    },
    {
      id: 139,
      value: 'Monégasque, Monacan',
      alpha2_code: 'MC',
      alpha3_code: 'MCO',
    },
    {
      id: 140,
      value: 'Moldovan',
      alpha2_code: 'MD',
      alpha3_code: 'MDA',
    },
    {
      id: 141,
      value: 'Malagasy',
      alpha2_code: 'MG',
      alpha3_code: 'MDG',
    },
    {
      id: 142,
      value: 'Maldivian',
      alpha2_code: 'MV',
      alpha3_code: 'MDV',
    },
    {
      id: 143,
      value: 'Mexican',
      alpha2_code: 'MX',
      alpha3_code: 'MEX',
    },
    {
      id: 144,
      value: 'Marshallese',
      alpha2_code: 'MH',
      alpha3_code: 'MHL',
    },
    {
      id: 145,
      value: 'Macedonian',
      alpha2_code: 'MK',
      alpha3_code: 'MKD',
    },
    {
      id: 146,
      value: 'Malian, Malinese',
      alpha2_code: 'ML',
      alpha3_code: 'MLI',
    },
    {
      id: 147,
      value: 'Maltese',
      alpha2_code: 'MT',
      alpha3_code: 'MLT',
    },
    {
      id: 148,
      value: 'Burmese',
      alpha2_code: 'MM',
      alpha3_code: 'MMR',
    },
    {
      id: 149,
      value: 'Montenegrin',
      alpha2_code: 'ME',
      alpha3_code: 'MNE',
    },
    {
      id: 150,
      value: 'Mongolian',
      alpha2_code: 'MN',
      alpha3_code: 'MNG',
    },
    {
      id: 151,
      value: 'Northern Marianan',
      alpha2_code: 'MP',
      alpha3_code: 'MNP',
    },
    {
      id: 152,
      value: 'Mozambican',
      alpha2_code: 'MZ',
      alpha3_code: 'MOZ',
    },
    {
      id: 153,
      value: 'Mauritanian',
      alpha2_code: 'MR',
      alpha3_code: 'MRT',
    },
    {
      id: 154,
      value: 'Montserratian',
      alpha2_code: 'MS',
      alpha3_code: 'MSR',
    },
    {
      id: 155,
      value: 'Martiniquais, Martinican',
      alpha2_code: 'MQ',
      alpha3_code: 'MTQ',
    },
    {
      id: 156,
      value: 'Mauritian',
      alpha2_code: 'MU',
      alpha3_code: 'MUS',
    },
    {
      id: 157,
      value: 'Malawian',
      alpha2_code: 'MW',
      alpha3_code: 'MWI',
    },
    {
      id: 158,
      value: 'Malaysian',
      alpha2_code: 'MY',
      alpha3_code: 'MYS',
    },
    {
      id: 159,
      value: 'Mahoran',
      alpha2_code: 'YT',
      alpha3_code: 'MYT',
    },
    {
      id: 160,
      value: 'Namibian',
      alpha2_code: 'NA',
      alpha3_code: 'NAM',
    },
    {
      id: 161,
      value: 'New Caledonian',
      alpha2_code: 'NC',
      alpha3_code: 'NCL',
    },
    {
      id: 162,
      value: 'Nigerien',
      alpha2_code: 'NE',
      alpha3_code: 'NER',
    },
    {
      id: 163,
      value: 'Norfolk Island',
      alpha2_code: 'NF',
      alpha3_code: 'NFK',
    },
    {
      id: 164,
      value: 'Nigerian',
      alpha2_code: 'NG',
      alpha3_code: 'NGA',
    },
    {
      id: 165,
      value: 'Nicaraguan',
      alpha2_code: 'NI',
      alpha3_code: 'NIC',
    },
    {
      id: 166,
      value: 'Niuean',
      alpha2_code: 'NU',
      alpha3_code: 'NIU',
    },
    {
      id: 167,
      value: 'Dutch, Netherlandic',
      alpha2_code: 'NL',
      alpha3_code: 'NLD',
    },
    {
      id: 168,
      value: 'Norwegian',
      alpha2_code: 'NO',
      alpha3_code: 'NOR',
    },
    {
      id: 169,
      value: 'Nepali, Nepalese',
      alpha2_code: 'NP',
      alpha3_code: 'NPL',
    },
    {
      id: 170,
      value: 'Nauruan',
      alpha2_code: 'NR',
      alpha3_code: 'NRU',
    },
    {
      id: 171,
      value: 'New Zealand, NZ',
      alpha2_code: 'NZ',
      alpha3_code: 'NZL',
    },
    {
      id: 172,
      value: 'Omani',
      alpha2_code: 'OM',
      alpha3_code: 'OMN',
    },
    {
      id: 173,
      value: 'Pakistani',
      alpha2_code: 'PK',
      alpha3_code: 'PAK',
    },
    {
      id: 174,
      value: 'Panamanian',
      alpha2_code: 'PA',
      alpha3_code: 'PAN',
    },
    {
      id: 175,
      value: 'Pitcairn Island',
      alpha2_code: 'PN',
      alpha3_code: 'PCN',
    },
    {
      id: 176,
      value: 'Peruvian',
      alpha2_code: 'PE',
      alpha3_code: 'PER',
    },
    {
      id: 177,
      value: 'Philippine, Filipino',
      alpha2_code: 'PH',
      alpha3_code: 'PHL',
    },
    {
      id: 178,
      value: 'Palauan',
      alpha2_code: 'PW',
      alpha3_code: 'PLW',
    },
    {
      id: 179,
      value: 'Papua New Guinean, Papuan',
      alpha2_code: 'PG',
      alpha3_code: 'PNG',
    },
    {
      id: 180,
      value: 'Polish',
      alpha2_code: 'PL',
      alpha3_code: 'POL',
    },
    {
      id: 181,
      value: 'Puerto Rican',
      alpha2_code: 'PR',
      alpha3_code: 'PRI',
    },
    {
      id: 182,
      value: 'North Korean',
      alpha2_code: 'KP',
      alpha3_code: 'PRK',
    },
    {
      id: 183,
      value: 'Portuguese',
      alpha2_code: 'PT',
      alpha3_code: 'PRT',
    },
    {
      id: 184,
      value: 'Paraguayan',
      alpha2_code: 'PY',
      alpha3_code: 'PRY',
    },
    {
      id: 185,
      value: 'Palestinian',
      alpha2_code: 'PS',
      alpha3_code: 'PSE',
    },
    {
      id: 186,
      value: 'French Polynesian',
      alpha2_code: 'PF',
      alpha3_code: 'PYF',
    },
    {
      id: 187,
      value: 'Qatari',
      alpha2_code: 'QA',
      alpha3_code: 'QAT',
    },
    {
      id: 188,
      value: 'Réunionese, Réunionnais',
      alpha2_code: 'RE',
      alpha3_code: 'REU',
    },
    {
      id: 189,
      value: 'Romanian',
      alpha2_code: 'RO',
      alpha3_code: 'ROU',
    },
    {
      id: 190,
      value: 'Russian',
      alpha2_code: 'RU',
      alpha3_code: 'RUS',
    },
    {
      id: 191,
      value: 'Rwandan',
      alpha2_code: 'RW',
      alpha3_code: 'RWA',
    },
    {
      id: 192,
      value: 'Saudi, Saudi Arabian',
      alpha2_code: 'SA',
      alpha3_code: 'SAU',
    },
    {
      id: 193,
      value: 'Sudanese',
      alpha2_code: 'SD',
      alpha3_code: 'SDN',
    },
    {
      id: 194,
      value: 'Senegalese',
      alpha2_code: 'SN',
      alpha3_code: 'SEN',
    },
    {
      id: 195,
      value: 'Singaporean',
      alpha2_code: 'SG',
      alpha3_code: 'SGP',
    },
    {
      id: 196,
      value: 'South Georgia or South Sandwich Islands',
      alpha2_code: 'GS',
      alpha3_code: 'SGS',
    },
    {
      id: 197,
      value: 'Saint Helenian',
      alpha2_code: 'SH',
      alpha3_code: 'SHN',
    },
    {
      id: 198,
      value: 'Svalbard',
      alpha2_code: 'SJ',
      alpha3_code: 'SJM',
    },
    {
      id: 199,
      value: 'Solomon Island',
      alpha2_code: 'SB',
      alpha3_code: 'SLB',
    },
    {
      id: 200,
      value: 'Sierra Leonean',
      alpha2_code: 'SL',
      alpha3_code: 'SLE',
    },
    {
      id: 201,
      value: 'Salvadoran',
      alpha2_code: 'SV',
      alpha3_code: 'SLV',
    },
    {
      id: 202,
      value: 'Sammarinese',
      alpha2_code: 'SM',
      alpha3_code: 'SMR',
    },
    {
      id: 203,
      value: 'Somali, Somalian',
      alpha2_code: 'SO',
      alpha3_code: 'SOM',
    },
    {
      id: 204,
      value: 'Saint-Pierrais or Miquelonnais',
      alpha2_code: 'PM',
      alpha3_code: 'SPM',
    },
    {
      id: 205,
      value: 'Serbian',
      alpha2_code: 'RS',
      alpha3_code: 'SRB',
    },
    {
      id: 206,
      value: 'South Sudanese',
      alpha2_code: 'SS',
      alpha3_code: 'SSD',
    },
    {
      id: 207,
      value: 'São Toméan',
      alpha2_code: 'ST',
      alpha3_code: 'STP',
    },
    {
      id: 208,
      value: 'Surinamese',
      alpha2_code: 'SR',
      alpha3_code: 'SUR',
    },
    {
      id: 209,
      value: 'Slovak',
      alpha2_code: 'SK',
      alpha3_code: 'SVK',
    },
    {
      id: 210,
      value: 'Slovenian, Slovene',
      alpha2_code: 'SI',
      alpha3_code: 'SVN',
    },
    {
      id: 211,
      value: 'Swedish',
      alpha2_code: 'SE',
      alpha3_code: 'SWE',
    },
    {
      id: 212,
      value: 'Swazi',
      alpha2_code: 'SZ',
      alpha3_code: 'SWZ',
    },
    {
      id: 213,
      value: 'Sint Maarten',
      alpha2_code: 'SX',
      alpha3_code: 'SXM',
    },
    {
      id: 214,
      value: 'Seychellois',
      alpha2_code: 'SC',
      alpha3_code: 'SYC',
    },
    {
      id: 215,
      value: 'Syrian',
      alpha2_code: 'SY',
      alpha3_code: 'SYR',
    },
    {
      id: 216,
      value: 'Turks and Caicos Island',
      alpha2_code: 'TC',
      alpha3_code: 'TCA',
    },
    {
      id: 217,
      value: 'Chadian',
      alpha2_code: 'TD',
      alpha3_code: 'TCD',
    },
    {
      id: 218,
      value: 'Togolese',
      alpha2_code: 'TG',
      alpha3_code: 'TGO',
    },
    {
      id: 219,
      value: 'Thai',
      alpha2_code: 'TH',
      alpha3_code: 'THA',
    },
    {
      id: 220,
      value: 'Tajikistani',
      alpha2_code: 'TJ',
      alpha3_code: 'TJK',
    },
    {
      id: 221,
      value: 'Tokelauan',
      alpha2_code: 'TK',
      alpha3_code: 'TKL',
    },
    {
      id: 222,
      value: 'Turkmen',
      alpha2_code: 'TM',
      alpha3_code: 'TKM',
    },
    {
      id: 223,
      value: 'Timorese',
      alpha2_code: 'TL',
      alpha3_code: 'TLS',
    },
    {
      id: 224,
      value: 'Tongan',
      alpha2_code: 'TO',
      alpha3_code: 'TON',
    },
    {
      id: 225,
      value: 'Trinidadian or Tobagonian',
      alpha2_code: 'TT',
      alpha3_code: 'TTO',
    },
    {
      id: 226,
      value: 'Tunisian',
      alpha2_code: 'TN',
      alpha3_code: 'TUN',
    },
    {
      id: 227,
      value: 'Turkish',
      alpha2_code: 'TR',
      alpha3_code: 'TUR',
    },
    {
      id: 228,
      value: 'Tuvaluan',
      alpha2_code: 'TV',
      alpha3_code: 'TUV',
    },
    {
      id: 229,
      value: 'Chinese, Taiwanese',
      alpha2_code: 'TW',
      alpha3_code: 'TWN',
    },
    {
      id: 230,
      value: 'Tanzanian',
      alpha2_code: 'TZ',
      alpha3_code: 'TZA',
    },
    {
      id: 231,
      value: 'Ugandan',
      alpha2_code: 'UG',
      alpha3_code: 'UGA',
    },
    {
      id: 232,
      value: 'Ukrainian',
      alpha2_code: 'UA',
      alpha3_code: 'UKR',
    },
    {
      id: 233,
      value: 'American',
      alpha2_code: 'UM',
      alpha3_code: 'UMI',
    },
    {
      id: 234,
      value: 'Uruguayan',
      alpha2_code: 'UY',
      alpha3_code: 'URY',
    },
    {
      id: 235,
      value: 'American',
      alpha2_code: 'US',
      alpha3_code: 'USA',
    },
    {
      id: 236,
      value: 'Uzbekistani, Uzbek',
      alpha2_code: 'UZ',
      alpha3_code: 'UZB',
    },
    {
      id: 237,
      value: 'Vatican',
      alpha2_code: 'VA',
      alpha3_code: 'VAT',
    },
    {
      id: 238,
      value: 'Saint Vincentian, Vincentian',
      alpha2_code: 'VC',
      alpha3_code: 'VCT',
    },
    {
      id: 239,
      value: 'Venezuelan',
      alpha2_code: 'VE',
      alpha3_code: 'VEN',
    },
    {
      id: 240,
      value: 'British Virgin Island',
      alpha2_code: 'VG',
      alpha3_code: 'VGB',
    },
    {
      id: 241,
      value: 'U.S. Virgin Island',
      alpha2_code: 'VI',
      alpha3_code: 'VIR',
    },
    {
      id: 242,
      value: 'Vietnamese',
      alpha2_code: 'VN',
      alpha3_code: 'VNM',
    },
    {
      id: 243,
      value: 'Ni-Vanuatu, Vanuatuan',
      alpha2_code: 'VU',
      alpha3_code: 'VUT',
    },
    {
      id: 244,
      value: 'Wallis and Futuna, Wallisian or Futunan',
      alpha2_code: 'WF',
      alpha3_code: 'WLF',
    },
    {
      id: 245,
      value: 'Samoan',
      alpha2_code: 'WS',
      alpha3_code: 'WSM',
    },
    {
      id: 246,
      value: 'Yemeni',
      alpha2_code: 'YE',
      alpha3_code: 'YEM',
    },
    {
      id: 247,
      value: 'South African',
      alpha2_code: 'ZA',
      alpha3_code: 'ZAF',
    },
    {
      id: 248,
      value: 'Zambian',
      alpha2_code: 'ZM',
      alpha3_code: 'ZMB',
    },
    {
      id: 249,
      value: 'Zimbabwean',
      alpha2_code: 'ZW',
      alpha3_code: 'ZWE',
    },
  ],
  ranks: [
    {
      id: 1,
      value: 'MASTER',
    },
    {
      id: 2,
      value: 'CHIEF OFFICER',
    },
    {
      id: 3,
      value: '2ND OFFICER',
    },
    {
      id: 4,
      value: '3RD OFFICER',
    },
    {
      id: 5,
      value: 'CADET',
    },
    {
      id: 6,
      value: 'CHIEF ENGINEER',
    },
    {
      id: 7,
      value: '2ND ENGINEER',
    },
    {
      id: 8,
      value: '3RD ENGINEER',
    },
    {
      id: 9,
      value: '4TH ENGINEER',
    },
    {
      id: 10,
      value: '5TH ENGINEER',
    },
    {
      id: 12,
      value: 'POEN',
    },
    {
      id: 13,
      value: 'POCT',
    },
    {
      id: 14,
      value: 'BOSUN',
    },
    {
      id: 15,
      value: 'AB',
    },
    {
      id: 16,
      value: 'MM',
    },
    {
      id: 17,
      value: 'OS',
    },
    {
      id: 18,
      value: 'WIPER',
    },
    {
      id: 19,
      value: 'GS',
    },
    {
      id: 20,
      value: 'TRAINEE OS',
    },
    {
      id: 21,
      value: 'TRAINEE WPR',
    },
    {
      id: 22,
      value: 'TRAINEE GS',
    },
    {
      id: 23,
      value: 'SUPY',
    },
    {
      id: 24,
      value: 'ELECTRICAL OFFICER',
    },
    {
      id: 25,
      value: 'REEFER ENG.',
    },
    {
      id: 26,
      value: 'PUMP MAN',
    },
    {
      id: 27,
      value: 'RPFW',
    },
    {
      id: 28,
      value: 'RPCL',
    },
    {
      id: 29,
      value: 'NAVAL ARCHITECT',
    },
    {
      id: 30,
      value: 'TECHNICIAN',
    },
    {
      id: 31,
      value: 'RADIO OFFICER',
    },
    {
      id: 32,
      value: 'JUNIOR OFFICER',
    },
    {
      id: 33,
      value: 'SUPT',
    },
    {
      id: 34,
      value: 'FITTER',
    },
    {
      id: 35,
      value: 'OILER',
    },
    {
      id: 36,
      value: 'ENGINE ROOM ARTIFICER',
    },
    {
      id: 37,
      value: 'TRAINEE COOK',
    },
    {
      id: 38,
      value: 'JUNIOR ENGINEER',
    },
    {
      id: 39,
      value: 'TRAINEE ELECTRICAL OFFICER',
    },
    {
      id: 40,
      value: 'WELDER',
    },
    {
      id: 41,
      value: '2ND COOK',
    },
    {
      id: 42,
      value: 'TRAINEE MARINE ENGINEER',
    },
    {
      id: 43,
      value: '3RD COOK',
    },
    {
      id: 44,
      value: 'TRAINEE REEFER ENG.',
    },
    {
      id: 45,
      value: '1st Mate(F.G)',
    },
    {
      id: 46,
      value: 'ADMIN OFFICER',
    },
    {
      id: 47,
      value: 'Additional Master',
    },
    {
      id: 48,
      value: 'Addl Ch/Offr',
    },
    {
      id: 49,
      value: 'GAS ENGINEER',
    },
    {
      id: 50,
      value: 'TRAINEE CHIEF OFFICER',
    },
    {
      id: 51,
      value: 'Engine Fitter',
    },
    {
      id: 52,
      value: 'Trainee Master',
    },
    {
      id: 53,
      value: 'Carpenter',
    },
    {
      id: 54,
      value: 'Assistant Electrical Officer',
    },
    {
      id: 55,
      value: 'Cargo Officer',
    },
    {
      id: 56,
      value: 'Additional 2nd Engineer',
    },
    {
      id: 57,
      value: 'Additional Chief Engineer',
    },
    {
      id: 58,
      value: 'GP',
    },
    {
      id: 59,
      value: 'Additional 3rd Engineer',
    },
    {
      id: 60,
      value: 'Painter',
    },
    {
      id: 61,
      value: 'GP2',
    },
    {
      id: 62,
      value: 'Port Captain',
    },
    {
      id: 63,
      value: 'Chief Cook',
    },
    {
      id: 64,
      value: '4 th Engineer / Electrical Officer',
    },
    {
      id: 65,
      value: 'Additional 3rd Officer',
    },
    {
      id: 66,
      value: 'Asst. 3rd Engineer',
    },
    {
      id: 67,
      value: '3rd Engineer / Electrical Officer',
    },
    {
      id: 68,
      value: 'Electrical Officer cum Reefer Engineer',
    },
    {
      id: 69,
      value: 'Marine Superintendent',
    },
    {
      id: 70,
      value: 'Technical Superintendent',
    },
    {
      id: 71,
      value: 'Office Executive',
    },
    {
      id: 72,
      value: 'Trainee 2nd Engineer',
    },
    {
      id: 73,
      value: 'Additional 4th Engineer',
    },
    {
      id: 74,
      value: 'Paint Technician',
    },
    {
      id: 75,
      value: '4th Officer',
    },
    {
      id: 76,
      value: 'Medical Officer',
    },
    {
      id: 77,
      value: 'Helicopter Officer',
    },
    {
      id: 78,
      value: 'Helicopter Crew',
    },
    {
      id: 79,
      value: 'Hotel Engineer',
    },
    {
      id: 80,
      value: 'AC Engineer',
    },
    {
      id: 81,
      value: 'Crane Operator',
    },
    {
      id: 82,
      value: 'Plumber',
    },
    {
      id: 83,
      value: 'Steward / Storekeeper',
    },
    {
      id: 84,
      value: 'Trainee Pumpman',
    },
    {
      id: 85,
      value: 'Senior Cadet',
    },
    {
      id: 86,
      value: 'Deck Boy',
    },
    {
      id: 87,
      value: 'Engine Boy',
    },
    {
      id: 88,
      value: 'Catering Boy',
    },
    {
      id: 89,
      value: 'Deck Trainee',
    },
    {
      id: 90,
      value: 'Engine Trainee',
    },
    {
      id: 91,
      value: 'Catering Trainee',
    },
    {
      id: 92,
      value: 'Additional 2nd Officer',
    },
    {
      id: 93,
      value: 'Trainee Gas Engineer',
    },
    {
      id: 94,
      value: 'Observer - Deck',
    },
    {
      id: 95,
      value: 'SMS Trainer',
    },
    {
      id: 96,
      value: 'Training Superintendent',
    },
    {
      id: 97,
      value: 'Electro Technical Officer',
    },
    {
      id: 98,
      value: 'Electro Technical Rating',
    },
    {
      id: 99,
      value: 'Electrician',
    },
    {
      id: 100,
      value: 'Officer Cadet',
    },
    {
      id: 101,
      value: 'Trainee Fitter',
    },
    {
      id: 102,
      value: 'Deck Rating Trainee',
    },
    {
      id: 103,
      value: 'Engine Rating Trainee',
    },
    {
      id: 104,
      value: 'Trainee Electrical Engineer',
    },
    {
      id: 105,
      value: 'Trainee Electrician',
    },
    {
      id: 106,
      value: 'Electrical Engineer',
    },
    {
      id: 107,
      value: 'Messman',
    },
    {
      id: 108,
      value: 'Deck Cadet',
    },
    {
      id: 109,
      value: 'Junior 3rd Officer',
    },
    {
      id: 110,
      value: 'Junior 4th Engineer',
    },
    {
      id: 111,
      value: 'Boys',
    },
    {
      id: 112,
      value: 'Trainees',
    },
    {
      id: 114,
      value: 'ENGINE CADET',
    },
    {
      id: 113,
      value: 'Nurse',
    },
  ],
  offices: [
    {
      id: 53,
      value: 'USA - Houston',
      ship_party_id: null,
    },
    {
      id: 54,
      value: 'Eugenia Ltd., Ukraine',
      ship_party_id: 1738,
    },
    {
      id: 55,
      value: 'Dong Jin Shipping, Korea',
      ship_party_id: null,
    },
    {
      id: 56,
      value: 'Conmart (Ship Agents) Ltd., Israel',
      ship_party_id: null,
    },
    {
      id: 57,
      value: 'USA - Connecticut',
      ship_party_id: null,
    },
    {
      id: 58,
      value: 'Ulsan, South Korea',
      ship_party_id: null,
    },
    {
      id: 59,
      value: 'Yangzhou, China',
      ship_party_id: null,
    },
    {
      id: 60,
      value: 'Cosco Shanghai Manning Co., Ltd.',
      ship_party_id: 1734,
    },
    {
      id: 61,
      value: 'Dalian Huayang Maritime Co., Ltd.',
      ship_party_id: 1735,
    },
    {
      id: 62,
      value: 'Shanghai Sinoship Seafarer Management Co. Ltd.',
      ship_party_id: null,
    },
    {
      id: 63,
      value: 'Tianjin Seastar Seaman Human Resources Management Co., Ltd.',
      ship_party_id: null,
    },
    {
      id: 65,
      value: 'Panstar Shipping , Busan Korea',
      ship_party_id: null,
    },
    {
      id: 66,
      value: 'SDSC Crew Manning Co., Ltd.',
      ship_party_id: null,
    },
    {
      id: 67,
      value: 'Dat Maritime Co. Ltd.',
      ship_party_id: null,
    },
    {
      id: 7167,
      value: 'test invite sing',
      ship_party_id: null,
    },
    {
      id: 16,
      value: 'SAJC, Vladivostok',
      ship_party_id: null,
    },
    {
      id: 18,
      value: 'Conship, Karachi',
      ship_party_id: null,
    },
    {
      id: 20,
      value: 'Reliance Shipping, Chittagong',
      ship_party_id: null,
    },
    {
      id: 108,
      value: 'Manning agency ship party 2',
      ship_party_id: null,
    },
    {
      id: 23,
      value: 'Star Traders, Burma',
      ship_party_id: null,
    },
    {
      id: 89,
      value: 'Southfield Agencies, Inc.',
      ship_party_id: 1759,
    },
    {
      id: 37,
      value: 'Lucknow',
      ship_party_id: null,
    },
    {
      id: 39,
      value: 'LAPA',
      ship_party_id: null,
    },
    {
      id: 41,
      value: 'Smart Crewing Agency Limited',
      ship_party_id: null,
    },
    {
      id: 43,
      value: 'Eurocrewing Novorossiysk',
      ship_party_id: null,
    },
    {
      id: 68,
      value: 'Norteam Shipping Services Inc., Philippines',
      ship_party_id: null,
    },
    {
      id: 69,
      value: 'Global Gateway Crewing Services, Philippines',
      ship_party_id: null,
    },
    {
      id: 70,
      value: 'Busan, S. Korea',
      ship_party_id: null,
    },
    {
      id: 71,
      value: 'Sinocrew, Shanghai',
      ship_party_id: null,
    },
    {
      id: 72,
      value: 'TJSM Co., Ltd., Korea',
      ship_party_id: 1760,
    },
    {
      id: 73,
      value: 'NanJing GoldenKingShipping Co., Ltd.',
      ship_party_id: 1746,
    },
    {
      id: 74,
      value: 'FMIPL',
      ship_party_id: null,
    },
    {
      id: 75,
      value: 'Unk Sagacious (Pvt) Ltd.',
      ship_party_id: 1860,
    },
    {
      id: 76,
      value: 'China Shipping Guangzhou',
      ship_party_id: 1732,
    },
    {
      id: 95,
      value: 'Istanbul',
      ship_party_id: null,
    },
    {
      id: 77,
      value: 'Beijing Shouhai International Econ. & Tech. Consultant Service Co., Ltd.',
      ship_party_id: null,
    },
    {
      id: 78,
      value: 'Qingdao Huayang Maritime Co., Ltd.',
      ship_party_id: 1752,
    },
    {
      id: 79,
      value: 'Turkey',
      ship_party_id: null,
    },
    {
      id: 80,
      value: 'Ceyline Shipping Ltd.',
      ship_party_id: 1811,
    },
    {
      id: 81,
      value: 'Ocean 21 Holdings Pte. Ltd',
      ship_party_id: null,
    },
    {
      id: 82,
      value: 'Shandong Fleet Management Limited',
      ship_party_id: null,
    },
    {
      id: 83,
      value: 'Ocean 21 / PT. Indomaritime Management',
      ship_party_id: null,
    },
    {
      id: 84,
      value: 'Hai Phong Marine Service & Trading Investment Limited Company',
      ship_party_id: 1743,
    },
    {
      id: 85,
      value: 'PT Sentra Makmur Lines (Crew Management)',
      ship_party_id: 1750,
    },
    {
      id: 86,
      value: 'Shandong Chinasun International Crew Management Ltd.',
      ship_party_id: null,
    },
    {
      id: 87,
      value: 'Dolphin Maritime Service Ltd',
      ship_party_id: 1736,
    },
    {
      id: 88,
      value: 'Polaris Shipping Co., Ltd',
      ship_party_id: null,
    },
    {
      id: 90,
      value: 'J-Phil Marine Inc.',
      ship_party_id: 1745,
    },
    {
      id: 91,
      value: 'Merrow Crewing Company, Ukraine',
      ship_party_id: null,
    },
    {
      id: 92,
      value: 'Alkyon Ltd, Ukraine',
      ship_party_id: null,
    },
    {
      id: 93,
      value: 'Alimark Crewing, Ukraine',
      ship_party_id: null,
    },
    {
      id: 94,
      value: 'MMSA, Myanmar',
      ship_party_id: null,
    },
    {
      id: 96,
      value: 'Goa',
      ship_party_id: null,
    },
    {
      id: 2,
      value: 'Manila',
      ship_party_id: null,
    },
    {
      id: 97,
      value: 'Denmark',
      ship_party_id: null,
    },
    {
      id: 98,
      value: 'Huayang Maritime Center',
      ship_party_id: 1744,
    },
    {
      id: 99,
      value: 'Jaipur',
      ship_party_id: null,
    },
    {
      id: 100,
      value: 'Ocean Star International Ship Management (Dalian) Co. Ltd.',
      ship_party_id: null,
    },
    {
      id: 101,
      value: 'HR Shipping Co., Ltd.',
      ship_party_id: 1742,
    },
    {
      id: 102,
      value: 'China Marine & Seamen Service Corporation (Mases-Coscoman)',
      ship_party_id: 1731,
    },
    {
      id: 105,
      value: 'Dubai',
      ship_party_id: null,
    },
    {
      id: 106,
      value: 'Singhai Marine Services (Shanghai) Co. Ltd',
      ship_party_id: 1858,
    },
    {
      id: 107,
      value: 'Sinosin Marine Services Co., Ltd, Xiamen',
      ship_party_id: 1859,
    },
    {
      id: 2048,
      value: 'Test-Manning-Agency-11-18-A',
      ship_party_id: null,
    },
    {
      id: 4274,
      value: 'MT MA',
      ship_party_id: null,
    },
    {
      id: 4395,
      value: 'testMA',
      ship_party_id: null,
    },
    {
      id: 6557,
      value: 'test new mannning agent 20220112',
      ship_party_id: null,
    },
    {
      id: 1,
      value: 'Mumbai - Andheri',
      ship_party_id: null,
    },
    {
      id: 3,
      value: 'Kochi',
      ship_party_id: null,
    },
    {
      id: 4,
      value: 'Kolkata',
      ship_party_id: null,
    },
    {
      id: 5,
      value: 'Chennai',
      ship_party_id: null,
    },
    {
      id: 6,
      value: 'Gurgaon',
      ship_party_id: null,
    },
    {
      id: 8,
      value: 'Singapore',
      ship_party_id: null,
    },
    {
      id: 4039,
      value: 'Shanghai New Xing Yang Marine Service Ltd.',
      ship_party_id: 1856,
    },
    {
      id: 10,
      value: 'London',
      ship_party_id: null,
    },
    {
      id: 12,
      value: 'Dalian, China',
      ship_party_id: 1854,
    },
    {
      id: 38,
      value: 'KT ShIP Ltd.',
      ship_party_id: null,
    },
    {
      id: 14,
      value: 'Cyprus',
      ship_party_id: null,
    },
    {
      id: 17,
      value: 'Singh Marine, Odessa',
      ship_party_id: null,
    },
    {
      id: 21,
      value: 'Unicorn Shipping, Chittagong',
      ship_party_id: null,
    },
    {
      id: 36,
      value: 'Bay Shipping Services, Dhaka',
      ship_party_id: null,
    },
    {
      id: 15,
      value: 'FEMF, Vladivostok',
      ship_party_id: null,
    },
    {
      id: 44,
      value: 'Flair Grup, Romania',
      ship_party_id: null,
    },
    {
      id: 40,
      value: 'AST, Karachi',
      ship_party_id: null,
    },
    {
      id: 42,
      value: 'Maritime Crewing Ltd., Georgia',
      ship_party_id: null,
    },
    {
      id: 19,
      value: 'Marine Agency, Chittagong',
      ship_party_id: null,
    },
    {
      id: 13,
      value: 'Patna',
      ship_party_id: null,
    },
    {
      id: 9,
      value: 'Visakhapatnam',
      ship_party_id: null,
    },
    {
      id: 11,
      value: 'Polaris Maritime, Szczecin',
      ship_party_id: null,
    },
    {
      id: 7,
      value: 'Hong Kong',
      ship_party_id: 1852,
    },
    {
      id: 45,
      value: 'Shanghai New Xing Yang Marine Service Ltd.',
      ship_party_id: null,
    },
    {
      id: 109,
      value: 'Manning agency party',
      ship_party_id: 1762,
    },
    {
      id: 46,
      value: 'Chandigarh',
      ship_party_id: null,
    },
    {
      id: 47,
      value: 'Global Marine Services',
      ship_party_id: null,
    },
    {
      id: 48,
      value: 'Mumbai - Borivali',
      ship_party_id: null,
    },
    {
      id: 49,
      value: 'Zhoushan, China',
      ship_party_id: null,
    },
    {
      id: 50,
      value: 'Auckland',
      ship_party_id: null,
    },
    {
      id: 51,
      value: 'Penglai',
      ship_party_id: null,
    },
    {
      id: 52,
      value: 'Jiangsu',
      ship_party_id: null,
    },
    {
      id: 4038,
      value: 'Friday Party',
      ship_party_id: 1867,
    },
    {
      id: 752,
      value: 'MT test1',
      ship_party_id: 1765,
    },
    {
      id: 753,
      value: 'MT Test2',
      ship_party_id: 1766,
    },
    {
      id: 754,
      value: 'MA3 testing',
      ship_party_id: 1767,
    },
    {
      id: 755,
      value: 'TOTO',
      ship_party_id: 1768,
    },
    {
      id: 22,
      value: 'Cross Trade Shipping, Chittagong',
      ship_party_id: null,
    },
    {
      id: 24,
      value: 'Sanco Line, Colombo',
      ship_party_id: null,
    },
    {
      id: 756,
      value: 'MA approval testing',
      ship_party_id: 1770,
    },
    {
      id: 25,
      value: 'Cosco Dalian Manning Cooperation Co., Ltd.',
      ship_party_id: 1733,
    },
    {
      id: 26,
      value: 'Gold Fleet, Dalian',
      ship_party_id: null,
    },
    {
      id: 27,
      value: '-',
      ship_party_id: null,
    },
    {
      id: 758,
      value: 'TEST ABC',
      ship_party_id: 1772,
    },
    {
      id: 28,
      value: 'Gadot Yam',
      ship_party_id: null,
    },
    {
      id: 29,
      value: 'Sea Queen',
      ship_party_id: null,
    },
    {
      id: 759,
      value: 'Manning Agency to PARIS 1.0',
      ship_party_id: 1775,
    },
    {
      id: 30,
      value: 'Nova Shipping',
      ship_party_id: null,
    },
    {
      id: 760,
      value: 'Testing-MA-14-09',
      ship_party_id: 1776,
    },
    {
      id: 761,
      value: 'MA Test0004',
      ship_party_id: 1777,
    },
    {
      id: 31,
      value: 'MYINT',
      ship_party_id: null,
    },
    {
      id: 32,
      value: 'MCL - Crewing, Romania',
      ship_party_id: null,
    },
    {
      id: 762,
      value: 'Testing Agency',
      ship_party_id: 1778,
    },
    {
      id: 33,
      value: 'Mumbai - Nerul',
      ship_party_id: null,
    },
    {
      id: 757,
      value: 'Test-Manning-Agency-13-09-ag',
      ship_party_id: 1771,
    },
    {
      id: 763,
      value: 'RACHIDMANNINGAGENCY27Sep',
      ship_party_id: 1782,
    },
    {
      id: 34,
      value: "China Shipping Int'l Shipmanagement",
      ship_party_id: null,
    },
    {
      id: 764,
      value: 'Abcc',
      ship_party_id: 1727,
    },
    {
      id: 765,
      value: 'Manning Agency',
      ship_party_id: 1805,
    },
    {
      id: 35,
      value: 'BIMS',
      ship_party_id: null,
    },
    {
      id: 766,
      value: 'MAMAMIA',
      ship_party_id: 1806,
    },
    {
      id: 767,
      value: 'test',
      ship_party_id: 1816,
    },
    {
      id: 1647,
      value: 'CQC Crew Manning Company',
      ship_party_id: 1764,
    },
    {
      id: 103,
      value: 'Qingdao Ocean Line-Yuanfengrun International Manning Co. Ltd.',
      ship_party_id: 1753,
    },
    {
      id: 768,
      value: 'Manning-Agency-1210',
      ship_party_id: 1815,
    },
    {
      id: 104,
      value: 'Sinosin Marine Services Co., Ltd, Shanghai',
      ship_party_id: 1758,
    },
    {
      id: 769,
      value: 'Automated-Test-Ship-Party-4bv2',
      ship_party_id: 1820,
    },
    {
      id: 877,
      value: 'Dalian Sino Maritime Co., Ltd.',
      ship_party_id: null,
    },
    {
      id: 878,
      value: 'Rizhao Huanyu Seamen Service Co., Ltd.',
      ship_party_id: null,
    },
    {
      id: 879,
      value: 'Qingdao',
      ship_party_id: null,
    },
    {
      id: 880,
      value: 'China Marine & Seamen Service Xiamen Corp.',
      ship_party_id: null,
    },
    {
      id: 881,
      value: 'Shanghai Xing Yang Shipping Co., Ltd',
      ship_party_id: null,
    },
    {
      id: 882,
      value: 'Manning Agency',
      ship_party_id: null,
    },
    {
      id: 883,
      value: 'MAMAMIA',
      ship_party_id: null,
    },
    {
      id: 884,
      value: 'test',
      ship_party_id: null,
    },
    {
      id: 885,
      value: 'Manning-Agency-1210',
      ship_party_id: null,
    },
    {
      id: 4275,
      value: 'MT MAM Test',
      ship_party_id: 1874,
    },
    {
      id: 1002,
      value: 'Automated-Test-Ship-Party-qelj',
      ship_party_id: 1823,
    },
    {
      id: 1003,
      value: 'Automated-Test-Ship-Party-2uc6',
      ship_party_id: 1828,
    },
    {
      id: 4516,
      value: 'Shanghai Xing Yang Shipping Co., Ltd',
      ship_party_id: 1861,
    },
  ],
  signoffReason: [
    {
      id: 1,
      value: 'Completion of Contract',
    },
    {
      id: 2,
      value: 'Compassionate',
    },
    {
      id: 3,
      value: 'Medical',
    },
    {
      id: 4,
      value: 'Disciplinary',
    },
    {
      id: 5,
      value: 'Mutual Consent',
    },
    {
      id: 6,
      value: 'Transfer',
    },
    {
      id: 7,
      value: 'Promotion / Increment',
    },
    {
      id: 8,
      value: 'Desertion',
    },
    {
      id: 9,
      value: 'Personal Reasons',
    },
    {
      id: 10,
      value: 'Other',
    },
    {
      id: 11,
      value: 'Vessel Handed Over / Sold',
    },
  ],
};

export const visaRegionReferenceApiResponseData = {
  visaRegions: [
    {
      name: 'Hong Kong',
    },
    {
      name: 'Romania',
    },
    {
      name: 'United States',
    },
    {
      name: 'Schengen',
    },
    {
      name: 'Brazil',
    },
    {
      name: 'Russia',
    },
    {
      name: 'Australia',
    },
    {
      name: 'Singapore',
    },
    {
      name: 'India',
    },
    {
      name: 'United Kingdom',
    },
  ],
  count: 10,
};
