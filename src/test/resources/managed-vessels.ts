export const dropdownData = {
  data: {
    vesselTypes: [
      {
        id: 9,
        value: 'General Cargo',
      },
      {
        id: 10,
        value: 'Heavy Lift Vessel',
      },
      {
        id: 11,
        value: 'Livestock Carrier',
      },
      {
        id: 12,
        value: 'Log Carrier',
      },
    ],
    owners: [
      {
        id: 2,
        value: 'Great Field Limited',
      },
      {
        id: 3,
        value: 'Blue Forest Shipping Co.',
      },
      {
        id: 4,
        value: 'Kowa Kaiun Co. Ltd.',
      },
      {
        id: 6,
        value: 'Scottish Ship Owners & Managers Pty Ltd.',
      },
      {
        id: 7,
        value: 'Seven Seas Carriers',
      },
      {
        id: 8,
        value: 'Shoei Kisen Kaisha Ltd.',
      },
      {
        id: 9,
        value: 'B. Skaugen Shipping AS',
      },
      {
        id: 10,
        value: 'Spar Shipping AS',
      },
    ],
  },
};

export const keycloakData = [
  {
    full_name: 'Bunker Tech',
  },
  {
    full_name: 'Celsius Tech',
  },
  {
    full_name: 'CY Tech D1',
  },
  {
    full_name: 'CY Tech D2',
  },
];

export const techGroupKeycloakData = [
  { id: 1, value: 'Bunker Tech' },
  { id: 2, value: 'Celsius Tech' },
  { id: 3, value: 'CY Tech D1' },
  { id: 4, value: 'CY Tech D2' },
];

export const mockVesselData = {
  data: {
    results: [
      {
        id: 1,
        name: 'Test Vessel',
        vessel_type: {
          value: 'Bulk Carrier',
        },
        vessel: {
          id: 234,
        },
        flags: [
          {
            office: {
              value: 'HK',
            },
          },
        ],
        owner: { value: 'Rich Owner' },
        fleet_staff: {
          tech_group_group_head: {
            full_name: 'Master of Bulk Carrier',
          },
          tech_group: 'Tech T1',
        },
      },
      {
        id: 2,
        name: 'Second Vessel',
        vessel_type: {
          value: 'Bulk Carrier',
        },
      },
    ],
  },
};

export const mockItineraryData = {
  data: {
    results: [
      {
        id: '1',
        name: 'Test Itinerary',
        port: 'Habour',
        country: 'China',
      },
    ],
  },
};
