export const getContactLogResponse = [
  {
    id: 251,
    is_latest: false,
    created_by_hash: null,
    created_at: '2022-03-26T02:20:01.000Z',
    updated_at: '2022-05-06T07:45:05.884Z',
    seafarer_id: 37025,
    contact_date: '2019-01-28T00:00:00.000Z',
    next_contact_date: '2019-02-04T00:00:00.000Z',
    contact_mode: 'telephone',
    contact_mode_id: 1,
    general_remarks: null,
    availability_date: '2019-01-05T00:00:00.000Z',
    availability_remarks: 'online',
    docs_in_hand: false,
    created_by: '<EMAIL>',
  },
  {
    id: 454,
    is_latest: true,
    created_by_hash: 'MCVpGki4SIWwUdh6VhE80Kyc1aLqdHjnrrHtwBqtovnaptw4lTDf4fY9aOSFgtwn',
    created_at: '2022-05-06T07:55:04.666Z',
    updated_at: '2022-05-06T07:55:04.666Z',
    seafarer_id: 37025,
    contact_date: '2022-04-04T00:00:00.000Z',
    next_contact_date: '2022-09-04T00:00:00.000Z',
    contact_mode: 'telephone',
    contact_mode_id: 1,
    general_remarks: 'dsfsdfsd',
    availability_date: '2022-09-21T00:00:00.000Z',
    availability_remarks: 'huhu',
    docs_in_hand: false,
    created_by: '<EMAIL>',
  },
  {
    id: 453,
    is_latest: false,
    created_by_hash: 'MCVpGki4SIWwUdh6VhE80Kyc1aLqdHjnrrHtwBqtovnaptw4lTDf4fY9aOSFgtwn',
    created_at: '2022-05-06T07:45:05.779Z',
    updated_at: '2022-05-06T07:55:04.702Z',
    seafarer_id: 37025,
    contact_date: '2022-04-04T00:00:00.000Z',
    next_contact_date: '2022-09-04T00:00:00.000Z',
    contact_mode: 'telephone',
    contact_mode_id: 1,
    general_remarks: 'dsfsdfsd',
    availability_date: '2022-09-21T00:00:00.000Z',
    availability_remarks: 'huhu',
    docs_in_hand: false,
    created_by: '<EMAIL>',
  },
];
