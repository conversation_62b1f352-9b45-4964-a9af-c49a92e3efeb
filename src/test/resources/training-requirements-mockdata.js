export const trainingRequirementsApiResponse = [
  {
    id: 1,
    seafarer_id: 13,
    recommended_date: '2018-12-30T00:00:00.000Z',
    vessel_ownership_id: 42,
    vessel_name: 'Jack<PERSON>',
    deadline_date: '2001-12-31T00:00:00.000Z',
    completed_date: null,
    training_needs: 'Statutory courses as required',
    training_imparted: 'Safer /EMS Done',
    ref_id: 62618,
    supt_appraisal_id: null,
    master_appraisal_id: null,
    created_by_hash: '7K/iHp1qxdsBqccLt2u7fg==',
    updated_by_hash: '7K/iHp1qxdsBqccLt2u7fg==',
    created_at: '2020-04-06T19:26:00.753Z',
    updated_at: '2020-04-06T19:26:00.753Z',
    seafarer_training_requirement_document: [
      {
        id: 1,
        seafarer_document_id: 1212,
        seafarer_training_requirement_id: 1,
        created_at: '2023-07-13T06:43:55.465Z',
        updated_at: '2023-07-13T06:43:55.465Z',
        seafarer_document: {
          id: 1212,
          seafarer_person_id: 42,
          type: 'stcw',
          ref_id: 1921,
          doc_path: ' ui**/14**-M2**.pd**',
          is_deleted: false,
          created_at: '2022-03-23T04:29:45.508Z',
          updated_at: '2022-03-24T00:30:49.130Z',
          created_by_hash: 'ACgvbUHDL7hPqu/zKWU6e4GlVu9lHNX1Q3eWwc8bip0=',
          updated_by_hash: 'ACgvbUHDL7hPqu/zKWU6e4GlVu9lHNX1Q3eWwc8bip0=',
        },
      },
    ],
  },
];
