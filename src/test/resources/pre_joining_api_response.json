{"seafarer_id": 108701, "seafarer_person_id": 108701, "seafarer_status_history": {"id": 1479023, "seafarer_person_id": 108701, "seafarer_account_status": "active", "seafarer_journey_status": "crew_assignment_approved", "seafarer_exam_status": null, "rank_id": 26, "vessel_name": "Bochem Mumbai", "vessel_ref_id": 4589, "created_by_hash": "<EMAIL>", "created_by": "<PERSON><PERSON>", "seafarer_journey_remarks": "", "seafarer_exam_remarks": null, "status_date": "2021-09-08T00:00:00.000Z", "vessel_ownership_id": 520, "sign_off_date": null, "expected_contract_end_date": null, "embarkation_port": null, "repatriation_port": null, "vessel_tech_group": "Tech T10", "vessel_type": "Chemical Tanker", "replaced_by_id": null, "is_p1_history": null, "created_at": "2022-04-02T08:04:06.930Z", "updated_at": "2022-04-02T08:04:06.930Z", "created_at_p1": "2021-09-08T10:37:09.000Z", "updated_at_p1": "2021-09-08T10:37:09.000Z", "paris1_ref_id": 1452589, "is_current_status": false}, "seafarer_wages": {"amount_basic": 9999.05, "amount_total": 12000.05, "unit": "usd", "recommended_wages": 10000.0}, "seafarer_allotment": {"monthly_allotment": 100.0, "first_allotment": 100.0, "unit": "usd", "created_at": "2022-06-29T12:13:33.521Z", "updated_at": "2022-06-29T12:13:33.521Z", "created_by": "<PERSON>", "updated_by": "<PERSON>"}, "seafarer_joining_spendings": [{"id": 1, "seafarer_joining_spednings_reason": {"id": 1, "type": "expense", "value": "JNG/Repat Expenses", "ref_id": 1}, "amount": 100.0, "unit": "usd", "remarks": "this is remarks", "ref_id": 1, "created_at": "2022-06-29T12:13:33.521Z", "updated_at": "2022-06-29T12:13:33.521Z", "created_by": "<PERSON>", "updated_by": "<PERSON>"}, {"id": 2, "seafarer_joining_spednings_reason": {"id": 2, "type": "expense", "value": "Rejoining Bonus", "ref_id": 2}, "amount": 100.0, "unit": "usd", "remarks": "this is remarks", "ref_id": 1, "created_at": "2022-06-29T12:13:33.521Z", "updated_at": "2022-06-29T12:13:33.521Z", "created_by": "<PERSON>", "updated_by": "<PERSON>"}, {"id": 3, "seafarer_joining_spednings_reason": {"id": 3, "type": "expense", "value": "Travelling Expenses", "ref_id": 3}, "amount": 100.0, "unit": "usd", "remarks": "this is remarks", "ref_id": 1, "created_at": "2022-06-29T12:13:33.521Z", "updated_at": "2022-06-29T12:13:33.521Z", "created_by": "<PERSON>", "updated_by": "<PERSON>"}, {"id": 4, "seafarer_joining_spednings_reason": {"id": 4, "type": "expense", "value": "US Visa", "ref_idx": 4}, "amount": 100.0, "unit": "usd", "remarks": "this is remarks", "ref_id": 1, "created_at": "2022-06-29T12:13:33.521Z", "updated_at": "2022-06-29T12:13:33.521Z", "created_by": "<PERSON>", "updated_by": "<PERSON>"}, {"id": 5, "seafarer_joining_spednings_reason": {"id": 5, "type": "expense", "value": "Other Expenses", "ref_id": 5}, "amount": 100.0, "unit": "usd", "remarks": "this is remarks", "ref_id": 1, "created_at": "2022-06-29T12:13:33.521Z", "updated_at": "2022-06-29T12:13:33.521Z", "created_by": "<PERSON>", "updated_by": "<PERSON>"}, {"id": 6, "seafarer_joining_spednings_reason": {"id": 6, "type": "deduction", "value": "Airfare/Rep.", "ref_id": 6}, "amount": 100.0, "unit": "usd", "remarks": "this is remarks", "ref_id": 1, "created_at": "2022-06-29T12:13:33.521Z", "updated_at": "2022-06-29T12:13:33.521Z", "created_by": "<PERSON>", "updated_by": "<PERSON>"}, {"id": 7, "seafarer_joining_spednings_reason": {"id": 7, "type": "deduction", "value": "Cash from office", "ref_id": 7}, "amount": 100.0, "unit": "usd", "remarks": "this is remarks", "ref_id": 1, "created_at": "2022-06-29T12:13:33.521Z", "updated_at": "2022-06-29T12:13:33.521Z", "created_by": "<PERSON>", "updated_by": "<PERSON>"}, {"id": 8, "seafarer_joining_spednings_reason": {"id": 8, "type": "deduction", "value": "Visa", "ref_id": 8}, "amount": 100.0, "unit": "usd", "remarks": "this is remarks", "ref_id": 1, "created_at": "2022-06-29T12:13:33.521Z", "updated_at": "2022-06-29T12:13:33.521Z", "created_by": "<PERSON>", "updated_by": "<PERSON>"}], "created_at": "2022-06-29T12:13:33.521Z", "updated_at": "2022-06-29T12:13:33.521Z", "created_by": "<PERSON>", "updated_by": "<PERSON>"}