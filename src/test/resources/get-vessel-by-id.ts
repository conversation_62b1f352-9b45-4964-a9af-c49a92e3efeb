export const getVesselById = () => {
    return {
        "id": 1192,
        "name": "Atlantic Marble",
        "emails": [
          {
            "id": 1967,
            "created_at": "2020-10-22T09:59:14.254Z",
            "updated_at": "2020-10-22T09:59:14.254Z",
            "vessel_id": 1192,
            "email_type_id": 1,
            "email": "<EMAIL>"
          }
        ],
        "imo_number": "9798959",
        "vessel_hull_number": "H2642",
        "shipyard_text": null,
        "owner_id": 28,
        "misc_registered_owner_id": 638,
        "ref_id": 5231,
        "vessel_short_code": "AMB",
        "expected_date_of_takeover": "2018-03-21T00:00:00.000Z",
        "date_of_takeover": "2018-03-22T00:00:00.000Z",
        "vessel_account_code_new": "1726",
        "vessel_tec_fac_code": "0689",
        "techgroup": "Tech T5",
        "status": "active",
        "created_by": "paris1",
        "updated_by": null,
        "created_by_hash": "9qX9VU1Fw/XpO69G0YgDKw==",
        "updated_by_hash": null,
        "date_of_handover": null,
        "pending_status": null,
        "images": [
          {
            "created_at": "2018-02-25T22:19:16.370Z",
            "updated_at": "2018-02-25T22:19:16.370Z",
            "vessel_id": 1192,
            "path": "/ui2018/1519596999_IMG_4543.JPG",
            "order": 1,
            "caption": "Atlantic Marble",
            "ref_id": 8454,
            "id": 969
          }
        ],
        "ownerships": [
          {
            "id": 1299,
            "vessel_id": 1192,
            "owner_id": 28,
            "registered_owner_id": 638,
            "expected_owner_start_date": "2018-03-21T00:00:00.000Z",
            "owner_start_date": "2018-03-22T00:00:00.000Z",
            "owner_end_date": null,
            "wages_treatment": null,
            "wages_accumulation": null,
            "emails": [
              {
                "id": 1516,
                "created_at": "2020-10-22T09:59:14.254Z",
                "updated_at": "2020-10-22T09:59:14.254Z",
                "ownership_id": 1299,
                "email_type_id": 1,
                "email": "<EMAIL>",
                "email_type": {
                  "id": 1,
                  "value": "E-Mail Address",
                  "ref_id": null
                }
              },
              {
                "id": 4348,
                "created_at": "2020-10-22T09:59:14.254Z",
                "updated_at": "2020-10-22T09:59:14.254Z",
                "ownership_id": 1299,
                "email_type_id": 1,
                "email": "<EMAIL>",
                "email_type": {
                  "id": 1,
                  "value": "E-Mail Address",
                  "ref_id": null
                }
              }
            ],
            "phones": [
              {
                "id": 10351,
                "created_at": "2020-10-22T09:59:14.254Z",
                "updated_at": "2020-10-22T09:59:14.254Z",
                "ownership_id": 1299,
                "phone_type_id": 1,
                "phone_number": "Voice: +870 773154686  Fax: 783200154  Data64: 783253805",
                "phone_type": {
                  "id": 1,
                  "value": "Fleet Broadband",
                  "ref_id": null,
                  "created_at": "2021-07-30T05:03:59.672Z",
                  "updated_at": "2021-07-30T05:03:59.672Z"
                }
              },
              {
                "id": 10352,
                "created_at": "2020-10-22T09:59:14.254Z",
                "updated_at": "2020-10-22T09:59:14.254Z",
                "ownership_id": 1299,
                "phone_type_id": 3,
                "phone_number": "881651435306 (Sailor SC4150)",
                "phone_type": {
                  "id": 3,
                  "value": "Iridium / IsatPhone Phone (CITADEL Space)",
                  "ref_id": null,
                  "created_at": "2021-07-30T05:03:59.672Z",
                  "updated_at": "2021-07-30T05:03:59.672Z"
                }
              }
            ],
            "created_at": "2021-08-12T06:13:06.929Z",
            "updated_at": "2022-08-11T03:13:04.440Z",
            "class_notations": [
              {
                "ownership_id": 1299,
                "notation": "KR",
                "vessel_class_id": 15,
                "id": 396,
                "created_at": "2020-10-22T10:28:07.453Z",
                "updated_at": "2020-10-22T10:28:07.453Z",
                "vessel_class": {
                  "id": 15,
                  "value": "KR",
                  "ref_id": null,
                  "created_at": "2021-07-30T05:03:59.672Z",
                  "updated_at": "2021-07-30T05:03:59.672Z"
                }
              },
              {
                "ownership_id": 1299,
                "notation": "KR",
                "vessel_class_id": 15,
                "id": 1209,
                "created_at": "2020-10-22T10:28:07.453Z",
                "updated_at": "2020-10-22T10:28:07.453Z",
                "vessel_class": {
                  "id": 15,
                  "value": "KR",
                  "ref_id": null,
                  "created_at": "2021-07-30T05:03:59.672Z",
                  "updated_at": "2021-07-30T05:03:59.672Z"
                }
              }
            ]
          }
        ],
        "created_at": "2020-10-22T09:59:14.251Z",
        "updated_at": "2021-12-22T08:28:30.403Z",
        "vessel_service_status_id": 2,
        "vessel_type_id": 42,
        "shipyard_id": null,
        "date_of_delivery": "2018-03-22T00:00:00.000Z",
        "year_of_delivery": null,
        "flag_id": 5,
        "call_sign": "V7MJ4",
        "h_m_underwriter_id": 85,
        "p_i_club_id": 5,
        "port_of_registry_text": "MAJURO",
        "port_of_registry_id": null,
        "life_boat_capacity": "25.0000",
        "length_oa": "183.0000",
        "length_bp": "174175.2700",
        "depth": "19.1000",
        "breadth_extreme": "32.2000",
        "summer_draft": null,
        "summer_dwt": "49999.0000",
        "international_grt": "29256.0000",
        "international_nrt": "13876.0000",
        "service_speed": null,
        "vessel_account_code": 689,
        "supdt_name": null,
        "supdt_email": null,
        "misc_engine_id": null,
        "dwt": 50001,
        "bhp": 9743,
        "misc_operator_id": null,
        "misc_manager_id": 1,
        "misc_currency_id": 2,
        "is_manning_manager": "no",
        "misc_flag_isps_id": 5,
        "misc_classification_id": 9,
        "misc_classification_society_id": 6,
        "misc_qi_id": 1,
        "misc_osro_id": 1,
        "misc_salvage_id": 5,
        "misc_media_response_id": 1,
        "misc_management_type_id": null,
        "misc_other_contacts_id": 2,
        "us_visa_required": "no",
        "has_portage_bill": "yes",
        "flag_country": "Marshall Islands",
        "emission_type_id": 10,
        "maximum_continuous_rating_kw": "7180.0000",
        "maximum_continuous_rating_rpm": "86.9000",
        "shipyard": null,
        "flag": {
          "id": 5,
          "value": "Republic of Marshall Islands, Hong Kong",
          "ref_id": 3048,
          "created_at": "2016-07-26T12:03:44.690Z",
          "updated_at": "2016-07-26T12:03:44.690Z"
        },
        "h_m_underwriter": {
          "id": 85,
          "value": "KB Insurance Co., Ltd.",
          "ref_id": 4597,
          "created_at": "2018-02-23T08:53:35.749Z",
          "updated_at": "2022-05-16T00:17:47.821Z",
          "ship_party_id": 1555
        },
        "port_of_registry": null,
        "p_i_club": {
          "id": 5,
          "value": "Gard, Hong Kong",
          "ref_id": 3066,
          "created_at": "2019-08-29T09:14:44.500Z",
          "updated_at": "2022-05-16T00:17:49.644Z",
          "ship_party_id": 1591
        },
        "misc_engine": null,
        "service_status": {
          "id": 2,
          "value": "In Service",
          "ref_id": null,
          "created_at": "2021-07-30T05:03:59.672Z",
          "updated_at": "2021-07-30T05:03:59.672Z"
        },
        "misc_operator": null,
        "vessel_type": {
          "id": 42,
          "value": "Oil cum Chemical Tanker",
          "ref_id": 2021424,
          "created_at": "2014-03-17T03:47:05.000Z",
          "updated_at": "2014-03-17T03:47:05.000Z",
          "type": "tanker"
        },
        "owner": {
          "id": 28,
          "value": "CIDO Shipping Co., Ltd.",
          "ref_id": 3126,
          "ship_party_id": 24,
          "created_at": "2021-07-30T05:03:59.672Z",
          "updated_at": "2021-07-30T05:03:59.672Z"
        },
        "misc_registered_owner": {
          "id": 638,
          "value": "Infinite Future Shipping Inc.",
          "ref_id": 4635,
          "ship_party_id": 941,
          "created_at": "2021-07-30T05:03:59.672Z",
          "updated_at": "2021-07-30T05:03:59.672Z"
        },
        "misc_manager": {
          "id": 1,
          "value": "Fleet Management Limited",
          "ref_id": 1000,
          "created_at": "2022-07-25T01:55:19.504Z",
          "updated_at": "2022-07-25T01:55:19.504Z"
        },
        "misc_currency": {
          "id": 2,
          "value": "USD",
          "ref_id": 5006702,
          "created_at": "2016-07-13T06:33:35.739Z",
          "updated_at": "2016-07-13T06:33:35.739Z"
        },
        "misc_flag_isps": {
          "id": 5,
          "value": "THE REPUBLIC OF MARSHALL ISLANDS (ISPS)",
          "ref_id": 3155,
          "created_at": "2016-07-26T12:04:14.685Z",
          "updated_at": "2016-07-26T12:04:14.685Z"
        },
        "misc_classification": {
          "id": 9,
          "value": "Korean Register of Shipping, Hong Kong",
          "ref_id": 3147,
          "created_at": "2013-08-29T03:01:12.443Z",
          "updated_at": "2013-08-29T03:01:12.443Z"
        },
        "misc_classification_society": {
          "id": 6,
          "value": "Korean Register - ERS",
          "ref_id": 4210,
          "created_at": "2020-09-28T08:43:04.350Z",
          "updated_at": "2020-09-28T08:43:04.350Z"
        },
        "misc_qi": {
          "id": 1,
          "value": "O'Brien's Response Management",
          "ref_id": 3056,
          "created_at": "2018-08-03T02:52:24.570Z",
          "updated_at": "2018-08-03T02:52:24.570Z"
        },
        "misc_management_type": null,
        "misc_osro": {
          "id": 1,
          "value": "NATIONAL RESPONSE CORPORATION",
          "ref_id": 3059,
          "created_at": "2013-07-08T07:42:29.566Z",
          "updated_at": "2013-07-08T07:42:29.566Z"
        },
        "misc_salvage": {
          "id": 5,
          "value": "Donjon -Smit",
          "ref_id": 3486,
          "created_at": "2013-11-12T04:46:19.821Z",
          "updated_at": "2013-11-12T04:46:19.821Z"
        },
        "misc_media_response": {
          "id": 1,
          "value": "MTI UK",
          "ref_id": 3251,
          "created_at": "2020-07-02T01:46:38.102Z",
          "updated_at": "2020-07-02T01:46:38.102Z"
        },
        "misc_other_contacts": {
          "id": 2,
          "value": "24 Hour Emergency Chemical Support",
          "ref_id": 3162,
          "created_at": "2018-06-06T04:04:24.957Z",
          "updated_at": "2018-06-06T04:04:24.957Z"
        },
        "emission_type": {
          "id": 10,
          "created_at": "2021-10-28T05:10:00.156Z",
          "updated_at": "2021-10-28T05:10:00.156Z",
          "emission_type": "LR1 (Large Range) Tanker",
          "specification": "(DWT 45 to <80k)"
        },
        "sea_trials": [
          {
            "id": 133,
            "created_at": "2021-11-15T09:19:02.638Z",
            "updated_at": "2021-11-15T09:19:02.638Z",
            "vessel_id": 1192,
            "deleted_at": null,
            "load": "0.6600",
            "corrected_sfoc": "166.8000",
            "kw": "4746.0000",
            "rpm": "81.0000",
            "speed": "13.5800",
            "tons_per_hour": "0.9040"
          },
          {
            "id": 134,
            "created_at": "2021-11-15T09:19:02.638Z",
            "updated_at": "2021-11-15T09:19:02.638Z",
            "vessel_id": 1192,
            "deleted_at": null,
            "load": "0.7800",
            "corrected_sfoc": "165.1600",
            "kw": "5598.0000",
            "rpm": "84.4000",
            "speed": "14.2400",
            "tons_per_hour": "1.0560"
          },
          {
            "id": 135,
            "created_at": "2021-11-15T09:19:02.638Z",
            "updated_at": "2021-11-15T09:19:02.638Z",
            "vessel_id": 1192,
            "deleted_at": null,
            "load": "0.8800",
            "corrected_sfoc": "165.5800",
            "kw": "6324.0000",
            "rpm": "88.0000",
            "speed": "14.9000",
            "tons_per_hour": "1.1960"
          }
        ],
        "shop_trials": [
          {
            "id": 53,
            "created_at": "2021-11-15T09:26:10.861Z",
            "updated_at": "2021-11-15T09:26:10.861Z",
            "vessel_id": 1192,
            "deleted_at": null,
            "load": "0.2500",
            "corrected_sfoc": "189.4000"
          },
          {
            "id": 54,
            "created_at": "2021-11-15T09:26:10.861Z",
            "updated_at": "2021-11-15T09:26:10.861Z",
            "vessel_id": 1192,
            "deleted_at": null,
            "load": "0.5000",
            "corrected_sfoc": "178.6300"
          },
          {
            "id": 55,
            "created_at": "2021-11-15T09:26:10.861Z",
            "updated_at": "2021-11-15T09:26:10.861Z",
            "vessel_id": 1192,
            "deleted_at": null,
            "load": "0.7500",
            "corrected_sfoc": "174.4200"
          },
          {
            "id": 56,
            "created_at": "2021-11-15T09:26:10.861Z",
            "updated_at": "2021-11-15T09:26:10.861Z",
            "vessel_id": 1192,
            "deleted_at": null,
            "load": "0.5000",
            "corrected_sfoc": "173.6500"
          }
        ],
        "class_notations": [
          {
            "id": 17,
            "created_at": "2020-10-22T10:28:07.453Z",
            "updated_at": "2020-10-22T10:28:07.453Z",
            "vessel_id": 1192,
            "vessel_class_id": 15,
            "notation": "KR"
          }
        ],
        "phones": [
          {
            "id": 4278,
            "created_at": "2020-10-22T09:59:14.254Z",
            "updated_at": "2020-10-22T09:59:14.254Z",
            "vessel_id": 1192,
            "phone_type_id": 6,
            "phone_number": "453845245 / 453845246"
          },
          {
            "id": 4279,
            "created_at": "2020-10-22T09:59:14.254Z",
            "updated_at": "2020-10-22T09:59:14.254Z",
            "vessel_id": 1192,
            "phone_type_id": 1,
            "phone_number": "Voice: +870 773154686  Fax: 783200154  Data64: 783253805"
          },
          {
            "id": 4280,
            "created_at": "2020-10-22T09:59:14.254Z",
            "updated_at": "2020-10-22T09:59:14.254Z",
            "vessel_id": 1192,
            "phone_type_id": 3,
            "phone_number": "881651435306 (Sailor SC4150)"
          }
        ],
        "created_by_user_info": {},
        "updated_by_user_info": {}
      }
}