import React from 'react';

export function getDataWithFinalApproverStatus(status) {
  return {
    data: [
      {
        created_at: '2020-06-08T04:30:53.421Z',
        updated_at: '2020-06-08T04:30:53.421Z',
        vessel_id: 11,
        department: 'business',
        approval_group_id: 1,
        changed_by_user: '<PERSON>',
        remarks: null,
        approval_status: 'approved',
        final_approver: false,
        target_vessel_status: 'active',
      },
      {
        created_at: '2020-06-08T04:30:53.421Z',
        updated_at: '2020-06-08T04:30:53.421Z',
        vessel_id: 11,
        department: 'fpd',
        approval_group_id: 2,
        changed_by_user: '<PERSON>',
        remarks: null,
        approval_status: 'approved',
        final_approver: false,
        target_vessel_status: 'active',
      },
      {
        created_at: '2020-06-08T04:30:53.421Z',
        updated_at: '2020-06-08T04:30:53.421Z',
        vessel_id: 11,
        approval_group_id: 3,
        department: 'accountant',
        changed_by_user: 'Engerraund Serac',
        remarks: null,
        approval_status: 'approved',
        final_approver: false,
        target_vessel_status: 'active',
      },
      {
        created_at: '2020-06-08T04:30:53.421Z',
        updated_at: '2020-06-08T04:30:53.421Z',
        vessel_id: 11,
        approval_group_id: 4,
        department: 'insurance',
        changed_by_user: 'Engerraund Serac',
        remarks: null,
        approval_status: 'approved',
        final_approver: false,
        target_vessel_status: 'active',
      },
      {
        created_at: '2020-06-08T04:30:53.421Z',
        updated_at: '2020-06-08T04:30:53.421Z',
        vessel_id: 11,
        approval_group_id: 5,
        department: 'Technical',
        changed_by_user: 'Engerraund Serac',
        remarks: null,
        approval_status: 'approved',
        final_approver: false,
        target_vessel_status: 'active',
      },
      {
        id: 20,
        created_at: '2020-06-08T04:30:53.421Z',
        updated_at: '2020-06-08T04:30:53.421Z',
        vessel_id: 11,
        approval_group_id: 6,
        department: 'Technical',
        changed_by_user: 'Marc',
        remarks: null,
        approval_status: status,
        final_approver: true,
        target_vessel_status: 'active',
      },
    ],
  };
}

export default getDataWithFinalApproverStatus;
