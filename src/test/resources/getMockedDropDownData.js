export const getTechGroupDropDownResponse = () => {
  return {
    response: {
      tech_group: [
        'Bunker Tech',
        'Celsius Tech',
        'CY Tech D1',
        'CY Tech D2',
        'CY Tech D3',
        'KR Tech D1',
        'NSM',
        'SG Tech D1',
        'SG Tech D2',
        'SG Tech D3',
        'SG Tech D4',
        'SG Tech D5',
        'SG Tech D6',
        'Superintendent',
        'Tech D1',
        'Tech D10',
        'Tech D11',
        'Tech D2',
        'Tech D3',
        'Tech D4',
        'Tech D5',
        'Tech D6',
        'Tech D7',
        'Tech D8',
        'Tech DSMPL Group 1',
        'Tech FMEL',
        'Tech FMIPL',
        'Technical',
        'Tech Shandong',
        'Tech T1',
        'Tech T10',
        'Tech T11',
        'Tech T12',
        'Tech T2',
        'Tech T3',
        'Tech T4',
        'Tech T5',
        'Tech T6',
        'Tech T7',
        'Tech T8',
        'Tech T9',
        'Tech US',
        'VesselManager',
      ],
    },
  };
};

export const queryVesselOwnershipResponse = () => {
  return [
    {
      id: 2223,
      name: 'aaaa',
    },
    {
      id: 2222,
      name: 'new<PERSON><PERSON>',
    },
    {
      id: 2221,
      name: 'ABCD',
    },
    {
      id: 2220,
      name: 'ABCD',
    },
    {
      id: 2219,
      name: 'VESSELRARA Copy',
    },
    {
      id: 2218,
      name: 'sadasdasda',
    },
    {
      id: 2217,
      name: 'VESSELRARA Copy',
    },
    {
      id: 2216,
      name: 'VesselNameTest',
    },
    {
      id: 2215,
      name: 'vessel test',
    },
    {
      id: 2214,
      name: 'VESSELRARA Copy',
    },
    {
      id: 2213,
      name: 'VESSELRARA',
    },
    {
      id: 2212,
      name: 'Bitcoin Miner',
    },
    {
      id: 2211,
      name: 'Automated-Test-Change-Ownership-ae0d',
    },
    {
      id: 2210,
      name: 'New Aspire',
    },
    {
      id: 2208,
      name: null,
    },
    {
      id: 2207,
      name: 'test1',
    },
    {
      id: 2206,
      name: 'test nova email ab',
    },
    {
      id: 2205,
      name: 'RErer',
    },
    {
      id: 2204,
      name: 'test creationdate',
    },
    {
      id: 2203,
      name: 'YM Wisdom Copy',
    },
    {
      id: 2202,
      name: 'FPMC C Melody Part 4',
    },
    {
      id: 2201,
      name: 'CREATION DATE should come ownership change',
    },
    {
      id: 2200,
      name: 'CREATE DATE should come',
    },
    {
      id: 2199,
      name: 'test sync index new',
    },
    {
      id: 2198,
      name: 'Armatori',
    },
    {
      id: 2197,
      name: 'FPMC C Melody Part 2 Copy',
    },
    {
      id: 2194,
      name: 'Test IDK',
    },
    {
      id: 2193,
      name: 'caravel',
    },
    {
      id: 2192,
      name: 'Automated-Test-Change-Ownership-5584',
    },
    {
      id: 2191,
      name: 'Automated-Test-Robot-In_Service-BIZ-5584',
    },
    {
      id: 2190,
      name: 'Automated-Test-Robot-In_Service-BIZ-5584 Copy',
    },
    {
      id: 2189,
      name: 'Automated-Test-Robot-New_Building-BIZ-5584',
    },
    {
      id: 2188,
      name: 'Automated-Test-Robot-In_Service-BIZ-5584',
    },
    {
      id: 2187,
      name: 'Automated-Test-Robot-New_Building-BIZ-acf3',
    },
    {
      id: 2186,
      name: 'Automated-Test-Robot-In_Service-BIZ-acf3',
    },
    {
      id: 2185,
      name: 'Automated-Test-Robot-New_Building-BIZ-100a',
    },
    {
      id: 2184,
      name: 'Automated-Test-Robot-In_Service-BIZ-100a',
    },
    {
      id: 2183,
      name: '21837274asdsjasx',
    },
    {
      id: 2182,
      name: '12321adasdsad',
    },
    {
      id: 2181,
      name: 'NAVY testtt',
    },
    {
      id: 2180,
      name: 'TESLA TO THE MOON BB',
    },
    {
      id: 2179,
      name: 'TESLA TO THE MOON',
    },
    {
      id: 2178,
      name: 'test bad vessel',
    },
    {
      id: 2177,
      name: 'Thicc Vessel',
    },
    {
      id: 2176,
      name: '3213213sad',
    },
    {
      id: 2175,
      name: null,
    },
    {
      id: 2174,
      name: 'anakin',
    },
    {
      id: 2173,
      name: 'dasdsadqw312',
    },
    {
      id: 2172,
      name: 'dasdsadwqlel2321',
    },
    {
      id: 2171,
      name: 'tadah',
    },
    {
      id: 2170,
      name: 'dasdsadsad',
    },
    {
      id: 2169,
      name: 'dasdsadasdqw',
    },
    {
      id: 2168,
      name: 'dasdsadasd',
    },
    {
      id: 2167,
      name: 'dsadsadsadsadasdSFOC',
    },
    {
      id: 2166,
      name: 'MIKEWAZOWSKI',
    },
    {
      id: 2165,
      name: 'dev test 321',
    },
    {
      id: 2164,
      name: 'Vessel Dry test 213223',
    },
    {
      id: 2163,
      name: 'Vessel Dry test 2132',
    },
    {
      id: 2162,
      name: 'Vessel Dry test 21',
    },
    {
      id: 2161,
      name: 'Vessel Dry test',
    },
    {
      id: 2160,
      name: 'Test dsacxzcxz',
    },
    {
      id: 2159,
      name: 'ddsadasd test 123',
    },
    {
      id: 2158,
      name: 'Santori Sponsor',
    },
    {
      id: 2157,
      name: 'Vessel Dry',
    },
    {
      id: 2156,
      name: 'Vessel Dry',
    },
    {
      id: 2155,
      name: 'bad vessel',
    },
    {
      id: 2154,
      name: 'Automated-Test-Robot-In_Service-BIZ-95be',
    },
    {
      id: 2153,
      name: 'Automated-Test-Robot-In_Service-BIZ-95be Copy',
    },
    {
      id: 2152,
      name: 'Automated-Test-Robot-New_Building-BIZ-95be',
    },
    {
      id: 2151,
      name: 'Automated-Test-Robot-In_Service-BIZ-95be',
    },
    {
      id: 2150,
      name: 'Automated-Test-Change-Ownership-eb45',
    },
    {
      id: 2149,
      name: 'Automated-Test-Robot-In_Service-BIZ-eb45',
    },
    {
      id: 2148,
      name: 'Automated-Test-Robot-In_Service-BIZ-eb45 Copy',
    },
    {
      id: 2147,
      name: 'Automated-Test-Robot-New_Building-BIZ-eb45',
    },
    {
      id: 2146,
      name: 'Automated-Test-Robot-In_Service-BIZ-eb45',
    },
    {
      id: 2145,
      name: 'Automated-Test-Change-Ownership-5f6c',
    },
    {
      id: 2144,
      name: 'Automated-Test-Robot-In_Service-BIZ-5f6c Copy',
    },
    {
      id: 2143,
      name: 'Automated-Test-Robot-New_Building-BIZ-5f6c',
    },
    {
      id: 2142,
      name: 'Automated-Test-Robot-In_Service-BIZ-5f6c',
    },
    {
      id: 2140,
      name: 'Automated-Test-Robot-In_Service-BIZ-39be',
    },
    {
      id: 2139,
      name: 'Automated-Test-Robot-In_Service-BIZ-39be Copy',
    },
    {
      id: 2138,
      name: 'Automated-Test-Robot-New_Building-BIZ-39be',
    },
    {
      id: 2137,
      name: 'Automated-Test-Robot-In_Service-BIZ-39be',
    },
    {
      id: 2136,
      name: 'Automated-Test-Change-Ownership-2cec part 2',
    },
    {
      id: 2135,
      name: 'Automated-Test-Change-Ownership-e580 part 2.1',
    },
    {
      id: 2134,
      name: 'Automated-Test-Robot-In_Service-BIZ-d534',
    },
    {
      id: 2129,
      name: 'Automated-Test-Robot-In_Service-BIZ-d534 Copy',
    },
    {
      id: 2128,
      name: 'Automated-Test-Robot-New_Building-BIZ-d534',
    },
    {
      id: 2127,
      name: 'Automated-Test-Robot-In_Service-BIZ-d534',
    },
    {
      id: 2126,
      name: '*Insert caption*',
    },
    {
      id: 2125,
      name: 'It should work',
    },
    {
      id: 2124,
      name: "The Perfect Ship doesn't exi..",
    },
    {
      id: 2123,
      name: 'Owner Change Final Test',
    },
    {
      id: 2122,
      name: 'Sing test ownership finalisation 12345',
    },
    {
      id: 2121,
      name: 'Altair Sky New',
    },
    {
      id: 2120,
      name: 'FPMC C Melody Part 3',
    },
    {
      id: 2119,
      name: 'Sing test ownership finalisation 12345',
    },
    {
      id: 2118,
      name: 'Sing test ownership finalisation 12345',
    },
    {
      id: 2117,
      name: 'Sing test ownership finalisation 12345',
    },
    {
      id: 2116,
      name: 'Sing test ownership finalisation 12345',
    },
    {
      id: 2115,
      name: 'Sing test ownership finalisation 12345',
    },
    {
      id: 2114,
      name: 'Sing test ownership finalisation 12345',
    },
    {
      id: 2113,
      name: 'Sing test ownership finalisation 12345',
    },
    {
      id: 2112,
      name: 'Automated-Test-Change-Ownership-7fba Copy',
    },
    {
      id: 2111,
      name: 'Automated-Test-Robot-New_Building-BIZ-51e6',
    },
    {
      id: 2110,
      name: 'Automated-Test-Robot-In_Service-BIZ-51e6',
    },
    {
      id: 2109,
      name: 'Automated-Test-Change-Ownership-7fba Copy',
    },
    {
      id: 2108,
      name: 'Automated-Test-Robot-New_Building-BIZ-9568',
    },
    {
      id: 2107,
      name: 'Automated-Test-Robot-In_Service-BIZ-9568',
    },
    {
      id: 2106,
      name: 'Owner Change Test New',
    },
    {
      id: 2105,
      name: 'Automated-Test-Change-Ownership-7fba',
    },
    {
      id: 2104,
      name: 'Automated-Test-Robot-In_Service-BIZ-7fba',
    },
    {
      id: 2103,
      name: 'Automated-Test-Robot-In_Service-BIZ-7fba Copy',
    },
    {
      id: 2102,
      name: 'Automated-Test-Robot-New_Building-BIZ-7fba',
    },
    {
      id: 2101,
      name: 'Automated-Test-Robot-In_Service-BIZ-7fba',
    },
    {
      id: 2100,
      name: 'Sing test ownership finalisation',
    },
    {
      id: 2099,
      name: 'Owner Test Dev',
    },
    {
      id: 2098,
      name: 'Automated-Test-Change-Ownership-730b',
    },
    {
      id: 2097,
      name: 'Automated-Test-Robot-In_Service-BIZ-730b',
    },
    {
      id: 2096,
      name: 'Automated-Test-Robot-In_Service-BIZ-730b Copy',
    },
    {
      id: 2095,
      name: 'Automated-Test-Robot-New_Building-BIZ-730b',
    },
    {
      id: 2094,
      name: 'Automated-Test-Robot-In_Service-BIZ-730b',
    },
    {
      id: 2093,
      name: 'Automated-Test-Change-Ownership-ae0d',
    },
    {
      id: 2092,
      name: 'Automated-Test-Robot-In_Service-BIZ-ae0d Copy',
    },
    {
      id: 2091,
      name: 'Automated-Test-Robot-New_Building-BIZ-ae0d',
    },
    {
      id: 2090,
      name: 'Automated-Test-Robot-In_Service-BIZ-ae0d',
    },
    {
      id: 2089,
      name: 'Automated-Test-Change-Ownership-e580',
    },
    {
      id: 2088,
      name: 'Automated-Test-Robot-In_Service-BIZ-e580 Copy',
    },
    {
      id: 2087,
      name: 'Automated-Test-Robot-New_Building-BIZ-e580',
    },
    {
      id: 2086,
      name: 'Automated-Test-Robot-In_Service-BIZ-e580',
    },
    {
      id: 2085,
      name: 'Automated-Test-Change-Ownership-1cc1',
    },
    {
      id: 2084,
      name: 'Automated-Test-Robot-In_Service-BIZ-1cc1 Copy',
    },
    {
      id: 2083,
      name: 'Automated-Test-Robot-New_Building-BIZ-1cc1',
    },
    {
      id: 2082,
      name: 'Automated-Test-Robot-In_Service-BIZ-1cc1',
    },
    {
      id: 2081,
      name: 'Owner Change Test',
    },
    {
      id: 2080,
      name: 'Automated-Test-Change-Ownership-2cec',
    },
    {
      id: 2079,
      name: 'Automated-Test-Robot-In_Service-BIZ-2cec',
    },
    {
      id: 2078,
      name: 'Automated-Test-Robot-In_Service-BIZ-2cec Copy',
    },
    {
      id: 2077,
      name: 'Automated-Test-Robot-New_Building-BIZ-2cec',
    },
    {
      id: 2076,
      name: 'Automated-Test-Robot-In_Service-BIZ-2cec',
    },
    {
      id: 2075,
      name: 'New test new Owner Copy',
    },
    {
      id: 2074,
      name: 'Automated-Test-Robot-New_Building-BIZ-e23c',
    },
    {
      id: 2073,
      name: 'Automated-Test-Robot-In_Service-BIZ-e23c',
    },
    {
      id: 2072,
      name: 'New test new Owner',
    },
    {
      id: 2071,
      name: 'New test Copy',
    },
    {
      id: 2070,
      name: 'new create test vessel',
    },
    {
      id: 2069,
      name: 'New vessel',
    },
    {
      id: 2068,
      name: 'Jack Sparrow',
    },
    {
      id: 2067,
      name: 'toto Alfred N v2',
    },
    {
      id: 2066,
      name: 'Automated-Test-Change-Ownership-aca6',
    },
    {
      id: 2065,
      name: 'Automated-Test-Robot-In_Service-BIZ-aca6 Copy',
    },
    {
      id: 2064,
      name: 'Automated-Test-Robot-New_Building-BIZ-aca6',
    },
    {
      id: 2063,
      name: 'Automated-Test-Robot-In_Service-BIZ-aca6',
    },
    {
      id: 2062,
      name: 'Automated-Test-Robot-In_Service-BIZ-51e8',
    },
    {
      id: 2061,
      name: 'Automated-Test-Robot-In_Service-BIZ-51e8 Copy',
    },
    {
      id: 2060,
      name: 'Automated-Test-Robot-New_Building-BIZ-51e8',
    },
    {
      id: 2059,
      name: 'Automated-Test-Robot-In_Service-BIZ-51e8',
    },
    {
      id: 2058,
      name: 'Automated-Test-Robot-In_Service-BIZ-45c4',
    },
    {
      id: 2057,
      name: 'Automated-Test-Robot-In_Service-BIZ-45c4 Copy',
    },
    {
      id: 2056,
      name: 'Automated-Test-Robot-New_Building-BIZ-45c4',
    },
    {
      id: 2055,
      name: 'Automated-Test-Robot-In_Service-BIZ-45c4',
    },
    {
      id: 2054,
      name: 'YM Wisdom',
    },
    {
      id: 2053,
      name: 'Sebring Express New WOW',
    },
    {
      id: 2052,
      name: 'toto Alfred N',
    },
    {
      id: 2049,
      name: 'Friday vessel 3 Sep with new owner',
    },
    {
      id: 2041,
      name: 'Automated-Test-Robot-In_Service-BIZ-d924',
    },
    {
      id: 2040,
      name: 'Automated-Test-Robot-In_Service-BIZ-d924 Copy',
    },
    {
      id: 2039,
      name: 'Friday vessel 3 Sep',
    },
    {
      id: 2038,
      name: 'Automated-Test-Robot-New_Building-BIZ-d924',
    },
    {
      id: 2037,
      name: 'Automated-Test-Robot-In_Service-BIZ-d924',
    },
    {
      id: 2031,
      name: 'f',
    },
    {
      id: 2030,
      name: null,
    },
    {
      id: 2027,
      name: 'Megatron',
    },
    {
      id: 2023,
      name: 'test image upload',
    },
    {
      id: 2022,
      name: 'Automated-Test-Change-Ownership-4f62 Copy',
    },
    {
      id: 2020,
      name: 'test',
    },
    {
      id: 2019,
      name: 'Automated-Test-Change-Ownership-4f62',
    },
    {
      id: 2015,
      name: 'TANKER VESSEL',
    },
    {
      id: 2014,
      name: 'Automated-Test-Change-Ownership-e755 Copy',
    },
    {
      id: 2010,
      name: 'Automated-Test-Change-Ownership-e755',
    },
    {
      id: 2009,
      name: 'Automated-Test-Robot-In_Service-BIZ-e755',
    },
    {
      id: 2005,
      name: 'qweqewe',
    },
    {
      id: 2004,
      name: 'edit vessel',
    },
    {
      id: 1998,
      name: 'ABC vessel',
    },
    {
      id: 1986,
      name: 'new vessel edit changed owner Copy',
    },
    {
      id: 1985,
      name: 'new vessel edit changed archived owner',
    },
    {
      id: 1980,
      name: 'new vessel edit Copy',
    },
    {
      id: 1978,
      name: 'new vessel edit',
    },
    {
      id: 1967,
      name: 'Sync vessel handed over',
    },
    {
      id: 1966,
      name: 'Final test vessel',
    },
    {
      id: 1935,
      name: 'Chemical Challenger',
    },
    {
      id: 1868,
      name: 'Bahamian Express',
    },
    {
      id: 1867,
      name: 'MP Panamax 5',
    },
    {
      id: 1779,
      name: 'PROD vessel Test Copy',
    },
    {
      id: 1778,
      name: 'PROD vessel Test old Ownership Changed',
    },
    {
      id: 1775,
      name: 'PROD vessel Test Copy',
    },
    {
      id: 1772,
      name: 'PROD vessel Test',
    },
    {
      id: 1753,
      name: 'VesselManager',
    },
    {
      id: 1735,
      name: 'Celsius Rimini',
    },
    {
      id: 1734,
      name: 'Sebring Express',
    },
    {
      id: 1733,
      name: 'Alfred N',
    },
    {
      id: 1732,
      name: 'Pioneer Star',
    },
    {
      id: 1731,
      name: 'Pacific Silver (Old)',
    },
    {
      id: 1730,
      name: 'Eships Progress',
    },
    {
      id: 1729,
      name: 'GRM Princess',
    },
    {
      id: 1728,
      name: 'Maharshi Devatreya (Old)',
    },
    {
      id: 1727,
      name: 'Marinex',
    },
    {
      id: 1726,
      name: 'UACC Manama',
    },
    {
      id: 1724,
      name: 'Rising Sky',
    },
    {
      id: 1723,
      name: 'Fairchem Blue Shark',
    },
    {
      id: 1722,
      name: 'Supreme (ex. Heydar Aliyev)',
    },
    {
      id: 1721,
      name: 'Sichem Contester (Old)',
    },
    {
      id: 1720,
      name: 'Ocean Ambitious',
    },
    {
      id: 1719,
      name: 'Nord Taurus',
    },
    {
      id: 1718,
      name: 'GH Rich Wall',
    },
    {
      id: 1717,
      name: 'Fomento Two',
    },
    {
      id: 1716,
      name: 'PSU Tenth',
    },
    {
      id: 1715,
      name: 'Star Crios',
    },
    {
      id: 1714,
      name: 'Anna',
    },
    {
      id: 1713,
      name: 'Gulf Star 1',
    },
    {
      id: 1712,
      name: 'Morning Carina',
    },
    {
      id: 1711,
      name: 'Njord Cloud',
    },
    {
      id: 1709,
      name: 'Chemical Luna (Old)',
    },
    {
      id: 1708,
      name: 'C Phoenix',
    },
    {
      id: 1706,
      name: 'New Aspire',
    },
    {
      id: 1705,
      name: 'MP Panamax 5',
    },
    {
      id: 1704,
      name: 'Mid Eagle',
    },
    {
      id: 1703,
      name: 'Falmouth Bay',
    },
    {
      id: 1702,
      name: 'APL Minnesota',
    },
    {
      id: 1701,
      name: 'Caledonian Express',
    },
    {
      id: 1700,
      name: 'Sagar Kanya',
    },
    {
      id: 1698,
      name: 'Maharshi Krishnatreya (Old)',
    },
    {
      id: 1697,
      name: 'Ocean Garnet (Old)',
    },
    {
      id: 1696,
      name: 'Maharshi Shubhatreya',
    },
    {
      id: 1695,
      name: 'Royal Flush',
    },
    {
      id: 1694,
      name: 'TRF Kristiansand',
    },
    {
      id: 1693,
      name: 'Sagar Samrat',
    },
    {
      id: 1692,
      name: 'Stena Tiger',
    },
    {
      id: 1691,
      name: 'Pacific Sapphire',
    },
    {
      id: 1690,
      name: 'Bum Chin',
    },
    {
      id: 1689,
      name: 'Crown Alexandra',
    },
    {
      id: 1688,
      name: 'Stellar Cupid',
    },
    {
      id: 1687,
      name: 'Honduras Star',
    },
    {
      id: 1686,
      name: 'Imola Express',
    },
    {
      id: 1685,
      name: 'C Frontier',
    },
    {
      id: 1684,
      name: 'Chem Leo',
    },
    {
      id: 1681,
      name: 'Marina Bay',
    },
    {
      id: 1680,
      name: 'Marina South',
    },
    {
      id: 1679,
      name: 'Ecuador Star (Old)',
    },
    {
      id: 1678,
      name: 'Whitney Bay',
    },
    {
      id: 1677,
      name: 'Humboldt Bay',
    },
    {
      id: 1676,
      name: 'Forward Bright',
    },
    {
      id: 1675,
      name: 'Bonita Light',
    },
    {
      id: 1674,
      name: 'Clipper Glory',
    },
    {
      id: 1671,
      name: 'Amer Annapurna',
    },
    {
      id: 1664,
      name: 'Frio Poseidon',
    },
    {
      id: 1635,
      name: 'Challenge Pines',
    },
    {
      id: 1630,
      name: 'Spar Taurus Copy',
    },
    {
      id: 1598,
      name: 'Churchill Bulker',
    },
    {
      id: 1581,
      name: 'TestingHandover',
    },
    {
      id: 1531,
      name: 'Nord Vantage Copy',
    },
    {
      id: 1523,
      name: 'VesselTest Copy',
    },
    {
      id: 1521,
      name: 'werwerw',
    },
    {
      id: 1510,
      name: 'Final test Vessel edited Copy',
    },
    {
      id: 1509,
      name: 'vessel-changed-structure Copy',
    },
    {
      id: 1506,
      name: 'vessel-changed-structure edited',
    },
    {
      id: 1505,
      name: 'test sing vessel 1',
    },
    {
      id: 1504,
      name: 'new building vessel with new Owner Antrak Copy',
    },
    {
      id: 1503,
      name: 'UACC AL Medinae with Gulf Navigation Holding',
    },
    {
      id: 1502,
      name: 'Istrian Express (Old)',
    },
    {
      id: 1501,
      name: 'Torm Stellar',
    },
    {
      id: 1500,
      name: 'Celsius Eagle',
    },
    {
      id: 1499,
      name: 'Solar Nesrin',
    },
    {
      id: 1498,
      name: 'Venice Bridge',
    },
    {
      id: 1497,
      name: 'Shandong Hai Chang (Old)',
    },
    {
      id: 1496,
      name: 'Shandong Hong Tu',
    },
    {
      id: 1495,
      name: 'Stream Atlantic',
    },
    {
      id: 1494,
      name: 'Nord Vanguard',
    },
    {
      id: 1493,
      name: 'Altair Sky',
    },
    {
      id: 1492,
      name: 'GH Mistral',
    },
    {
      id: 1491,
      name: 'Nord Valorous',
    },
    {
      id: 1490,
      name: 'Celsius Porto',
    },
    {
      id: 1489,
      name: 'Arabian Express (Old)',
    },
    {
      id: 1488,
      name: 'UACC Masafi',
    },
    {
      id: 1487,
      name: 'Western Stavanger (Old)',
    },
    {
      id: 1486,
      name: 'Linus P',
    },
    {
      id: 1485,
      name: 'GEM No. 2',
    },
    {
      id: 1484,
      name: 'GH Urban Sea',
    },
    {
      id: 1483,
      name: 'HTC Delta',
    },
    {
      id: 1482,
      name: 'Silver Stacie',
    },
    {
      id: 1481,
      name: 'Shandong Hai Xing',
    },
    {
      id: 1480,
      name: 'Ever Shine',
    },
    {
      id: 1479,
      name: 'Camilla Bulker',
    },
    {
      id: 1478,
      name: 'Harvest Moon',
    },
    {
      id: 1477,
      name: 'Marjorie K',
    },
    {
      id: 1476,
      name: 'Morning Cherry',
    },
    {
      id: 1475,
      name: 'Vincent Thomas Bridge',
    },
    {
      id: 1474,
      name: 'Spar Capella',
    },
    {
      id: 1473,
      name: 'Mid Nature',
    },
    {
      id: 1472,
      name: 'Promise 3',
    },
    {
      id: 1471,
      name: 'Ocean Sapphire',
    },
    {
      id: 1470,
      name: 'Ocean Colossus',
    },
    {
      id: 1469,
      name: 'Le Mans Express',
    },
    {
      id: 1468,
      name: 'Maritime Cuate',
    },
    {
      id: 1467,
      name: 'Spar Draco',
    },
    {
      id: 1466,
      name: 'Sinar Bali',
    },
    {
      id: 1465,
      name: 'Aigran D',
    },
    {
      id: 1464,
      name: 'Spar Lupus',
    },
    {
      id: 1463,
      name: 'Radiance',
    },
    {
      id: 1460,
      name: 'TestOwnership',
    },
    {
      id: 1456,
      name: 'Celsius Mayfair Copy',
    },
    {
      id: 1454,
      name: 'abcde',
    },
    {
      id: 1453,
      name: 'Harvest Rising',
    },
    {
      id: 1452,
      name: 'C Grandeur',
    },
    {
      id: 1451,
      name: 'Harvest Frost',
    },
    {
      id: 1444,
      name: 'Sea Power',
    },
    {
      id: 1442,
      name: 'test insert vessel',
    },
    {
      id: 1441,
      name: 'Kuo Lih',
    },
    {
      id: 1440,
      name: 'Yuan Fu Star',
    },
    {
      id: 1439,
      name: 'CMA CGM Lilac',
    },
    {
      id: 1438,
      name: 'Vega Spring',
    },
    {
      id: 1437,
      name: 'Sing test vessel',
    },
    {
      id: 1436,
      name: 'Chemroad Sea',
    },
    {
      id: 1435,
      name: 'Nord Vantage',
    },
    {
      id: 1434,
      name: 'UACC Yanbu',
    },
    {
      id: 1433,
      name: 'Seamax Niantic',
    },
    {
      id: 1432,
      name: 'Chemical Challenger',
    },
    {
      id: 1431,
      name: 'Silverstone Express',
    },
    {
      id: 1430,
      name: 'Triton Gannet',
    },
    {
      id: 1429,
      name: 'VesselTest',
    },
    {
      id: 1428,
      name: 'Neelambari',
    },
    {
      id: 1426,
      name: 'Vessel - changed owner',
    },
    {
      id: 1425,
      name: 'Vega Spirit',
    },
    {
      id: 1424,
      name: 'M.P.Panamax-2',
    },
    {
      id: 1423,
      name: 'Bering Light',
    },
    {
      id: 1422,
      name: 'New Vessel Testing 123123',
    },
    {
      id: 1421,
      name: 'Triton Swift',
    },
    {
      id: 1419,
      name: 'VesselTest',
    },
    {
      id: 1409,
      name: 'Sakhara Lotus 1',
    },
    {
      id: 1408,
      name: 'Final test Vessel edited with new Owner',
    },
    {
      id: 1405,
      name: 'Friday vessel new',
    },
    {
      id: 1404,
      name: 'new building vessel',
    },
    {
      id: 1403,
      name: 'Ming Test 4',
    },
    {
      id: 1402,
      name: 'New Vessel Bla',
    },
    {
      id: 1401,
      name: '13213123',
    },
    {
      id: 1400,
      name: 'Test Vessel 111',
    },
    {
      id: 1399,
      name: '1_Owner+RegisterOwner_withoutSplit',
    },
    {
      id: 1398,
      name: 'MT2',
    },
    {
      id: 1397,
      name: 'Rac-eqwe',
    },
    {
      id: 1396,
      name: 'Soya Tianjin',
    },
    {
      id: 1395,
      name: 'qwe',
    },
    {
      id: 1394,
      name: '212321',
    },
    {
      id: 1393,
      name: '212321',
    },
    {
      id: 1392,
      name: '2_Owner_withSplit',
    },
    {
      id: 1391,
      name: 'Interasia Catalyst',
    },
    {
      id: 1390,
      name: 'Amity',
    },
    {
      id: 1389,
      name: 'Bow Tone',
    },
    {
      id: 1388,
      name: 'FMT Efes',
    },
    {
      id: 1387,
      name: 'TEST VESSEL',
    },
    {
      id: 1386,
      name: 'Gulf Jalmuda',
    },
    {
      id: 1385,
      name: 'Western Fuji',
    },
    {
      id: 1384,
      name: 'Maharshi Mahatreya',
    },
    {
      id: 1383,
      name: 'Ownership Test Vessel New',
    },
    {
      id: 1382,
      name: 'AcerFake',
    },
    {
      id: 1381,
      name: 'Celsius Roskilde New RO',
    },
    {
      id: 1380,
      name: 'Solar Majesty',
    },
    {
      id: 1379,
      name: 'Silver Monika',
    },
    {
      id: 1378,
      name: 'Awasan Pioneer',
    },
    {
      id: 1377,
      name: 'Virgo Confidence',
    },
    {
      id: 1376,
      name: 'Cape Condor',
    },
    {
      id: 1375,
      name: 'GW Fortune',
    },
    {
      id: 1374,
      name: 'TS Shenzhen',
    },
    {
      id: 1373,
      name: 'Ocean Ambition',
    },
    {
      id: 1372,
      name: 'Solar Ailene',
    },
    {
      id: 1371,
      name: 'TR Niklas',
    },
    {
      id: 1370,
      name: 'Mari Couva',
    },
    {
      id: 1369,
      name: '3213213213',
    },
    {
      id: 1368,
      name: 'Ocean Diamond (ODD)',
    },
    {
      id: 1367,
      name: 'dsfsdf',
    },
    {
      id: 1366,
      name: 'TS Kaohsiung',
    },
    {
      id: 1365,
      name: 'Ridgebury Cindy A',
    },
    {
      id: 1364,
      name: 'HL Imabari',
    },
    {
      id: 1363,
      name: 'Ivory Ace',
    },
    {
      id: 1362,
      name: 'Neha Ownership with splited book',
    },
    {
      id: 1361,
      name: 'Chemical Traveller',
    },
    {
      id: 1359,
      name: 'Hamburg City',
    },
    {
      id: 1358,
      name: 'TS Manila',
    },
    {
      id: 1357,
      name: 'GW Dolphin',
    },
    {
      id: 1356,
      name: 'Aquavita Sol',
    },
    {
      id: 1355,
      name: 'Shandong Hai Yao (FML)',
    },
    {
      id: 1354,
      name: 'Paula Trader',
    },
    {
      id: 1353,
      name: 'UACC Doha',
    },
    {
      id: 1352,
      name: 'GH Leste',
    },
    {
      id: 1351,
      name: 'Atlantic Marble',
    },
    {
      id: 1350,
      name: 'Ulriken',
    },
    {
      id: 1349,
      name: 'Ocean Lily',
    },
    {
      id: 1348,
      name: 'Summertime Dream',
    },
    {
      id: 1347,
      name: 'Sirius Sky (Old)',
    },
    {
      id: 1346,
      name: 'Jabal Alkawr',
    },
    {
      id: 1345,
      name: 'Celsius Copenhagen (SHI Hull No. 2297)',
    },
    {
      id: 1344,
      name: 'Ridgebury Alexandra Z',
    },
    {
      id: 1343,
      name: 'Chemical Sailor',
    },
    {
      id: 1342,
      name: 'Shandong Hai Yao (SFML Old)',
    },
    {
      id: 1341,
      name: 'Explorer Oceania',
    },
    {
      id: 1340,
      name: 'Sirius Sky',
    },
    {
      id: 1339,
      name: 'Ganbei',
    },
    {
      id: 1338,
      name: 'Cressida',
    },
    {
      id: 1337,
      name: 'UACC Riyadh',
    },
    {
      id: 1336,
      name: 'Star Damon',
    },
    {
      id: 1335,
      name: 'Fairchem Charger',
    },
    {
      id: 1334,
      name: 'Cosco Shipping Panama',
    },
    {
      id: 1333,
      name: 'FPMC P Alpine',
    },
    {
      id: 1332,
      name: 'UACC Mansouria',
    },
    {
      id: 1331,
      name: 'Sheng Shi',
    },
    {
      id: 1329,
      name: 'Cepheus Ocean',
    },
    {
      id: 1328,
      name: 'La Donna I',
    },
    {
      id: 1327,
      name: 'PSU CMP',
    },
    {
      id: 1326,
      name: 'Harvest Time',
    },
    {
      id: 1324,
      name: 'Shourong Harmony',
    },
    {
      id: 1323,
      name: 'Bochem Mumbai (Old)',
    },
    {
      id: 1322,
      name: 'San Sebastian',
    },
    {
      id: 1321,
      name: 'Amber Champion new',
    },
    {
      id: 1320,
      name: 'UACC Mirdif',
    },
    {
      id: 1319,
      name: 'Shandong Peng Cheng',
    },
    {
      id: 1318,
      name: 'Chemical Hunter',
    },
    {
      id: 1317,
      name: 'Crusader',
    },
    {
      id: 1316,
      name: 'Stenia Colossus',
    },
    {
      id: 1315,
      name: 'Sicilian Express',
    },
    {
      id: 1314,
      name: 'Cape Med',
    },
    {
      id: 1313,
      name: 'Spar Vega',
    },
    {
      id: 1312,
      name: 'Sagar Shakti',
    },
    {
      id: 1311,
      name: 'Coral Jasper',
    },
    {
      id: 1310,
      name: 'Amber Beverly',
    },
    {
      id: 1309,
      name: 'Mid Fortune',
    },
    {
      id: 1308,
      name: 'Bochem Ghent',
    },
    {
      id: 1307,
      name: 'Tenki Maru',
    },
    {
      id: 1306,
      name: 'Centaur',
    },
    {
      id: 1305,
      name: 'Spring Ploeg name changed',
    },
    {
      id: 1304,
      name: 'Chem Sea',
    },
    {
      id: 1303,
      name: 'test insert vessel',
    },
    {
      id: 1302,
      name: 'Bolivar Light',
    },
    {
      id: 1300,
      name: 'Ocean Freedom',
    },
    {
      id: 1299,
      name: 'Vessel for demoe',
    },
    {
      id: 1298,
      name: 'fdsfsdfsdf',
    },
    {
      id: 1297,
      name: 'Kota Perabu',
    },
    {
      id: 1296,
      name: 'Maritime Lijiang',
    },
    {
      id: 1295,
      name: 'Chemical Marketer',
    },
    {
      id: 1294,
      name: 'Twinkle Express',
    },
    {
      id: 1293,
      name: 'Vietnam Express',
    },
    {
      id: 1292,
      name: 'TSL Rosemary',
    },
    {
      id: 1291,
      name: 'Baltic East',
    },
    {
      id: 1290,
      name: 'Spar Canis',
    },
    {
      id: 1289,
      name: 'Magny Cours Express',
    },
    {
      id: 1288,
      name: 'Bulk Avanti',
    },
    {
      id: 1287,
      name: 'Vega Spring (Old)',
    },
    {
      id: 1286,
      name: 'UACC IBN Al Haitham',
    },
    {
      id: 1285,
      name: 'Test vessel for demo',
    },
    {
      id: 1284,
      name: 'Bhairavi',
    },
    {
      id: 1283,
      name: 'Nordstraum',
    },
    {
      id: 1282,
      name: 'Condor Bay',
    },
    {
      id: 1281,
      name: 'Grand Breaker (Old)',
    },
    {
      id: 1280,
      name: 'Fairchem Hawk',
    },
    {
      id: 1279,
      name: 'Saranga',
    },
    {
      id: 1278,
      name: 'Cepheus Ocean (Old)',
    },
    {
      id: 1277,
      name: 'Rotterdam Bridge',
    },
    {
      id: 1276,
      name: 'Dreggen',
    },
    {
      id: 1275,
      name: 'Aquarius Ocean',
    },
    {
      id: 1274,
      name: 'Peace Hill',
    },
    {
      id: 1273,
      name: 'Stream Baltic',
    },
    {
      id: 1272,
      name: 'Rosco Plum',
    },
    {
      id: 1271,
      name: 'Eclipse',
    },
    {
      id: 1270,
      name: 'Chilean Express',
    },
    {
      id: 1269,
      name: 'Tenshin Maru',
    },
    {
      id: 1268,
      name: 'Red Strength',
    },
    {
      id: 1267,
      name: 'Belcargo',
    },
    {
      id: 1265,
      name: '2_RegOwner_withSplit',
    },
    {
      id: 1264,
      name: 'sadasd',
    },
    {
      id: 1263,
      name: 'Test Vessel Julietta',
    },
    {
      id: 1262,
      name: 'Cape Moreton',
    },
    {
      id: 1261,
      name: 'Enterprise',
    },
    {
      id: 1260,
      name: 'Silver Amanda',
    },
    {
      id: 1259,
      name: 'Cape Canary',
    },
    {
      id: 1258,
      name: 'Fortune Elephant',
    },
    {
      id: 1257,
      name: 'Silver Joan',
    },
    {
      id: 1256,
      name: 'UACC Marah',
    },
    {
      id: 1255,
      name: 'Ocean Vendor (Old)',
    },
    {
      id: 1254,
      name: 'Cape Canary (Old)',
    },
    {
      id: 1253,
      name: 'Fomento Four',
    },
    {
      id: 1252,
      name: 'Olivia',
    },
    {
      id: 1251,
      name: 'RGL First',
    },
    {
      id: 1249,
      name: 'Otto H',
    },
    {
      id: 1248,
      name: 'friday vessel',
    },
    {
      id: 1247,
      name: 'Ming Test 3',
    },
    {
      id: 1246,
      name: 'Heydar Aliyev (ex. Absheron)',
    },
    {
      id: 1245,
      name: 'Packer',
    },
    {
      id: 1244,
      name: 'Test Julietta',
    },
    {
      id: 1243,
      name: '1_Owner+RegisterOwner_withoutSplit Copy',
    },
    {
      id: 1242,
      name: 'New Vessel Test',
    },
    {
      id: 1241,
      name: 'Ocean Tianbao',
    },
    {
      id: 1240,
      name: 'Cygnus Ocean',
    },
    {
      id: 1239,
      name: 'PSU First',
    },
    {
      id: 1238,
      name: 'Silver Manoora',
    },
    {
      id: 1237,
      name: 'Southern Light',
    },
    {
      id: 1235,
      name: 'Etoile',
    },
    {
      id: 1234,
      name: 'Celsius Monaco',
    },
    {
      id: 1233,
      name: 'Ionian Express',
    },
    {
      id: 1231,
      name: 'Nathan Brandon',
    },
    {
      id: 1230,
      name: 'Northern Light',
    },
    {
      id: 1229,
      name: 'Asian Grace (Old)',
    },
    {
      id: 1228,
      name: 'Rachid Vessel Changed Name',
    },
    {
      id: 1227,
      name: 'Bremen Belle',
    },
    {
      id: 1226,
      name: 'Tristar Legend',
    },
    {
      id: 1225,
      name: 'Bow Engineer (Old)',
    },
    {
      id: 1224,
      name: 'Rachid Vessel Copy',
    },
    {
      id: 1223,
      name: null,
    },
    {
      id: 1221,
      name: 'Iberian Express Changed Ownership',
    },
    {
      id: 1220,
      name: 'Crimson Queen',
    },
    {
      id: 1217,
      name: 'new building vessel with new Owner Antrak Copy',
    },
    {
      id: 1216,
      name: 'Vessel 1271 New',
    },
    {
      id: 1215,
      name: 'Nord Minute with New Owner Copy',
    },
    {
      id: 1214,
      name: 'Bum Eun',
    },
    {
      id: 1213,
      name: 'Jbu Sapphire',
    },
    {
      id: 1212,
      name: 'Demo vessel',
    },
    {
      id: 1210,
      name: 'Ming Test2 222',
    },
    {
      id: 1209,
      name: 'Leap Heart',
    },
    {
      id: 1208,
      name: 'Test Vessel',
    },
    {
      id: 1207,
      name: 'Silver Ginny',
    },
    {
      id: 1206,
      name: 'Final test Vessel edited with new Owner Copy',
    },
    {
      id: 1205,
      name: 'Ridgebury Julia M',
    },
    {
      id: 1204,
      name: 'Mid Osprey',
    },
    {
      id: 1202,
      name: 'Fiorela',
    },
    {
      id: 1201,
      name: 'VesselRachid',
    },
    {
      id: 1200,
      name: 'Italian Express (Old)',
    },
    {
      id: 1199,
      name: 'Sebring Express (Old)',
    },
    {
      id: 1198,
      name: null,
    },
    {
      id: 1197,
      name: 'joseph31',
    },
    {
      id: 1196,
      name: 'Ultra Bosque',
    },
    {
      id: 1195,
      name: 'Rachid Vessel Test  Copy Copy',
    },
    {
      id: 1194,
      name: 'mugeesh',
    },
    {
      id: 1193,
      name: '2_Owner+RegisterOwner_withSplit',
    },
    {
      id: 1191,
      name: 'joseph33',
    },
    {
      id: 1190,
      name: 'joseph22',
    },
    {
      id: 1189,
      name: 'Ridgebury Rosemary E',
    },
    {
      id: 1188,
      name: 'Celsius Mexico',
    },
    {
      id: 1187,
      name: 'Seaways Valour',
    },
    {
      id: 1186,
      name: 'RachidSyncM',
    },
    {
      id: 1185,
      name: 'RachidZirconB',
    },
    {
      id: 1184,
      name: 'Enduro Trader',
    },
    {
      id: 1183,
      name: 'joseph 29',
    },
    {
      id: 1181,
      name: 'VESSELRARA',
    },
    {
      id: 1180,
      name: null,
    },
    {
      id: 1179,
      name: 'joseph23',
    },
    {
      id: 1178,
      name: 'RachidTest1AAC',
    },
    {
      id: 1177,
      name: 'Vessel Rachid',
    },
    {
      id: 1176,
      name: '212321',
    },
    {
      id: 1175,
      name: 'Tokyo Pioneer',
    },
    {
      id: 1174,
      name: 'Ultra Forest',
    },
    {
      id: 1173,
      name: '212321',
    },
    {
      id: 1172,
      name: 'joseph 241',
    },
    {
      id: 1171,
      name: 'joseph23',
    },
    {
      id: 1170,
      name: 'Wheat Weifang',
    },
    {
      id: 1168,
      name: 'Shandong Hai Xing (SFML Old)',
    },
    {
      id: 1167,
      name: 'Scorpio Confidence',
    },
    {
      id: 1165,
      name: '212321',
    },
    {
      id: 1164,
      name: 'joseph 401',
    },
    {
      id: 1163,
      name: '212321',
    },
    {
      id: 1162,
      name: 'D Whale',
    },
    {
      id: 1161,
      name: 'Five Stars Fujian',
    },
    {
      id: 1160,
      name: 'joseph24',
    },
    {
      id: 1159,
      name: 'Xin Jin Hai',
    },
    {
      id: 1158,
      name: 'Magic Nova',
    },
    {
      id: 1157,
      name: 'Vancouver Bridge',
    },
    {
      id: 1156,
      name: 'RachidTestSync14',
    },
    {
      id: 1154,
      name: 'Christina Bulker',
    },
    {
      id: 1153,
      name: 'Chang Tai Hong',
    },
    {
      id: 1152,
      name: 'Nord Vision (S483)',
    },
    {
      id: 1151,
      name: 'Vessel for Test',
    },
    {
      id: 1150,
      name: 'Harvest Plains',
    },
    {
      id: 1149,
      name: 'Triton Hawk',
    },
    {
      id: 1148,
      name: 'Interasia Pursuit',
    },
    {
      id: 1147,
      name: 'Nord Victorius (S484)',
    },
    {
      id: 1146,
      name: 'Shandong De Feng',
    },
    {
      id: 1145,
      name: 'Sea Venus',
    },
    {
      id: 1143,
      name: 'Prem Pride',
    },
    {
      id: 1142,
      name: 'joseph30',
    },
    {
      id: 1141,
      name: 'RachidSyncU',
    },
    {
      id: 1140,
      name: 'joseph 402',
    },
    {
      id: 1139,
      name: 'Shandong De Guang',
    },
    {
      id: 1138,
      name: 'joseph 28',
    },
    {
      id: 1136,
      name: 'Test notification',
    },
    {
      id: 1135,
      name: 'Rachid Vessel Test ',
    },
    {
      id: 1134,
      name: 'Rachid-Vessel-1308',
    },
    {
      id: 1133,
      name: 'joseph 99',
    },
    {
      id: 1132,
      name: 'joseph32',
    },
    {
      id: 1131,
      name: 'joseph31',
    },
    {
      id: 1130,
      name: null,
    },
    {
      id: 1129,
      name: 'joseph 400',
    },
    {
      id: 1128,
      name: 'joseph 123',
    },
    {
      id: 1127,
      name: 'joseph21',
    },
    {
      id: 1126,
      name: 'joseph20',
    },
    {
      id: 1125,
      name: 'joseph 333',
    },
    {
      id: 1124,
      name: 'Atlantic East',
    },
    {
      id: 1123,
      name: 'TS Kobe',
    },
    {
      id: 1121,
      name: 'Solar Legend',
    },
    {
      id: 1120,
      name: 'V. Prosperity',
    },
    {
      id: 1119,
      name: 'Pacific Singapore (Naess)',
    },
    {
      id: 1118,
      name: 'Celsius Everett',
    },
    {
      id: 1117,
      name: 'New Joviality',
    },
    {
      id: 1116,
      name: 'Karimata',
    },
    {
      id: 1115,
      name: 'Stellar Way',
    },
    {
      id: 1114,
      name: 'TS Qingdao',
    },
    {
      id: 1113,
      name: 'Celsius Randers',
    },
    {
      id: 1112,
      name: 'Celsius Riga',
    },
    {
      id: 1111,
      name: 'Pacific Geneva',
    },
    {
      id: 1110,
      name: 'Pacific Beijing',
    },
    {
      id: 1109,
      name: 'Vincent Talisman',
    },
    {
      id: 1108,
      name: 'Clearocean Mustang (S479)',
    },
    {
      id: 1107,
      name: 'FMT Urla',
    },
    {
      id: 1106,
      name: 'Cape Maple',
    },
    {
      id: 1105,
      name: 'Chem Mia',
    },
    {
      id: 1104,
      name: 'Seamax New Haven',
    },
    {
      id: 1103,
      name: 'Vela',
    },
    {
      id: 1102,
      name: 'Hinoki',
    },
    {
      id: 1101,
      name: 'Glenda Megan',
    },
    {
      id: 1100,
      name: 'Pacific Sarah (PSA)',
    },
    {
      id: 1099,
      name: 'Chem Antares',
    },
    {
      id: 1098,
      name: 'Gulf Fanatir',
    },
    {
      id: 1097,
      name: 'Viola',
    },
    {
      id: 1096,
      name: 'Celsius Montreal',
    },
    {
      id: 1095,
      name: 'Summer Sky (Old)',
    },
    {
      id: 1094,
      name: 'Forever Melody',
    },
    {
      id: 1093,
      name: 'KN Fortune',
    },
    {
      id: 1092,
      name: 'Guroni',
    },
    {
      id: 1091,
      name: 'Shandong Hai Xing (FML)',
    },
    {
      id: 1090,
      name: 'Dong-A Krios (Old)',
    },
    {
      id: 1089,
      name: 'HL Saijo',
    },
    {
      id: 1088,
      name: 'Pacific Tianjin',
    },
    {
      id: 1087,
      name: 'Pacific Shenzhen',
    },
    {
      id: 1086,
      name: 'Pacific Carrier',
    },
    {
      id: 1085,
      name: 'TS Singapore',
    },
    {
      id: 1084,
      name: 'Gulf Deffi',
    },
    {
      id: 1082,
      name: 'Dong-A Triton (Old)',
    },
    {
      id: 1081,
      name: 'testing123',
    },
    {
      id: 1080,
      name: 'Chemroad Fuji',
    },
    {
      id: 1079,
      name: 'GH Rough Habit',
    },
    {
      id: 1078,
      name: 'Orion',
    },
    {
      id: 1077,
      name: 'Baltic North',
    },
    {
      id: 1076,
      name: 'Spring Warbler',
    },
    {
      id: 1075,
      name: 'Beijing Bridge',
    },
    {
      id: 1074,
      name: 'Mineral Hokkaido',
    },
    {
      id: 1073,
      name: 'Solar Skyler',
    },
    {
      id: 1072,
      name: 'Crown Iris',
    },
    {
      id: 1071,
      name: 'Celsius Messina',
    },
    {
      id: 1070,
      name: 'Cape Eternity',
    },
    {
      id: 1069,
      name: 'CL Suzhou',
    },
    {
      id: 1068,
      name: 'Celsius Carolina (SHI Hull No. 2314)',
    },
    {
      id: 1067,
      name: 'Celsius Charlotte (SHI Hull No. 2313)',
    },
    {
      id: 1066,
      name: 'Pacific Treasure',
    },
    {
      id: 1065,
      name: 'Straum',
    },
    {
      id: 1064,
      name: 'Aliyah Pertiwi',
    },
    {
      id: 1063,
      name: 'Vincent Mountain',
    },
    {
      id: 1062,
      name: 'Vincent Triton',
    },
    {
      id: 1061,
      name: 'Azul Victoria',
    },
    {
      id: 1060,
      name: 'sing test',
    },
    {
      id: 1058,
      name: 'TS Shanghai',
    },
    {
      id: 1057,
      name: 'TR Omaha (Old)',
    },
    {
      id: 1056,
      name: 'Western Singapore',
    },
    {
      id: 1055,
      name: 'Gulf Huwaylat',
    },
    {
      id: 1053,
      name: 'Shandong Zheng Tong',
    },
    {
      id: 1052,
      name: 'Solar Dolphin',
    },
    {
      id: 1051,
      name: 'Stellar Young',
    },
    {
      id: 1050,
      name: 'Amber Star (Old)',
    },
    {
      id: 1049,
      name: 'Gulf Trader',
    },
    {
      id: 1048,
      name: 'Jabal Samhan',
    },
    {
      id: 1047,
      name: 'Clearocean Maverick (S481)',
    },
    {
      id: 1046,
      name: 'Nord Valkyrie (S480)',
    },
    {
      id: 1045,
      name: 'Pacific Qingdao',
    },
    {
      id: 1044,
      name: 'Pacific Monaco',
    },
    {
      id: 1042,
      name: 'Azul Harmony',
    },
    {
      id: 1041,
      name: 'Test 123 2021-25-03',
    },
    {
      id: 1040,
      name: 'TS Yokohama',
    },
    {
      id: 1039,
      name: 'FMT Bergama',
    },
    {
      id: 1038,
      name: 'Onomichhi SNo. 772',
    },
    {
      id: 1036,
      name: 'Pacific Myra',
    },
    {
      id: 1034,
      name: 'High Force',
    },
    {
      id: 1033,
      name: 'Celsius Esbjerg',
    },
    {
      id: 1032,
      name: 'Bremen Belle (Old)',
    },
    {
      id: 1031,
      name: 'Solar Sharna',
    },
    {
      id: 1030,
      name: 'BLC Second',
    },
    {
      id: 1029,
      name: 'Bochem Oslo (Old)',
    },
    {
      id: 1028,
      name: 'Tristar Triumph',
    },
    {
      id: 1027,
      name: 'Santa Ursula',
    },
    {
      id: 1026,
      name: 'Celsius Riga (Old)',
    },
    {
      id: 1025,
      name: 'GH Frankel',
    },
    {
      id: 1024,
      name: 'Blue Sky 1',
    },
    {
      id: 1023,
      name: 'dasdsada',
    },
    {
      id: 1022,
      name: 'Sephora',
    },
    {
      id: 1021,
      name: 'Berge Ishizuchi',
    },
    {
      id: 1020,
      name: 'Ridgebury John B',
    },
    {
      id: 1019,
      name: 'Shandong Peng Cheng (Old)',
    },
    {
      id: 1018,
      name: 'Seamax Norwalk',
    },
    {
      id: 1017,
      name: 'Holly Arrow',
    },
    {
      id: 1016,
      name: 'Aquamarine Star',
    },
    {
      id: 1015,
      name: 'United Grace',
    },
    {
      id: 1014,
      name: 'Sunda',
    },
    {
      id: 1013,
      name: 'Shandong Ren He',
    },
    {
      id: 1012,
      name: 'Stellar Venture',
    },
    {
      id: 1011,
      name: 'TS Pusan',
    },
    {
      id: 1010,
      name: 'Celsius Rimini (Temp. Page)',
    },
    {
      id: 1009,
      name: 'Celsius Canberra (SHI Hull No. 2298)',
    },
    {
      id: 1008,
      name: 'Shandong He Xie',
    },
    {
      id: 1007,
      name: 'GH Chinook',
    },
    {
      id: 1006,
      name: 'FPMC P Fortune',
    },
    {
      id: 1005,
      name: 'Nord Vanquish',
    },
    {
      id: 1004,
      name: 'Berge Lyngor',
    },
    {
      id: 1003,
      name: 'TR Prince',
    },
    {
      id: 1002,
      name: 'Jane S',
    },
    {
      id: 1001,
      name: 'Shandong Chong Wen',
    },
    {
      id: 1000,
      name: 'Pacific Pioneer',
    },
    {
      id: 999,
      name: 'Marinor',
    },
    {
      id: 998,
      name: 'Aqua Splendor',
    },
    {
      id: 997,
      name: 'Singapore Bridge',
    },
    {
      id: 996,
      name: null,
    },
    {
      id: 995,
      name: 'Pacific Rawan',
    },
    {
      id: 991,
      name: 'Conception Light',
    },
    {
      id: 990,
      name: 'TS Bangkok',
    },
    {
      id: 989,
      name: 'GH Zephyr',
    },
    {
      id: 988,
      name: 'Stream Pacific',
    },
    {
      id: 987,
      name: 'Stream Arctic',
    },
    {
      id: 986,
      name: 'Mercury Sky',
    },
    {
      id: 985,
      name: 'Palchem 1',
    },
    {
      id: 984,
      name: 'Chemical Enterprise',
    },
    {
      id: 983,
      name: 'Rising Sky (Old)',
    },
    {
      id: 982,
      name: 'Ocean Sukses',
    },
    {
      id: 981,
      name: 'Ocean Phoenix',
    },
    {
      id: 980,
      name: 'Shandong Xin Rui',
    },
    {
      id: 977,
      name: 'Naess Courageous',
    },
    {
      id: 976,
      name: 'Eships Progress (ex. GH Secretariat) (Temp. use)',
    },
    {
      id: 975,
      name: null,
    },
    {
      id: 973,
      name: 'Ocean Future',
    },
    {
      id: 971,
      name: 'Harvest Sun',
    },
    {
      id: 970,
      name: 'new new vessel',
    },
    {
      id: 969,
      name: 'SG Express',
    },
    {
      id: 968,
      name: 'Xin Tai Hai',
    },
    {
      id: 967,
      name: 'GH Eclipse',
    },
    {
      id: 966,
      name: 'GH Dawn Run',
    },
    {
      id: 965,
      name: 'Bochem Chennai (internal use)',
    },
    {
      id: 964,
      name: 'Matsu Arrow',
    },
    {
      id: 963,
      name: 'MGI Two',
    },
    {
      id: 962,
      name: 'Amber Star',
    },
    {
      id: 960,
      name: 'MP MR Tanker 1',
    },
    {
      id: 959,
      name: 'GH Phar Lap',
    },
    {
      id: 958,
      name: 'Eships Prosperity (ex. GH Phar Lap) (Temp. Use)',
    },
    {
      id: 957,
      name: 'Explorer Asia',
    },
    {
      id: 956,
      name: 'Fomento Three (Old)',
    },
    {
      id: 955,
      name: 'Serpentine',
    },
    {
      id: 954,
      name: 'Fomento One (Old)',
    },
    {
      id: 953,
      name: 'ATA-M',
    },
    {
      id: 952,
      name: 'Harvest Peace',
    },
    {
      id: 950,
      name: 'Cygnus',
    },
    {
      id: 949,
      name: 'Test P1 to P2 sync',
    },
    {
      id: 948,
      name: 'Solar Suzanne',
    },
    {
      id: 947,
      name: 'S Cape',
    },
    {
      id: 946,
      name: 'GEM No. 5',
    },
    {
      id: 945,
      name: 'Monax',
    },
    {
      id: 944,
      name: 'Vittoria',
    },
    {
      id: 943,
      name: 'Humen Bridge',
    },
    {
      id: 942,
      name: 'Seattle Bridge',
    },
    {
      id: 940,
      name: 'High Explorer',
    },
    {
      id: 939,
      name: 'Beacon Hill',
    },
    {
      id: 938,
      name: 'MGI One',
    },
    {
      id: 936,
      name: 'Eships Barracuda',
    },
    {
      id: 934,
      name: 'Ocean Ace',
    },
    {
      id: 932,
      name: 'Naess Endurance',
    },
    {
      id: 931,
      name: 'Socar',
    },
    {
      id: 930,
      name: 'Ocean Makmur',
    },
    {
      id: 929,
      name: 'Naess Resolute',
    },
    {
      id: 928,
      name: 'Chemical Contender',
    },
    {
      id: 927,
      name: 'Saturnus',
    },
    {
      id: 926,
      name: 'Eships Agamid',
    },
    {
      id: 925,
      name: 'Bochem Chennai (Old)',
    },
    {
      id: 924,
      name: 'TS Hongkong',
    },
    {
      id: 923,
      name: 'Atlantic Journey',
    },
    {
      id: 922,
      name: 'Nord Fuji',
    },
    {
      id: 921,
      name: 'KN Future',
    },
    {
      id: 920,
      name: 'Biscayne Light',
    },
    {
      id: 919,
      name: 'GH Sky Beauty',
    },
    {
      id: 918,
      name: 'Grand Pioneer (Old)',
    },
    {
      id: 917,
      name: 'GH Scirocco',
    },
    {
      id: 916,
      name: 'Summer Sky',
    },
    {
      id: 915,
      name: 'Ocean Century',
    },
    {
      id: 914,
      name: 'Ocean Royal',
    },
    {
      id: 912,
      name: 'Weser',
    },
    {
      id: 911,
      name: 'Spar Ursa',
    },
    {
      id: 909,
      name: 'Wilton',
    },
    {
      id: 908,
      name: 'Venus Sky (Old)',
    },
    {
      id: 907,
      name: 'Mercury Sky (Old)',
    },
    {
      id: 906,
      name: 'Eships Dugon',
    },
    {
      id: 905,
      name: 'FG Rotterdam (Old)',
    },
    {
      id: 904,
      name: 'Celsius Macau',
    },
    {
      id: 903,
      name: 'Marmotas',
    },
    {
      id: 902,
      name: 'Mount Nicholson',
    },
    {
      id: 901,
      name: 'Bonas',
    },
    {
      id: 900,
      name: 'Finch Arrow',
    },
    {
      id: 899,
      name: 'Seamax Stratford',
    },
    {
      id: 898,
      name: 'Monaco Bridge',
    },
    {
      id: 897,
      name: 'Run Xiang',
    },
    {
      id: 896,
      name: 'Phoenix Hill',
    },
    {
      id: 895,
      name: 'Celsius Birdie',
    },
    {
      id: 894,
      name: 'Tectus',
    },
    {
      id: 893,
      name: 'GH Zonda',
    },
    {
      id: 892,
      name: 'Venus Sky',
    },
    {
      id: 891,
      name: 'Ocean Pearl',
    },
    {
      id: 890,
      name: 'Amber Alena',
    },
    {
      id: 889,
      name: 'Pacific Julia',
    },
    {
      id: 888,
      name: 'Ulriken (Internal Use)',
    },
    {
      id: 886,
      name: 'Jbu Onyx (2nd Old)',
    },
    {
      id: 885,
      name: 'Alpine Minute (2nd Old)',
    },
    {
      id: 884,
      name: 'Shandong Hai Tong',
    },
    {
      id: 883,
      name: 'YM Warranty',
    },
    {
      id: 882,
      name: 'YM Wonderland',
    },
    {
      id: 881,
      name: 'TR Princess',
    },
    {
      id: 880,
      name: 'Ever Goods',
    },
    {
      id: 879,
      name: 'GEM No. 3',
    },
    {
      id: 878,
      name: 'Maritina',
    },
    {
      id: 877,
      name: 'Marika',
    },
    {
      id: 876,
      name: 'TS Osaka',
    },
    {
      id: 874,
      name: 'GH Desert Orchid (Hull BG 03)',
    },
    {
      id: 873,
      name: 'Socar-2',
    },
    {
      id: 872,
      name: 'Hakkasan',
    },
    {
      id: 871,
      name: 'GH Galileo',
    },
    {
      id: 870,
      name: 'Pacific Sarah',
    },
    {
      id: 869,
      name: 'Star Eagle',
    },
    {
      id: 868,
      name: 'Bering Light (Internal use)',
    },
    {
      id: 867,
      name: 'Chemstar River',
    },
    {
      id: 866,
      name: 'Fortunato (Old)',
    },
    {
      id: 865,
      name: 'Oste',
    },
    {
      id: 864,
      name: 'GH Secretariat',
    },
    {
      id: 863,
      name: 'Sun Diana',
    },
    {
      id: 862,
      name: 'Celsius Penang (Edith Kirk)',
    },
    {
      id: 861,
      name: 'Couga',
    },
    {
      id: 860,
      name: 'Orient Light (Internal use)',
    },
    {
      id: 859,
      name: 'Hull No. 663 (Shintoku Kaiun)',
    },
    {
      id: 857,
      name: 'High Adventurer',
    },
    {
      id: 856,
      name: 'TR Porthos',
    },
    {
      id: 855,
      name: 'Mila',
    },
    {
      id: 854,
      name: 'Chemical Challenger (Old)',
    },
    {
      id: 853,
      name: 'OOCL Vancouver',
    },
    {
      id: 852,
      name: 'Pacific Debbie',
    },
    {
      id: 851,
      name: 'Mount Kellett',
    },
    {
      id: 850,
      name: 'Milano Bridge',
    },
    {
      id: 849,
      name: 'Run Far',
    },
    {
      id: 848,
      name: 'Jehuty',
    },
    {
      id: 847,
      name: 'Ocean Hiryu',
    },
    {
      id: 846,
      name: 'YM Wellspring',
    },
    {
      id: 845,
      name: 'Lake Dynasty',
    },
    {
      id: 844,
      name: 'Win Win',
    },
    {
      id: 840,
      name: 'Naess Absolute',
    },
    {
      id: 838,
      name: 'YM Wisdom',
    },
    {
      id: 837,
      name: 'Naess Intrepid',
    },
    {
      id: 836,
      name: 'YM Wellbeing',
    },
    {
      id: 835,
      name: 'Aliyah Pratama',
    },
    {
      id: 834,
      name: 'Celsius Palermo',
    },
    {
      id: 833,
      name: 'Nova',
    },
    {
      id: 832,
      name: 'Nancy P',
    },
    {
      id: 831,
      name: 'Chemical Explorer',
    },
    {
      id: 830,
      name: 'TRF Horten',
    },
    {
      id: 829,
      name: 'Xin Bin Hai',
    },
    {
      id: 828,
      name: 'SG Foundation',
    },
    {
      id: 827,
      name: 'Nord Olympia',
    },
    {
      id: 826,
      name: 'Nord Oceania',
    },
    {
      id: 825,
      name: 'TS Tokyo',
    },
    {
      id: 820,
      name: 'Holmen',
    },
    {
      id: 819,
      name: 'Natty',
    },
    {
      id: 818,
      name: 'Ocean Destiny',
    },
    {
      id: 816,
      name: 'Sky Ploeg',
    },
    {
      id: 814,
      name: 'Crown Mina',
    },
    {
      id: 813,
      name: 'Dover',
    },
    {
      id: 812,
      name: 'Barnet',
    },
    {
      id: 811,
      name: 'Spar Pavo',
    },
    {
      id: 810,
      name: 'Spar Indus',
    },
    {
      id: 809,
      name: 'Amstel Eagle',
    },
    {
      id: 808,
      name: 'Maharshi Bhavatreya',
    },
    {
      id: 807,
      name: 'Maharshi Krishnatreya',
    },
    {
      id: 806,
      name: 'Maharshi Atreya',
    },
    {
      id: 804,
      name: 'Shandong Hai Rong',
    },
    {
      id: 803,
      name: 'Abby',
    },
    {
      id: 801,
      name: 'MP Panamax 5 (Old)',
    },
    {
      id: 800,
      name: 'RB Lisa',
    },
    {
      id: 799,
      name: 'Pacific Fighter',
    },
    {
      id: 798,
      name: 'Amstel Falcon',
    },
    {
      id: 797,
      name: 'Melbourne',
    },
    {
      id: 796,
      name: 'Fomento Three',
    },
    {
      id: 795,
      name: 'GH Black Caviar',
    },
    {
      id: 794,
      name: 'GH Citation',
    },
    {
      id: 793,
      name: 'Pacific A. Dorodchi',
    },
    {
      id: 792,
      name: 'Fomento One',
    },
    {
      id: 791,
      name: 'Celsius Malaga',
    },
    {
      id: 790,
      name: 'Spar Pyxis',
    },
    {
      id: 789,
      name: 'CMB Partner',
    },
    {
      id: 788,
      name: 'Ultra Agility',
    },
    {
      id: 787,
      name: 'Silver Hessa',
    },
    {
      id: 786,
      name: 'Silver Muna',
    },
    {
      id: 785,
      name: 'Silver Sawsan',
    },
    {
      id: 784,
      name: 'Fairchem Falcon',
    },
    {
      id: 783,
      name: 'Sadanand',
    },
    {
      id: 782,
      name: 'Azerbaijan',
    },
    {
      id: 781,
      name: 'PSU Ninth',
    },
    {
      id: 780,
      name: 'Hull No. 651',
    },
    {
      id: 779,
      name: 'Torm Loke',
    },
    {
      id: 778,
      name: 'HTC Charlie',
    },
    {
      id: 777,
      name: 'Essie C',
    },
    {
      id: 776,
      name: 'Maharshi Vamadeva (Old)',
    },
    {
      id: 775,
      name: 'Torm Hardrada',
    },
    {
      id: 774,
      name: 'Harvest Rain',
    },
    {
      id: 773,
      name: 'Maharshi Bhavatreya (Old)',
    },
    {
      id: 772,
      name: 'Liberty',
    },
    {
      id: 771,
      name: 'Taipei Triumph',
    },
    {
      id: 770,
      name: 'Lowlands Amstel',
    },
    {
      id: 769,
      name: 'Lucia Ambition',
    },
    {
      id: 768,
      name: 'New Horizon',
    },
    {
      id: 767,
      name: 'Tokyo Triumph',
    },
    {
      id: 766,
      name: 'Mount Hallowes',
    },
    {
      id: 765,
      name: 'GH Northern Dancer',
    },
    {
      id: 764,
      name: 'Hudson',
    },
    {
      id: 763,
      name: 'TRF Charleston',
    },
    {
      id: 761,
      name: 'Hanjin Venezia',
    },
    {
      id: 759,
      name: 'Mariline',
    },
    {
      id: 758,
      name: 'Aqua Lauren',
    },
    {
      id: 756,
      name: 'Harriett',
    },
    {
      id: 755,
      name: 'Fairchem Friesian',
    },
    {
      id: 754,
      name: 'Western Oslo (Old)',
    },
    {
      id: 753,
      name: 'Nord Missouri',
    },
    {
      id: 752,
      name: 'Grand Pioneer',
    },
    {
      id: 751,
      name: 'Chemical Voyager',
    },
    {
      id: 750,
      name: 'Linton',
    },
    {
      id: 749,
      name: 'MOL Paradise',
    },
    {
      id: 748,
      name: 'Pacific Anna',
    },
    {
      id: 747,
      name: 'Ocean Dalian',
    },
    {
      id: 746,
      name: 'RB Jake',
    },
    {
      id: 745,
      name: 'Tristar Glory',
    },
    {
      id: 744,
      name: 'Neer',
    },
    {
      id: 743,
      name: 'Integrity',
    },
    {
      id: 742,
      name: 'Lime Galaxy',
    },
    {
      id: 741,
      name: 'Lavraki',
    },
    {
      id: 740,
      name: 'Malmo',
    },
    {
      id: 739,
      name: 'Gwen',
    },
    {
      id: 738,
      name: 'Eships Prosperity',
    },
    {
      id: 737,
      name: 'FG Rotterdam',
    },
    {
      id: 736,
      name: 'Loyalty',
    },
    {
      id: 735,
      name: 'Willard J',
    },
    {
      id: 734,
      name: 'Sichem Contester',
    },
    {
      id: 733,
      name: 'Sage Pioneer',
    },
    {
      id: 731,
      name: 'Tristar Courage',
    },
    {
      id: 730,
      name: 'Mount Gough',
    },
    {
      id: 729,
      name: 'UACC Muharraq',
    },
    {
      id: 728,
      name: 'Atlantic Light',
    },
    {
      id: 727,
      name: 'Grand Breaker',
    },
    {
      id: 726,
      name: 'Maharshi Vishwamitra',
    },
    {
      id: 724,
      name: 'Mid Fighter',
    },
    {
      id: 723,
      name: 'Fairchem Bronco',
    },
    {
      id: 722,
      name: 'Royal Bliss',
    },
    {
      id: 721,
      name: 'Aliyah Permata',
    },
    {
      id: 720,
      name: 'Berge Tsurugi',
    },
    {
      id: 719,
      name: 'Recco',
    },
    {
      id: 718,
      name: 'Pioneer Bay',
    },
    {
      id: 717,
      name: 'Nordic Americas',
    },
    {
      id: 716,
      name: 'Amstel Tiger',
    },
    {
      id: 715,
      name: 'UACC Messila',
    },
    {
      id: 714,
      name: 'UACC Shamiya',
    },
    {
      id: 713,
      name: 'Bochem Chennai',
    },
    {
      id: 712,
      name: 'Primrose Atlantic',
    },
    {
      id: 711,
      name: 'Marie S',
    },
    {
      id: 710,
      name: 'GH Danzero',
    },
    {
      id: 709,
      name: 'GH Storm Cat',
    },
    {
      id: 708,
      name: 'Pacific Nafsika',
    },
    {
      id: 706,
      name: 'Celsius Mexico (Internal)',
    },
    {
      id: 705,
      name: 'GH Seabird',
    },
    {
      id: 704,
      name: 'Red Sakura',
    },
    {
      id: 703,
      name: 'Marie C',
    },
    {
      id: 702,
      name: 'TR Lady',
    },
    {
      id: 701,
      name: 'RB Eden',
    },
    {
      id: 700,
      name: 'Pacific Jewels',
    },
    {
      id: 699,
      name: 'Louis P',
    },
    {
      id: 698,
      name: 'Silver Heba',
    },
    {
      id: 696,
      name: 'Harvest Legend',
    },
    {
      id: 695,
      name: 'Chem New York',
    },
    {
      id: 692,
      name: 'GEM No. 1',
    },
    {
      id: 691,
      name: 'Mount Cameron',
    },
    {
      id: 690,
      name: 'Mount Butler',
    },
    {
      id: 689,
      name: 'weqeqw',
    },
    {
      id: 688,
      name: 'Zircon',
    },
    {
      id: 687,
      name: 'Tristar Spirit',
    },
    {
      id: 686,
      name: 'Baltic Light',
    },
    {
      id: 685,
      name: 'Silver Venus',
    },
    {
      id: 684,
      name: '1_Owner_withoutSplit',
    },
    {
      id: 683,
      name: 'Red Orchid',
    },
    {
      id: 680,
      name: 'Tai Fu Star',
    },
    {
      id: 679,
      name: 'Snow Ploeg',
    },
    {
      id: 678,
      name: 'Sea Ploeg',
    },
    {
      id: 677,
      name: 'Triton Swallow',
    },
    {
      id: 676,
      name: 'Chemical Master',
    },
    {
      id: 675,
      name: 'Marycam Swan (Old)',
    },
    {
      id: 674,
      name: 'Neptune Horizon',
    },
    {
      id: 673,
      name: 'Citrine',
    },
    {
      id: 672,
      name: 'Sagar Kanta',
    },
    {
      id: 671,
      name: 'Sunbeam',
    },
    {
      id: 669,
      name: 'Nord Star (Old)',
    },
    {
      id: 668,
      name: 'Vessel Test',
    },
    {
      id: 667,
      name: 'Bow Wind',
    },
    {
      id: 666,
      name: 'Indian Express',
    },
    {
      id: 665,
      name: 'Silver Gertrude',
    },
    {
      id: 664,
      name: 'Sun Ploeg',
    },
    {
      id: 663,
      name: 'Spar Octans',
    },
    {
      id: 662,
      name: 'Maharshi Vamadeva',
    },
    {
      id: 661,
      name: 'Matrix',
    },
    {
      id: 660,
      name: 'Ever Judger',
    },
    {
      id: 659,
      name: 'Silver Esther',
    },
    {
      id: 658,
      name: 'Shandong Hai Yao',
    },
    {
      id: 657,
      name: 'Global Spirit',
    },
    {
      id: 656,
      name: 'Jbu Onyx (Old)',
    },
    {
      id: 655,
      name: 'Sunlight',
    },
    {
      id: 654,
      name: 'Gulf Barakah',
    },
    {
      id: 653,
      name: 'Indian Light',
    },
    {
      id: 652,
      name: 'Berwick',
    },
    {
      id: 651,
      name: 'One Munchen',
    },
    {
      id: 650,
      name: 'UACC Ibn Sina',
    },
    {
      id: 649,
      name: 'Gladys W',
    },
    {
      id: 648,
      name: 'Chemical Luna',
    },
    {
      id: 646,
      name: 'Federal Crimson',
    },
    {
      id: 645,
      name: 'Maemi II (2nd old)',
    },
    {
      id: 644,
      name: 'E Whale',
    },
    {
      id: 643,
      name: 'YM Navigator',
    },
    {
      id: 642,
      name: 'Spar Apus',
    },
    {
      id: 641,
      name: 'Stove Caledonia',
    },
    {
      id: 639,
      name: 'Maharshi Devatreya',
    },
    {
      id: 638,
      name: 'Selma B',
    },
    {
      id: 637,
      name: 'African Joy (Old)',
    },
    {
      id: 636,
      name: 'Fiesta',
    },
    {
      id: 635,
      name: 'Chemical Distributor (2nd old)',
    },
    {
      id: 632,
      name: 'Cano',
    },
    {
      id: 630,
      name: 'Marit (Old)',
    },
    {
      id: 629,
      name: 'Fortunato',
    },
    {
      id: 628,
      name: 'Acer Arrow',
    },
    {
      id: 627,
      name: 'PSU Fifth',
    },
    {
      id: 626,
      name: 'Berge Catherine',
    },
    {
      id: 625,
      name: 'TRF Kobe',
    },
    {
      id: 624,
      name: 'Ginkgo Arrow',
    },
    {
      id: 623,
      name: 'Cypress Arrow',
    },
    {
      id: 622,
      name: 'Pacific Silver',
    },
    {
      id: 621,
      name: 'Hegren',
    },
    {
      id: 620,
      name: 'Betula Arrow',
    },
    {
      id: 619,
      name: 'Maemi II (Old)',
    },
    {
      id: 618,
      name: 'Punita',
    },
    {
      id: 617,
      name: 'Pacific Condor',
    },
    {
      id: 615,
      name: 'A Ladybug',
    },
    {
      id: 614,
      name: 'Celsius Miami',
    },
    {
      id: 613,
      name: 'Orient Fortune',
    },
    {
      id: 612,
      name: 'Skausund',
    },
    {
      id: 611,
      name: 'YM Moderation',
    },
    {
      id: 610,
      name: 'Glory Claire',
    },
    {
      id: 608,
      name: 'Dodo',
    },
    {
      id: 606,
      name: 'Cape Dover (Old)',
    },
    {
      id: 605,
      name: 'Cape Legacy',
    },
    {
      id: 603,
      name: 'Veenus',
    },
    {
      id: 602,
      name: 'Sichem Defender (Old)',
    },
    {
      id: 601,
      name: 'Sher-E Punjab',
    },
    {
      id: 600,
      name: 'Maemi',
    },
    {
      id: 599,
      name: 'Obsidian',
    },
    {
      id: 598,
      name: 'Neptune Hellas',
    },
    {
      id: 597,
      name: 'Silver Dover',
    },
    {
      id: 596,
      name: 'Tharkey',
    },
    {
      id: 595,
      name: 'SC Stealth',
    },
    {
      id: 593,
      name: 'MP Panamax 4',
    },
    {
      id: 592,
      name: 'Dong-A Chronos',
    },
    {
      id: 591,
      name: 'Caribbean Express',
    },
    {
      id: 590,
      name: 'Dong-A Pontus',
    },
    {
      id: 589,
      name: 'One Manchester',
    },
    {
      id: 588,
      name: 'Challenge Paradise',
    },
    {
      id: 587,
      name: 'D Ladybug (Old)',
    },
    {
      id: 586,
      name: 'Sisouli Prem',
    },
    {
      id: 585,
      name: 'Orient Eternity',
    },
    {
      id: 583,
      name: 'Jbu Opal',
    },
    {
      id: 582,
      name: 'Silver Gwen',
    },
    {
      id: 581,
      name: 'Key Future',
    },
    {
      id: 580,
      name: 'Belfri',
    },
    {
      id: 578,
      name: 'APL Austria',
    },
    {
      id: 577,
      name: 'Brave Haralambos',
    },
    {
      id: 576,
      name: 'APL New Jersey',
    },
    {
      id: 575,
      name: 'Asavari',
    },
    {
      id: 573,
      name: 'Prem Putli',
    },
    {
      id: 572,
      name: 'Stolt Virtue',
    },
    {
      id: 571,
      name: 'Pacific Jewel',
    },
    {
      id: 570,
      name: 'Trident Hope (Old)',
    },
    {
      id: 569,
      name: 'Alpine Marie',
    },
    {
      id: 568,
      name: 'Atlantic Mirage',
    },
    {
      id: 567,
      name: 'Frio Olympic',
    },
    {
      id: 566,
      name: 'Rita M',
    },
    {
      id: 565,
      name: 'Songa Winds',
    },
    {
      id: 564,
      name: 'Sagarjeet',
    },
    {
      id: 563,
      name: 'Shamrock Venus',
    },
    {
      id: 561,
      name: 'Ocean Topaz',
    },
    {
      id: 560,
      name: 'Aruna Ismail',
    },
    {
      id: 559,
      name: 'Smart',
    },
    {
      id: 558,
      name: 'Draco Ocean',
    },
    {
      id: 557,
      name: 'Matumba',
    },
    {
      id: 556,
      name: 'Chem Venus',
    },
    {
      id: 555,
      name: 'Ocean Qingdao',
    },
    {
      id: 554,
      name: 'FPMC P Hero',
    },
    {
      id: 553,
      name: 'Shandong Hai Sheng',
    },
    {
      id: 552,
      name: 'Aqua Honor',
    },
    {
      id: 551,
      name: 'APL Norway',
    },
    {
      id: 550,
      name: 'Deleted',
    },
    {
      id: 549,
      name: 'Caecilie Bulker',
    },
    {
      id: 548,
      name: 'Five Stars Beijing',
    },
    {
      id: 547,
      name: 'Araya',
    },
    {
      id: 546,
      name: 'Chemical Marketer (old)',
    },
    {
      id: 545,
      name: 'Spar Lyra',
    },
    {
      id: 544,
      name: 'MSC Peru',
    },
    {
      id: 543,
      name: 'Morning Cornelia',
    },
    {
      id: 542,
      name: 'Southern Bay (Old)',
    },
    {
      id: 541,
      name: 'Southern Express (OLD)',
    },
    {
      id: 538,
      name: 'Delfa',
    },
    {
      id: 537,
      name: 'Harvest Festival',
    },
    {
      id: 536,
      name: 'YM Movement',
    },
    {
      id: 535,
      name: 'Sagar Moti',
    },
    {
      id: 534,
      name: 'Triton Valk',
    },
    {
      id: 533,
      name: 'Calypso Colossus',
    },
    {
      id: 532,
      name: 'Maritime Prosperity',
    },
    {
      id: 531,
      name: 'Suzuka Express',
    },
    {
      id: 530,
      name: 'Maharshi Shivatreya',
    },
    {
      id: 529,
      name: 'Celsius Manila',
    },
    {
      id: 528,
      name: 'Torm Eric',
    },
    {
      id: 525,
      name: 'Chemical Provider',
    },
    {
      id: 524,
      name: 'PSU Second',
    },
    {
      id: 523,
      name: 'Shandong Hai Wang',
    },
    {
      id: 520,
      name: 'Xin Hai Tong 8',
    },
    {
      id: 519,
      name: 'New Pioneer',
    },
    {
      id: 518,
      name: 'Maharshi Bhardwaj',
    },
    {
      id: 517,
      name: 'Cape Riviera (Old)',
    },
    {
      id: 516,
      name: 'Bochem Oslo',
    },
    {
      id: 515,
      name: 'Julia L',
    },
    {
      id: 514,
      name: 'Summer Ploeg',
    },
    {
      id: 513,
      name: 'PSU Sixth',
    },
    {
      id: 512,
      name: 'Peruvian Express (3rd old)',
    },
    {
      id: 511,
      name: 'Noble Jade',
    },
    {
      id: 510,
      name: 'Alpine Minute (Old)',
    },
    {
      id: 509,
      name: 'Maritime Taboneo (Old)',
    },
    {
      id: 508,
      name: 'Arcadia Progress',
    },
    {
      id: 507,
      name: 'Sagar Ratan',
    },
    {
      id: 506,
      name: 'Dong-A Rigel',
    },
    {
      id: 505,
      name: 'PSU Chile',
    },
    {
      id: 504,
      name: 'Njord Clear',
    },
    {
      id: 502,
      name: 'Dong-A Sirius',
    },
    {
      id: 499,
      name: 'Orchid Star',
    },
    {
      id: 497,
      name: 'Polar Discovery',
    },
    {
      id: 496,
      name: 'Star Ploeg',
    },
    {
      id: 495,
      name: 'Spar Aries',
    },
    {
      id: 494,
      name: 'La Paix',
    },
    {
      id: 493,
      name: 'Aegean Express (Old)',
    },
    {
      id: 492,
      name: 'Hellen',
    },
    {
      id: 491,
      name: 'CNC Bangkok',
    },
    {
      id: 490,
      name: 'Pacific Singapore (Old)',
    },
    {
      id: 489,
      name: 'Zeyno',
    },
    {
      id: 488,
      name: 'Hako',
    },
    {
      id: 487,
      name: 'Lowlands Serenity',
    },
    {
      id: 486,
      name: 'Spar Corona',
    },
    {
      id: 485,
      name: 'GMT Venus',
    },
    {
      id: 484,
      name: 'Siberian Express',
    },
    {
      id: 483,
      name: 'Pacific Fantasy',
    },
    {
      id: 482,
      name: 'Triton Seahawk',
    },
    {
      id: 481,
      name: 'Persus Ocean',
    },
    {
      id: 480,
      name: 'Clipper Freeway',
    },
    {
      id: 479,
      name: 'FMT Gumuldur',
    },
    {
      id: 478,
      name: 'Stolt Vanguard',
    },
    {
      id: 477,
      name: 'MSC Parana',
    },
    {
      id: 476,
      name: 'Belo Horizonte (Old)',
    },
    {
      id: 475,
      name: 'Cassiopeia Bulker',
    },
    {
      id: 474,
      name: 'Lily Atlantic',
    },
    {
      id: 473,
      name: 'E Trader',
    },
    {
      id: 472,
      name: 'Serene Star',
    },
    {
      id: 471,
      name: 'Rainbow',
    },
    {
      id: 470,
      name: 'OOCL Ambition',
    },
    {
      id: 469,
      name: 'Winco Providence',
    },
    {
      id: 468,
      name: 'Premvati',
    },
    {
      id: 467,
      name: 'Pacific Glory',
    },
    {
      id: 466,
      name: 'Galata Star',
    },
    {
      id: 465,
      name: 'Bright Future',
    },
    {
      id: 464,
      name: 'Santa Rosa',
    },
    {
      id: 463,
      name: 'Prem Mala',
    },
    {
      id: 462,
      name: 'Golten',
    },
    {
      id: 460,
      name: 'Fortune Victoria',
    },
    {
      id: 458,
      name: 'Morning Camilla',
    },
    {
      id: 456,
      name: 'Promise 1',
    },
    {
      id: 455,
      name: 'Prem Pride (old)',
    },
    {
      id: 454,
      name: 'Asphodel (old)',
    },
    {
      id: 453,
      name: 'Barbouni',
    },
    {
      id: 452,
      name: 'Positive Brave',
    },
    {
      id: 451,
      name: 'Tendra Trader',
    },
    {
      id: 450,
      name: 'Beffen',
    },
    {
      id: 449,
      name: 'Doro',
    },
    {
      id: 448,
      name: 'Fantholmen',
    },
    {
      id: 447,
      name: 'Signe Bulker',
    },
    {
      id: 446,
      name: 'Aqua Vision',
    },
    {
      id: 445,
      name: 'Malhari',
    },
    {
      id: 444,
      name: 'Ocean Ruby',
    },
    {
      id: 443,
      name: 'Frio Dolphin',
    },
    {
      id: 442,
      name: 'Spar Hydra',
    },
    {
      id: 441,
      name: 'Bochem Mumbai',
    },
    {
      id: 440,
      name: 'Spar Corvus',
    },
    {
      id: 439,
      name: 'GMT Polaris',
    },
    {
      id: 438,
      name: 'Spar Rigel',
    },
    {
      id: 437,
      name: 'GH Seabiscuit',
    },
    {
      id: 436,
      name: 'Spar Mira',
    },
    {
      id: 435,
      name: 'Ocean Amber',
    },
    {
      id: 433,
      name: 'Charles Martin',
    },
    {
      id: 430,
      name: 'Ursula',
    },
    {
      id: 429,
      name: 'Shandong De Tai',
    },
    {
      id: 428,
      name: 'Yi Chun 15',
    },
    {
      id: 427,
      name: 'White Hawk',
    },
    {
      id: 426,
      name: 'Marycam Swan',
    },
    {
      id: 425,
      name: 'Triton Swan',
    },
    {
      id: 424,
      name: 'Delmar',
    },
    {
      id: 423,
      name: 'Asian Dynasty',
    },
    {
      id: 422,
      name: 'Anacapa Light',
    },
    {
      id: 420,
      name: 'Peruvian Express',
    },
    {
      id: 419,
      name: 'Lark',
    },
    {
      id: 418,
      name: 'Crown Esmeralda',
    },
    {
      id: 417,
      name: 'Rachel',
    },
    {
      id: 416,
      name: 'Aruna Ece',
    },
    {
      id: 415,
      name: 'Aruna Hulya',
    },
    {
      id: 414,
      name: 'Felicia',
    },
    {
      id: 413,
      name: 'Cielo Lucia',
    },
    {
      id: 411,
      name: 'Istrian Express',
    },
    {
      id: 409,
      name: 'Bahia (Old)',
    },
    {
      id: 408,
      name: 'Panorama',
    },
    {
      id: 407,
      name: 'Cape Valencia',
    },
    {
      id: 406,
      name: 'Han Fu Star',
    },
    {
      id: 405,
      name: 'FPMC C Melody',
    },
    {
      id: 404,
      name: 'Pacific Jewel (Old)',
    },
    {
      id: 403,
      name: 'Atlantic Mirage (Old)',
    },
    {
      id: 402,
      name: 'Atlantic Muse (Old)',
    },
    {
      id: 401,
      name: 'Silvia Glory',
    },
    {
      id: 400,
      name: 'C Prosperity',
    },
    {
      id: 399,
      name: 'Silvia Ambition',
    },
    {
      id: 398,
      name: 'Alpine Magic (Old)',
    },
    {
      id: 397,
      name: 'Tokomaru Bay',
    },
    {
      id: 396,
      name: 'Sepang Express',
    },
    {
      id: 395,
      name: 'Morning Charlotte',
    },
    {
      id: 394,
      name: 'Pos Jade (Old)',
    },
    {
      id: 393,
      name: 'Italian Express',
    },
    {
      id: 392,
      name: 'Fleet Trader 1',
    },
    {
      id: 391,
      name: 'Shandong Hai Chang',
    },
    {
      id: 390,
      name: 'Jo Kiri',
    },
    {
      id: 389,
      name: 'Corona Bulker',
    },
    {
      id: 388,
      name: 'Stolt Valor (Old)',
    },
    {
      id: 387,
      name: 'Rike',
    },
    {
      id: 386,
      name: 'Spar Libra',
    },
    {
      id: 384,
      name: 'Morning Crystal',
    },
    {
      id: 383,
      name: 'Ocean Diamond',
    },
    {
      id: 382,
      name: 'Ocean Prefect',
    },
    {
      id: 381,
      name: 'Ocean Emerald',
    },
    {
      id: 379,
      name: 'Texas',
    },
    {
      id: 377,
      name: 'MP Panamax 3',
    },
    {
      id: 376,
      name: 'Bochem Singapura',
    },
    {
      id: 375,
      name: 'Scotian Express',
    },
    {
      id: 374,
      name: 'Ten Yoshi Maru',
    },
    {
      id: 373,
      name: 'Stolt Vanguard (Old)',
    },
    {
      id: 372,
      name: 'Spar Spica',
    },
    {
      id: 370,
      name: 'Cooper',
    },
    {
      id: 369,
      name: 'Stolt Teal',
    },
    {
      id: 368,
      name: 'Aries Confidence',
    },
    {
      id: 366,
      name: 'eqweqwe8980990',
    },
    {
      id: 365,
      name: 'Lyngholmen',
    },
    {
      id: 364,
      name: 'Luisia Colossus',
    },
    {
      id: 363,
      name: 'Tarsus',
    },
    {
      id: 362,
      name: 'Shandong De Yu',
    },
    {
      id: 361,
      name: 'Rosco Palm',
    },
    {
      id: 360,
      name: 'Rosco Litchi',
    },
    {
      id: 359,
      name: 'Promise 2',
    },
    {
      id: 358,
      name: 'Terra Lumina',
    },
    {
      id: 357,
      name: 'Cambria Colossus',
    },
    {
      id: 356,
      name: 'N251 R B D Ocean Of Joy',
    },
    {
      id: 355,
      name: 'Daffodil',
    },
    {
      id: 354,
      name: 'Sentosa Bulker',
    },
    {
      id: 353,
      name: 'Orchard Bulker',
    },
    {
      id: 352,
      name: 'Scandinavian Express',
    },
    {
      id: 351,
      name: 'Emilie Bulker',
    },
    {
      id: 350,
      name: 'Chemical Mariner',
    },
    {
      id: 349,
      name: 'Belgian Express',
    },
    {
      id: 347,
      name: 'AAV9 Miniserver Hong Kong',
    },
    {
      id: 346,
      name: 'Fortune Ocean',
    },
    {
      id: 345,
      name: 'V9 Miniserver Mumbai',
    },
    {
      id: 344,
      name: 'Ingolstadt',
    },
    {
      id: 343,
      name: 'Tenmyo Maru',
    },
    {
      id: 342,
      name: 'Triton Ace',
    },
    {
      id: 341,
      name: 'Bahamian Express',
    },
    {
      id: 340,
      name: 'High Pearl',
    },
    {
      id: 339,
      name: 'Ocean Garnet',
    },
    {
      id: 335,
      name: 'Monza Express',
    },
    {
      id: 333,
      name: 'Triton Condor (KOYO -2298)',
    },
    {
      id: 332,
      name: 'Akshneer',
    },
    {
      id: 331,
      name: 'Asphodel',
    },
    {
      id: 330,
      name: 'Diamond Queen',
    },
    {
      id: 329,
      name: 'Emerald Queen',
    },
    {
      id: 328,
      name: 'Al Farabi',
    },
    {
      id: 326,
      name: 'Celsius Mumbai',
    },
    {
      id: 325,
      name: 'UACC AL Medinae',
    },
    {
      id: 324,
      name: '1_RegOwner_withoutSplit',
    },
    {
      id: 323,
      name: 'Glory Marugame',
    },
    {
      id: 322,
      name: 'Devsi',
    },
    {
      id: 321,
      name: 'Uruguay Star (Old)',
    },
    {
      id: 320,
      name: 'MOL Premium',
    },
    {
      id: 319,
      name: 'Honduras Star (Old)',
    },
    {
      id: 318,
      name: 'Maistros Breeze (Old)',
    },
    {
      id: 317,
      name: 'Trans Trader',
    },
    {
      id: 316,
      name: 'Timat 1',
    },
    {
      id: 315,
      name: 'Stolt Vision',
    },
    {
      id: 314,
      name: 'Spar Lynx',
    },
    {
      id: 313,
      name: 'Prem Prachi',
    },
    {
      id: 312,
      name: 'Regal Star (Old)',
    },
    {
      id: 311,
      name: 'Iver Spirit (Old)',
    },
    {
      id: 310,
      name: 'Triton Stork',
    },
    {
      id: 309,
      name: 'Oak Star',
    },
    {
      id: 308,
      name: 'Atlantic Trader',
    },
    {
      id: 307,
      name: 'Saturnus (old)',
    },
    {
      id: 306,
      name: 'S. Venus',
    },
    {
      id: 305,
      name: 'Sibulk Dedication (Old)',
    },
    {
      id: 304,
      name: 'Nord Strait (Old)',
    },
    {
      id: 303,
      name: 'Philippine Express',
    },
    {
      id: 302,
      name: 'Iver Express',
    },
    {
      id: 301,
      name: 'Star Providence (Old)',
    },
    {
      id: 300,
      name: 'Test Vessel - Julietta',
    },
    {
      id: 299,
      name: 'Maritime Harmony',
    },
    {
      id: 298,
      name: 'Argentina Star (Old)',
    },
    {
      id: 297,
      name: 'Union Pride',
    },
    {
      id: 296,
      name: 'Isis',
    },
    {
      id: 295,
      name: 'Brasil Star (Old)',
    },
    {
      id: 294,
      name: 'Union Force',
    },
    {
      id: 293,
      name: 'Stolt Razorbill',
    },
    {
      id: 292,
      name: 'Lowlands Sumida',
    },
    {
      id: 291,
      name: 'Atlantic Diana',
    },
    {
      id: 290,
      name: 'River Eternity',
    },
    {
      id: 289,
      name: 'Kyodo Progress',
    },
    {
      id: 288,
      name: 'Eurasian Brilliance',
    },
    {
      id: 287,
      name: 'Eurasian Chariot',
    },
    {
      id: 286,
      name: 'Eurasian Alliance',
    },
    {
      id: 285,
      name: 'Tolteca',
    },
    {
      id: 284,
      name: 'Safe Voyager',
    },
    {
      id: 283,
      name: 'Barock',
    },
    {
      id: 281,
      name: 'Brasil Star',
    },
    {
      id: 280,
      name: 'Chile Star (Old)',
    },
    {
      id: 279,
      name: 'Celsius Manhattan',
    },
    {
      id: 278,
      name: 'asdasd',
    },
    {
      id: 277,
      name: 'Pacific Brave',
    },
    {
      id: 276,
      name: 'Pacific Apollo',
    },
    {
      id: 275,
      name: 'OOCL Kuala Lumpur',
    },
    {
      id: 274,
      name: 'Atlantic Blue',
    },
    {
      id: 272,
      name: 'Everest Bay',
    },
    {
      id: 271,
      name: 'Atlantic Crown',
    },
    {
      id: 270,
      name: 'Ivory Tirupati',
    },
    {
      id: 268,
      name: 'Amer Himalaya',
    },
    {
      id: 267,
      name: 'Amer Choapa',
    },
    {
      id: 266,
      name: 'M.P.Panamax-1',
    },
    {
      id: 265,
      name: 'Orient Light',
    },
    {
      id: 264,
      name: 'Cala Paguro',
    },
    {
      id: 263,
      name: 'Mediterranean Highway',
    },
    {
      id: 261,
      name: 'Ridgebury Katherine Z',
    },
    {
      id: 260,
      name: 'Triton Seagull',
    },
    {
      id: 259,
      name: 'CMB Chardonnay',
    },
    {
      id: 258,
      name: 'Kailash',
    },
    {
      id: 257,
      name: 'Swan Bay',
    },
    {
      id: 256,
      name: 'Beech Galaxy',
    },
    {
      id: 255,
      name: 'Dahlia',
    },
    {
      id: 254,
      name: 'Fa Mei Shan',
    },
    {
      id: 252,
      name: 'Spar Gemini',
    },
    {
      id: 250,
      name: 'Fostraum',
    },
    {
      id: 249,
      name: 'Saltstraum (old)',
    },
    {
      id: 248,
      name: 'Sundstraum',
    },
    {
      id: 246,
      name: 'Jbu Schelde',
    },
    {
      id: 245,
      name: 'Jbu Forth',
    },
    {
      id: 244,
      name: 'Jbu Maas',
    },
    {
      id: 242,
      name: 'High Prosperity',
    },
    {
      id: 240,
      name: 'Camellia',
    },
    {
      id: 239,
      name: 'CMA CGM Violet',
    },
    {
      id: 236,
      name: 'Spar Scorpio',
    },
    {
      id: 235,
      name: 'Pacific Challenger',
    },
    {
      id: 233,
      name: 'Celsius Mayfair',
    },
    {
      id: 232,
      name: 'Solar Sheridan',
    },
    {
      id: 230,
      name: 'asdsad',
    },
    {
      id: 229,
      name: 'Pos Jade',
    },
    {
      id: 227,
      name: 'Emirates Swan',
    },
    {
      id: 226,
      name: 'Triton Swift (old)',
    },
    {
      id: 225,
      name: 'Imogen',
    },
    {
      id: 224,
      name: 'Chemical Voyager (old)',
    },
    {
      id: 223,
      name: 'Golden Companion',
    },
    {
      id: 222,
      name: 'Union Spirit',
    },
    {
      id: 221,
      name: 'Wari',
    },
    {
      id: 220,
      name: 'Union Bay',
    },
    {
      id: 219,
      name: 'Peruvian Express (2nd Old)',
    },
    {
      id: 218,
      name: 'Silver Constellation',
    },
    {
      id: 217,
      name: 'Mid Falcon',
    },
    {
      id: 216,
      name: 'Orient Alliance',
    },
    {
      id: 214,
      name: 'Canada Express',
    },
    {
      id: 212,
      name: 'Eastern Bay (Old)',
    },
    {
      id: 211,
      name: 'OOCL Kaohsiung',
    },
    {
      id: 210,
      name: 'Timor Stream (Old)',
    },
    {
      id: 209,
      name: 'CE-Mikela',
    },
    {
      id: 208,
      name: 'Trident Hope',
    },
    {
      id: 207,
      name: 'Polar Belgica',
    },
    {
      id: 206,
      name: 'Chile Star',
    },
    {
      id: 205,
      name: 'Argentina Star',
    },
    {
      id: 203,
      name: 'Prem Pranshu',
    },
    {
      id: 202,
      name: 'Southern Express (Vroon)',
    },
    {
      id: 201,
      name: 'Scandinavian Express (V)',
    },
    {
      id: 200,
      name: 'Nescall',
    },
    {
      id: 199,
      name: 'Ocean Shanghai',
    },
    {
      id: 198,
      name: 'Clipper Suffolk',
    },
    {
      id: 197,
      name: 'Bow Engineer',
    },
    {
      id: 196,
      name: 'Ocean Prologue',
    },
    {
      id: 195,
      name: 'Ocean Probe',
    },
    {
      id: 194,
      name: 'MSC Zurich',
    },
    {
      id: 193,
      name: 'MSC Greece',
    },
    {
      id: 192,
      name: 'MSC Belem',
    },
    {
      id: 191,
      name: 'MSC Nairobi',
    },
    {
      id: 190,
      name: 'Spar Taurus',
    },
    {
      id: 189,
      name: 'Terra Bona',
    },
    {
      id: 188,
      name: 'Pacific Dhow',
    },
    {
      id: 187,
      name: 'Uruguay Star',
    },
    {
      id: 186,
      name: 'Ecuador Star',
    },
    {
      id: 185,
      name: 'Celsius Rome',
    },
    {
      id: 184,
      name: 'Columbian Express',
    },
    {
      id: 183,
      name: 'Chemical Distributor (Old)',
    },
    {
      id: 182,
      name: 'Ducky Science',
    },
    {
      id: 181,
      name: 'Sarla',
    },
    {
      id: 180,
      name: 'Stream Express (Vroon)',
    },
    {
      id: 179,
      name: 'Giada D',
    },
    {
      id: 178,
      name: 'Trident Star',
    },
    {
      id: 177,
      name: 'Lykes Osprey (Old)',
    },
    {
      id: 176,
      name: 'Ikaria',
    },
    {
      id: 175,
      name: 'Red Nacre',
    },
    {
      id: 173,
      name: 'Hertford',
    },
    {
      id: 170,
      name: 'Perth',
    },
    {
      id: 169,
      name: 'Prem Gopali',
    },
    {
      id: 168,
      name: 'Spar Virgo edited',
    },
    {
      id: 167,
      name: 'Stream Express (Old)',
    },
    {
      id: 166,
      name: 'Tianjin Pioneer',
    },
    {
      id: 165,
      name: 'Triton Lark',
    },
    {
      id: 164,
      name: 'Asian Hope',
    },
    {
      id: 163,
      name: 'Liberty Ace',
    },
    {
      id: 162,
      name: 'Iver Prosperity',
    },
    {
      id: 161,
      name: 'Iberian Express (Old)',
    },
    {
      id: 160,
      name: 'Arisara',
    },
    {
      id: 159,
      name: 'Amer Everest',
    },
    {
      id: 158,
      name: 'UACC IBN Al Atheer',
    },
    {
      id: 157,
      name: 'Spar Topaz',
    },
    {
      id: 156,
      name: 'Maritime Taboneo',
    },
    {
      id: 154,
      name: 'Syrena',
    },
    {
      id: 153,
      name: 'Spar Eight',
    },
    {
      id: 152,
      name: 'Bow Singapore',
    },
    {
      id: 151,
      name: 'Bow Asia',
    },
    {
      id: 150,
      name: 'Multitank Batavia',
    },
    {
      id: 149,
      name: 'Nord Minute',
    },
    {
      id: 148,
      name: 'Noble Empress',
    },
    {
      id: 147,
      name: 'Kedarnath',
    },
    {
      id: 146,
      name: 'Atlantic Muse',
    },
    {
      id: 145,
      name: 'Lake Dahlia',
    },
    {
      id: 144,
      name: 'Amer Fuji',
    },
    {
      id: 143,
      name: 'Amer Whitney',
    },
    {
      id: 142,
      name: 'Far Singapore',
    },
    {
      id: 141,
      name: 'Ipsea Colossus',
    },
    {
      id: 140,
      name: 'Badrinath',
    },
    {
      id: 138,
      name: 'Clipper Grace',
    },
    {
      id: 137,
      name: 'Singapore Grace',
    },
    {
      id: 136,
      name: 'Super Ace',
    },
    {
      id: 134,
      name: 'Triton Eagle',
    },
    {
      id: 133,
      name: 'Midnight Sun',
    },
    {
      id: 132,
      name: 'Chinook Maiden',
    },
    {
      id: 131,
      name: 'Fjordstraum',
    },
    {
      id: 130,
      name: 'Saltstraum',
    },
    {
      id: 129,
      name: 'Elliott Bay',
    },
    {
      id: 128,
      name: 'Sanya',
    },
    {
      id: 127,
      name: 'UACC Sound',
    },
    {
      id: 126,
      name: 'Sagami',
    },
    {
      id: 125,
      name: 'Cala Portofino',
    },
    {
      id: 124,
      name: 'Seaways Venture',
    },
    {
      id: 123,
      name: 'Cala Positano',
    },
    {
      id: 122,
      name: 'Semakau',
    },
    {
      id: 121,
      name: 'Cala Ponente',
    },
    {
      id: 120,
      name: 'Kilstraum',
    },
    {
      id: 119,
      name: 'Sydstraum',
    },
    {
      id: 118,
      name: 'MOL Treasure',
    },
    {
      id: 117,
      name: 'Jbu Onyx2',
    },
    {
      id: 116,
      name: 'Spar Leo',
    },
    {
      id: 115,
      name: 'New Resolution',
    },
    {
      id: 114,
      name: 'Mexican Reefer',
    },
    {
      id: 113,
      name: 'Andalucia Star',
    },
    {
      id: 112,
      name: 'Avelona Star',
    },
    {
      id: 111,
      name: 'Constantinoupolis',
    },
    {
      id: 110,
      name: 'BM Breeze',
    },
    {
      id: 108,
      name: 'Freja Baltic',
    },
    {
      id: 107,
      name: 'Nord Obtainer',
    },
    {
      id: 106,
      name: 'Union Breeze',
    },
    {
      id: 105,
      name: 'Cape Dover',
    },
    {
      id: 104,
      name: 'Chem Polaris',
    },
    {
      id: 103,
      name: 'TRF Miami',
    },
    {
      id: 102,
      name: 'Spar Vega (old)',
    },
    {
      id: 101,
      name: 'Panam Flota (Old)',
    },
    {
      id: 100,
      name: 'Panam Felice (Old)',
    },
    {
      id: 99,
      name: 'TH Symphony',
    },
    {
      id: 98,
      name: 'Raffles Light',
    },
    {
      id: 97,
      name: 'Maud',
    },
    {
      id: 96,
      name: 'Bow De Feng',
    },
    {
      id: 95,
      name: 'Cumbrian Express',
    },
    {
      id: 94,
      name: 'Polar Endurance',
    },
    {
      id: 93,
      name: 'Levantes Breeze',
    },
    {
      id: 92,
      name: 'Ostria Breeze',
    },
    {
      id: 91,
      name: 'Zefyros Breeze',
    },
    {
      id: 90,
      name: 'Sirocco Breeze',
    },
    {
      id: 89,
      name: 'New Independence',
    },
    {
      id: 88,
      name: 'Swan Stream',
    },
    {
      id: 87,
      name: 'Spar Two',
    },
    {
      id: 86,
      name: 'Spar Three',
    },
    {
      id: 85,
      name: 'Spar Sirius',
    },
    {
      id: 84,
      name: 'Spar Orion',
    },
    {
      id: 83,
      name: 'Spar Jade',
    },
    {
      id: 82,
      name: 'Sinar Lombok',
    },
    {
      id: 81,
      name: 'MSC Paraguay',
    },
    {
      id: 80,
      name: 'Min Noble',
    },
    {
      id: 79,
      name: 'Ecuadorian Reefer',
    },
    {
      id: 78,
      name: 'Canadian Star',
    },
    {
      id: 77,
      name: 'Pioneer Sky',
    },
    {
      id: 76,
      name: 'Noble Dragon',
    },
    {
      id: 75,
      name: 'Rosco Cypress',
    },
    {
      id: 74,
      name: 'Celsius Perth (Marie Kirk)',
    },
    {
      id: 73,
      name: 'Tasman Campaigner',
    },
    {
      id: 72,
      name: 'Marit',
    },
    {
      id: 71,
      name: 'Ducky Shiny',
    },
    {
      id: 70,
      name: 'Chem Star',
    },
    {
      id: 69,
      name: 'Almeda Star',
    },
    {
      id: 68,
      name: 'Andalusian Express (Old)',
    },
    {
      id: 65,
      name: 'Tasman Commander',
    },
    {
      id: 64,
      name: 'Peruvian Express (old)',
    },
    {
      id: 63,
      name: 'Australian Express',
    },
    {
      id: 62,
      name: 'Akari',
    },
    {
      id: 61,
      name: 'Chembulk Shanghai',
    },
    {
      id: 60,
      name: 'Spar Carina',
    },
    {
      id: 59,
      name: 'Noble Light',
    },
    {
      id: 58,
      name: 'Cielo Lucia (Old)',
    },
    {
      id: 57,
      name: 'Bow De Silver',
    },
    {
      id: 56,
      name: 'Afric Star',
    },
    {
      id: 55,
      name: 'Waglan Light',
    },
    {
      id: 54,
      name: 'Spar Capella (old)',
    },
    {
      id: 53,
      name: 'Steintor',
    },
    {
      id: 52,
      name: 'Victoria Louise',
    },
    {
      id: 51,
      name: 'Stolt Valor',
    },
    {
      id: 50,
      name: 'Aqua Fortune',
    },
    {
      id: 49,
      name: 'Spike',
    },
    {
      id: 48,
      name: 'New Era',
    },
    {
      id: 47,
      name: 'Isaac Light',
    },
    {
      id: 46,
      name: 'Perth Bridge',
    },
    {
      id: 45,
      name: 'Pyramid Light',
    },
    {
      id: 44,
      name: 'Spar Ruby',
    },
    {
      id: 43,
      name: 'Jupiter Light',
    },
    {
      id: 42,
      name: 'Scotian Express (Old)',
    },
    {
      id: 41,
      name: 'Sagarkiran',
    },
    {
      id: 40,
      name: 'Spar Neptun',
    },
    {
      id: 39,
      name: 'Bow De Jin',
    },
    {
      id: 38,
      name: 'Jackaroo',
    },
    {
      id: 37,
      name: 'Venice Express',
    },
    {
      id: 36,
      name: 'Tamdhu',
    },
    {
      id: 35,
      name: 'Swan Ocean',
    },
    {
      id: 34,
      name: 'Swan Lagoon',
    },
    {
      id: 33,
      name: 'Star Providence',
    },
    {
      id: 32,
      name: 'Spar Opal',
    },
    {
      id: 31,
      name: 'Angel Light',
    },
    {
      id: 30,
      name: 'Bahamian Express (old)',
    },
    {
      id: 29,
      name: 'Bermudian Express',
    },
    {
      id: 27,
      name: 'Spar Garnet',
    },
    {
      id: 25,
      name: 'Pacific Osprey',
    },
    {
      id: 24,
      name: 'Swan River',
    },
    {
      id: 23,
      name: 'Ivory Nina',
    },
    {
      id: 22,
      name: 'Spar Cetus',
    },
    {
      id: 21,
      name: 'Caribbean Star',
    },
    {
      id: 20,
      name: 'Regal Star',
    },
    {
      id: 19,
      name: 'Bow De Rich',
    },
    {
      id: 18,
      name: 'Stolt Skua',
    },
    {
      id: 16,
      name: 'Young Liberty',
    },
    {
      id: 14,
      name: 'Highland Light',
    },
    {
      id: 13,
      name: 'Iver Progress',
    },
    {
      id: 12,
      name: 'Avila Star',
    },
    {
      id: 11,
      name: 'Valparaiso Stars',
    },
    {
      id: 10,
      name: 'Spar Corona (Old)',
    },
    {
      id: 9,
      name: 'Katsina',
    },
    {
      id: 8,
      name: 'Costa Rican Star',
    },
    {
      id: 7,
      name: 'Alpine Magic new',
    },
    {
      id: 6,
      name: 'Chemical Sprinter',
    },
    {
      id: 5,
      name: 'test sing vessel 1',
    },
  ];
};

export const getShipPartyDataResponse = () => {
  return {
    pagination: {
      currentCount: 3,
      offset: null,
      limit: null,
      totalCount: 3,
    },
    results: [
      {
        updated_at: '2021-04-08T05:08:48.295Z',
        created_at: '2021-04-08T05:08:48.295Z',
        id: 365,
        name: 'TEST NEW INSERT',
        ship_party_type_id: 1,
        state_detail: null,
        city_detail: null,
        building_detail: 'Manila, Philippines',
        house_detail:
          '2nd Floor, Mary Bachrach Building, 25th corner A.C. Delgado Streets, Port Area',
        postal_zip_code: null,
        iso_country_code: null,
        telephone_office: '..',
        telephone_office_ext: null,
        fax: null,
        telephone_office_after_hours: null,
        telephone_office_after_hours_ext: null,
        doc_url: null,
        last_updated_by: '29',
        paris1_ref_id: 5181,
        approval_status: 'approved',
        ship_party_office_emails: [],
        ship_party_contact_person: [],
        ship_party_type: {
          updated_at: '2021-04-08T04:59:01.126Z',
          created_at: '2021-04-08T04:59:01.126Z',
          id: 1,
          name: 'Owner',
        },
        ship_party_owner: {
          updated_at: '2021-04-08T05:08:48.338Z',
          created_at: '2021-04-08T05:08:48.338Z',
          id: 365,
          ship_party_id: 365,
          is_zero_alcohal_policy: false,
        },
        ship_party_owner_registered: null,
        ship_party_manning_agency: null,
        fos_ref_id: null,
        short_code: null,
      },
      {
        updated_at: '2021-04-08T05:08:48.207Z',
        created_at: '2021-04-08T05:08:48.207Z',
        id: 364,
        name: 'Log-In Logistica',
        ship_party_type_id: 1,
        state_detail: null,
        city_detail: null,
        building_detail: 'Centro - Rio de Janeiro - RJ - 20021-130, Brazil',
        house_detail: 'Av. General Justo, 375 - Edificio Bay View - 6 andar',
        postal_zip_code: null,
        iso_country_code: null,
        telephone_office: '+21 2111 6651',
        telephone_office_ext: null,
        fax: null,
        telephone_office_after_hours: null,
        telephone_office_after_hours_ext: null,
        doc_url: null,
        last_updated_by: '29',
        paris1_ref_id: 5151,
        approval_status: 'approved',
        ship_party_office_emails: [],
        ship_party_contact_person: [],
        ship_party_type: {
          updated_at: '2021-04-08T04:59:01.126Z',
          created_at: '2021-04-08T04:59:01.126Z',
          id: 1,
          name: 'Owner',
        },
        ship_party_owner: {
          updated_at: '2021-04-08T05:08:48.251Z',
          created_at: '2021-04-08T05:08:48.251Z',
          id: 364,
          ship_party_id: 364,
          is_zero_alcohal_policy: false,
        },
        ship_party_owner_registered: null,
        ship_party_manning_agency: null,
        fos_ref_id: null,
        short_code: null,
      },
      {
        updated_at: '2021-04-08T05:08:48.029Z',
        created_at: '2021-04-08T05:08:48.029Z',
        id: 363,
        name: 'Zodiac Maritime Ltd',
        ship_party_type_id: 1,
        state_detail: null,
        city_detail: null,
        building_detail: null,
        house_detail: 'Portman House, 5th Floor, 2 Portman Street, London, W1H 6DU, United Kingdom',
        postal_zip_code: null,
        iso_country_code: null,
        telephone_office: '+44 (0)20 7333 2273',
        telephone_office_ext: null,
        fax: null,
        telephone_office_after_hours: null,
        telephone_office_after_hours_ext: null,
        doc_url: null,
        last_updated_by: '29',
        paris1_ref_id: 5091,
        approval_status: 'approved',
        ship_party_office_emails: [
          {
            updated_at: '2021-04-08T05:08:48.076Z',
            created_at: '2021-04-08T05:08:48.076Z',
            id: 382,
            ship_party_id: 363,
            office_email: '<EMAIL>',
          },
        ],
        ship_party_contact_person: [
          {
            updated_at: '2021-04-08T05:08:48.076Z',
            created_at: '2021-04-08T05:08:48.076Z',
            id: 289,
            ship_party_id: 363,
            contact_person_name:
              'Captain Gordon Lowe MNI, General Fleet Manager (Dry - Operations)',
            mobile_phone: '+44 (0)************',
            contact_person_email: null,
            auth_user_name: null,
          },
        ],
        ship_party_type: {
          updated_at: '2021-04-08T04:59:01.126Z',
          created_at: '2021-04-08T04:59:01.126Z',
          id: 1,
          name: 'Owner',
        },
        ship_party_owner: {
          updated_at: '2021-04-08T05:08:48.076Z',
          created_at: '2021-04-08T05:08:48.076Z',
          id: 363,
          ship_party_id: 363,
          is_zero_alcohal_policy: false,
        },
        ship_party_owner_registered: null,
        ship_party_manning_agency: null,
        fos_ref_id: null,
        short_code: null,
      },
    ],
  };
};
