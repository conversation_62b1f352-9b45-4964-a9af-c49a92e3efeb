export const modellingMockData = {
  pagination: {
    totalCount: 6353,
    count: 2,
    offset: '0',
    limit: '2',
    orderBy: 'seafarer_report_modeller.vessel_name asc',
  },
  summary: {
    planned_total_number: 9695,
    actual_total_number: 8955,
    planned_wages: 34565118.99,
    actual_wages: 34272839.0,
  },
  results: [
    {
      id: 1,
      copy_from_id: null,
      vessel_ownership_id: 1637,
      vessel_tech_group: 'Celsius Tech',
      vessel_name: 'Celsius Copenhagen (SHI Hull No. 2297)',
      vessel_ref_id: 5315,
      planned_number: 20,
      actual_number: 22,
      planned_wages: 999999,
      actual_wages: 999900,
      created_at: '2022-04-02T08:04:06.930Z',
      updated_at: '2022-04-02T08:04:06.930Z',
      created_by_hash: '9qX9VU1Fw/XpO69G0YgDKw==',
      last_updated_by_hash: '9qX9VU1Fw/XpO69G0YgDKw==',
    },
    {
      id: 2,
      copy_from_id: 1,
      vessel_ownership_id: 1394,
      vessel_tech_group: 'Tech T1',
      vessel_name: '<PERSON><PERSON>',
      vessel_ref_id: 5401,
      planned_number: 23,
      actual_number: 21,
      planned_wages: 888888,
      actual_wages: 888800,
      created_at: '2022-04-02T08:04:06.930Z',
      updated_at: '2022-04-02T08:04:06.930Z',
      created_by_hash: '9qX9VU1Fw/XpO69G0YgDKw==',
      last_updated_by_hash: '9qX9VU1Fw/XpO69G0YgDKw==',
    },
  ],
};
