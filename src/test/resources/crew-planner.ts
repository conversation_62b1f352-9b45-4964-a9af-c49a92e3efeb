export const ContractExpiry = {
  data: {
    pagination: {
      totalCount: 4,
      offset: '0',
      limit: '50',
    },
    results: [
      {
        id: 115448,
        seafarer_person_id: 115450,
        ref_id: 217619,
        rank_id: 41,
        is_only_worked_fml: false,
        seafarer_reporting_office: {
          value: 'Polaris Shipping Co.,Ltd',
          id: 88,
        },
        hkid: 127348,
        created_at: '2023-04-26T10:55:44.863Z',
        updated_at: '2023-05-18T05:25:45.527Z',
        office_id: 88,
        manning_agent_id: 88,
        not_to_be_employed: null,
        not_to_be_employed_reason: null,
        framo_experience: null,
        ice_conditions_experience: true,
        show_cargo_experience: null,
        show_fml_experience: null,
        cargo_experience: null,
        additional_experience: null,
        parent_hkid: null,
        is_parent: null,
        data_quality: 'data_invalid',
        with_fml_vessel_experience: true,
        'seafarer_person:seafarer_status_history.expected_contract_end_d':
          '2023-11-08T00:00:00.000Z',
        seafarer_person: {
          id: 115450,
          current_account_status: 'active',
          current_journey_status: 'signed_on',
          current_exam_status: null,
          first_name: 'JONGGIL',
          middle_name: null,
          last_name: 'SE *',
          date_of_birth: '1986-12-12T00:00:00.000Z',
          gender: 'male',
          place_of_birth: 'Korea',
          nearest_airport: null,
          smoking: null,
          vegetarian: null,
          country_of_birth_id: 123,
          nationality_id: 127,
          passports: [
            {
              number: 'M4 *',
              id: 523573,
            },
          ],
          seaman_books: [
            {
              number: 'MH *',
              id: 997331,
            },
            {
              number: 'BS * 02**',
              id: 997326,
            },
          ],
          nationality: {
            value: 'South Korean',
            id: 127,
          },
          country_of_birth: {
            value: 'Korea, Republic of',
            id: 123,
          },
          addresses: [
            {
              country_id: null,
              id: 1050397,
            },
          ],
          seafarer_status_history: [
            {
              id: 1860404,
              seafarer_journey_status: 'signed_on',
              is_current_status: true,
              rank_id: 41,
              vessel_name: 'Stellar Way',
              vessel_ref_id: 5370,
              status_date: '2023-05-12T00:00:00.000Z',
              vessel_ownership_id: 1225,
              vessel_id: 1360,
              sign_off_date: null,
              expected_contract_end_date: '2023-11-08T00:00:00.000Z',
              embarkation_port: 'KWANGYANG',
              repatriation_port: 'KWANGYANG',
              vessel_tech_group: 'Tech D3',
              vessel_type: 'Very Large Ore Carrier',
              replaced_by_id: null,
              paris1_ref_id: null,
              replaced_by: null,
              seafarer_rank: {
                id: 41,
                value: '2ND COOK',
                unit: '2/C',
                ref_id: 2001203,
                sortpriority: 310,
              },
            },
          ],
          bank_accounts: [
            {
              id: 376533,
              created_at: '2023-04-26T10:55:44.863Z',
              updated_at: '2023-04-27T00:22:44.633Z',
              seafarer_person_id: 115450,
              is_primary_payroll_account: true,
              seafarer_is_account_holder: true,
              relationship_with_beneficiary: null,
              account_holder_first_name: 'JONGGIL',
              account_holder_middle_name: '',
              account_holder_last_name: 'SEON',
              account_holder_date_of_birth: '1986-12-12T00:00:00.000Z',
              account_holder_gender: 'male',
              account_holder_nationality_id: null,
              account_holder_address_id: null,
              number: null,
              bank_name: null,
              bank_address_id: 811317,
              ifsc_number: null,
              swift_code: null,
              iban_number: null,
              cnaps: null,
              account_type: null,
              fcnr_months: null,
              account_holder_birth_place: null,
              account_holder_contact_1: null,
              account_holder_contact_2: null,
              intermediary_swift_code: null,
              intermediary_bank_name: null,
              intermediary_bank_address: null,
              intermediary_bank_account: null,
              special_remittence_instrcution: null,
              remarks: null,
              pay_mode: null,
              account_holder_address: null,
              account_holder_nationality: null,
              bank_address: {
                id: 811317,
                created_at: '2023-04-27T00:22:44.555Z',
                updated_at: '2023-04-27T00:22:44.555Z',
                postal_zip_code: null,
                country_id: null,
                address1: null,
                address2: null,
                address3: null,
                address4: null,
              },
              document: [],
            },
          ],
        },
        seafarer_manning_agent: {
          value: 'Polaris Shipping Co.,Ltd',
          id: 88,
        },
        seafarer_contact_log: [],
        experience_summary: {
          seafarer_id: 115448,
          duration_with_company: null,
          duration_on_all_vessel_type: '1532',
          duration_on_target_vessel: '398',
          duration_on_target_vessel_type: null,
          duration_in_target_rank: '581',
          target_vessel_type: 'Very Large Ore Carrier',
          target_rank: '2ND COOK',
          target_vessel_name: 'Stellar Way',
        },
        crew_planning: null,
        recommended_replacement: [],
      },
    ],
  },
};
export const AvailableSeafarer = {
  data: {
    pagination: {
      totalCount: 4,
      offset: '0',
      limit: '50',
    },
    results: [
      {
        id: 120499,
        seafarer_person_id: 120501,
        ref_id: 222764,
        rank_id: 7,
        is_only_worked_fml: false,
        seafarer_reporting_office: {
          value: 'Sinosin Marine Services Co., Ltd, Shanghai',
          id: 104,
        },
        rank__2nd_engineer: '449',
        hkid: 132450,
        created_at: '2023-12-14T08:29:26.895Z',
        updated_at: '2024-03-13T11:30:49.162Z',
        office_id: 104,
        manning_agent_id: 104,
        not_to_be_employed: null,
        not_to_be_employed_reason: null,
        framo_experience: null,
        ice_conditions_experience: null,
        show_cargo_experience: null,
        show_fml_experience: null,
        cargo_experience: null,
        additional_experience: null,
        parent_hkid: null,
        is_parent: null,
        data_quality: 'clean',
        with_fml_vessel_experience: true,
        'seafarer_contact_log.availability_date': null,
        seafarer_person: {
          id: 120501,
          current_account_status: 'active',
          current_journey_status: 'on_leave',
          current_exam_status: 'no_examination',
          first_name: 'XINGYAN',
          middle_name: null,
          last_name: 'CHwqwq',
          date_of_birth: '1987-12-03T00:00:00.000Z',
          created_at: '2023-12-14T08:29:26.839Z',
          gender: 'male',
          place_of_birth: 'SHANDONG',
          country_of_birth_id: 105,
          nationality_id: 44,
          passports: [
            {
              number: 'EFQWEWE',
              id: 528894,
            },
          ],
          seaman_books: [
            {
              number: 'EFQWWQ',
              id: 1009632,
            },
          ],
          nationality: {
            value: 'Chinese',
            id: 44,
          },
          country_of_birth: {
            value: 'India',
            id: 105,
          },
          addresses: [
            {
              id: 1054145,
              created_at: '2023-12-14T08:29:26.905Z',
              updated_at: '2024-02-21T02:29:41.113Z',
              seafarer_person_id: 120501,
              postal_zip_code: null,
              country_id: 44,
              state: 'SHANDONG',
              city: 'LIAOCHENG',
              building: null,
              other_address: 'No  06**, Ch** Vi**, Sh** To**, Ya** Co**',
            },
          ],
          seafarer_contacts: [
            {
              id: 1108600,
              created_at: '2023-12-14T08:29:26.895Z',
              updated_at: '2024-02-21T02:29:41.088Z',
              seafarer_person_id: 120501,
              contact: '8676786876876',
              contact_type: 'mobile_number',
            },
          ],
          seafarer_document: [],
        },
        seafarer_rank: {
          id: 7,
          value: '2ND ENGINEER',
          unit: '2/E',
          ref_id: 2000342,
          sortpriority: 22,
        },
        seafarer_manning_agent: {
          value: 'Sinosin Marine Services Co., Ltd, Shanghai',
          id: 104,
        },
        seafarer_contact_log: [],
        latest_experience: {
          seafarer_id: 120499,
          vessel_name: 'Ariana A',
          vessel_type: 'Container Vessel',
          deadweight_tonnage: 38121,
          deadweight_gross_registered_tonnage: 'D',
          engine_type: 'B&W',
          engine_sub_type: null,
          brake_horse_power: 29194,
          start_date: '2024-02-21T00:00:00.000Z',
          end_date: '2024-02-29T00:00:00.000Z',
          owner_name: 'Pavimar SA',
          vessel_ownership_id: 1816,
          vessel_tech_group: 'Tech D5',
          rank: {
            value: '2ND COOK',
            id: 41,
          },
        },
        crew_plan: {
          seafarer_person_id: 120501,
          seafarer_journey_status: 'recommended',
          vessel_name: 'Alkaios',
          vessel_ownership_id: 1761,
          vessel_ref_id: 5888,
        },
        experience_summary: {
          duration_with_company: 15,
          duration_on_all_vessel_type: 1118,
          duration_on_target_vessel_type: null,
          duration_in_target_rank: 449,
          target_rank: '2ND ENGINEER',
        },
        crew_planning: null,
      },
    ],
  },
};
