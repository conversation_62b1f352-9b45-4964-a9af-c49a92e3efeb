export const competencyCertificateData = () => {
    return [
      {
        id: 157,
        seafarer_person_id: 9,
        type: 'certificate_of_competency',
        ref_id: 5,
        doc_path: null,
        is_deleted: false,
        created_at: '2022-03-23T04:29:35.711Z',
        updated_at: '2022-08-16T06:46:54.125Z',
        created_by_hash: 'YsEI5tquUWISEK+GTbDw7xnmYvpS0eay4+V5fShJ9pk=',
        updated_by_hash: 'YsEI5tquUWISEK+GTbDw7xnmYvpS0eay4+V5fShJ9pk=',
        seafarer_doc_medical: null,
        seafarer_doc_verification: null,
        seafarer_doc_dce_verification: null,
        seafarer_doc_correspondence_details: null,
        seafarer_doc_visa: null,
        seafarer_doc_education: null,
        seafarer_doc_apprenticeship: null,
        seafarer_doc_training: null,
        seafarer_doc_pre_sea_training: null,
        seafarer_doc_stcw: null,
        seafarer_doc_certificate_of_competency: {
          id: 8,
          seafarer_document_id: 157,
          country_id: 174,
          coc_certificate_id: 4,
          certificate_no: 'OM 38**',
          date_of_issue: '1998-08-13T00:00:00.000Z',
          date_of_expiry: '1999-04-12T00:00:00.000Z',
          is_original: false,
          created_at: '2022-03-23T04:29:35.715Z',
          updated_at: '2022-03-23T04:29:35.715Z',
        },
        seafarer_doc_drug_alcohol_test: null,
        seafarer_doc_endorsement: null,
        seafarer_doc_indos: null,
        seafarer_doc_other_course: null,
        seafarer_doc_other_document: null,
        seafarer_doc_user_defined_document: null,
      },
    ];
  };