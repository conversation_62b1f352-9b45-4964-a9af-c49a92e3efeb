export const masterAppraisalQueryApiEmptyResponse = {
  pagination: {
    totalCount: 0,
    count: 0,
    offset: '0',
    limit: '1',
    orderBy: 'master_appraisal.updated_at desc',
  },
  results: [],
};

export const masterAppraisalQueryApiResponse = {
  pagination: {
    totalCount: 6353,
    count: 1,
    offset: '0',
    limit: '1',
    orderBy: 'supt_appraisal.updated_at desc',
  },
  results: [
    {
      id: 1,
      created_at: '2022-11-02T04:30:39.313Z',
      updated_at: '2022-11-02T04:30:39.313Z',
      ref_id: 1,
      rank_id: 1,
      rank: {
        id: 1,
        value: 'Master',
      },
      seafarer_id: 2,
      vessel_ownership_id: 122,
      vessel_name: 'Spar <PERSON>',
      master_comment: 'BLAH',
      head_comment: 'TEST',
      crew_comment: 'TEST',
      overall_grade: 20,
      master_appraisal_reason_id: null,
      master_appraisal_drinking_status_id: null,
      is_promotion_recommended: false,
      new_rank_id: null,
      promotion_requirement: '',
      training_needs: '',
      start_date: '2022-10-10',
      end_date: '2024-10-10',
      master_id: 222,
      head_id: 333,
      doc_path: 'BLHABLAHLBAH/BLAH',
      master_name: 'Deva',
      head_name: 'Prabhu',
      created_by_hash: null,
      updated_by_hash: null,
    },
  ],
};
