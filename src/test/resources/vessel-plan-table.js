export const mockedVesselPlanTableData = {
  id: 1,
  copy_from_id: null,
  vessel_ownership_id: 1637,
  vessel_tech_group: 'Celsius Tech',
  vessel_name: '<PERSON>lsius Copenhagen (SHI Hull No. 2297)',
  vessel_ref_id: 5315,
  planned_number: '5',
  planned_wages: '26250',
  actual_wages: '23885',
  actual_number: '4',
  created_at: '2022-04-02T08:04:06.930Z',
  updated_at: '2022-04-02T08:04:06.930Z',
  created_by_hash: '9qX9VU1Fw/XpO69G0YgDKw==',
  last_updated_by_hash: '9qX9VU1Fw/XpO69G0YgDKw==',
  seafarer_report_modeller_details: [
    {
      seafarer_report_modeller_id: 1,
      seafarer_id: 1,
      rank_id: 1,
      rank: {
        id: 1,
        value: 'MASTER',
        unit: 'MSTR',
        ref_id: 2000336,
        sortpriority: 1,
      },
      planned_number: 20,
      planned_wages: 13500,
      planned_nationality_id: 109,
      planned_nationality: {
        id: 109,
        alpha2_code: 'IN',
        alpha3_code: 'IND',
        value: 'Indian',
        ref_id: null,
      },
      actual_wages: 12200,
      actual_number: 22,
      actual_nationality_id: 109,
      actual_nationality: {
        id: 109,
        alpha2_code: 'IN',
        alpha3_code: 'IND',
        value: 'Indian',
        ref_id: null,
      },
      created_at: '2022-04-02T08:04:06.930Z',
      updated_at: '2022-04-02T08:04:06.930Z',
      created_by_hash: '9qX9VU1Fw/XpO69G0YgDKw==',
      last_updated_by_hash: '9qX9VU1Fw/XpO69G0YgDKw==',
    },
    {
      seafarer_report_modeller_id: 1,
      seafarer_id: 1,
      rank_id: 1,
      rank: {
        id: 1,
        value: 'MASTER',
        unit: 'MSTR',
        ref_id: 2000336,
        sortpriority: 1,
      },
      planned_number: '20',
      planned_wages: '13500',
      planned_nationality_id: 109,
      planned_nationality: {
        id: 109,
        alpha2_code: 'IN',
        alpha3_code: 'IND',
        value: 'Indian',
        ref_id: null,
      },
      actual_wages: '12200',
      actual_number: '22',
      actual_nationality_id: 109,
      actual_nationality: {
        id: 109,
        alpha2_code: 'IN',
        alpha3_code: 'IND',
        value: 'Indian',
        ref_id: null,
      },
      created_at: '2022-04-02T08:04:06.930Z',
      updated_at: '2022-04-02T08:04:06.930Z',
      created_by_hash: '9qX9VU1Fw/XpO69G0YgDKw==',
      last_updated_by_hash: '9qX9VU1Fw/XpO69G0YgDKw==',
    },
    {
      seafarer_report_modeller_id: 1,
      seafarer_id: 2,
      rank_id: 2,
      rank: {
        id: 2,
        value: 'CHIEF OFFICER',
        unit: 'C/O',
        ref_id: 2000337,
        sortpriority: 4,
      },
      planned_number: '1',
      planned_wages: '10250',
      planned_nationality_id: 109,
      planned_nationality: {
        id: 109,
        alpha2_code: 'IN',
        alpha3_code: 'IND',
        value: 'Indian',
        ref_id: null,
      },
      actual_nationality_id: 109,
      actual_nationality: {
        id: 109,
        alpha2_code: 'IN',
        alpha3_code: 'IND',
        value: 'Indian',
        ref_id: null,
      },
      actual_wages: '9300',
      actual_number: '1',
      created_at: '2022-04-02T08:04:06.930Z',
      updated_at: '2022-04-02T08:04:06.930Z',
      created_by_hash: '9qX9VU1Fw/XpO69G0YgDKw==',
      last_updated_by_hash: '9qX9VU1Fw/XpO69G0YgDKw==',
    },
    {
      seafarer_report_modeller_id: 1,
      seafarer_id: 3,
      rank_id: 15,
      rank: {
        id: 15,
        value: 'AB',
        unit: 'AB',
        ref_id: 2000351,
        sortpriority: 210,
      },
      planned_number: '3',
      planned_wages: '1300',
      planned_nationality_id: 109,
      planned_nationality: {
        id: 109,
        alpha2_code: 'IN',
        alpha3_code: 'IND',
        value: 'Indian',
        ref_id: null,
      },
      actual_wages: '1185',
      actual_number: '2',
      actual_nationality_id: 2,
      actual_nationality: {
        id: 23,
        alpha2_code: 'BD',
        alpha3_code: 'BGD',
        value: 'Bangladesh',
        ref_id: null,
      },
      created_at: '2022-04-02T08:04:06.930Z',
      updated_at: '2022-04-02T08:04:06.930Z',
      created_by_hash: '9qX9VU1Fw/XpO69G0YgDKw==',
      last_updated_by_hash: '9qX9VU1Fw/XpO69G0YgDKw==',
    },
    {
      seafarer_report_modeller_id: 1,
      seafarer_id: 4,
      rank_id: 15,
      rank: {
        id: 15,
        value: 'AB',
        unit: 'AB',
        ref_id: 2000351,
        sortpriority: 210,
      },
      planned_number: '3',
      planned_wages: '1300',
      planned_nationality_id: 109,
      planned_nationality: {
        id: 109,
        alpha2_code: 'IN',
        alpha3_code: 'IND',
        value: 'Indian',
        ref_id: null,
      },
      actual_wages: '1200',
      actual_number: '2',
      actual_nationality_id: 23,
      actual_nationality: {
        id: 23,
        alpha2_code: 'BD',
        alpha3_code: 'BGD',
        value: 'Bangladesh',
        ref_id: null,
      },
      created_at: '2022-04-02T08:04:06.930Z',
      updated_at: '2022-04-02T08:04:06.930Z',
      created_by_hash: '9qX9VU1Fw/XpO69G0YgDKw==',
      last_updated_by_hash: '9qX9VU1Fw/XpO69G0YgDKw==',
    },
  ],
};
