{"countries": [{"id": 1, "value": "Aruba", "alpha2_code": "AW", "alpha3_code": "ABW"}, {"id": 2, "value": "Afghanistan", "alpha2_code": "AF", "alpha3_code": "AFG"}, {"id": 3, "value": "Angola", "alpha2_code": "AO", "alpha3_code": "AGO"}, {"id": 4, "value": "<PERSON><PERSON><PERSON>", "alpha2_code": "AI", "alpha3_code": "AIA"}, {"id": 5, "value": "Åland Islands", "alpha2_code": "AX", "alpha3_code": "ALA"}, {"id": 6, "value": "Albania", "alpha2_code": "AL", "alpha3_code": "ALB"}, {"id": 7, "value": "Andorra", "alpha2_code": "AD", "alpha3_code": "AND"}, {"id": 8, "value": "United Arab Emirates", "alpha2_code": "AE", "alpha3_code": "ARE"}, {"id": 9, "value": "Argentina", "alpha2_code": "AR", "alpha3_code": "ARG"}, {"id": 10, "value": "Armenia", "alpha2_code": "AM", "alpha3_code": "ARM"}, {"id": 11, "value": "American Samoa", "alpha2_code": "AS", "alpha3_code": "ASM"}, {"id": 12, "value": "Antarctica", "alpha2_code": "AQ", "alpha3_code": "ATA"}, {"id": 13, "value": "French Southern Territories", "alpha2_code": "TF", "alpha3_code": "ATF"}, {"id": 14, "value": "Antigua and Barbuda", "alpha2_code": "AG", "alpha3_code": "ATG"}, {"id": 15, "value": "Australia", "alpha2_code": "AU", "alpha3_code": "AUS"}, {"id": 16, "value": "Austria", "alpha2_code": "AT", "alpha3_code": "AUT"}, {"id": 17, "value": "Azerbaijan", "alpha2_code": "AZ", "alpha3_code": "AZE"}, {"id": 18, "value": "Burundi", "alpha2_code": "BI", "alpha3_code": "BDI"}, {"id": 19, "value": "Belgium", "alpha2_code": "BE", "alpha3_code": "BEL"}, {"id": 20, "value": "Benin", "alpha2_code": "BJ", "alpha3_code": "BEN"}, {"id": 21, "value": "Bonaire, Sint Eustatius and Saba", "alpha2_code": "BQ", "alpha3_code": "BES"}, {"id": 22, "value": "Burkina Faso", "alpha2_code": "BF", "alpha3_code": "BFA"}, {"id": 23, "value": "Bangladesh", "alpha2_code": "BD", "alpha3_code": "BGD"}, {"id": 24, "value": "Bulgaria", "alpha2_code": "BG", "alpha3_code": "BGR"}, {"id": 25, "value": "Bahrain", "alpha2_code": "BH", "alpha3_code": "BHR"}, {"id": 26, "value": "Bahamas", "alpha2_code": "BS", "alpha3_code": "BHS"}, {"id": 27, "value": "Bosnia and Herzegovina", "alpha2_code": "BA", "alpha3_code": "BIH"}, {"id": 28, "value": "<PERSON>", "alpha2_code": "BL", "alpha3_code": "BLM"}, {"id": 29, "value": "Belarus", "alpha2_code": "BY", "alpha3_code": "BLR"}, {"id": 30, "value": "Belize", "alpha2_code": "BZ", "alpha3_code": "BLZ"}, {"id": 31, "value": "Bermuda", "alpha2_code": "BM", "alpha3_code": "BMU"}, {"id": 32, "value": "Bolivia, Plurinational State of", "alpha2_code": "BO", "alpha3_code": "BOL"}, {"id": 33, "value": "Brazil", "alpha2_code": "BR", "alpha3_code": "BRA"}, {"id": 34, "value": "Barbados", "alpha2_code": "BB", "alpha3_code": "BRB"}, {"id": 35, "value": "Brunei Darussalam", "alpha2_code": "BN", "alpha3_code": "BRN"}, {"id": 36, "value": "Bhutan", "alpha2_code": "BT", "alpha3_code": "BTN"}, {"id": 37, "value": "Bouvet Island", "alpha2_code": "BV", "alpha3_code": "BVT"}, {"id": 38, "value": "Botswana", "alpha2_code": "BW", "alpha3_code": "BWA"}, {"id": 39, "value": "Central African Republic", "alpha2_code": "CF", "alpha3_code": "CAF"}, {"id": 40, "value": "Canada", "alpha2_code": "CA", "alpha3_code": "CAN"}, {"id": 41, "value": "Cocos (Keeling) Islands", "alpha2_code": "CC", "alpha3_code": "CCK"}, {"id": 42, "value": "Switzerland", "alpha2_code": "CH", "alpha3_code": "CHE"}, {"id": 43, "value": "Chile", "alpha2_code": "CL", "alpha3_code": "CHL"}, {"id": 44, "value": "China", "alpha2_code": "CN", "alpha3_code": "CHN"}, {"id": 45, "value": "Côte d'Ivoire", "alpha2_code": "CI", "alpha3_code": "CIV"}, {"id": 46, "value": "Cameroon", "alpha2_code": "CM", "alpha3_code": "CMR"}, {"id": 47, "value": "Congo, the Democratic Republic of the", "alpha2_code": "CD", "alpha3_code": "COD"}, {"id": 48, "value": "Congo", "alpha2_code": "CG", "alpha3_code": "COG"}, {"id": 49, "value": "Cook Islands", "alpha2_code": "CK", "alpha3_code": "COK"}, {"id": 50, "value": "Colombia", "alpha2_code": "CO", "alpha3_code": "COL"}, {"id": 51, "value": "Comoros", "alpha2_code": "KM", "alpha3_code": "COM"}, {"id": 52, "value": "Cape Verde", "alpha2_code": "CV", "alpha3_code": "CPV"}, {"id": 53, "value": "Costa Rica", "alpha2_code": "CR", "alpha3_code": "CRI"}, {"id": 54, "value": "Cuba", "alpha2_code": "CU", "alpha3_code": "CUB"}, {"id": 55, "value": "Curaçao", "alpha2_code": "CW", "alpha3_code": "CUW"}, {"id": 56, "value": "Christmas Island", "alpha2_code": "CX", "alpha3_code": "CXR"}, {"id": 57, "value": "Cayman Islands", "alpha2_code": "KY", "alpha3_code": "CYM"}, {"id": 58, "value": "Cyprus", "alpha2_code": "CY", "alpha3_code": "CYP"}, {"id": 59, "value": "Czech Republic", "alpha2_code": "CZ", "alpha3_code": "CZE"}, {"id": 60, "value": "Germany", "alpha2_code": "DE", "alpha3_code": "DEU"}, {"id": 61, "value": "Djibouti", "alpha2_code": "DJ", "alpha3_code": "DJI"}, {"id": 62, "value": "Dominica", "alpha2_code": "DM", "alpha3_code": "DMA"}, {"id": 63, "value": "Denmark", "alpha2_code": "DK", "alpha3_code": "DNK"}, {"id": 64, "value": "Dominican Republic", "alpha2_code": "DO", "alpha3_code": "DOM"}, {"id": 65, "value": "Algeria", "alpha2_code": "DZ", "alpha3_code": "DZA"}, {"id": 66, "value": "Ecuador", "alpha2_code": "EC", "alpha3_code": "ECU"}, {"id": 67, "value": "Egypt", "alpha2_code": "EG", "alpha3_code": "EGY"}, {"id": 68, "value": "Eritrea", "alpha2_code": "ER", "alpha3_code": "ERI"}, {"id": 69, "value": "Western Sahara", "alpha2_code": "EH", "alpha3_code": "ESH"}, {"id": 70, "value": "Spain", "alpha2_code": "ES", "alpha3_code": "ESP"}, {"id": 71, "value": "Estonia", "alpha2_code": "EE", "alpha3_code": "EST"}, {"id": 72, "value": "Ethiopia", "alpha2_code": "ET", "alpha3_code": "ETH"}, {"id": 73, "value": "Finland", "alpha2_code": "FI", "alpha3_code": "FIN"}, {"id": 74, "value": "Fiji", "alpha2_code": "FJ", "alpha3_code": "FJI"}, {"id": 75, "value": "Falkland Islands (Malvinas)", "alpha2_code": "FK", "alpha3_code": "FLK"}, {"id": 76, "value": "France", "alpha2_code": "FR", "alpha3_code": "FRA"}, {"id": 77, "value": "Faroe Islands", "alpha2_code": "FO", "alpha3_code": "FRO"}, {"id": 78, "value": "Micronesia, Federated States of", "alpha2_code": "FM", "alpha3_code": "FSM"}, {"id": 79, "value": "Gabon", "alpha2_code": "GA", "alpha3_code": "GAB"}, {"id": 80, "value": "United Kingdom", "alpha2_code": "GB", "alpha3_code": "GBR"}, {"id": 81, "value": "Georgia", "alpha2_code": "GE", "alpha3_code": "GEO"}, {"id": 82, "value": "Guernsey", "alpha2_code": "GG", "alpha3_code": "GGY"}, {"id": 83, "value": "Ghana", "alpha2_code": "GH", "alpha3_code": "GHA"}, {"id": 84, "value": "Gibraltar", "alpha2_code": "GI", "alpha3_code": "GIB"}, {"id": 85, "value": "Guinea", "alpha2_code": "GN", "alpha3_code": "GIN"}, {"id": 86, "value": "Guadeloupe", "alpha2_code": "GP", "alpha3_code": "GLP"}, {"id": 87, "value": "Gambia", "alpha2_code": "GM", "alpha3_code": "GMB"}, {"id": 88, "value": "Guinea-Bissau", "alpha2_code": "GW", "alpha3_code": "GNB"}, {"id": 89, "value": "Equatorial Guinea", "alpha2_code": "GQ", "alpha3_code": "GNQ"}, {"id": 90, "value": "Greece", "alpha2_code": "GR", "alpha3_code": "GRC"}, {"id": 91, "value": "Grenada", "alpha2_code": "GD", "alpha3_code": "GRD"}, {"id": 92, "value": "Greenland", "alpha2_code": "GL", "alpha3_code": "GRL"}, {"id": 93, "value": "Guatemala", "alpha2_code": "GT", "alpha3_code": "GTM"}, {"id": 94, "value": "French Guiana", "alpha2_code": "GF", "alpha3_code": "GUF"}, {"id": 95, "value": "Guam", "alpha2_code": "GU", "alpha3_code": "GUM"}, {"id": 96, "value": "Guyana", "alpha2_code": "GY", "alpha3_code": "GUY"}, {"id": 97, "value": "Hong Kong", "alpha2_code": "HK", "alpha3_code": "HKG"}, {"id": 98, "value": "Heard Island and McDonald Mcdonald Islands", "alpha2_code": "HM", "alpha3_code": "HMD"}, {"id": 99, "value": "Honduras", "alpha2_code": "HN", "alpha3_code": "HND"}, {"id": 100, "value": "Croatia", "alpha2_code": "HR", "alpha3_code": "HRV"}, {"id": 101, "value": "Haiti", "alpha2_code": "HT", "alpha3_code": "HTI"}, {"id": 102, "value": "Hungary", "alpha2_code": "HU", "alpha3_code": "HUN"}, {"id": 103, "value": "Indonesia", "alpha2_code": "ID", "alpha3_code": "IDN"}, {"id": 104, "value": "Isle of Man", "alpha2_code": "IM", "alpha3_code": "IMN"}, {"id": 105, "value": "India", "alpha2_code": "IN", "alpha3_code": "IND"}, {"id": 106, "value": "British Indian Ocean Territory", "alpha2_code": "IO", "alpha3_code": "IOT"}, {"id": 107, "value": "Ireland", "alpha2_code": "IE", "alpha3_code": "IRL"}, {"id": 108, "value": "Iran, Islamic Republic of", "alpha2_code": "IR", "alpha3_code": "IRN"}, {"id": 109, "value": "Iraq", "alpha2_code": "IQ", "alpha3_code": "IRQ"}, {"id": 110, "value": "Iceland", "alpha2_code": "IS", "alpha3_code": "ISL"}, {"id": 111, "value": "Israel", "alpha2_code": "IL", "alpha3_code": "ISR"}, {"id": 112, "value": "Italy", "alpha2_code": "IT", "alpha3_code": "ITA"}, {"id": 113, "value": "Jamaica", "alpha2_code": "JM", "alpha3_code": "JAM"}, {"id": 114, "value": "Jersey", "alpha2_code": "JE", "alpha3_code": "JEY"}, {"id": 115, "value": "Jordan", "alpha2_code": "JO", "alpha3_code": "JOR"}, {"id": 116, "value": "Japan", "alpha2_code": "JP", "alpha3_code": "JPN"}, {"id": 117, "value": "Kazakhstan", "alpha2_code": "KZ", "alpha3_code": "KAZ"}, {"id": 118, "value": "Kenya", "alpha2_code": "KE", "alpha3_code": "KEN"}, {"id": 119, "value": "Kyrgyzstan", "alpha2_code": "KG", "alpha3_code": "KGZ"}, {"id": 120, "value": "Cambodia", "alpha2_code": "KH", "alpha3_code": "KHM"}, {"id": 121, "value": "Kiribati", "alpha2_code": "KI", "alpha3_code": "KIR"}, {"id": 122, "value": "Saint Kitts and Nevis", "alpha2_code": "KN", "alpha3_code": "KNA"}, {"id": 123, "value": "Korea, Republic of", "alpha2_code": "KR", "alpha3_code": "KOR"}, {"id": 124, "value": "Kuwait", "alpha2_code": "KW", "alpha3_code": "KWT"}, {"id": 125, "value": "Lao People's Democratic Republic", "alpha2_code": "LA", "alpha3_code": "LAO"}, {"id": 126, "value": "Lebanon", "alpha2_code": "LB", "alpha3_code": "LBN"}, {"id": 127, "value": "Liberia", "alpha2_code": "LR", "alpha3_code": "LBR"}, {"id": 128, "value": "Libya", "alpha2_code": "LY", "alpha3_code": "LBY"}, {"id": 129, "value": "Saint Lucia", "alpha2_code": "LC", "alpha3_code": "LCA"}, {"id": 130, "value": "Liechtenstein", "alpha2_code": "LI", "alpha3_code": "LIE"}, {"id": 131, "value": "Sri Lanka", "alpha2_code": "LK", "alpha3_code": "LKA"}, {"id": 132, "value": "Lesotho", "alpha2_code": "LS", "alpha3_code": "LSO"}, {"id": 133, "value": "Lithuania", "alpha2_code": "LT", "alpha3_code": "LTU"}, {"id": 134, "value": "Luxembourg", "alpha2_code": "LU", "alpha3_code": "LUX"}, {"id": 135, "value": "Latvia", "alpha2_code": "LV", "alpha3_code": "LVA"}, {"id": 136, "value": "Macao", "alpha2_code": "MO", "alpha3_code": "MAC"}, {"id": 137, "value": "<PERSON> (French part)", "alpha2_code": "MF", "alpha3_code": "MAF"}, {"id": 138, "value": "Morocco", "alpha2_code": "MA", "alpha3_code": "MAR"}, {"id": 139, "value": "Monaco", "alpha2_code": "MC", "alpha3_code": "MCO"}, {"id": 140, "value": "Moldova, Republic of", "alpha2_code": "MD", "alpha3_code": "MDA"}, {"id": 141, "value": "Madagascar", "alpha2_code": "MG", "alpha3_code": "MDG"}, {"id": 142, "value": "Maldives", "alpha2_code": "MV", "alpha3_code": "MDV"}, {"id": 143, "value": "Mexico", "alpha2_code": "MX", "alpha3_code": "MEX"}, {"id": 144, "value": "Marshall Islands", "alpha2_code": "MH", "alpha3_code": "MHL"}, {"id": 145, "value": "North Macedonia", "alpha2_code": "MK", "alpha3_code": "MKD"}, {"id": 146, "value": "Mali", "alpha2_code": "ML", "alpha3_code": "MLI"}, {"id": 147, "value": "Malta", "alpha2_code": "MT", "alpha3_code": "MLT"}, {"id": 148, "value": "Myanmar", "alpha2_code": "MM", "alpha3_code": "MMR"}, {"id": 149, "value": "Montenegro", "alpha2_code": "ME", "alpha3_code": "MNE"}, {"id": 150, "value": "Mongolia", "alpha2_code": "MN", "alpha3_code": "MNG"}, {"id": 151, "value": "Northern Mariana Islands", "alpha2_code": "MP", "alpha3_code": "MNP"}, {"id": 152, "value": "Mozambique", "alpha2_code": "MZ", "alpha3_code": "MOZ"}, {"id": 153, "value": "Mauritania", "alpha2_code": "MR", "alpha3_code": "MRT"}, {"id": 154, "value": "Montserrat", "alpha2_code": "MS", "alpha3_code": "MSR"}, {"id": 155, "value": "Martinique", "alpha2_code": "MQ", "alpha3_code": "MTQ"}, {"id": 156, "value": "Mauritius", "alpha2_code": "MU", "alpha3_code": "MUS"}, {"id": 157, "value": "Malawi", "alpha2_code": "MW", "alpha3_code": "MWI"}, {"id": 158, "value": "Malaysia", "alpha2_code": "MY", "alpha3_code": "MYS"}, {"id": 159, "value": "Mayotte", "alpha2_code": "YT", "alpha3_code": "MYT"}, {"id": 160, "value": "Namibia", "alpha2_code": "NA", "alpha3_code": "NAM"}, {"id": 161, "value": "New Caledonia", "alpha2_code": "NC", "alpha3_code": "NCL"}, {"id": 162, "value": "Niger", "alpha2_code": "NE", "alpha3_code": "NER"}, {"id": 163, "value": "Norfolk Island", "alpha2_code": "NF", "alpha3_code": "NFK"}, {"id": 164, "value": "Nigeria", "alpha2_code": "NG", "alpha3_code": "NGA"}, {"id": 165, "value": "Nicaragua", "alpha2_code": "NI", "alpha3_code": "NIC"}, {"id": 166, "value": "Niue", "alpha2_code": "NU", "alpha3_code": "NIU"}, {"id": 167, "value": "Netherlands", "alpha2_code": "NL", "alpha3_code": "NLD"}, {"id": 168, "value": "Norway", "alpha2_code": "NO", "alpha3_code": "NOR"}, {"id": 169, "value": "Nepal", "alpha2_code": "NP", "alpha3_code": "NPL"}, {"id": 170, "value": "Nauru", "alpha2_code": "NR", "alpha3_code": "NRU"}, {"id": 171, "value": "New Zealand", "alpha2_code": "NZ", "alpha3_code": "NZL"}, {"id": 172, "value": "Oman", "alpha2_code": "OM", "alpha3_code": "OMN"}, {"id": 173, "value": "Pakistan", "alpha2_code": "PK", "alpha3_code": "PAK"}, {"id": 174, "value": "Panama", "alpha2_code": "PA", "alpha3_code": "PAN"}, {"id": 175, "value": "Pitcairn", "alpha2_code": "PN", "alpha3_code": "PCN"}, {"id": 176, "value": "Peru", "alpha2_code": "PE", "alpha3_code": "PER"}, {"id": 177, "value": "Philippines", "alpha2_code": "PH", "alpha3_code": "PHL"}, {"id": 178, "value": "<PERSON><PERSON>", "alpha2_code": "PW", "alpha3_code": "PLW"}, {"id": 179, "value": "Papua New Guinea", "alpha2_code": "PG", "alpha3_code": "PNG"}, {"id": 180, "value": "Poland", "alpha2_code": "PL", "alpha3_code": "POL"}, {"id": 181, "value": "Puerto Rico", "alpha2_code": "PR", "alpha3_code": "PRI"}, {"id": 182, "value": "Korea, Democratic People's Republic of", "alpha2_code": "KP", "alpha3_code": "PRK"}, {"id": 183, "value": "Portugal", "alpha2_code": "PT", "alpha3_code": "PRT"}, {"id": 184, "value": "Paraguay", "alpha2_code": "PY", "alpha3_code": "PRY"}, {"id": 185, "value": "Palestine, State of", "alpha2_code": "PS", "alpha3_code": "PSE"}, {"id": 186, "value": "French Polynesia", "alpha2_code": "PF", "alpha3_code": "PYF"}, {"id": 187, "value": "Qatar", "alpha2_code": "QA", "alpha3_code": "QAT"}, {"id": 188, "value": "Réunion", "alpha2_code": "RE", "alpha3_code": "REU"}, {"id": 189, "value": "Romania", "alpha2_code": "RO", "alpha3_code": "ROU"}, {"id": 190, "value": "Russian Federation", "alpha2_code": "RU", "alpha3_code": "RUS"}, {"id": 191, "value": "Rwanda", "alpha2_code": "RW", "alpha3_code": "RWA"}, {"id": 192, "value": "Saudi Arabia", "alpha2_code": "SA", "alpha3_code": "SAU"}, {"id": 193, "value": "Sudan", "alpha2_code": "SD", "alpha3_code": "SDN"}, {"id": 194, "value": "Senegal", "alpha2_code": "SN", "alpha3_code": "SEN"}, {"id": 195, "value": "Singapore", "alpha2_code": "SG", "alpha3_code": "SGP"}, {"id": 196, "value": "South Georgia and the South Sandwich Islands", "alpha2_code": "GS", "alpha3_code": "SGS"}, {"id": 197, "value": "Saint Helena, Ascension and Tristan <PERSON>ha", "alpha2_code": "SH", "alpha3_code": "SHN"}, {"id": 198, "value": "Svalbard and <PERSON>", "alpha2_code": "SJ", "alpha3_code": "SJM"}, {"id": 199, "value": "Solomon Islands", "alpha2_code": "SB", "alpha3_code": "SLB"}, {"id": 200, "value": "Sierra Leone", "alpha2_code": "SL", "alpha3_code": "SLE"}, {"id": 201, "value": "El Salvador", "alpha2_code": "SV", "alpha3_code": "SLV"}, {"id": 202, "value": "San Marino", "alpha2_code": "SM", "alpha3_code": "SMR"}, {"id": 203, "value": "Somalia", "alpha2_code": "SO", "alpha3_code": "SOM"}, {"id": 204, "value": "Saint Pierre and Miquelon", "alpha2_code": "PM", "alpha3_code": "SPM"}, {"id": 205, "value": "Serbia", "alpha2_code": "RS", "alpha3_code": "SRB"}, {"id": 206, "value": "South Sudan", "alpha2_code": "SS", "alpha3_code": "SSD"}, {"id": 207, "value": "Sao Tome and Principe", "alpha2_code": "ST", "alpha3_code": "STP"}, {"id": 208, "value": "Suriname", "alpha2_code": "SR", "alpha3_code": "SUR"}, {"id": 209, "value": "Slovakia", "alpha2_code": "SK", "alpha3_code": "SVK"}, {"id": 210, "value": "Slovenia", "alpha2_code": "SI", "alpha3_code": "SVN"}, {"id": 211, "value": "Sweden", "alpha2_code": "SE", "alpha3_code": "SWE"}, {"id": 212, "value": "<PERSON><PERSON><PERSON><PERSON>", "alpha2_code": "SZ", "alpha3_code": "SWZ"}, {"id": 213, "value": "<PERSON><PERSON> Ma<PERSON>n (Dutch part)", "alpha2_code": "SX", "alpha3_code": "SXM"}, {"id": 214, "value": "Seychelles", "alpha2_code": "SC", "alpha3_code": "SYC"}, {"id": 215, "value": "Syrian Arab Republic", "alpha2_code": "SY", "alpha3_code": "SYR"}, {"id": 216, "value": "Turks and Caicos Islands", "alpha2_code": "TC", "alpha3_code": "TCA"}, {"id": 217, "value": "Chad", "alpha2_code": "TD", "alpha3_code": "TCD"}, {"id": 218, "value": "Togo", "alpha2_code": "TG", "alpha3_code": "TGO"}, {"id": 219, "value": "Thailand", "alpha2_code": "TH", "alpha3_code": "THA"}, {"id": 220, "value": "Tajikistan", "alpha2_code": "TJ", "alpha3_code": "TJK"}, {"id": 221, "value": "Tokelau", "alpha2_code": "TK", "alpha3_code": "TKL"}, {"id": 222, "value": "Turkmenistan", "alpha2_code": "TM", "alpha3_code": "TKM"}, {"id": 223, "value": "Timor-Leste", "alpha2_code": "TL", "alpha3_code": "TLS"}, {"id": 224, "value": "Tonga", "alpha2_code": "TO", "alpha3_code": "TON"}, {"id": 225, "value": "Trinidad and Tobago", "alpha2_code": "TT", "alpha3_code": "TTO"}, {"id": 226, "value": "Tunisia", "alpha2_code": "TN", "alpha3_code": "TUN"}, {"id": 227, "value": "Turkey", "alpha2_code": "TR", "alpha3_code": "TUR"}, {"id": 228, "value": "Tuvalu", "alpha2_code": "TV", "alpha3_code": "TUV"}, {"id": 229, "value": "Taiwan, Province of China", "alpha2_code": "TW", "alpha3_code": "TWN"}, {"id": 230, "value": "Tanzania, United Republic of", "alpha2_code": "TZ", "alpha3_code": "TZA"}, {"id": 231, "value": "Uganda", "alpha2_code": "UG", "alpha3_code": "UGA"}, {"id": 232, "value": "Ukraine", "alpha2_code": "UA", "alpha3_code": "UKR"}, {"id": 233, "value": "United States Minor Outlying Islands", "alpha2_code": "UM", "alpha3_code": "UMI"}, {"id": 234, "value": "Uruguay", "alpha2_code": "UY", "alpha3_code": "URY"}, {"id": 235, "value": "United States", "alpha2_code": "US", "alpha3_code": "USA"}, {"id": 236, "value": "Uzbekistan", "alpha2_code": "UZ", "alpha3_code": "UZB"}, {"id": 237, "value": "Holy See (Vatican City State)", "alpha2_code": "VA", "alpha3_code": "VAT"}, {"id": 238, "value": "Saint Vincent and the Grenadines", "alpha2_code": "VC", "alpha3_code": "VCT"}, {"id": 239, "value": "Venezuela, Bolivarian Republic of", "alpha2_code": "VE", "alpha3_code": "VEN"}, {"id": 240, "value": "Virgin Islands, British", "alpha2_code": "VG", "alpha3_code": "VGB"}, {"id": 241, "value": "Virgin Islands, U.S.", "alpha2_code": "VI", "alpha3_code": "VIR"}, {"id": 242, "value": "Viet Nam", "alpha2_code": "VN", "alpha3_code": "VNM"}, {"id": 243, "value": "Vanuatu", "alpha2_code": "VU", "alpha3_code": "VUT"}, {"id": 244, "value": "Wallis and Futuna", "alpha2_code": "WF", "alpha3_code": "WLF"}, {"id": 245, "value": "Samoa", "alpha2_code": "WS", "alpha3_code": "WSM"}, {"id": 246, "value": "Yemen", "alpha2_code": "YE", "alpha3_code": "YEM"}, {"id": 247, "value": "South Africa", "alpha2_code": "ZA", "alpha3_code": "ZAF"}, {"id": 248, "value": "Zambia", "alpha2_code": "ZM", "alpha3_code": "ZMB"}, {"id": 249, "value": "Zimbabwe", "alpha2_code": "ZW", "alpha3_code": "ZWE"}, {"id": 250, "value": "Unknown", "alpha2_code": null, "alpha3_code": "ZZZ"}], "nationalities": [{"id": 1, "value": "Aruban", "alpha2_code": "AW", "alpha3_code": "ABW"}, {"id": 2, "value": "Afghan", "alpha2_code": "AF", "alpha3_code": "AFG"}, {"id": 3, "value": "Angolan", "alpha2_code": "AO", "alpha3_code": "AGO"}, {"id": 4, "value": "<PERSON><PERSON><PERSON>", "alpha2_code": "AI", "alpha3_code": "AIA"}, {"id": 5, "value": "Åland Island", "alpha2_code": "AX", "alpha3_code": "ALA"}, {"id": 6, "value": "Albanian", "alpha2_code": "AL", "alpha3_code": "ALB"}, {"id": 7, "value": "Andorran", "alpha2_code": "AD", "alpha3_code": "AND"}, {"id": 8, "value": "Emirati, Emirian, Emiri", "alpha2_code": "AE", "alpha3_code": "ARE"}, {"id": 9, "value": "Argentine", "alpha2_code": "AR", "alpha3_code": "ARG"}, {"id": 10, "value": "Armenian", "alpha2_code": "AM", "alpha3_code": "ARM"}, {"id": 11, "value": "American Samoan", "alpha2_code": "AS", "alpha3_code": "ASM"}, {"id": 12, "value": "Antarctic", "alpha2_code": "AQ", "alpha3_code": "ATA"}, {"id": 13, "value": "French Southern Territories", "alpha2_code": "TF", "alpha3_code": "ATF"}, {"id": 14, "value": "Antiguan or Barbudan", "alpha2_code": "AG", "alpha3_code": "ATG"}, {"id": 15, "value": "Australian", "alpha2_code": "AU", "alpha3_code": "AUS"}, {"id": 16, "value": "Austrian", "alpha2_code": "AT", "alpha3_code": "AUT"}, {"id": 17, "value": "Azerbaijani, Azeri", "alpha2_code": "AZ", "alpha3_code": "AZE"}, {"id": 18, "value": "Burundian", "alpha2_code": "BI", "alpha3_code": "BDI"}, {"id": 19, "value": "Belgian", "alpha2_code": "BE", "alpha3_code": "BEL"}, {"id": 20, "value": "<PERSON><PERSON>, Beninois", "alpha2_code": "BJ", "alpha3_code": "BEN"}, {"id": 21, "value": "Bonaire", "alpha2_code": "BQ", "alpha3_code": "BES"}, {"id": 22, "value": "Burkinabé", "alpha2_code": "BF", "alpha3_code": "BFA"}, {"id": 23, "value": "Bangladeshi", "alpha2_code": "BD", "alpha3_code": "BGD"}, {"id": 24, "value": "Bulgarian", "alpha2_code": "BG", "alpha3_code": "BGR"}, {"id": 25, "value": "Bahraini", "alpha2_code": "BH", "alpha3_code": "BHR"}, {"id": 26, "value": "<PERSON><PERSON><PERSON>", "alpha2_code": "BS", "alpha3_code": "BHS"}, {"id": 27, "value": "Bosnian or Herzegovinian", "alpha2_code": "BA", "alpha3_code": "BIH"}, {"id": 28, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "alpha2_code": "BL", "alpha3_code": "BLM"}, {"id": 29, "value": "Belarusian", "alpha2_code": "BY", "alpha3_code": "BLR"}, {"id": 30, "value": "Belizean", "alpha2_code": "BZ", "alpha3_code": "BLZ"}, {"id": 31, "value": "<PERSON><PERSON><PERSON>an, Bermudan", "alpha2_code": "BM", "alpha3_code": "BMU"}, {"id": 32, "value": "Bolivian", "alpha2_code": "BO", "alpha3_code": "BOL"}, {"id": 33, "value": "Brazilian", "alpha2_code": "BR", "alpha3_code": "BRA"}, {"id": 34, "value": "Barbadian", "alpha2_code": "BB", "alpha3_code": "BRB"}, {"id": 35, "value": "Bruneian", "alpha2_code": "BN", "alpha3_code": "BRN"}, {"id": 36, "value": "Bhutanese", "alpha2_code": "BT", "alpha3_code": "BTN"}, {"id": 37, "value": "Bouvet Island", "alpha2_code": "BV", "alpha3_code": "BVT"}, {"id": 38, "value": "Motswana, Botswanan", "alpha2_code": "BW", "alpha3_code": "BWA"}, {"id": 39, "value": "Central African", "alpha2_code": "CF", "alpha3_code": "CAF"}, {"id": 40, "value": "Canadian", "alpha2_code": "CA", "alpha3_code": "CAN"}, {"id": 41, "value": "Cocos Island", "alpha2_code": "CC", "alpha3_code": "CCK"}, {"id": 42, "value": "Swiss", "alpha2_code": "CH", "alpha3_code": "CHE"}, {"id": 43, "value": "Chilean", "alpha2_code": "CL", "alpha3_code": "CHL"}, {"id": 44, "value": "Chinese", "alpha2_code": "CN", "alpha3_code": "CHN"}, {"id": 45, "value": "Ivorian", "alpha2_code": "CI", "alpha3_code": "CIV"}, {"id": 46, "value": "Cameroonian", "alpha2_code": "CM", "alpha3_code": "CMR"}, {"id": 47, "value": "Congolese", "alpha2_code": "CD", "alpha3_code": "COD"}, {"id": 48, "value": "Congolese", "alpha2_code": "CG", "alpha3_code": "COG"}, {"id": 49, "value": "Cook Island", "alpha2_code": "CK", "alpha3_code": "COK"}, {"id": 50, "value": "Colombian", "alpha2_code": "CO", "alpha3_code": "COL"}, {"id": 51, "value": "<PERSON><PERSON>, Comorian", "alpha2_code": "KM", "alpha3_code": "COM"}, {"id": 52, "value": "Cabo Verdean", "alpha2_code": "CV", "alpha3_code": "CPV"}, {"id": 53, "value": "Costa Rican", "alpha2_code": "CR", "alpha3_code": "CRI"}, {"id": 54, "value": "Cuban", "alpha2_code": "CU", "alpha3_code": "CUB"}, {"id": 55, "value": "Curaçaoan", "alpha2_code": "CW", "alpha3_code": "CUW"}, {"id": 56, "value": "Christmas Island", "alpha2_code": "CX", "alpha3_code": "CXR"}, {"id": 57, "value": "Caymanian", "alpha2_code": "KY", "alpha3_code": "CYM"}, {"id": 58, "value": "Cypriot", "alpha2_code": "CY", "alpha3_code": "CYP"}, {"id": 59, "value": "Czech", "alpha2_code": "CZ", "alpha3_code": "CZE"}, {"id": 60, "value": "Djiboutian", "alpha2_code": "DJ", "alpha3_code": "DJI"}, {"id": 61, "value": "Dominican", "alpha2_code": "DM", "alpha3_code": "DMA"}, {"id": 62, "value": "Danish", "alpha2_code": "DK", "alpha3_code": "DNK"}, {"id": 63, "value": "Dominican", "alpha2_code": "DO", "alpha3_code": "DOM"}, {"id": 64, "value": "Algerian", "alpha2_code": "DZ", "alpha3_code": "DZA"}, {"id": 65, "value": "Ecuadorian", "alpha2_code": "EC", "alpha3_code": "ECU"}, {"id": 66, "value": "Egyptian", "alpha2_code": "EG", "alpha3_code": "EGY"}, {"id": 67, "value": "Eritrean", "alpha2_code": "ER", "alpha3_code": "ERI"}, {"id": 68, "value": "Sahra<PERSON>, Sahrawian, Sahraouian", "alpha2_code": "EH", "alpha3_code": "ESH"}, {"id": 69, "value": "Spanish", "alpha2_code": "ES", "alpha3_code": "ESP"}, {"id": 70, "value": "Estonian", "alpha2_code": "EE", "alpha3_code": "EST"}, {"id": 71, "value": "Ethiopian", "alpha2_code": "ET", "alpha3_code": "ETH"}, {"id": 72, "value": "Finnish", "alpha2_code": "FI", "alpha3_code": "FIN"}, {"id": 73, "value": "Fijian", "alpha2_code": "FJ", "alpha3_code": "FJI"}, {"id": 74, "value": "Falkland Island", "alpha2_code": "FK", "alpha3_code": "FLK"}, {"id": 75, "value": "French", "alpha2_code": "FR", "alpha3_code": "FRA"}, {"id": 76, "value": "Faroese", "alpha2_code": "FO", "alpha3_code": "FRO"}, {"id": 77, "value": "Micronesian", "alpha2_code": "FM", "alpha3_code": "FSM"}, {"id": 78, "value": "Gabonese", "alpha2_code": "GA", "alpha3_code": "GAB"}, {"id": 79, "value": "GBD", "alpha2_code": null, "alpha3_code": "GBD"}, {"id": 80, "value": "GBN", "alpha2_code": null, "alpha3_code": "GBN"}, {"id": 81, "value": "GBO", "alpha2_code": null, "alpha3_code": "GBO"}, {"id": 82, "value": "GBP", "alpha2_code": null, "alpha3_code": "GBP"}, {"id": 83, "value": "British, UK", "alpha2_code": "GB", "alpha3_code": "GBR"}, {"id": 84, "value": "GBS", "alpha2_code": null, "alpha3_code": "GBS"}, {"id": 85, "value": "Georgian", "alpha2_code": "GE", "alpha3_code": "GEO"}, {"id": 86, "value": "Channel Island", "alpha2_code": "GG", "alpha3_code": "GGY"}, {"id": 87, "value": "Ghanaian", "alpha2_code": "GH", "alpha3_code": "GHA"}, {"id": 88, "value": "Gibraltar", "alpha2_code": "GI", "alpha3_code": "GIB"}, {"id": 89, "value": "Guinean", "alpha2_code": "GN", "alpha3_code": "GIN"}, {"id": 90, "value": "Guadeloupe", "alpha2_code": "GP", "alpha3_code": "GLP"}, {"id": 91, "value": "Gambian", "alpha2_code": "GM", "alpha3_code": "GMB"}, {"id": 92, "value": "Bissau-Guinean", "alpha2_code": "GW", "alpha3_code": "GNB"}, {"id": 93, "value": "Equatorial Guinean, Equatoguinean", "alpha2_code": "GQ", "alpha3_code": "GNQ"}, {"id": 94, "value": "Greek, Hellenic", "alpha2_code": "GR", "alpha3_code": "GRC"}, {"id": 95, "value": "Grenadian", "alpha2_code": "GD", "alpha3_code": "GRD"}, {"id": 96, "value": "Greenlandic", "alpha2_code": "GL", "alpha3_code": "GRL"}, {"id": 97, "value": "Guatemalan", "alpha2_code": "GT", "alpha3_code": "GTM"}, {"id": 98, "value": "French Guianese", "alpha2_code": "GF", "alpha3_code": "GUF"}, {"id": 99, "value": "Guamanian, Guambat", "alpha2_code": "GU", "alpha3_code": "GUM"}, {"id": 100, "value": "Guyanese", "alpha2_code": "GY", "alpha3_code": "GUY"}, {"id": 101, "value": "Hong Kong, Hong Kongese", "alpha2_code": "HK", "alpha3_code": "HKG"}, {"id": 102, "value": "Heard Island or McDonald Islands", "alpha2_code": "HM", "alpha3_code": "HMD"}, {"id": 103, "value": "<PERSON><PERSON><PERSON>", "alpha2_code": "HN", "alpha3_code": "HND"}, {"id": 104, "value": "Croatian", "alpha2_code": "HR", "alpha3_code": "HRV"}, {"id": 105, "value": "Haitian", "alpha2_code": "HT", "alpha3_code": "HTI"}, {"id": 106, "value": "Hungarian, Ma<PERSON>ar", "alpha2_code": "HU", "alpha3_code": "HUN"}, {"id": 107, "value": "Indonesian", "alpha2_code": "ID", "alpha3_code": "IDN"}, {"id": 108, "value": "Manx", "alpha2_code": "IM", "alpha3_code": "IMN"}, {"id": 109, "value": "Indian", "alpha2_code": "IN", "alpha3_code": "IND"}, {"id": 110, "value": "BIOT", "alpha2_code": "IO", "alpha3_code": "IOT"}, {"id": 111, "value": "Irish", "alpha2_code": "IE", "alpha3_code": "IRL"}, {"id": 112, "value": "Iranian, Persian", "alpha2_code": "IR", "alpha3_code": "IRN"}, {"id": 113, "value": "Iraqi", "alpha2_code": "IQ", "alpha3_code": "IRQ"}, {"id": 114, "value": "Icelandic", "alpha2_code": "IS", "alpha3_code": "ISL"}, {"id": 115, "value": "Israeli", "alpha2_code": "IL", "alpha3_code": "ISR"}, {"id": 116, "value": "Italian", "alpha2_code": "IT", "alpha3_code": "ITA"}, {"id": 117, "value": "Jamaican", "alpha2_code": "JM", "alpha3_code": "JAM"}, {"id": 118, "value": "Channel Island", "alpha2_code": "JE", "alpha3_code": "JEY"}, {"id": 119, "value": "<PERSON><PERSON>", "alpha2_code": "JO", "alpha3_code": "JOR"}, {"id": 120, "value": "Japanese", "alpha2_code": "JP", "alpha3_code": "JPN"}, {"id": 121, "value": "Kazakhstani, Kazakh", "alpha2_code": "KZ", "alpha3_code": "KAZ"}, {"id": 122, "value": "Kenyan", "alpha2_code": "KE", "alpha3_code": "KEN"}, {"id": 123, "value": "Kyrgyzstani, Kyrgyz, Kirgiz, Kirghiz", "alpha2_code": "KG", "alpha3_code": "KGZ"}, {"id": 124, "value": "Cambodian", "alpha2_code": "KH", "alpha3_code": "KHM"}, {"id": 125, "value": "I-Kiribati", "alpha2_code": "KI", "alpha3_code": "KIR"}, {"id": 126, "value": "Kittitian or Nevisian", "alpha2_code": "KN", "alpha3_code": "KNA"}, {"id": 127, "value": "South Korean", "alpha2_code": "KR", "alpha3_code": "KOR"}, {"id": 128, "value": "Kuwaiti", "alpha2_code": "KW", "alpha3_code": "KWT"}, {"id": 129, "value": "Lao, Laotian", "alpha2_code": "LA", "alpha3_code": "LAO"}, {"id": 130, "value": "Lebanese", "alpha2_code": "LB", "alpha3_code": "LBN"}, {"id": 131, "value": "Liberian", "alpha2_code": "LR", "alpha3_code": "LBR"}, {"id": 132, "value": "Libyan", "alpha2_code": "LY", "alpha3_code": "LBY"}, {"id": 133, "value": "Saint Lucian", "alpha2_code": "LC", "alpha3_code": "LCA"}, {"id": 134, "value": "Liechtenstein", "alpha2_code": "LI", "alpha3_code": "LIE"}, {"id": 135, "value": "Sri Lankan", "alpha2_code": "LK", "alpha3_code": "LKA"}, {"id": 136, "value": "Basotho", "alpha2_code": "LS", "alpha3_code": "LSO"}, {"id": 137, "value": "Lithuanian", "alpha2_code": "LT", "alpha3_code": "LTU"}, {"id": 138, "value": "Luxembourg, Luxembourgish", "alpha2_code": "LU", "alpha3_code": "LUX"}, {"id": 139, "value": "Latvian", "alpha2_code": "LV", "alpha3_code": "LVA"}, {"id": 140, "value": "<PERSON><PERSON><PERSON>, Chinese", "alpha2_code": "MO", "alpha3_code": "MAC"}, {"id": 141, "value": "Saint-Martinoise", "alpha2_code": "MF", "alpha3_code": "MAF"}, {"id": 142, "value": "Moroccan", "alpha2_code": "MA", "alpha3_code": "MAR"}, {"id": 143, "value": "Monégasque, Monacan", "alpha2_code": "MC", "alpha3_code": "MCO"}, {"id": 144, "value": "Moldovan", "alpha2_code": "MD", "alpha3_code": "MDA"}, {"id": 145, "value": "Malagasy", "alpha2_code": "MG", "alpha3_code": "MDG"}, {"id": 146, "value": "Maldivian", "alpha2_code": "MV", "alpha3_code": "MDV"}, {"id": 147, "value": "Mexican", "alpha2_code": "MX", "alpha3_code": "MEX"}, {"id": 148, "value": "<PERSON><PERSON>", "alpha2_code": "MH", "alpha3_code": "MHL"}, {"id": 149, "value": "Macedonian", "alpha2_code": "MK", "alpha3_code": "MKD"}, {"id": 150, "value": "<PERSON><PERSON>, Malinese", "alpha2_code": "ML", "alpha3_code": "MLI"}, {"id": 151, "value": "Maltese", "alpha2_code": "MT", "alpha3_code": "MLT"}, {"id": 152, "value": "Burmese", "alpha2_code": "MM", "alpha3_code": "MMR"}, {"id": 153, "value": "Montenegrin", "alpha2_code": "ME", "alpha3_code": "MNE"}, {"id": 154, "value": "Mongolian", "alpha2_code": "MN", "alpha3_code": "MNG"}, {"id": 155, "value": "Northern Marianan", "alpha2_code": "MP", "alpha3_code": "MNP"}, {"id": 156, "value": "Mozambican", "alpha2_code": "MZ", "alpha3_code": "MOZ"}, {"id": 157, "value": "Mauritanian", "alpha2_code": "MR", "alpha3_code": "MRT"}, {"id": 158, "value": "<PERSON><PERSON><PERSON><PERSON>", "alpha2_code": "MS", "alpha3_code": "MSR"}, {"id": 159, "value": "Martiniquais, Martinican", "alpha2_code": "MQ", "alpha3_code": "MTQ"}, {"id": 160, "value": "<PERSON><PERSON><PERSON>", "alpha2_code": "MU", "alpha3_code": "MUS"}, {"id": 161, "value": "Malawian", "alpha2_code": "MW", "alpha3_code": "MWI"}, {"id": 162, "value": "Malaysian", "alpha2_code": "MY", "alpha3_code": "MYS"}, {"id": 163, "value": "<PERSON><PERSON><PERSON>", "alpha2_code": "YT", "alpha3_code": "MYT"}, {"id": 164, "value": "Namibian", "alpha2_code": "NA", "alpha3_code": "NAM"}, {"id": 165, "value": "New Caledonian", "alpha2_code": "NC", "alpha3_code": "NCL"}, {"id": 166, "value": "Nigerien", "alpha2_code": "NE", "alpha3_code": "NER"}, {"id": 167, "value": "Norfolk Island", "alpha2_code": "NF", "alpha3_code": "NFK"}, {"id": 168, "value": "Nigerian", "alpha2_code": "NG", "alpha3_code": "NGA"}, {"id": 169, "value": "Nicaraguan", "alpha2_code": "NI", "alpha3_code": "NIC"}, {"id": 170, "value": "<PERSON><PERSON><PERSON>", "alpha2_code": "NU", "alpha3_code": "NIU"}, {"id": 171, "value": "Dutch, Netherlandic", "alpha2_code": "NL", "alpha3_code": "NLD"}, {"id": 172, "value": "Norwegian", "alpha2_code": "NO", "alpha3_code": "NOR"}, {"id": 173, "value": "Nepali, Nepalese", "alpha2_code": "NP", "alpha3_code": "NPL"}, {"id": 174, "value": "Nauruan", "alpha2_code": "NR", "alpha3_code": "NRU"}, {"id": 175, "value": "New Zealand, NZ", "alpha2_code": "NZ", "alpha3_code": "NZL"}, {"id": 176, "value": "Omani", "alpha2_code": "OM", "alpha3_code": "OMN"}, {"id": 177, "value": "Pakistani", "alpha2_code": "PK", "alpha3_code": "PAK"}, {"id": 178, "value": "Panamanian", "alpha2_code": "PA", "alpha3_code": "PAN"}, {"id": 179, "value": "Pitcairn Island", "alpha2_code": "PN", "alpha3_code": "PCN"}, {"id": 180, "value": "Peruvian", "alpha2_code": "PE", "alpha3_code": "PER"}, {"id": 181, "value": "Philippine, Filipino", "alpha2_code": "PH", "alpha3_code": "PHL"}, {"id": 182, "value": "<PERSON><PERSON><PERSON>", "alpha2_code": "PW", "alpha3_code": "PLW"}, {"id": 183, "value": "Papua New Guinean, Papuan", "alpha2_code": "PG", "alpha3_code": "PNG"}, {"id": 184, "value": "Polish", "alpha2_code": "PL", "alpha3_code": "POL"}, {"id": 185, "value": "Puerto Rican", "alpha2_code": "PR", "alpha3_code": "PRI"}, {"id": 186, "value": "North Korean", "alpha2_code": "KP", "alpha3_code": "PRK"}, {"id": 187, "value": "Portuguese", "alpha2_code": "PT", "alpha3_code": "PRT"}, {"id": 188, "value": "Paraguayan", "alpha2_code": "PY", "alpha3_code": "PRY"}, {"id": 189, "value": "Palestinian", "alpha2_code": "PS", "alpha3_code": "PSE"}, {"id": 190, "value": "French Polynesian", "alpha2_code": "PF", "alpha3_code": "PYF"}, {"id": 191, "value": "Qatari", "alpha2_code": "QA", "alpha3_code": "QAT"}, {"id": 192, "value": "<PERSON><PERSON><PERSON><PERSON>, Réunionnais", "alpha2_code": "RE", "alpha3_code": "REU"}, {"id": 193, "value": "Romanian", "alpha2_code": "RO", "alpha3_code": "ROU"}, {"id": 194, "value": "Russian", "alpha2_code": "RU", "alpha3_code": "RUS"}, {"id": 195, "value": "Rwandan", "alpha2_code": "RW", "alpha3_code": "RWA"}, {"id": 196, "value": "Saudi, Saudi Arabian", "alpha2_code": "SA", "alpha3_code": "SAU"}, {"id": 197, "value": "Sudanese", "alpha2_code": "SD", "alpha3_code": "SDN"}, {"id": 198, "value": "Senegalese", "alpha2_code": "SN", "alpha3_code": "SEN"}, {"id": 199, "value": "Singaporean", "alpha2_code": "SG", "alpha3_code": "SGP"}, {"id": 200, "value": "South Georgia or South Sandwich Islands", "alpha2_code": "GS", "alpha3_code": "SGS"}, {"id": 201, "value": "<PERSON> <PERSON>", "alpha2_code": "SH", "alpha3_code": "SHN"}, {"id": 202, "value": "Svalbard", "alpha2_code": "SJ", "alpha3_code": "SJM"}, {"id": 203, "value": "Solomon Island", "alpha2_code": "SB", "alpha3_code": "SLB"}, {"id": 204, "value": "Sierra Leonean", "alpha2_code": "SL", "alpha3_code": "SLE"}, {"id": 205, "value": "Salvadoran", "alpha2_code": "SV", "alpha3_code": "SLV"}, {"id": 206, "value": "Sammarinese", "alpha2_code": "SM", "alpha3_code": "SMR"}, {"id": 207, "value": "Somali, Somalian", "alpha2_code": "SO", "alpha3_code": "SOM"}, {"id": 208, "value": "Saint-Pierrais or Miquelonnais", "alpha2_code": "PM", "alpha3_code": "SPM"}, {"id": 209, "value": "Serbian", "alpha2_code": "RS", "alpha3_code": "SRB"}, {"id": 210, "value": "South Sudanese", "alpha2_code": "SS", "alpha3_code": "SSD"}, {"id": 211, "value": "<PERSON>", "alpha2_code": "ST", "alpha3_code": "STP"}, {"id": 212, "value": "Surinamese", "alpha2_code": "SR", "alpha3_code": "SUR"}, {"id": 213, "value": "Slovak", "alpha2_code": "SK", "alpha3_code": "SVK"}, {"id": 214, "value": "Slovenian, Slovene", "alpha2_code": "SI", "alpha3_code": "SVN"}, {"id": 215, "value": "Swedish", "alpha2_code": "SE", "alpha3_code": "SWE"}, {"id": 216, "value": "Swazi", "alpha2_code": "SZ", "alpha3_code": "SWZ"}, {"id": 217, "value": "Sint Maarten", "alpha2_code": "SX", "alpha3_code": "SXM"}, {"id": 218, "value": "<PERSON><PERSON><PERSON><PERSON>", "alpha2_code": "SC", "alpha3_code": "SYC"}, {"id": 219, "value": "Syrian", "alpha2_code": "SY", "alpha3_code": "SYR"}, {"id": 220, "value": "Turks and Caicos Island", "alpha2_code": "TC", "alpha3_code": "TCA"}, {"id": 221, "value": "Chadian", "alpha2_code": "TD", "alpha3_code": "TCD"}, {"id": 222, "value": "Togolese", "alpha2_code": "TG", "alpha3_code": "TGO"}, {"id": 223, "value": "Thai", "alpha2_code": "TH", "alpha3_code": "THA"}, {"id": 224, "value": "Tajikistani", "alpha2_code": "TJ", "alpha3_code": "TJK"}, {"id": 225, "value": "Tokelauan", "alpha2_code": "TK", "alpha3_code": "TKL"}, {"id": 226, "value": "Turkmen", "alpha2_code": "TM", "alpha3_code": "TKM"}, {"id": 227, "value": "Timorese", "alpha2_code": "TL", "alpha3_code": "TLS"}, {"id": 228, "value": "Tongan", "alpha2_code": "TO", "alpha3_code": "TON"}, {"id": 229, "value": "Trinidadian or Tobagonian", "alpha2_code": "TT", "alpha3_code": "TTO"}, {"id": 230, "value": "Tunisian", "alpha2_code": "TN", "alpha3_code": "TUN"}, {"id": 231, "value": "Turkish", "alpha2_code": "TR", "alpha3_code": "TUR"}, {"id": 232, "value": "Tuvaluan", "alpha2_code": "TV", "alpha3_code": "TUV"}, {"id": 233, "value": "Chinese, Taiwanese", "alpha2_code": "TW", "alpha3_code": "TWN"}, {"id": 234, "value": "Tanzanian", "alpha2_code": "TZ", "alpha3_code": "TZA"}, {"id": 235, "value": "Ugandan", "alpha2_code": "UG", "alpha3_code": "UGA"}, {"id": 236, "value": "Ukrainian", "alpha2_code": "UA", "alpha3_code": "UKR"}, {"id": 237, "value": "American", "alpha2_code": "UM", "alpha3_code": "UMI"}, {"id": 238, "value": "UNA", "alpha2_code": null, "alpha3_code": "UNA"}, {"id": 239, "value": "UNK", "alpha2_code": null, "alpha3_code": "UNK"}, {"id": 240, "value": "UNO", "alpha2_code": null, "alpha3_code": "UNO"}, {"id": 241, "value": "Uruguayan", "alpha2_code": "UY", "alpha3_code": "URY"}, {"id": 242, "value": "American", "alpha2_code": "US", "alpha3_code": "USA"}, {"id": 243, "value": "Uzbekistani, Uzbek", "alpha2_code": "UZ", "alpha3_code": "UZB"}, {"id": 244, "value": "Vatican", "alpha2_code": "VA", "alpha3_code": "VAT"}, {"id": 245, "value": "<PERSON>, Vincentian", "alpha2_code": "VC", "alpha3_code": "VCT"}, {"id": 246, "value": "Venezuelan", "alpha2_code": "VE", "alpha3_code": "VEN"}, {"id": 247, "value": "British Virgin Island", "alpha2_code": "VG", "alpha3_code": "VGB"}, {"id": 248, "value": "U.S. Virgin Island", "alpha2_code": "VI", "alpha3_code": "VIR"}, {"id": 249, "value": "Vietnamese", "alpha2_code": "VN", "alpha3_code": "VNM"}, {"id": 250, "value": "Ni-Vanuatu, Vanuatuan", "alpha2_code": "VU", "alpha3_code": "VUT"}, {"id": 251, "value": "Wallis and Futuna, Wallisian or Futunan", "alpha2_code": "WF", "alpha3_code": "WLF"}, {"id": 252, "value": "Samoan", "alpha2_code": "WS", "alpha3_code": "WSM"}, {"id": 253, "value": "XCC", "alpha2_code": null, "alpha3_code": "XCC"}, {"id": 254, "value": "XOM", "alpha2_code": null, "alpha3_code": "XOM"}, {"id": 255, "value": "XXA", "alpha2_code": null, "alpha3_code": "XXA"}, {"id": 256, "value": "XXB", "alpha2_code": null, "alpha3_code": "XXB"}, {"id": 257, "value": "XXC", "alpha2_code": null, "alpha3_code": "XXC"}, {"id": 258, "value": "XXX", "alpha2_code": null, "alpha3_code": "XXX"}, {"id": 259, "value": "Yemeni", "alpha2_code": "YE", "alpha3_code": "YEM"}, {"id": 260, "value": "South African", "alpha2_code": "ZA", "alpha3_code": "ZAF"}, {"id": 261, "value": "Zambian", "alpha2_code": "ZM", "alpha3_code": "ZMB"}, {"id": 262, "value": "Zimbabwean", "alpha2_code": "ZW", "alpha3_code": "ZWE"}, {"id": 263, "value": "ZZZ", "alpha2_code": null, "alpha3_code": "ZZZ"}], "ranks": [{"id": 1, "value": "MASTER"}, {"id": 2, "value": "CHIEF OFFICER"}, {"id": 3, "value": "2ND OFFICER"}, {"id": 4, "value": "3RD OFFICER"}, {"id": 5, "value": "CADET"}, {"id": 6, "value": "CHIEF ENGINEER"}, {"id": 7, "value": "2ND ENGINEER"}, {"id": 8, "value": "3RD ENGINEER"}, {"id": 9, "value": "4TH ENGINEER"}, {"id": 10, "value": "5TH ENGINEER"}, {"id": 11, "value": "ENGINE CADET"}, {"id": 12, "value": "POEN"}, {"id": 13, "value": "POCT"}, {"id": 14, "value": "BOSUN"}, {"id": 15, "value": "AB"}, {"id": 16, "value": "MM"}, {"id": 17, "value": "OS"}, {"id": 18, "value": "WIPER"}, {"id": 19, "value": "GS"}, {"id": 20, "value": "TRAINEE OS"}, {"id": 21, "value": "TRAINEE WPR"}, {"id": 22, "value": "TRAINEE GS"}, {"id": 23, "value": "SUPY"}, {"id": 24, "value": "ELECTRICAL OFFICER"}, {"id": 25, "value": "REEFER ENG."}, {"id": 26, "value": "PUMP MAN"}, {"id": 27, "value": "RPFW"}, {"id": 28, "value": "RPCL"}, {"id": 29, "value": "NAVAL ARCHITECT"}, {"id": 30, "value": "TECHNICIAN"}, {"id": 31, "value": "RADIO OFFICER"}, {"id": 32, "value": "JUNIOR OFFICER"}, {"id": 33, "value": "SUPT"}, {"id": 34, "value": "FITTER"}, {"id": 35, "value": "OILER"}, {"id": 36, "value": "ENGINE ROOM ARTIFICER"}, {"id": 37, "value": "TRAINEE COOK"}, {"id": 38, "value": "JUNIOR ENGINEER"}, {"id": 39, "value": "TRAINEE ELECTRICAL OFFICER"}, {"id": 40, "value": "WELDER"}, {"id": 41, "value": "2ND COOK"}, {"id": 42, "value": "TRAINEE MARINE ENGINEER"}, {"id": 43, "value": "3RD COOK"}, {"id": 44, "value": "TRAINEE REEFER ENG."}, {"id": 45, "value": "1st <PERSON>e(F.G)"}, {"id": 46, "value": "ADMIN OFFICER"}, {"id": 47, "value": "Additional Master"}, {"id": 48, "value": "Addl Ch/Offr"}, {"id": 49, "value": "GAS ENGINEER"}, {"id": 50, "value": "TRAINEE CHIEF OFFICER"}, {"id": 51, "value": "Engine Fitter"}, {"id": 52, "value": "Trainee Master"}, {"id": 53, "value": "<PERSON>"}, {"id": 54, "value": "Assistant Electrical Officer"}, {"id": 55, "value": "Cargo Officer"}, {"id": 56, "value": "Additional 2nd Engineer"}, {"id": 57, "value": "Additional Chief Engineer"}, {"id": 58, "value": "GP"}, {"id": 59, "value": "Additional 3rd Engineer"}, {"id": 60, "value": "<PERSON>"}, {"id": 61, "value": "GP2"}, {"id": 62, "value": "Port Captain"}, {"id": 63, "value": "Chief <PERSON>"}, {"id": 64, "value": "4 th Engineer / Electrical Officer"}, {"id": 65, "value": "Additional 3rd Officer"}, {"id": 66, "value": "Asst. 3rd Engineer"}, {"id": 67, "value": "3rd Engineer / Electrical Officer"}, {"id": 68, "value": "Electrical Officer cum Reefer Engineer"}, {"id": 69, "value": "Marine Superintendent"}, {"id": 70, "value": "Technical Superintendent"}, {"id": 71, "value": "Office Executive"}, {"id": 72, "value": "Trainee 2nd Engineer"}, {"id": 73, "value": "Additional 4th Engineer"}, {"id": 74, "value": "Paint Technician"}, {"id": 75, "value": "4th Officer"}, {"id": 76, "value": "Medical Officer"}, {"id": 77, "value": "Helicopter Officer"}, {"id": 78, "value": "Helicopter Crew"}, {"id": 79, "value": "Hotel Engineer"}, {"id": 80, "value": "AC Engineer"}, {"id": 81, "value": "Crane Operator"}, {"id": 82, "value": "Plumber"}, {"id": 83, "value": "Steward / Storekeeper"}, {"id": 84, "value": "Trainee <PERSON>man"}, {"id": 85, "value": "Senior Cadet"}, {"id": 86, "value": "<PERSON> Boy"}, {"id": 87, "value": "Engine Boy"}, {"id": 88, "value": "Catering Boy"}, {"id": 89, "value": "<PERSON>"}, {"id": 90, "value": "Engine Trainee"}, {"id": 91, "value": "Catering Trainee"}, {"id": 92, "value": "Additional 2nd Officer"}, {"id": 93, "value": "Trainee Gas Engineer"}, {"id": 94, "value": "Observer - Deck"}, {"id": 95, "value": "SMS Trainer"}, {"id": 96, "value": "Training Superintendent"}, {"id": 97, "value": "Electro Technical Officer"}, {"id": 98, "value": "Electro Technical Rating"}, {"id": 99, "value": "Electrician"}, {"id": 100, "value": "Officer Cadet"}, {"id": 101, "value": "Trainee <PERSON>tter"}, {"id": 102, "value": "Deck Rating Trainee"}, {"id": 103, "value": "Engine Rating Trainee"}, {"id": 104, "value": "Trainee Electrical Engineer"}, {"id": 105, "value": "Trainee Electrician"}, {"id": 106, "value": "Electrical Engineer"}, {"id": 107, "value": "<PERSON><PERSON><PERSON>"}, {"id": 108, "value": "Deck Cadet"}, {"id": 109, "value": "Junior 3rd Officer"}, {"id": 110, "value": "Junior 4th Engineer"}, {"id": 111, "value": "Boys"}, {"id": 112, "value": "Trainees"}], "offices": [{"id": 1, "value": "Mumbai - Andheri", "ship_party_id": 1727}, {"id": 2, "value": "Manila", "ship_party_id": 1729}, {"id": 3, "value": "<PERSON><PERSON>", "ship_party_id": 1730}, {"id": 4, "value": "Kolkata", "ship_party_id": 1731}, {"id": 5, "value": "Chennai", "ship_party_id": 1732}, {"id": 6, "value": "Gurgaon", "ship_party_id": 1733}, {"id": 7, "value": "Hong Kong", "ship_party_id": 1734}, {"id": 8, "value": "Singapore", "ship_party_id": 1735}, {"id": 9, "value": "Visakhapatnam", "ship_party_id": 1736}, {"id": 10, "value": "London", "ship_party_id": 1737}, {"id": 11, "value": "Polaris Maritime, Szczecin", "ship_party_id": 1738}, {"id": 12, "value": "<PERSON>, China", "ship_party_id": 1739}, {"id": 13, "value": "<PERSON><PERSON>", "ship_party_id": 1740}, {"id": 14, "value": "Cyprus", "ship_party_id": 1741}, {"id": 15, "value": "FEMF, Vladivostok", "ship_party_id": 1742}, {"id": 16, "value": "SAJC, Vladivostok", "ship_party_id": 1743}, {"id": 17, "value": "<PERSON>, Odessa", "ship_party_id": 1744}, {"id": 18, "value": "Conship, Karachi", "ship_party_id": 1745}, {"id": 19, "value": "Marine Agency, Chittagong", "ship_party_id": 1746}, {"id": 20, "value": "Reliance Shipping, Chittagong", "ship_party_id": 1747}, {"id": 21, "value": "Unicorn Shipping, Chittagong", "ship_party_id": 1448}, {"id": 22, "value": "Cross Trade Shipping, Chittagong", "ship_party_id": 1749}, {"id": 23, "value": "Star Traders, Burma", "ship_party_id": 17250}, {"id": 24, "value": "Sanco Line, Colombo", "ship_party_id": 1751}, {"id": 25, "value": "Cosco Dalian Manning Cooperation Co., Ltd.", "ship_party_id": 1752}, {"id": 26, "value": "Gold Fleet, Dalian", "ship_party_id": 1753}, {"id": 27, "value": "-", "ship_party_id": 1754}, {"id": 28, "value": "<PERSON><PERSON><PERSON>", "ship_party_id": 1755}, {"id": 29, "value": "Sea Queen", "ship_party_id": 1756}, {"id": 30, "value": "Nova Shipping", "ship_party_id": 1757}, {"id": 31, "value": "MYINT", "ship_party_id": 1758}, {"id": 32, "value": "MCL - Crewing, Romania", "ship_party_id": 1759}, {"id": 33, "value": "Mumbai - Nerul", "ship_party_id": 1760}, {"id": 34, "value": "China Shipping Int'l Shipmanagement", "ship_party_id": 1761}, {"id": 35, "value": "BIMS", "ship_party_id": 1762}, {"id": 36, "value": "Bay Shipping Services, Dhaka", "ship_party_id": 17263}, {"id": 37, "value": "Lucknow", "ship_party_id": 1764}, {"id": 38, "value": "KT ShIP Ltd.", "ship_party_id": 1765}, {"id": 39, "value": "LAPA", "ship_party_id": 1766}, {"id": 40, "value": "AST, Karachi", "ship_party_id": 1767}, {"id": 41, "value": "Smart Crewing Agency Limited", "ship_party_id": 1768}, {"id": 42, "value": "Maritime Crewing Ltd., Georgia", "ship_party_id": 1769}, {"id": 43, "value": "Eurocrewing Novorossiysk", "ship_party_id": 1770}, {"id": 44, "value": "<PERSON><PERSON>, Romania", "ship_party_id": 1771}, {"id": 45, "value": "Shanghai New Xing Yang Marine Service Ltd.", "ship_party_id": 1772}, {"id": 46, "value": "Chandigarh", "ship_party_id": 1773}, {"id": 47, "value": "Global Marine Services", "ship_party_id": 1774}, {"id": 48, "value": "Mumbai - Borivali", "ship_party_id": 1775}, {"id": 49, "value": "Zhoushan, China", "ship_party_id": 1776}, {"id": 50, "value": "Auckland", "ship_party_id": 1777}, {"id": 51, "value": "<PERSON><PERSON><PERSON>", "ship_party_id": 1778}, {"id": 52, "value": "Jiangsu", "ship_party_id": 1789}, {"id": 53, "value": "USA - Houston", "ship_party_id": 1790}, {"id": 54, "value": "Eugenia Ltd., Ukraine", "ship_party_id": 1791}, {"id": 55, "value": "Dong Jin Shipping, Korea", "ship_party_id": 1792}, {"id": 56, "value": "Conmart (Ship Agents) Ltd., Israel", "ship_party_id": 1793}, {"id": 57, "value": "USA - Connecticut", "ship_party_id": 1794}, {"id": 58, "value": "Ulsan, South Korea", "ship_party_id": 1795}, {"id": 59, "value": "Yangzhou, China", "ship_party_id": 1796}, {"id": 60, "value": "Cosco Shanghai Manning Co., Ltd.", "ship_party_id": 1797}, {"id": 61, "value": "<PERSON>ian <PERSON>ayang Maritime Co., Ltd.", "ship_party_id": 1798}, {"id": 62, "value": "Shanghai Sinoship Seafarer Management Co. Ltd.", "ship_party_id": 1799}, {"id": 63, "value": "Tianjin Seastar Seaman Human Resources Management Co., Ltd.", "ship_party_id": 1800}, {"id": 64, "value": "CQC Crew Manning Company", "ship_party_id": 1801}, {"id": 65, "value": "Panstar Shipping , Busan Korea", "ship_party_id": 1802}, {"id": 66, "value": "SDSC Crew Manning Co., Ltd.", "ship_party_id": 1803}, {"id": 67, "value": "Dat Maritime Co. Ltd.", "ship_party_id": 1804}, {"id": 68, "value": "Norteam Shipping Services Inc., Philippines", "ship_party_id": 1805}, {"id": 69, "value": "Global Gateway Crewing Services, Philippines", "ship_party_id": 1806}, {"id": 70, "value": "Busan, S. Korea", "ship_party_id": 1807}, {"id": 71, "value": "Sinocrew, Shanghai", "ship_party_id": 1808}, {"id": 72, "value": "TJSM Co., Ltd., Korea", "ship_party_id": 1809}, {"id": 73, "value": "NanJing GoldenKingShipping Co., Ltd.", "ship_party_id": 1810}, {"id": 74, "value": "FMIPL", "ship_party_id": 1811}, {"id": 75, "value": "Unk Sagacious (Pvt) Ltd.", "ship_party_id": 1812}, {"id": 76, "value": "China Shipping Guangzhou", "ship_party_id": 1813}, {"id": 77, "value": "Beijing Shouhai International Econ. & Tech. Consultant Service Co., Ltd.", "ship_party_id": 1814}, {"id": 78, "value": "Qingdao Huayang Maritime Co., Ltd.", "ship_party_id": 1815}, {"id": 79, "value": "Turkey", "ship_party_id": 1816}, {"id": 80, "value": "Ceyline Shipping Ltd.", "ship_party_id": 1817}, {"id": 81, "value": "Ocean 21 Holdings Pte. Ltd", "ship_party_id": 1818}, {"id": 82, "value": "Shandong Fleet Management Limited", "ship_party_id": 1819}, {"id": 83, "value": "Ocean 21 / PT. Indomaritime Management", "ship_party_id": 1820}, {"id": 84, "value": "Hai Phong Marine Service & Trading Investment Limited Company", "ship_party_id": 1821}, {"id": 85, "value": "PT Sentra Makmur Lines (Crew Management)", "ship_party_id": 1822}, {"id": 86, "value": "Shandong Chinasun International Crew Management Ltd.", "ship_party_id": 1823}, {"id": 87, "value": "Dolphin Maritime Service Ltd", "ship_party_id": 1824}, {"id": 88, "value": "Polaris Shipping Co., Ltd", "ship_party_id": 1825}, {"id": 89, "value": "Southfield Agencies, Inc.", "ship_party_id": 1826}, {"id": 90, "value": "J-Phil Marine Inc.", "ship_party_id": 1827}, {"id": 91, "value": "Merrow Crewing Company, Ukraine", "ship_party_id": 1828}, {"id": 92, "value": "Alkyon Ltd, Ukraine", "ship_party_id": 1829}, {"id": 93, "value": "Alimark Crewing, Ukraine", "ship_party_id": 1830}, {"id": 94, "value": "MMSA, Myanmar", "ship_party_id": 1831}, {"id": 95, "value": "Istanbul", "ship_party_id": 1832}, {"id": 96, "value": "Goa", "ship_party_id": 1833}, {"id": 97, "value": "Denmark", "ship_party_id": 1834}], "manningAgents": [{"id": 27, "value": "-"}, {"id": 93, "value": "Alimark Crewing, Ukraine"}, {"id": 92, "value": "Alkyon Ltd, Ukraine"}, {"id": 40, "value": "AST, Karachi"}, {"id": 50, "value": "Auckland"}, {"id": 36, "value": "Bay Shipping Services, Dhaka"}, {"id": 77, "value": "Beijing Shouhai International Econ. & Tech. Consultant Service Co., Ltd."}, {"id": 35, "value": "BIMS"}, {"id": 70, "value": "Busan, S. Korea"}, {"id": 80, "value": "Ceyline Shipping Ltd."}, {"id": 46, "value": "Chandigarh"}], "institutes": [{"id": 31, "value": "(CIFNET) Central Institute of Fisheries Nautical & Engg Training, Kochi"}, {"id": 66, "value": "(MAAP) Maritime Academy of Asia and the Pacific"}, {"id": 2, "value": "Acadamy of Marine Education & Training (AMET), Chennai"}, {"id": 1, "value": "Applied Research International (ARI), New Delhi"}, {"id": 25, "value": "Aquatech Institute of Maritime Studies, New Delhi"}, {"id": 28, "value": "B.P.Marine Academy, Navi Mumbai"}, {"id": 57, "value": "Bombay Institute of Adv. Maritime Studies (BIAMS), Mumbai"}, {"id": 54, "value": "BPT Fosma"}, {"id": 48, "value": "CHENNAI SCHOOL OF SHIP MANAGEMENT"}, {"id": 3, "value": "Chidambaram Institute of Maritime Technology, Chennai"}, {"id": 4, "value": "Cochin Shipyard Limited, Kochi"}, {"id": 5, "value": "Coimbatore Marine College, Coimbatore"}, {"id": 37, "value": "Cosmopolitan Technology of Maritime, Chennai"}, {"id": 45, "value": "CV RAMAN Institute of Engineering"}, {"id": 60, "value": "Don Bosco Normar Maritime Academy, Mumbai"}, {"id": 64, "value": "Dr B R <PERSON>kar Institute of Technology (DBRAIT), Andaman & Nicobar Is"}, {"id": 15, "value": "Eurotech Maritime Academy, Kochi"}, {"id": 23, "value": "Garden Reach Ship Builders & Engineers Ltd (GRSE) Kolkata"}, {"id": 47, "value": "GKM College of Engineering"}, {"id": 69, "value": "Great eastern institute maritime studies"}, {"id": 30, "value": "Haldia Institute of Maritime Studies & Research (HIMSAR), Kolkata"}, {"id": 39, "value": "Hindustan Institute Of Maritime Training, Chennai"}, {"id": 50, "value": "Hindustan Shipyard Ltd.,Visakhapatnam"}, {"id": 24, "value": "IMI, Noida"}, {"id": 32, "value": "IMU Chennai Campus"}, {"id": 34, "value": "IMU Kandla Campus"}, {"id": 35, "value": "IMU Kochi Campus"}, {"id": 8, "value": "IMU Kolkata (MERI)"}, {"id": 43, "value": "IMU Mumbai Port Campus"}, {"id": 40, "value": "IMU Visakhapatnam Campus"}, {"id": 63, "value": "Indian Institute of Planning & Management (IIPM), Chennai"}, {"id": 59, "value": "Institute of Maritime Studies (IIMS), Goa"}, {"id": 21, "value": "International Maritime Academy, Chennai"}, {"id": 70, "value": "LIMA (LYCEUM INT MA<PERSON><PERSON>ME ACADEMY), BATANGAS, PHILIPPINES"}, {"id": 7, "value": "Maharashtra Academy of Naval Education and Training, Pune"}, {"id": 41, "value": "Marine Officers Training Academy, Pondicherry"}, {"id": 44, "value": "Maritime Foundation , Chennai"}, {"id": 56, "value": "Mazagaon Docks Ltd"}, {"id": 53, "value": "MTI (SCI), Powai"}, {"id": 29, "value": "Mumbai Maritime Training Institute (MMTI), Khopoli"}, {"id": 9, "value": "Naval Maritime Academy"}, {"id": 6, "value": "Neotia Institute of Technolgy, Management & Science (NITMAS), Kolkata"}, {"id": 68, "value": "Nimbus Maritime Academy"}, {"id": 55, "value": "NIPM Madras"}, {"id": 18, "value": "Other"}, {"id": 38, "value": "Perunthalaivar <PERSON>st of Maritime Sci & Engg (PKIMSE), Chidambaram"}, {"id": 65, "value": "PMMA (Philippine Merchant Marine Academy)"}, {"id": 22, "value": "Praveenya Institute of Marine Engg and Maritime Studies, Vizag"}, {"id": 42, "value": "R<PERSON>A.Pandey Marine Academy, Mumbai"}, {"id": 10, "value": "RL Institute of Nautical Sciences, Madurai"}, {"id": 36, "value": "Sai Ram Shipping Science Institute, Puducherry"}, {"id": 20, "value": "Sailor's Maritime Academy, Vizianagaram (AP)"}, {"id": 62, "value": "Sea Horse Academy of Merchant Navy, Kakinada, A.P."}, {"id": 72, "value": "Seacom Marine College, Howrah"}, {"id": 67, "value": "Shirdi Sai Nautical Science Academy, Bengalure"}, {"id": 71, "value": "Singapore Maritime Academy (SMA), Singapore"}, {"id": 14, "value": "Southern Academy of Maritime Studies (SAMS), Chennai"}, {"id": 49, "value": "SRI NANDHANAM MARITIME ACADEMY"}, {"id": 11, "value": "Sri Venkateshwara College of Engg"}, {"id": 33, "value": "Sriram Institute of Marine Studies (SIMS), New Delhi"}, {"id": 27, "value": "STET Maritime, Singapore"}, {"id": 52, "value": "The Great Eastern Cadet Academy (T S Jawahar) Mumbai"}, {"id": 51, "value": "The Great Eastern Institute of Maritime Studies, Lonavala"}, {"id": 12, "value": "Tolani Maritime Institute"}, {"id": 58, "value": "Trident College of Marine Technology (TCMT), W.Bengal"}, {"id": 17, "value": "TS Chanakya"}, {"id": 16, "value": "T<PERSON> Rahman"}, {"id": 19, "value": "<PERSON><PERSON>"}, {"id": 61, "value": "United Marine Academy, Navi Mumbai"}, {"id": 13, "value": "Vels Acadamy of Maritime Education & Training"}, {"id": 46, "value": "VISVESVARAYA TECHNICAL UNIVERSITY"}, {"id": 26, "value": "YAK, Khopoli"}], "endorsements": [{"id": 2, "value": "DANGEROUS CARGO ENDORSEMENT (CHEMICAL)"}, {"id": 3, "value": "DANGEROUS CARGO ENDORSEMENT (GAS)"}, {"id": 1, "value": "DANGEROUS CARGO ENDORSEMENT (OIL)"}, {"id": 4, "value": "GMDSS Endorsement"}, {"id": 5, "value": "IGF - Advance Level COP"}, {"id": 6, "value": "IGF - Basic COP"}], "medicalCertificates": [{"id": 1, "value": "ILO medical Cerificate"}, {"id": 2, "value": "other  medicals (NIS.Panama etc.)"}, {"id": 4, "value": "Psychometry"}, {"id": 3, "value": "TMT"}], "stcwLicences": [{"id": 1, "value": "ADVANCED FIRE FIGHTING"}, {"id": 5, "value": "AMOS - DOS BASED"}, {"id": 6, "value": "AMOS - WINDOWS BASED"}, {"id": 8, "value": "AUTOMATIC RADAR PLOTTING AID"}, {"id": 10, "value": "BASIC FIRE FIGHTING"}, {"id": 14, "value": "BRIDGE RESOURCE MANAGEMENT"}, {"id": 55, "value": "BRIDGE RESOURCE MANAGEMENT- ON LINE"}, {"id": 13, "value": "BRIDGE TEAM MANAGEMENT"}, {"id": 52, "value": "BRIDGE TEAM MANAGEMENT - REFRESHER COURSE"}, {"id": 38, "value": "C/E REFRESHER AND TRAINING COURSE"}, {"id": 39, "value": "CERTIFICATE OF LIFEBOAT MAN"}, {"id": 16, "value": "CHEMICAL TANKER FAMILIARISATION COURSE"}, {"id": 3, "value": "CHEMICAL TANKER SAFETY COURSE"}, {"id": 37, "value": "COC REVALIDATION COURSE"}, {"id": 15, "value": "COMPUTER FAMILIARISATION COURSE"}, {"id": 40, "value": "CRUDE OIL WASHING"}, {"id": 35, "value": "DECK WATCH KEEPING"}, {"id": 21, "value": "ELEMENTARY FIRST AID"}, {"id": 50, "value": "ENGINE ROOM SIMULATOR (MANAGEMENT LEVEL)"}, {"id": 41, "value": "ENGINE ROOM SIMULATOR (OPERATIONAL LEVEL)"}, {"id": 36, "value": "ENGINE WATCH KEEPING"}, {"id": 11, "value": "FIRE PREVENTION AND FIRE FIGHTING"}, {"id": 22, "value": "FIRST AID AT SEA"}, {"id": 18, "value": "GAS TANKER FAMILIARISATION COURSE"}, {"id": 4, "value": "GAS TANKER SAFETY COURSE"}, {"id": 25, "value": "GLOBAL MARITIME DISTRESS SAFETY SYSTEM"}, {"id": 26, "value": "HAZARDOUS MATERIAL HANDLING COURSE"}, {"id": 54, "value": "High Voltage Safety & Switch Gear Course"}, {"id": 42, "value": "INERT GAS AND CRUDE OIL WASHING"}, {"id": 27, "value": "LIQUID CARGO HANDLING SIMULATOR"}, {"id": 28, "value": "MARITIME ENGLISH COURSE"}, {"id": 24, "value": "MASTER'S MEDICARE COURSE"}, {"id": 23, "value": "MEDICAL FIRST AID"}, {"id": 32, "value": "NAVIGATIONAL CONTROL COURSE"}, {"id": 17, "value": "OIL TANKER FAMILIARISATION COURSE"}, {"id": 30, "value": "OTHER"}, {"id": 29, "value": "PERSONAL SURVIVAL TECHNIQUES"}, {"id": 43, "value": "PRE SEA RATING FAMILIARISATION COURSE"}, {"id": 46, "value": "Pre-Sea Course for Deck Cadets"}, {"id": 48, "value": "PROFESSIONAL TRAINING"}, {"id": 47, "value": "PROFICIENCY IN ELEMENTARY FIRST AID"}, {"id": 19, "value": "PROFICIENCY IN SURVIVAL CRAFT"}, {"id": 20, "value": "PROFICIENCY IN SURVIVAL CRAFT & RESCUE BOAT"}, {"id": 33, "value": "PSSR"}, {"id": 34, "value": "RADAR MAINTENANCE COURSE"}, {"id": 7, "value": "RADAR OBSERVERS COURSE"}, {"id": 9, "value": "RADAR SIMULATOR COURSE"}, {"id": 44, "value": "RESCUE BOAT"}, {"id": 53, "value": "Security Training for Seafarers with Designated Security Duties"}, {"id": 12, "value": "SHIP HANDLING SIMULATOR"}, {"id": 49, "value": "SHIP SECURITY OFFICER'S COURSE"}, {"id": 2, "value": "SPECIALIZED TANKER SAFETY COURSE"}, {"id": 45, "value": "SURVIVAL AT SEA"}, {"id": 51, "value": "TANKER FAMILIARIZATION COURSE"}, {"id": 31, "value": "WATCH KEEPING CERT"}], "cocCertificates": [{"id": 34, "value": "2nd Class Ref Eng"}, {"id": 37, "value": "2nd Engineer"}, {"id": 3, "value": "2nd <PERSON><PERSON> (F.G)"}, {"id": 38, "value": "3rd Engineer"}, {"id": 4, "value": "3rd Mate"}, {"id": 47, "value": "4th Engineer"}, {"id": 23, "value": "AB"}, {"id": 30, "value": "<PERSON><PERSON>"}, {"id": 35, "value": "<PERSON>"}, {"id": 45, "value": "Chief <PERSON>"}, {"id": 39, "value": "Chief Engineer"}, {"id": 36, "value": "Chief <PERSON><PERSON>"}, {"id": 1, "value": "Class 1 (Deck)"}, {"id": 5, "value": "Class 1 (Motor)"}, {"id": 2, "value": "Class 2 (Deck)"}, {"id": 6, "value": "Class 2 (Motor)"}, {"id": 13, "value": "Class 3 (Deck)"}, {"id": 14, "value": "Class 3 (Motor)"}, {"id": 7, "value": "Class 4 (Motor)"}, {"id": 12, "value": "Class IV (part A)"}, {"id": 29, "value": "Deck Cadet"}, {"id": 27, "value": "Electrical Officer"}, {"id": 28, "value": "Eng. Cadet"}, {"id": 44, "value": "Engine Officer"}, {"id": 16, "value": "Extra Chief"}, {"id": 15, "value": "Extra Master"}, {"id": 8, "value": "First Asst. Engineer"}, {"id": 33, "value": "First Mate (F.G)"}, {"id": 48, "value": "Fitter"}, {"id": 17, "value": "G - Form"}, {"id": 43, "value": "GS"}, {"id": 19, "value": "Indian Electrician Certificate"}, {"id": 49, "value": "Junior Engineer"}, {"id": 31, "value": "Master (FG)"}, {"id": 20, "value": "Master's (HT)"}, {"id": 26, "value": "<PERSON><PERSON><PERSON>"}, {"id": 21, "value": "N.W.K.O."}, {"id": 24, "value": "Oiler"}, {"id": 22, "value": "Ordinary Seaman"}, {"id": 41, "value": "OS"}, {"id": 11, "value": "Part (A)"}, {"id": 42, "value": "Pump Man"}, {"id": 32, "value": "R.T.G."}, {"id": 9, "value": "Second Asst. Engineer"}, {"id": 18, "value": "Second Class C.O.P."}, {"id": 40, "value": "Ship's Welder"}, {"id": 46, "value": "Steward"}, {"id": 10, "value": "Third Asst. Engineer"}, {"id": 25, "value": "Wiper"}], "preSeaTrainingCourses": [{"id": 8, "value": "1 Yr IGNOU"}, {"id": 17, "value": "1.5 Years DNS Singapore Polytechnic"}, {"id": 10, "value": "2 yr HND Glasgow course"}, {"id": 16, "value": "3m Pre-Sea Learning Programme (PLP)"}, {"id": 5, "value": "4 Yr workshop"}, {"id": 9, "value": "4M pre-sea training"}, {"id": 6, "value": "6M UK Course"}, {"id": 3, "value": "ATS"}, {"id": 1, "value": "<PERSON><PERSON> (Marine)"}, {"id": 15, "value": "B.S.M.E<PERSON> (Bachelor of Science Marine Engineering)"}, {"id": 14, "value": "B<PERSON>S.M.T. (Bachelor of Science Marine Transportation)"}, {"id": 13, "value": "BFSc(NS)"}, {"id": 7, "value": "BSc (Nautical Science)"}, {"id": 4, "value": "DME"}, {"id": 18, "value": "ETO COURSE"}, {"id": 2, "value": "GME"}, {"id": 12, "value": "IMU One Year DNS"}, {"id": 11, "value": "Other"}], "otherDocumentsTypes": [{"id": 9, "value": "5 year award"}, {"id": 2, "value": "Cholera Vaccination"}, {"id": 13, "value": "ECDIS Familiarisation Course"}, {"id": 8, "value": "EMS"}, {"id": 10, "value": "Energy Conservation Course"}, {"id": 11, "value": "Framo Cargo Pumping System Course"}, {"id": 23, "value": "HEMPEL Painting Course"}, {"id": 15, "value": "INDIAN DATABASE OF SEAFARERS NUMBER (INDOS)"}, {"id": 17, "value": "ISM code"}, {"id": 3, "value": "ISO 14001 Awareness Training"}, {"id": 6, "value": "ISO 9001 Awareness Training"}, {"id": 4, "value": "ISPS Code - SSO Certificate"}, {"id": 21, "value": "marked for promotion"}, {"id": 5, "value": "OHSAS 18001 Awareness Training"}, {"id": 26, "value": "Other Seaman Doc"}, {"id": 19, "value": "PARIS training"}, {"id": 24, "value": "PPG chemical course"}, {"id": 7, "value": "Proficient in Swimming"}, {"id": 16, "value": "REEFER CONTAINER FAMILIARISATION AND MAINTENANCE  COURSE"}, {"id": 20, "value": "Ship security officer -upgraded certificate"}, {"id": 18, "value": "Ship Security Training"}, {"id": 12, "value": "Spar Officer Course"}, {"id": 22, "value": "Training in company's EMS"}, {"id": 25, "value": "VIQ Online"}, {"id": 14, "value": "WORKSHOP ON SOFT SKILLS"}, {"id": 1, "value": "Yellow Fever Vaccination"}], "otherCourseTypes": [{"id": 2, "value": "5 E induction check list"}, {"id": 3, "value": "ADM Training"}, {"id": 4, "value": "Advanced ME Engine Course"}, {"id": 5, "value": "Anchoring Procedures"}, {"id": 6, "value": "Basic ME engine Course"}, {"id": 7, "value": "Basic Training on High Voltage"}, {"id": 13, "value": "Cadet - 1 CRA & Application"}, {"id": 14, "value": "Cadet - 2 Appl Comprehensive"}, {"id": 15, "value": "Cadet - 3 PJCL"}, {"id": 16, "value": "Cadet - 4 Orientation prior joining vessel"}, {"id": 17, "value": "Cadet - 5 ESE EMS SAFER FML Curriculum"}, {"id": 18, "value": "Cadet - 6 TOEFL"}, {"id": 19, "value": "Cadet - 7 Loyalty Bonus Agreement"}, {"id": 8, "value": "COLREGS online assessment"}, {"id": 20, "value": "Command Orientation Program (Certificate)"}, {"id": 9, "value": "COVID VACCINE FIRST DOSE"}, {"id": 10, "value": "COVID VACCINE SECOND DOSE"}, {"id": 11, "value": "COVID VACCINE SINGLE DOSE (J & J, etc.)"}, {"id": 12, "value": "COVID-19 CONSENT/SCREENING FORM"}, {"id": 21, "value": "ECDIS Generic 5d"}, {"id": 22, "value": "ECDIS Type Specific"}, {"id": 29, "value": "Ecdis Type Specific Furuno FEA"}, {"id": 30, "value": "Ecdis Type Specific Furuno FMD"}, {"id": 23, "value": "ECDIS Type Specific JRC"}, {"id": 24, "value": "ECDIS Type Specific MARIS"}, {"id": 25, "value": "ECDIS Type Specific TRANSAS"}, {"id": 26, "value": "EMS Refresher GRB ORB exercise"}, {"id": 27, "value": "EMS refresher training"}, {"id": 31, "value": "Enclosed space entry"}, {"id": 32, "value": "Engine Rm Simulator for 5/Es"}, {"id": 33, "value": "Engine Room Resource Management"}, {"id": 34, "value": "Enhanced ECDIS Workshop"}, {"id": 35, "value": "Enhanced Navigation Training"}, {"id": 36, "value": "Environmental Manangement System Course"}, {"id": 28, "value": "ESE Refresher Course"}, {"id": 37, "value": "Familairisation on Bilge Water Seperator Oil Content Monitor System"}, {"id": 40, "value": "Hazards of Nitrogen"}, {"id": 38, "value": "HAZMAT"}, {"id": 39, "value": "HEMPEL Painting Course"}, {"id": 41, "value": "Hydrogen Sulphide Course"}, {"id": 42, "value": "Incident Investigation and Analysis"}, {"id": 43, "value": "Korean Maritime Legislation"}, {"id": 44, "value": "Large Ship Handling Course"}, {"id": 46, "value": "Maritime Partners in Safety - LET - Q1 2016 Falling Into Water - V0"}, {"id": 47, "value": "Maritime Partners in Safety - LET - Q1 2017 Engine Failure - V0"}, {"id": 48, "value": "Maritime Partners in Safety - LET - Q1 2019 Dangerous(enclosed) Sapces V2 - V0"}, {"id": 49, "value": "Maritime Partners in Safety - LET - Q1 2020 Dry docking - V0"}, {"id": 50, "value": "Maritime Partners in Safety - LET - Q2 2016 Lifting and Hoisting - V0"}, {"id": 51, "value": "Maritime Partners in Safety - LET - Q2 2019 Cargo Operation"}, {"id": 52, "value": "Maritime Partners in Safety - LET - Q3 2016 Personal Injury - V0"}, {"id": 53, "value": "Maritime Partners in Safety - LET - Q3 2018 Personnel Transfer - V0"}, {"id": 54, "value": "Maritime Partners in Safety - LET - Q3 2019 Invisible Hazard - V0"}, {"id": 55, "value": "Maritime Partners in Safety - LET - Q4 2016 Slips, Trips and Falls - V0"}, {"id": 56, "value": "Maritime Partners in Safety - LET - Q4 2017 STS Operations - V0"}, {"id": 57, "value": "Maritime Partners in Safety - LET Archive (Pre -Jan 2016)"}, {"id": 58, "value": "Maritime Partners in Safety - LET- Q2 2017 Lifeboat Operations"}, {"id": 59, "value": "Maritime Partners in Safety - Reflective Learning - Chronic Unease - V0"}, {"id": 60, "value": "Maritime Partners in Safety - Reflective Lrng - Collective Normalisation - V0"}, {"id": 61, "value": "Maritime Partners in Safety - Reflective Lrng - I Keep My Barrier Strong - V0"}, {"id": 62, "value": "Maritime Partners in Safety - Reflective Lrng - It Will Never Happen To Me! - V0"}, {"id": 63, "value": "Maritime Partners in Safety - Reflective Lrng - Mooring - V0"}, {"id": 64, "value": "Maritime Partners in Safety - Resil - Looking at Situations in diffrnt way  - V0"}, {"id": 65, "value": "Maritime Partners in Safety - Resilience (2017) - Change is part of Living - V1"}, {"id": 66, "value": "Maritime Partners in Safety - Resilience (2017) – What is Resilience - V1"}, {"id": 67, "value": "Maritime Partners in Safety - Resilience (2017)- Take Care of Yourself - V1"}, {"id": 68, "value": "Maritime Partners in Safety - Resilience (2017)- Take Decisive Action - V1"}, {"id": 69, "value": "Maritime Partners in Safety - Resilience - Change is a Part of Living - V0"}, {"id": 70, "value": "Maritime Partners in Safety - Resilience - Dealing with a Crisis - v0"}, {"id": 71, "value": "Maritime Partners in Safety - Resilience - Keep Things in Perspective - V0"}, {"id": 72, "value": "Maritime Partners in Safety - Resilience - Maintaining a Hopeful Outlook - v0"}, {"id": 73, "value": "Maritime Partners in Safety - Resilience - Making Connections - v0"}, {"id": 74, "value": "Maritime Partners in Safety - Resilience - Take Care of Yourself - V0"}, {"id": 75, "value": "Maritime Partners in Safety - Resilience - Take Decisive Action - V0"}, {"id": 76, "value": "Maritime Partners in Safety - Resilience - What is Resilience - V0"}, {"id": 77, "value": "Maritime Resource Management"}, {"id": 45, "value": "ME engine course 4 days"}, {"id": 84, "value": "Navigation and Command Orientation Training"}, {"id": 80, "value": "Navigation Awareness for Ratings"}, {"id": 85, "value": "Navigation in Ice"}, {"id": 81, "value": "Navigation Orientation Training (earlier ENT)"}, {"id": 82, "value": "Navigation Refresher (NAVREF)"}, {"id": 83, "value": "Navigation Safety campaign"}, {"id": 78, "value": "NIS Legislation Course"}, {"id": 79, "value": "NIS safety Rep on board training report"}, {"id": 86, "value": "Obstruction of Justice"}, {"id": 87, "value": "Onboard SMS Training"}, {"id": 88, "value": "Onboard Type Specific Navigation Equipment Training"}, {"id": 1, "value": "Other Courses"}, {"id": 92, "value": "Passage Planning Course"}, {"id": 89, "value": "PDOS CHECKLIST"}, {"id": 93, "value": "Planning and Reporting Infrastructure Ships"}, {"id": 94, "value": "Port State Control Course"}, {"id": 90, "value": "PPG Chemical Training Course"}, {"id": 91, "value": "PPG PaintingTraining Course"}, {"id": 95, "value": "Pre Departure Orientation Seminar (Officer)"}, {"id": 96, "value": "Pre Departure Orientation Seminar (Rating)"}, {"id": 97, "value": "Prem Pride Orientation Course"}, {"id": 104, "value": "Promotion orientation program (2O)"}, {"id": 98, "value": "Promotion Orientation Program (3/O)"}, {"id": 99, "value": "Promotion Orientation Program (CO)"}, {"id": 100, "value": "Promotion Orientation Program (Master)"}, {"id": 103, "value": "Promotion Orientation program 2E"}, {"id": 101, "value": "Promotion Orientation Program 3E"}, {"id": 102, "value": "Promotion Orientation Program CE"}, {"id": 105, "value": "Risk Assessment Course"}, {"id": 106, "value": "SAFER Cheques"}, {"id": 111, "value": "SafeR+ (4 Hr Training - 2012)- OLD"}, {"id": 107, "value": "SAFER+ SSW 2014-15 NEW"}, {"id": 112, "value": "SafeR+ TOLAS Course"}, {"id": 115, "value": "Safety officer course"}, {"id": 114, "value": "Safety officer course"}, {"id": 113, "value": "Safety Reach Out"}, {"id": 108, "value": "SCBA testing and donning"}, {"id": 116, "value": "Seagull - Navigation Test"}], "userDefinedDocumentTypes": [{"id": 1, "value": "Ice / Polar Code Sea Service"}], "visaRegions": ["Australia", "Brazil", "Hong Kong", "India", "None", "Romania", "Russia", "Schengen", "Singapore", "United Kingdom", "United States"], "bool": [{"id": 1, "value": "yes"}, {"id": 2, "value": "no"}], "gender": [{"id": 1, "value": "male"}, {"id": 2, "value": "female"}], "dataQuality": [{"label": "Urgent Correction", "color": "red", "name": "mandatory_data_missing"}, {"label": "No Urgent Correction", "color": "orange", "name": "data_invalid"}, {"label": "Data is OK", "color": "green", "name": "clean"}]}