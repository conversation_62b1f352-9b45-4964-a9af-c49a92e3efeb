export const wagesApiResponseWithWagesDetailsAndIsPromotion = [
  {
    id: 2,
    seafarer_status_history: {
      id: 1479023,
      seafarer_person_id: 108701,
      seafarer_account_status: 'active',
      seafarer_journey_status: 'signed_on',
      seafarer_exam_status: null,
      rank_id: 26,
      vessel_name: 'Bochem Mumbai',
      vessel_ref_id: 4589,
      created_by_hash: '<EMAIL>',
      created_by: '<PERSON><PERSON>',
      seafarer_journey_remarks: '',
      seafarer_exam_remarks: null,
      status_date: '2021-09-08T00:00:00.000Z',
      vessel_ownership_id: 520,
      sign_off_date: null,
      expected_contract_end_date: null,
      embarkation_port: null,
      repatriation_port: null,
      vessel_tech_group: 'Tech T10',
      vessel_type: 'Chemical Tanker',
      replaced_by_id: null,
      is_p1_history: null,
      created_at: '2022-04-02T08:04:06.930Z',
      updated_at: '2022-04-02T08:04:06.930Z',
      created_at_p1: '2021-09-08T10:37:09.000Z',
      updated_at_p1: '2021-09-08T10:37:09.000Z',
      paris1_ref_id: 1452589,
      is_current_status: false,
    },
    seafarer_id: 108701,
    seafarer_person_id: 108701,

    status: 'pending',
    amount_basic_usd: 9999.05,
    amount_total_usd: 12000.05,
    effective_date: '2022-07-29T12:13:33.521Z',
    remarks: 'this is the remarks',
    is_history: false,
    ref_id: 1,

    seafarer_promotion: [
      {
        id: 1,
        new_rank: {
          id: 1,
          value: 'MASTER',
          unit: 'MSTR',
          ref_id: 2000336,
          sortpriority: 1,
        },
        prev_rank: {
          id: 2,
          value: 'CHIEF OFFICER',
          unit: 'C/O',
          ref_id: 2000337,
          sortpriority: 4,
        },
        new_contract_end_date: '2022-10-01',
        ref_id: 1,
        is_promotion: true,
      },
    ],
    created_at: '2022-06-29T12:13:33.521Z',
    updated_at: '2022-06-29T12:13:33.521Z',
    created_by: 'Brian Lai',
    updated_by: 'Brian Lai',

    seafarer_wages_details: [
      {
        id: 1,
        seafarer_wages_id: 1,
        payhead: {
          id: 1,
          head_name: 'Annual All',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          ref_id: 1,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 1,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 2,
        seafarer_wages_id: 1,
        payhead: {
          id: 2,
          head_name: 'Basic',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: true,
          display_order: 2,
          ref_id: 2,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 2,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 3,
        seafarer_wages_id: 1,
        payhead: {
          id: 3,
          head_name: 'Bonus',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 3,
          ref_id: 3,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 3,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 4,
        seafarer_wages_id: 1,
        payhead: {
          id: 4,
          head_name: 'Extend Stay',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 4,
          ref_id: 4,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 5,
        seafarer_wages_id: 1,
        payhead: {
          id: 5,
          head_name: 'Fixed SVA',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 5,
          ref_id: 5,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 5,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 6,
        seafarer_wages_id: 1,
        payhead: {
          id: 6,
          head_name: 'GMDSS',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 6,
          ref_id: 6,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 6,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 7,
        seafarer_wages_id: 1,
        payhead: {
          id: 7,
          head_name: 'Gross Monthly Wages',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 7,
          ref_id: 7,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 7,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 8,
        seafarer_wages_id: 1,
        payhead: {
          id: 8,
          head_name: 'Guranteed Overtime',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 8,
          ref_id: 8,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 8,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 9,
        seafarer_wages_id: 1,
        payhead: {
          id: 9,
          head_name: 'JSU Retirement Pay',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 9,
          ref_id: 9,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 9,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 10,
        seafarer_wages_id: 1,
        payhead: {
          id: 10,
          head_name: 'Longevity',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 10,
          ref_id: 19,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 10,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 11,
        seafarer_wages_id: 1,
        payhead: {
          id: 11,
          head_name: 'Loyalty Bonus',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 11,
          ref_id: 11,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 11,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 12,
        seafarer_wages_id: 1,
        payhead: {
          id: 12,
          head_name: 'Leave Pay',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 12,
          ref_id: 12,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 12,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 13,
        seafarer_wages_id: 1,
        payhead: {
          id: 13,
          head_name: 'Provident Fund - SS All',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 13,
          ref_id: 13,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 13,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 14,
        seafarer_wages_id: 1,
        payhead: {
          id: 14,
          head_name: 'Uniform Allowance',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 14,
          ref_id: 14,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 14,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 15,
        seafarer_wages_id: 1,
        payhead: {
          id: 15,
          head_name: 'Pension Fund',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 15,
          ref_id: 15,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 15,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 16,
        seafarer_wages_id: 1,
        payhead: {
          id: 16,
          head_name: 'R.A.(Retirals)',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 16,
          ref_id: 16,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 16,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 17,
        seafarer_wages_id: 1,
        payhead: {
          id: 17,
          head_name: 'SBM Allowance',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 17,
          ref_id: 17,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 17,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 18,
        seafarer_wages_id: 1,
        payhead: {
          id: 18,
          head_name: 'Service Incentive',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 18,
          ref_id: 18,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 18,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 19,
        seafarer_wages_id: 1,
        payhead: {
          id: 19,
          head_name: 'Sub Allowance',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 19,
          ref_id: 19,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 19,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 20,
        seafarer_wages_id: 1,
        payhead: {
          id: 20,
          head_name: 'Subsistance Allowance',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 20,
          ref_id: 20,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 20,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 21,
        seafarer_wages_id: 1,
        payhead: {
          id: 21,
          head_name: 'SUB-CBA',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 21,
          ref_id: 21,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 21,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 22,
        seafarer_wages_id: 1,
        payhead: {
          id: 22,
          head_name: 'Superior Certificate Allowance',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 22,
          ref_id: 22,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 22,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 23,
        seafarer_wages_id: 1,
        payhead: {
          id: 23,
          head_name: 'SWB',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 23,
          ref_id: 23,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 23,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 24,
        seafarer_wages_id: 1,
        payhead: {
          id: 24,
          head_name: 'Tanker Allowances',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 24,
          ref_id: 24,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 24,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 25,
        seafarer_wages_id: 1,
        payhead: {
          id: 25,
          head_name: 'Victualling All',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 25,
          ref_id: 25,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 25,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 26,
        seafarer_wages_id: 1,
        payhead: {
          id: 26,
          head_name: 'Laundry Allowance',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 26,
          ref_id: 26,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 26,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },

      {
        id: 27,
        seafarer_wages_id: 1,
        payhead: {
          id: 27,
          head_name: 'Crew Provident Fund',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed Deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          ref_id: 27,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 200.01,
        ref_id: 27,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 28,
        seafarer_wages_id: 1,
        payhead: {
          id: 28,
          head_name: 'Home Allotment',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed Deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          ref_id: 28,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 200.01,
        ref_id: 28,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 29,
        seafarer_wages_id: 1,
        payhead: {
          id: 29,
          head_name: 'JSU Retirement Pay Ded',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed Deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          ref_id: 29,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 200.01,
        ref_id: 29,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 30,
        seafarer_wages_id: 1,
        payhead: {
          id: 30,
          head_name: 'JSU Union Fee',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed Deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          ref_id: 30,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 200.01,
        ref_id: 30,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 31,
        seafarer_wages_id: 1,
        payhead: {
          id: 31,
          head_name: 'NIS Union Fee',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed Deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          ref_id: 31,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 200.01,
        ref_id: 31,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 32,
        seafarer_wages_id: 1,
        payhead: {
          id: 32,
          head_name: 'Paid By MA',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed Deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          ref_id: 32,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 200.01,
        ref_id: 32,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
    ],
  },
  {
    id: 1,
    seafarer_status_history: {
      id: 1479023,
      seafarer_person_id: 108701,
      seafarer_account_status: 'active',
      seafarer_journey_status: 'crew_assignment_approved',
      seafarer_exam_status: null,
      rank_id: 26,
      vessel_name: 'Bochem Mumbai',
      vessel_ref_id: 4589,
      created_by_hash: '<EMAIL>',
      created_by: 'Rajesh Nair',
      seafarer_journey_remarks: '',
      seafarer_exam_remarks: null,
      status_date: '2021-09-08T00:00:00.000Z',
      vessel_ownership_id: 520,
      sign_off_date: null,
      expected_contract_end_date: null,
      embarkation_port: null,
      repatriation_port: null,
      vessel_tech_group: 'Tech T10',
      vessel_type: 'Chemical Tanker',
      replaced_by_id: null,
      is_p1_history: null,
      created_at: '2022-04-02T08:04:06.930Z',
      updated_at: '2022-04-02T08:04:06.930Z',
      created_at_p1: '2021-09-08T10:37:09.000Z',
      updated_at_p1: '2021-09-08T10:37:09.000Z',
      paris1_ref_id: 1452589,
      is_current_status: false,
    },
    seafarer_id: 108701,
    seafarer_person_id: 108701,

    status: 'applied',
    amount_basic_usd: 8999.05,
    amount_total_usd: 11000.05,
    effective_date: '2022-06-20T12:13:33.521Z',
    remarks: 'this is the remarks',
    is_history: false,
    ref_id: 1,

    seafarer_promotion: [
      {
        id: 1,
        new_rank: {
          id: 1,
          value: 'MASTER',
          unit: 'MSTR',
          ref_id: 2000336,
          sortpriority: 1,
        },
        prev_rank: {
          id: 2,
          value: 'CHIEF OFFICER',
          unit: 'C/O',
          ref_id: 2000337,
          sortpriority: 4,
        },
        new_contract_end_date: '2022-10-01',
        ref_id: 1,
        is_promotion: true,
      },
    ],
    created_at: '2022-06-20T12:13:33.521Z',
    updated_at: '2022-06-20T12:13:33.521Z',
    created_by: 'Brian Lai',
    updated_by: 'Brian Lai',

    seafarer_wages_details: [
      {
        id: 1,
        seafarer_wages_id: 1,
        payhead: {
          id: 1,
          head_name: 'Annual All',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          ref_id: 1,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 1,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 2,
        seafarer_wages_id: 1,
        payhead: {
          id: 2,
          head_name: 'Basic',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: true,
          display_order: 2,
          ref_id: 2,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 2,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 3,
        seafarer_wages_id: 1,
        payhead: {
          id: 3,
          head_name: 'Bonus',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 3,
          ref_id: 3,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 3,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 4,
        seafarer_wages_id: 1,
        payhead: {
          id: 4,
          head_name: 'Extend Stay',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 4,
          ref_id: 4,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 5,
        seafarer_wages_id: 1,
        payhead: {
          id: 5,
          head_name: 'Fixed SVA',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 5,
          ref_id: 5,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 5,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 6,
        seafarer_wages_id: 1,
        payhead: {
          id: 6,
          head_name: 'GMDSS',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 6,
          ref_id: 6,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 6,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 7,
        seafarer_wages_id: 1,
        payhead: {
          id: 7,
          head_name: 'Gross Monthly Wages',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 7,
          ref_id: 7,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 7,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 8,
        seafarer_wages_id: 1,
        payhead: {
          id: 8,
          head_name: 'Guranteed Overtime',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 8,
          ref_id: 8,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 8,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 9,
        seafarer_wages_id: 1,
        payhead: {
          id: 9,
          head_name: 'JSU Retirement Pay',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 9,
          ref_id: 9,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 9,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 10,
        seafarer_wages_id: 1,
        payhead: {
          id: 10,
          head_name: 'Longevity',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 10,
          ref_id: 19,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 10,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 11,
        seafarer_wages_id: 1,
        payhead: {
          id: 11,
          head_name: 'Loyalty Bonus',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 11,
          ref_id: 11,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 11,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 12,
        seafarer_wages_id: 1,
        payhead: {
          id: 12,
          head_name: 'Leave Pay',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 12,
          ref_id: 12,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 12,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 13,
        seafarer_wages_id: 1,
        payhead: {
          id: 13,
          head_name: 'Provident Fund - SS All',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 13,
          ref_id: 13,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 13,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 14,
        seafarer_wages_id: 1,
        payhead: {
          id: 14,
          head_name: 'Uniform Allowance',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 14,
          ref_id: 14,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 14,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 15,
        seafarer_wages_id: 1,
        payhead: {
          id: 15,
          head_name: 'Pension Fund',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 15,
          ref_id: 15,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 15,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 16,
        seafarer_wages_id: 1,
        payhead: {
          id: 16,
          head_name: 'R.A.(Retirals)',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 16,
          ref_id: 16,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 16,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 17,
        seafarer_wages_id: 1,
        payhead: {
          id: 17,
          head_name: 'SBM Allowance',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 17,
          ref_id: 17,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 17,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 18,
        seafarer_wages_id: 1,
        payhead: {
          id: 18,
          head_name: 'Service Incentive',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 18,
          ref_id: 18,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 18,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 19,
        seafarer_wages_id: 1,
        payhead: {
          id: 19,
          head_name: 'Sub Allowance',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 19,
          ref_id: 19,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 19,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 20,
        seafarer_wages_id: 1,
        payhead: {
          id: 20,
          head_name: 'Subsistance Allowance',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 20,
          ref_id: 20,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 20,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 21,
        seafarer_wages_id: 1,
        payhead: {
          id: 21,
          head_name: 'SUB-CBA',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 21,
          ref_id: 21,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 21,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 22,
        seafarer_wages_id: 1,
        payhead: {
          id: 22,
          head_name: 'Superior Certificate Allowance',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 22,
          ref_id: 22,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 22,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 23,
        seafarer_wages_id: 1,
        payhead: {
          id: 23,
          head_name: 'SWB',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 23,
          ref_id: 23,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 23,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 24,
        seafarer_wages_id: 1,
        payhead: {
          id: 24,
          head_name: 'Tanker Allowances',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 24,
          ref_id: 24,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 24,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 25,
        seafarer_wages_id: 1,
        payhead: {
          id: 25,
          head_name: 'Victualling All',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 25,
          ref_id: 25,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 25,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 26,
        seafarer_wages_id: 1,
        payhead: {
          id: 26,
          head_name: 'Laundry Allowance',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 26,
          ref_id: 26,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 26,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },

      {
        id: 27,
        seafarer_wages_id: 1,
        payhead: {
          id: 27,
          head_name: 'Crew Provident Fund',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed Deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          ref_id: 27,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 200.01,
        ref_id: 27,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 28,
        seafarer_wages_id: 1,
        payhead: {
          id: 28,
          head_name: 'Home Allotment',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed Deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          ref_id: 28,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 200.01,
        ref_id: 28,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 29,
        seafarer_wages_id: 1,
        payhead: {
          id: 29,
          head_name: 'JSU Retirement Pay Ded',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed Deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          ref_id: 29,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 200.01,
        ref_id: 29,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 30,
        seafarer_wages_id: 1,
        payhead: {
          id: 30,
          head_name: 'JSU Union Fee',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed Deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          ref_id: 30,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 200.01,
        ref_id: 30,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 31,
        seafarer_wages_id: 1,
        payhead: {
          id: 31,
          head_name: 'NIS Union Fee',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed Deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          ref_id: 31,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 200.01,
        ref_id: 31,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 32,
        seafarer_wages_id: 1,
        payhead: {
          id: 32,
          head_name: 'Paid By MA',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed Deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          ref_id: 32,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 200.01,
        ref_id: 32,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
    ],
  },
];
export const wagesApiResponseWithWagesDetails = [
  {
    id: 2,
    seafarer_status_history: {
      id: 1479023,
      seafarer_person_id: 108701,
      seafarer_account_status: 'active',
      seafarer_journey_status: 'signed_on',
      seafarer_exam_status: null,
      rank_id: 26,
      vessel_name: 'Bochem Mumbai',
      vessel_ref_id: 4589,
      created_by_hash: '<EMAIL>',
      created_by: 'Rajesh Nair',
      seafarer_journey_remarks: '',
      seafarer_exam_remarks: null,
      status_date: '2021-09-08T00:00:00.000Z',
      vessel_ownership_id: 520,
      sign_off_date: null,
      expected_contract_end_date: null,
      embarkation_port: null,
      repatriation_port: null,
      vessel_tech_group: 'Tech T10',
      vessel_type: 'Chemical Tanker',
      replaced_by_id: null,
      is_p1_history: null,
      created_at: '2022-04-02T08:04:06.930Z',
      updated_at: '2022-04-02T08:04:06.930Z',
      created_at_p1: '2021-09-08T10:37:09.000Z',
      updated_at_p1: '2021-09-08T10:37:09.000Z',
      paris1_ref_id: 1452589,
      is_current_status: false,
    },
    seafarer_id: 108701,
    seafarer_person_id: 108701,

    status: 'pending',
    amount_basic_usd: 9999.05,
    amount_total_usd: 12000.05,
    effective_date: '2022-07-29T12:13:33.521Z',
    remarks: 'this is the remarks',
    is_history: false,
    ref_id: 1,

    seafarer_promotion: [
      {
        id: 1,
        new_rank: {
          id: 1,
          value: 'MASTER',
          unit: 'MSTR',
          ref_id: 2000336,
          sortpriority: 1,
        },
        prev_rank: {
          id: 2,
          value: 'CHIEF OFFICER',
          unit: 'C/O',
          ref_id: 2000337,
          sortpriority: 4,
        },
        new_contract_end_date: '2022-10-01',
        ref_id: 1,
      },
    ],
    created_at: '2022-06-29T12:13:33.521Z',
    updated_at: '2022-06-29T12:13:33.521Z',
    created_by: 'Brian Lai',
    updated_by: 'Brian Lai',

    seafarer_wages_details: [
      {
        id: 1,
        seafarer_wages_id: 1,
        payhead: {
          id: 1,
          head_name: 'Annual All',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          ref_id: 1,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 1,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 2,
        seafarer_wages_id: 1,
        payhead: {
          id: 2,
          head_name: 'Basic',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: true,
          display_order: 2,
          ref_id: 2,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 2,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 3,
        seafarer_wages_id: 1,
        payhead: {
          id: 3,
          head_name: 'Bonus',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 3,
          ref_id: 3,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 3,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 4,
        seafarer_wages_id: 1,
        payhead: {
          id: 4,
          head_name: 'Extend Stay',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 4,
          ref_id: 4,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 5,
        seafarer_wages_id: 1,
        payhead: {
          id: 5,
          head_name: 'Fixed SVA',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 5,
          ref_id: 5,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 5,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 6,
        seafarer_wages_id: 1,
        payhead: {
          id: 6,
          head_name: 'GMDSS',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 6,
          ref_id: 6,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 6,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 7,
        seafarer_wages_id: 1,
        payhead: {
          id: 7,
          head_name: 'Gross Monthly Wages',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 7,
          ref_id: 7,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 7,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 8,
        seafarer_wages_id: 1,
        payhead: {
          id: 8,
          head_name: 'Guranteed Overtime',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 8,
          ref_id: 8,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 8,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 9,
        seafarer_wages_id: 1,
        payhead: {
          id: 9,
          head_name: 'JSU Retirement Pay',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 9,
          ref_id: 9,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 9,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 10,
        seafarer_wages_id: 1,
        payhead: {
          id: 10,
          head_name: 'Longevity',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 10,
          ref_id: 19,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 10,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 11,
        seafarer_wages_id: 1,
        payhead: {
          id: 11,
          head_name: 'Loyalty Bonus',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 11,
          ref_id: 11,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 11,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 12,
        seafarer_wages_id: 1,
        payhead: {
          id: 12,
          head_name: 'Leave Pay',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 12,
          ref_id: 12,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 12,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 13,
        seafarer_wages_id: 1,
        payhead: {
          id: 13,
          head_name: 'Provident Fund - SS All',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 13,
          ref_id: 13,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 13,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 14,
        seafarer_wages_id: 1,
        payhead: {
          id: 14,
          head_name: 'Uniform Allowance',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 14,
          ref_id: 14,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 14,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 15,
        seafarer_wages_id: 1,
        payhead: {
          id: 15,
          head_name: 'Pension Fund',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 15,
          ref_id: 15,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 15,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 16,
        seafarer_wages_id: 1,
        payhead: {
          id: 16,
          head_name: 'R.A.(Retirals)',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 16,
          ref_id: 16,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 16,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 17,
        seafarer_wages_id: 1,
        payhead: {
          id: 17,
          head_name: 'SBM Allowance',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 17,
          ref_id: 17,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 17,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 18,
        seafarer_wages_id: 1,
        payhead: {
          id: 18,
          head_name: 'Service Incentive',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 18,
          ref_id: 18,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 18,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 19,
        seafarer_wages_id: 1,
        payhead: {
          id: 19,
          head_name: 'Sub Allowance',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 19,
          ref_id: 19,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 19,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 20,
        seafarer_wages_id: 1,
        payhead: {
          id: 20,
          head_name: 'Subsistance Allowance',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 20,
          ref_id: 20,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 20,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 21,
        seafarer_wages_id: 1,
        payhead: {
          id: 21,
          head_name: 'SUB-CBA',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 21,
          ref_id: 21,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 21,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 22,
        seafarer_wages_id: 1,
        payhead: {
          id: 22,
          head_name: 'Superior Certificate Allowance',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 22,
          ref_id: 22,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 22,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 23,
        seafarer_wages_id: 1,
        payhead: {
          id: 23,
          head_name: 'SWB',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 23,
          ref_id: 23,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 23,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 24,
        seafarer_wages_id: 1,
        payhead: {
          id: 24,
          head_name: 'Tanker Allowances',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 24,
          ref_id: 24,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 24,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 25,
        seafarer_wages_id: 1,
        payhead: {
          id: 25,
          head_name: 'Victualling All',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 25,
          ref_id: 25,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 25,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 26,
        seafarer_wages_id: 1,
        payhead: {
          id: 26,
          head_name: 'Laundry Allowance',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 26,
          ref_id: 26,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 26,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },

      {
        id: 27,
        seafarer_wages_id: 1,
        payhead: {
          id: 27,
          head_name: 'Crew Provident Fund',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed Deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          ref_id: 27,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 200.01,
        ref_id: 27,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 28,
        seafarer_wages_id: 1,
        payhead: {
          id: 28,
          head_name: 'Home Allotment',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed Deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          ref_id: 28,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 200.01,
        ref_id: 28,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 29,
        seafarer_wages_id: 1,
        payhead: {
          id: 29,
          head_name: 'JSU Retirement Pay Ded',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed Deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          ref_id: 29,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 200.01,
        ref_id: 29,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 30,
        seafarer_wages_id: 1,
        payhead: {
          id: 30,
          head_name: 'JSU Union Fee',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed Deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          ref_id: 30,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 200.01,
        ref_id: 30,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 31,
        seafarer_wages_id: 1,
        payhead: {
          id: 31,
          head_name: 'NIS Union Fee',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed Deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          ref_id: 31,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 200.01,
        ref_id: 31,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 32,
        seafarer_wages_id: 1,
        payhead: {
          id: 32,
          head_name: 'Paid By MA',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed Deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          ref_id: 32,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 200.01,
        ref_id: 32,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
    ],
  },
  {
    id: 1,
    seafarer_status_history: {
      id: 1479023,
      seafarer_person_id: 108701,
      seafarer_account_status: 'active',
      seafarer_journey_status: 'crew_assignment_approved',
      seafarer_exam_status: null,
      rank_id: 26,
      vessel_name: 'Bochem Mumbai',
      vessel_ref_id: 4589,
      created_by_hash: '<EMAIL>',
      created_by: 'Rajesh Nair',
      seafarer_journey_remarks: '',
      seafarer_exam_remarks: null,
      status_date: '2021-09-08T00:00:00.000Z',
      vessel_ownership_id: 520,
      sign_off_date: null,
      expected_contract_end_date: null,
      embarkation_port: null,
      repatriation_port: null,
      vessel_tech_group: 'Tech T10',
      vessel_type: 'Chemical Tanker',
      replaced_by_id: null,
      is_p1_history: null,
      created_at: '2022-04-02T08:04:06.930Z',
      updated_at: '2022-04-02T08:04:06.930Z',
      created_at_p1: '2021-09-08T10:37:09.000Z',
      updated_at_p1: '2021-09-08T10:37:09.000Z',
      paris1_ref_id: 1452589,
      is_current_status: false,
    },
    seafarer_id: 108701,
    seafarer_person_id: 108701,

    status: 'applied',
    amount_basic_usd: 8999.05,
    amount_total_usd: 11000.05,
    effective_date: '2022-06-20T12:13:33.521Z',
    remarks: 'this is the remarks',
    is_history: false,
    ref_id: 1,

    seafarer_promotion: [
      {
        id: 1,
        new_rank: {
          id: 1,
          value: 'MASTER',
          unit: 'MSTR',
          ref_id: 2000336,
          sortpriority: 1,
        },
        prev_rank: {
          id: 2,
          value: 'CHIEF OFFICER',
          unit: 'C/O',
          ref_id: 2000337,
          sortpriority: 4,
        },
        new_contract_end_date: '2022-10-01',
        ref_id: 1,
      },
    ],
    created_at: '2022-06-20T12:13:33.521Z',
    updated_at: '2022-06-20T12:13:33.521Z',
    created_by: 'Brian Lai',
    updated_by: 'Brian Lai',

    seafarer_wages_details: [
      {
        id: 1,
        seafarer_wages_id: 1,
        payhead: {
          id: 1,
          head_name: 'Annual All',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          ref_id: 1,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 1,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 2,
        seafarer_wages_id: 1,
        payhead: {
          id: 2,
          head_name: 'Basic',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: true,
          display_order: 2,
          ref_id: 2,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 2,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 3,
        seafarer_wages_id: 1,
        payhead: {
          id: 3,
          head_name: 'Bonus',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 3,
          ref_id: 3,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 3,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 4,
        seafarer_wages_id: 1,
        payhead: {
          id: 4,
          head_name: 'Extend Stay',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 4,
          ref_id: 4,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 5,
        seafarer_wages_id: 1,
        payhead: {
          id: 5,
          head_name: 'Fixed SVA',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 5,
          ref_id: 5,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 5,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 6,
        seafarer_wages_id: 1,
        payhead: {
          id: 6,
          head_name: 'GMDSS',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 6,
          ref_id: 6,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 6,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 7,
        seafarer_wages_id: 1,
        payhead: {
          id: 7,
          head_name: 'Gross Monthly Wages',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 7,
          ref_id: 7,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 7,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 8,
        seafarer_wages_id: 1,
        payhead: {
          id: 8,
          head_name: 'Guranteed Overtime',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 8,
          ref_id: 8,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 8,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 9,
        seafarer_wages_id: 1,
        payhead: {
          id: 9,
          head_name: 'JSU Retirement Pay',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 9,
          ref_id: 9,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 9,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 10,
        seafarer_wages_id: 1,
        payhead: {
          id: 10,
          head_name: 'Longevity',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 10,
          ref_id: 19,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 10,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 11,
        seafarer_wages_id: 1,
        payhead: {
          id: 11,
          head_name: 'Loyalty Bonus',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 11,
          ref_id: 11,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 11,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 12,
        seafarer_wages_id: 1,
        payhead: {
          id: 12,
          head_name: 'Leave Pay',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 12,
          ref_id: 12,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 12,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 13,
        seafarer_wages_id: 1,
        payhead: {
          id: 13,
          head_name: 'Provident Fund - SS All',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 13,
          ref_id: 13,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 13,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 14,
        seafarer_wages_id: 1,
        payhead: {
          id: 14,
          head_name: 'Uniform Allowance',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 14,
          ref_id: 14,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 14,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 15,
        seafarer_wages_id: 1,
        payhead: {
          id: 15,
          head_name: 'Pension Fund',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 15,
          ref_id: 15,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 15,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 16,
        seafarer_wages_id: 1,
        payhead: {
          id: 16,
          head_name: 'R.A.(Retirals)',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 16,
          ref_id: 16,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 16,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 17,
        seafarer_wages_id: 1,
        payhead: {
          id: 17,
          head_name: 'SBM Allowance',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 17,
          ref_id: 17,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 17,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 18,
        seafarer_wages_id: 1,
        payhead: {
          id: 18,
          head_name: 'Service Incentive',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 18,
          ref_id: 18,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 18,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 19,
        seafarer_wages_id: 1,
        payhead: {
          id: 19,
          head_name: 'Sub Allowance',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 19,
          ref_id: 19,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 19,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 20,
        seafarer_wages_id: 1,
        payhead: {
          id: 20,
          head_name: 'Subsistance Allowance',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 20,
          ref_id: 20,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 20,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 21,
        seafarer_wages_id: 1,
        payhead: {
          id: 21,
          head_name: 'SUB-CBA',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 21,
          ref_id: 21,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 21,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 22,
        seafarer_wages_id: 1,
        payhead: {
          id: 22,
          head_name: 'Superior Certificate Allowance',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 22,
          ref_id: 22,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 22,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 23,
        seafarer_wages_id: 1,
        payhead: {
          id: 23,
          head_name: 'SWB',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 23,
          ref_id: 23,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 23,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 24,
        seafarer_wages_id: 1,
        payhead: {
          id: 24,
          head_name: 'Tanker Allowances',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 24,
          ref_id: 24,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 24,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 25,
        seafarer_wages_id: 1,
        payhead: {
          id: 25,
          head_name: 'Victualling All',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 25,
          ref_id: 25,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 25,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 26,
        seafarer_wages_id: 1,
        payhead: {
          id: 26,
          head_name: 'Laundry Allowance',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 26,
          ref_id: 26,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 26,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },

      {
        id: 27,
        seafarer_wages_id: 1,
        payhead: {
          id: 27,
          head_name: 'Crew Provident Fund',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed Deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          ref_id: 27,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 200.01,
        ref_id: 27,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 28,
        seafarer_wages_id: 1,
        payhead: {
          id: 28,
          head_name: 'Home Allotment',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed Deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          ref_id: 28,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 200.01,
        ref_id: 28,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 29,
        seafarer_wages_id: 1,
        payhead: {
          id: 29,
          head_name: 'JSU Retirement Pay Ded',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed Deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          ref_id: 29,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 200.01,
        ref_id: 29,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 30,
        seafarer_wages_id: 1,
        payhead: {
          id: 30,
          head_name: 'JSU Union Fee',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed Deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          ref_id: 30,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 200.01,
        ref_id: 30,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 31,
        seafarer_wages_id: 1,
        payhead: {
          id: 31,
          head_name: 'NIS Union Fee',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed Deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          ref_id: 31,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 200.01,
        ref_id: 31,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 32,
        seafarer_wages_id: 1,
        payhead: {
          id: 32,
          head_name: 'Paid By MA',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed Deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          ref_id: 32,
          created_at: '2022-06-20T12:13:33.521Z',
          updated_at: '2022-06-20T12:13:33.521Z',
        },
        amount_usd: 200.01,
        ref_id: 32,
        created_at: '2022-06-20T12:13:33.521Z',
        updated_at: '2022-06-20T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
    ],
  },
];

export const wagesApiResponseWithUserNotSignedOn = [
  {
    id: 2,
    seafarer_status_history: {
      id: 1479023,
      seafarer_person_id: 108701,
      seafarer_account_status: 'active',
      seafarer_journey_status: 'crew_assignment_approved',
      seafarer_exam_status: null,
      rank_id: 26,
      vessel_name: 'Bochem Mumbai',
      vessel_ref_id: 4589,
      created_by_hash: '<EMAIL>',
      created_by: 'Rajesh Nair',
      seafarer_journey_remarks: '',
      seafarer_exam_remarks: null,
      status_date: '2021-09-08T00:00:00.000Z',
      vessel_ownership_id: 520,
      sign_off_date: null,
      expected_contract_end_date: null,
      embarkation_port: null,
      repatriation_port: null,
      vessel_tech_group: 'Tech T10',
      vessel_type: 'Chemical Tanker',
      replaced_by_id: null,
      is_p1_history: null,
      created_at: '2022-04-02T08:04:06.930Z',
      updated_at: '2022-04-02T08:04:06.930Z',
      created_at_p1: '2021-09-08T10:37:09.000Z',
      updated_at_p1: '2021-09-08T10:37:09.000Z',
      paris1_ref_id: 1452589,
      is_current_status: false,
    },
    seafarer_id: 108701,
    seafarer_person_id: 108701,

    status: 'pending',
    amount_basic_usd: 9999.05,
    amount_total_usd: 12000.05,
    effective_date: '2022-07-29T12:13:33.521Z',
    remarks: 'this is the remarks',
    is_history: false,
    ref_id: 1,

    promotion: {
      id: 1,
      new_rank: {
        id: 1,
        value: 'MASTER',
        unit: 'MSTR',
        ref_id: 2000336,
        sortpriority: 1,
      },
      prev_rank: {
        id: 2,
        value: 'CHIEF OFFICER',
        unit: 'C/O',
        ref_id: 2000337,
        sortpriority: 4,
      },
      new_contract_end_date: '2022-10-01',
      ref_id: 1,
    },
    created_at: '2022-06-29T12:13:33.521Z',
    updated_at: '2022-06-29T12:13:33.521Z',
    created_by: 'Brian Lai',
    updated_by: 'Brian Lai',

    seafarer_wages_details: [
      {
        id: 1,
        seafarer_wages_id: 1,
        payhead: {
          id: 1,
          head_name: 'Annual All',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          ref_id: 1,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 1,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 2,
        seafarer_wages_id: 1,
        payhead: {
          id: 2,
          head_name: 'Basic',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: true,
          display_order: 2,
          ref_id: 2,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 2,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 3,
        seafarer_wages_id: 1,
        payhead: {
          id: 3,
          head_name: 'Bonus',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 3,
          ref_id: 3,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 3,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 4,
        seafarer_wages_id: 1,
        payhead: {
          id: 4,
          head_name: 'Extend Stay',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 4,
          ref_id: 4,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 5,
        seafarer_wages_id: 1,
        payhead: {
          id: 5,
          head_name: 'Fixed SVA',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 5,
          ref_id: 5,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 5,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 6,
        seafarer_wages_id: 1,
        payhead: {
          id: 6,
          head_name: 'GMDSS',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 6,
          ref_id: 6,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 6,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 7,
        seafarer_wages_id: 1,
        payhead: {
          id: 7,
          head_name: 'Gross Monthly Wages',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 7,
          ref_id: 7,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 7,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 8,
        seafarer_wages_id: 1,
        payhead: {
          id: 8,
          head_name: 'Guranteed Overtime',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 8,
          ref_id: 8,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 8,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 9,
        seafarer_wages_id: 1,
        payhead: {
          id: 9,
          head_name: 'JSU Retirement Pay',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 9,
          ref_id: 9,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 9,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 10,
        seafarer_wages_id: 1,
        payhead: {
          id: 10,
          head_name: 'Longevity',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 10,
          ref_id: 19,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 10,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 11,
        seafarer_wages_id: 1,
        payhead: {
          id: 11,
          head_name: 'Loyalty Bonus',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 11,
          ref_id: 11,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 11,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 12,
        seafarer_wages_id: 1,
        payhead: {
          id: 12,
          head_name: 'Leave Pay',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 12,
          ref_id: 12,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 12,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 13,
        seafarer_wages_id: 1,
        payhead: {
          id: 13,
          head_name: 'Provident Fund - SS All',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 13,
          ref_id: 13,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 13,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 14,
        seafarer_wages_id: 1,
        payhead: {
          id: 14,
          head_name: 'Uniform Allowance',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 14,
          ref_id: 14,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 14,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 15,
        seafarer_wages_id: 1,
        payhead: {
          id: 15,
          head_name: 'Pension Fund',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 15,
          ref_id: 15,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 15,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 16,
        seafarer_wages_id: 1,
        payhead: {
          id: 16,
          head_name: 'R.A.(Retirals)',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 16,
          ref_id: 16,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 16,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 17,
        seafarer_wages_id: 1,
        payhead: {
          id: 17,
          head_name: 'SBM Allowance',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 17,
          ref_id: 17,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 17,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 18,
        seafarer_wages_id: 1,
        payhead: {
          id: 18,
          head_name: 'Service Incentive',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 18,
          ref_id: 18,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 18,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 19,
        seafarer_wages_id: 1,
        payhead: {
          id: 19,
          head_name: 'Sub Allowance',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 19,
          ref_id: 19,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 19,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 20,
        seafarer_wages_id: 1,
        payhead: {
          id: 20,
          head_name: 'Subsistance Allowance',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 20,
          ref_id: 20,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 20,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 21,
        seafarer_wages_id: 1,
        payhead: {
          id: 21,
          head_name: 'SUB-CBA',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 21,
          ref_id: 21,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 21,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 22,
        seafarer_wages_id: 1,
        payhead: {
          id: 22,
          head_name: 'Superior Certificate Allowance',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 22,
          ref_id: 22,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 22,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 23,
        seafarer_wages_id: 1,
        payhead: {
          id: 23,
          head_name: 'SWB',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 23,
          ref_id: 23,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 23,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 24,
        seafarer_wages_id: 1,
        payhead: {
          id: 24,
          head_name: 'Tanker Allowances',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 24,
          ref_id: 24,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 24,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 25,
        seafarer_wages_id: 1,
        payhead: {
          id: 25,
          head_name: 'Victualling All',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 25,
          ref_id: 25,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 25,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 26,
        seafarer_wages_id: 1,
        payhead: {
          id: 26,
          head_name: 'Laundry Allowance',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 26,
          ref_id: 26,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 100.01,
        ref_id: 26,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },

      {
        id: 27,
        seafarer_wages_id: 1,
        payhead: {
          id: 27,
          head_name: 'Crew Provident Fund',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed Deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          ref_id: 27,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 200.01,
        ref_id: 27,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 28,
        seafarer_wages_id: 1,
        payhead: {
          id: 28,
          head_name: 'Home Allotment',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed Deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          ref_id: 28,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 200.01,
        ref_id: 28,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 29,
        seafarer_wages_id: 1,
        payhead: {
          id: 29,
          head_name: 'JSU Retirement Pay Ded',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed Deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          ref_id: 29,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 200.01,
        ref_id: 29,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 30,
        seafarer_wages_id: 1,
        payhead: {
          id: 30,
          head_name: 'JSU Union Fee',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed Deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          ref_id: 30,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 200.01,
        ref_id: 30,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 31,
        seafarer_wages_id: 1,
        payhead: {
          id: 31,
          head_name: 'NIS Union Fee',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed Deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          ref_id: 31,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 200.01,
        ref_id: 31,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
      {
        id: 32,
        seafarer_wages_id: 1,
        payhead: {
          id: 32,
          head_name: 'Paid By MA',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed Deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_endered: 'office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          ref_id: 32,
          created_at: '2022-06-29T12:13:33.521Z',
          updated_at: '2022-06-29T12:13:33.521Z',
        },
        amount_usd: 200.01,
        ref_id: 32,
        created_at: '2022-06-29T12:13:33.521Z',
        updated_at: '2022-06-29T12:13:33.521Z',
        created_by: 'Brian Lai',
        updated_by: 'Brian Lai',
      },
    ],
  },
];

export const wagesApiResponseWithHistoryQueryParams = [
  {
    id: 55,
    amount_total: '1120.00',
    amount_basic: '490.00',
    status: 'applied',
    ref_id: null,
    remarks: '',
    is_history: true,
    amount_unit: 'usd',
    effective_date: '2022-04-01T00:00:00.000Z',
    seafarer_wages_details: [
      {
        id: 1187,
        seafarer_wages_id: 55,
        payhead_id: 2,
        amount: '490.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: null,
        last_updated_by_hash: null,
        created_at: '2022-08-01T09:44:36.765Z',
        updated_at: '2022-08-01T09:44:36.765Z',
        payhead: {
          id: 2,
          head_name: 'Basic',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: true,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: null,
        last_updated_by: null,
      },
      {
        id: 1188,
        seafarer_wages_id: 55,
        payhead_id: 5,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: null,
        last_updated_by_hash: null,
        created_at: '2022-08-01T09:44:36.765Z',
        updated_at: '2022-08-01T09:44:36.765Z',
        payhead: {
          id: 5,
          head_name: 'Fixed SVA',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: null,
        last_updated_by: null,
      },
      {
        id: 1189,
        seafarer_wages_id: 55,
        payhead_id: 55,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: null,
        last_updated_by_hash: null,
        created_at: '2022-08-01T09:44:36.765Z',
        updated_at: '2022-08-01T09:44:36.765Z',
        payhead: {
          id: 55,
          head_name: 'GMDSS',
          type: 'Allowance',
          nature: 'Input',
          category: 'Variable Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Vessel',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: null,
        last_updated_by: null,
      },
      {
        id: 1190,
        seafarer_wages_id: 55,
        payhead_id: 8,
        amount: '365.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: null,
        last_updated_by_hash: null,
        created_at: '2022-08-01T09:44:36.765Z',
        updated_at: '2022-08-01T09:44:36.765Z',
        payhead: {
          id: 8,
          head_name: 'Guranteed Overtime',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: null,
        last_updated_by: null,
      },
      {
        id: 1191,
        seafarer_wages_id: 55,
        payhead_id: 9,
        amount: '50.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: null,
        last_updated_by_hash: null,
        created_at: '2022-08-01T09:44:36.765Z',
        updated_at: '2022-08-01T09:44:36.765Z',
        payhead: {
          id: 9,
          head_name: 'JSU Retirement Pay',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: null,
        last_updated_by: null,
      },
      {
        id: 1192,
        seafarer_wages_id: 55,
        payhead_id: 12,
        amount: '215.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: null,
        last_updated_by_hash: null,
        created_at: '2022-08-01T09:44:36.765Z',
        updated_at: '2022-08-01T09:44:36.765Z',
        payhead: {
          id: 12,
          head_name: 'Leave Pay',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: null,
        last_updated_by: null,
      },
      {
        id: 1193,
        seafarer_wages_id: 55,
        payhead_id: 11,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: null,
        last_updated_by_hash: null,
        created_at: '2022-08-01T09:44:36.765Z',
        updated_at: '2022-08-01T09:44:36.765Z',
        payhead: {
          id: 11,
          head_name: 'Loyalty Bonus',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: null,
        last_updated_by: null,
      },
      {
        id: 1194,
        seafarer_wages_id: 55,
        payhead_id: 15,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: null,
        last_updated_by_hash: null,
        created_at: '2022-08-01T09:44:36.765Z',
        updated_at: '2022-08-01T09:44:36.765Z',
        payhead: {
          id: 15,
          head_name: 'Pension Fund',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: null,
        last_updated_by: null,
      },
      {
        id: 1195,
        seafarer_wages_id: 55,
        payhead_id: 13,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: null,
        last_updated_by_hash: null,
        created_at: '2022-08-01T09:44:36.765Z',
        updated_at: '2022-08-01T09:44:36.765Z',
        payhead: {
          id: 13,
          head_name: 'Provident Fund - SS All',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: null,
        last_updated_by: null,
      },
      {
        id: 1196,
        seafarer_wages_id: 55,
        payhead_id: 16,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: null,
        last_updated_by_hash: null,
        created_at: '2022-08-01T09:44:36.765Z',
        updated_at: '2022-08-01T09:44:36.765Z',
        payhead: {
          id: 16,
          head_name: 'R.A.(Retirals)',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: null,
        last_updated_by: null,
      },
      {
        id: 1197,
        seafarer_wages_id: 55,
        payhead_id: 18,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: null,
        last_updated_by_hash: null,
        created_at: '2022-08-01T09:44:36.765Z',
        updated_at: '2022-08-01T09:44:36.765Z',
        payhead: {
          id: 18,
          head_name: 'Service Incentive',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: null,
        last_updated_by: null,
      },
      {
        id: 1198,
        seafarer_wages_id: 55,
        payhead_id: 19,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: null,
        last_updated_by_hash: null,
        created_at: '2022-08-01T09:44:36.765Z',
        updated_at: '2022-08-01T09:44:36.765Z',
        payhead: {
          id: 19,
          head_name: 'Sub Allowance',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: null,
        last_updated_by: null,
      },
      {
        id: 1199,
        seafarer_wages_id: 55,
        payhead_id: 21,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: null,
        last_updated_by_hash: null,
        created_at: '2022-08-01T09:44:36.765Z',
        updated_at: '2022-08-01T09:44:36.765Z',
        payhead: {
          id: 21,
          head_name: 'SUB-CBA',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: null,
        last_updated_by: null,
      },
      {
        id: 1200,
        seafarer_wages_id: 55,
        payhead_id: 14,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: null,
        last_updated_by_hash: null,
        created_at: '2022-08-01T09:44:36.765Z',
        updated_at: '2022-08-01T09:44:36.765Z',
        payhead: {
          id: 14,
          head_name: 'Uniform Allowance',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: null,
        last_updated_by: null,
      },
      {
        id: 1201,
        seafarer_wages_id: 55,
        payhead_id: 28,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: null,
        last_updated_by_hash: null,
        created_at: '2022-08-01T09:44:36.765Z',
        updated_at: '2022-08-01T09:44:36.765Z',
        payhead: {
          id: 28,
          head_name: 'Superiority Allowance',
          type: 'Allowance',
          nature: 'Special',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: null,
        last_updated_by: null,
      },
      {
        id: 1202,
        seafarer_wages_id: 55,
        payhead_id: 27,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: null,
        last_updated_by_hash: null,
        created_at: '2022-08-01T09:44:36.765Z',
        updated_at: '2022-08-01T09:44:36.765Z',
        payhead: {
          id: 27,
          head_name: 'Trade Allowance',
          type: 'Allowance',
          nature: 'Special',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: null,
        last_updated_by: null,
      },
      {
        id: 1203,
        seafarer_wages_id: 55,
        payhead_id: 34,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: null,
        last_updated_by_hash: null,
        created_at: '2022-08-01T09:44:36.765Z',
        updated_at: '2022-08-01T09:44:36.765Z',
        payhead: {
          id: 34,
          head_name: 'Uniform Allowance EOC',
          type: 'Allowance',
          nature: 'Accumulation',
          category: 'Accumulated Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: null,
        last_updated_by: null,
      },
      {
        id: 1204,
        seafarer_wages_id: 55,
        payhead_id: 33,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: null,
        last_updated_by_hash: null,
        created_at: '2022-08-01T09:44:36.765Z',
        updated_at: '2022-08-01T09:44:36.765Z',
        payhead: {
          id: 33,
          head_name: 'Provident Fund - SS All EOC',
          type: 'Allowance',
          nature: 'Accumulation',
          category: 'Accumulated Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: null,
        last_updated_by: null,
      },
      {
        id: 1205,
        seafarer_wages_id: 55,
        payhead_id: 32,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: null,
        last_updated_by_hash: null,
        created_at: '2022-08-01T09:44:36.765Z',
        updated_at: '2022-08-01T09:44:36.765Z',
        payhead: {
          id: 32,
          head_name: 'Leave Pay EOC',
          type: 'Allowance',
          nature: 'Accumulation',
          category: 'Accumulated Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: null,
        last_updated_by: null,
      },
      {
        id: 1206,
        seafarer_wages_id: 55,
        payhead_id: 35,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: null,
        last_updated_by_hash: null,
        created_at: '2022-08-01T09:44:36.765Z',
        updated_at: '2022-08-01T09:44:36.765Z',
        payhead: {
          id: 35,
          head_name: 'Crew Provident Fund',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: null,
        last_updated_by: null,
      },
      {
        id: 1207,
        seafarer_wages_id: 55,
        payhead_id: 36,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: null,
        last_updated_by_hash: null,
        created_at: '2022-08-01T09:44:36.765Z',
        updated_at: '2022-08-01T09:44:36.765Z',
        payhead: {
          id: 36,
          head_name: 'Home Allotment',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: null,
        last_updated_by: null,
      },
      {
        id: 1208,
        seafarer_wages_id: 55,
        payhead_id: 37,
        amount: '50.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: null,
        last_updated_by_hash: null,
        created_at: '2022-08-01T09:44:36.765Z',
        updated_at: '2022-08-01T09:44:36.765Z',
        payhead: {
          id: 37,
          head_name: 'JSU Retirement Pay Ded',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: null,
        last_updated_by: null,
      },
      {
        id: 1209,
        seafarer_wages_id: 55,
        payhead_id: 38,
        amount: '40.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: null,
        last_updated_by_hash: null,
        created_at: '2022-08-01T09:44:36.765Z',
        updated_at: '2022-08-01T09:44:36.765Z',
        payhead: {
          id: 38,
          head_name: 'JSU Union Fee',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: null,
        last_updated_by: null,
      },
    ],
    seafarer_promotion: [],
    seafarer_status_history: {
      id: 933992,
      seafarer_person_id: 60715,
      seafarer_account_status: 'active',
      seafarer_journey_status: 'signed_on',
      seafarer_exam_status: null,
      rank_id: 17,
      vessel_name: 'Amber Star',
      vessel_ref_id: 5116,
      created_by_hash: '<EMAIL>',
      created_by: 'Ship Master',
      seafarer_journey_remarks: 'Onboard Sign On',
      seafarer_exam_remarks: null,
      status_date: '2022-03-30T00:00:00.000Z',
      vessel_ownership_id: 1305,
      sign_off_date: '2022-07-30T18:30:00.000Z',
      expected_contract_end_date: '2022-12-24T00:00:00.000Z',
      embarkation_port: 'BATAM INDONESIA',
      repatriation_port: 'MANILA',
      vessel_tech_group: 'SG Tech D4',
      vessel_type: 'Bulk Carrier',
      replaced_by_id: null,
      is_p1_history: null,
      seafarer_allotment_id: null,
      expected_contract_start_date: null,
      created_at: '2022-04-01T14:21:59.215Z',
      updated_at: '2022-08-09T08:35:00.424Z',
      created_at_p1: '2022-04-06T05:50:37.917Z',
      updated_at_p1: '2022-04-06T05:50:37.917Z',
      paris1_ref_id: null,
      is_current_status: false,
    },
    created_by_hash: null,
    last_updated_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
    created_at: '2022-08-01T09:44:36.765Z',
    updated_at: '2022-08-05T06:02:12.389Z',
    seafarer_id: 60715,
    created_by: null,
    last_updated_by: 'Neha Arora18',
    seafarer_person_id: 60715,
  },
  {
    id: 3236,
    amount_total: '1308.01',
    amount_basic: '678.01',
    status: 'applied',
    ref_id: null,
    remarks: 'this is the remarks',
    is_history: true,
    amount_unit: 'usd',
    effective_date: '2022-08-01T00:00:00.000Z',
    seafarer_wages_details: [
      {
        id: 74975,
        seafarer_wages_id: 3236,
        payhead_id: 2,
        amount: '678.01',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        last_updated_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        created_at: '2022-08-05T06:02:12.490Z',
        updated_at: '2022-08-05T06:02:12.490Z',
        payhead: {
          id: 2,
          head_name: 'Basic',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: true,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Neha Arora18',
        last_updated_by: 'Neha Arora18',
      },
      {
        id: 74976,
        seafarer_wages_id: 3236,
        payhead_id: 5,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        last_updated_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        created_at: '2022-08-05T06:02:12.490Z',
        updated_at: '2022-08-05T06:02:12.490Z',
        payhead: {
          id: 5,
          head_name: 'Fixed SVA',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Neha Arora18',
        last_updated_by: 'Neha Arora18',
      },
      {
        id: 74977,
        seafarer_wages_id: 3236,
        payhead_id: 55,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        last_updated_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        created_at: '2022-08-05T06:02:12.490Z',
        updated_at: '2022-08-05T06:02:12.490Z',
        payhead: {
          id: 55,
          head_name: 'GMDSS',
          type: 'Allowance',
          nature: 'Input',
          category: 'Variable Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Vessel',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Neha Arora18',
        last_updated_by: 'Neha Arora18',
      },
      {
        id: 74978,
        seafarer_wages_id: 3236,
        payhead_id: 8,
        amount: '365.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        last_updated_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        created_at: '2022-08-05T06:02:12.490Z',
        updated_at: '2022-08-05T06:02:12.490Z',
        payhead: {
          id: 8,
          head_name: 'Guranteed Overtime',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Neha Arora18',
        last_updated_by: 'Neha Arora18',
      },
      {
        id: 74979,
        seafarer_wages_id: 3236,
        payhead_id: 9,
        amount: '50.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        last_updated_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        created_at: '2022-08-05T06:02:12.490Z',
        updated_at: '2022-08-05T06:02:12.490Z',
        payhead: {
          id: 9,
          head_name: 'JSU Retirement Pay',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Neha Arora18',
        last_updated_by: 'Neha Arora18',
      },
      {
        id: 74980,
        seafarer_wages_id: 3236,
        payhead_id: 12,
        amount: '215.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        last_updated_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        created_at: '2022-08-05T06:02:12.490Z',
        updated_at: '2022-08-05T06:02:12.490Z',
        payhead: {
          id: 12,
          head_name: 'Leave Pay',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Neha Arora18',
        last_updated_by: 'Neha Arora18',
      },
      {
        id: 74981,
        seafarer_wages_id: 3236,
        payhead_id: 11,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        last_updated_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        created_at: '2022-08-05T06:02:12.490Z',
        updated_at: '2022-08-05T06:02:12.490Z',
        payhead: {
          id: 11,
          head_name: 'Loyalty Bonus',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Neha Arora18',
        last_updated_by: 'Neha Arora18',
      },
      {
        id: 74982,
        seafarer_wages_id: 3236,
        payhead_id: 15,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        last_updated_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        created_at: '2022-08-05T06:02:12.490Z',
        updated_at: '2022-08-05T06:02:12.490Z',
        payhead: {
          id: 15,
          head_name: 'Pension Fund',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Neha Arora18',
        last_updated_by: 'Neha Arora18',
      },
      {
        id: 74983,
        seafarer_wages_id: 3236,
        payhead_id: 13,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        last_updated_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        created_at: '2022-08-05T06:02:12.490Z',
        updated_at: '2022-08-05T06:02:12.490Z',
        payhead: {
          id: 13,
          head_name: 'Provident Fund - SS All',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Neha Arora18',
        last_updated_by: 'Neha Arora18',
      },
      {
        id: 74984,
        seafarer_wages_id: 3236,
        payhead_id: 16,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        last_updated_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        created_at: '2022-08-05T06:02:12.490Z',
        updated_at: '2022-08-05T06:02:12.490Z',
        payhead: {
          id: 16,
          head_name: 'R.A.(Retirals)',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Neha Arora18',
        last_updated_by: 'Neha Arora18',
      },
      {
        id: 74985,
        seafarer_wages_id: 3236,
        payhead_id: 18,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        last_updated_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        created_at: '2022-08-05T06:02:12.490Z',
        updated_at: '2022-08-05T06:02:12.490Z',
        payhead: {
          id: 18,
          head_name: 'Service Incentive',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Neha Arora18',
        last_updated_by: 'Neha Arora18',
      },
      {
        id: 74986,
        seafarer_wages_id: 3236,
        payhead_id: 19,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        last_updated_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        created_at: '2022-08-05T06:02:12.490Z',
        updated_at: '2022-08-05T06:02:12.490Z',
        payhead: {
          id: 19,
          head_name: 'Sub Allowance',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Neha Arora18',
        last_updated_by: 'Neha Arora18',
      },
      {
        id: 74987,
        seafarer_wages_id: 3236,
        payhead_id: 21,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        last_updated_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        created_at: '2022-08-05T06:02:12.490Z',
        updated_at: '2022-08-05T06:02:12.490Z',
        payhead: {
          id: 21,
          head_name: 'SUB-CBA',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Neha Arora18',
        last_updated_by: 'Neha Arora18',
      },
      {
        id: 74988,
        seafarer_wages_id: 3236,
        payhead_id: 14,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        last_updated_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        created_at: '2022-08-05T06:02:12.490Z',
        updated_at: '2022-08-05T06:02:12.490Z',
        payhead: {
          id: 14,
          head_name: 'Uniform Allowance',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Neha Arora18',
        last_updated_by: 'Neha Arora18',
      },
      {
        id: 74989,
        seafarer_wages_id: 3236,
        payhead_id: 28,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        last_updated_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        created_at: '2022-08-05T06:02:12.490Z',
        updated_at: '2022-08-05T06:02:12.490Z',
        payhead: {
          id: 28,
          head_name: 'Superiority Allowance',
          type: 'Allowance',
          nature: 'Special',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Neha Arora18',
        last_updated_by: 'Neha Arora18',
      },
      {
        id: 74990,
        seafarer_wages_id: 3236,
        payhead_id: 27,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        last_updated_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        created_at: '2022-08-05T06:02:12.490Z',
        updated_at: '2022-08-05T06:02:12.490Z',
        payhead: {
          id: 27,
          head_name: 'Trade Allowance',
          type: 'Allowance',
          nature: 'Special',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Neha Arora18',
        last_updated_by: 'Neha Arora18',
      },
      {
        id: 74991,
        seafarer_wages_id: 3236,
        payhead_id: 34,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        last_updated_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        created_at: '2022-08-05T06:02:12.490Z',
        updated_at: '2022-08-05T06:02:12.490Z',
        payhead: {
          id: 34,
          head_name: 'Uniform Allowance EOC',
          type: 'Allowance',
          nature: 'Accumulation',
          category: 'Accumulated Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Neha Arora18',
        last_updated_by: 'Neha Arora18',
      },
      {
        id: 74992,
        seafarer_wages_id: 3236,
        payhead_id: 33,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        last_updated_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        created_at: '2022-08-05T06:02:12.490Z',
        updated_at: '2022-08-05T06:02:12.490Z',
        payhead: {
          id: 33,
          head_name: 'Provident Fund - SS All EOC',
          type: 'Allowance',
          nature: 'Accumulation',
          category: 'Accumulated Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Neha Arora18',
        last_updated_by: 'Neha Arora18',
      },
      {
        id: 74993,
        seafarer_wages_id: 3236,
        payhead_id: 32,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        last_updated_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        created_at: '2022-08-05T06:02:12.490Z',
        updated_at: '2022-08-05T06:02:12.490Z',
        payhead: {
          id: 32,
          head_name: 'Leave Pay EOC',
          type: 'Allowance',
          nature: 'Accumulation',
          category: 'Accumulated Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Neha Arora18',
        last_updated_by: 'Neha Arora18',
      },
      {
        id: 74994,
        seafarer_wages_id: 3236,
        payhead_id: 35,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        last_updated_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        created_at: '2022-08-05T06:02:12.490Z',
        updated_at: '2022-08-05T06:02:12.490Z',
        payhead: {
          id: 35,
          head_name: 'Crew Provident Fund',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Neha Arora18',
        last_updated_by: 'Neha Arora18',
      },
      {
        id: 74995,
        seafarer_wages_id: 3236,
        payhead_id: 36,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        last_updated_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        created_at: '2022-08-05T06:02:12.490Z',
        updated_at: '2022-08-05T06:02:12.490Z',
        payhead: {
          id: 36,
          head_name: 'Home Allotment',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Neha Arora18',
        last_updated_by: 'Neha Arora18',
      },
      {
        id: 74996,
        seafarer_wages_id: 3236,
        payhead_id: 37,
        amount: '50.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        last_updated_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        created_at: '2022-08-05T06:02:12.490Z',
        updated_at: '2022-08-05T06:02:12.490Z',
        payhead: {
          id: 37,
          head_name: 'JSU Retirement Pay Ded',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Neha Arora18',
        last_updated_by: 'Neha Arora18',
      },
      {
        id: 74997,
        seafarer_wages_id: 3236,
        payhead_id: 38,
        amount: '40.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        last_updated_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
        created_at: '2022-08-05T06:02:12.490Z',
        updated_at: '2022-08-05T06:02:12.490Z',
        payhead: {
          id: 38,
          head_name: 'JSU Union Fee',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Neha Arora18',
        last_updated_by: 'Neha Arora18',
      },
    ],
    seafarer_promotion: [],
    seafarer_status_history: {
      id: 933992,
      seafarer_person_id: 60715,
      seafarer_account_status: 'active',
      seafarer_journey_status: 'signed_on',
      seafarer_exam_status: null,
      rank_id: 17,
      vessel_name: 'Amber Star',
      vessel_ref_id: 5116,
      created_by_hash: '<EMAIL>',
      created_by: 'Ship Master',
      seafarer_journey_remarks: 'Onboard Sign On',
      seafarer_exam_remarks: null,
      status_date: '2022-03-30T00:00:00.000Z',
      vessel_ownership_id: 1305,
      sign_off_date: '2022-07-30T18:30:00.000Z',
      expected_contract_end_date: '2022-12-24T00:00:00.000Z',
      embarkation_port: 'BATAM INDONESIA',
      repatriation_port: 'MANILA',
      vessel_tech_group: 'SG Tech D4',
      vessel_type: 'Bulk Carrier',
      replaced_by_id: null,
      is_p1_history: null,
      seafarer_allotment_id: null,
      expected_contract_start_date: null,
      created_at: '2022-04-01T14:21:59.215Z',
      updated_at: '2022-08-09T08:35:00.424Z',
      created_at_p1: '2022-04-06T05:50:37.917Z',
      updated_at_p1: '2022-04-06T05:50:37.917Z',
      paris1_ref_id: null,
      is_current_status: false,
    },
    created_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
    last_updated_by_hash: 'nUcG13clQFCnYDSRbEk5rPGH9KtXvXIdbUdC54Ai9FQ=',
    created_at: '2022-08-05T06:02:12.428Z',
    updated_at: '2022-08-05T06:14:45.300Z',
    seafarer_id: 60715,
    created_by: 'Neha Arora18',
    last_updated_by: 'Neha Arora18',
    seafarer_person_id: 60715,
  },
  {
    id: 3239,
    amount_total: '1819.01',
    amount_basic: '678.01',
    status: 'applied',
    ref_id: null,
    remarks: null,
    is_history: true,
    amount_unit: 'usd',
    effective_date: '2022-08-03T00:00:00.000Z',
    seafarer_wages_details: [
      {
        id: 75044,
        seafarer_wages_id: 3239,
        payhead_id: 2,
        amount: '678.01',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        last_updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        created_at: '2022-08-05T06:17:13.932Z',
        updated_at: '2022-08-05T06:17:13.932Z',
        payhead: {
          id: 2,
          head_name: 'Basic',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: true,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Brian Lai test',
        last_updated_by: 'Brian Lai test',
      },
      {
        id: 75045,
        seafarer_wages_id: 3239,
        payhead_id: 5,
        amount: '11.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        last_updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        created_at: '2022-08-05T06:17:13.932Z',
        updated_at: '2022-08-05T06:17:13.932Z',
        payhead: {
          id: 5,
          head_name: 'Fixed SVA',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Brian Lai test',
        last_updated_by: 'Brian Lai test',
      },
      {
        id: 75046,
        seafarer_wages_id: 3239,
        payhead_id: 55,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        last_updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        created_at: '2022-08-05T06:17:13.932Z',
        updated_at: '2022-08-05T06:17:13.932Z',
        payhead: {
          id: 55,
          head_name: 'GMDSS',
          type: 'Allowance',
          nature: 'Input',
          category: 'Variable Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Vessel',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Brian Lai test',
        last_updated_by: 'Brian Lai test',
      },
      {
        id: 75047,
        seafarer_wages_id: 3239,
        payhead_id: 8,
        amount: '365.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        last_updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        created_at: '2022-08-05T06:17:13.932Z',
        updated_at: '2022-08-05T06:17:13.932Z',
        payhead: {
          id: 8,
          head_name: 'Guranteed Overtime',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Brian Lai test',
        last_updated_by: 'Brian Lai test',
      },
      {
        id: 75048,
        seafarer_wages_id: 3239,
        payhead_id: 9,
        amount: '150.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        last_updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        created_at: '2022-08-05T06:17:13.932Z',
        updated_at: '2022-08-05T06:17:13.932Z',
        payhead: {
          id: 9,
          head_name: 'JSU Retirement Pay',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Brian Lai test',
        last_updated_by: 'Brian Lai test',
      },
      {
        id: 75049,
        seafarer_wages_id: 3239,
        payhead_id: 12,
        amount: '615.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        last_updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        created_at: '2022-08-05T06:17:13.932Z',
        updated_at: '2022-08-05T06:17:13.932Z',
        payhead: {
          id: 12,
          head_name: 'Leave Pay',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Brian Lai test',
        last_updated_by: 'Brian Lai test',
      },
      {
        id: 75050,
        seafarer_wages_id: 3239,
        payhead_id: 11,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        last_updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        created_at: '2022-08-05T06:17:13.932Z',
        updated_at: '2022-08-05T06:17:13.932Z',
        payhead: {
          id: 11,
          head_name: 'Loyalty Bonus',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Brian Lai test',
        last_updated_by: 'Brian Lai test',
      },
      {
        id: 75051,
        seafarer_wages_id: 3239,
        payhead_id: 15,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        last_updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        created_at: '2022-08-05T06:17:13.932Z',
        updated_at: '2022-08-05T06:17:13.932Z',
        payhead: {
          id: 15,
          head_name: 'Pension Fund',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Brian Lai test',
        last_updated_by: 'Brian Lai test',
      },
      {
        id: 75052,
        seafarer_wages_id: 3239,
        payhead_id: 13,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        last_updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        created_at: '2022-08-05T06:17:13.932Z',
        updated_at: '2022-08-05T06:17:13.932Z',
        payhead: {
          id: 13,
          head_name: 'Provident Fund - SS All',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Brian Lai test',
        last_updated_by: 'Brian Lai test',
      },
      {
        id: 75053,
        seafarer_wages_id: 3239,
        payhead_id: 16,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        last_updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        created_at: '2022-08-05T06:17:13.932Z',
        updated_at: '2022-08-05T06:17:13.932Z',
        payhead: {
          id: 16,
          head_name: 'R.A.(Retirals)',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Brian Lai test',
        last_updated_by: 'Brian Lai test',
      },
      {
        id: 75054,
        seafarer_wages_id: 3239,
        payhead_id: 18,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        last_updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        created_at: '2022-08-05T06:17:13.932Z',
        updated_at: '2022-08-05T06:17:13.932Z',
        payhead: {
          id: 18,
          head_name: 'Service Incentive',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Brian Lai test',
        last_updated_by: 'Brian Lai test',
      },
      {
        id: 75055,
        seafarer_wages_id: 3239,
        payhead_id: 19,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        last_updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        created_at: '2022-08-05T06:17:13.932Z',
        updated_at: '2022-08-05T06:17:13.932Z',
        payhead: {
          id: 19,
          head_name: 'Sub Allowance',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Brian Lai test',
        last_updated_by: 'Brian Lai test',
      },
      {
        id: 75056,
        seafarer_wages_id: 3239,
        payhead_id: 21,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        last_updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        created_at: '2022-08-05T06:17:13.932Z',
        updated_at: '2022-08-05T06:17:13.932Z',
        payhead: {
          id: 21,
          head_name: 'SUB-CBA',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Brian Lai test',
        last_updated_by: 'Brian Lai test',
      },
      {
        id: 75057,
        seafarer_wages_id: 3239,
        payhead_id: 14,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        last_updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        created_at: '2022-08-05T06:17:13.932Z',
        updated_at: '2022-08-05T06:17:13.932Z',
        payhead: {
          id: 14,
          head_name: 'Uniform Allowance',
          type: 'Allowance',
          nature: 'Monthly',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Brian Lai test',
        last_updated_by: 'Brian Lai test',
      },
      {
        id: 75058,
        seafarer_wages_id: 3239,
        payhead_id: 28,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        last_updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        created_at: '2022-08-05T06:17:13.932Z',
        updated_at: '2022-08-05T06:17:13.932Z',
        payhead: {
          id: 28,
          head_name: 'Superiority Allowance',
          type: 'Allowance',
          nature: 'Special',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Brian Lai test',
        last_updated_by: 'Brian Lai test',
      },
      {
        id: 75059,
        seafarer_wages_id: 3239,
        payhead_id: 27,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        last_updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        created_at: '2022-08-05T06:17:13.932Z',
        updated_at: '2022-08-05T06:17:13.932Z',
        payhead: {
          id: 27,
          head_name: 'Trade Allowance',
          type: 'Allowance',
          nature: 'Special',
          category: 'Standard Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Brian Lai test',
        last_updated_by: 'Brian Lai test',
      },
      {
        id: 75060,
        seafarer_wages_id: 3239,
        payhead_id: 34,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        last_updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        created_at: '2022-08-05T06:17:13.932Z',
        updated_at: '2022-08-05T06:17:13.932Z',
        payhead: {
          id: 34,
          head_name: 'Uniform Allowance EOC',
          type: 'Allowance',
          nature: 'Accumulation',
          category: 'Accumulated Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Brian Lai test',
        last_updated_by: 'Brian Lai test',
      },
      {
        id: 75061,
        seafarer_wages_id: 3239,
        payhead_id: 33,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        last_updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        created_at: '2022-08-05T06:17:13.932Z',
        updated_at: '2022-08-05T06:17:13.932Z',
        payhead: {
          id: 33,
          head_name: 'Provident Fund - SS All EOC',
          type: 'Allowance',
          nature: 'Accumulation',
          category: 'Accumulated Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Brian Lai test',
        last_updated_by: 'Brian Lai test',
      },
      {
        id: 75062,
        seafarer_wages_id: 3239,
        payhead_id: 32,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        last_updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        created_at: '2022-08-05T06:17:13.932Z',
        updated_at: '2022-08-05T06:17:13.932Z',
        payhead: {
          id: 32,
          head_name: 'Leave Pay EOC',
          type: 'Allowance',
          nature: 'Accumulation',
          category: 'Accumulated Earnings',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: true,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Brian Lai test',
        last_updated_by: 'Brian Lai test',
      },
      {
        id: 75063,
        seafarer_wages_id: 3239,
        payhead_id: 35,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        last_updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        created_at: '2022-08-05T06:17:13.932Z',
        updated_at: '2022-08-05T06:17:13.932Z',
        payhead: {
          id: 35,
          head_name: 'Crew Provident Fund',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Brian Lai test',
        last_updated_by: 'Brian Lai test',
      },
      {
        id: 75064,
        seafarer_wages_id: 3239,
        payhead_id: 36,
        amount: '0.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        last_updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        created_at: '2022-08-05T06:17:13.932Z',
        updated_at: '2022-08-05T06:17:13.932Z',
        payhead: {
          id: 36,
          head_name: 'Home Allotment',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Brian Lai test',
        last_updated_by: 'Brian Lai test',
      },
      {
        id: 75065,
        seafarer_wages_id: 3239,
        payhead_id: 37,
        amount: '50.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        last_updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        created_at: '2022-08-05T06:17:13.932Z',
        updated_at: '2022-08-05T06:17:13.932Z',
        payhead: {
          id: 37,
          head_name: 'JSU Retirement Pay Ded',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Brian Lai test',
        last_updated_by: 'Brian Lai test',
      },
      {
        id: 75066,
        seafarer_wages_id: 3239,
        payhead_id: 38,
        amount: '40.00',
        ref_id: null,
        amount_unit: 'usd',
        created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        last_updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
        created_at: '2022-08-05T06:17:13.932Z',
        updated_at: '2022-08-05T06:17:13.932Z',
        payhead: {
          id: 38,
          head_name: 'JSU Union Fee',
          type: 'Deduction',
          nature: 'Monthly',
          category: 'Fixed deductions',
          is_display_input_sheet: false,
          default_value: 0,
          place_entered: 'Office',
          is_add_to_total_wages: false,
          is_add_to_basic_wages: false,
          display_order: 1,
          status: 1,
          created_by: '2022-07-25 15:34:39.75419+05:30',
          updated_by: null,
        },
        created_by: 'Brian Lai test',
        last_updated_by: 'Brian Lai test',
      },
    ],
    seafarer_promotion: [],
    seafarer_status_history: {
      id: 933992,
      seafarer_person_id: 60715,
      seafarer_account_status: 'active',
      seafarer_journey_status: 'signed_on',
      seafarer_exam_status: null,
      rank_id: 17,
      vessel_name: 'Amber Star',
      vessel_ref_id: 5116,
      created_by_hash: '<EMAIL>',
      created_by: 'Ship Master',
      seafarer_journey_remarks: 'Onboard Sign On',
      seafarer_exam_remarks: null,
      status_date: '2022-03-30T00:00:00.000Z',
      vessel_ownership_id: 1305,
      sign_off_date: '2022-07-30T18:30:00.000Z',
      expected_contract_end_date: '2022-12-24T00:00:00.000Z',
      embarkation_port: 'BATAM INDONESIA',
      repatriation_port: 'MANILA',
      vessel_tech_group: 'SG Tech D4',
      vessel_type: 'Bulk Carrier',
      replaced_by_id: null,
      is_p1_history: null,
      seafarer_allotment_id: null,
      expected_contract_start_date: null,
      created_at: '2022-04-01T14:21:59.215Z',
      updated_at: '2022-08-09T08:35:00.424Z',
      created_at_p1: '2022-04-06T05:50:37.917Z',
      updated_at_p1: '2022-04-06T05:50:37.917Z',
      paris1_ref_id: null,
      is_current_status: false,
    },
    created_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
    last_updated_by_hash: '6/BxTmPiChHaFRK8kpc40dv7gu0B23K64bIsXRmXN64=',
    created_at: '2022-08-05T06:17:13.929Z',
    updated_at: '2022-08-09T08:34:12.528Z',
    seafarer_id: 60715,
    created_by: 'Brian Lai test',
    last_updated_by: 'Brian Lai test',
    seafarer_person_id: 60715,
  },
];
