[{"seafarer_status_history": {"id": 1479023, "seafarer_person_id": 108701, "seafarer_account_status": "active", "seafarer_journey_status": "crew_assignment_approved", "seafarer_exam_status": null, "rank_id": 26, "vessel_name": "Bochem Mumbai", "vessel_ref_id": 4589, "created_by_hash": "<EMAIL>", "created_by": "<PERSON><PERSON>", "seafarer_journey_remarks": "", "seafarer_exam_remarks": null, "status_date": "2021-09-08T00:00:00.000Z", "vessel_ownership_id": 520, "sign_off_date": null, "expected_contract_end_date": null, "embarkation_port": null, "repatriation_port": null, "vessel_tech_group": "Tech T10", "vessel_type": "Chemical Tanker", "replaced_by_id": null, "is_p1_history": null, "created_at": "2022-04-02T08:04:06.930Z", "updated_at": "2022-04-02T08:04:06.930Z", "created_at_p1": "2021-09-08T10:37:09.000Z", "updated_at_p1": "2021-09-08T10:37:09.000Z", "paris1_ref_id": 1452589, "is_current_status": false}, "seafarer_id": 108701, "seafarer_person_id": 108701, "payheads": [{"id": 143, "head_name": "HRA Allowance", "type": "Allowance", "nature": "Input", "category": "Variable Earnings", "is_display_input_sheet": true, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 134, "head_name": "Bonus - Tech", "type": "Allowance", "nature": "Input", "category": "Variable Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 135, "head_name": "Bonus - Short Hand", "type": "Allowance", "nature": "Input", "category": "Variable Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 136, "head_name": "Arrear", "type": "Allowance", "nature": "Input", "category": "Variable Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 137, "head_name": "Balance B/F", "type": "Allowance", "nature": "Input", "category": "Variable Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 138, "head_name": "Boiler / Scav Cleaning", "type": "Allowance", "nature": "Input", "category": "Variable Earnings", "is_display_input_sheet": true, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 139, "head_name": "Travelling Exp Reim", "type": "Allowance", "nature": "Input", "category": "Variable Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 140, "head_name": "CTM From Crew", "type": "Allowance", "nature": "Input", "category": "Variable Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 141, "head_name": "GMDSS", "type": "Allowance", "nature": "Input", "category": "Variable Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 142, "head_name": "Hold Cleaning", "type": "Allowance", "nature": "Input", "category": "Variable Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 133, "head_name": "Bonus - Vetting", "type": "Allowance", "nature": "Input", "category": "Variable Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 144, "head_name": "<PERSON><PERSON><PERSON>", "type": "Allowance", "nature": "Input", "category": "Variable Earnings", "is_display_input_sheet": true, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 145, "head_name": "Miscellaneous Credit", "type": "Allowance", "nature": "Input", "category": "Variable Earnings", "is_display_input_sheet": true, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 146, "head_name": "Op Bal - Cr", "type": "Allowance", "nature": "Input", "category": "Variable Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 147, "head_name": "Pandemic Allowance", "type": "Allowance", "nature": "Input", "category": "Variable Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 148, "head_name": "Round Off Credit", "type": "Allowance", "nature": "Input", "category": "Variable Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 149, "head_name": "Seniority Allowance", "type": "Allowance", "nature": "Input", "category": "Variable Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 150, "head_name": "PF Rev", "type": "Allowance", "nature": "Input", "category": "Variable Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 151, "head_name": "Allotment Rev", "type": "Allowance", "nature": "Input", "category": "Variable Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 152, "head_name": "Special Allowance", "type": "Allowance", "nature": "Input", "category": "Variable Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 153, "head_name": "Standby", "type": "Allowance", "nature": "Input", "category": "Variable Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 154, "head_name": "Tank Cleaning", "type": "Allowance", "nature": "Input", "category": "Variable Earnings", "is_display_input_sheet": true, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 155, "head_name": "Travel Allowance", "type": "Allowance", "nature": "Input", "category": "Variable Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 127, "head_name": "S<PERSON><PERSON> Achiever", "type": "Allowance", "nature": "Input", "category": "Variable Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 128, "head_name": "Shoei-PSC-CH", "type": "Allowance", "nature": "Input", "category": "Variable Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 129, "head_name": "Bonus", "type": "Allowance", "nature": "Input", "category": "Variable Earnings", "is_display_input_sheet": true, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 130, "head_name": "Bonus - PSC", "type": "Allowance", "nature": "Input", "category": "Variable Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 131, "head_name": "Bonus - Reefer", "type": "Allowance", "nature": "Input", "category": "Variable Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 132, "head_name": "Bonus - CC", "type": "Allowance", "nature": "Input", "category": "Variable Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 162, "head_name": "Accumulation Payable", "type": "Deduction", "nature": "Input", "category": "Variable Deductions", "is_display_input_sheet": false, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 163, "head_name": "Allotment", "type": "Deduction", "nature": "Input", "category": "Variable Deductions", "is_display_input_sheet": false, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 164, "head_name": "Bonded Stores", "type": "Deduction", "nature": "Input", "category": "Variable Deductions", "is_display_input_sheet": true, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 177, "head_name": "Round Off Debit", "type": "Deduction", "nature": "Input", "category": "Variable Deductions", "is_display_input_sheet": false, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 176, "head_name": "Recovery Excess Wages", "type": "Deduction", "nature": "Input", "category": "Variable Deductions", "is_display_input_sheet": false, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 175, "head_name": "Recovery Excess PF", "type": "Deduction", "nature": "Input", "category": "Variable Deductions", "is_display_input_sheet": false, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 174, "head_name": "Radio Traffic", "type": "Deduction", "nature": "Input", "category": "Variable Deductions", "is_display_input_sheet": true, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 173, "head_name": "Phone Card", "type": "Deduction", "nature": "Input", "category": "Variable Deductions", "is_display_input_sheet": true, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 172, "head_name": "Other Deduction", "type": "Deduction", "nature": "Input", "category": "Variable Deductions", "is_display_input_sheet": true, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 171, "head_name": "<PERSON> Bal - Dr", "type": "Deduction", "nature": "Input", "category": "Variable Deductions", "is_display_input_sheet": false, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 170, "head_name": "Miscellaneous Debit", "type": "Deduction", "nature": "Input", "category": "Variable Deductions", "is_display_input_sheet": true, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 169, "head_name": "Licence Fees", "type": "Deduction", "nature": "Input", "category": "Variable Deductions", "is_display_input_sheet": true, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 168, "head_name": "Family Airfare/Expenses", "type": "Deduction", "nature": "Input", "category": "Variable Deductions", "is_display_input_sheet": false, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 167, "head_name": "Early Sign Off", "type": "Deduction", "nature": "Input", "category": "Variable Deductions", "is_display_input_sheet": false, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 166, "head_name": "Cash On Board", "type": "Deduction", "nature": "Input", "category": "Variable Deductions", "is_display_input_sheet": true, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 165, "head_name": "B<PERSON> Pa<PERSON> from Office", "type": "Deduction", "nature": "Input", "category": "Variable Deductions", "is_display_input_sheet": true, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 178, "head_name": "Union Fees", "type": "Deduction", "nature": "Input", "category": "Variable Deductions", "is_display_input_sheet": false, "default_value": 0, "place_entered": "<PERSON><PERSON><PERSON>", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 109, "head_name": "SWB", "type": "Allowance", "nature": "Monthly", "category": "Standard Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": true, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 88, "head_name": "Basic", "type": "Allowance", "nature": "Monthly", "category": "Standard Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": true, "is_add_to_basic_wages": true, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 89, "head_name": "Bonus", "type": "Allowance", "nature": "Monthly", "category": "Standard Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": true, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 90, "head_name": "Extend Stay", "type": "Allowance", "nature": "Monthly", "category": "Standard Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": true, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 91, "head_name": "Fixed SVA", "type": "Allowance", "nature": "Monthly", "category": "Standard Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": true, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 92, "head_name": "GMDSS", "type": "Allowance", "nature": "Monthly", "category": "Standard Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": true, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 93, "head_name": "Gross Monthly Wages", "type": "Allowance", "nature": "Monthly", "category": "Standard Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": true, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 94, "head_name": "Guranteed Overtime", "type": "Allowance", "nature": "Monthly", "category": "Standard Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": true, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 95, "head_name": "JSU Retirement Pay", "type": "Allowance", "nature": "Monthly", "category": "Standard Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": true, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 96, "head_name": "Longevity", "type": "Allowance", "nature": "Monthly", "category": "Standard Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": true, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 97, "head_name": "Loyalty Bonus", "type": "Allowance", "nature": "Monthly", "category": "Standard Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": true, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 98, "head_name": "Leave Pay", "type": "Allowance", "nature": "Monthly", "category": "Standard Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": true, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 99, "head_name": "Provident Fund - SS All", "type": "Allowance", "nature": "Monthly", "category": "Standard Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": true, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 100, "head_name": "Uniform Allowance", "type": "Allowance", "nature": "Monthly", "category": "Standard Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": true, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 101, "head_name": "Pension Fund", "type": "Allowance", "nature": "Monthly", "category": "Standard Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": true, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 102, "head_name": "R.A.(Retirals)", "type": "Allowance", "nature": "Monthly", "category": "Standard Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": true, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 103, "head_name": "SBM Allowance", "type": "Allowance", "nature": "Monthly", "category": "Standard Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": true, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 104, "head_name": "Service Incentive", "type": "Allowance", "nature": "Monthly", "category": "Standard Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": true, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 105, "head_name": "Sub Allowance", "type": "Allowance", "nature": "Monthly", "category": "Standard Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": true, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 106, "head_name": "Subsistance Allowance", "type": "Allowance", "nature": "Monthly", "category": "Standard Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": true, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 107, "head_name": "SUB-CBA", "type": "Allowance", "nature": "Monthly", "category": "Standard Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": true, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 108, "head_name": "Superior Certificate Allowance", "type": "Allowance", "nature": "Monthly", "category": "Standard Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": true, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 87, "head_name": "Annual All", "type": "Allowance", "nature": "Monthly", "category": "Standard Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": true, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 110, "head_name": "Tank<PERSON>", "type": "Allowance", "nature": "Monthly", "category": "Standard Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": true, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 111, "head_name": "Victualling All", "type": "Allowance", "nature": "Monthly", "category": "Standard Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": true, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 112, "head_name": "<PERSON><PERSON><PERSON> Allowance", "type": "Allowance", "nature": "Monthly", "category": "Standard Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": true, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 113, "head_name": "Trade Allowance", "type": "Allowance", "nature": "Special", "category": "Standard Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": true, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 114, "head_name": "Superiority Allowance", "type": "Allowance", "nature": "Special", "category": "Standard Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": true, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 115, "head_name": "Good Service Bonus", "type": "Allowance", "nature": "Special", "category": "Standard Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": true, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 116, "head_name": "Seniority Allowance", "type": "Allowance", "nature": "Special", "category": "Standard Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": true, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 117, "head_name": "Special Allowance", "type": "Allowance", "nature": "Special", "category": "Standard Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": true, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 161, "head_name": "XCrew O<PERSON>", "type": "Allowance", "nature": "Hourly", "category": "Standard Earnings", "is_display_input_sheet": true, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 181, "head_name": "Airfare/Rep.", "type": "Deduction", "nature": "Input", "category": "Joining Deductions", "is_display_input_sheet": true, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 180, "head_name": "Cash from office", "type": "Deduction", "nature": "Input", "category": "Joining Deductions", "is_display_input_sheet": true, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 179, "head_name": "Visa", "type": "Deduction", "nature": "Input", "category": "Joining Deductions", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 157, "head_name": "US Visa", "type": "Allowance", "nature": "Input", "category": "Joining Allowances", "is_display_input_sheet": true, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 156, "head_name": "Travelling Expenses", "type": "Allowance", "nature": "Input", "category": "Joining Allowances", "is_display_input_sheet": true, "default_value": 0, "place_entered": "Offiice", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 160, "head_name": "Rejoining Bonus", "type": "Allowance", "nature": "Input", "category": "Joining Allowances", "is_display_input_sheet": true, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 159, "head_name": "Other Expenses", "type": "Allowance", "nature": "Input", "category": "Joining Allowances", "is_display_input_sheet": true, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 158, "head_name": "JNG/Repat Expenses", "type": "Allowance", "nature": "Input", "category": "Joining Allowances", "is_display_input_sheet": true, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 123, "head_name": "JSU Retirement Pay Ded", "type": "Deduction", "nature": "Monthly", "category": "Fixed deductions", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 122, "head_name": "Home Allotment", "type": "Deduction", "nature": "Monthly", "category": "Fixed deductions", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 121, "head_name": "Crew Provident Fund", "type": "Deduction", "nature": "Monthly", "category": "Fixed deductions", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 124, "head_name": "JSU Union Fee", "type": "Deduction", "nature": "Monthly", "category": "Fixed deductions", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 125, "head_name": "NIS Union Fee", "type": "Deduction", "nature": "Monthly", "category": "Fixed deductions", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 126, "head_name": "<PERSON><PERSON>", "type": "Deduction", "nature": "Monthly", "category": "Fixed deductions", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": false, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 120, "head_name": "Uniform Allowance EOC", "type": "Allowance", "nature": "Accumulation", "category": "Accumulated Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": true, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 119, "head_name": "Provident Fund - SS All EOC", "type": "Allowance", "nature": "Accumulation", "category": "Accumulated Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": true, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}, {"id": 118, "head_name": "Leave Pay EOC", "type": "Allowance", "nature": "Accumulation", "category": "Accumulated Earnings", "is_display_input_sheet": false, "default_value": 0, "place_entered": "Office", "is_add_to_total_wages": true, "is_add_to_basic_wages": false, "display_order": 1, "status": 1, "created_by": "2022-06-30 19:11:17.445367+08", "updated_by": null}]}]