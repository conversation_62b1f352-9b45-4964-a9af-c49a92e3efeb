export const activePendingVessel = {
  vessel: {
    id: 11,
    name: 'Big Boat',
    status: 'draft',
    pending_status: 'active',
  },
};

export const handedOverPendingVessel = {
  vessel: {
    id: 11,
    name: 'Big Boat',
    status: 'active',
    pending_status: 'handed_over',
  },
};

export const activeButHandedOverNotStartedResponse = {
  vessel: {
    id: 11,
    name: 'Big Boat',
    status: 'active',
    pending_status: null,
  },
};

export const handedOverButArchivalNotStartedResponse = {
  vessel: {
    id: 11,
    name: 'Big Boat',
    status: 'handed_over',
    pending_status: null,
  },
};

export const archivalPendingVessel = {
  vessel: {
    id: 11,
    name: 'Big Boat',
    status: 'handed_over',
    pending_status: 'archived',
  },
};
