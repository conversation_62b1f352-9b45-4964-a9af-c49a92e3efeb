export const MedicalCertificate = [
    {
        "id": 1,
        "value": "ILO medical Cerificate"
    },
    {
        "id": 2,
        "value": "other  medicals (NIS.Panama etc.)"
    },
    {
        "id": 3,
        "value": "TMT"
    },
    {
        "id": 4,
        "value": "Psychometry"
    }
]

export const COCCertificate = [
    {
        "id": 1,
        "value": "Class 1 (Deck)"
    },
    {
        "id": 2,
        "value": "Class 2 (Deck)"
    },
    {
        "id": 3,
        "value": "2nd Mate (F.G)"
    },
    {
        "id": 4,
        "value": "3rd Mate"
    },
    {
        "id": 5,
        "value": "Class 1 (Motor)"
    },
    {
        "id": 6,
        "value": "Class 2 (Motor)"
    },
    {
        "id": 7,
        "value": "Class 4 (Motor)"
    },
    {
        "id": 8,
        "value": "First Asst. Engineer"
    },
    {
        "id": 9,
        "value": "Second Asst. Engineer"
    },
    {
        "id": 10,
        "value": "Third Asst. Engineer"
    },
    {
        "id": 11,
        "value": "Part (A)"
    },
    {
        "id": 12,
        "value": "Class IV (part A)"
    },
    {
        "id": 13,
        "value": "Class 3 (Deck)"
    },
    {
        "id": 14,
        "value": "Class 3 (Motor)"
    },
    {
        "id": 15,
        "value": "Extra Master"
    },
    {
        "id": 16,
        "value": "Extra Chief"
    },
    {
        "id": 17,
        "value": "G - Form"
    },
    {
        "id": 18,
        "value": "Second Class C.O.P."
    },
    {
        "id": 19,
        "value": "Indian Electrician Certificate"
    },
    {
        "id": 20,
        "value": "Master's (HT)"
    },
    {
        "id": 21,
        "value": "N.W.K.O."
    },
    {
        "id": 22,
        "value": "Ordinary Seaman"
    },
    {
        "id": 23,
        "value": "AB"
    },
    {
        "id": 24,
        "value": "Oiler"
    },
    {
        "id": 25,
        "value": "Wiper"
    },
    {
        "id": 26,
        "value": "Messman"
    },
    {
        "id": 27,
        "value": "Electrical Officer"
    },
    {
        "id": 28,
        "value": "Eng. Cadet"
    },
    {
        "id": 29,
        "value": "Deck Cadet"
    },
    {
        "id": 30,
        "value": "Bosun"
    },
    {
        "id": 31,
        "value": "Master (FG)"
    },
    {
        "id": 32,
        "value": "R.T.G."
    },
    {
        "id": 33,
        "value": "First Mate (F.G)"
    },
    {
        "id": 34,
        "value": "2nd Class Ref Eng"
    },
    {
        "id": 35,
        "value": "Carpenter"
    },
    {
        "id": 36,
        "value": "Chief Mate"
    },
    {
        "id": 37,
        "value": "2nd Engineer"
    },
    {
        "id": 38,
        "value": "3rd Engineer"
    },
    {
        "id": 39,
        "value": "Chief Engineer"
    },
    {
        "id": 40,
        "value": "Ship's Welder"
    },
    {
        "id": 41,
        "value": "OS"
    },
    {
        "id": 42,
        "value": "Pump Man"
    },
    {
        "id": 43,
        "value": "GS"
    },
    {
        "id": 44,
        "value": "Engine Officer"
    },
    {
        "id": 45,
        "value": "Chief Cook"
    },
    {
        "id": 46,
        "value": "Steward"
    },
    {
        "id": 47,
        "value": "4th Engineer"
    },
    {
        "id": 48,
        "value": "Fitter"
    },
    {
        "id": 49,
        "value": "Junior Engineer"
    }
]

export const STCWLicence = [
    {
        "id": 1,
        "value": "ADVANCED FIRE FIGHTING"
    },
    {
        "id": 2,
        "value": "SPECIALIZED TANKER SAFETY COURSE"
    },
    {
        "id": 3,
        "value": "CHEMICAL TANKER SAFETY COURSE"
    },
    {
        "id": 4,
        "value": "GAS TANKER SAFETY COURSE"
    },
    {
        "id": 5,
        "value": "AMOS - DOS BASED"
    },
    {
        "id": 6,
        "value": "AMOS - WINDOWS BASED"
    },
    {
        "id": 7,
        "value": "RADAR OBSERVERS COURSE"
    },
    {
        "id": 8,
        "value": "AUTOMATIC RADAR PLOTTING AID"
    },
    {
        "id": 9,
        "value": "RADAR SIMULATOR COURSE"
    },
    {
        "id": 10,
        "value": "BASIC FIRE FIGHTING"
    },
    {
        "id": 11,
        "value": "FIRE PREVENTION AND FIRE FIGHTING"
    },
    {
        "id": 12,
        "value": "SHIP HANDLING SIMULATOR"
    },
    {
        "id": 13,
        "value": "BRIDGE TEAM MANAGEMENT"
    },
    {
        "id": 14,
        "value": "BRIDGE RESOURCE MANAGEMENT"
    },
    {
        "id": 15,
        "value": "COMPUTER FAMILIARISATION COURSE"
    },
    {
        "id": 16,
        "value": "CHEMICAL TANKER FAMILIARISATION COURSE"
    },
    {
        "id": 17,
        "value": "OIL TANKER FAMILIARISATION COURSE"
    },
    {
        "id": 18,
        "value": "GAS TANKER FAMILIARISATION COURSE"
    },
    {
        "id": 19,
        "value": "PROFICIENCY IN SURVIVAL CRAFT"
    },
    {
        "id": 20,
        "value": "PROFICIENCY IN SURVIVAL CRAFT & RESCUE BOAT"
    },
    {
        "id": 21,
        "value": "ELEMENTARY FIRST AID"
    },
    {
        "id": 22,
        "value": "FIRST AID AT SEA"
    },
    {
        "id": 23,
        "value": "MEDICAL FIRST AID"
    },
    {
        "id": 24,
        "value": "MASTER'S MEDICARE COURSE"
    },
    {
        "id": 25,
        "value": "GLOBAL MARITIME DISTRESS SAFETY SYSTEM"
    },
    {
        "id": 26,
        "value": "HAZARDOUS MATERIAL HANDLING COURSE"
    },
    {
        "id": 27,
        "value": "LIQUID CARGO HANDLING SIMULATOR"
    },
    {
        "id": 28,
        "value": "MARITIME ENGLISH COURSE"
    },
    {
        "id": 29,
        "value": "PERSONAL SURVIVAL TECHNIQUES"
    },
    {
        "id": 30,
        "value": "OTHER"
    },
    {
        "id": 31,
        "value": "WATCH KEEPING CERT"
    },
    {
        "id": 32,
        "value": "NAVIGATIONAL CONTROL COURSE"
    },
    {
        "id": 33,
        "value": "PSSR"
    },
    {
        "id": 34,
        "value": "RADAR MAINTENANCE COURSE"
    },
    {
        "id": 35,
        "value": "DECK WATCH KEEPING"
    },
    {
        "id": 36,
        "value": "ENGINE WATCH KEEPING"
    },
    {
        "id": 37,
        "value": "COC REVALIDATION COURSE"
    },
    {
        "id": 38,
        "value": "C/E REFRESHER AND TRAINING COURSE"
    },
    {
        "id": 39,
        "value": "CERTIFICATE OF LIFEBOAT MAN"
    },
    {
        "id": 40,
        "value": "CRUDE OIL WASHING"
    },
    {
        "id": 41,
        "value": "ENGINE ROOM SIMULATOR (OPERATIONAL LEVEL)"
    },
    {
        "id": 42,
        "value": "INERT GAS AND CRUDE OIL WASHING"
    },
    {
        "id": 43,
        "value": "PRE SEA RATING FAMILIARISATION COURSE"
    },
    {
        "id": 44,
        "value": "RESCUE BOAT"
    },
    {
        "id": 45,
        "value": "SURVIVAL AT SEA"
    },
    {
        "id": 46,
        "value": "Pre-Sea Course for Deck Cadets"
    },
    {
        "id": 47,
        "value": "PROFICIENCY IN ELEMENTARY FIRST AID"
    },
    {
        "id": 48,
        "value": "PROFESSIONAL TRAINING"
    },
    {
        "id": 49,
        "value": "SHIP SECURITY OFFICER'S COURSE"
    },
    {
        "id": 50,
        "value": "ENGINE ROOM SIMULATOR (MANAGEMENT LEVEL)"
    },
    {
        "id": 51,
        "value": "TANKER FAMILIARIZATION COURSE"
    },
    {
        "id": 52,
        "value": "BRIDGE TEAM MANAGEMENT - REFRESHER COURSE"
    },
    {
        "id": 53,
        "value": "Security Training for Seafarers with Designated Security Duties"
    },
    {
        "id": 54,
        "value": "High Voltage Safety & Switch Gear Course"
    },
    {
        "id": 55,
        "value": "BRIDGE RESOURCE MANAGEMENT- ON LINE"
    }
]

export const OtherDocumentsType = [
    {
        "id": 1,
        "value": "Yellow Fever Vaccination"
    },
    {
        "id": 2,
        "value": "Cholera Vaccination"
    },
    {
        "id": 3,
        "value": "ISO 14001 Awareness Training"
    },
    {
        "id": 4,
        "value": "ISPS Code - SSO Certificate"
    },
    {
        "id": 5,
        "value": "OHSAS 18001 Awareness Training"
    },
    {
        "id": 6,
        "value": "ISO 9001 Awareness Training"
    },
    {
        "id": 7,
        "value": "Proficient in Swimming"
    },
    {
        "id": 8,
        "value": "EMS"
    },
    {
        "id": 9,
        "value": "5 year award"
    },
    {
        "id": 10,
        "value": "Energy Conservation Course"
    },
    {
        "id": 11,
        "value": "Framo Cargo Pumping System Course"
    },
    {
        "id": 12,
        "value": "Spar Officer Course"
    },
    {
        "id": 13,
        "value": "ECDIS Familiarisation Course"
    },
    {
        "id": 14,
        "value": "WORKSHOP ON SOFT SKILLS"
    },
    {
        "id": 15,
        "value": "INDIAN DATABASE OF SEAFARERS NUMBER (INDOS)"
    },
    {
        "id": 16,
        "value": "REEFER CONTAINER FAMILIARISATION AND MAINTENANCE  COURSE"
    },
    {
        "id": 17,
        "value": "ISM code"
    },
    {
        "id": 18,
        "value": "Ship Security Training"
    },
    {
        "id": 19,
        "value": "PARIS training"
    },
    {
        "id": 20,
        "value": "Ship security officer -upgraded certificate"
    },
    {
        "id": 21,
        "value": "marked for promotion"
    },
    {
        "id": 22,
        "value": "Training in company's EMS"
    },
    {
        "id": 23,
        "value": "HEMPEL Painting Course"
    },
    {
        "id": 24,
        "value": "PPG chemical course"
    },
    {
        "id": 25,
        "value": "VIQ Online"
    },
    {
        "id": 26,
        "value": "Other Seaman Doc"
    }
]

export const OtherCourseType = [
    {
        "id": 1,
        "value": "Other Courses"
    },
    {
        "id": 2,
        "value": "5 E induction check list"
    },
    {
        "id": 3,
        "value": "ADM Training"
    },
    {
        "id": 4,
        "value": "Advanced ME Engine Course"
    },
    {
        "id": 5,
        "value": "Anchoring Procedures"
    },
    {
        "id": 6,
        "value": "Basic ME engine Course"
    },
    {
        "id": 7,
        "value": "Basic Training on High Voltage"
    },
    {
        "id": 8,
        "value": "COLREGS online assessment"
    },
    {
        "id": 9,
        "value": "COVID VACCINE FIRST DOSE"
    },
    {
        "id": 10,
        "value": "COVID VACCINE SECOND DOSE"
    },
    {
        "id": 11,
        "value": "COVID VACCINE SINGLE DOSE (J & J, etc.)"
    },
    {
        "id": 12,
        "value": "COVID-19 CONSENT/SCREENING FORM"
    },
    {
        "id": 13,
        "value": "Cadet - 1 CRA & Application"
    },
    {
        "id": 14,
        "value": "Cadet - 2 Appl Comprehensive"
    },
    {
        "id": 15,
        "value": "Cadet - 3 PJCL"
    },
    {
        "id": 16,
        "value": "Cadet - 4 Orientation prior joining vessel"
    },
    {
        "id": 17,
        "value": "Cadet - 5 ESE EMS SAFER FML Curriculum"
    },
    {
        "id": 18,
        "value": "Cadet - 6 TOEFL"
    },
    {
        "id": 19,
        "value": "Cadet - 7 Loyalty Bonus Agreement"
    },
    {
        "id": 20,
        "value": "Command Orientation Program (Certificate)"
    },
    {
        "id": 21,
        "value": "ECDIS Generic 5d"
    },
    {
        "id": 22,
        "value": "ECDIS Type Specific"
    },
    {
        "id": 23,
        "value": "ECDIS Type Specific JRC"
    },
    {
        "id": 24,
        "value": "ECDIS Type Specific MARIS"
    },
    {
        "id": 25,
        "value": "ECDIS Type Specific TRANSAS"
    },
    {
        "id": 26,
        "value": "EMS Refresher GRB ORB exercise"
    },
    {
        "id": 27,
        "value": "EMS refresher training"
    },
    {
        "id": 28,
        "value": "ESE Refresher Course"
    },
    {
        "id": 29,
        "value": "Ecdis Type Specific Furuno FEA"
    },
    {
        "id": 30,
        "value": "Ecdis Type Specific Furuno FMD"
    },
    {
        "id": 31,
        "value": "Enclosed space entry"
    },
    {
        "id": 32,
        "value": "Engine Rm Simulator for 5/Es"
    },
    {
        "id": 33,
        "value": "Engine Room Resource Management"
    },
    {
        "id": 34,
        "value": "Enhanced ECDIS Workshop"
    },
    {
        "id": 35,
        "value": "Enhanced Navigation Training"
    },
    {
        "id": 36,
        "value": "Environmental Manangement System Course"
    },
    {
        "id": 37,
        "value": "Familairisation on Bilge Water Seperator Oil Content Monitor System"
    },
    {
        "id": 38,
        "value": "HAZMAT"
    },
    {
        "id": 39,
        "value": "HEMPEL Painting Course"
    },
    {
        "id": 40,
        "value": "Hazards of Nitrogen"
    },
    {
        "id": 41,
        "value": "Hydrogen Sulphide Course"
    },
    {
        "id": 42,
        "value": "Incident Investigation and Analysis"
    },
    {
        "id": 43,
        "value": "Korean Maritime Legislation"
    },
    {
        "id": 44,
        "value": "Large Ship Handling Course"
    },
    {
        "id": 45,
        "value": "ME engine course 4 days"
    },
    {
        "id": 46,
        "value": "Maritime Partners in Safety - LET - Q1 2016 Falling Into Water - V0"
    },
    {
        "id": 47,
        "value": "Maritime Partners in Safety - LET - Q1 2017 Engine Failure - V0"
    },
    {
        "id": 48,
        "value": "Maritime Partners in Safety - LET - Q1 2019 Dangerous(enclosed) Sapces V2 - V0"
    },
    {
        "id": 49,
        "value": "Maritime Partners in Safety - LET - Q1 2020 Dry docking - V0"
    },
    {
        "id": 50,
        "value": "Maritime Partners in Safety - LET - Q2 2016 Lifting and Hoisting - V0"
    },
    {
        "id": 51,
        "value": "Maritime Partners in Safety - LET - Q2 2019 Cargo Operation"
    },
    {
        "id": 52,
        "value": "Maritime Partners in Safety - LET - Q3 2016 Personal Injury - V0"
    },
    {
        "id": 53,
        "value": "Maritime Partners in Safety - LET - Q3 2018 Personnel Transfer - V0"
    },
    {
        "id": 54,
        "value": "Maritime Partners in Safety - LET - Q3 2019 Invisible Hazard - V0"
    },
    {
        "id": 55,
        "value": "Maritime Partners in Safety - LET - Q4 2016 Slips, Trips and Falls - V0"
    },
    {
        "id": 56,
        "value": "Maritime Partners in Safety - LET - Q4 2017 STS Operations - V0"
    },
    {
        "id": 57,
        "value": "Maritime Partners in Safety - LET Archive (Pre -Jan 2016)"
    },
    {
        "id": 58,
        "value": "Maritime Partners in Safety - LET- Q2 2017 Lifeboat Operations"
    },
    {
        "id": 59,
        "value": "Maritime Partners in Safety - Reflective Learning - Chronic Unease - V0"
    },
    {
        "id": 60,
        "value": "Maritime Partners in Safety - Reflective Lrng - Collective Normalisation - V0"
    },
    {
        "id": 61,
        "value": "Maritime Partners in Safety - Reflective Lrng - I Keep My Barrier Strong - V0"
    },
    {
        "id": 62,
        "value": "Maritime Partners in Safety - Reflective Lrng - It Will Never Happen To Me! - V0"
    },
    {
        "id": 63,
        "value": "Maritime Partners in Safety - Reflective Lrng - Mooring - V0"
    },
    {
        "id": 64,
        "value": "Maritime Partners in Safety - Resil - Looking at Situations in diffrnt way  - V0"
    },
    {
        "id": 65,
        "value": "Maritime Partners in Safety - Resilience (2017) - Change is part of Living - V1"
    },
    {
        "id": 66,
        "value": "Maritime Partners in Safety - Resilience (2017) – What is Resilience - V1"
    },
    {
        "id": 67,
        "value": "Maritime Partners in Safety - Resilience (2017)- Take Care of Yourself - V1"
    },
    {
        "id": 68,
        "value": "Maritime Partners in Safety - Resilience (2017)- Take Decisive Action - V1"
    },
    {
        "id": 69,
        "value": "Maritime Partners in Safety - Resilience - Change is a Part of Living - V0"
    },
    {
        "id": 70,
        "value": "Maritime Partners in Safety - Resilience - Dealing with a Crisis - v0"
    },
    {
        "id": 71,
        "value": "Maritime Partners in Safety - Resilience - Keep Things in Perspective - V0"
    },
    {
        "id": 72,
        "value": "Maritime Partners in Safety - Resilience - Maintaining a Hopeful Outlook - v0"
    },
    {
        "id": 73,
        "value": "Maritime Partners in Safety - Resilience - Making Connections - v0"
    },
    {
        "id": 74,
        "value": "Maritime Partners in Safety - Resilience - Take Care of Yourself - V0"
    },
    {
        "id": 75,
        "value": "Maritime Partners in Safety - Resilience - Take Decisive Action - V0"
    },
    {
        "id": 76,
        "value": "Maritime Partners in Safety - Resilience - What is Resilience - V0"
    },
    {
        "id": 77,
        "value": "Maritime Resource Management"
    },
    {
        "id": 78,
        "value": "NIS Legislation Course"
    },
    {
        "id": 79,
        "value": "NIS safety Rep on board training report"
    },
    {
        "id": 80,
        "value": "Navigation Awareness for Ratings"
    },
    {
        "id": 81,
        "value": "Navigation Orientation Training (earlier ENT)"
    },
    {
        "id": 82,
        "value": "Navigation Refresher (NAVREF)"
    },
    {
        "id": 83,
        "value": "Navigation Safety campaign"
    },
    {
        "id": 84,
        "value": "Navigation and Command Orientation Training"
    },
    {
        "id": 85,
        "value": "Navigation in Ice"
    },
    {
        "id": 86,
        "value": "Obstruction of Justice"
    },
    {
        "id": 87,
        "value": "Onboard SMS Training"
    },
    {
        "id": 88,
        "value": "Onboard Type Specific Navigation Equipment Training"
    },
    {
        "id": 89,
        "value": "PDOS CHECKLIST"
    },
    {
        "id": 90,
        "value": "PPG Chemical Training Course"
    },
    {
        "id": 91,
        "value": "PPG PaintingTraining Course"
    },
    {
        "id": 92,
        "value": "Passage Planning Course"
    },
    {
        "id": 93,
        "value": "Planning and Reporting Infrastructure Ships"
    },
    {
        "id": 94,
        "value": "Port State Control Course"
    },
    {
        "id": 95,
        "value": "Pre Departure Orientation Seminar (Officer)"
    },
    {
        "id": 96,
        "value": "Pre Departure Orientation Seminar (Rating)"
    },
    {
        "id": 97,
        "value": "Prem Pride Orientation Course"
    },
    {
        "id": 98,
        "value": "Promotion Orientation Program (3/O)"
    },
    {
        "id": 99,
        "value": "Promotion Orientation Program (CO)"
    },
    {
        "id": 100,
        "value": "Promotion Orientation Program (Master)"
    },
    {
        "id": 101,
        "value": "Promotion Orientation Program 3E"
    },
    {
        "id": 102,
        "value": "Promotion Orientation Program CE"
    },
    {
        "id": 103,
        "value": "Promotion Orientation program 2E"
    },
    {
        "id": 104,
        "value": "Promotion orientation program (2O)"
    },
    {
        "id": 105,
        "value": "Risk Assessment Course"
    },
    {
        "id": 106,
        "value": "SAFER Cheques"
    },
    {
        "id": 107,
        "value": "SAFER+ SSW 2014-15 NEW"
    },
    {
        "id": 108,
        "value": "SCBA testing and donning"
    },
    {
        "id": 109,
        "value": "SOLAP"
    },
    {
        "id": 110,
        "value": "STSDSD Course"
    },
    {
        "id": 111,
        "value": "SafeR+ (4 Hr Training - 2012)- OLD"
    },
    {
        "id": 112,
        "value": "SafeR+ TOLAS Course"
    },
    {
        "id": 113,
        "value": "Safety Reach Out"
    },
    {
        "id": 114,
        "value": "Safety officer course"
    },
    {
        "id": 115,
        "value": "Safety officer course"
    },
    {
        "id": 116,
        "value": "Seagull - Navigation Test"
    },
    {
        "id": 117,
        "value": "Seagull NIS safety rep Assesment Sheet"
    },
    {
        "id": 118,
        "value": "Seagull SHE Form"
    },
    {
        "id": 119,
        "value": "Seagull SHE certificate"
    },
    {
        "id": 120,
        "value": "Seagull Test Report"
    },
    {
        "id": 121,
        "value": "Specialised Training Programme on Chemical Tanker Operations"
    },
    {
        "id": 122,
        "value": "Specialized Training for Oil and Chemical Tanker Cargo and Safety Operations"
    },
    {
        "id": 123,
        "value": "Spectrometer Course"
    },
    {
        "id": 124,
        "value": "Steering Assessment"
    },
    {
        "id": 125,
        "value": "Tanker Vetting Inspection Co"
    },
    {
        "id": 126,
        "value": "Training in Company's EMS for rating"
    },
    {
        "id": 127,
        "value": "Tristar Liferaft training"
    },
    {
        "id": 128,
        "value": "VIQ Online"
    },
    {
        "id": 129,
        "value": "VROON Safety Induction Training"
    },
    {
        "id": 130,
        "value": "Volatile Organic Compounds Management Course(VOC)"
    },
    {
        "id": 131,
        "value": "Wall Wash Test Course"
    }
];

export const PreSeaTraningInstitute = [
    {
        "id": 1,
        "value": "Applied Research International (ARI), New Delhi"
    },
    {
        "id": 2,
        "value": "Acadamy of Marine Education & Training (AMET), Chennai"
    },
    {
        "id": 3,
        "value": "Chidambaram Institute of Maritime Technology, Chennai"
    },
    {
        "id": 4,
        "value": "Cochin Shipyard Limited, Kochi"
    },
    {
        "id": 5,
        "value": "Coimbatore Marine College, Coimbatore"
    },
    {
        "id": 6,
        "value": "Neotia Institute of Technolgy, Management & Science (NITMAS), Kolkata"
    },
    {
        "id": 7,
        "value": "Maharashtra Academy of Naval Education and Training, Pune"
    },
    {
        "id": 8,
        "value": "IMU Kolkata (MERI)"
    },
    {
        "id": 9,
        "value": "Naval Maritime Academy"
    },
    {
        "id": 10,
        "value": "RL Institute of Nautical Sciences, Madurai"
    },
    {
        "id": 11,
        "value": "Sri Venkateshwara College of Engg"
    },
    {
        "id": 12,
        "value": "Tolani Maritime Institute"
    },
    {
        "id": 13,
        "value": "Vels Acadamy of Maritime Education & Training"
    },
    {
        "id": 14,
        "value": "Southern Academy of Maritime Studies (SAMS), Chennai"
    },
    {
        "id": 15,
        "value": "Eurotech Maritime Academy, Kochi"
    },
    {
        "id": 16,
        "value": "TS Rahman"
    },
    {
        "id": 17,
        "value": "TS Chanakya"
    },
    {
        "id": 18,
        "value": "Other"
    },
    {
        "id": 19,
        "value": "TS Rajendra"
    },
    {
        "id": 20,
        "value": "Sailor's Maritime Academy, Vizianagaram (AP)"
    },
    {
        "id": 21,
        "value": "International Maritime Academy, Chennai"
    },
    {
        "id": 22,
        "value": "Praveenya Institute of Marine Engg and Maritime Studies, Vizag"
    },
    {
        "id": 23,
        "value": "Garden Reach Ship Builders & Engineers Ltd (GRSE) Kolkata"
    },
    {
        "id": 24,
        "value": "IMI, Noida"
    },
    {
        "id": 25,
        "value": "Aquatech Institute of Maritime Studies, New Delhi"
    },
    {
        "id": 26,
        "value": "YAK, Khopoli"
    },
    {
        "id": 27,
        "value": "STET Maritime, Singapore"
    },
    {
        "id": 28,
        "value": "B.P.Marine Academy, Navi Mumbai"
    },
    {
        "id": 29,
        "value": "Mumbai Maritime Training Institute (MMTI), Khopoli"
    },
    {
        "id": 30,
        "value": "Haldia Institute of Maritime Studies & Research (HIMSAR), Kolkata"
    },
    {
        "id": 31,
        "value": "(CIFNET) Central Institute of Fisheries Nautical & Engg Training, Kochi"
    },
    {
        "id": 32,
        "value": "IMU Chennai Campus"
    },
    {
        "id": 33,
        "value": "Sriram Institute of Marine Studies (SIMS), New Delhi"
    },
    {
        "id": 34,
        "value": "IMU Kandla Campus"
    },
    {
        "id": 35,
        "value": "IMU Kochi Campus"
    },
    {
        "id": 36,
        "value": "Sai Ram Shipping Science Institute, Puducherry"
    },
    {
        "id": 37,
        "value": "Cosmopolitan Technology of Maritime, Chennai"
    },
    {
        "id": 38,
        "value": "Perunthalaivar Kamarajar Inst of Maritime Sci & Engg (PKIMSE), Chidambaram"
    },
    {
        "id": 39,
        "value": "Hindustan Institute Of Maritime Training, Chennai"
    },
    {
        "id": 40,
        "value": "IMU Visakhapatnam Campus"
    },
    {
        "id": 41,
        "value": "Marine Officers Training Academy, Pondicherry"
    },
    {
        "id": 42,
        "value": "R.A.Pandey Marine Academy, Mumbai"
    },
    {
        "id": 43,
        "value": "IMU Mumbai Port Campus"
    },
    {
        "id": 44,
        "value": "Maritime Foundation , Chennai"
    },
    {
        "id": 45,
        "value": "CV RAMAN Institute of Engineering"
    },
    {
        "id": 46,
        "value": "VISVESVARAYA TECHNICAL UNIVERSITY"
    },
    {
        "id": 47,
        "value": "GKM College of Engineering"
    },
    {
        "id": 48,
        "value": "CHENNAI SCHOOL OF SHIP MANAGEMENT"
    },
    {
        "id": 49,
        "value": "SRI NANDHANAM MARITIME ACADEMY"
    },
    {
        "id": 50,
        "value": "Hindustan Shipyard Ltd.,Visakhapatnam"
    },
    {
        "id": 51,
        "value": "The Great Eastern Institute of Maritime Studies, Lonavala"
    },
    {
        "id": 52,
        "value": "The Great Eastern Cadet Academy (T S Jawahar) Mumbai"
    },
    {
        "id": 53,
        "value": "MTI (SCI), Powai"
    },
    {
        "id": 54,
        "value": "BPT Fosma"
    },
    {
        "id": 55,
        "value": "NIPM Madras"
    },
    {
        "id": 56,
        "value": "Mazagaon Docks Ltd"
    },
    {
        "id": 57,
        "value": "Bombay Institute of Adv. Maritime Studies (BIAMS), Mumbai"
    },
    {
        "id": 58,
        "value": "Trident College of Marine Technology (TCMT), W.Bengal"
    },
    {
        "id": 59,
        "value": "Institute of Maritime Studies (IIMS), Goa"
    },
    {
        "id": 60,
        "value": "Don Bosco Normar Maritime Academy, Mumbai"
    },
    {
        "id": 61,
        "value": "United Marine Academy, Navi Mumbai"
    },
    {
        "id": 62,
        "value": "Sea Horse Academy of Merchant Navy, Kakinada, A.P."
    },
    {
        "id": 63,
        "value": "Indian Institute of Planning & Management (IIPM), Chennai"
    },
    {
        "id": 64,
        "value": "Dr B R Ambedkar Institute of Technology (DBRAIT), Andaman & Nicobar Is"
    },
    {
        "id": 65,
        "value": "PMMA (Philippine Merchant Marine Academy)"
    },
    {
        "id": 66,
        "value": "(MAAP) Maritime Academy of Asia and the Pacific"
    },
    {
        "id": 67,
        "value": "Shirdi Sai Nautical Science Academy, Bengalure"
    },
    {
        "id": 68,
        "value": "Nimbus Maritime Academy"
    },
    {
        "id": 69,
        "value": "Great eastern institute maritime studies"
    },
    {
        "id": 70,
        "value": "LIMA (LYCEUM INT MARITIME ACADEMY), BATANGAS, PHILIPPINES"
    },
    {
        "id": 71,
        "value": "Singapore Maritime Academy (SMA), Singapore"
    },
    {
        "id": 72,
        "value": "Seacom Marine College, Howrah"
    }
]

export const PreSeaTrainingCourse = [
    {
        "id": 1,
        "value": "Applied Research International (ARI), New Delhi"
    },
    {
        "id": 2,
        "value": "Acadamy of Marine Education & Training (AMET), Chennai"
    },
    {
        "id": 3,
        "value": "Chidambaram Institute of Maritime Technology, Chennai"
    },
    {
        "id": 4,
        "value": "Cochin Shipyard Limited, Kochi"
    },
    {
        "id": 5,
        "value": "Coimbatore Marine College, Coimbatore"
    },
    {
        "id": 6,
        "value": "Neotia Institute of Technolgy, Management & Science (NITMAS), Kolkata"
    },
    {
        "id": 7,
        "value": "Maharashtra Academy of Naval Education and Training, Pune"
    },
    {
        "id": 8,
        "value": "IMU Kolkata (MERI)"
    },
    {
        "id": 9,
        "value": "Naval Maritime Academy"
    },
    {
        "id": 10,
        "value": "RL Institute of Nautical Sciences, Madurai"
    },
    {
        "id": 11,
        "value": "Sri Venkateshwara College of Engg"
    },
    {
        "id": 12,
        "value": "Tolani Maritime Institute"
    },
    {
        "id": 13,
        "value": "Vels Acadamy of Maritime Education & Training"
    },
    {
        "id": 14,
        "value": "Southern Academy of Maritime Studies (SAMS), Chennai"
    },
    {
        "id": 15,
        "value": "Eurotech Maritime Academy, Kochi"
    },
    {
        "id": 16,
        "value": "TS Rahman"
    },
    {
        "id": 17,
        "value": "TS Chanakya"
    },
    {
        "id": 18,
        "value": "Other"
    },
    {
        "id": 19,
        "value": "TS Rajendra"
    },
    {
        "id": 20,
        "value": "Sailor's Maritime Academy, Vizianagaram (AP)"
    },
    {
        "id": 21,
        "value": "International Maritime Academy, Chennai"
    },
    {
        "id": 22,
        "value": "Praveenya Institute of Marine Engg and Maritime Studies, Vizag"
    },
    {
        "id": 23,
        "value": "Garden Reach Ship Builders & Engineers Ltd (GRSE) Kolkata"
    },
    {
        "id": 24,
        "value": "IMI, Noida"
    },
    {
        "id": 25,
        "value": "Aquatech Institute of Maritime Studies, New Delhi"
    },
    {
        "id": 26,
        "value": "YAK, Khopoli"
    },
    {
        "id": 27,
        "value": "STET Maritime, Singapore"
    },
    {
        "id": 28,
        "value": "B.P.Marine Academy, Navi Mumbai"
    },
    {
        "id": 29,
        "value": "Mumbai Maritime Training Institute (MMTI), Khopoli"
    },
    {
        "id": 30,
        "value": "Haldia Institute of Maritime Studies & Research (HIMSAR), Kolkata"
    },
    {
        "id": 31,
        "value": "(CIFNET) Central Institute of Fisheries Nautical & Engg Training, Kochi"
    },
    {
        "id": 32,
        "value": "IMU Chennai Campus"
    },
    {
        "id": 33,
        "value": "Sriram Institute of Marine Studies (SIMS), New Delhi"
    },
    {
        "id": 34,
        "value": "IMU Kandla Campus"
    },
    {
        "id": 35,
        "value": "IMU Kochi Campus"
    },
    {
        "id": 36,
        "value": "Sai Ram Shipping Science Institute, Puducherry"
    },
    {
        "id": 37,
        "value": "Cosmopolitan Technology of Maritime, Chennai"
    },
    {
        "id": 38,
        "value": "Perunthalaivar Kamarajar Inst of Maritime Sci & Engg (PKIMSE), Chidambaram"
    },
    {
        "id": 39,
        "value": "Hindustan Institute Of Maritime Training, Chennai"
    },
    {
        "id": 40,
        "value": "IMU Visakhapatnam Campus"
    },
    {
        "id": 41,
        "value": "Marine Officers Training Academy, Pondicherry"
    },
    {
        "id": 42,
        "value": "R.A.Pandey Marine Academy, Mumbai"
    },
    {
        "id": 43,
        "value": "IMU Mumbai Port Campus"
    },
    {
        "id": 44,
        "value": "Maritime Foundation , Chennai"
    },
    {
        "id": 45,
        "value": "CV RAMAN Institute of Engineering"
    },
    {
        "id": 46,
        "value": "VISVESVARAYA TECHNICAL UNIVERSITY"
    },
    {
        "id": 47,
        "value": "GKM College of Engineering"
    },
    {
        "id": 48,
        "value": "CHENNAI SCHOOL OF SHIP MANAGEMENT"
    },
    {
        "id": 49,
        "value": "SRI NANDHANAM MARITIME ACADEMY"
    },
    {
        "id": 50,
        "value": "Hindustan Shipyard Ltd.,Visakhapatnam"
    },
    {
        "id": 51,
        "value": "The Great Eastern Institute of Maritime Studies, Lonavala"
    },
    {
        "id": 52,
        "value": "The Great Eastern Cadet Academy (T S Jawahar) Mumbai"
    },
    {
        "id": 53,
        "value": "MTI (SCI), Powai"
    },
    {
        "id": 54,
        "value": "BPT Fosma"
    },
    {
        "id": 55,
        "value": "NIPM Madras"
    },
    {
        "id": 56,
        "value": "Mazagaon Docks Ltd"
    },
    {
        "id": 57,
        "value": "Bombay Institute of Adv. Maritime Studies (BIAMS), Mumbai"
    },
    {
        "id": 58,
        "value": "Trident College of Marine Technology (TCMT), W.Bengal"
    },
    {
        "id": 59,
        "value": "Institute of Maritime Studies (IIMS), Goa"
    },
    {
        "id": 60,
        "value": "Don Bosco Normar Maritime Academy, Mumbai"
    },
    {
        "id": 61,
        "value": "United Marine Academy, Navi Mumbai"
    },
    {
        "id": 62,
        "value": "Sea Horse Academy of Merchant Navy, Kakinada, A.P."
    },
    {
        "id": 63,
        "value": "Indian Institute of Planning & Management (IIPM), Chennai"
    },
    {
        "id": 64,
        "value": "Dr B R Ambedkar Institute of Technology (DBRAIT), Andaman & Nicobar Is"
    },
    {
        "id": 65,
        "value": "PMMA (Philippine Merchant Marine Academy)"
    },
    {
        "id": 66,
        "value": "(MAAP) Maritime Academy of Asia and the Pacific"
    },
    {
        "id": 67,
        "value": "Shirdi Sai Nautical Science Academy, Bengalure"
    },
    {
        "id": 68,
        "value": "Nimbus Maritime Academy"
    },
    {
        "id": 69,
        "value": "Great eastern institute maritime studies"
    },
    {
        "id": 70,
        "value": "LIMA (LYCEUM INT MARITIME ACADEMY), BATANGAS, PHILIPPINES"
    },
    {
        "id": 71,
        "value": "Singapore Maritime Academy (SMA), Singapore"
    },
    {
        "id": 72,
        "value": "Seacom Marine College, Howrah"
    }
]