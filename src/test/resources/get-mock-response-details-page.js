export const getSeafarerMockResponse = {
  seafarer_person_id: 10828,
  rank_id: 12,
  id: 10828,
  hkid: 1970,
  created_at: '2021-04-29T06:08:45.539Z',
  updated_at: '2022-05-06T06:37:30.105Z',
  ref_id: 1684,
  office_id: 33,
  manning_agent_id: null,
  not_to_be_employed: false,
  not_to_be_employed_reason: null,
  framo_experience: false,
  ice_conditions_experience: null,
  show_cargo_experience: false,
  show_fml_experience: true,
  cargo_experience: null,
  additional_experience: null,
  parent_hkid: null,
  is_parent: null,
  data_quality: 'mandatory_data_missing',
  replaced_by_id: null,
  is_only_worked_fml: null,
  seafarer_manning_agent: null,
  replaced_by: null,
  seafarer_person: {
    id: 10828,
    current_account_status: 'active',
    current_journey_status: 'signed_on',
    current_exam_status: null,
    created_by_hash: 'hJolEflCGlFhFIaJHtvSXA==',
    created_at: '2021-04-29T06:08:45.539Z',
    updated_at: '2022-03-30T08:32:20.533Z',
    screening_status: 'passed',
    created_by: 'PARIS1.0 - Sync',
    created_by_username: 'paris1',
    updated_by: null,
    updated_by_username: null,
    first_name: 'De** Na**',
    middle_name: 'Ra**',
    last_name: 'Ch**',
    date_of_birth: '1973-03-20T00:00:00.000Z',
    gender: 'male',
    place_of_birth: 'Jokhan Khas',
    country_of_birth_id: null,
    nationality_id: 105,
    height: 0,
    weight: 0,
    overall_size: 0,
    tshirt_size: 0,
    jacket_size: 0,
    shoe_size: 0,
    smoking: 'unknown',
    vegetarian: 'no',
    nearest_airport: null,
    marital_status: 'married',
    surname_of_spouse: 'SU**',
    name_of_spouse: 'DE**',
    number_of_children: 2,
    children_names: 'RA** CH** SO** SU** CH** DA**',
    photo_path: '/ui2021/1616145675_DEENA_CHAUHAN.jpg',
    updated_by_hash: null,
    updated_at_p1: '2021-06-26T17:13:11.890Z',
    created_at_p1: '2021-06-26T17:13:11.890Z',
    country_of_birth: {
      value: 'India',
    },
    nationality: {
      id: 105,
      alpha2_code: 'IN',
      alpha3_code: 'IND',
      value: 'Indian',
      ref_id: null,
    },
    bank_accounts: [
      {
        id: 24737,
        created_at: '2021-10-09T14:06:35.067Z',
        updated_at: '2021-10-09T14:06:35.067Z',
        seafarer_person_id: 10828,
        is_primary_payroll_account: false,
        seafarer_is_account_holder: true,
        relationship_with_beneficiary: null,
        account_holder_first_name: null,
        account_holder_middle_name: null,
        account_holder_last_name: null,
        account_holder_date_of_birth: null,
        account_holder_gender: null,
        account_holder_nationality_id: null,
        account_holder_address_id: null,
        number: null,
        bank_name: null,
        bank_address_id: 54392,
        ifsc_number: null,
        swift_code: null,
        iban_number: null,
        account_holder_nationality: null,
        account_holder_address: null,
        bank_address: {
          id: 54392,
          created_at: '2021-10-09T14:06:34.809Z',
          updated_at: '2021-10-09T14:06:34.808Z',
          postal_zip_code: null,
          country_id: 13,
          address1: null,
          address2: null,
          address3: null,
          address4: null,
        },
        document: [],
      },
    ],
    passports: [
      {
        id: 42174,
        created_at: '2021-10-09T14:06:34.809Z',
        updated_at: '2021-10-09T14:06:34.809Z',
        seafarer_person_id: 10828,
        number: 'P 03**',
        place_of_issue: 'Lu**',
        country_id: null,
        date_of_issue: '2014-09-25T00:00:00.000Z',
        date_of_expiry: '2028-07-18T00:00:00.000Z',
        ref_id: 8798,
        doc_path: '/ui2021/1615182863_Fleet_20210308_111555.pdf',
        country: null,
        document: [
          {
            id: 16224,
            name: '1615182863_Fleet_20210308_111555.pdf',
            created_at: '2021-10-09T14:06:35.325Z',
            updated_at: '2021-10-09T14:06:35.325Z',
            mime: null,
          },
        ],
      },
    ],
    photos: [
      {
        id: 10462,
        name: '1014203638_DeenaNChauhan.jpg',
        created_at: '2021-08-10T01:40:48.344Z',
        updated_at: '2021-08-10T01:40:48.344Z',
        mime: null,
      },
      {
        id: 32055,
        name: '1616145675_DEENA_CHAUHAN.jpg',
        created_at: '2021-10-09T14:06:35.636Z',
        updated_at: '2021-10-09T14:06:35.636Z',
        mime: null,
      },
    ],
    seaman_books: [
      {
        id: 66709,
        created_at: '2021-10-09T14:06:34.809Z',
        updated_at: '2021-10-09T14:06:34.809Z',
        seafarer_person_id: 10828,
        country_id: 103,
        number: 'MU** 10**',
        date_of_issue: '2018-11-09T00:00:00.000Z',
        date_of_expiry: '2028-10-16T00:00:00.000Z',
        is_original: false,
        port_of_issue: 'MU**',
        ref_id: 46942,
        doc_path: '/ui2021/1615182964_Fleet_20210308_111642.pdf',
        country: {
          id: 103,
          alpha2_code: 'IN',
          alpha3_code: 'IND',
          value: 'India',
          numeric_code: 356,
        },
        document: [
          {
            id: 30789,
            name: '1615182964_Fleet_20210308_111642.pdf',
            created_at: '2021-10-09T14:06:35.442Z',
            updated_at: '2021-10-09T14:06:35.442Z',
            mime: null,
          },
        ],
      },
      {
        id: 66710,
        created_at: '2021-10-09T14:06:34.809Z',
        updated_at: '2021-10-09T14:06:34.809Z',
        seafarer_person_id: 10828,
        country_id: null,
        number: 'MH**',
        date_of_issue: '2015-02-10T00:00:00.000Z',
        date_of_expiry: '2021-09-20T00:00:00.000Z',
        is_original: false,
        port_of_issue: 'Ma** Is**',
        ref_id: 783194,
        doc_path: '/ui2021/1626930955_1._FITTER_MI_CDC.pdf',
        country: null,
        document: [
          {
            id: 30790,
            name: '1626930955_1._FITTER_MI_CDC.pdf',
            created_at: '2021-10-09T14:06:35.547Z',
            updated_at: '2021-10-09T14:06:35.547Z',
            mime: null,
          },
        ],
      },
    ],
    seafarer_contacts: [
      {
        id: 71214,
        created_at: '2021-10-09T14:06:34.809Z',
        updated_at: '2021-10-09T14:06:34.809Z',
        seafarer_person_id: 10828,
        contact: '79**',
        contact_type: 'mobile_number',
      },
      {
        id: 71215,
        created_at: '2021-10-09T14:06:34.809Z',
        updated_at: '2021-10-09T14:06:34.809Z',
        seafarer_person_id: 10828,
        contact: 'd***@g***',
        contact_type: 'email',
      },
    ],
    addresses: [
      {
        id: 87503,
        created_at: '2021-10-09T14:06:34.857Z',
        updated_at: '2021-10-09T14:06:34.857Z',
        seafarer_person_id: 10828,
        postal_zip_code: '274001',
        country_id: null,
        state: 'UTTAR PRADESH.',
        city: 'DEORIA',
        building: null,
        other_address: 'JO** KH** CH** JO** KH** RU** DE** UT** PR**',
        country: null,
      },
      {
        id: 87504,
        created_at: '2021-10-09T14:06:34.857Z',
        updated_at: '2021-10-09T14:06:34.857Z',
        seafarer_person_id: 10828,
        postal_zip_code: '274001',
        country_id: null,
        state: null,
        city: 'DEORIA',
        building: null,
        other_address: 'JO** KH** CH** JO** KH** RU** DE** UT** PR**',
        country: null,
      },
    ],
    family_members: [
      {
        id: 31069,
        created_at: '2021-10-09T14:06:35.067Z',
        updated_at: '2021-10-09T14:06:35.067Z',
        seafarer_person_id: 10828,
        name: null,
        surname: 'SU** DE**',
        relationship: 'Wife',
        telephone: null,
        mobilephone: '95**',
        email: null,
        address_id: 54393,
        address: {
          id: 54393,
          created_at: '2021-10-09T14:06:34.809Z',
          updated_at: '2021-10-09T14:06:34.809Z',
          postal_zip_code: '274001',
          country_id: null,
          address1: null,
          address2: 'DEORIA',
          address3: null,
          address4: 'JO** KH** CH** JO** KH** RU** DE** UT** PR**',
        },
      },
    ],
    seafarer_account_status: [
      {
        id: 1281,
        seafarer_person_id: 10828,
        status: 'active',
        created_by: 'Ci** La**',
        p1_user_email: '<EMAIL>',
        created_at: '2001-06-22T16:00:00.000Z',
        updated_at: '2001-06-22T16:00:00.000Z',
      },
    ],
    seafarer_status_history: [
      {
        vessel_name: 'African Turaco',
        is_current_status: true,
        embarkation_port: 'chile',
        repatriation_port: 'chile',
        expected_contract_end_date: '2024-10-01T00:00:00.000Z',
        expected_contract_start_date: '2024-03-05T00:00:00.000Z',
      },
    ],
    created_by_user_info: {},
    updated_by_user_info: null,
  },
  seafarer_rank: {
    id: 12,
    value: 'POEN',
    unit: 'POEN',
    ref_id: 2000348,
  },
  seafarer_reporting_office: {
    id: 33,
    value: 'Mumbai - Nerul',
    unit: 'BOM',
    ref_id: 2021752,
    ship_party_id: null,
  },
  seafarer_contact_log: [],
  seafarer_experience: [
    {
      seafarer_id: 10828,
      duration_on_all_vessel_type: '5013',
      duration_on_current_vessel_tour: '324',
      duration_with_company: '5013',
      rank__2nd_cook: null,
      rank__2nd_engineer: null,
      rank__2nd_officer: null,
      rank__3rd_cook: null,
      rank__3rd_engineer: null,
      vessel_type__accommodation_repair_vessel: null,
      vessel_type__aframax_crude_tanker: null,
      vessel_type__aframax_crude_tanker_with_lng_as_alternate_fuel: null,
      vessel_type__anchor_handling_tug: null,
      vessel_type__bitumen_tanker: null,
      vessel_type__bulk_carrier: '1059',
      'rank__1st_mate(f.g)': null,
      'rank__3rd_engineer_/_electrical_officer': null,
      rank__3rd_officer: null,
      rank__4th_engineer: null,
      'rank__4_th_engineer_/_electrical_officer': null,
      rank__5th_engineer: null,
      rank__ab: null,
      rank__additional_2nd_engineer: null,
      rank__additional_2nd_officer: null,
      rank__additional_3rd_engineer: null,
      rank__additional_3rd_officer: null,
      rank__additional_4th_engineer: null,
      rank__additional_chief_engineer: null,
      rank__additional_master: null,
      'rank__addl_ch/offr': null,
      rank__admin_officer: null,
      rank__assistant_electrical_officer: null,
      'rank__asst._3rd_engineer': null,
      rank__bosun: null,
      rank__cadet: null,
      rank__cargo_officer: null,
      rank__carpenter: null,
      rank__chief_cook: null,
      rank__chief_engineer: null,
      rank__chief_officer: null,
      rank__deck_cadet: null,
      rank__deck_trainee: null,
      rank__electrical_officer: null,
      rank__electrical_officer_cum_reefer_engineer: null,
      rank__electrician: null,
      rank__electro_technical_officer: null,
      rank__electro_technical_rating: null,
      rank__engine_cadet: null,
      rank__engine_fitter: null,
      rank__engine_room_artificer: null,
      rank__engine_trainee: null,
      rank__fitter: '2044',
      rank__gas_engineer: null,
      rank__gp: null,
      rank__gp2: null,
      rank__gs: null,
      rank__junior_engineer: null,
      rank__junior_officer: null,
      rank__marine_superintendent: null,
      rank__master: null,
      rank__messman: null,
      rank__mm: null,
      rank__naval_architect: null,
      rank__oiler: null,
      rank__os: null,
      rank__painter: null,
      rank__poct: null,
      rank__poen: null,
      rank__port_captain: null,
      rank__pump_man: null,
      rank__radio_officer: null,
      'rank__reefer_eng.': null,
      rank__rpcl: null,
      rank__rpfw: '1572',
      rank__senior_cadet: null,
      rank__sms_trainer: null,
      rank__supt: null,
      rank__supy: null,
      rank__technical_superintendent: null,
      rank__technician: null,
      rank__trainee_2nd_engineer: null,
      rank__trainee_chief_officer: null,
      rank__trainee_cook: null,
      rank__trainee_electrical_officer: null,
      rank__trainee_fitter: null,
      rank__trainee_gs: null,
      rank__trainee_marine_engineer: null,
      rank__trainee_master: null,
      rank__trainee_os: null,
      'rank__trainee_reefer_eng.': null,
      rank__trainees: null,
      rank__trainee_wpr: null,
      rank__welder: '1397',
      rank__wiper: null,
      vessel_type__bunkering_tanker: null,
      vessel_type__cape_size_vessel: null,
      vessel_type__car_carrier: null,
      vessel_type__cement_carrier: null,
      vessel_type__chemical_tanker: null,
      vessel_type__container_vessel: '1586',
      vessel_type__container_with_gantry: null,
      vessel_type__crude_oil_tanker: '1011',
      vessel_type__diving_support_vessel: null,
      vessel_type__dredger: null,
      vessel_type__drill_ship: null,
      vessel_type__floating_storage_offloading: null,
      vessel_type__gantry: null,
      vessel_type__gas_and_chemical_tanker: null,
      vessel_type__gas_tanker: null,
      vessel_type__general_cargo: '133',
      vessel_type__heavy_lift_vessel: null,
      vessel_type__livestock_carrier: null,
      vessel_type__lng: null,
      'vessel_type__log/bulk_carrier': null,
      vessel_type__log_carrier: null,
      'vessel_type__lpg_+_ammonia': null,
      vessel_type__lpg_carrier: null,
      vessel_type__mpsv: null,
      vessel_type__multi_purpose: null,
      'vessel_type__multi_purpose_-_container': null,
      vessel_type__oil_bulk_ore_carrier: null,
      vessel_type__oil_cum_chemical_tanker: null,
      vessel_type__oil_tanker: '844',
      vessel_type__open_hatch_bulk_carrier: null,
      vessel_type__other: '69',
      vessel_type__panamax_container_vessel: null,
      vessel_type__passenger_vessel: null,
      vessel_type__platform_supply_vessel: null,
      vessel_type__post_panamax_container_vessel: null,
      vessel_type__product_cum_chemical_tanker: null,
      vessel_type__product_tanker: null,
      vessel_type__pure_car_carrier: null,
      'vessel_type__pure_car_/_truck_carrier': null,
      vessel_type__reefer_cum_container: null,
      vessel_type__reefer_vessel: '311',
      vessel_type__roro: null,
      'vessel_type__roro_/_passenger_ferry': null,
      vessel_type__seismic_survey_vessel: null,
      vessel_type__self_unloader: null,
      vessel_type__suez_max_tanker: null,
      vessel_type__supply_vessel: null,
      vessel_type__survey_vessel: null,
      vessel_type__tugboat: null,
      vessel_type__ulcc: null,
      vessel_type__very_large_ore_carrier: null,
      vessel_type__vlcc: null,
      vessel_type__wood_chip_carrier: null,
    },
  ],
};

export const getSeafarerStatusMockResponse = [
  {
    id: 103,
    seafarer_person_id: 10828,
    seafarer_account_status: 'active',
    seafarer_journey_status: 'signed_on',
    seafarer_exam_status: null,
    rank_id: 12,
    vessel_name: 'TRF Miami',
    vessel_ref_id: 4426,
    created_by_hash: '<EMAIL>',
    created_by: 'Sh** Ma**',
    seafarer_journey_remarks: 'Onboard Sign On',
    seafarer_exam_remarks: null,
    status_date: '2021-06-26T00:00:00.000Z',
    vessel_ownership_id: 103,
    vessel_id: 206,
    sign_off_date: null,
    expected_contract_end_date: null,
    embarkation_port: null,
    repatriation_port: null,
    created_at: '2022-03-30T08:32:20.432Z',
    updated_at: '2022-03-30T08:32:20.432Z',
    created_at_p1: null,
    updated_at_p1: null,
    paris1_ref_id: null,
    is_current_status: true,
  },
];
