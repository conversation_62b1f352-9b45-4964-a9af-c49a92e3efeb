import React from 'react';
import { MemoryRouter, Route } from 'react-router-dom';
import { render, waitFor, screen, fireEvent, act } from '@testing-library/react';
import AddSeafarePage from '../../pages/AddSeafarerPage';
import seafarerService from '../../service/seafarer-service';
import '@testing-library/jest-dom';
import { getMockedSeafarerErrorResponse } from '../resources/seafarer-mock-data';
import { AccessProvider } from '@src/component/common/Access';

jest.mock('../../styleGuide');
jest.mock('../../service/user-service');
jest.mock('../../controller/add-seafarer-controller');

describe('AddSeafarerBasicForm validation', () => {
  beforeEach(async () => {
    jest.setTimeout(100000);
    seafarerService.getSeafarer = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: getMockedSeafarerErrorResponse() }));
    await act(async () => {
      render(
        <MemoryRouter initialEntries={['/seafarer/1/add/basic']}>
          <Route path="/seafarer/:seafarerId?/add/:step?">
            <AccessProvider config={{
              seafarer: {
                addSeafarer: true,
                editSeafarer: true,
                edit: {
                  personalDetails: true,
                  passport: true,
                  seamansBook: true,
                },
                create: {},
                hidden: {
                  bankAccount: false,
                },
              },
            }}>
              <AddSeafarePage />
            </AccessProvider>
          </Route>
        </MemoryRouter>,
      );
    });
  });

  it('should have passport number warning', async () => {
    await waitFor(() => {
      const passport = screen.getAllByTestId('passport-number');
      expect(passport[0]).toBeInTheDocument();
      const errorMsg = screen.getAllByText('Please enter a valid Passport Number.');
      expect(errorMsg[0]).toBeInTheDocument();
    });
  });

  it('should have throw error message when date of Expiry greater than date of issue in passport', async () => {
    await waitFor(() => {
      const dateOfIssue = screen.getAllByTestId('fml-passport-dateofissue');
      const dateOfExpiry = screen.getAllByTestId('fml-passport-dateofexpiry');
      expect(dateOfIssue[0]).toBeInTheDocument();
      expect(dateOfExpiry[0]).toBeInTheDocument();
      const errorMsg = screen.getAllByText('Date of Expiry should be later than Date of Issue');
      expect(errorMsg[0]).toBeInTheDocument();
    });
  });

  it('should have throw error message when date of Expiry greater than date of issue in passport while changing', async () => {
    await waitFor(() => {
      const dateOfIssue = screen.getAllByTestId('fml-passport-dateofissue');
      const dateOfExpiry = screen.getAllByTestId('fml-passport-dateofexpiry');

      fireEvent.change(dateOfIssue[0], { value: '13 Jun 2022' });
      fireEvent.change(dateOfExpiry[0], { value: '13 Jun 2020' });

      const errorMsg = screen.getAllByText('Date of Expiry should be later than Date of Issue');
      expect(errorMsg[0]).toBeInTheDocument();
    });
  });

  it('should have throw error message when date of Expiry greater than date of issue in seaman book', async () => {
    await waitFor(() => {
      const elements = document.querySelectorAll('input[value="15 Aug 2013"]');
      expect(elements.length).toBe(1);
      const errorMsg = screen.getAllByText('Date of Expiry should be later than Date of Issue');
      expect(errorMsg[0]).toBeInTheDocument();
    });
  });

  it('should have throw error message when date of Expiry greater than date of issue in seaman book while changing', async () => {
    await waitFor(() => {
      const dateOfIssue = screen.getAllByTestId('fml-seafarer-date-of-issue');
      const allInputs = screen.getAllByRole('textbox');
      const dateOfExpiry = allInputs.find((input) => input.name === 'date_of_expiry');
      fireEvent.change(dateOfIssue[0], { value: '13 Jun 2022' });
      fireEvent.change(dateOfExpiry, { value: '13 Jun 2020' });

      const errorMsg = screen.getAllByText('Date of Expiry should be later than Date of Issue');
      expect(errorMsg[0]).toBeInTheDocument();
    });
  });

  it('should validate all seaman books if clicking on add another seaman book', async () => {
    const errorsBeforeChange = screen.queryAllByTestId('form-seafarer-seamanbook-port-field');
    expect(errorsBeforeChange).toHaveLength(2);
    let btn: HTMLElement[];
    await waitFor(() => {
      btn = screen.getAllByTestId('fml-seafarer-contact-address-add-another');
    });
    await act(async () => {
      fireEvent.click(btn[0]);
      // no errors if user update seaman books
      const errorsAfterChange = screen.getAllByTestId('form-seafarer-seamanbook-port-field');
      expect(errorsAfterChange.length).toBeGreaterThan(0);
    });
  });

  it('should validate all seaman books if some fields in seaman book is updated', async () => {
    const errorsBeforeChange = screen.queryAllByTestId('form-seafarer-seamanbook-port-field');
    expect(errorsBeforeChange).toHaveLength(2);
    let input: HTMLElement[];
    await waitFor(() => {
      input = screen.getAllByTestId('form-seafarer-seamanbook-country-field');
    });
    await act(async () => {
      fireEvent.change(input[0], { target: { value: null } });
    });

    await waitFor(() => {
      const errorsAfterChange = screen.getAllByTestId('form-seafarer-seamanbook-port-field');
      expect(errorsAfterChange.length).toBeGreaterThan(0);
    });
  });
});
