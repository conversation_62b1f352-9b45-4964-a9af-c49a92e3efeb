import React from 'react';
import { MemoryRouter, Route } from 'react-router-dom';
import { act, render, screen, waitFor } from '@testing-library/react';
import AddSeafarePage from '../../pages/AddSeafarerPage';
import '@testing-library/jest-dom';
import { AccessProvider } from '@src/component/common/Access';

jest.mock('../../styleGuide');
jest.mock('../../service/user-service');
jest.mock('../../controller/add-seafarer-controller');

describe('AddSeafarerContactsForm validation', () => {
  beforeAll(async () => {
    act(() => {
      render(
        <MemoryRouter initialEntries={['/seafarer/1/add/contact-details']}>
          <Route path="/seafarer/:seafarerId?/add/:step?">
            <AccessProvider config={{
              seafarer: {
                addSeafarer: true,
                editSeafarer: true,
                edit: {
                  personalDetails: true,
                  passport: true,
                  seamansBook: true,
                },
                create: {},
                hidden: {
                  bankAccount: false,
                },
              },
            }}>
              <AddSeafarePage />
            </AccessProvider>
          </Route>
        </MemoryRouter>,
      );
    });
  });

  it('should show warning on invalid phone number', async () => {
    await waitFor(() => {
      const phoneFieldWarning = screen.getAllByText(
        /invalid number, cannot detect country of area code/i,
      );
      expect(phoneFieldWarning).toHaveLength(2);
      expect(phoneFieldWarning[0]).toBeInTheDocument();
      expect(phoneFieldWarning[1]).toBeInTheDocument();
    });
  });
});
