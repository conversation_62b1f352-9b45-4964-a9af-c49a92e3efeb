import { seafarerStatus } from '../../model/SeafarerModel';

export function getMockedSeafarer() {
  return {
    general: {
      current_rank: 'Master',
      full_name: '<PERSON>',
      first_name: '<PERSON>',
      middle_name: '<PERSON>',
      last_name: '<PERSON><PERSON><PERSON>',
      date_of_birth: '15 Nov 1973',
      gender: 'Male',
    },
    seafarer_person: {
      current_rank: 'Master',
      full_name: '<PERSON>',
      first_name: '<PERSON>',
      middle_name: '<PERSON>',
      last_name: '<PERSON><PERSON><PERSON>',
      date_of_birth: '15 Nov 1973',
      gender: 'Male',
      status: seafarerStatus,
    },
    passports: [
      {
        number: '********',
        date_of_issue: '31 Jul 2017',
        date_of_expiry: '30 Jul 2027',
        issuing_country: 'India',
        place_of_issue: 'Mumbai',
      },
    ],
    seamanBooks: [
      {
        number: 'MUM74956',
        country: 'India',
        port_of_issue: 'Mumbai',
        date_of_issue: '22 May 2014',
        date_of_expiry: '21 May 2024',
      },
    ],
  };
}

export function getMockedSeafarerResponse() {
  return {
    seafarer_person: {
      banks: [],
      passports: [
        {
          id: 1,
          created_at: '2020-11-16T04:11:21.121Z',
          updated_at: '2020-11-16T09:35:48.099Z',
          seafarer_person_id: 4,
          number: 'E 0325801',
          place_of_issue: 'Lucknow',
          country_id: 4,
          date_of_issue: '2012-11-15T00:00:00.000Z',
          date_of_expiry: '2011-11-14T00:00:00.000Z',
          ref_id: 45868,
          country: {
            id: 4,
            alpha2_code: 'AI',
            alpha3_code: 'AIA',
            value: 'Anguilla',
            numeric_code: 660,
          },
          document: [],
        },
        {
          id: 2,
          created_at: '2020-11-16T04:11:21.121Z',
          updated_at: '2020-11-16T09:35:48.099Z',
          seafarer_person_id: 4,
          number: 'A123456',
          place_of_issue: 'Lucknow',
          country_id: 4,
          date_of_issue: '2012-11-15T00:00:00.000Z',
          date_of_expiry: '2011-11-14T00:00:00.000Z',
          ref_id: 45868,
          country: {
            id: 4,
            alpha2_code: 'AI',
            alpha3_code: 'AIA',
            value: 'Anguilla',
            numeric_code: 660,
          },
          document: [],
        },
      ],
      seaman_books: [
        {
          id: 5,
          created_at: '2020-11-16T04:11:21.121Z',
          updated_at: '2020-11-16T09:35:48.117Z',
          seafarer_person_id: 4,
          country_id: 105,
          number: 'BY69018',
          date_of_issue: '1993-05-07T00:00:00.000Z',
          date_of_expiry: '2013-08-15T00:00:00.000Z',
          is_original: false,
          port_of_issue: 'Mumbai',
          ref_id: 45869,
          country: {
            id: 105,
            alpha2_code: 'IN',
            alpha3_code: 'IND',
            value: 'India',
            numeric_code: 356,
          },
          document: [],
        },
        {
          id: 6,
          created_at: '2020-11-16T04:11:21.121Z',
          updated_at: '2020-11-16T09:35:48.117Z',
          seafarer_person_id: 4,
          country_id: 105,
          number: 'BY69019',
          date_of_issue: '1993-05-07T00:00:00.000Z',
          date_of_expiry: '2012-08-15T00:00:00.000Z',
          is_original: false,
          port_of_issue: null,
          ref_id: 45869,
          country: {
            id: 105,
            alpha2_code: 'IN',
            alpha3_code: 'IND',
            value: 'India',
            numeric_code: 356,
          },
          document: [],
        },
      ],
      first_name: 'Hector',
      middle_name: 'Jason',
      last_name: 'Escaton',
      date_of_birth: '2020-07-23T00:00:00.000Z',
      gender: 'Male',
      photos: [],
      reports: [],
      address: 'Hong Kong, Kowloon',
      country: { id: 4, value: 'Russia', ref_id: null },
      country_id: 4,
      created_at: '2020-07-23T02:39:18.012Z',
      id: 2,
      is_parent: null,
      parent_hkid: null,
      nationality: { id: 2, value: 'Russian', ref_id: null },
      nationality_id: 2,
      remarks: null,
      screening_status: 'under_screening',
      updated_at: '2020-07-23T02:39:18.012Z',
      current_account_status: 'active',
      current_journey_status: 'on_leave',
      current_exam_status: 'no_examination',
      seafarer_contacts: [
        {
          contact: '*************',
          contact_type: 'telephone_number',
          created_at: '2020-10-29T08:45:58.554Z',
          id: 1,
          seafarer_person_id: 2,
          updated_at: '2020-10-29T08:45:58.554Z',
        },
        {
          contact: '***********',
          contact_type: 'telephone_number',
          created_at: '2020-10-29T08:45:58.554Z',
          id: 2,
          seafarer_person_id: 2,
          updated_at: '2020-10-29T08:45:58.554Z',
        },
      ],
      telephone_numbers: [
        {
          contact: '*************',
          contact_type: 'telephone_number',
          created_at: '2020-10-29T08:45:58.554Z',
          id: 1,
          seafarer_person_id: 2,
          updated_at: '2020-10-29T08:45:58.554Z',
        },
        {
          contact: '***********',
          contact_type: 'telephone_number',
          created_at: '2020-10-29T08:45:58.554Z',
          id: 2,
          seafarer_person_id: 2,
          updated_at: '2020-10-29T08:45:58.554Z',
        },
      ],
      addresses: [
        {
          building: 'asdasd',
          city: 'asdas',
          country: {
            alpha2_code: 'AI',
            alpha3_code: 'AIA',
            id: 4,
            numeric_code: 660,
            value: 'Anguilla',
          },
          country_id: 4,
          created_at: '2020-10-29T08:45:58.554Z',
          id: 3,
          other_address: 'asdsd',
          postal_zip_code: '12313',
          seafarer_person_id: 3,
          state: 'dasdsa',
          updated_at: '2020-10-29T08:45:58.554Z',
        },
      ],
      // personal_particulars
      height: 179,
      weight: 55,
      overall_size: 42,
      tshirt_size: 30,
      jacket_size: 52,
      shoe_size: 43,
      smoking: 'No',
      vegetarian: 'No',
      nearest_airpot: 'Haneda Tokyo',
      marital_status: 'single',
      name_of_spouse: 'Tzuyu',
      number_of_children: 2,
      children_names: 'Test Chou',
      // next of kin
      family_members: [
        {
          address: {
            id: 14,
            created_at: '2020-10-29T06:10:21.855Z',
            updated_at: '2020-10-29T06:34:58.882Z',
            postal_zip_code: '0.232',
            country_id: 233,
          },
          address_id: 14,
          created_at: '2020-10-29T06:10:21.904Z',
          email: '<EMAIL>;<EMAIL>',
          id: 2,
          mobilephone: '***********',
          name: 'Ya boy Shawn',
          relationship: 'Rapper Brother',
          seafarer_person_id: 3,
          surname: 'Kingston',
          telephone: '*************',
          updated_at: '2020-10-29T06:34:58.878Z',
        },
      ],
      status: seafarerStatus,
    },
    seafarer_contact_log: [
      {
        is_latest: true,
        contact_date: '2021-07-23T00:00:00.000Z',
        next_contact_date: '2021-08-23T00:00:00.000Z',
        docs_in_hand: 'docs_in_hand',
        availability_date: '2022-03-23T00:00:00.000Z',
        availability_remarks: 'testing remarks',
      },
    ],
    can_view_contacts: true,
  };
}

export function getMockedSeafarerResponseWithNoContactDetailAccess() {
  return {
    seafarer_person: {
      banks: [],
      passports: [
        {
          id: 1,
          created_at: '2020-11-16T04:11:21.121Z',
          updated_at: '2020-11-16T09:35:48.099Z',
          seafarer_person_id: 4,
          number: 'E 0325801',
          place_of_issue: 'Lucknow',
          country_id: 4,
          date_of_issue: '2001-11-15T00:00:00.000Z',
          date_of_expiry: '2011-11-14T00:00:00.000Z',
          ref_id: 45868,
          country: {
            id: 4,
            alpha2_code: 'AI',
            alpha3_code: 'AIA',
            value: 'Anguilla',
            numeric_code: 660,
          },
          document: [],
        },
        {
          id: 2,
          created_at: '2020-11-16T04:11:21.121Z',
          updated_at: '2020-11-16T09:35:48.099Z',
          seafarer_person_id: 4,
          number: 'A123456',
          place_of_issue: 'Lucknow',
          country_id: 4,
          date_of_issue: '2001-11-15T00:00:00.000Z',
          date_of_expiry: '2011-11-14T00:00:00.000Z',
          ref_id: 45868,
          country: {
            id: 4,
            alpha2_code: 'AI',
            alpha3_code: 'AIA',
            value: 'Anguilla',
            numeric_code: 660,
          },
          document: [],
        },
      ],
      seaman_books: [
        {
          id: 5,
          created_at: '2020-11-16T04:11:21.121Z',
          updated_at: '2020-11-16T09:35:48.117Z',
          seafarer_person_id: 4,
          country_id: 105,
          number: 'BY69018',
          date_of_issue: '1993-05-07T00:00:00.000Z',
          date_of_expiry: '2013-08-15T00:00:00.000Z',
          is_original: false,
          port_of_issue: 'Mumbai',
          ref_id: 45869,
          country: {
            id: 105,
            alpha2_code: 'IN',
            alpha3_code: 'IND',
            value: 'India',
            numeric_code: 356,
          },
          document: [],
        },
        {
          id: 6,
          created_at: '2020-11-16T04:11:21.121Z',
          updated_at: '2020-11-16T09:35:48.117Z',
          seafarer_person_id: 4,
          country_id: 105,
          number: 'BY69019',
          date_of_issue: '1993-05-07T00:00:00.000Z',
          date_of_expiry: '2012-08-15T00:00:00.000Z',
          is_original: false,
          port_of_issue: null,
          ref_id: 45869,
          country: {
            id: 105,
            alpha2_code: 'IN',
            alpha3_code: 'IND',
            value: 'India',
            numeric_code: 356,
          },
          document: [],
        },
      ],
      first_name: 'Hector',
      middle_name: 'Jason',
      last_name: 'Escaton',
      date_of_birth: '2020-07-23T00:00:00.000Z',
      gender: 'Male',
      photos: [],
      reports: [],
      address: 'Hong Kong, Kowloon',
      country: { id: 4, value: 'Russia', ref_id: null },
      country_id: 4,
      created_at: '2020-07-23T02:39:18.012Z',
      id: 2,
      is_parent: null,
      parent_hkid: null,
      nationality: { id: 2, value: 'Russian', ref_id: null },
      nationality_id: 2,
      remarks: null,
      screening_status: 'under_screening',
      updated_at: '2020-07-23T02:39:18.012Z',
      current_account_status: 'active',
      current_journey_status: 'on_leave',
      current_exam_status: 'no_examination',
      // personal_particulars
      height: 179,
      weight: 55,
      overall_size: 42,
      tshirt_size: 30,
      jacket_size: 52,
      shoe_size: 43,
      smoking: 'No',
      vegetarian: 'No',
      nearest_airpot: 'Haneda Tokyo',
      marital_status: 'single',
      name_of_spouse: 'Tzuyu',
      number_of_children: 2,
      children_names: 'Test Chou',
      // next of kin
      family_members: [
        {
          created_at: '2020-10-29T06:10:21.904Z',
          id: 2,
          name: 'Ya boy Shawn',
          relationship: 'Rapper Brother',
          seafarer_person_id: 3,
          surname: 'Kingston',
          updated_at: '2020-10-29T06:34:58.878Z',
        },
      ],
      status: seafarerStatus,
    },
    id: 2,
    can_view_contacts: false,
  };
}

export function getMockedParentSeafarer() {
  return {
    is_parent: true,
    parent_hkid: null,
    seafarer_person: {
      banks: [],
      passports: [],
      seaman_books: [],
      first_name: 'Hector',
      middle_name: 'Jason',
      last_name: 'Escaton',
      date_of_birth: '2020-07-23T00:00:00.000Z',
      gender: 'Male',
      photos: [],
      reports: [],
      address: 'Hong Kong, Kowloon',
      country: { id: 4, value: 'Russia', ref_id: null },
      country_id: 4,
      created_at: '2020-07-23T02:39:18.012Z',
      id: 2,
      is_parent: true,
      parent_hkid: null,
      nationality: { id: 2, value: 'Russian', ref_id: null },
      nationality_id: 2,
      remarks: null,
      screening_status: 'under_screening',
      updated_at: '2020-07-23T02:39:18.012Z',
      seafarer_contacts: [],
      telephone_numbers: [],
      addresses: [],
      family_members: [],
      created_by_user_info: {
        email: '<EMAIL>',
        full_name: 'Test User',
      },
      current_account_status: 'active',
      current_journey_status: 'on_leave',
      current_exam_status: 'no_examination',
      status: seafarerStatus,
    },
  };
}

export function getMockedChildSeafarer() {
  return {
    is_parent: false,
    parent_hkid: 1234,
    seafarer_person: {
      banks: [],
      passports: [],
      seaman_books: [],
      first_name: 'Hector',
      middle_name: 'Jason',
      last_name: 'Escaton',
      date_of_birth: '2020-07-23T00:00:00.000Z',
      gender: 'Male',
      photos: [],
      reports: [],
      address: 'Hong Kong, Kowloon',
      country: { id: 4, value: 'Russia', ref_id: null },
      country_id: 4,
      created_at: '2020-07-23T02:39:18.012Z',
      id: 2,
      is_parent: true,
      parent_hkid: 1234,
      nationality: { id: 2, value: 'Russian', ref_id: null },
      nationality_id: 2,
      remarks: null,
      screening_status: 'under_screening',
      updated_at: '2020-07-23T02:39:18.012Z',
      seafarer_contacts: [],
      telephone_numbers: [],
      addresses: [],
      family_members: [],
      status: seafarerStatus,
      current_account_status: 'active',
      current_journey_status: 'on_leave',
      current_exam_status: 'no_examination',
    },
  };
}

export function getMockedSeafarerErrorResponse() {
  return {
    seafarer_person: {
      banks: [],
      passports: [
        {
          id: 1,
          created_at: '2020-11-16T04:11:21.121Z',
          updated_at: '2020-11-16T09:35:48.099Z',
          seafarer_person_id: 4,
          number: 'E 0325801',
          place_of_issue: 'Lucknow',
          country_id: 4,
          date_of_issue: '2012-11-15T00:00:00.000Z',
          date_of_expiry: '2011-11-14T00:00:00.000Z',
          ref_id: 45868,
          country: {
            id: 4,
            alpha2_code: 'AI',
            alpha3_code: 'AIA',
            value: 'Anguilla',
            numeric_code: 660,
          },
          document: [],
        },
        {
          id: 2,
          created_at: '2020-11-16T04:11:21.121Z',
          updated_at: '2020-11-16T09:35:48.099Z',
          seafarer_person_id: 4,
          number: 'A123456',
          place_of_issue: 'Lucknow',
          country_id: 4,
          date_of_issue: '2012-11-15T00:00:00.000Z',
          date_of_expiry: '2011-11-14T00:00:00.000Z',
          ref_id: 45868,
          country: {
            id: 4,
            alpha2_code: 'AI',
            alpha3_code: 'AIA',
            value: 'Anguilla',
            numeric_code: 660,
          },
          document: [],
        },
      ],
      seaman_books: [
        {
          id: 5,
          created_at: '2020-11-16T04:11:21.121Z',
          updated_at: '2020-11-16T09:35:48.117Z',
          seafarer_person_id: 4,
          country_id: 105,
          number: 'BY 69018',
          date_of_issue: '2020-05-07T00:00:00.000Z',
          date_of_expiry: '2013-08-15T00:00:00.000Z',
          is_original: false,
          port_of_issue: 'Mumbai',
          ref_id: 45869,
          country: {
            id: 105,
            alpha2_code: 'IN',
            alpha3_code: 'IND',
            value: 'India',
            numeric_code: 356,
          },
          document: [],
        },
      ],
      middle_name: 'Jason',
      last_name: 'Escaton',
      date_of_birth: '2020-07-23T00:00:00.000Z',
      gender: 'Male',
      photos: [],
      reports: [],
      address: 'Hong Kong, Kowloon',
      country: { id: 4, value: 'Russia', ref_id: null },
      country_id: 4,
      created_at: '2020-07-23T02:39:18.012Z',
      id: 2,
      is_parent: null,
      parent_hkid: null,
      nationality: { id: 2, value: 'Russian', ref_id: null },
      nationality_id: 2,
      remarks: null,
      screening_status: 'under_screening',
      updated_at: '2020-07-23T02:39:18.012Z',
      seafarer_contacts: [
        {
          contact: '*************',
          contact_type: 'telephone_number',
          created_at: '2020-10-29T08:45:58.554Z',
          id: 1,
          seafarer_person_id: 2,
          updated_at: '2020-10-29T08:45:58.554Z',
        },
        {
          contact: '8262132',
          contact_type: 'telephone_number',
          created_at: '2020-10-29T08:45:58.554Z',
          id: 2,
          seafarer_person_id: 2,
          updated_at: '2020-10-29T08:45:58.554Z',
        },
      ],
      telephone_numbers: [
        {
          contact: '*************',
          contact_type: 'telephone_number',
          created_at: '2020-10-29T08:45:58.554Z',
          id: 1,
          seafarer_person_id: 2,
          updated_at: '2020-10-29T08:45:58.554Z',
        },
        {
          contact: '8262132',
          contact_type: 'telephone_number',
          created_at: '2020-10-29T08:45:58.554Z',
          id: 2,
          seafarer_person_id: 2,
          updated_at: '2020-10-29T08:45:58.554Z',
        },
      ],
      addresses: [
        {
          building: 'asdasd',
          city: 'asdas',
          country: {
            alpha2_code: 'AI',
            alpha3_code: 'AIA',
            id: 4,
            numeric_code: 660,
            value: 'Anguilla',
          },
          country_id: 4,
          created_at: '2020-10-29T08:45:58.554Z',
          id: 3,
          other_address: 'asdsd',
          postal_zip_code: '12313',
          seafarer_person_id: 3,
          state: 'dasdsa',
          updated_at: '2020-10-29T08:45:58.554Z',
        },
      ],
      // personal_particulars
      height: 179,
      weight: 55,
      overall_size: 42,
      tshirt_size: 30,
      jacket_size: 52,
      shoe_size: 43,
      smoking: 'No',
      vegetarian: 'No',
      nearest_airpot: 'Haneda Tokyo',
      marital_status: 'single',
      name_of_spouse: 'Tzuyu',
      number_of_children: 2,
      children_names: 'Test Chou',
      // next of kin
      family_members: [
        {
          address: {
            id: 14,
            created_at: '2020-10-29T06:10:21.855Z',
            updated_at: '2020-10-29T06:34:58.882Z',
            postal_zip_code: '0.232',
            country_id: 233,
          },
          address_id: 14,
          created_at: '2020-10-29T06:10:21.904Z',
          email: '<EMAIL>',
          id: 2,
          mobilephone: '***********',
          name: 'Ya boy Shawn',
          relationship: 'Rapper Brother',
          seafarer_person_id: 3,
          surname: 'Kingston',
          telephone: '*************',
          updated_at: '2020-10-29T06:34:58.878Z',
        },
      ],
      status: seafarerStatus,
    },
  };
}

export function getMockedSeafarerResponseWithoutError() {
  return {
    rank_id: 92,
    office_id: 1,
    seafarer_person: {
      banks: [],
      passports: [
        {
          country: {
            id: 103,
            alpha2_code: 'IN',
            alpha3_code: 'IND',
            value: 'India',
            numeric_code: 356,
          },
          country_id: 103,
          created_at: '2021-08-10T01:41:55.217Z',
          date_of_expiry: '2021-07-10T00:00:00.000Z',
          date_of_issue: '2021-06-01T00:00:00.000Z',
          doc_path: '/ui2021/1623119680010_Code_of_Conduct_and_Ethics-ROW.pdf',
          document: [
            {
              created_at: '2021-08-10T01:41:55.231Z',
              id: 12105,
              mime: null,
              name: '1623119680010_Code_of_Conduct_and_Ethics-ROW.pdf',
              updated_at: '2021-08-10T01:41:55.231Z',
            },
          ],
          id: 12455,
          number: '2345556',
          place_of_issue: 'Mumbai',
          ref_id: 743082,
          seafarer_person_id: 2,
          updated_at: '2021-08-10T01:41:55.217Z',
        },
      ],
      seaman_books: [
        {
          country: {
            id: 1,
            alpha2_code: 'AF',
            alpha3_code: 'AFG',
            value: 'Afghanistan',
            numeric_code: 4,
          },
          country_id: 1,
          created_at: '2021-06-22T15:51:26.234Z',
          date_of_expiry: '2021-07-10T00:00:00.000Z',
          date_of_issue: '2021-06-09T00:00:00.000Z',
          doc_path: '/ui2021/1624435608713_SeamanBook_seeta_owale_India.pdf',
          document: [
            {
              created_at: '2021-06-23T08:07:12.437Z',
              id: 20,
              mime: 'application/pdf',
              name: 'SeamanBook_seeta_owale_India.pdf',
              updated_at: '2021-07-04T23:05:34.537Z',
            },
          ],
          id: 32,
          is_original: false,
          number: '1225643',
          port_of_issue: 'Qalat',
          ref_id: 743119,
          seafarer_person_id: 2,
          updated_at: '2021-07-04T23:05:34.519Z',
        },
      ],
      first_name: 'Hector',
      middle_name: 'Jason',
      last_name: 'Escaton',
      date_of_birth: '2020-07-23T00:00:00.000Z',
      gender: 'Male',
      photos: [],
      reports: [],
      address: 'Hong Kong, Kowloon',
      country: { id: 4, value: 'Russia', ref_id: null },
      country_id: 4,
      created_at: '2020-07-23T02:39:18.012Z',
      id: 2,
      is_parent: null,
      parent_hkid: null,
      nationality: { id: 2, value: 'Russian', ref_id: null },
      nationality_id: 2,
      remarks: null,
      screening_status: 'under_screening',
      updated_at: '2020-07-23T02:39:18.012Z',
      seafarer_contacts: [
        {
          contact: '*************',
          contact_type: 'telephone_number',
          created_at: '2020-10-29T08:45:58.554Z',
          id: 1,
          seafarer_person_id: 2,
          updated_at: '2020-10-29T08:45:58.554Z',
        },
        {
          contact: '2342342342342',
          contact_type: 'telephone_number',
          created_at: '2020-10-29T08:45:58.554Z',
          id: 2,
          seafarer_person_id: 2,
          updated_at: '2020-10-29T08:45:58.554Z',
        },
        {
          contact: '<EMAIL>',
          contact_type: 'email',
          created_at: '2021-08-26T05:49:45.170Z',
          id: 65087,
          seafarer_person_id: 2,
          updated_at: '2021-08-26T05:49:45.170Z',
        },
      ],
      addresses: [
        {
          building: 'asdasd',
          city: 'asdas',
          country: {
            alpha2_code: 'AI',
            alpha3_code: 'AIA',
            id: 4,
            numeric_code: 660,
            value: 'Anguilla',
          },
          country_id: 4,
          created_at: '2020-10-29T08:45:58.554Z',
          id: 3,
          other_address: 'asdsd',
          postal_zip_code: '12313',
          seafarer_person_id: 3,
          state: 'dasdsa',
          updated_at: '2020-10-29T08:45:58.554Z',
        },
      ],
      // personal_particulars
      height: 179,
      weight: 55,
      overall_size: 42,
      tshirt_size: 30,
      jacket_size: 52,
      shoe_size: 43,
      smoking: 'No',
      vegetarian: 'No',
      nearest_airpot: 'Haneda Tokyo',
      marital_status: 'single',
      name_of_spouse: 'Tzuyu',
      number_of_children: 2,
      children_names: 'Test Chou',
      // next of kin
      family_members: [
        {
          address: {
            id: 14,
            created_at: '2020-10-29T06:10:21.855Z',
            updated_at: '2020-10-29T06:34:58.882Z',
            postal_zip_code: '0.232',
            country_id: 233,
          },
          address_id: 14,
          created_at: '2020-10-29T06:10:21.904Z',
          email: '<EMAIL>',
          id: 2,
          mobilephone: '***********',
          name: 'Ya boy Shawn',
          relationship: 'Rapper Brother',
          seafarer_person_id: 3,
          surname: 'Kingston',
          telephone: '*************',
          updated_at: '2020-10-29T06:34:58.878Z',
        },
      ],
      status: {
        seafarer_account_status: 'active',
        seafarer_exam_status: 'no_examination',
        seafarer_journey_status: 'recommended',
      },
      place_of_birth: 'Seofll',
      country_of_birth: {
        alpha2_code: 'IN',
        alpha3_code: 'IND',
        id: 103,
        numeric_code: 356,
      },
      country_of_birth_id: 103,
      current_account_status: 'active',
      current_journey_status: 'on_leave',
      current_exam_status: 'no_examination',
    },
  };
}

export function getMockedSeafarerExperience() {
  return [
    {
      id: 3,
      seafarer_id: 22,
      ownership_id: 1,
      rank_id: 1,
      vessel_name: 'vessel name 1',
      vessel_type: 'vessel type 1',
      deadweight_tonnage: 100,
      deadweight_gross_registered_tonnage: 1000,
      engine_type: 'engine type',
      engine_sub_type: 'engine sub type',
      brake_horse_power: 4444,
      start_date: '2010-09-30T18:30:00.000Z',
      end_date: '2015-09-30T18:30:00.000Z',
      owner_name: 'owner name',
      signoff_port_name: 'signoff port name',
      signoff_reason_id: 1,
      created_by_hash: '',
      last_updated_by: '',
      created_at: '2021-10-22T09:08:30.284Z',
      updated_at: '2021-10-22T09:08:30.284Z',
      value: 'MASTER',
      unit: 'MSTR',
      ref_id: 2000336,
      signoff_reason: {
        id: 1,
        value: 'value 1',
        unit: 'unit 1',
        ref_id: 1,
      },
      rank: {
        id: 1,
        value: 'MASTER',
        unit: 'MSTR',
        ref_id: 2000336,
      },
    },
    {
      id: 1,
      seafarer_id: 22,
      ownership_id: 1,
      rank_id: 1,
      vessel_name: 'vessel name 1',
      vessel_type: 'vessel type 1',
      deadweight_tonnage: 100,
      deadweight_gross_registered_tonnage: 1000,
      engine_type: 'engine type',
      engine_sub_type: 'engine sub type',
      brake_horse_power: 4444,
      start_date: '2018-09-30T18:30:00.000Z',
      end_date: '2019-09-30T18:30:00.000Z',
      owner_name: 'owner name',
      signoff_port_name: 'signoff port name',
      signoff_reason_id: 1,
      created_by_hash: '',
      last_updated_by: '',
      created_at: '2021-10-22T09:08:30.284Z',
      updated_at: '2021-10-22T09:08:30.284Z',
      value: 'MASTER',
      unit: 'MSTR',
      ref_id: 2000336,
      signoff_reason: {
        id: 1,
        value: 'value 1',
        unit: 'unit 1',
        ref_id: 1,
      },
      rank: {
        id: 1,
        value: 'MASTER',
        unit: 'MSTR',
        ref_id: 2000336,
      },
    },
    {
      id: 2,
      seafarer_id: 22,
      ownership_id: 1,
      rank_id: 1,
      vessel_name: 'vessel name 1',
      vessel_type: 'vessel type 1',
      deadweight_tonnage: 100,
      deadweight_gross_registered_tonnage: 1000,
      engine_type: 'engine type',
      engine_sub_type: 'engine sub type',
      brake_horse_power: 4444,
      start_date: '2021-09-30T18:30:00.000Z',
      end_date: '2022-09-30T18:30:00.000Z',
      owner_name: 'owner name',
      signoff_port_name: 'signoff port name',
      signoff_reason_id: 1,
      created_by_hash: '',
      last_updated_by: '',
      created_at: '2021-10-22T09:08:30.284Z',
      updated_at: '2021-10-22T09:08:30.284Z',
      value: 'MASTER',
      unit: 'MSTR',
      ref_id: 2000336,
      signoff_reason: {
        id: 1,
        value: 'value 1',
        unit: 'unit 1',
        ref_id: 1,
      },
      rank: {
        id: 1,
        value: 'MASTER',
        unit: 'MSTR',
        ref_id: 2000336,
      },
    },
  ];
}
