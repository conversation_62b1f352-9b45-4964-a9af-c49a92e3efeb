import { getDuration, getDurationByDateRanges, getDurationStrByDateRanges } from '../../model/utils';

describe('Get duration in years months and days', () => {

  const day = 24 * 60 * 60 * 1000;
  const month = 31 * day;
  const year = 12 * month;

  it('should get duration', () => {
    expect(getDuration(0)).toBe('0');
    expect(getDuration(day)).toBe('1 Day');
    expect(getDuration(month)).toBe('1 Month');
    expect(getDuration(year)).toBe('1 Year');
    expect(getDuration(5 * month + 6 * day)).toBe('5 Months 6 Days');
    expect(getDuration(2 * year + 3 * month + 4 * day)).toBe('2 Years 3 Months 4 Days');
    expect(getDuration(year + 3 * day)).toBe('1 Year 3 Days');
  });

  it('should get duration from signle date range, with a result of 6 Mo 8 Days', () => {
    let durationObject = getDurationByDateRanges([{
      fromDateISOString: '1999-02-16T00:00:00.000Z',
      toDateISOString: '1999-08-23T00:00:00.000Z'
    }])

    expect(durationObject).toEqual({ years: 0, months: 6, days: 8, hours: 0 })
  });

  //example from https://parisuat.fleetship.com/fml/PARIS?display=crewoverview&crewid=5, ,group by vessel type of 'Container Vessel'
  it('should get sum of duration from date ranges (grouped by vessel type of "Container Vessel")', () => {
    let durationObject = getDurationByDateRanges([{
      fromDateISOString: '1993-06-21T00:00:00.000Z',
      toDateISOString: '1994-04-15T00:00:00.000Z'
    }, {
      fromDateISOString: '1994-09-11T00:00:00.000Z',
      toDateISOString: '1995-01-04T00:00:00.000Z'
    }, {
      fromDateISOString: '1995-03-19T00:00:00.000Z',
      toDateISOString: '1995-12-20T00:00:00.000Z'
    }])

    expect(durationObject).toEqual({ years: 1, months: 10, days: 23, hours: 0 })
  });

  //example from https://parisuat.fleetship.com/fml/PARIS?display=crewoverview&crewid=5, ,group by vessel type of 'Reefer Vessel'
  it('should get sum of duration from date ranges (grouped by vessel type of "Reefer Vessel")', () => {
    let durationObject = getDurationByDateRanges([{
      fromDateISOString: '1991-02-22T00:00:00.000Z',
      toDateISOString: '1992-11-14T00:00:00.000Z'
    }, {
      fromDateISOString: '1996-07-23T00:00:00.000Z',
      toDateISOString: '1997-02-10T00:00:00.000Z'
    }])

    expect(durationObject).toEqual({ years: 2, months: 3, days: 13, hours: 0 })
  });

  it('should get duration str from signle date range, with a result of 6 Mo 8 Days', () => {
    let durationStr = getDurationStrByDateRanges([{
      fromDateISOString: '1999-02-16T00:00:00.000Z',
      toDateISOString: '1999-08-23T00:00:00.000Z'
    }]);
    expect(durationStr).toEqual('6 Months 8 Days')
  })

  //example from https://parisuat.fleetship.com/fml/PARIS?display=crewoverview&crewid=5, ,group by vessel type of 'Container Vessel'
  it('should get duration str from multiple date ranges (grouped by vessel type of "Container Vessel"), should sum and normalized the to the unit of year/month/days', () => {
    let durationStr = getDurationStrByDateRanges([{
      fromDateISOString: '1993-06-21T00:00:00.000Z',
      toDateISOString: '1994-04-15T00:00:00.000Z'
    }, {
      fromDateISOString: '1994-09-11T00:00:00.000Z',
      toDateISOString: '1995-01-04T00:00:00.000Z'
    }, {
      fromDateISOString: '1995-03-19T00:00:00.000Z',
      toDateISOString: '1995-12-20T00:00:00.000Z'
    }]);
    expect(durationStr).toEqual('1 Year 10 Months 23 Days')
  })

  //example from https://parisuat.fleetship.com/fml/PARIS?display=crewoverview&crewid=5, ,group by vessel type of 'Reefer Vessel'
  it('should get duration str from multiple date ranges (grouped by vessel type of "Reefer Vessel"), should sum and normalized the to the unit of year/month/days', () => {
    let durationStr = getDurationStrByDateRanges([{
      fromDateISOString: '1991-02-22T00:00:00.000Z',
      toDateISOString: '1992-11-14T00:00:00.000Z'
    }, {
      fromDateISOString: '1996-07-23T00:00:00.000Z',
      toDateISOString: '1997-02-10T00:00:00.000Z'
    }]);
    expect(durationStr).toEqual('2 Years 3 Months 13 Days')
  })
});
