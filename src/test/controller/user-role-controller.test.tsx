import UserRoleController from '../../controller/user-role-controller';

describe('UserRoleController', () => {
  let controller;
  beforeEach(() => {
    controller = new UserRoleController();
  });

  describe('role config', () => {
    it('should have all config', async () => {
      const keycloakJson = {
        realmAccess: { roles: [] },
        tokenParsed: { group: [] },
        idTokenParsed: {},
      };
      const config = await controller.getConfig(keycloakJson);
      expect(Object.keys(config)).toEqual([
        'seafarer',
        'vessel',
        'screeningGroup',
        'shipPartyId',
        'shipPartyType',
        'techGroup',
      ]);
      expect(Object.keys(config.seafarer.screening)).toEqual([
        'caseReportUpload',
        'documentsUpload',
        'view',
        'reapply',
        'forward',
        'approve',
      ]);
      expect(Object.keys(config.seafarer)).toEqual([
        'screening',
        'addSeafarer',
        'editSeafarer',
        'replaceCrewList',
        'add',
        'edit',
        'generate',
        'hidden',
        'view',
        'user',
        'manage',
        'training',
      ]);
    });

    describe('user group config', () => {
      const keycloakJson = (groups) => {
        return {
          realmAccess: { roles: ['vessel|approve', 'seafarer|approve'] },
          tokenParsed: {
            group: groups,
          },
          idTokenParsed: {},
        };
      };

      it('should return Compliance Employee group', async () => {
        const config = await controller.getConfig(
          keycloakJson([
            '/Department/Fleet Personnel/VesselManager',
            '/Department/Compliance/Employee',
          ]),
        );
        expect(config.screeningGroup).toEqual('Compliance Employee');
      });
      it('should return Compliance Supervisor group', async () => {
        const config = await controller.getConfig(
          keycloakJson([
            '/Department/Fleet Personnel/VesselManager',
            '/Department/Compliance/Supervisor',
          ]),
        );
        expect(config.screeningGroup).toEqual('Compliance Supervisor');
      });
      it('should return Fleet Personnel group', async () => {
        const config = await controller.getConfig(
          keycloakJson([
            '/Department/Fleet Personnel/VesselManager',
            '/Department/Fleet Personnel/SeniorManagement',
          ]),
        );
        expect(config.screeningGroup).toEqual('Fleet Personnel');
      });
    });
  });
});
