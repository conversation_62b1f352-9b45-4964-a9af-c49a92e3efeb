import AddSeafarerController from '../../controller/add-seafarer-controller';

jest.mock('../../styleGuide');
jest.mock('../../service/user-service');
jest.mock('../../service/travel-service');

const addController = new AddSeafarerController();
const documentObjectExample = {
  id: 1,
  document: [{ id: 1 }],
};

const seamansBookNoIdToRemoveExample = {
  seaman_books: [{ ...documentObjectExample }],
};

const seamansBookWithIdToRemoveExample = {
  seaman_books: [{ ...documentObjectExample, idToRemove: 1 }],
};

const passportNoIdToRemoveExample = {
  passports: [{ ...documentObjectExample }],
};

const passportWithIdToRemoveExample = {
  passports: [{ ...documentObjectExample, idToRemove: 1 }],
};

const bankAccountNoIdToRemoveExample = {
  bank_accounts: [{ ...documentObjectExample, bank_address: null }],
};

const bankAccountWithIdToRemoveExample = {
  bank_accounts: [
    {
      ...documentObjectExample,
      idToRemove: 1,
      bank_address: {
        country_id: 1,
        address1: 'sad',
      },
    },
  ],
};

const seafarerNullExample = {
  seafarer_person: {
    bank_accounts: [
      {
        bank_address: null,
      },
    ],
    seafarer_contacts: [
      {
        id: 4,
        created_at: '2020-11-18T02:11:40.549Z',
        updated_at: '2020-11-18T02:11:40.549Z',
        seafarer_person_id: 6,
        contact: 'null',
        contact_type: 'mobile_number',
      },
    ],
  },
};

const seafarerUndefinedExample = {
  seafarer_person: {
    ...seamansBookNoIdToRemoveExample,
    ...passportNoIdToRemoveExample,
    ...bankAccountNoIdToRemoveExample,
  },
};

const seafarerValueExample = {
  seafarer_person: {
    seafarer_contacts: [
      {
        id: 4,
        created_at: '2020-11-18T02:11:40.549Z',
        updated_at: '2020-11-18T02:11:40.549Z',
        seafarer_person_id: 6,
        contact: '+91-**********',
        contact_type: 'mobile_number',
      },
    ],
    ...seamansBookWithIdToRemoveExample,
    ...passportWithIdToRemoveExample,
    ...bankAccountWithIdToRemoveExample,
  },
};

describe('AddSeafarerController', () => {
  describe('cleanBankAddress', () => {
    it('should convert null address to empty object', async () => {
      let seafarer = { ...seafarerNullExample };
      seafarer = addController.cleanBankAddress(seafarer);
      expect(seafarer.seafarer_person.bank_accounts[0].bank_address).toEqual({});
    });

    it('should convert undefined address to empty object', async () => {
      let seafarer = { ...seafarerUndefinedExample };
      seafarer = addController.cleanBankAddress(seafarer);
      expect(seafarer.seafarer_person.bank_accounts[0].bank_address).toEqual({});
    });

    it('should not convert to empty object when address has value', async () => {
      let seafarer = { ...seafarerValueExample };
      seafarer = addController.cleanBankAddress(seafarer);
      expect(seafarer.seafarer_person.bank_accounts[0].bank_address).not.toEqual({});
    });
  });

  describe('mapContactFields', () => {
    it('should only retrieve numbers in contact and ignore special characters', async () => {
      let seafarer = { ...seafarerValueExample };
      seafarer = addController.mapContactFields(seafarer);
      expect(seafarer.seafarer_person.mobile_numbers[0].contact).toEqual('************');
    });

    it('should not populate field if invalid value', async () => {
      let seafarer = { ...seafarerNullExample };
      seafarer = addController.mapContactFields(seafarer);
      expect(seafarer.seafarer_person.mobile_numbers[0].contact).toEqual('');
    });
  });

  describe('getDocumentToRemove', () => {
    describe('getDocumentToRemove for seaman_books', () => {
      it(`should return an empty array if no seaman's book`, async () => {
        const seamanBooks = seafarerNullExample.seafarer_person.seaman_books || [];
        const docs = addController.getDocumentToRemove(seamanBooks);
        expect(docs).toEqual([]);
      });

      it(`should return empty array if no id to remove in list of seaman's book`, async () => {
        const seamanBooks = seafarerUndefinedExample.seafarer_person.seaman_books || [];
        const docs = addController.getDocumentToRemove(seamanBooks);
        expect(docs).toEqual([]);
      });

      it(`should return list of id when one or more of seaman's book that has idToRemove property`, async () => {
        const seamanBooks = seafarerValueExample.seafarer_person.seaman_books || [];
        const docs = addController.getDocumentToRemove(seamanBooks);
        expect(docs).toEqual([1]);
      });
    });

    describe('getDocumentToRemove for passports', () => {
      it(`should return an empty array if no passports`, async () => {
        const passports = seafarerNullExample.seafarer_person.passports || [];
        const docs = addController.getDocumentToRemove(passports);
        expect(docs).toEqual([]);
      });

      it(`should return empty array if no id to remove in list of passports`, async () => {
        const passports = seafarerUndefinedExample.seafarer_person.passports || [];
        const docs = addController.getDocumentToRemove(passports);
        expect(docs).toEqual([]);
      });

      it(`should return list of id when one or more of passports that has idToRemove property`, async () => {
        const passports = seafarerValueExample.seafarer_person.passports || [];
        const docs = addController.getDocumentToRemove(passports);
        expect(docs).toEqual([1]);
      });
    });

    describe('getDocumentToRemove for bank_accounts', () => {
      it(`should return an empty array if no bank_accounts`, async () => {
        const bankAccounts = seafarerNullExample.seafarer_person.bank_accounts || [];
        const docs = addController.getDocumentToRemove(bankAccounts);
        expect(docs).toEqual([]);
      });

      it(`should return empty array if no id to remove in list of bank_accounts`, async () => {
        const bankAccounts = seafarerUndefinedExample.seafarer_person.bank_accounts || [];
        const docs = addController.getDocumentToRemove(bankAccounts);
        expect(docs).toEqual([]);
      });

      it(`should return list of id when one or more of bank_accounts that has idToRemove property`, async () => {
        const bankAccounts = seafarerValueExample.seafarer_person.bank_accounts || [];
        const docs = addController.getDocumentToRemove(bankAccounts);
        expect(docs).toEqual([1]);
      });
    });
  });

  describe('getCleanedSeafarer with doc paths', () => {
    const expectedPath = `^/ui${new Date().getFullYear()}/d*`;

    it('should add doc_path in passports and seaman book', async () => {
      const passsportFile = new File([], 'passport-file.jpeg');
      const seamanbookFile = new File([], 'seaman_book.pdf');

      const seafarerWithDocs = {
        seafarer_person: {
          passports: [
            {
              file: passsportFile,
            },
          ],
          seaman_books: [
            {
              file: seamanbookFile,
            },
          ],
        },
      };
      const cleanedSeafarerJson = await addController.genDocPathForFileToUpload(seafarerWithDocs);
      expect(cleanedSeafarerJson.seafarer_person.passports[0].doc_path).toEqual(
        expect.stringMatching(expectedPath),
      );
      expect(cleanedSeafarerJson.seafarer_person.seaman_books[0].doc_path).toEqual(
        expect.stringMatching(expectedPath),
      );
    });

    it('should add photo_path, when photo name is not null', async () => {
      const personWithPhoto = {
        seafarer_person: {
          photo: {
            file: {
              name: 'photo.png',
              doc_url: 'ui2020/photo.png',
            },
          },
        },
      };
      const cleanedSeafarerJson = await addController.getCleanedSeafarer(personWithPhoto);
      expect(cleanedSeafarerJson.seafarer_person.photo_path).toEqual(
        expect.stringMatching(expectedPath),
      );
    });

    it('should add photo_path null, when photo is null', async () => {
      const personWithNoPhoto = { seafarer_person: { photo: null } };
      const cleanedSeafarerJson = await addController.getCleanedSeafarer(personWithNoPhoto);
      expect(cleanedSeafarerJson.seafarer_person.photo_path).toEqual(null);
    });
  });

  describe('get changes', () => {
    it('should discard the values which are same', async () => {
      expect({}).toBeTruthy();

      const initial = {
        id: 100,
        seafarer_person: {
          id: 101,
          first_name: 'firstName',
          nationality: {
            id: 105,
            value: 'India',
          },
          bank_accounts: [
            {
              id: 107,
              account_holder_first_name: 'first',
              document: [
                {
                  id: 5,
                  name: 'firstTest.pdf',
                },
                {
                  id: 6,
                  name: 'secondTest.pdf',
                },
              ],
            },
          ],
        },
      };
      const changed = {
        id: 100,
        seafarer_person: {
          id: 101,
          // name changed
          first_name: 'firstNameChanged',
          nationality: {
            id: 105,
            // value changed
            value: 'IndiaChanged',
          },
          bank_accounts: [
            {
              id: 107,
              account_holder_first_name: 'first',
              document: [
                {
                  id: 5,
                  name: 'firstTest.pdf',
                },
                // id 6 deleted
                // new document added
                {
                  name: 'ThridNewAdded.pdf',
                },
              ],
            },
          ],
          photos: [],
        },
      };
      const val = addController.difference(changed, initial);
      expect(val).toEqual({
        id: 100,
        seafarer_person: {
          first_name: 'firstNameChanged',
          id: 101,
          nationality: {
            id: 105,
            value: 'IndiaChanged',
          },
          bank_accounts: [
            {
              id: 107,
              document: [
                {
                  id: 5,
                },
                {
                  name: 'ThridNewAdded.pdf',
                },
              ],
            },
          ],
          photos: [],
        },
      });
    });
  });
});
