import React from 'react';
import { Router } from 'react-router-dom';
import { createMemoryHistory } from 'history';
import { waitFor, within, fireEvent, render, act } from '@testing-library/react';
import VesselEditPlanPage from '../../pages/report/modeller/VesselPlanEditPage';
import { mockedVesselPlanTableData } from '../resources/vessel-plan-table';
import seafarerService from '../../service/seafarer-service';
import seafarerReportService from '../../service/seafarer-report-service';
import dropdowndata from '../resources/drop-down-data.json';
import * as referenceService from '../../service/reference-service';
import * as mockResponse from '../resources/document-response';
import '@testing-library/jest-dom';
import { AccessProvider } from '@src/component/common/Access';

jest.mock('../../styleGuide');
jest.mock('../../service/user-service');
jest.mock('../../service/travel-service');
jest.mock('../../service/seafarer-service');

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'), // use actual for all non-hook parts
  useParams: () => ({
    vesselOwnerShipId: '1637',
  }),
  useRouteMatch: () => ({ url: 'seafarer-reports/modeller/1637/edit' }),
}));

describe('<VesselPlanPage />', () => {
  const renderVesselEditPlanPage = (history, viewGeneral: boolean, editReportModeller: boolean) =>
    render(
      <Router history={history}>
        <AccessProvider config={{
            seafarer: {
              view: {
                general: viewGeneral,
              },
              edit: {
                reportModeller: editReportModeller,
              },
            },
          }}>
        <VesselEditPlanPage />
        </AccessProvider>
        ,
      </Router>,
    );

  describe('page default view', () => {
    beforeAll(async () => {
      seafarerReportService.getVesselPlanByOwnershipId = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ data: mockedVesselPlanTableData }));
      seafarerService.getSeafarerDropDownData = jest.fn().mockImplementation(() => dropdowndata);
      seafarerService.getSeafarerDocumentDropdown = jest
        .fn()
        .mockImplementation(() => dropdowndata);
      seafarerService.getSeafarerReportingOfficeDropDownData = jest
        .fn()
        .mockImplementation(() => [dropdowndata.offices]);
      jest
        .spyOn(referenceService, 'getVisaRegionDropDownData')
        .mockImplementation(() => Promise.resolve(mockResponse.visaRegionReferenceApiResponseData));
      jest
        .spyOn(seafarerReportService, 'patchVesselPlan')
        .mockImplementation(() => Promise.resolve({}));
    });

    describe('Render vessel edit plan page', () => {
      it('should render all table header', async () => {
        const localHistory = createMemoryHistory();
        const renderedView = renderVesselEditPlanPage(localHistory, true, true);

        const headerColumns = [
          'No.',
          'Rank',
          'Planned Number',
          'Planned Nationality',
          'Planned Wages (USD)',
        ];

        await waitFor(() => {
          const headerPW = renderedView.getByText('Planned Wages (USD)');
          expect(headerPW).toBeInTheDocument();
          const headers = renderedView.getAllByRole('columnheader');
          const headerTexts = headers.map((header) => header.textContent);

          expect(headerTexts).toEqual(expect.arrayContaining(headerColumns));
        });
      });

      it('Should validate fail when amounts in the number fields have more than 2 digits post decimal', async () => {
        const localHistory = createMemoryHistory();
        const renderedView = renderVesselEditPlanPage(localHistory, true, true);

        let inputBox: HTMLElement;
        let saveButton: HTMLElement;

        await waitFor(() => {
          const row = renderedView.getByRole('row', {
            name: /CHIEF OFFICER/i,
          });

          const cell = within(row).getAllByRole('cell');
          inputBox = within(cell[4]).getByRole('spinbutton');

          saveButton = renderedView.getByRole('button', { name: 'Save' });
        });

        act(() => {
          fireEvent.change(inputBox, { target: { value: '2.3445' } });

          fireEvent.click(saveButton);
        });

        await waitFor(() => {
          const errorFields = renderedView.getAllByText(
            'Please enter the amount with at most 2 decimal places',
          );
          expect(errorFields).toHaveLength(1);
        });
      });

      it('Should validate pass when amounts in the number fields do not have more than 2 digits post decimal', async () => {
        const localHistory = createMemoryHistory();
        const renderedView = renderVesselEditPlanPage(localHistory, true, true);

        let inputBox: HTMLElement;
        let saveButton: HTMLElement;

        await waitFor(() => {
          const row = renderedView.getByRole('row', {
            name: /CHIEF OFFICER/i,
          });

          const cell = within(row).getAllByRole('cell');
          inputBox = within(cell[4]).getByRole('spinbutton');

          saveButton = renderedView.getByRole('button', { name: 'Save' });
        });

        act(() => {
          fireEvent.change(inputBox, { target: { value: '2.35' } });

          fireEvent.click(saveButton);
        });

        // await waitFor(() => {

        // });

        // act(() => {

        // });

        await waitFor(() => {
          const errorFields = renderedView.queryAllByText(
            'Please enter the amount with at most 2 decimal places',
          );
          expect(errorFields).toHaveLength(0);
        });
      });

      it('Should validate patch body to only include fields that were updated in UI', async () => {
        const localHistory = createMemoryHistory();
        const renderedView = renderVesselEditPlanPage(localHistory, true, true);

        const patchSpy = jest
          .spyOn(seafarerReportService, 'patchVesselPlan')
          .mockImplementation(() => Promise.resolve({}));

        let inputBox: HTMLElement;
        let inputBox2: HTMLElement;
        let inputBox3: HTMLElement;
        let saveButton: HTMLElement;

        await waitFor(() => {
          const row = renderedView.getByRole('row', {
            name: /MASTER/i,
          });
          const cell = within(row).getAllByRole('cell');
          inputBox = within(cell[4]).getByRole('spinbutton');

          const row2 = renderedView.getByRole('row', {
            name: /CHIEF OFFICER/i,
          });
          const cell2 = within(row2).getAllByRole('cell');
          inputBox2 = within(cell2[4]).getByRole('spinbutton');

          inputBox3 = within(cell2[2]).getByRole('spinbutton');

          saveButton = renderedView.getByRole('button', { name: 'Save' });
        });

        act(() => {
          fireEvent.change(inputBox, { target: { value: '5.22' } });

          fireEvent.change(inputBox2, { target: { value: '2.35' } });

          fireEvent.change(inputBox3, { target: { value: '10' } });

          fireEvent.click(saveButton);
        });

        await waitFor(() => {
          const payload = {
            seafarer_report_modeller_details: [
              {
                rank_id: 1,
                planned_wages: 5.22,
              },
              {
                rank_id: 2,
                planned_wages: 2.35,
                planned_number: 10,
              },
            ],
          };

          expect(patchSpy).toHaveBeenCalledWith(1637, payload);
        });
      });

      it('should show 403 page when sf|rt|md|e role is absent', async () => {
        const localHistory = createMemoryHistory();
        const renderedView = renderVesselEditPlanPage(localHistory, true, false);

        await waitFor(() => {
          const textElement = renderedView.getByText('403');
          expect(textElement).toBeInTheDocument();
        });
      });
    });
  });
});
