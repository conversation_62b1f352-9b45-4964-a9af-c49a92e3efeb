import React from 'react';
import '@testing-library/jest-dom';
import { render, screen, fireEvent, act, waitFor } from '@testing-library/react';
import { Route, MemoryRouter } from 'react-router-dom';
import moment from 'moment';
import SeafarerRecommendation from '../../pages/seafarer-recommendation/seafarer-recommendation';
import seafarerService from '../../service/seafarer-service';
import * as mockResponse from '../resources/vessel-data';
import { getSeafarerMockResponse } from '../resources/get-mock-response-details-page';
import { recommendedChecksData } from '../resources/recommended-checks-data';
import { recommendedCheckListData } from '../resources/recommended-checkList-data';
import dropdowndata from '../resources/drop-down-data.json';
import vesselService from '../../service/vessel-service';
import { getVesselById } from '../resources/get-vessel-by-id';

const { PARIS_ONE_HOST } = process.env;

jest.mock('../../styleGuide');
jest.mock('../../service/user-service');
window.scrollTo = jest.fn();

beforeAll(() => {
  jest.setTimeout(100000);
  seafarerService.getSeafarer = jest
    .fn()
    .mockImplementation(() => Promise.resolve({ data: getSeafarerMockResponse }));
  vesselService.getVesselV2Ownerships = jest
    .fn()
    .mockImplementation(() => Promise.resolve(mockResponse.getVesselList));
  seafarerService.getRecommendedCheckList = jest
    .fn()
    .mockImplementation(() => Promise.resolve({ data: recommendedCheckListData }));
  seafarerService.getRecommendedChecks = jest
    .fn()
    .mockImplementation(() => Promise.resolve({ data: recommendedChecksData }));
  seafarerService.getSeafarerDropDownData = jest
    .fn()
    .mockImplementation((input) => Promise.resolve({ status: 200, ranks: dropdowndata.ranks }));
});
afterEach(() => {
  jest.clearAllMocks();
});

const renderSeafarerRecommendationPage = () => {
  const { container } = render(
    <MemoryRouter initialEntries={['/seafarer/1/recommendation']}>
      <Route path="/seafarer/:seafarerId/recommendation">
        <SeafarerRecommendation />
      </Route>
    </MemoryRouter>,
  );
  return container;
};

describe('Testing recommendation component', () => {
  it('should component render', async () => {
    let container;
    await act(async () => {
      container = renderSeafarerRecommendationPage();
    });
    const breadcrumbLabel = container.getElementsByClassName('seafarer-recommendation');
    expect(breadcrumbLabel.length).toBe(1);
  });

  it('should have View Paris 1.0 Appraisals Page link', async () => {
    await act(async () => {
      renderSeafarerRecommendationPage();
    });
    const parin1Link = screen.getByText('View PARIS 1.0 Training Requirements');
    expect(parin1Link).toBeInTheDocument();
    expect(
      screen.getByRole('link', { name: 'View PARIS 1.0 Training Requirements' }),
    ).toHaveAttribute(
      'href',
      `${PARIS_ONE_HOST}/fml/PARIS?display=appraisals&appraisaltype=1&crewid=${getSeafarerMockResponse.ref_id}`,
    );
  });

  it('should change event fired on wages change', async () => {
    await act(async () => {
      renderSeafarerRecommendationPage();
    });
    fireEvent.keyPress(screen.getByTestId('wages'), { key: 'e' });
    const input = screen.getByTestId('wages');
    fireEvent.change(input, { target: { value: '1' } });
    expect(input.value).toBe('1');
  });

  it('should click event fired on form errors', async () => {
    await act(async () => {
      renderSeafarerRecommendationPage();
    });
    fireEvent.click(screen.getAllByTestId('errorKey')[0]);
    expect(screen.getAllByText(/Please select a vessel/i)).toHaveLength(2);
  });

  it('should change event fired on select yes answer', async () => {
    await act(async () => {
      renderSeafarerRecommendationPage();
    });
    const checkbox = fireEvent.change(screen.getByTestId('yesCheckbox'), {
      target: { value: 'yes' },
    });
    expect(checkbox).toEqual(true);
  });

  it('should change event fired on select no answer', async () => {
    await act(async () => {
      renderSeafarerRecommendationPage();
    });
    const checkbox = fireEvent.change(screen.getByTestId('noCheckbox'), {
      target: { value: 'no' },
    });
    expect(checkbox).toEqual(true);
  });

  it('should change event fired on select NA answer', async () => {
    await act(async () => {
      renderSeafarerRecommendationPage();
    });
    const checkbox = fireEvent.change(screen.getByTestId('naCheckbox'), {
      target: { value: 'n/a' },
    });
    expect(checkbox).toEqual(true);
  });
});

describe('Testing email, signOnDate, vesselname fields', () => {
  it('should change event fired on vessel change', async () => {
    await act(async () => {
      renderSeafarerRecommendationPage();
    });
    const input = screen.getByTestId('vesselName');
    fireEvent.change(input, { target: { value: '1572' } });
    await waitFor(() => expect(input.value).toBe('1572'));
  });

  it('should change event fired on email change', async () => {
    await act(async () => {
      renderSeafarerRecommendationPage();
    });
    const input = screen.getByTestId('email');
    fireEvent.change(input, { target: { value: '<EMAIL>' } });
    expect(input.value).toBe('<EMAIL>');
  });

  it('should change event fired on sign on date change', async () => {
    let container;
    await act(async () => {
      container = renderSeafarerRecommendationPage();
    });
    const input = container.querySelector('#expectedSignOnDate');
    fireEvent.change(input, { target: { value: new Date() } });
    expect(input.value).toBe(moment(new Date()).format('D MMM YYYY'));
  });

  it('should submit event fire on click submit button', async () => {
    await act(async () => {
      renderSeafarerRecommendationPage();
    });
    fireEvent.submit(screen.getByTestId('submit'));
    vesselService.getVesselById = jest.fn().mockResolvedValue({ data: getVesselById() });
    seafarerService.createRecommendation = jest.fn().mockResolvedValue({
      data: {
        error: false,
        message: 'Recommendation information saved successfully',
        statusCode: 200,
      },
    });
  });
});
