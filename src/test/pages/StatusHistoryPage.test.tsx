import React from 'react';
import { render, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { MemoryRouter, Route } from 'react-router-dom';
import StatusHistoryPage from '../../pages/StatusHistoryPage';
import { statusHistoryMockData } from '../screening-mocks';
import seafarerService from '../../service/seafarer-service';
import dropdowndata from '../resources/drop-down-data.json';
import { AccessProvider } from '@src/component/common/Access';

jest.mock('../../styleGuide');
jest.mock('../../service/user-service');

describe('<StatusHistoryPage />', () => {
  describe('page default view', () => {
    beforeAll(async () => {
      seafarerService.getSeafarerStatus = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ data: statusHistoryMockData() }));
      seafarerService.getSeafarerDropDownData = jest
        .fn()
        .mockImplementation(() => Promise.resolve(dropdowndata));
      seafarerService.getSeafarerReportingOfficeDropDownData = jest.fn().mockImplementation(() => [dropdowndata.offices]);
    });

    it('should render all table header', async () => {
      const { getByText } = render(
        <MemoryRouter initialEntries={['/seafarer/details/1/status-history']}>
          <Route path="/seafarer/details/:seafarerId/status-history">
            <AccessProvider config={{
              seafarer: {
                view: {
                  general: true,
                },
              },
            }}>
              <StatusHistoryPage />
            </AccessProvider>
          </Route>
        </MemoryRouter>
      );

      await waitFor(() => {
        const headers = ['Date', 'Vessel Name', 'Rank', 'Journey Status', 'By', 'Remark'];
        headers.forEach(headerText => {
          expect(getByText(headerText)).toBeInTheDocument();
        });
      });
    });

    it('should render the journey status data', async () => {
      const { getByText } = render(
        <MemoryRouter initialEntries={['/seafarer/details/1/status-history']}>
          <Route path="/seafarer/details/:seafarerId/status-history">
            <AccessProvider config={{
              seafarer: {
                view: {
                  general: true,
                },
              },
            }}>
              <StatusHistoryPage />
            </AccessProvider>
          </Route>
        </MemoryRouter>
      );

      await waitFor(() => {
        const journeyStatusData = ['On Leave', 'Ready with Class 4 Certificate Of Competency'];
        journeyStatusData.forEach(status => {
          expect(getByText(status)).toBeInTheDocument();
        });
      });
    });

    it('should show proper error message for empty data', async () => {
      seafarerService.getSeafarerStatus = jest.fn().mockResolvedValueOnce({ data: [] });

      const { getByText } = render(
        <MemoryRouter initialEntries={['/seafarer/details/1/status-history']}>
          <Route path="/seafarer/details/:seafarerId/status-history">
          <AccessProvider config={{
                seafarer: {
                  view: {
                    general: true,
                  },
                },
              }}>
            <StatusHistoryPage />
            </AccessProvider>
          </Route>
        </MemoryRouter>,
      );

      await waitFor(() => {
        expect(getByText('No Results Found')).toBeInTheDocument();
      });
    });
  });
});
