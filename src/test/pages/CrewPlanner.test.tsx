import React from 'react';
import { MemoryRouter, Route } from 'react-router-dom';
import { act, fireEvent, render, RenderResult, waitFor, screen } from '@testing-library/react';
import seafarerService from '@src/service/seafarer-service';
import { SEAFARERS_TO_RELIEVE } from '@src/constants/crewPlanner';
import CrewPlanner from '../../pages/CrewPlanner';

import * as mockResponse from '../resources/document-response';
import * as referenceService from '../../service/reference-service';

import '@testing-library/jest-dom';
import vesselService from '../../service/vessel-service';
import { AvailableSeafarer, ContractExpiry } from '../resources/crew-planner';

jest.mock('../../styleGuide');
jest.mock('../../service/user-service');
jest.mock('../../service/vessel-service');
jest.mock('../../service/travel-service');
// Mock the services directly in the test file

jest.mock('@src/service/seafarer-service', () => {
  const response = {
    results: [require('../resources/seafarer-mock-data').getMockedSeafarerResponse()],
    pagination: {
      totalCount: 1,
    },
  };
  const dropdowndata = require('../resources/drop-down-data.json');
  const vesselTypeAndMiscDropdown = require('../resources/vessel-experience');
  const {
    getShipPartyDataResponse,
    getTechGroupDropDownResponse,
  } = require('../resources/getMockedDropDownData');
  return {
    getSeafarers: jest.fn().mockResolvedValue({ data: response }),
    getSeafarerListUsernames: jest.fn().mockResolvedValue({ data: response }),
    getSeafarerDropDownData: jest.fn().mockResolvedValue(dropdowndata),
    getSeafarerDocumentDropdown: jest.fn().mockResolvedValue(dropdowndata),
    getDropDownDataFromVessel: jest.fn().mockResolvedValue({ ...vesselTypeAndMiscDropdown }),
    getTechGroupDropDown: jest.fn().mockResolvedValue(getTechGroupDropDownResponse()),
    getSeafarerReportingOfficeDropDownData: jest.fn().mockResolvedValue(dropdowndata.offices),

    getShipPartyOwnerList: jest.fn().mockResolvedValue(getShipPartyDataResponse()),

    getMissingRanks: jest.fn().mockResolvedValue({ data: {} }),
    getAdditionalCrewRequest: jest.fn().mockResolvedValue({ data: {} }),
    getContractExpirySeafarers: jest.fn().mockImplementation(() => Promise.resolve()),
    getRelieverSeafarersWithPagination: jest.fn().mockImplementation(() => Promise.resolve()),
  };
});
Element.prototype.getBoundingClientRect = jest.fn(() => {
  return {
    width: 120,
    height: 120,
    top: 0,
    left: 0,
    bottom: 0,
    right: 0,
  };
});
describe('<CrewPlanner />', () => {
  const renderViewFunc = async (path, addSeafarer, general) => {
    let renderResult: RenderResult;
    await act(async () => {
      renderResult = render(
        <MemoryRouter initialEntries={[path]}>
          <Route path="/seafarer/crew-planner/planner/:tab">
            <CrewPlanner
              ga4react={{ event: () => {}, pageview: () => {} }}
              keycloak={null}
              alertRef={null}
              isVisible
              setAlertMessage={() => {}}
              roleConfig={{
                seafarer: {
                  addSeafarer,
                  view: {
                    general,
                  },
                },
              }}
            />
          </Route>
        </MemoryRouter>,
      );
    });
    return renderResult;
  };
  beforeEach(async () => {
    jest
      .spyOn(referenceService, 'getVisaRegionDropDownData')
      .mockImplementation(() => Promise.resolve(mockResponse.visaRegionReferenceApiResponseData));
    vesselService.queryVesselOwnership = jest.fn().mockImplementation(() =>
      Promise.resolve({
        results: [
          {
            id: 2277,
            name: 'TS Tianjin Copy1',
            vessel_id: 1963,
            owner: {
              value: 'new owner',
            },
          },
        ],
        totalCount: 1,
      }),
    );
    jest
      .spyOn(seafarerService, 'getContractExpirySeafarers')
      .mockImplementation(() => Promise.resolve(ContractExpiry));
    jest
      .spyOn(seafarerService, 'getRelieverSeafarersWithPagination')
      .mockImplementation(() => Promise.resolve(AvailableSeafarer));
  });

  describe('should render tab', () => {
    afterEach(async () => {
      jest.clearAllMocks();
    });
    it('Tabs should be visible and cold start screen should come', async () => {
      const localHistory = '/seafarer/crew-planner/planner/seafarers-to-relieve';
      const list = await renderViewFunc(localHistory, true, true);

      const expectedHeader = ['Due Relieve Seafarer', 'On Leave Seafarer'];
      const messageIncaseOfRankNotSelected = list.queryByText(
        'To plan Seafarers to Relief, start by selecting the Rank',
      );
      await waitFor(() => {
        expectedHeader.forEach((e) => {
          const header = list.getAllByText(e);
          expect(header[0]).toBeInTheDocument();
          expect(messageIncaseOfRankNotSelected).toBeInTheDocument();
        });
      });
    });
    it('Due Relieve Table should be visible when rank is selected', async () => {
      const list = await renderViewFunc(
        '/seafarer/crew-planner/planner/seafarers-to-relieve?&seafarer_person:seafarer_status_history:seafarer_rank.value=41&crew_planning_status=unplanned',
        true,
        true,
      );
      await waitFor(() => {
        const [rank] = list.queryAllByTitle('2ND COOK');
        expect(seafarerService.getSeafarerDropDownData).toHaveBeenCalled();
        expect(rank).toBeInTheDocument();
      });
    });
    it('Second table should load when selecting a seafarer of Due Relieve ', async () => {
      const crewPlanner = await renderViewFunc(
        '/seafarer/crew-planner/planner/seafarers-to-relieve?&seafarer_person:seafarer_status_history:seafarer_rank.value=41&crew_planning_status=unplanned',
        true,
        true,
      );

      await waitFor(() => {
        expect(seafarerService.getContractExpirySeafarers).toHaveBeenCalled();
        expect(crewPlanner.getByText('JONGGIL SE *')).toBeInTheDocument();
      });
      const checkbox = crewPlanner.getAllByTestId('checkbox-for-seafarer');
      fireEvent.click(checkbox[0]);
      await waitFor(() => {
        expect(checkbox[0]).toBeChecked();
      });
      const button = crewPlanner.getByText('Available Seafarers');
      fireEvent.click(button);
      await waitFor(() => {
        expect(seafarerService.getRelieverSeafarersWithPagination).toHaveBeenCalled();
        expect(crewPlanner.getByText('XINGYAN CHwqwq')).toBeInTheDocument();
      });
    });
    it('Clubbed tab should render when clicking on it of Due Seafarer', async () => {
      const crewPlanner = await renderViewFunc(
        '/seafarer/crew-planner/planner/seafarers-to-relieve?&seafarer_person:seafarer_status_history:seafarer_rank.value=41&crew_planning_status=clubbed,club_confirmed',
        true,
        true,
      );

      await waitFor(() => {
        expect(seafarerService.getContractExpirySeafarers).toHaveBeenCalled();
        // Check second table message
        const button = crewPlanner.getByText('Clubbed');
        fireEvent.click(button);
        expect(crewPlanner.getByText('Reliever Seafarer')).toBeInTheDocument();
      });
    });
    it('On Leave Seafarer Table should be visible when rank is selected', async () => {
      const list = await renderViewFunc(
        '/seafarer/crew-planner/planner/on-leave-seafarers?&target_rank.value=41&crew_planning_status=unplanned',
        true,
        true,
      );
      await waitFor(() => {
        expect(seafarerService.getSeafarerDropDownData).toHaveBeenCalled();
        const rank = list.getByText('2ND COOK');
        expect(rank).toBeInTheDocument();
      });
    });
    it('Checkbox should work when selecting a seafarer of On Leave Seafarer', async () => {
      const crewPlanner = await renderViewFunc(
        '/seafarer/crew-planner/planner/on-leave-seafarers?&target_rank.value=41&crew_planning_status=unplanned',
        true,
        true,
      );
      await waitFor(
        () => {
          expect(seafarerService.getRelieverSeafarersWithPagination).toHaveBeenCalled();
        },
        { timeout: 4000 },
      );
      // Check second table message
      expect(
        crewPlanner.getByText(
          'Select a On-Leave Seafarer to view recommendation for relieving Onboarding Seafarer',
        ),
      ).toBeInTheDocument();
      expect(crewPlanner.getByText('XINGYAN CHwqwq')).toBeInTheDocument();

      const checkbox = crewPlanner.getAllByTestId('checkbox-for-seafarer');
      fireEvent.click(checkbox[0]);
      expect(checkbox[0]).toBeChecked();
    });
    it('Unplanned tab should render when clicking on it of On Leave Seafarer', async () => {
      const crewPlanner = await renderViewFunc(
        '/seafarer/crew-planner/planner/on-leave-seafarers?&target_rank.value=41&crew_planning_status=unplanned',
        true,
        true,
      );
      await waitFor(() => {
        expect(seafarerService.getRelieverSeafarersWithPagination).toHaveBeenCalled();
        // Check second table message
        const button = crewPlanner.getByText('Unclubbed');
        fireEvent.click(button);
        expect(crewPlanner.getByText('Onboard Seafarer Remarks')).toBeInTheDocument();
      });
    });
  });
});

const mockRoleConfig = {
  seafarer: {
    view: {
      crewPlannerSeafarer: true,
      general: true,
    },
    edit: {
      crewPlannerSeafarer: true,
    },
  },
};

const mockPlannerRanks = [
  { rank_id: 1, rank: 'MASTER' },
  { rank_id: 2, rank: 'Chief Engineer' },
];

const mockVesselTypesList = [
  { id: 1, value: 'Bulk Carrier' },
  { id: 2, value: 'Tanker' },
];

const renderCrewPlanner = async (initialTab = SEAFARERS_TO_RELIEVE) => {
  await act(async () => {
    render(
      <MemoryRouter initialEntries={[`/seafarer/crew-planner/planner/${initialTab}`]}>
        <Route path="/seafarer/crew-planner/planner/:tab">
          <CrewPlanner
            keycloak={{ office: 'HK' }}
            roleConfig={mockRoleConfig}
            ga4react={{}}
            isVisible
            plannerRanks={mockPlannerRanks}
            vesselTypesList={mockVesselTypesList}
            setAlertMessage={jest.fn()}
            alertRef={{ current: { hide: jest.fn() } }}
            setTimeoutError={jest.fn()}
          />
        </Route>
      </MemoryRouter>,
    );
  });
};
describe('CrewPlanner Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    seafarerService.getRelieverSeafarersWithPagination.mockResolvedValue({
      data: { results: [], pagination: { totalCount: 0 } },
    });
    vesselService.queryVesselOwnership.mockResolvedValue([]);
  });

  it('should render the CrewPlanner component with default tab', async () => {
    await renderCrewPlanner();
    expect(screen.getByText('Due in 30 Days')).toBeInTheDocument();
    expect(screen.getByText('Overdue Seafarers')).toBeInTheDocument();
    expect(screen.getByText('Missing Ranks')).toBeInTheDocument();
    expect(screen.getByText('Not Contacted (Last 15 Days)')).toBeInTheDocument();
    expect(screen.getByText('Available (Not Planned)')).toBeInTheDocument();
  });

  it('should switch tabs and render the correct content', async () => {
    await renderCrewPlanner();
    const onLeaveTab = screen.getByText('On Leave Seafarer');
    fireEvent.click(onLeaveTab);

    await waitFor(() => {
      expect(screen.getByText('Available (Not Planned)')).toBeInTheDocument();
    });
  });
});
