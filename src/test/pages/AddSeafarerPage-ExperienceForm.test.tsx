import React from 'react';
import { act } from 'react-dom/test-utils';

import { screen, within, fireEvent, waitFor } from '@testing-library/react';
import { createMemoryHistory } from 'history';
import AddSeafarePage from '../../pages/AddSeafarerPage';

import seafarerService from '../../service/seafarer-service';
import * as referenceService from '../../service/reference-service';

import * as mockResponse from '../resources/document-response';
import dropdowndata from '../resources/drop-down-data.json';
import bankNameResponse from '../resources/bank-name-data.json';

import * as documentTypes from '../../constants/documentTypes';
import { renderWithRoute } from '../util/test-utils';
import '@testing-library/jest-dom';

jest.mock('../../styleGuide');
jest.mock('../../service/user-service');
jest.mock('../../service/travel-service');

let history: any;

describe('AddSeafarerPage Experience Form', () => {
  beforeAll(async () => {
    jest.setTimeout(*********);

    seafarerService.getSeafarerDropDownData = jest.fn().mockImplementation(() => dropdowndata);
    seafarerService.getBankNames = jest.fn().mockImplementation(() => bankNameResponse);
    seafarerService.getSeafarerDocumentDropdown = jest.fn().mockImplementation(() => dropdowndata);
    seafarerService.getSeafarerReportingOfficeDropDownData = jest
      .fn()
      .mockImplementation(() => [dropdowndata.offices]);
    jest.spyOn(referenceService, 'getPortsOfIssueForSeamanBook').mockImplementation(() => {
      return { ports: [] };
    });
    jest
      .spyOn(referenceService, 'getVisaRegionDropDownData')
      .mockImplementation(() => Promise.resolve(mockResponse.visaRegionReferenceApiResponseData));
  });

  describe('For seafarer with ice experience', () => {
    it('Should open document create modal for ice experience document when click on upload button', async () => {
      seafarerService.getSeafarer = jest
        .fn()
        .mockImplementation(() => Promise.resolve(mockResponse.seafarerGetResponse));

      await mountAddSeafarerPageExperienceTab();

      const uploadBtn = screen.getByTestId('upload-ice-experience-btn');
      const editBtn = screen.queryByTestId('edit-ice-experience-btn');

      expect(uploadBtn).toBeInTheDocument();
      expect(editBtn).not.toBeInTheDocument();

      act(() => {
        fireEvent.click(uploadBtn);
      });

      expectDocumentModalToBeOpened();
      expectFormDropDownToHaveFormId(documentTypes.DOC_FORM_IDS.USER_DEFINED_DOCUMENT);

      const userDefinedDocTypeDropdown = screen.getByTestId('user-defined-doc-type-dropdown');
      expect(userDefinedDocTypeDropdown.disabled).toBe(false);
    });
  });

  describe('For seafarer with ice experience', () => {
    describe('when click on edit button', () => {
      beforeAll(async () => {
        seafarerService.getSeafarer = jest
          .fn()
          .mockImplementation(() =>
            Promise.resolve(mockResponse.seafarerWithIceExperienceGetResponse),
          );
      });

      describe('if form data has not been changed', () => {
        let uploadBtn, editBtn;

        beforeAll(async () => {
          await mountAddSeafarerPageExperienceTab();

          uploadBtn = screen.queryByTestId('upload-ice-experience-btn');
          editBtn = screen.queryByTestId('edit-ice-experience-btn');
        });

        it('Should not have modal prompt for unsaved data ', async () => {
          expect(uploadBtn).not.toBeInTheDocument();
          expect(editBtn).toBeInTheDocument();

          await act(async () => {
            fireEvent.click(editBtn);
          });

          const confirmActionModal = screen.queryByTestId('confirm-action-modal');
          expect(confirmActionModal).not.toBeInTheDocument();
        });

        it('should go to other documents page when click on edit button', async () => {
          expect(history.location.pathname).toBe('/seafarer/details/1234/other-documents');
          expect(history.location.hash).toBe('#user-defined-document');
        });
      });

      describe('if form data has been updated', () => {
        let uploadBtn, editBtn;

        beforeAll(async () => {
          await mountAddSeafarerPageExperienceTab();

          uploadBtn = screen.queryByTestId('upload-ice-experience-btn');
          editBtn = screen.queryByTestId('edit-ice-experience-btn');
        });

        it('Should prompt users for unsaved changes if input has been chnaged when click on edit button', async () => {
          expect(uploadBtn).not.toBeInTheDocument();
          expect(editBtn).toBeInTheDocument();

          await act(async () => {
            const additionalExpTextArea = screen.getByTestId('additional-experience-text-area');
            fireEvent.change(additionalExpTextArea, {
              target: { name: 'additional_experience', value: 'my additional exp' },
            });
            fireEvent.click(editBtn);
          });
          await waitFor(() => {
            const confirmActionModal = screen.getByTestId('confirm-action-modal');
            expect(confirmActionModal).toBeInTheDocument();

            const modalMessage = confirmActionModal.querySelector('.modal-body').textContent;
            expect(modalMessage).toEqual(
              'Your changes will be lost. Are you sure you want to cancel?',
            );
          });
        });
      });
    });
  });
});

const mountAddSeafarerPageExperienceTab = async () => {
  history = createMemoryHistory();
  history.push('/seafarer/1234/add/experience');
  await renderWithRoute(
    history,
    <AddSeafarePage />,
    '/seafarer/:seafarerId?/add/experience',
    {
      seafarer: {
        // addSeafarer: true,
        editSeafarer: true,
        edit: {
          personalDetails: true,
          passport: true,
          seamansBook: true,
          seafarerExperience: true,
        },
        create: {},
        hidden: {
          bankAccount: true,
        },
      },
    }
  );
};

const expectDocumentModalToBeOpened = () => {
  const documentEditModal = screen.getByRole('dialog');
  expect(documentEditModal).toBeInTheDocument();
};

const expectFormDropDownToHaveFormId = (expectedFormId) => {
  const docTypeDropdown = screen.getByTestId('doc-type-dropdown');
  const dropDownOption = within(docTypeDropdown).getByText('User Defined Document');

  expect(dropDownOption).toHaveValue(expectedFormId + '');
  expect(docTypeDropdown.disabled).toBe(true);
};
