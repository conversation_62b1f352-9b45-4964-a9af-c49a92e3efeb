import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import CrewListLink from '../../component/CrewList/CrewListLink';

const mockEventTracker = jest.fn();
const mockWindowOpen = jest.spyOn(window, 'open').mockImplementation();

describe('CrewListLink Component', () => {
  const vesselId = 123;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the Crew List button', () => {
    render(<CrewListLink vesselId={vesselId} eventTracker={mockEventTracker} />);
    expect(screen.getAllByText('Crew List')).toBeTruthy();
  });

  it('calls eventTracker and opens a new window when clicked', () => {
    render(<CrewListLink vesselId={vesselId} eventTracker={mockEventTracker} />);
    const button = screen.getByRole('button', { name: /crew list/i });

    fireEvent.click(button);

    expect(mockEventTracker).toHaveBeenCalledWith('crewListLink', 'Routes to Crew List');
    expect(mockWindowOpen).toHaveBeenCalledWith(`/seafarer/crew-list/vessel/${vesselId}`, '_blank');
  });
});
