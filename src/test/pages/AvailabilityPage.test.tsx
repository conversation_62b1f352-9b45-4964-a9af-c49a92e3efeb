import React from 'react';
import '@testing-library/jest-dom';
import { screen, waitFor } from '@testing-library/react';
import { createMemoryHistory } from 'history';
import { renderWithRoute } from '../util/test-utils';
import seafarerService from '../../service/seafarer-service';
import { seafarerGetResponse } from '../resources/document-response';
import { getContactLogResponse } from '../resources/contact-log-response';
import AvailabilityPage from '../../pages/AvailabilityPage';

jest.mock('../../styleGuide');
jest.mock('../../service/user-service');
jest.mock('../../service/seafarer-service');
const roleConfig = {
  seafarer: {
    screening: {
      view: true,
    },
    create: {},
    edit: {
      duplicateHKID: false,
      seafarerExperience: true,
    },
    view: {
      general: true,
      bankAccount: true,
      hkid: true,
    },
    hidden: {
      contactDetails: false,
    },
  },
};

const history = createMemoryHistory();

const renderAvailabilityPage = async (seafarerId) => {
  history.push(`/seafarer/details/${seafarerGetResponse.data.id}/status-history`);
  await renderWithRoute(
    history,
    <AvailabilityPage
      seafarer={seafarerGetResponse}
      seafarerId={seafarerId}
    />,
    '/seafarer/details/:seafarerId/status-history',
    roleConfig
  );
};

describe('<AvailabilityPage />', () => {
  beforeAll(async () => {
    seafarerService.getContactLog = jest.fn().mockResolvedValue({
      status: 200,
      data: getContactLogResponse,
    });
  });

  it('should render all table header', async () => {
    await renderAvailabilityPage(seafarerGetResponse.data.id);

    const headerColumns = [
      'Contact Date',
      'Contact Mode',
      'Next Contact Date',
      'Docs in Hand',
      'Availability Date',
      'Availability Remark',
      'Added by',
    ];

    const headerElements = screen.getAllByRole('columnheader');
    const headers = headerElements.map((header) => header.textContent);
    expect(headers).toEqual(expect.arrayContaining(headerColumns));
  });

  it('should render contact log data', async () => {
    await renderAvailabilityPage(seafarerGetResponse.data.id);

    const expectedData = [
      '04 Apr 2022',
      'telephone',
      '04 Sep 2022',
      'No',
      '21 Sep 2022',
      'huhu',
      '<EMAIL>',
    ];

    await waitFor(() => screen.queryByText(/Contact Log/));

    const cellElements = screen.getAllByRole('cell');
    const row = cellElements.map((cell) => cell.textContent).slice(15, 22);

    expect(row).toEqual(expect.arrayContaining(expectedData));
  });
});
