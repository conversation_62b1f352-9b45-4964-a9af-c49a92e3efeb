import { PdfUtils } from '@src/util/pdf-utils';
import { jsPDF } from 'jspdf';

jest.mock('jspdf', () => {
  return {
    jsPDF: jest.fn().mockImplementation(() => ({
      text: jest.fn(),
      setFontSize: jest.fn(),
      setFont: jest.fn(),
      addPage: jest.fn(),
      setPage: jest.fn(),
      getStringUnitWidth: jest.fn().mockReturnValue(10),
      splitTextToSize: jest.fn().mockReturnValue(['Line 1', 'Line 2']),
      internal: {
        pageSize: {
          width: 210,
          height: 297,
          getWidth: jest.fn().mockReturnValue(210),
        },
        getNumberOfPages: jest.fn().mockReturnValue(1),
        scaleFactor: 1,
      },
      line: jest.fn(),
    })),
  };
});

describe('PdfUtils', () => {
  let pdf: jsPDF;
  let pdfUtils: PdfUtils;

  beforeEach(() => {
    pdf = new jsPDF();
    pdfUtils = new PdfUtils(pdf, 10, 10, 'helvetica', 12, 20, 'middle');
  });

  it('should set the page title correctly', () => {
    pdfUtils.addPageTitle('Test Title');
    expect(pdf.setFontSize).toHaveBeenCalledWith(16);
    expect(pdf.setFont).toHaveBeenCalledWith('helvetica', 'bold');
    expect(pdf.text).toHaveBeenCalledWith('Test Title', 105, pdfUtils.titleStartY, {
      align: 'center',
    });
  });

  it('should add header and footer correctly', () => {
    pdfUtils.addHeaderFooter('Header Text', 'Footer Text');
    expect(pdf.setFontSize).toHaveBeenCalledWith(12);
    expect(pdf.setFont).toHaveBeenCalledWith('helvetica', 'bold');
    expect(pdf.text).toHaveBeenCalledWith('Header Text', 10, 10);
    expect(pdf.text).toHaveBeenCalledWith('Footer Text', 10, pdfUtils.footerStartY);
  });

  it('should generate a table correctly', () => {
    const tableData = [
      ['Header 1', 'Header 2'],
      ['Row 1 Col 1', 'Row 1 Col 2'],
    ];
    const cellWidths = [50, 50];

    pdfUtils.generateTable(tableData, cellWidths);

    expect(pdf.text).toHaveBeenCalledWith('Header 1', 10, 10, { align: 'left' });
    expect(pdf.text).toHaveBeenCalledWith('Header 2', 60, 10, { align: 'left' });
    expect(pdf.text).toHaveBeenCalledWith('Row 1 Col 1', 10, 30, { align: 'left' });
    expect(pdf.text).toHaveBeenCalledWith('Row 1 Col 2', 60, 30, { align: 'left' });
  });

  it('should generate paragraphs with points correctly', () => {
    const paragraphs = ['Point 1', 'Point 2', '   Subpoint 2.1'];
    pdfUtils.generateParagraphsWihPoints(paragraphs, 10);

    // Verify the first point
    expect(pdf.text).toHaveBeenCalledWith('1.', 10, 10);
    expect(pdf.text).toHaveBeenCalledWith('Point ', 22, 10);
    expect(pdf.text).toHaveBeenCalledWith('1', 22, 24.4);
  });

  it('should generate justified text correctly', () => {
    const paragraph = 'This is a test paragraph that should be justified.';
    pdfUtils.generateNormalParagraph(paragraph, 10);

    expect(pdf.text).toHaveBeenCalledWith(
      expect.any(String),
      expect.any(Number),
      expect.any(Number),
      {
        align: 'left',
      },
    );
  });

  it('should handle page breaks when generating a table', () => {
    const tableData = Array(50).fill(['Row 1 Col 1', 'Row 1 Col 2']);
    const cellWidths = [50, 50];

    pdfUtils.generateTable(tableData, cellWidths);

    expect(pdf.addPage).toHaveBeenCalled();
  });
});
