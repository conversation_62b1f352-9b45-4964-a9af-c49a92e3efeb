import React from 'react';
import { screen } from '@testing-library/react';
import { createMemoryHistory } from 'history';
import AddSeafarePage from '../../pages/AddSeafarerPage';
import { renderWithRoute } from '../util/test-utils';
import '@testing-library/jest-dom';

jest.mock('../../styleGuide');
jest.mock('../../service/user-service');
jest.mock('../../controller/add-seafarer-controller');

const renderSeafarerPage = async (haveEditRole: boolean, initialEntry: string, urlPath: string) => {
  const history = createMemoryHistory();
  history.push(initialEntry);
  await renderWithRoute(history, <AddSeafarePage />, urlPath, {
    seafarer: {
      addSeafarer: false,
      editSeafarer: haveEditRole,
      edit: {
        personalDetails: true,
        passport: true,
        seamansBook: true,
      },
      create: {},
      hidden: {
        bankAccount: false,
      },
    },
  });
};

describe('AddSeafarerPage role based view', () => {
  it('should render no access view when user does not have edit role', async () => {
    await renderSeafarerPage(false, '/seafarer/1/add/basic', '/seafarer/:seafarerId?/add/:step?');

    const textElement = screen.getByText('403');
    expect(textElement).toBeInTheDocument();
  });

  it('should render edit view when user has edit role', async () => {
    await renderSeafarerPage(true, '/seafarer/1/add/basic', '/seafarer/:seafarerId?/add/:step?');

    const textElement = screen.queryByText('403');
    expect(textElement).not.toBeInTheDocument();

    const textElement2 = screen.getByText('Basic');
    expect(textElement2).toBeInTheDocument();
  });

  it('should not render add view when user has only edit role', async () => {
    await renderSeafarerPage(true, '/seafarer/add/basic', '/seafarer/:seafarerId?/add/:step?');

    const textElement = screen.getByText('403');
    expect(textElement).toBeInTheDocument();

    const textElement2 = screen.queryByText('Basic');
    expect(textElement2).not.toBeInTheDocument();
  });
});
