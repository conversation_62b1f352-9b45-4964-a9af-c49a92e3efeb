import React from 'react';
import '@testing-library/jest-dom';
import { act } from 'react-dom/test-utils';
import { createMemoryHistory } from 'history';
import { Router } from 'react-router-dom';
import {
  waitFor,
  screen,
  fireEvent,
  within,
  render,
} from '@testing-library/react';
import Details from '../../pages/Details';
import seafarerService from '../../service/seafarer-service';
import seafarerSurveryService from '../../service/seafarer-survery-service';
import * as referenceService from '../../service/reference-service';
import dropdowndata from '../resources/drop-down-data.json';
import ImageController from '../../controller/image-upload-controller';

import * as documentTypes from '../../constants/documentTypes';
import * as mockResponse from '../resources/document-response';
import { renderWithRoute } from '../util/test-utils';
import * as mockRecommendationCheckData from '../resources/recommended-checks-data';
import { getSeafarerStatusHistoryByPersonIDMockResponse } from '../resources/seafarer-status-history';
import { AccessProvider } from '@src/component/common/Access';

jest.mock('../../styleGuide');
jest.mock('../../service/user-service');
jest.mock('../../service/travel-service');
jest.mock('../../service/vessel-service');
jest.mock('../../service/seafarer-service');

/**
 * Should cover
 * - render from the Details component with edit url to test the route
 * - render from the Details component with document page url, then click on edit to open the edit modal
 *
 * For other logic specific to the modal, to be covered in
 * src/test/component/document/AddDocumentModal.test.js
 */

const EDIT_DOC_BTN = 'edit-doc-btn';
const DELETE_DOC_BTN = 'delete-doc-btn';

const PATH_ID_DOCUMENT = '/seafarer/details/1234/id-documents';
const PATH_ENDORSMENT = '/seafarer/details/1234/endorsement';
const PATH_OTHER_DOC = '/seafarer/details/1234/other-documents';

const VISA_TABLE = 'visa-table';
const INDOS_TABLE = 'indos-table';
const PASSPORT_TABLE = 'passport-table';
const SEAMAN_BOOK_TABLE = 'seaman-book-table';

const ENDORSEMENT_TABLE = 'endorsement-table';
const DCE_TABLE = 'dce-verification-table';
const DOC_VER_TABLE = 'document-verification-table';

const renderViewFunc = (localHistory, roleConfig) =>
  render(
    <Router history={localHistory}>
      <AccessProvider config={roleConfig}>
        <Details seafarer />
      </AccessProvider>
    </Router>,
  );


describe('Render Details Page with document related routes', () => {
  const renderView = async (renderRoute: string, roleConfig: any) => {
    const historyTmp = createMemoryHistory();
    historyTmp.push(renderRoute);
    await renderWithRoute(historyTmp, <Details seafarer />, renderRoute, roleConfig);
  };

  describe('Render the document routes', () => {
    const mockImageController = () => {
      ImageController.prototype.downloadSeafarerImage = jest.fn().mockImplementation(() => {
        return Promise.resolve({});
      });

      ImageController.prototype.arrayBufferToBase64 = jest.fn().mockImplementation(() => { });
    };
    beforeAll(async () => {
      jest.setTimeout(100000000);
      mockImageController();
      seafarerService.getSeafarerStatusHistoryByPersonID = jest
        .fn()
        .mockImplementation(() =>
          Promise.resolve({ data: getSeafarerStatusHistoryByPersonIDMockResponse() }),
        );
      seafarerService.getRecommendedChecks = jest.fn().mockImplementation(() =>
        Promise.resolve({
          status: 200,
          data: mockRecommendationCheckData.recommendedChecksData(),
        }),
      );
      seafarerService.getSeafarer = jest
        .fn()
        .mockImplementation(() => Promise.resolve(mockResponse.seafarerGetResponse));

      seafarerService.getSeafarerDocument = jest
        .fn()
        .mockImplementation(() => Promise.resolve(mockResponse.visaDocumentGetResponse));

      /**
       * Mock all 3 API calls in loadDropDownData in src/controller/add-seafarer-controller.js
       * - seafarerService.getSeafarerDropDownData
       * - seafarerService.getSeafarerDocumentDropdown
       * - referenceService.getVisaRegionDropDownData
       */
      seafarerService.getSeafarerDropDownData = jest.fn().mockImplementation(() => dropdowndata);
      seafarerService.getSeafarerReportingOfficeDropDownData = jest
        .fn()
        .mockImplementation(() => [dropdowndata.offices]);
      seafarerService.getSeafarerDocumentDropdown = jest
        .fn()
        .mockImplementation(() => dropdowndata);
      seafarerSurveryService.getSuptAppraisalList = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ data: { results: [] } }));
        jest.spyOn(referenceService, 'getVisaRegionDropDownData').mockImplementation(() =>
          Promise.resolve(mockResponse.visaRegionReferenceApiResponseData)
        );
    });

    describe('Check role and visibilility of buttons in different document tabs', () => {
      const expectEditAndDeleteBtnToBeRendered = (tableDataTestId: string) => {
        const table = screen.getByTestId(tableDataTestId);
        expect(table).toBeInTheDocument();

        const editBtns = within(table).queryAllByTestId(EDIT_DOC_BTN);
        const deleteBtns = within(table).queryAllByTestId(DELETE_DOC_BTN);

        expect(editBtns.length).toBeGreaterThan(0);
        expect(deleteBtns.length).toBeGreaterThan(0);
      };

      const expectEditAndDeleteBtnNotToBeRendered = (tableDataTestId: string) => {
        const table = screen.getByTestId(tableDataTestId);
        expect(table).toBeInTheDocument();

        const editBtns = within(table).queryAllByTestId(EDIT_DOC_BTN);
        const deleteBtns = within(table).queryAllByTestId(DELETE_DOC_BTN);

        expect(editBtns.length).toEqual(0);
        expect(deleteBtns.length).toEqual(0);
      };

      const expectOnlyEditButNoDeleteBtnToBeRendered = (tableDataTestId: string) => {
        const table = screen.getByTestId(tableDataTestId);
        expect(table).toBeInTheDocument();

        const editBtns = within(table).queryAllByTestId(EDIT_DOC_BTN);
        const deleteBtns = within(table).queryAllByTestId(DELETE_DOC_BTN);

        expect(editBtns.length).toBeGreaterThan(0);
        expect(deleteBtns.length).toEqual(0);
      };

      beforeAll(() => {
        seafarerService.getSeafarerDocumentsList = jest
          .fn()
          .mockImplementation(() =>
            Promise.resolve(mockResponse.documentListResponses.idDocumentTab),
          );
      });

      describe('ID Document tab', () => {
        describe('when edit seafarerDocument role is assigned', () => {
          const roleConfig = {
            seafarer: {
              view: {
                general: true,
              },
              edit: {
                seafarerDocument: true,
              },
              screening: {},
              user: {
                manage: true,
              },
            },
          };

          beforeEach(async () => {
            await renderView(PATH_ID_DOCUMENT, roleConfig);
          });

          it('should render the visa section with both edit/delete button if edit seafarerDocument role is assigned', async () => {
            await waitFor(() => screen.getByText(/VISA/));
            expectEditAndDeleteBtnToBeRendered(VISA_TABLE);
            expect(true).toEqual(true);
          });

          it('should render the indos section with both edit/delete button if edit seafarerDocument role is assigned', async () => {
            await waitFor(() => screen.getByText(/INDIAN DATABASE OF SEAFARERS NUMBER/));
            expectEditAndDeleteBtnToBeRendered(INDOS_TABLE);
            expect(true).toEqual(true);
          });

          it('should render the passport section with only edit button if edit seafarerDocument role is assigned', async () => {
            await waitFor(() => screen.getByText(/PASSPORT/));
            expectOnlyEditButNoDeleteBtnToBeRendered(PASSPORT_TABLE);
            expect(true).toEqual(true);
          });

          it("should render the Seaman's book with only edit button if edit seafarerDocument role is assigned", async () => {
            await waitFor(() => screen.getByText(/SEAMAN'S BOOK/));
            expectOnlyEditButNoDeleteBtnToBeRendered(SEAMAN_BOOK_TABLE);
            expect(true).toEqual(true);
          });
        });

        describe('when edit seafarerDocument role is missing', () => {
          beforeAll(() => {
            seafarerService.getSeafarerDocumentsList = jest
              .fn()
              .mockImplementation(() =>
                Promise.resolve(mockResponse.documentListResponses.idDocumentTab),
              );
          });

          beforeEach(async () => {
            await renderView(PATH_ID_DOCUMENT, {
              seafarer: {
                view: {
                  general: true,
                },
                edit: {
                  seafarerDocument: false,
                },
                screening: {},
                user: {
                  manage: true,
                },
              },
            });
          });

          it('should render the visa section with no edit/delete button if edit seafarerDocument role is missing', async () => {
            await waitFor(() => screen.getByText(/VISA/));
            expectEditAndDeleteBtnNotToBeRendered(VISA_TABLE);
            expect(true).toEqual(true);
          });

          it('should render the indos section with no edit/delete button if edit seafarerDocument role is missing', async () => {
            await waitFor(() => screen.getByText(/INDIAN DATABASE OF SEAFARERS NUMBER/));
            expectEditAndDeleteBtnNotToBeRendered(INDOS_TABLE);
            expect(true).toEqual(true);
          });

          it('should render the passport section with no edit button if edit seafarerDocument role is missing', async () => {
            await waitFor(() => screen.getByText(/PASSPORT/));
            expectEditAndDeleteBtnNotToBeRendered(PASSPORT_TABLE);
            expect(true).toEqual(true);
          });

          it("should render the Seaman's book section with no edit button if edit seafarerDocument role is missing", async () => {
            await waitFor(() => screen.getByText(/SEAMAN'S BOOK/));
            expectEditAndDeleteBtnNotToBeRendered(SEAMAN_BOOK_TABLE);
            expect(true).toEqual(true);
          });
        });
      });

      describe('Endorsement and verification tab', () => {
        beforeAll(() => {
          seafarerService.getSeafarerDocumentsList = jest
            .fn()
            .mockImplementation(() =>
              Promise.resolve(mockResponse.documentListResponses.verificationAndEndorsementTab),
            );
        });

        describe('when edit seafarerDocument role is assigned', () => {
          beforeEach(async () => {
            await renderView(PATH_ENDORSMENT, {
              seafarer: {
                view: {
                  general: true,
                },
                edit: {
                  seafarerDocument: true,
                },
                screening: {},
                user: {
                  manage: true,
                },
              },
            });
          });

          it('should render the endorsement section with both edit/delete button if edit seafarerDocument role is assigned', async () => {
            await waitFor(() => screen.getByText('ENDORSEMENT'));
            expectEditAndDeleteBtnToBeRendered(ENDORSEMENT_TABLE);
            expect(true).toEqual(true);
          });

          it('should render the dce verification section with both edit/delete button if edit seafarerDocument role is assigned', async () => {
            await waitFor(() => screen.getByText(/DCE VERIFICATION/));
            expectEditAndDeleteBtnToBeRendered(DCE_TABLE);
            expect(true).toEqual(true);
          });

          it('should render the document verification section with both edit/delete button if edit seafarerDocument role is assigned', async () => {
            await waitFor(() => screen.getByText(/DOCUMENT VERIFICATION/));
            expectEditAndDeleteBtnToBeRendered(DOC_VER_TABLE);
            expect(true).toEqual(true);
          });
        });

        describe('when edit seafarerDocument role is missing', () => {
          const roleConfig = {
            seafarer: {
              view: {
                general: true,
              },
              edit: {
                seafarerDocument: false,
              },
              screening: {},
              user: {
                manage: true,
              },
            },
          };

          beforeEach(async () => {
            await renderView(PATH_ENDORSMENT, roleConfig);
          });
          it('should render the endrosement section with no edit/delete button if edit seafarerDocument role is missing', async () => {
            await waitFor(() => screen.getByText('ENDORSEMENT'));
            expectEditAndDeleteBtnNotToBeRendered(ENDORSEMENT_TABLE);
            expect(true).toEqual(true);
          });

          it('should render the dce verification section with no edit/delete button if edit seafarerDocument role is missing', async () => {
            await waitFor(() => screen.getByText(/DCE VERIFICATION/));
            expectEditAndDeleteBtnNotToBeRendered(DCE_TABLE);
            expect(true).toEqual(true);
          });

          it('should render the document verification section with no edit/delete button if edit seafarerDocument role is missing', async () => {
            await waitFor(() => screen.getByText(/DOCUMENT VERIFICATION/));
            expectEditAndDeleteBtnNotToBeRendered(DOC_VER_TABLE);
            expect(true).toEqual(true);
          });
        });
      });

      describe('Other Documents tab', () => {
        beforeAll(() => {
          seafarerService.getSeafarerDocumentsList = jest
            .fn()
            .mockImplementation(() =>
              Promise.resolve(mockResponse.documentListResponses.otherDocumentsTab),
            );
        });

        describe('when edit seafarerDocument role is assigned', () => {
          beforeEach(async () => {
            await renderView(PATH_OTHER_DOC, {
              seafarer: {
                view: {
                  general: true,
                },
                edit: {
                  seafarerDocument: true,
                },
                screening: {},
                user: {
                  manage: true,
                },
              },
            });
          });

          it('should render the medical section with both edit/delete button if edit seafarerDocument role is assigned', () => {
            expectEditAndDeleteBtnToBeRendered('medical-table');
            expect(true).toEqual(true);
          });

          it('should render the certificate of competency section with both edit/delete button if edit seafarerDocument role is assigned', () => {
            expectEditAndDeleteBtnToBeRendered('certificate-of-competency-table');
            expect(true).toEqual(true);
          });

          it('should render the stcw section with both edit/delete button if edit seafarerDocument role is assigned', () => {
            expectEditAndDeleteBtnToBeRendered('stcw-table');
            expect(true).toEqual(true);
          });

          it('should render the education section with both edit/delete button if edit seafarerDocument role is assigned', () => {
            expectEditAndDeleteBtnToBeRendered('education-table');
            expect(true).toEqual(true);
          });

          it('should render the apprenticeship section with both edit/delete button if edit seafarerDocument role is assigned', () => {
            expectEditAndDeleteBtnToBeRendered('apprenticeship-table');
            expect(true).toEqual(true);
          });

          it('should render the training section with both edit/delete button if edit seafarerDocument role is assigned', () => {
            expectEditAndDeleteBtnToBeRendered('training-table');
            expect(true).toEqual(true);
          });

          it('should render the pre-sea training section with both edit/delete button if edit seafarerDocument role is assigned', () => {
            expectEditAndDeleteBtnToBeRendered('pre-sea-training-table');
            expect(true).toEqual(true);
          });

          it('should render the other courses section with both edit/delete button if edit seafarerDocument role is assigned', () => {
            expectEditAndDeleteBtnToBeRendered('other-courses-table');
            expect(true).toEqual(true);
          });

          it('should render the correspondence details section with both edit/delete button if edit seafarerDocument role is assigned', () => {
            expectEditAndDeleteBtnToBeRendered('correspondence-details-table');
            expect(true).toEqual(true);
          });

          it('should render the other documents section with both edit/delete button if edit seafarerDocument role is assigned', () => {
            expectEditAndDeleteBtnToBeRendered('other-documents-table');
            expect(true).toEqual(true);
          });
        });

        describe('when edit seafarerDocument role is missing', () => {
          beforeEach(async () => {
            await renderView(PATH_OTHER_DOC, {
              seafarer: {
                view: {
                  general: true,
                },
                edit: {
                  seafarerDocument: false,
                },
                screening: {},
                user: {
                  manage: true,
                },
              },
            });
          });

          it('should render the medical section with no edit/delete button if edit seafarerDocument role is missing', () => {
            expectEditAndDeleteBtnNotToBeRendered('medical-table');
            expect(true).toEqual(true);
          });

          it('should render the certificate of competency section with no edit/delete button if edit seafarerDocument role is missing', () => {
            expectEditAndDeleteBtnNotToBeRendered('certificate-of-competency-table');
            expect(true).toEqual(true);
          });

          it('should render the stcw section with no edit/delete button if edit seafarerDocument role is missing', () => {
            expectEditAndDeleteBtnNotToBeRendered('stcw-table');
            expect(true).toEqual(true);
          });

          it('should render the education section with no edit/delete button if edit seafarerDocument role is missing', () => {
            expectEditAndDeleteBtnNotToBeRendered('education-table');
            expect(true).toEqual(true);
          });

          it('should render the apprenticeship section with no edit/delete button if edit seafarerDocument role is missing', () => {
            expectEditAndDeleteBtnNotToBeRendered('apprenticeship-table');
            expect(true).toEqual(true);
          });

          it('should render the pre-sea training section with no edit/delete button if edit seafarerDocument role is missing', () => {
            expectEditAndDeleteBtnNotToBeRendered('pre-sea-training-table');
            expect(true).toEqual(true);
          });

          it('should render the other courses section with no edit/delete button if edit seafarerDocument role is missing', () => {
            expectEditAndDeleteBtnNotToBeRendered('other-courses-table');
            expect(true).toEqual(true);
          });

          it('should render the correspondence details section with no edit/delete button if edit seafarerDocument role is missing', () => {
            expectEditAndDeleteBtnNotToBeRendered('correspondence-details-table');
            expect(true).toEqual(true);
          });

          it('should render the other documents section with no edit/delete button if edit seafarerDocument role is missing', () => {
            expectEditAndDeleteBtnNotToBeRendered('other-documents-table');
            expect(true).toEqual(true);
          });
        });
      });
    });

    describe('Check delete buttons clicks in different document tabs', () => {
      const clickDeleteButton = (tableDataTestId: string) => {
        const table = screen.getByTestId(tableDataTestId);
        expect(table).toBeInTheDocument();

        const deleteVisaBtn = within(table).getAllByTestId(DELETE_DOC_BTN);

        act(() => {
          fireEvent.click(deleteVisaBtn[0]);
        });
      };

      const expectConfirmDeleteModalToBeOpened = () => {
        const cancelButton = screen.getByRole('button', { name: 'Cancel' });
        const confirmButton = screen.getByRole('button', { name: 'Confirm' });
        expect(cancelButton).toBeInTheDocument();
        expect(confirmButton).toBeInTheDocument();

        const deletePopUpTitle = screen.getByText('Confirm Deleting the document?');
        expect(deletePopUpTitle).toBeInTheDocument();
      };

      describe('ID Document tab', () => {
        beforeAll(() => {
          seafarerService.getSeafarerDocumentsList = jest
            .fn()
            .mockImplementation(() =>
              Promise.resolve(mockResponse.documentListResponses.idDocumentTab),
            );
        });

        describe('when edit seafarerDocument role is assigned, should render the delete button', () => {
          beforeEach(async () => {
            await renderView(PATH_ID_DOCUMENT, {
              seafarer: {
                view: {
                  general: true,
                },
                edit: {
                  seafarerDocument: true,
                },
                screening: {},
                user: {
                  manage: true,
                },
              },
            });
          });

          it('should open the delete confirmation pop up modal when click on delete button in the visa table section', async () => {
            await waitFor(() => screen.getByText(/VISA/));
            clickDeleteButton(VISA_TABLE);
            expectConfirmDeleteModalToBeOpened();
            expect(true).toEqual(true);
          });

          it('should open the delete confirmation pop up modal when click on delete button in the indos table section', async () => {
            await waitFor(() => screen.getByText(/INDIAN DATABASE OF SEAFARERS NUMBER/));
            clickDeleteButton(INDOS_TABLE);
            expectConfirmDeleteModalToBeOpened();
            expect(true).toEqual(true);
          });
        });
      });

      describe('Endorsement and verification tab', () => {
        beforeAll(() => {
          seafarerService.getSeafarerDocumentsList = jest
            .fn()
            .mockImplementation(() =>
              Promise.resolve(mockResponse.documentListResponses.verificationAndEndorsementTab),
            );
        });

        describe('when edit seafarerDocument role is assigned, should render the delete button', () => {
          beforeEach(async () => {
            await renderView(PATH_ENDORSMENT, {
              seafarer: {
                view: {
                  general: true,
                },
                edit: {
                  seafarerDocument: true,
                },
                screening: {},
                user: {
                  manage: true,
                },
              },
            });
          });

          it('should open the delete confirmation pop up modal when click on delete button in the endorsement table section', async () => {
            clickDeleteButton(ENDORSEMENT_TABLE);
            expectConfirmDeleteModalToBeOpened();
            expect(true).toEqual(true);
          });

          it('should open the delete confirmation pop up modal when click on delete button in the dce vertification table section', async () => {
            clickDeleteButton(DCE_TABLE);
            expectConfirmDeleteModalToBeOpened();
            expect(true).toEqual(true);
          });

          it('should open the delete confirmation pop up modal when click on delete button in the document vertification table section', async () => {
            clickDeleteButton(DOC_VER_TABLE);
            expectConfirmDeleteModalToBeOpened();
            expect(true).toEqual(true);
          });
        });
      });

      describe('Other Documents tab', () => {
        beforeAll(() => {
          seafarerService.getSeafarerDocumentsList = jest
            .fn()
            .mockImplementation(() =>
              Promise.resolve(mockResponse.documentListResponses.otherDocumentsTab),
            );
        });

        describe('when edit seafarerDocument role is assigned, should render the delete button', () => {
          beforeEach(async () => {
            await renderView(PATH_OTHER_DOC, {
              seafarer: {
                view: {
                  general: true,
                },
                edit: {
                  seafarerDocument: true,
                },
                screening: {},
                user: {
                  manage: true,
                },
              },
            });
          });

          it('should open the delete confirmation pop up modal when click on delete button in the medical table section', async () => {
            clickDeleteButton('medical-table');
            expectConfirmDeleteModalToBeOpened();
            expect(true).toEqual(true);
          });

          it('should open the delete confirmation pop up modal when click on delete button in the certificate of competency table section', async () => {
            clickDeleteButton('certificate-of-competency-table');
            expectConfirmDeleteModalToBeOpened();
            expect(true).toEqual(true);
          });

          it('should open the delete confirmation pop up modal when click on delete button in the stcw table section', async () => {
            clickDeleteButton('stcw-table');
            expectConfirmDeleteModalToBeOpened();
            expect(true).toEqual(true);
          });

          it('should open the delete confirmation pop up modal when click on delete button in the education table section', async () => {
            clickDeleteButton('education-table');
            expectConfirmDeleteModalToBeOpened();
            expect(true).toEqual(true);
          });

          it('should open the delete confirmation pop up modal when click on delete button in the apprenticeship table section', async () => {
            clickDeleteButton('apprenticeship-table');
            expectConfirmDeleteModalToBeOpened();
            expect(true).toEqual(true);
          });

          it('should open the delete confirmation pop up modal when click on delete button in the training table section', async () => {
            clickDeleteButton('training-table');
            expectConfirmDeleteModalToBeOpened();
            expect(true).toEqual(true);
          });

          it('should open the delete confirmation pop up modal when click on delete button in the pre-sea training table section', async () => {
            clickDeleteButton('pre-sea-training-table');
            expectConfirmDeleteModalToBeOpened();
            expect(true).toEqual(true);
          });

          it('should open the delete confirmation pop up modal when click on delete button in the other courses table section', async () => {
            clickDeleteButton('other-courses-table');
            expectConfirmDeleteModalToBeOpened();
            expect(true).toEqual(true);
          });

          it('should open the delete confirmation pop up modal when click on delete button in the correspondence details table section', async () => {
            clickDeleteButton('correspondence-details-table');
            expectConfirmDeleteModalToBeOpened();
            expect(true).toEqual(true);
          });

          it('should open the delete confirmation pop up modal when click on delete button in the other documents table section', async () => {
            clickDeleteButton('other-documents-table');
            expectConfirmDeleteModalToBeOpened();
            expect(true).toEqual(true);
          });
        });
      });

      describe('Check edit buttons clicks in different document tabs', () => {
        const clickEditButton = async (renderedView, tableDataTestId: string) => {
          await waitFor(() => {
            const table = renderedView.getByTestId(tableDataTestId);
            expect(table).toBeInTheDocument();
            const editBtns = within(table).getAllByTestId(EDIT_DOC_BTN);
            expect(editBtns).toBeInTheDocument();
            act(() => {
              fireEvent.click(editBtns[0]);
            });
          });
        };

        const expectEditModalToBeOpened = async (renderedView) => {
          await waitFor(() => {
            renderedView.findByText('Add/Edit Document');
            const documentEditModal = renderedView.getByTestId('add-document-modal');
            expect(documentEditModal).toBeInTheDocument();

            const cancelButton = renderedView.getByRole('button', { name: 'Cancel' });
            const confirmButton = renderedView.getByRole('button', { name: 'Confirm' });
            expect(cancelButton).toBeInTheDocument();
            expect(confirmButton).toBeInTheDocument();
          });
        };

        const expectFormDropDownToHaveFormId = async (renderedView, expectedFormId) => {
          await waitFor(() => {
            const docTypeDropdown = renderedView.getByTestId('doc-type-dropdown') as HTMLInputElement;
            expect(docTypeDropdown.value).toEqual(expectedFormId);
            expect(docTypeDropdown).toBeDisabled();
          });
        };

        describe('ID Document tab', () => {
          beforeAll(() => {
            seafarerService.getSeafarerDocumentsList = jest
              .fn()
              .mockImplementation(() =>
                Promise.resolve(mockResponse.documentListResponses.idDocumentTab),
              );
          });

          describe('when edit seafarerDocument role is assigned, should render the edit button', () => {
            const roleConfig = {
              seafarer: {
                view: {
                  general: true,
                },
                edit: {
                  seafarerDocument: true,
                },
                screening: {},
                user: {
                  manage: true,
                },
              },
            };

            it('should open the edit modal when click on the edit button in the visa table section', async () => {
              const localHistory = createMemoryHistory();
              localHistory.push(PATH_ID_DOCUMENT);

              const renderedView = renderViewFunc(localHistory, roleConfig);

              waitFor(() => {
                screen.getByText(/VISA/);

                clickEditButton(renderedView, VISA_TABLE);

                expectEditModalToBeOpened(renderedView);
                expectFormDropDownToHaveFormId(renderedView, documentTypes.DOC_FORM_IDS.VISA);
                expect(localHistory.location.pathname).toBe(
                  '/seafarer/details/1234/id-documents/visa/edit/22912',
                );
              });
            });

            it('should open the edit modal when click on the edit button in the indos table section', async () => {
              const localHistory = createMemoryHistory();
              localHistory.push(PATH_ID_DOCUMENT);

              const renderedView = renderViewFunc(localHistory, roleConfig);
              waitFor(() => {
                screen.getByText(/INDIAN DATABASE OF SEAFARERS NUMBER/);
                clickEditButton(renderedView, INDOS_TABLE);
                expectEditModalToBeOpened(renderedView);
                expectFormDropDownToHaveFormId(renderedView, documentTypes.DOC_FORM_IDS.INDOS);
                expect(localHistory.location.pathname).toBe(
                  '/seafarer/details/1234/id-documents/indos/edit/24270',
                );
              });
            });
          });
        });

        describe('Endorsement and verification tab', () => {
          beforeAll(() => {
            seafarerService.getSeafarerDocumentsList = jest
              .fn()
              .mockImplementation(() =>
                Promise.resolve(mockResponse.documentListResponses.verificationAndEndorsementTab),
              );
          });

          describe('when edit seafarerDocument role is assigned, should render the edit button', () => {
            const roleConfig = {
              seafarer: {
                view: {
                  general: true,
                },
                edit: {
                  seafarerDocument: true,
                },
                screening: {},
                user: {
                  manage: true,
                },
              },
            };

            it('should open the edit modal when click on the edit button on endorsement document in the endorsement table section', async () => {
              const localHistory = createMemoryHistory();
              localHistory.push(PATH_ENDORSMENT);
              const renderedView = renderViewFunc(localHistory, roleConfig);

              waitFor(() => {
                clickEditButton(renderedView, ENDORSEMENT_TABLE);
                expectEditModalToBeOpened(renderedView);
                expectFormDropDownToHaveFormId(renderedView, documentTypes.DOC_FORM_IDS.ENDORSEMENT);

                expect(localHistory.location.pathname).toBe(
                  '/seafarer/details/1234/endorsement/endorsement/edit/24158',
                );
              });
            });

            it('should open the edit modal when click on the edit button on dce verification document in the document DCE verification table section', async () => {
              const localHistory = createMemoryHistory();
              localHistory.push(PATH_ENDORSMENT);
              const renderedView = renderViewFunc(localHistory, roleConfig);

              waitFor(() => {
                clickEditButton(renderedView, DCE_TABLE);
                expectEditModalToBeOpened(renderedView);
                expectFormDropDownToHaveFormId(
                  renderView,
                  documentTypes.DOC_FORM_IDS.DCE_VERIFICAITON,
                );

                expect(localHistory.location.pathname).toBe(
                  '/seafarer/details/1234/endorsement/dce_verification/edit/24204',
                );
              });
            });

            it('should open the edit modal when click on the edit button on document verification document in the document verification table section', async () => {
              const localHistory = createMemoryHistory();
              localHistory.push(PATH_ENDORSMENT);
              const renderedView = renderViewFunc(localHistory, roleConfig);

              waitFor(() => {
                clickEditButton(renderedViewasFragment, DOC_VER_TABLE);
                expectEditModalToBeOpened(renderedView);
                expectFormDropDownToHaveFormId(renderedView, documentTypes.DOC_FORM_IDS.VERIFICATION);
                expect(localHistory.location.pathname).toBe(
                  '/seafarer/details/1234/endorsement/verification/edit/24203',
                );
              });
            });
          });
        });

        describe('Other Documents tab', () => {
          beforeAll(() => {
            seafarerService.getSeafarerDocumentsList = jest
              .fn()
              .mockImplementation(() =>
                Promise.resolve(mockResponse.documentListResponses.otherDocumentsTab),
              );
          });

          describe('when edit seafarerDocument role is assigned, should render the edit button', () => {
            const roleConfig = {
              seafarer: {
                view: {
                  general: true,
                },
                edit: {
                  seafarerDocument: true,
                },
                screening: {},
                user: {
                  manage: true,
                },
              },
            };

            it('should open the edit modal when click on the edit button in the medical table section', async () => {
              const localHistory = createMemoryHistory();
              localHistory.push(PATH_OTHER_DOC);

              const renderedView = renderViewFunc(localHistory, roleConfig);

              waitFor(() => {
                clickEditButton(renderedView, 'medical-table');
                expectEditModalToBeOpened(renderedView);
                expectFormDropDownToHaveFormId(renderedView, documentTypes.DOC_FORM_IDS.MEDICAL);
                expect(localHistory.location.pathname).toBe(
                  '/seafarer/details/37025/other-documents/medical/edit/24449',
                );
              });
            });

            it('should open the edit modal when click on the edit button in the certificate of competency table section', async () => {
              const localHistory = createMemoryHistory();
              localHistory.push(PATH_OTHER_DOC);

              const renderedView = renderViewFunc(localHistory, roleConfig);

              waitFor(() => {
                clickEditButton(renderedView, 'certificate-of-competency-table');
                expectEditModalToBeOpened(renderedView);
                expectFormDropDownToHaveFormId(
                  renderView,
                  documentTypes.DOC_FORM_IDS.CERTIFICATE_OF_COMPETENCY,
                );
                expect(localHistory.location.pathname).toBe(
                  '/seafarer/details/37025/other-documents/certificate_of_competency/edit/24461',
                );
              });
            });

            it('should open the edit modal when click on the edit button in the stcw table section', async () => {
              const localHistory = createMemoryHistory();
              localHistory.push(PATH_OTHER_DOC);

              const renderedView = renderViewFunc(localHistory, roleConfig);

              waitFor(() => {
                clickEditButton(renderedView, 'stcw-table');
                expectEditModalToBeOpened(renderedView);
                expectFormDropDownToHaveFormId(renderedView, documentTypes.DOC_FORM_IDS.STCW);
                expect(localHistory.location.pathname).toBe(
                  '/seafarer/details/37025/other-documents/stcw/edit/24545',
                );
              });
            });

            it('should open the edit modal when click on the edit button in the education table section', async () => {
              const localHistory = createMemoryHistory();
              localHistory.push(PATH_OTHER_DOC);

              const renderedView = renderViewFunc(localHistory, roleConfig);

              waitFor(() => {
                clickEditButton(renderedView, 'education-table');
                expectEditModalToBeOpened(renderedView);
                expectFormDropDownToHaveFormId(renderedView, documentTypes.DOC_FORM_IDS.EDUCATION);
                expect(localHistory.location.pathname).toBe(
                  '/seafarer/details/37025/other-documents/education/edit/24477',
                );
              });
            });

            it('should open the edit modal when click on the edit button in the apprenticeship table section', async () => {
              const localHistory = createMemoryHistory();
              localHistory.push(PATH_OTHER_DOC);

              const renderedView = renderViewFunc(localHistory, roleConfig);

              waitFor(() => {
                clickEditButton(renderedView, 'apprenticeship-table');
                expectEditModalToBeOpened(renderedView);
                expectFormDropDownToHaveFormId(
                  renderView,
                  documentTypes.DOC_FORM_IDS.APPRENTICESHIP,
                );
                expect(localHistory.location.pathname).toBe(
                  '/seafarer/details/37025/other-documents/apprenticeship/edit/24479',
                );
              });
            });

            it('should open the edit modal when click on the edit button in the training table section', async () => {
              const localHistory = createMemoryHistory();
              localHistory.push(PATH_OTHER_DOC);

              const renderedView = renderViewFunc(localHistory, roleConfig);

              waitFor(() => {
                clickEditButton(renderedView, 'training-table');
                expectEditModalToBeOpened(renderedView);
                expectFormDropDownToHaveFormId(renderedView, documentTypes.DOC_FORM_IDS.TRAINING);
                expect(renderedView.find('Router').prop('history').location.pathname).toBe(
                  '/seafarer/details/37025/other-documents/training/edit/24480',
                );
                expect(localHistory.location.pathname).toBe(
                  '/seafarer/details/37025/other-documents/training/edit/24480',
                );
              });
            });

            it('should open the edit modal when click on the edit button in the pre-sea training table section', async () => {
              const localHistory = createMemoryHistory();
              localHistory.push(PATH_OTHER_DOC);

              const renderedView = renderViewFunc(localHistory, roleConfig);

              waitFor(() => {
                clickEditButton(renderedView, 'pre-sea-training-table');
                expectEditModalToBeOpened(renderedView);
                expectFormDropDownToHaveFormId(
                  renderView,
                  documentTypes.DOC_FORM_IDS.PRE_SEA_TRAINING,
                );
                expect(localHistory.location.pathname).toBe(
                  '/seafarer/details/37025/other-documents/pre_sea_training/edit/24481',
                );
              });
            });

            it('should open the edit modal when click on the edit button in the other courses table section', async () => {
              const localHistory = createMemoryHistory();
              localHistory.push(PATH_OTHER_DOC);

              const renderedView = renderViewFunc(localHistory, roleConfig);

              waitFor(() => {
                clickEditButton(renderedView, 'other-courses-table');
                expectEditModalToBeOpened(renderedView);
                expectFormDropDownToHaveFormId(renderedView, documentTypes.DOC_FORM_IDS.OTHER_COURSE);
                expect(localHistory.location.pathname).toBe(
                  '/seafarer/details/37025/other-documents/other_course/edit/24482',
                );
              });
            });

            it('should open the edit modal when click on the edit button in the correspondence details table section', async () => {
              const localHistory = createMemoryHistory();
              localHistory.push(PATH_OTHER_DOC);

              const renderedView = renderViewFunc(localHistory, roleConfig);

              waitFor(() => {
                clickEditButton(renderedView, 'correspondence-details-table');
                expectEditModalToBeOpened(renderedView);
                expectFormDropDownToHaveFormId(
                  renderView,
                  documentTypes.DOC_FORM_IDS.CORRESPONDENCE_DETAILS,
                );
                expect(localHistory.location.pathname).toBe(
                  '/seafarer/details/37025/other-documents/correspondence_details/edit/24534',
                );
              });
            });

            it('should open the edit modal when click on the edit button in the other documents table section', async () => {
              const localHistory = createMemoryHistory();
              localHistory.push(PATH_OTHER_DOC);

              const renderedView = renderViewFunc(localHistory, roleConfig);

              waitFor(() => {
                clickEditButton(renderedView, 'other-documents-table');
                expectEditModalToBeOpened(renderedView);
                expectFormDropDownToHaveFormId(
                  renderView,
                  documentTypes.DOC_FORM_IDS.OTHER_DOCUMENT,
                );
                expect(localHistory.location.pathname).toBe(
                  '/seafarer/details/37025/other-documents/other_document/edit/24462',
                );
              });
            });
          });
        });
      });
    });

    describe('Check edit buttons clicks in different document tabs', () => {
      beforeAll(() => {
        seafarerService.getSeafarerDocumentsList = jest
          .fn()
          .mockImplementation(() =>
            Promise.resolve(mockResponse.documentListResponses.idDocumentTab),
          );
      });

      const roleConfig = {
        seafarer: {
          view: {
            general: true,
          },
          edit: {
            seafarerDocument: true,
          },
          screening: {},
          user: {
            manage: true,
          },
        },
      };

      it('should render the id document pages, click on edit button of a visa record should open the edit modal of visa form', async () => {
        const localHistory = createMemoryHistory();
        localHistory.push(PATH_ID_DOCUMENT);

        const renderedView = renderViewFunc(localHistory, roleConfig);

        waitFor(() => {
          const visaTable = renderedView.getByTestId(VISA_TABLE);
          const editVisaBtns = within(visaTable).queryAllByTestId(EDIT_DOC_BTN);
          const firstEditVisaBtn = editVisaBtns[0];
          fireEvent.click(firstEditVisaBtn);

          expect(localHistory.location.pathname).toBe(
            '/seafarer/details/1234/id-documents/visa/edit/22912',
          );

          renderedView.findByText('Add/Edit Document');
          const documentEditModal = renderedView.getByTestId('add-document-modal');
          expect(documentEditModal).toBeInTheDocument();

          const docTypeDropdown = renderedView.getByTestId('doc-type-dropdown') as HTMLInputElement;
          expect(docTypeDropdown.value).toEqual(documentTypes.DOC_FORM_IDS.VISA);
          expect(docTypeDropdown).toBeDisabled();
        });
      });
    });

    describe('Render form edit route', () => {
      const roleConfig = {
        seafarer: {
          view: {
            general: true,
          },
          edit: {
            seafarerDocument: true,
          },
          screening: {},
          user: {
            manage: true,
          },
        },
      };

      it('should render the visa form in the modal for edit', async () => {
        const localHistory = createMemoryHistory();
        localHistory.push('/seafarer/details/1234/id-documents/visa/edit/2234');

        const renderedView = renderViewFunc(localHistory, roleConfig);

        waitFor(() => {
          renderedView.findByText('Add/Edit Document');
          const documentEditModal = renderedView.getByTestId('add-document-modal');
          expect(documentEditModal).toBeInTheDocument();

          const docTypeDropdown = renderedView.getByTestId('doc-type-dropdown') as HTMLInputElement;
          expect(docTypeDropdown.value).toEqual(documentTypes.DOC_FORM_IDS.VISA);
          expect(docTypeDropdown).toBeDisabled();
        });
      });
    });
  });
});
