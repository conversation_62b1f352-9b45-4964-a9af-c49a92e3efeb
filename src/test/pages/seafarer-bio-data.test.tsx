import React from 'react';
import { MemoryRouter, Route } from 'react-router';
import { render, screen, waitFor } from '@testing-library/react';
import SeafarerBioData from '../../pages/seafarer-bio-data/seafarer-bio-data';
import seafarerService from '../../service/seafarer-service';
import { seafarerBiodataMockResponse } from '../resources/seafarer-bio-data';
import ImageController from '../../controller/image-upload-controller';
import '@testing-library/jest-dom';
import { AccessProvider } from '@src/component/common/Access';

jest.mock('../../styleGuide');
jest.mock('../../service/user-service');

const roleConfig = {
  seafarer: {
    edit: {
      recommendation: true,
      recommendationApproval: true,
      recommendationApprovalExceptTopRanks: true,
      recommendationDeviationApproval: true,
      recommendationDeviationApprovalExceptTopRanks: true,
    },
    screening: {
      view: true,
    },
  },
};

const renderBiodataPage = () => {
  render(
    <MemoryRouter initialEntries={['/seafarer/1/bio-data']}>
      <Route path="/seafarer/:seafarerId/bio-data">
        <AccessProvider config={roleConfig}>
          <SeafarerBioData />
        </AccessProvider>
      </Route>
    </MemoryRouter>,
  );
};

describe('Testing Seafarer Biodata component', () => {
  const mockImageController = () => {
    ImageController.prototype.downloadSeafarerImage = jest.fn().mockImplementation(() => {
      return Promise.resolve({});
    });

    ImageController.prototype.arrayBufferToBase64 = jest.fn().mockImplementation(() => {
      return '445fggg4332';
    });
  };
  beforeAll(async () => {
    jest.setTimeout(100000);
    mockImageController();

    seafarerService.getSeafarerBioDataById = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: seafarerBiodataMockResponse }));
  });
  afterEach(() => {
    jest.clearAllMocks();
  });
  it('should component render', async () => {
    renderBiodataPage();
    await waitFor(() => {
      const seafarerBio = screen.getByText('BIO-DATA');
      expect(seafarerBio).toBeInTheDocument();
    });
  });
});
