import React from 'react';
import '@testing-library/jest-dom';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { createMemoryHistory } from 'history';
import { Router } from 'react-router-dom';
import ButtonsToolbar from '@src/component/Details/ButtonsToolBar';
import { truncate } from 'fs';

describe('ButtonsToolbar Component', () => {
  const defaultProps = {
    seafarer: {
      seafarer_rank: { value: 'Master' },
      seafarer_person: { current_journey_status: 'SIGNED_ON' },
    },
    isEditPreJoiningEnabled: true,
    roleConfig: {
      seafarer: {
        user: { manage: true },
        edit: {
          wages: true,
          recommendation: true,
          seafarerDocument: true,
          status: true,
          bankAccount: true,
          duplicateHKID: true,
        },
        screening: { view: true },
        view: { bankAccount: true },
        editSeafarer: true,
      },
    },
    visitUpdateVessel: jest.fn(),
    handleScreeningHistoryButton: jest.fn(),
    isEnableGenerateAppointmentLetter: true,
    handleGenerateAppointmentLetterButton: jest.fn(),
    handleMarkDuplicateButton: jest.fn(),
    handleParis2UserAccountButton: jest.fn(),
    disableChangeStatusButton: false,
    visitUpdatePrejoining: jest.fn(),
    setModalPopUp: jest.fn(),
    setShowDocumentModal: jest.fn(),
    handleUpdateWagesButton: jest.fn(),
    eventTracker: jest.fn(),
    activeTab: 'general',
    recommendClickAction: jest.fn(),
    printBioClickAction: jest.fn(),
    enableRecommendationButton: true,
    visitBankAccountEditForm: jest.fn(),
  };

  const renderComponent = (props = {}) => {
    const history = createMemoryHistory();
    render(
      <Router history={history}>
        <ButtonsToolbar {...defaultProps} {...props} />
      </Router>
    );
  };

  it('should render recommendation and change status buttons when edit permissions are enabled', () => {
    renderComponent();
    expect(screen.getByTestId('recommendation-button')).toBeInTheDocument();
    expect(screen.getByTestId('fml-change-status')).toBeInTheDocument();
  });

  it('should disable Change Status button based on disableChangeStatusButton prop', () => {
    renderComponent({ disableChangeStatusButton: true });
    const changeStatusButton = screen.getByTestId('fml-change-status');
    expect(changeStatusButton).toBeDisabled();
  });

  it('should call setModalPopUp when Change Status button is clicked', () => {
    renderComponent();
    fireEvent.click(screen.getByTestId('fml-change-status'));
    expect(defaultProps.setModalPopUp).toHaveBeenCalledWith(true);
  });

  it('should render Add Document button when edit document permission is enabled', () => {
    renderComponent();
    expect(screen.getByTestId('fml-add-document')).toBeInTheDocument();
  });

  it('should open Document modal when Add Document button is clicked', () => {
    renderComponent();
    fireEvent.click(screen.getByTestId('fml-add-document'));
    expect(defaultProps.setShowDocumentModal).toHaveBeenCalledWith(true);
  });

  it('should render Edit button based on activeTab being general or experience', () => {
    renderComponent({ activeTab: 'general', roleConfig: { seafarer: { screening: { view: { backAccount: true } } , editSeafarer: true , edit: { recommendation: true }, view: { hasAccount: true }} } });
    
    const editButton = screen.getByTestId('fml-edit');
    expect(editButton).toBeInTheDocument();
  });
  
  it('should render Edit Bank Account button when activeTab is account-details', () => {
    renderComponent({ activeTab: 'account-details' });
    expect(screen.getByTestId('fml-edit-bank-account')).toBeInTheDocument();
  });

  it('should show tooltip when Edit Bank Account button is disabled', async () => {
    renderComponent({
      activeTab: 'account-details',
      roleConfig: { ...defaultProps.roleConfig, seafarer: { edit: { bankAccount: false }, screening: { view: true }, view: { bankAccount: truncate } } },
    });
    fireEvent.mouseOver(screen.getByTestId('fml-edit-bank-account'));
    await waitFor(() => {
      expect(screen.getByText('Your role is not authorised to perform this action')).toBeInTheDocument();
    });
  });

  it('should call handleScreeningHistoryButton when Screening History option is clicked', () => {
    renderComponent();
    fireEvent.click(screen.getByText('Actions'));
    fireEvent.click(screen.getByTestId('fml-screening-history'));
    expect(defaultProps.handleScreeningHistoryButton).toHaveBeenCalled();
  });

  it('should call recommendClickAction when Recommend button is clicked', () => {
    renderComponent();
    fireEvent.click(screen.getByText('Actions'));
    fireEvent.click(screen.getByTestId('recommendation-button'));
    expect(defaultProps.recommendClickAction).toHaveBeenCalled();
  });

  it('should call printBioClickAction when Print Bio option is clicked', () => {
    renderComponent();
    fireEvent.click(screen.getByText('Actions'));
    fireEvent.click(screen.getByText('Print Bio'));
    expect(defaultProps.printBioClickAction).toHaveBeenCalled();
  });

  it('should disable Recommend button based on enableRecommendationButton prop', () => {
    renderComponent({ enableRecommendationButton: false });
    fireEvent.click(screen.getByText('Actions'));
    const recommendButton = screen.getByTestId('recommendation-button').querySelector('button');
    expect(recommendButton).toBeDisabled();
  });

  it('should call handleParis2UserAccountButton when PARIS 2.0 User Account option is clicked', () => {
    renderComponent();
    fireEvent.click(screen.getByText('Actions'));
    fireEvent.click(screen.getByTestId('fml-paris2.0-user-account'));
    expect(defaultProps.handleParis2UserAccountButton).toHaveBeenCalled();
  });

  it('should disable Generate Appt. Letter when isEnableGenerateAppointmentLetter is false', async () => {
    renderComponent({ isEnableGenerateAppointmentLetter: false });
    fireEvent.click(screen.getByText('Actions'));
    await waitFor(() => {
      const generateApptLetter = screen.getByTestId('fml-add-appointment-letter');
      expect(generateApptLetter).toHaveClass('disabled');
    });
  });
  
});
