import React from 'react';
import '@testing-library/jest-dom';
import { act, fireEvent, screen, waitFor } from '@testing-library/react';
import { createMemoryHistory } from 'history';
import { experienceRankHistoryMockData, experienceMockData } from '../screening-mocks';
import { getMockedSeafarerResponse } from '../resources/seafarer-mock-data';
import seafarerService from '../../service/seafarer-service';
import seafarerSurveryService from '../../service/seafarer-survery-service';
import * as referenceService from '../../service/reference-service';
import Details from '../../pages/Details';
import dropdowndata from '../resources/drop-down-data.json';
import * as mockResponse from '../resources/document-response';
import * as mockRecommendationCheckData from '../resources/recommended-checks-data';
import { getSeafarerStatusHistoryByPersonIDMockResponse } from '../resources/seafarer-status-history';
import { renderWithRoute } from '../util/test-utils';

jest.mock('../../styleGuide');
jest.mock('../../service/user-service');
jest.mock('../../service/travel-service');
jest.mock('../../service/vessel-service');
jest.mock('../../service/seafarer-service');

// As we don't need to test 'AddExperience' Modal. Use dummy component to mock it.
jest.mock('../../component/seafarerExperience/AddExperience', () => {
  return function DummyComponent(props) {
    return <div data-testid="dummy-add-seafarer-experience-modal">dummy</div>;
  };
});

describe('<SeafarerExperienceSummary />', () => {
  beforeAll(async () => {
    jest.setTimeout(*********);
    seafarerService.getSeafarerStatusHistoryByPersonID = jest
      .fn()
      .mockImplementation(() =>
        Promise.resolve({ data: getSeafarerStatusHistoryByPersonIDMockResponse() }),
      );
    seafarerService.getSeafarer = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: getMockedSeafarerResponse() }));
    seafarerService.getRecommendedChecks = jest.fn().mockImplementation((input) =>
      Promise.resolve({
        data: mockRecommendationCheckData.recommendedChecksData(),
      }),
    );
    seafarerService.getChildHKID = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: [] }));
    seafarerService.getParentSeafarerDetails = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: { results: [{ id: 1 }] } }));
    seafarerService.getSeafarerExperience = jest.fn().mockImplementation(() =>
      Promise.resolve({
        data: experienceMockData(),
      }),
    );
    seafarerService.getExperienceRankHistory = jest.fn().mockImplementation(() =>
      Promise.resolve({
        data: experienceRankHistoryMockData(),
      }),
    );
    seafarerService.getSeafarerDropDownData = jest.fn().mockImplementation(() => dropdowndata);
    seafarerService.getSeafarerDocumentDropdown = jest.fn().mockImplementation(() => dropdowndata);
    seafarerService.getSeafarerReportingOfficeDropDownData = jest

      .fn()

      .mockImplementation(() => [dropdowndata.offices]);
    seafarerSurveryService.getSuptAppraisalList = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: { results: [] } }));
    jest
      .spyOn(referenceService, 'getVisaRegionDropDownData')
      .mockImplementation(() => Promise.resolve(mockResponse.visaRegionReferenceApiResponseData));
  });

  const roleConfigData = {
    seafarer: {
      screening: {
        view: true,
      },
      create: {},
      edit: {
        duplicateHKID: false,
        seafarerExperience: true,
      },
      view: {
        general: true,
        bankAccount: true,
        hkid: true,
      },
      hidden: {
        contactDetails: false,
      },
    },
  };

  const renderExperiencePage = async (roleConfig = roleConfigData) => {
    const history = createMemoryHistory();
    history.push('/seafarer/details/1/experience');
    await renderWithRoute(
      history,
      <Details />,
      '/seafarer/details/:seafarerId/:step',
      roleConfig
    );
  };
  describe('page default view', () => {
    describe('Render Rank History Table', () => {
      it('should render all table header', async () => {
        await renderExperiencePage();
        await waitFor(() => screen.getByText(/Vessel Experience/));
        const tableHeaders = screen.getAllByRole('columnheader');
        const headerColumns = ['Date of Promotion', 'Rank', 'By'];
        const headerTexts = tableHeaders.map((header) => header.textContent);
        expect(headerTexts).toEqual(expect.arrayContaining(headerColumns));
      });

      it('should render the rank history value column data', async () => {
        await renderExperiencePage();
        await waitFor(() => screen.getByText(/OILER/));
        const rankValueColumns = screen.getAllByRole('cell');
        const rankValueColumnData = ['MM', 'OILER'];
        const columnDataTexts = rankValueColumns.map((column) => column.textContent);
        expect(columnDataTexts).toEqual(expect.arrayContaining(rankValueColumnData));
      });
    });
    describe('Render Vessel Exprience Table', () => {
      it('should render all table header', async () => {
        await renderExperiencePage();
        await waitFor(() => screen.getByText(/Vessel Experience/));
        const tableHeaders = screen.getAllByRole('columnheader', { hidden: true });
        const headerColumns = [
          'Vessel Name',
          'Type',
          'Rank',
          'Start Date',
          'End Date',
          'Period',
          'Actions',
        ];
        const headerTexts = tableHeaders.map((header) => header.textContent);
        expect(headerTexts).toEqual(expect.arrayContaining(headerColumns));
      });

      it('should render the vessel exprience value column data', async () => {
        await renderExperiencePage();
        await waitFor(() => screen.getByText(/Vessel Experience/));
        const vesselExperienceColumns = screen.getAllByRole('cell');
        const vesselExperienceColumnData = [
          'Spar Eight',
          'Bulk Carrier',
          'MM',
          '05 Nov 1998',
          '15 Sep 1999',
          '10 Months 11 Days',
        ];
        const columnDataTexts = vesselExperienceColumns.map((column) => column.textContent);
        expect(columnDataTexts).toEqual(expect.arrayContaining(vesselExperienceColumnData));
      });
      /** this test is skipped already, not refactoring now
      it.skip('click on Delete Exprience button, should open model popup', async () => {
        const deleteExprienceButton = wrapper.find(`[data-testid='delete-doc-exp-btn']`);
        deleteExprienceButton.at(0).simulate('click');
        await updateWrapper(wrapper);
        const deleteModal = wrapper.find('.modal-content');
        expect(deleteModal).toHaveLength(1);
      });
       */
      it('should click on vessel exprience edit button, should open model popup', async () => {
        await renderExperiencePage();
        await waitFor(() => screen.getByText(/Vessel Experience/));
        const editExpBtn = screen.getByTestId('edit-0-doc-exp-btn');
        await act(async () => {
          fireEvent.click(editExpBtn);
        });
        const vesselModal = screen.getByTestId('dummy-add-seafarer-experience-modal');
        expect(vesselModal).toBeInTheDocument();
      });
    });

    describe('Add Seafarer Exprience button', () => {
      it('should check Add Exprience button', async () => {
        await renderExperiencePage();
        await waitFor(() => screen.getByText(/Vessel Experience/));
        const addExperienceButton = screen.getByRole('button', { name: 'Add Experience' });
        expect(addExperienceButton).toBeInTheDocument();
      });
      it('click on Add Exprience button, should open model popup', async () => {
        await renderExperiencePage();
        await waitFor(() => screen.getByText(/Vessel Experience/));
        const addExperienceButton = screen.getByRole('button', { name: 'Add Experience' });
        await act(async () => {
          fireEvent.click(addExperienceButton);
        });
        await waitFor(() => {
          const vesselModal = screen.getByTestId('dummy-add-seafarer-experience-modal');
          expect(vesselModal).toBeInTheDocument();
        });
      });
    });
  });

  const roleConfigDataWithoutExperience = {
    seafarer: {
      screening: {
        view: true,
      },
      create: {},
      edit: {
        duplicateHKID: false,
        seafarerExperience: false,
      },
      view: {
        general: true,
        bankAccount: true,
        hkid: true,
      },
      hidden: {
        contactDetails: false,
      },
    },
  };
  describe('Experience Page without experience permission', () => {
    it('should not shouw Add Experience button', async () => {
      await renderExperiencePage(roleConfigDataWithoutExperience);
      await waitFor(() => screen.getByText(/Vessel Experience/));
      const addExperienceButton = screen.queryByRole('button', { name: 'Add Experience' });
      expect(addExperienceButton).not.toBeInTheDocument();
    });
  });
});
