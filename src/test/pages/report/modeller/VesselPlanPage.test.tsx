import React from 'react';
import { render, waitFor } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import VesselPlanPage from '../../../../pages/report/modeller/VesselPlanPage';
import { mockedVesselPlanTableData } from '../../../resources/vessel-plan-table';
import seafarerReportService from '../../../../service/seafarer-report-service';
import '@testing-library/jest-dom';
import { AccessProvider } from '@src/component/common/Access';

jest.mock('../../../../styleGuide');
jest.mock('../../../../service/user-service');
jest.mock('../../../../service/travel-service');

const renderWithRoute = (
  ui,
  { route = '/seafarer-reports/modeller/1637' } = {},
  roleConfig: object = {},
) => {
  window.history.pushState({}, 'Seafarer reports', route);
  return render(<AccessProvider config={roleConfig}>{ui}</AccessProvider>, {
    wrapper: MemoryRouter,
  });
};

describe('<VesselPlanPage />', () => {
  let container;

  beforeAll(() => {
    seafarerReportService.getVesselPlanByOwnershipId = jest
      .fn()
      .mockResolvedValue({ data: mockedVesselPlanTableData });
    seafarerReportService.getModellerReports = jest
      .fn()
      .mockResolvedValue({ data: { results: [] } });
  });

  describe('page default view', () => {
    beforeAll(async () => {
      container = renderWithRoute(
        <VesselPlanPage />,
        {},
        {
          seafarer: {
            view: {
              general: true,
              reportModeller: true,
            },
          },
        },
      );
      await waitFor(() => expect(container).toBeDefined());
    });

    describe('Render vessel plan page', () => {
      it('should render all table headers', async () => {
        const headers = await waitFor(() => container.getAllByRole('columnheader'));
        const headerColumns = [
          'No.',
          'Rank',
          'Planned Number',
          'Actual Number',
          'Planned Nationality',
          'Actual Nationality',
          'Planned Wages (USD)',
          'Actual Wages (USD)',
        ];
        expect(headers.map((header) => header.textContent)).toEqual(
          expect.arrayContaining(headerColumns),
        );
      });

      it('If any rows is repeated then for rank, planned number,actual number and planned Nationality show empty fields for second record', async () => {
        const renderedContainer = renderWithRoute(
          <VesselPlanPage />,
          {},
          {
            seafarer: {
              view: {
                general: true,
                reportModeller: true,
              },
            },
          },
        );
        const rows = await waitFor(() => renderedContainer.getAllByRole('row'));
        const allRows = Array.from(rows).map((row) =>
          Array.from(row.querySelectorAll('.td')).map((item) => item.textContent),
        );
        const expectedRow1 = [
          '1',
          'MASTER',
          '20 ',
          '22 ',
          'Indian ',
          'Indian ',
          '13500 ',
          '12200 ',
        ];
        const expectedRow2 = ['2', ' ', '  ', '  ', 'Indian ', 'Indian ', '13500 ', '12200 '];
        const expectedRow3 = [
          '3',
          'CHIEF OFFICER',
          '1 ',
          '1 ',
          'Indian ',
          'Indian ',
          '10250 ',
          '9300 ',
        ];
        expect(allRows[1]).toEqual(expectedRow1);
        expect(allRows[2]).toEqual(expectedRow2);
        expect(allRows[3]).toEqual(expectedRow3);
      });

      it('should show 403 page when sf|rt|md|v role is absent', async () => {
        const { getByText } = renderWithRoute(
          <VesselPlanPage />,
          {},
          {
            seafarer: {
              view: {
                general: true,
              },
            },
          },
        );
        await waitFor(() => expect(getByText('403')).toBeInTheDocument());
      });
    });
  });
});
