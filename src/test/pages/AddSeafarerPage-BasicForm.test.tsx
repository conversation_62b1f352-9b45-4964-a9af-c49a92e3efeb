import React from 'react';
import { MemoryRouter, Route } from 'react-router-dom';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import AddSeafarePage from '../../pages/AddSeafarerPage';
import { mockLoadSeafarer } from '../../controller/add-seafarer-controller';
import { AccessProvider } from '@src/component/common/Access';

jest.mock('../../styleGuide');
jest.mock('../../service/user-service');
jest.mock('../../controller/add-seafarer-controller');

const mountAddSeafarerPage = () => (
  <MemoryRouter initialEntries={['/seafarer/add/basic']}>
    <AccessProvider config={{
      seafarer: {
        addSeafarer: true,
        editSeafarer: false,
        edit: {
          personalDetails: true,
          passport: true,
          seamansBook: true,
        },
        create: {},
        hidden: {
          bankAccount: true,
        },
      },
    }}>
      <Route path="/seafarer/:seafarerId?/add/:step?">
        <AddSeafarePage />
      </Route>
    </AccessProvider>
  </MemoryRouter>
);

describe('AddSeafarerPage Handling Forms', () => {
  beforeEach(() => {
    jest.setTimeout(20000);
    mockLoadSeafarer.mockClear();
  });

  it('Should not display errors on first load', async () => {
    render(mountAddSeafarerPage());

    await waitFor(() => {
      const formError = screen.queryByTestId('form-error-list');
      expect(formError).not.toBeInTheDocument();
    });
  });

  it('Should display errors going to other tab when basic form is incomplete', async () => {
    render(mountAddSeafarerPage());

    await waitFor(() => {
      const basicTab = screen.getByTestId('basic-tab');
      expect(basicTab).toBeInTheDocument();

      const errorComponent = screen.getByTestId('contact-details-tab');
      fireEvent.click(errorComponent);
    });

    await waitFor(() => {
      const basicTab = screen.getByTestId('form-error-list');
      expect(basicTab).toBeInTheDocument();
    });
  });

  it('Should display errors when clicking Save button when basic form is incomplete', async () => {
    render(mountAddSeafarerPage());

    await waitFor(() => {
      const saveButton = screen.getByTestId('form-seafarer-save-button');
      fireEvent.click(saveButton);
    });

    await waitFor(() => {
      const basicTab = screen.getByTestId('form-error-list');
      expect(basicTab).toBeInTheDocument();
    });
  });
});
