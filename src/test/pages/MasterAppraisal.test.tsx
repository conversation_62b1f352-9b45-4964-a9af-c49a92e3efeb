import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { MemoryRouter, Route } from 'react-router-dom';
import MasterAppraisal from '../../pages/MasterAppraisal';
import * as seafarerService from '../../service/seafarer-service';
import AddSeafarerController from '../../controller/add-seafarer-controller';

jest.mock('../../service/seafarer-service');
jest.mock('../../controller/add-seafarer-controller');
jest.mock('../../styleGuide');
const mockSeafarerData = {
  seafarer_person: { first_name: '<PERSON>', last_name: '<PERSON><PERSON>' },
  seafarer_rank: { unit: 'MASTER' },
  hkid: 1231,
};

const mockDropDownData = {
  ranks: [
    { id: 1, value: 'MASTER' },
    { id: 2, value: 'Chief Engineer' },
  ],
};

const mockMasterAppraisalData = {
  primaryLeft: [{ label: 'Label 1', value: 'Value 1' }],
  primaryRight: [{ label: 'Label 2', value: 'Value 2' }],
  secondaryLeft: [{ label: 'Label 3', value: 'Value 3' }],
  secondaryRight: [{ label: 'Label 4', value: 'Value 4' }],
  gradingTable: [{ label: 'Grade', value: 'A+' }],
  master_appraisal_result: [
    {
      master_appraisal_question: {
        value: 'test',
      },
      result: 10,
    },
  ],
};

describe('MasterAppraisal Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    seafarerService.getSeafarer.mockResolvedValue({ data: mockSeafarerData });
    seafarerService.getMasterAppraisal = jest.fn().mockResolvedValue({
      data: mockMasterAppraisalData,
    });
    AddSeafarerController.prototype.loadDropDownData = jest
      .fn()
      .mockResolvedValue(mockDropDownData);
  });

  const renderMasterAppraisal = () => {
    render(
      <MemoryRouter initialEntries={['/seafarer/123/appraisals/456']}>
        <Route path="/seafarer/:seafarerId/appraisals/:mstrAppraisalId">
          <MasterAppraisal />
        </Route>
      </MemoryRouter>,
    );
  };

  it('should render the loading spinner initially', async () => {
    renderMasterAppraisal();
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('should render the MasterAppraisal component with data', async () => {
    renderMasterAppraisal();

    await waitFor(() => {
      expect(screen.getAllByText('John Doe (MASTER) (1231)')).toHaveLength(2);
    });
  });

  it('should handle errors gracefully when API calls fail', async () => {
    seafarerService.getSeafarer.mockRejectedValue(new Error('API Error'));
    renderMasterAppraisal();

    await waitFor(() => {
      expect(screen.getByText('Loading...')).toBeInTheDocument();
    });
  });
});
