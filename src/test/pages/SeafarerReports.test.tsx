import React from 'react';
import { render, cleanup, screen, within } from '@testing-library/react';
import '@testing-library/jest-dom';
import { act } from 'react-dom/test-utils';
import { MemoryRouter, Route } from 'react-router-dom';
import SeafarerReports from '../../pages/SeafarerReports';
import seafarerService from '../../service/seafarer-service';
import vesselService from '../../service/vessel-service';
import seafarerReportService from '../../service/seafarer-report-service';
import dropdowndata from '../resources/drop-down-data.json';
import {
  getTechGroupDropDownResponse,
  queryVesselOwnershipResponse,
} from '../resources/getMockedDropDownData';
import { modellingMockData } from '../resources/modelling-table';
import { AccessProvider } from '@src/component/common/Access';

jest.mock('../../styleGuide');
jest.mock('../../service/user-service');
jest.mock('../../service/travel-service');
jest.mock('../../service/seafarer-service');

const role = {
  seafarer: {
    view: {
      general: true,
      reportModeller: true,
    },
  },
};

const renderSeafarerReportPage = (tab = 'modeller', queryparam = '', roleConfig = role) => (
  <MemoryRouter initialEntries={[`/seafarer-reports/${tab}?${queryparam}`]}>
    <Route exact path={'/seafarer-reports/:tab'}>
      <AccessProvider config={roleConfig}>
        <SeafarerReports />
      </AccessProvider>
    </Route>
  </MemoryRouter>
);

describe('Seafarer Report Modeller', () => {
  beforeAll(async () => {
    jest.setTimeout(*********);
  });
  afterEach(cleanup);
  afterEach(() => {
    jest.clearAllMocks();
  });
  beforeEach(() => {
    seafarerService.getTechGroupDropDown = jest
      .fn()
      .mockImplementation(() => Promise.resolve(getTechGroupDropDownResponse()));
    vesselService.queryVesselOwnership = jest
      .fn()
      .mockImplementation(() => Promise.resolve(queryVesselOwnershipResponse()));
    seafarerService.getSeafarerReportingOfficeDropDownData = jest
      .fn()
      .mockImplementation(() => dropdowndata.offices);
    seafarerReportService.getModellerReports = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: modellingMockData }));
  });

  it('Should Load default filters', async () => {
    await act(async () => {
      render(renderSeafarerReportPage());
    });
    const advanceSearchMenu = await screen.findByTestId('advanced-search-menu');
    expect(await within(advanceSearchMenu).findByText('All Vessels')).toBeInTheDocument();
  });
  it('Should load multiple filters from URL params', async () => {
    const queryparam =
      'vessel_name=Sagami&vessel_tech_group=Celsius%20Tech&planned_number=20%2C30&actual_wages=40%2C60';
    await act(async () => {
      render(renderSeafarerReportPage('modeller', queryparam));
    });
    const advanceSearchMenu = await screen.findByTestId('advanced-search-menu');
    expect(await within(advanceSearchMenu).findByText('Sagami')).toBeInTheDocument();
    expect(await within(advanceSearchMenu).findByText('Celsius Tech')).toBeInTheDocument();
    expect(await within(advanceSearchMenu).findByDisplayValue('20')).toBeInTheDocument();
    expect(await within(advanceSearchMenu).findByDisplayValue('30')).toBeInTheDocument();
    expect(await within(advanceSearchMenu).findByDisplayValue('40')).toBeInTheDocument();
    expect(await within(advanceSearchMenu).findByDisplayValue('60')).toBeInTheDocument();
  });
  it('Should overwrite the default filters with filters from URL params', async () => {
    await act(async () => {
      render(
        renderSeafarerReportPage('modeller', 'vessel_name=Sagami&vessel_tech_group=Celsius%20Tech'),
      );
    });
    const advanceSearchMenu = await screen.findByTestId('advanced-search-menu');
    expect(await within(advanceSearchMenu).findByText('Sagami')).toBeInTheDocument();
    expect(await within(advanceSearchMenu).findByText('Celsius Tech')).toBeInTheDocument();
  });
  it.skip('Should call getApi with right query param', async () => {
    const urlQueryParam =
      'vessel_name=Sagami&vessel_tech_group=Celsius%20Tech&planned_number=20%2C30&actual_wages=40%2C60';
    await act(async () => {
      render(renderSeafarerReportPage('modeller', urlQueryParam));
    });
    const querySpy = jest.spyOn(seafarerReportService, 'getModellerReports');
    const advanceSearchMenu = await screen.findByTestId('advanced-search-menu');
    expect(await within(advanceSearchMenu).findByText('Celsius Tech')).toBeInTheDocument();
    const apiQueryparam = `&limit=10&offset=0&orderBy=vessel_name asc&${urlQueryParam}`;
    expect(querySpy).toHaveBeenLastCalledWith(apiQueryparam);
  });
  it('should show 403 page when sf|rt|md|v role is absent', async () => {
    await act(async () => {
      render(renderSeafarerReportPage('modeller', '', {}));
    });
    const textElement = screen.getByText('403');
    expect(textElement).toBeInTheDocument();
  });
});
