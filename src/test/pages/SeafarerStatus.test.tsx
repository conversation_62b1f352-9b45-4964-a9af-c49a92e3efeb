import React from 'react';
import { screen, waitFor } from '@testing-library/react';
import { createMemoryHistory } from 'history';
import seafarerService from '../../service/seafarer-service';

import Details from '../../pages/Details';

import { mockScreeningDataCall } from '../screening-mocks';
import { renderWithRoute } from '../util/test-utils';
import '@testing-library/jest-dom';
import { getMockedSeafarerResponse } from '../resources/seafarer-mock-data';

jest.mock('../../styleGuide');
jest.mock('../../service/user-service');
jest.mock('../../service/travel-service');
jest.mock('../../service/vessel-service');
jest.mock('../../service/seafarer-service');
jest.mock('../../service/screening-service');
jest.mock('@src/service/reference-service');
jest.mock('../../service/seafarer-survery-service');

describe('Seafarer status change', () => {
  beforeAll(async () => {
    seafarerService.getSeafarer = jest
      .fn()
      .mockImplementation(() => Promise.resolve({ data: getMockedSeafarerResponse() }));
    seafarerService.getSeafarerDropDownData = jest.fn().mockImplementation(() =>
      Promise.resolve({
        countries: [],
        nationalities: [],
        offices: [],
        ranks: [],
      }),
    );
  });

  const renderSeafarerStatus = async () => {
    const history = createMemoryHistory();
    history.push('/seafarer/details/1');
    await renderWithRoute(history, <Details />, '/seafarer/details/:seafarerId', {
      seafarer: {
        editSeafarer: true,
        screening: {
          view: true,
        },
        create: {},
        edit: {
          duplicateHKID: true,
        },
        view: {
          general: true,
          bankAccount: true,
          hkid: true,
        },
        hidden: {
          contactDetails: false,
        },
      },
    });
  };

  describe('Change Status Button', () => {
    it('should have Change Status button in button toolbar', async () => {
      await renderSeafarerStatus();
      await waitFor(() => {
        const changeStatusBtn = screen.getByRole('button', { name: 'Change Status' }); // Get the "Change Status" button
        expect(changeStatusBtn).toBeInTheDocument();
      });
    });

    it('should disable Change Status button in button toolbar when seafarer rejected', async () => {
      await renderSeafarerStatus();
      mockScreeningDataCall('rejected', 'some remarks');

      await waitFor(() => {
        const changeStatusBtn = screen.getByRole('button', { name: 'Change Status' }); // Get the "Change Status" button
        expect(changeStatusBtn).toBeDisabled();
      });
    });
  });
});
