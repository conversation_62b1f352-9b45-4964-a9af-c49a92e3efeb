import React from 'react';
import '@testing-library/jest-dom';
import { act, fireEvent, screen, waitFor, within } from '@testing-library/react';
import { createMemoryHistory } from 'history';
import { renderWithRoute } from '../util/test-utils';
import * as seafarerSurveyService from '../../service/seafarer-survery-service';
import seafarerService from '../../service/seafarer-service';
import AddSeafarerController from '../../controller/add-seafarer-controller';
import AppraisalsPage from '../../pages/AppraisalsPage';
import AppraisalListData from '../resources/appraisal_get_query.json';
import { masterAppraisalQueryApiResponse } from '../resources/master-appraisal-mockdata';
import debriefingQueryApiResponse from '../resources/debriefing_query.json';
import dropdowndata from '../resources/drop-down-data.json';
import { trainingRequirementsApiResponse } from '../resources/training-requirements-mockdata';
import { getMockedChildSeafarer } from '../resources/seafarer-mock-data';

jest.mock('../../styleGuide');
jest.mock('../../service/user-service');
jest.mock('../../service/travel-service');
jest.mock('../../service/seafarer-service');
jest.mock('../../service/vessel-service');
jest.mock('../../service/reference-service');

const addSeafarerController = new AddSeafarerController();
const seafarer = getMockedChildSeafarer();
describe('Appraisal Page', () => {
  beforeAll(() => {
    jest.setTimeout(100000);

    jest
      .spyOn(addSeafarerController, 'loadDropDownData')
      .mockImplementation(() => Promise.resolve(dropdowndata));

    jest
      .spyOn(seafarerSurveyService, 'getSuptAppraisalList')
      .mockImplementation(() => Promise.resolve({ status: 200, data: AppraisalListData }));

    jest
      .spyOn(seafarerSurveyService, 'getDebriefingList')
      .mockImplementation(() => Promise.resolve({ status: 200, data: debriefingQueryApiResponse }));

    jest.spyOn(seafarerService, 'getMasterAppraisalList').mockImplementation(() =>
      Promise.resolve({
        status: 200,
        data: masterAppraisalQueryApiResponse,
      }),
    );

    jest.spyOn(seafarerService, 'getTrainingReqListBySeafarerId').mockImplementation(() =>
      Promise.resolve({
        status: 200,
        data: trainingRequirementsApiResponse,
      }),
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  const history = createMemoryHistory();

  const renderAppraisalPage = async (roleConfig) => {
    history.push('/seafarer/details/10828/appraisals');
    await renderWithRoute(
      history,
      <AppraisalsPage seafarer={seafarer} />,
      '/seafarer/details/:seafarerId/appraisals',
      roleConfig,
    );
  };

  it('Should not render supt appraisal section when user does not have view role', async () => {
    const roleConfig = {
      seafarer: {
        view: {
          viewSuptAppraisal: false,
          viewMasterAppraisal: true,
        },
      },
    };
    await renderAppraisalPage(roleConfig);

    expect(screen.queryByTestId('superintendent-appraisals-table')).toBeFalsy();
    expect(screen.getByTestId('master-appraisals-table')).toBeInTheDocument();
  });

  it('Should not render training requirement section when user does not have view role', async () => {
    const roleConfig = {
      seafarer: {
        view: {
          viewTraining: false,
        },
      },
    };
    await renderAppraisalPage(roleConfig);
    const trainingRequirementsSection = screen.queryByTestId('training-requirements-table');
    expect(trainingRequirementsSection).not.toBeInTheDocument();
  });

  it('Should render the appraisal page with a 403 error page when user does not have any of the view role', async () => {
    const roleConfig = {
      seafarer: {
        view: {
          viewSuptAppraisal: false,
          viewMasterAppraisal: false,
          viewDebriefing: false,
          viewTraining: false,
        },
      },
    };
    await renderAppraisalPage(roleConfig);
    expect(screen.queryByTestId('superintendent-appraisals-table')).toBeFalsy();
    expect(screen.queryByTestId('master-appraisals-table')).toBeFalsy();
    expect(screen.queryByTestId('debriefing-appraisals-table')).toBeFalsy();
    expect(screen.queryByTestId('training-requirements-table')).toBeFalsy();
    expect(screen.queryByTestId('investigation-training-requirements-table')).toBeFalsy();
    expect(screen.getByText('403')).toBeInTheDocument();
  });

  it('Should render supt appraisal, master appraisal and debriefing section when user have view role', async () => {
    const roleConfig = {
      seafarer: {
        view: {
          viewSuptAppraisal: true,
          viewMasterAppraisal: true,
          viewDebriefing: true,
          viewTraining: true,
        },
      },
    };
    await renderAppraisalPage(roleConfig);
    expect(
      screen.getAllByTestId('superintendent-appraisals-table', { exact: true }).length,
    ).toEqual(1);
    expect(screen.getAllByTestId('master-appraisals-table').length).toEqual(1);
    expect(screen.getAllByTestId('debriefing-appraisals-table').length).toEqual(1);
    expect(screen.getAllByTestId('training-requirements-table').length).toEqual(1);
    expect(screen.getAllByTestId('investigation-training-requirements-table').length).toEqual(1);
  });

  describe('Render Training Requirement Table', () => {
    const roleConfig = {
      seafarer: {
        view: {
          viewTraining: true,
        },
      },
    };

    afterEach(() => {
      jest.clearAllMocks();
    });

    it('Should render training requirement section when user has view role', async () => {
      await renderAppraisalPage(roleConfig);
      const trainingRequirementsSection = screen.getByTestId('training-requirements-table');
      expect(trainingRequirementsSection).toBeInTheDocument();
    });

    it('should render all table header', async () => {
      await renderAppraisalPage(roleConfig);
      const trainingRequirementsSection = screen.getByTestId('training-requirements-table');
      const headers = within(trainingRequirementsSection).getAllByRole('columnheader');
      const headerColumns = [
        'Date Recommended',
        'Vessel',
        'Deadline',
        'Date Completed',
        'Created by',
        'Training Needs',
        'Training Imparted',
        'Supporting Document',
        'Supt. Report',
      ];
      expect(headers.map((link) => link.textContent)).toEqual(
        expect.arrayContaining(headerColumns),
      );
    });

    it('should show proper error message for empty data', async () => {
      jest
        .spyOn(seafarerService, 'getTrainingReqListBySeafarerId')
        .mockResolvedValueOnce({ data: [] });
      await renderAppraisalPage(roleConfig);
      await waitFor(() => {
        const trainingRequirementsSection = screen.getByTestId('training-requirements-table');
        const noResult = within(trainingRequirementsSection).getByText('No Records');
        expect(noResult).toBeInTheDocument();
      });
    });
  });

  describe('Add Training Requirement Button', () => {
    it('Should not show Add button when user does not have add training role', async () => {
      const roleConfig = {
        seafarer: {
          view: {
            viewTraining: true,
          },
          add: {
            training: false,
          },
        },
      };
      await renderAppraisalPage(roleConfig);
      const addTrainingRequirementButton = screen.queryByTestId('fml-add-training');
      expect(addTrainingRequirementButton).not.toBeInTheDocument();
    });

    it('Should show Add button when user has add training role', async () => {
      const roleConfig = {
        seafarer: {
          view: {
            viewTraining: true,
          },
          add: {
            training: true,
          },
        },
      };
      await renderAppraisalPage(roleConfig);
      const addTrainingRequirementButton = screen.getByTestId('fml-add-training');
      expect(addTrainingRequirementButton).toBeInTheDocument();
    });

    it('Click on Add Training Requirement button, should open model popup', async () => {
      const roleConfig = {
        seafarer: {
          view: {
            viewTraining: true,
          },
          add: {
            training: true,
          },
        },
      };
      await renderAppraisalPage(roleConfig);
      await act(async () => {
        const addTrainingRequirementButton = screen.getByTestId('fml-add-training');
        fireEvent.click(addTrainingRequirementButton);
      });
      await waitFor(() => {
        const trainingModal = screen.getByText('Add Training Requirement');
        expect(trainingModal).toBeInTheDocument();
      });
    });
  });

  describe('Edit Training Requirement Button', () => {
    it('Should not show Edit button when user does not have edit training role', async () => {
      const roleConfig = {
        seafarer: {
          view: {
            viewTraining: true,
          },
          edit: {
            training: false,
            suptTraining: false,
          },
        },
      };
      await renderAppraisalPage(roleConfig);
      const editTrainingRequirementButton = screen.queryByTestId('edit-0-training-req-btn');
      expect(editTrainingRequirementButton).not.toBeInTheDocument();
    });
  });

  describe('Render Investigation Training Requirement Table', () => {
    const roleConfig = {
      seafarer: {
        view: {
          viewTraining: true,
        },
      },
    };

    afterEach(() => {
      jest.clearAllMocks();
    });

    it('Should render Investigation training requirement section when user has view role', async () => {
      await renderAppraisalPage(roleConfig);
      const trainingRequirementsSection = screen.getByTestId('training-requirements-table');
      expect(trainingRequirementsSection).toBeInTheDocument();
    });

    it('should render all table header', async () => {
      await renderAppraisalPage(roleConfig);
      const trainingRequirementsSection = screen.getByTestId(
        'investigation-training-requirements-table',
      );
      const headers = within(trainingRequirementsSection).getAllByRole('columnheader');
      const headerColumns = [
        'Date Recommended',
        'Vessel',
        'Deadline',
        'Date Completed',
        'Created by',
        'Training Needs',
        'Training Imparted',
        'Supporting Document',
        'Supt. Report',
      ];
      expect(headers.map((link) => link.textContent)).toEqual(
        expect.arrayContaining(headerColumns),
      );
    });

    it('should show proper error message for empty data', async () => {
      jest
        .spyOn(seafarerService, 'getTrainingReqListBySeafarerId')
        .mockResolvedValueOnce({ data: [] });
      await renderAppraisalPage(roleConfig);
      await waitFor(() => {
        const trainingRequirementsSection = screen.getByTestId('training-requirements-table');
        const noResult = within(trainingRequirementsSection).getByText('No Records');
        expect(noResult).toBeInTheDocument();
      });
    });
  });

  describe('Add investigation Training Requirement Button', () => {
    const roleConfigAddTraining = {
      seafarer: {
        view: {
          viewTraining: true,
        },
        add: {
          training: true,
        },
      },
    };
    it('Should not show Add button when user does not have add training role', async () => {
      const roleConfig = {
        seafarer: {
          view: {
            viewTraining: true,
          },
          add: {
            training: false,
          },
        },
      };
      await renderAppraisalPage(roleConfig);
      const addTrainingRequirementButton = screen.queryByTestId('fml-add-investigation-training');
      expect(addTrainingRequirementButton).not.toBeInTheDocument();
    });

    it('Should show Add button when user has add training role', async () => {
      await renderAppraisalPage(roleConfigAddTraining);
      const addTrainingRequirementButton = screen.getByTestId('fml-add-investigation-training');
      expect(addTrainingRequirementButton).toBeInTheDocument();
    });

    it('Click on Add Investigation Training Requirement button, should open model popup', async () => {
      await renderAppraisalPage(roleConfigAddTraining);
      await act(async () => {
        const addInvTrainingRequirementButton = screen.getByTestId(
          'fml-add-investigation-training',
        );
        fireEvent.click(addInvTrainingRequirementButton);
      });
      await waitFor(() => {
        const trainingModal = screen.getByText('Add Investigation of Training Requirement');
        expect(trainingModal).toBeInTheDocument();
      });
    });
  });
});
