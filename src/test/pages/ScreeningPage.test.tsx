import React from 'react';
import '@testing-library/jest-dom';
import { waitFor, screen, within, fireEvent } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { createMemoryHistory } from 'history';
import { renderWithRoute } from '../util/test-utils';
import ScreeningPage from '../../pages/ScreeningPage';
import * as wcoService from '../../service/wco-service';
import WCOData from '../resources/wco-case-with-matches.json';

import {
  mockSeafarerlDataCall,
  mockScreeningDataCall,
  getButtonsForApprovalStatus,
  mockScreeningDataWithForwardedCall,
  mockScreeningDataWithFPDCall,
} from '../screening-mocks';
import seafarerService from '../../service/seafarer-service';
import { getMockedSeafarerResponse } from '../resources/seafarer-mock-data';

jest.mock('../../styleGuide');
jest.mock('../../service/user-service');
jest.mock('../../service/travel-service');
jest.mock('../../service/wco-service');

const history = createMemoryHistory();
const renderScreeningPage = async (
  screeningGroup = 'Compliance Employee',
  view = true,
  forward = false,
  approve = false,
  reapply = false,
) => {
  history.push('/seafarer/details/1/screening');

  await renderWithRoute(history, <ScreeningPage />, '/seafarer/details/:seafarerId/screening', {
    screeningGroup,
    seafarer: {
      screening: {
        view,
        forward,
        approve,
        reapply,
      },
    },
  });
};

describe('<ScreeningPage />', () => {
  describe('Compliance employee view on pending', () => {
    beforeAll(async () => {
      const response = {
        results: [getMockedSeafarerResponse()],
      };
      seafarerService.getSeafarerFieldsData = jest.fn().mockResolvedValue({ data: response });
      mockScreeningDataCall();

      jest.spyOn(wcoService, 'getWcoCase').mockResolvedValue({ data: { wcoCaseData: WCOData } });
    });

    describe('<ScreeningPage />', () => {
      it('should render first approver for `Compliance Employee` with pending data', async () => {
        await renderScreeningPage('Compliance Employee', true, true, true);
        const screeningPageColumns = await waitFor(() => screen.getAllByRole('cell'));
        const columnDataTexts = screeningPageColumns.map((column) => column.textContent);
        expect(columnDataTexts.slice(0, 6)).toEqual([
          'Compliance Employee',
          '',
          '',
          '',
          'pending',
          '',
        ]);
      });
    });

    it('should not render approver name, last_update and remarks when status is pending', async () => {
      await renderScreeningPage('Compliance Employee', true, true, true);
      const screeningPageColumns = await waitFor(() => screen.getAllByRole('cell'));
      const columnDataTexts = screeningPageColumns.map((column) => column.textContent);
      expect(columnDataTexts.slice(0, 6)).toEqual([
        'Compliance Employee',
        '',
        '',
        '',
        'pending',
        '',
      ]);
    });

    it('should not have remarks link, when remarks are not available', async () => {
      await renderScreeningPage('Compliance Employee', true, true, true);
      const screeningPageColumns = await waitFor(() => screen.getAllByRole('cell'));
      const columnDataTexts = screeningPageColumns.map((column) => column.textContent);
      const remarksColumnIndex = columnDataTexts.indexOf('pending') + 1;
      const remarksColumnText = columnDataTexts[remarksColumnIndex];
      expect(remarksColumnText).toBe('');
    });

    it('should have approve, forward and reject buttons when status is pending for Compliance Employee', async () => {
      await renderScreeningPage('Compliance Employee', true, true, true);
      const allButtons = await waitFor(() => screen.getAllByRole('button'));
      expect(allButtons.map((btn) => btn.textContent)).toEqual([
        'Approve',
        'Forward to Supervisor',
        'Reject',
      ]);
    });

    it.skip('should have rework button when status is rejected', async () => {
      mockScreeningDataCall('rejected', 'some remarks');
      screeningPage = mount(
        <MemoryRouter>
          <ScreeningPage
            roleConfig={{
              screeningGroup: 'Compliance Employee',
              seafarer: {
                screening: {
                  view: true,
                },
              },
            }}
          />
        </MemoryRouter>,
      );
      await updateWrapper(screeningPage);
      const allButtons = getButtonsForApprovalStatus(screeningPage);

      expect(allButtons.map((btn) => btn.text())).toEqual(['Rework']);
    });

    it.skip('should have rework button when status is approved', async () => {
      mockScreeningDataCall('approved');
      screeningPage = mount(
        <MemoryRouter>
          <ScreeningPage
            roleConfig={{
              screeningGroup: 'Compliance Employee',
              seafarer: {
                screening: {
                  view: true,
                },
              },
            }}
          />
        </MemoryRouter>,
      );
      await updateWrapper(screeningPage);
      const allButtons = getButtonsForApprovalStatus(screeningPage);

      expect(allButtons.map((btn) => btn.text())).toEqual(['Rework']);
    });

    it('should open remarks model, on click of remark link', async () => {
      mockScreeningDataCall(
        'rejected',
        'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec sapien neque, suscipit.',
      );

      await renderScreeningPage('Compliance Employee', true, false, false);

      const rows = screen.getAllByRole('row');

      const rowWithRejectedStatus = rows.filter((row) => row.getElementsByClassName('rejected'));
      const remarksLink = within(rowWithRejectedStatus[1]).getByRole('button', {
        name: 'Lorem ipsum dolor sit amet, consectetur...',
      });
      expect(remarksLink.textContent).toBe('Lorem ipsum dolor sit amet, consectetur...');

      fireEvent.click(remarksLink);

      const remarksModal = await screen.findByRole('dialog');
      expect(remarksModal).toBeInTheDocument();
      expect(
        screen.getByText(
          'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec sapien neque, suscipit.',
        ),
      ).toBeInTheDocument();
    });

    it('should have documents view link when documents present for approval', async () => {
      mockScreeningDataCall('rejected', 'remarks');

      await renderScreeningPage('Compliance Employee', true, false, false);
      const rows = screen.getAllByRole('row');

      const rowWithRejectedStatus = rows.filter((row) => row.getElementsByClassName('rejected'));
      const remarksLink = within(rowWithRejectedStatus[1]).getByRole('button', { name: 'View' });
      expect(remarksLink.textContent).toBe('View');
    });

    it('should not have documents view link when documents are not present for approval', async () => {
      mockScreeningDataCall('rejected', 'remarks', 'Compliance Employee', false);

      await renderScreeningPage('Compliance Employee', true, false, false);

      const documentsLink = screen.queryByRole('button', { name: 'View' });
      expect(documentsLink).not.toBeInTheDocument();
    });
  });

  describe('Compliance employee view on forward', () => {
    beforeAll(async () => {
      const response = {
        results: [getMockedSeafarerResponse()],
      };
      seafarerService.getSeafarerFieldsData = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ data: response }));
      mockScreeningDataWithForwardedCall();

      jest
        .spyOn(wcoService, 'getWcoCase')
        .mockImplementation(() => Promise.resolve({ data: { wcoCaseData: WCOData } }));
    });

    it('should not have any action button after forward', async () => {
      await renderScreeningPage('Compliance Employee', true, false, false);

      const buttons = screen.queryAllByRole('button');
      expect(buttons.length).toBe(0);
    });
  });

  describe.skip('Compliance Supervisor view after forwarded by Compliance Employee', () => {
    beforeAll(async () => {
      const response = {
        results: [getMockedSeafarerResponse()],
      };
      seafarerService.getSeafarerFieldsData = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ data: response }));
      mockScreeningDataWithForwardedCall();
      screeningPage = mount(
        <MemoryRouter>
          <ScreeningPage
            roleConfig={{
              screeningGroup: 'Compliance Supervisor',
              seafarer: {
                screening: {
                  view: true,
                  approve: true,
                },
              },
            }}
          />
        </MemoryRouter>,
      );
      await updateWrapper(screeningPage);
    });
    it('should show latest non pending Compliance Employee forwarded approval data', () => {
      const columnData = screeningPage.find('tr.forwarded td').map((col) => col.text());
      expect(columnData[0]).toEqual('Compliance Employee');
      expect(columnData[2]).toEqual('test.user');
      expect(columnData[3]).toEqual('forwarded');
      expect(columnData[4]).toEqual('some remarks');
      expect(columnData[5]).toEqual('');
    });
    it('should not have pending approval row for supervisor', () => {
      expect(screeningPage.find('tr.pending').exists()).toEqual(false);
    });
    it('should have remarks link, when remarks are available', async () => {
      const rowWithRemarks = screeningPage.find('td').at(4);
      expect(rowWithRemarks.find('Remarks').exists()).toBe(true);
    });

    it('should have approve reject button', async () => {
      const allButtons = screeningPage.find('LoggedInUserActionButtons Button');
      expect(allButtons.map((btn) => btn.text())).toEqual(['Approve', 'Reject']);
    });
  });

  describe('Fleet Personnel View after screening rejected', () => {
    beforeAll(async () => {
      const response = {
        results: [getMockedSeafarerResponse()],
      };
      jest
        .spyOn(wcoService, 'getWcoCase')
        .mockImplementation(() => Promise.resolve({ data: { wcoCaseData: WCOData } }));
      seafarerService.getSeafarerFieldsData = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ data: response }));
      mockScreeningDataWithFPDCall();
    });

    it('should have rejected approval view', async () => {
      await renderScreeningPage('Fleet Personnel', true, false, false, true);

      const rows = screen.getAllByRole('row');
      const columnData = rows.filter((row) => row.getElementsByClassName('rejected'));

      expect(within(columnData[1]).getByText('Compliance Supervisor')).toBeInTheDocument();
      expect(within(columnData[1]).getByText('rejected')).toBeInTheDocument();
    });

    it('should have reapplied button', async () => {
      await renderScreeningPage('Fleet Personnel', true, false, false, true);

      const rows = screen.getAllByRole('row');
      const columnData = rows.filter((row) => row.getElementsByClassName('rejected'));

      expect(within(columnData[2]).getByText('Reapply')).toBeInTheDocument();
    });
  });

  describe('HKID Info', () => {
    it('should render HKID when seafarer have HKID genearted', async () => {
      mockSeafarerlDataCall(1000);
      mockScreeningDataCall('approved');

      await renderScreeningPage(null, true, false, false, false);

      expect(screen.getByText('The Seafarer ID (HKID) is')).toBeInTheDocument();
      expect(screen.getByText('1000')).toBeInTheDocument();

      // images not rendering : fix later
      // expect(rows.getElementsByClassName('screening-page__moved-tick')).toBeInTheDocument();
    });

    it('should not render HKID when seafarer have HKID genearted', async () => {
      const response = {
        results: [getMockedSeafarerResponse()],
      };

      jest
        .spyOn(wcoService, 'getWcoCase')
        .mockImplementation(() => Promise.resolve({ data: { wcoCaseData: WCOData } }));

      seafarerService.getSeafarerFieldsData = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ data: response }));
      mockScreeningDataCall();

      await renderScreeningPage(null, true, false, false, false);

      expect(screen.queryByText('The Seafarer ID (HKID) is')).not.toBeInTheDocument();
      expect(screen.queryByText('1000')).not.toBeInTheDocument();

      // images not rendering : fix later
      // expect(screeningPage.find('HKIdInfo .screening-page__moved-tick').exists()).toBe(false);
    });
  });

  describe('Screening History Link', () => {
    it('should have link to screening history page when user have screening history', async () => {
      const response = {
        results: [getMockedSeafarerResponse()],
      };

      jest
        .spyOn(wcoService, 'getWcoCase')
        .mockImplementation(() => Promise.resolve({ data: { wcoCaseData: WCOData } }));
      seafarerService.getSeafarerFieldsData = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ data: response }));
      mockScreeningDataCall('approved');

      await renderScreeningPage(null, true, false, false, false);

      const screeningHistoryLink = screen.getByRole('link', { name: 'Screening History' });
      expect(screeningHistoryLink.textContent).toEqual('Screening History');
      expect(screeningHistoryLink.getAttribute('href')).toEqual('/seafarer/screening-history/1');
    });

    it('should not have link to screening history page when user have no screening history', async () => {
      const response = {
        results: [getMockedSeafarerResponse()],
      };
      seafarerService.getSeafarerFieldsData = jest
        .fn()
        .mockImplementation(() => Promise.resolve({ data: response }));
      mockScreeningDataCall();

      await renderScreeningPage(null, true, false, false, false);

      const screeningHistoryLink = screen.queryByRole('link', { name: 'Screening History' });
      expect(screeningHistoryLink).toBeNull();
    });
  });

  describe('ScreeningSeafarerPage role based view', () => {
    mockSeafarerlDataCall(1000);
    mockScreeningDataCall('approved');

    it('should not render when user has no screening | view role', async () => {
      await renderScreeningPage(null, false, false, false, false);

      const textElement = screen.queryByText('403');
      expect(textElement).toBeInTheDocument();

      const textElement2 = screen.queryByText('The Seafarer ID (HKID) is');
      expect(textElement2).not.toBeInTheDocument();
    });

    it('should render when user has screening | view role', async () => {
      await renderScreeningPage(null, true, false, false, false);

      const textElement = screen.queryByText('403');
      expect(textElement).not.toBeInTheDocument();

      expect(screen.getByText('1. APPROVAL')).toBeInTheDocument();
    });
  });
});
