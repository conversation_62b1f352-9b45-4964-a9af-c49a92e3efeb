import React from 'react';
import '@testing-library/jest-dom';
import { createMemoryHistory } from 'history';
import { cleanup, waitFor, screen, fireEvent } from '@testing-library/react';
import { act } from 'react-dom/test-utils';
import CrewListPage from '../../pages/CrewListPage';
import seafarerService from '../../service/seafarer-service';
import vesselService from '../../service/vessel-service';
import CrewListMockData from '../resources/crew-list.json';
import OcimfMatrix from '../resources/ocimf-matrix.json';
import { renderWithRoute } from '../util/test-utils';

jest.mock('../../styleGuide');
jest.mock('../../service/user-service');
jest.mock('../../service/travel-service');

afterEach(cleanup);

const roleConfig = (crewListAccess, editSeafarerAccess) => {
  return {
    seafarer: {
      screening: {
        view: true,
      },
      replaceCrewList: true,
      editSeafarer: editSeafarerAccess,
      create: {},
      edit: {
        duplicateHKID: true,
        seafarerExperience: true,
        travel: true,
      },
      view: {
        general: true,
        bankAccount: true,
        hkid: true,
        crewList: crewListAccess,
      },
      hidden: {
        contactDetails: false,
      },
    },
  };
};

const date = '2022-06-10';

const history = createMemoryHistory();

const renderCrewListPage = async (vesselId, crewListAccess, editSeafarerAccess) => {
  history.push(`/seafarer/crew-list/vessel/${vesselId}/${date}`);
  await renderWithRoute(
    history,
    <CrewListPage />,
    '/seafarer/crew-list/vessel/:vesselIdParam/:date',
    roleConfig(crewListAccess, editSeafarerAccess),
  );
};

beforeAll(async () => {
  vesselService.getAllVessels = jest.fn().mockImplementation(() =>
    Promise.resolve({
      data: {
        results: [
          { id: 2225, name: '- - -' },
          { id: 2224, name: 'NITIAcerArrow' },
          { id: 2223, name: 'aaaa' },
          { id: 2222, name: 'newABCD' },
          { id: 2221, name: 'ABCD' },
          { id: 2220, name: 'ABCD' },
          { id: 2219, name: 'VESSELRARA Copy' },
          { id: 2218, name: 'sadasdasda' },
          { id: 2217, name: 'VESSELRARA Copy' },
          { id: 2216, name: 'VesselNameTest' },
          { id: 2215, name: 'vessel test' },
          { id: 2214, name: 'VESSELRARA Copy' },
          { id: 2213, name: 'VESSELRARA' },
          { id: 2212, name: 'Bitcoin Miner' },
          { id: 2211, name: 'Automated-Test-Change-Ownership-ae0d' },
          { id: 2210, name: 'New Aspire' },
          { id: 2208, name: '- - -' },
          { id: 2207, name: 'test1' },
          { id: 2206, name: 'test nova email ab' },
          { id: 2205, name: 'RErer' },
          { id: 2204, name: 'test' },
          { id: 102, name: 'ABC', vessel: { id: 103 }, vessel_type: { type: 'tanker' } },
        ],
      },
    }),
  );
  seafarerService.getCrewList = jest
    .fn()
    .mockImplementation(() => Promise.resolve({ data: CrewListMockData[0] }));
  seafarerService.getOcimfMatrix = jest
    .fn()
    .mockImplementation(() => Promise.resolve({ data: OcimfMatrix }));
});

let windowOpenSpy;

beforeEach(() => {
  windowOpenSpy = jest.spyOn(window, 'open');
});

describe('Fleet icon styling for crew member name column', () => {
  it('should show fleet icon for crew member worked with fleet in the past', async () => {
    await renderCrewListPage(103, true, true);
    await waitFor(() => screen.queryByText('An** Ku** Si**'));

    const crewMemberName = screen.getByRole('cell', { name: 'An** Ku** Si**' });

    const fleetIcon = crewMemberName.querySelector('img');
    expect(fleetIcon).toBeInTheDocument();
  });

  it('should not show fleet icon for crew member not worked with fleet in the past', async () => {
    await renderCrewListPage(103, true, true);
    await waitFor(() => screen.queryByText('Sh** ch* L**'));
    const crewMemberName = screen.getByRole('cell', { name: 'Sh** ch* L**' });

    const fleetIcon = crewMemberName.querySelector('img');
    expect(fleetIcon).not.toBeInTheDocument();
  });
});

describe('Pencil button on CrewList page', () => {
  it('should not show edit pencil when seafarer|edit|basic role is absent', async () => {
    await renderCrewListPage(103, true, false);

    expect(screen.queryByTestId('edit-0-crew-list-btn')).toBeFalsy();
  });

  it('should call route  /seafarer/:seafarerId/add/basic on clicking edit pencil', async () => {
    await renderCrewListPage(103, true, true);

    const editPencil = screen.queryByTestId('edit-0-crew-list-btn');

    act(() => {
      fireEvent.click(editPencil);
    });

    expect(history.location.pathname).toEqual('/seafarer/36560/add/basic');
  });
});

describe('Role based access for crew list', () => {
  it('should show 403 page when seafarer|view|crew-list role is absent', async () => {
    await renderCrewListPage(103, false, true);

    const textElement = screen.getByText('403');

    expect(textElement).toBeInTheDocument();
  });
  it('should not show 403 page when seafarer|view|crew-list role is present', async () => {
    await renderCrewListPage(103, true, true);

    const textElement = screen.queryByText('403');

    expect(textElement).not.toBeInTheDocument();
  });
});

describe('HKID clickable', () => {
  it('should call route  /seafarer/details/:seafarerId/general on clicking HKID', async () => {
    await renderCrewListPage(103, true, true);

    await waitFor(() => screen.queryByText('1719'));

    const hkidButton = screen.getByText('1719');

    act(() => {
      fireEvent.click(hkidButton);
    });

    expect(window.open).toHaveBeenCalledWith('/seafarer/details/36560/general', '_blank');
  });
});

describe('Main Page', () => {
  it('should display “{Vessel Name}/Crew List” on the top and  {Vessel Name} should be a link that go to the Vessel', async () => {
    await renderCrewListPage(103, true, true);

    const historyPushFunction = jest.spyOn(history, 'push');
    const vesselName = screen.getByText('ABC');

    expect(vesselName).toBeInTheDocument();

    act(() => {
      fireEvent.click(vesselName);
    });

    expect(historyPushFunction).toHaveBeenCalledWith('/vessel/ownership/details/102');
  });

  it('should render default headers of crew list table', async () => {
    await renderCrewListPage(103, true, true);

    const headers = screen.getAllByRole('columnheader');

    const expectedText = [
      'HKID',
      'Onboard Seafarer',
      'Rank',
      'Contract End Date',
      'Sign On Date',
      'Length Of Contract',
      'Repatriation Port',
      'Embarkation Port',
      'Nationality',
      'Months on Current Voyage',
      'Date of Birth',
      'Place of Birth',
      'Replacer Seafarer',
    ];

    const headerText = headers.map((header) => header.textContent);

    expect(headerText).toEqual(expect.arrayContaining(expectedText));
  });

  it('should display table column button with dropdown values when clicked', async () => {
    await renderCrewListPage(103, true, true);

    const tableColumnsButton = screen.getByText('Table Columns');
    act(() => {
      fireEvent.click(tableColumnsButton);
    });

    await screen.findByRole('button', { name: 'HKID' });

    const tableColumnButton = screen
      .getAllByRole('button')
      .filter((e) => e.className === 'dropdown-item');

    const expectedText = [
      'HKID',
      'Onboard Seafarer',
      'Rank',
      'Contract End Date',
      'Sign On Date',
      'Length Of Contract',
      'Repatriation Port',
      'Embarkation Port',
      'Nationality',
      'Months on Current Voyage',
      'Date of Birth',
      'Place of Birth',
      'Replacer Seafarer',
    ];

    const buttonText = tableColumnButton.map((button) => button.textContent);
    expect(buttonText).toEqual(expect.arrayContaining(expectedText));
  });

  it('when dropdown value from column button is clicked, corresponding column is added to the listing', async () => {
    await renderCrewListPage(103, true, true);

    const ACCOUNT_STATUS = 'Account Status';
    const expectedText = [ACCOUNT_STATUS];

    const tableColumnBeforeSelection = screen
      .getAllByRole('columnheader')
      .map((header) => header.textContent);
    expect(tableColumnBeforeSelection).toEqual(expect.not.arrayContaining(expectedText));

    const tableColumnsButton = screen.getByText('Table Columns');

    act(() => {
      fireEvent.click(tableColumnsButton);
    });

    await screen.findByText(ACCOUNT_STATUS);
    const accountStatusOption = screen.getByText(ACCOUNT_STATUS);

    act(() => {
      fireEvent.click(accountStatusOption);
    });

    const tableColumnAfterSelection = screen
      .getAllByRole('columnheader')
      .map((header) => header.textContent);
    expect(tableColumnAfterSelection).toEqual(expect.arrayContaining(expectedText));
  });
});

describe('Testing travel modal', () => {
  it('should travel modal open on click travel button', async () => {
    await renderCrewListPage(103, true, true);
    const checkbox = screen.getByTestId('checkbox');
    fireEvent.click(checkbox);
    const button = screen.getByText('Email Profile Data');
    fireEvent.click(button);
    await waitFor(() => screen.getByTestId('travel-modal'));
    expect(screen.getByTestId('travel-modal')).toBeInTheDocument();
  });

  it('should change event fired on change message content', async () => {
    await renderCrewListPage(103, true, true);
    const checkbox = screen.getByTestId('checkbox');
    fireEvent.click(checkbox);
    const button = screen.getByText('Email Profile Data');
    fireEvent.click(button);
    const messageField = screen.getByTestId('message-content');
    const message = `Dear Sir/Madam,

    Please find the following seafarer information sent from PARIS 2.0 for travel arrangement.

    Regards,
    Fleet Management Limited`;
    fireEvent.change(messageField, { target: { value: message } });
    await waitFor(() => expect(messageField).toHaveValue(message));
    expect(messageField.value).toHaveLength(154);
  });
});

describe('Testing OCIMF Matrix', () => {
  it('should have the ocimf checkbox', async () => {
    await renderCrewListPage(103, true, true);
    const checkbox = screen.getByTestId('ocimf-checkbox');
    expect(checkbox).toBeTruthy();
  });

  it('should not have the ocimf checkbox for non tankers', async () => {
    await renderCrewListPage(102, true, true);
    const checkbox = screen.queryByTestId('ocimf-checkbox');
    expect(checkbox).toBeFalsy();
  });

  it('should check the ocimf checkbox', async () => {
    await renderCrewListPage(103, true, true);
    setTimeout(async () => {
      const checkbox = screen.getByTestId('ocimf-checkbox');
      fireEvent.click(checkbox);
      const certText = screen.getByText('Certificate of Competancy');
      const endorsementsText = screen.getByText('Endorsements');
      const crewComplianceText = screen.getByText('Crew Compliance');
      const administrationAcceptText = screen.getByText('Administration Acceptance');
      await waitFor(() => {
        expect(certText).toBeTruthy();
        expect(endorsementsText).toBeTruthy();
        expect(crewComplianceText).toBeTruthy();
        expect(administrationAcceptText).toBeTruthy();
      });
    }, 3000);
  }); // NOSONAR

  it('should uncheck the ocimf checkbox', async () => {
    await renderCrewListPage(103, true, true);
    const checkbox = screen.getByTestId('ocimf-checkbox');
    fireEvent.click(checkbox);
    fireEvent.click(checkbox);
    const certText = screen.queryByText('Certificate of Competancy');
    const endorsementsText = screen.queryByText('Endorsements');
    const crewComplianceText = screen.queryByText('Crew Compliance');
    const administrationAcceptText = screen.queryByText('Administration Acceptance');
    expect(certText).toBeFalsy();
    expect(endorsementsText).toBeFalsy();
    expect(crewComplianceText).toBeFalsy();
    expect(administrationAcceptText).toBeFalsy();
  });
});
