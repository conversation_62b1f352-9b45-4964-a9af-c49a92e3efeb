import React from 'react';
import '@testing-library/jest-dom';
import { render, screen, waitFor, fireEvent, act } from '@testing-library/react';
import { MemoryRouter, Route } from 'react-router-dom';
import { getCountries, getPorts } from '@src/service/reference-service';
import SimulatePortModal from '@src/component/CrewManagement/SimulatePort/SimulatedPortModal';
import { getVesselItinerary } from '../../service/vessel-service';
import CrewManagementPage from '../../pages/CrewManagementPage';
import { CREW_MANAGEMEMT_TAB_LIST } from '../../model/TabData';
import CrewManagementHeaderSection from '../../component/CrewManagement/HeaderSection';
import { mockItineraryData, mockVesselData } from '../resources/managed-vessels';
import { AccessProvider } from '@src/component/common/Access';
// Mock the imported services
jest.mock('@src/service/reference-service');
jest.mock('../../styleGuide');
jest.mock('../../service/user-service');
jest.mock('../../service/vessel-service');
jest.mock('../../service/keycloak-service');
jest.mock('../../service/seafarer-service');
jest.mock('../../service/travel-service');
jest.mock('@src/service/crew-planner');

const roleConfig = {
  seafarer: {
    view: {
      crewPlannerSeafarer: true,
    },
  },
};
const renderCrewManagementPage = () => {
  return act(async () => {
    return render(
      <MemoryRouter initialEntries={['/seafarer/crew-planner/vessel/1']}>
        <Route path="/seafarer/crew-planner/vessel/:vesselOwnershipId">
          <AccessProvider config={roleConfig}>
            <CrewManagementPage ga4react={{}} />
          </AccessProvider>
        </Route>
      </MemoryRouter>,
    );
  });
};

describe('CrewManagementPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render the page correctly', async () => {
    await renderCrewManagementPage();
    await waitFor(() => {
      expect(screen.getByText('Crew Planner /')).toBeInTheDocument();
      expect(screen.getByText('Test Vessel')).toBeInTheDocument();
      expect(screen.getByText('/ Crew Details')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Switch Vessel')).toBeInTheDocument();
      expect(screen.getByText('Vessel Details')).toBeInTheDocument();
      CREW_MANAGEMEMT_TAB_LIST.forEach((tab) => {
        expect(screen.getByText(tab.tabName)).toBeInTheDocument();
      });
      expect(screen.getByText('Test Vessel')).toBeInTheDocument();
    });
  });

  it('should render the correct vessel data', async () => {
    render(
      <CrewManagementHeaderSection
        vesselData={mockVesselData.data.results[0]}
        itinerary={mockItineraryData.data.results[0]}
      />,
    );
    // Vessel Category
    expect(screen.getByText('Vessel Category')).toBeInTheDocument();
    expect(screen.getByText('Bulk Carrier')).toBeInTheDocument();

    // Flag Office
    expect(screen.getByText('Flag Office')).toBeInTheDocument();
    expect(screen.getByText('HK')).toBeInTheDocument();

    // Vessel Owner
    expect(screen.getByText('Vessel Owner')).toBeInTheDocument();
    expect(screen.getByText('Rich Owner')).toBeInTheDocument();

    // Tech Group
    expect(screen.getByText('Tech Group')).toBeInTheDocument();
    expect(screen.getByText('Tech T1')).toBeInTheDocument();

    // Group Head
    expect(screen.getByText('Group Head')).toBeInTheDocument();
    expect(screen.getByText('Master of Bulk Carrier')).toBeInTheDocument();

    // Estimated Port
    expect(screen.getByText('Estimated Port')).toBeInTheDocument();
    expect(screen.getByText('Habour, China')).toBeInTheDocument();
  });

  it('should handle vessel dropdown change', async () => {
    const mockHistory = {
      push: jest.fn(),
    };

    jest.mock('react-router-dom', () => ({
      ...jest.requireActual('react-router-dom'),
      useHistory: jest.fn(() => mockHistory),
    }));
    await renderCrewManagementPage();
    await act(async () => {
      const dropdownControl = screen.getByPlaceholderText('Switch Vessel');
      fireEvent.change(dropdownControl, { target: { value: 'Second Vessel' } });
      expect(dropdownControl.value).toBe('Second Vessel');
    });
  });

  it('should handle error', async () => {
    getVesselItinerary.mockRejectedValue({ response: { status: 500, message: 'Error' } });
    await renderCrewManagementPage();
    await waitFor(() => {
      const alertBox = screen.getByRole('alert');
      expect(alertBox).toBeInTheDocument();
      expect(
        screen.getByText('Error when loading Crew list, error:', { exact: false }),
      ).toBeInTheDocument();
    });
  });
});

jest.mock('@src/service/reference-service', () => ({
  getCountries: jest.fn(),
  getPorts: jest.fn(),
}));

describe('SimulatePortModal', () => {
  const mockProps = {
    onClose: jest.fn(),
    show: true,
    crewChangeDestinationData: {
      startDate: '2024-06-01',
      endDate: '2024-12-01',
    },
  };

  beforeEach(() => {
    getCountries.mockImplementation(() =>
      Promise.resolve({ countries: [{ alpha2_code: 'US', value: 'United States' }] }),
    );
    getPorts.mockImplementation(() =>
      Promise.resolve({ ports: [{ id: 1, name: 'Port of Los Angeles' }] }),
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders the component correctly', async () => {
    await act(async () => {
      render(<SimulatePortModal {...mockProps} />);
    });
    await waitFor(() => {
      expect(screen.getByText('Add a Crew Change Destination')).toBeInTheDocument();
      expect(screen.getByText('Country*')).toBeInTheDocument();
      expect(screen.getByText('Port*')).toBeInTheDocument();
      expect(screen.getByText('Estimated Date of Arrival*')).toBeInTheDocument();
    });
  });

  it('opens and closes the modal', async () => {
    await act(async () => {
      render(<SimulatePortModal {...mockProps} />);
    });
    expect(screen.getByText('Add a Crew Change Destination')).toBeInTheDocument();
    await act(async () => {
      fireEvent.click(screen.getByText('Cancel'));
    });
    await waitFor(() => {
      expect(mockProps.onClose).toHaveBeenCalled();
    });
  });

  it('shows an error message if something goes wrong', async () => {
    getCountries.mockImplementationOnce(() =>
      Promise.reject(new Error('Failed to fetch countries')),
    );
    await act(async () => {
      render(<SimulatePortModal {...mockProps} />);
    });

    await waitFor(() => {
      expect(screen.getByText('Something went wrong, please try again later')).toBeInTheDocument();
    });
  });
});
