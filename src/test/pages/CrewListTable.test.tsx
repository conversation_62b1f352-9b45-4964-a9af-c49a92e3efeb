import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import CrewListTable from '../../component/CrewList/CrewListTable';
import { getCrewListPageColumns } from '@src/component/CrewList/MenuList';

jest.mock('../../styleGuide');
jest.mock('../../service/user-service');
jest.mock('../../service/travel-service');

const mockData = [
  {
    id: 100087,
    seafarer_person_id: 100087,
    ref_id: 202332,
    rank_id: 1,
    is_only_worked_fml: false,
    seafarer_reporting_office: {
      value: 'Panstar Shipping Co., Ltd. (Can sign off crew)',
      id: 65,
    },
    hkid: 73913,
    created_at: '2021-09-06T14:06:02.212Z',
    updated_at: '2024-08-26T07:43:26.532Z',
    office_id: 65,
    manning_agent_id: 65,
    not_to_be_employed: false,
    not_to_be_employed_reason: null,
    framo_experience: false,
    ice_conditions_experience: null,
    show_cargo_experience: false,
    show_fml_experience: true,
    cargo_experience: null,
    additional_experience: null,
    parent_hkid: null,
    is_parent: null,
    data_quality: 'clean',
    with_fml_vessel_experience: true,
    'seafarer_person:seafarer_status_history:seafarer_rank.sortprior': 1,
    ocimf: [
      {
        id: 10271099,
        seafarer_id: 100087,
        group_value: 'tanker',
        group_by_name: 'tanker',
        years: '11.94',
      },
      {
        id: 10271121,
        seafarer_id: 100087,
        group_value: 'ULCC',
        group_by_name: 'vessel_type',
        years: '8.43',
      },
      {
        id: 10271111,
        seafarer_id: 100087,
        group_value: 'Chemical Tanker',
        group_by_name: 'vessel_type',
        years: '5.56',
      },
      {
        id: 10271112,
        seafarer_id: 100087,
        group_value: 'Gas and Chemical Tanker',
        group_by_name: 'vessel_type',
        years: '5.79',
      },
      {
        id: 10271098,
        seafarer_id: 100087,
        group_value: 'dry',
        group_by_name: 'dry',
        years: '0.50',
      },
      {
        id: 10271101,
        seafarer_id: 100087,
        group_value: 'General Cargo',
        group_by_name: 'vessel_type',
        years: '1.41',
      },
      {
        id: 10271115,
        seafarer_id: 100087,
        group_value: 'Gas Tanker',
        group_by_name: 'vessel_type',
        years: '0.22',
      },
      {
        id: 10271116,
        seafarer_id: 100087,
        group_value: 'LPG Carrier',
        group_by_name: 'vessel_type',
        years: '0.22',
      },
      {
        id: 10271117,
        seafarer_id: 100087,
        group_value: 'LNG',
        group_by_name: 'vessel_type',
        years: '0.22',
      },
      {
        id: 10271125,
        seafarer_id: 100087,
        group_value: 'Suez Max Tanker',
        group_by_name: 'vessel_type',
        years: '8.43',
      },
      {
        id: 10271128,
        seafarer_id: 100087,
        group_value: 'Bitumen Tanker',
        group_by_name: 'vessel_type',
        years: '8.43',
      },
      {
        id: 10271102,
        seafarer_id: 100087,
        group_value: 'Heavy Lift Vessel',
        group_by_name: 'vessel_type',
        years: '1.41',
      },
      {
        id: 10271105,
        seafarer_id: 100087,
        group_value: 'Multi Purpose - Container',
        group_by_name: 'vessel_type',
        years: '1.41',
      },
      {
        id: 10271124,
        seafarer_id: 100087,
        group_value: 'Aframax Crude Tanker',
        group_by_name: 'vessel_type',
        years: '8.43',
      },
      {
        id: 10271126,
        seafarer_id: 100087,
        group_value: 'Crude Oil Tanker',
        group_by_name: 'vessel_type',
        years: '8.43',
      },
      {
        id: 10271109,
        seafarer_id: 100087,
        group_value: 'Panamax Container Vessel',
        group_by_name: 'vessel_type',
        years: '0.50',
      },
      {
        id: 10271113,
        seafarer_id: 100087,
        group_value: 'Product cum Chemical Tanker',
        group_by_name: 'vessel_type',
        years: '11.30',
      },
      {
        id: 10271114,
        seafarer_id: 100087,
        group_value: 'Oil cum Chemical Tanker',
        group_by_name: 'vessel_type',
        years: '11.71',
      },
      {
        id: 10271120,
        seafarer_id: 100087,
        group_value: 'Oil Tanker',
        group_by_name: 'vessel_type',
        years: '8.43',
      },
      {
        id: 10271127,
        seafarer_id: 100087,
        group_value: 'Bunkering Tanker',
        group_by_name: 'vessel_type',
        years: '8.43',
      },
      {
        id: 10271129,
        seafarer_id: 100087,
        group_value: 'Aframax Crude Tanker with LNG as Alternate Fuel',
        group_by_name: 'vessel_type',
        years: '8.43',
      },
      {
        id: 10271110,
        seafarer_id: 100087,
        group_value: 'Post Panamax Container Vessel',
        group_by_name: 'vessel_type',
        years: '0.50',
      },
      {
        id: 10271119,
        seafarer_id: 100087,
        group_value: 'Oil Bulk Ore Carrier',
        group_by_name: 'vessel_type',
        years: '8.43',
      },
      {
        id: 10271122,
        seafarer_id: 100087,
        group_value: 'VLCC',
        group_by_name: 'vessel_type',
        years: '8.43',
      },
      {
        id: 10271123,
        seafarer_id: 100087,
        group_value: 'Product Tanker',
        group_by_name: 'vessel_type',
        years: '8.43',
      },
      {
        id: 10271107,
        seafarer_id: 100087,
        group_value: 'Container Vessel',
        group_by_name: 'vessel_type',
        years: '0.50',
      },
      {
        id: 10271108,
        seafarer_id: 100087,
        group_value: 'Container with Gantry',
        group_by_name: 'vessel_type',
        years: '0.50',
      },
      {
        id: 10271118,
        seafarer_id: 100087,
        group_value: 'LPG + AMMONIA',
        group_by_name: 'vessel_type',
        years: '0.22',
      },
      {
        id: 10271096,
        seafarer_id: 100087,
        group_value: 'MASTER',
        group_by_name: 'rank',
        years: '2.36',
      },
      {
        id: 10271097,
        seafarer_id: 100087,
        group_value: 'all',
        group_by_name: 'all',
        years: '13.85',
      },
      {
        id: 10271100,
        seafarer_id: 100087,
        group_value: 'company',
        group_by_name: 'company',
        years: '0.42',
      },
      {
        id: 10271103,
        seafarer_id: 100087,
        group_value: 'Multi Purpose',
        group_by_name: 'vessel_type',
        years: '1.41',
      },
      {
        id: ********,
        seafarer_id: 100087,
        group_value: 'Other',
        group_by_name: 'vessel_type',
        years: '1.41',
      },
      {
        id: ********,
        seafarer_id: 100087,
        group_value: 'Heavy Load Carrier',
        group_by_name: 'vessel_type',
        years: '1.41',
      },
    ],
    seafarer_person: {
      id: 100087,
      current_account_status: 'active',
      current_journey_status: 'signed_on',
      current_exam_status: null,
      first_name: 'Sa** Ra**',
      middle_name: null,
      last_name: 'Ju**',
      date_of_birth: '1982-03-16T00:00:00.000Z',
      gender: 'male',
      place_of_birth: 'Busan',
      nearest_airport: null,
      smoking: 'unknown',
      vegetarian: 'no',
      country_of_birth_id: 123,
      nationality_id: 127,
      passports: [
        {
          id: 341332,
          number: 'M4**',
          date_of_expiry: '2028-11-22T00:00:00.000Z',
        },
      ],
      seaman_books: [],
      nationality: {
        value: 'South Korean',
        id: 127,
      },
      country_of_birth: {
        value: 'Korea, Republic of',
        id: 123,
      },
      addresses: [
        {
          country_id: null,
          id: 697174,
        },
        {
          country_id: null,
          id: 697175,
        },
      ],
      seafarer_status_history: [
        {
          id: 2129276,
          seafarer_journey_status: 'signed_on',
          is_current_status: true,
          rank_id: 1,
          vessel_name: 'Silver Ginny',
          vessel_ref_id: 4789,
          status_date: '2024-08-30T00:00:00.000Z',
          vessel_ownership_id: 1896,
          vessel_id: 716,
          sign_off_date: null,
          expected_contract_end_date: '2024-12-25T00:00:00.000Z',
          embarkation_port: 'Durban, South Africa',
          repatriation_port: null,
          vessel_tech_group: 'Tech T3',
          vessel_type: 'Oil cum Chemical Tanker',
          replaced_by_id: null,
          paris1_ref_id: null,
          seafarer_rank: {
            id: 1,
            value: 'MASTER',
            unit: 'MSTR',
            ref_id: 2000336,
            sortpriority: 1,
            ocimf_value: 'MASTER',
            department: 'deck',
            is_gmdss_required: true,
            is_watch_years_required: false,
            is_admin_accept_required: true,
          },
          replaced_by: null,
        },
      ],
    },
    seafarer_manning_agent: {
      value: 'Panstar Shipping , Busan Korea',
      id: 65,
    },
    seafarer_contact_log: [
      {
        is_latest: true,
        contact_date: '2021-09-06T00:00:00.000Z',
        next_contact_date: null,
        availability_date: '2021-09-06T00:00:00.000Z',
        availability_remarks: null,
        docs_in_hand: false,
      },
    ],
    crew_planning: null,
    crew_planning_remarks: [],
    experience_summary: {
      seafarer_id: 100087,
      duration_with_company: null,
      duration_on_all_vessel_type: '4868',
      duration_on_target_vessel: '152',
      duration_on_target_vessel_type: '974',
      duration_in_target_rank: '700',
      target_vessel_type: 'Oil cum Chemical Tanker',
      target_rank: 'MASTER',
      target_vessel_name: 'Silver Ginny',
    },
    recommended_replacement: [],
  },
  {
    id: 23156,
    seafarer_person_id: 23156,
    ref_id: 121598,
    rank_id: 3,
    is_only_worked_fml: true,
    seafarer_reporting_office: {
      value: 'Manila',
      id: 2,
    },
    hkid: 18427,
    created_at: '2016-12-12T12:19:16.775Z',
    updated_at: '2024-10-03T07:13:52.242Z',
    office_id: 2,
    manning_agent_id: null,
    not_to_be_employed: false,
    not_to_be_employed_reason: null,
    framo_experience: false,
    ice_conditions_experience: null,
    show_cargo_experience: false,
    show_fml_experience: true,
    cargo_experience: null,
    additional_experience: null,
    parent_hkid: null,
    is_parent: null,
    data_quality: 'clean',
    with_fml_vessel_experience: true,
    'seafarer_person:seafarer_status_history:seafarer_rank.sortprior': 6,
    ocimf: [
      {
        id: 70586,
        seafarer_id: 23156,
        group_value: 'Log Carrier',
        group_by_name: 'vessel_type',
        years: '0.51',
      },
      {
        id: 70587,
        seafarer_id: 23156,
        group_value: 'Log/Bulk Carrier',
        group_by_name: 'vessel_type',
        years: '0.51',
      },
      {
        id: 70574,
        seafarer_id: 23156,
        group_value: 'Aframax Crude Tanker',
        group_by_name: 'vessel_type',
        years: '9.02',
      },
      {
        id: 70578,
        seafarer_id: 23156,
        group_value: 'Bunkering Tanker',
        group_by_name: 'vessel_type',
        years: '9.02',
      },
      {
        id: 70581,
        seafarer_id: 23156,
        group_value: 'Chemical Tanker',
        group_by_name: 'vessel_type',
        years: '6.54',
      },
      {
        id: 70571,
        seafarer_id: 23156,
        group_value: 'VLCC',
        group_by_name: 'vessel_type',
        years: '9.02',
      },
      {
        id: 70577,
        seafarer_id: 23156,
        group_value: 'Crude Oil Tanker',
        group_by_name: 'vessel_type',
        years: '9.02',
      },
      {
        id: 70584,
        seafarer_id: 23156,
        group_value: 'Cement Carrier',
        group_by_name: 'vessel_type',
        years: '0.51',
      },
      {
        id: 70564,
        seafarer_id: 23156,
        group_value: 'all',
        group_by_name: 'all',
        years: '11.32',
      },
      {
        id: 70565,
        seafarer_id: 23156,
        group_value: 'dry',
        group_by_name: 'dry',
        years: '0.51',
      },
      {
        id: 70567,
        seafarer_id: 23156,
        group_value: 'company',
        group_by_name: 'company',
        years: '16.33',
      },
      {
        id: 70572,
        seafarer_id: 23156,
        group_value: 'Product Tanker',
        group_by_name: 'vessel_type',
        years: '9.02',
      },
      {
        id: 70585,
        seafarer_id: 23156,
        group_value: 'Gantry',
        group_by_name: 'vessel_type',
        years: '0.51',
      },
      {
        id: 70563,
        seafarer_id: 23156,
        group_value: '2ND OFFICER',
        group_by_name: 'rank',
        years: '6.20',
      },
      {
        id: 70566,
        seafarer_id: 23156,
        group_value: 'tanker',
        group_by_name: 'tanker',
        years: '10.81',
      },
      {
        id: 70573,
        seafarer_id: 23156,
        group_value: 'Product cum Chemical Tanker',
        group_by_name: 'vessel_type',
        years: '10.51',
      },
      {
        id: 70575,
        seafarer_id: 23156,
        group_value: 'Suez Max Tanker',
        group_by_name: 'vessel_type',
        years: '9.02',
      },
      {
        id: 70576,
        seafarer_id: 23156,
        group_value: 'Oil cum Chemical Tanker',
        group_by_name: 'vessel_type',
        years: '10.81',
      },
      {
        id: 70580,
        seafarer_id: 23156,
        group_value: 'Aframax Crude Tanker with LNG as Alternate Fuel',
        group_by_name: 'vessel_type',
        years: '9.02',
      },
      {
        id: 70582,
        seafarer_id: 23156,
        group_value: 'Gas and Chemical Tanker',
        group_by_name: 'vessel_type',
        years: '6.54',
      },
      {
        id: 70589,
        seafarer_id: 23156,
        group_value: 'Self Unloader',
        group_by_name: 'vessel_type',
        years: '0.51',
      },
      {
        id: 70590,
        seafarer_id: 23156,
        group_value: 'Very Large Ore Carrier',
        group_by_name: 'vessel_type',
        years: '0.51',
      },
      {
        id: 70579,
        seafarer_id: 23156,
        group_value: 'Bitumen Tanker',
        group_by_name: 'vessel_type',
        years: '9.02',
      },
      {
        id: 70583,
        seafarer_id: 23156,
        group_value: 'Bulk Carrier',
        group_by_name: 'vessel_type',
        years: '0.51',
      },
      {
        id: 70588,
        seafarer_id: 23156,
        group_value: 'Open Hatch Bulk Carrier',
        group_by_name: 'vessel_type',
        years: '0.51',
      },
      {
        id: 70568,
        seafarer_id: 23156,
        group_value: 'Oil Bulk Ore Carrier',
        group_by_name: 'vessel_type',
        years: '9.02',
      },
      {
        id: 70569,
        seafarer_id: 23156,
        group_value: 'Oil Tanker',
        group_by_name: 'vessel_type',
        years: '9.02',
      },
      {
        id: 70570,
        seafarer_id: 23156,
        group_value: 'ULCC',
        group_by_name: 'vessel_type',
        years: '9.02',
      },
      {
        id: 70591,
        seafarer_id: 23156,
        group_value: 'Wood Chip Carrier',
        group_by_name: 'vessel_type',
        years: '0.51',
      },
      {
        id: 70592,
        seafarer_id: 23156,
        group_value: 'Cape Size Vessel',
        group_by_name: 'vessel_type',
        years: '0.51',
      },
    ],
    seafarer_person: {
      id: 23156,
      current_account_status: 'active',
      current_journey_status: 'signed_on',
      current_exam_status: null,
      first_name: 'Ph**',
      middle_name: 'De** Sa**',
      last_name: 'En**',
      date_of_birth: '1985-10-24T00:00:00.000Z',
      gender: 'male',
      place_of_birth: 'ALCOY CEBU',
      nearest_airport: null,
      smoking: 'unknown',
      vegetarian: 'no',
      country_of_birth_id: 177,
      nationality_id: 181,
      passports: [
        {
          id: 326617,
          number: 'P5**',
          date_of_expiry: '2030-12-08T00:00:00.000Z',
        },
      ],
      seaman_books: [
        {
          id: 577411,
          number: 'A0**',
          date_of_expiry: '2031-01-21T00:00:00.000Z',
          is_original: true,
        },
        {
          id: 577412,
          number: '23**',
          date_of_expiry: '2016-05-09T00:00:00.000Z',
          is_original: false,
        },
      ],
      nationality: {
        value: 'Philippine, Filipino',
        id: 181,
      },
      country_of_birth: {
        value: 'Philippines',
        id: 177,
      },
      addresses: [
        {
          country_id: null,
          id: 667759,
        },
        {
          country_id: null,
          id: 667760,
        },
      ],
      seafarer_status_history: [
        {
          id: 2148625,
          seafarer_journey_status: 'signed_on',
          is_current_status: true,
          rank_id: 3,
          vessel_name: 'Silver Ginny',
          vessel_ref_id: 4789,
          status_date: '2024-10-11T00:00:00.000Z',
          vessel_ownership_id: 1896,
          vessel_id: 716,
          sign_off_date: null,
          expected_contract_end_date: '2025-06-06T00:00:00.000Z',
          embarkation_port: 'DURBAN',
          repatriation_port: 'MANILA',
          vessel_tech_group: 'Tech T3',
          vessel_type: 'Oil cum Chemical Tanker',
          replaced_by_id: null,
          paris1_ref_id: null,
          seafarer_rank: {
            id: 3,
            value: '2ND OFFICER',
            unit: '2/O',
            ref_id: 2000338,
            sortpriority: 6,
            ocimf_value: '2ND OFFICER',
            department: 'deck',
            is_gmdss_required: true,
            is_watch_years_required: true,
            is_admin_accept_required: true,
          },
          replaced_by: null,
        },
      ],
    },
    seafarer_manning_agent: null,
    seafarer_contact_log: [
      {
        is_latest: true,
        contact_date: '2019-09-27T00:00:00.000Z',
        next_contact_date: '2021-12-16T00:00:00.000Z',
        availability_date: '2022-02-04T00:00:00.000Z',
        availability_remarks: null,
        docs_in_hand: false,
      },
    ],
    crew_planning: null,
    crew_planning_remarks: [],
    experience_summary: {
      seafarer_id: 23156,
      duration_with_company: '4000',
      duration_on_all_vessel_type: '4000',
      duration_on_target_vessel: '110',
      duration_on_target_vessel_type: '1133',
      duration_in_target_rank: '2140',
      target_vessel_type: 'Oil cum Chemical Tanker',
      target_rank: '2ND OFFICER',
      target_vessel_name: 'Silver Ginny',
    },
    recommended_replacement: [],
  },
];

const mockRoleConfig = {
  seafarer: { editSeafarer: true, replaceCrewList: true, view: { general: true } },
};

const mockVisitUpdateSeafarer = jest.fn();
const mockOnSelectRow = jest.fn();
const mockSetInitSort = jest.fn();
const mockEventTracker = jest.fn();

describe('CrewListTable', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render table with data and display correct columns', async () => {
    render(
      <CrewListTable
        selectedColumns={getCrewListPageColumns()}
        loading={false}
        data={mockData}
        tableRef={null}
        roleConfig={mockRoleConfig}
        visitUpdateSeafarer={mockVisitUpdateSeafarer}
        eventTracker={mockEventTracker}
        seafarersTotalCount={2}
        init_sort={[]}
        setInitSort={mockSetInitSort}
        selectedRows={getCrewListPageColumns()}
        onSelectRow={mockOnSelectRow}
        tab={'crew'}
        enableOcimf={true}
      />,
    );

    expect(screen.getByText('No.')).toBeInTheDocument();
    expect(screen.getByText('HKID')).toBeInTheDocument();
    expect(screen.getByText('Onboard Seafarer')).toBeInTheDocument();
    expect(screen.getByText('Crew Compliance')).toBeInTheDocument();
  });

  it('should toggle row selection when checkbox is clicked', async () => {
    render(
      <CrewListTable
        selectedColumns={[]}
        loading={false}
        data={mockData}
        tableRef={null}
        roleConfig={mockRoleConfig}
        visitUpdateSeafarer={mockVisitUpdateSeafarer}
        eventTracker={mockEventTracker}
        seafarersTotalCount={2}
        init_sort={[]}
        setInitSort={mockSetInitSort}
        selectedRows={[]}
        onSelectRow={mockOnSelectRow}
        tab={'crew'}
        enableOcimf={true}
      />,
    );
    const checkbox = screen.getByTestId('checkbox');
    fireEvent.click(checkbox);
    await waitFor(() => expect(mockOnSelectRow).toHaveBeenCalledTimes(1));
  });

  it('should render the table with loading spinner when loading is true', async () => {
    render(
      <CrewListTable
        selectedColumns={[]}
        loading={true}
        data={mockData}
        tableRef={null}
        roleConfig={mockRoleConfig}
        visitUpdateSeafarer={mockVisitUpdateSeafarer}
        eventTracker={mockEventTracker}
        seafarersTotalCount={2}
        init_sort={[]}
        setInitSort={mockSetInitSort}
        selectedRows={[]}
        onSelectRow={mockOnSelectRow}
        tab={'crew'}
        enableOcimf={true}
      />,
    );

    expect(screen.getByText('Crew Compliance')).toBeInTheDocument();
  });

  it('should render no records message if table has no data', async () => {
    render(
      <CrewListTable
        selectedColumns={[]}
        loading={false}
        data={[]}
        tableRef={null}
        roleConfig={mockRoleConfig}
        visitUpdateSeafarer={mockVisitUpdateSeafarer}
        eventTracker={mockEventTracker}
        seafarersTotalCount={0}
        init_sort={[]}
        setInitSort={mockSetInitSort}
        selectedRows={[]}
        onSelectRow={mockOnSelectRow}
        tab={'crew'}
        enableOcimf={true}
      />,
    );
    expect(screen.getByText('No Records')).toBeInTheDocument();
  });

  it('should filter the columns based on roleConfig permissions', async () => {
    render(
      <CrewListTable
        selectedColumns={[]}
        loading={false}
        data={mockData}
        tableRef={null}
        roleConfig={{
          seafarer: { editSeafarer: false, replaceCrewList: false, view: { general: true } },
        }}
        visitUpdateSeafarer={mockVisitUpdateSeafarer}
        eventTracker={mockEventTracker}
        seafarersTotalCount={2}
        init_sort={[]}
        setInitSort={mockSetInitSort}
        selectedRows={[]}
        onSelectRow={mockOnSelectRow}
        tab={'crew'}
        enableOcimf={true}
      />,
    );
    expect(screen.queryByText('Actions')).not.toBeInTheDocument();
  });
});
