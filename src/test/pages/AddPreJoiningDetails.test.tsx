import React from 'react';
import { MemoryRouter, Route } from 'react-router-dom';
import '@testing-library/jest-dom';
import { cloneDeep } from 'lodash';
import { render, waitFor, screen, fireEvent, act } from '@testing-library/react';
import { AccessProvider } from '@src/component/common/Access';
import * as seafarerService from '../../service/seafarer-service';
import AddPreJoiningDetails from '../../pages/AddPreJoiningDetails';
import {
  getSeafarerPreJoiningDetails,
  getSignedOnSeafarerPreJoiningDetails,
} from '../resources/pre-joining-response';
import { seafarerGetResponse } from '../resources/document-response';

jest.mock('../../styleGuide');
jest.mock('../../service/user-service');

const seafarer = seafarerGetResponse.data;
const seafarerId = seafarer.id;
const seafarerStatusHistoryId = getSeafarerPreJoiningDetails.seafarer_status_history.id;

const renderAddPreJoiningDetailsPage = () => (
  <MemoryRouter initialEntries={[`/seafarer/details/${seafarerId}/pre-joining/add`]}>
    <AccessProvider
      config={{
        seafarer: {
          edit: {
            wages: true,
          },
        },
      }}
    >
      <Route exact path="/seafarer/details/:seafarerId?/pre-joining/add">
        <AddPreJoiningDetails />
      </Route>
    </AccessProvider>
  </MemoryRouter>
);

const renderPageWithoutRole = () => (
  <MemoryRouter initialEntries={[`/seafarer/details/${seafarerId}/pre-joining/add`]}>
    <AccessProvider
      config={{}}>
      <Route exact path="/seafarer/details/:seafarerId?/pre-joining/add">
        <AddPreJoiningDetails />
      </Route>
    </AccessProvider>
  </MemoryRouter>
);

beforeEach(async () => {
  jest.setTimeout(100000);
  jest.spyOn(seafarerService, 'getSeafarer').mockImplementation(() =>
    Promise.resolve({
      status: 200,
      data: seafarer,
    }),
  );
  jest.spyOn(seafarerService, 'getSeafarerPreJoiningDetails').mockImplementation(() =>
    Promise.resolve({
      status: 200,
      data: getSeafarerPreJoiningDetails,
    }),
  );
  jest.spyOn(seafarerService, 'createSeafarerPreJoiningDetails').mockImplementation(() =>
    Promise.resolve({
      status: 201,
      data: '',
    }),
  );
  jest.spyOn(seafarerService, 'patchSeafarerPreJoiningDetails').mockImplementation(() =>
    Promise.resolve({
      status: 201,
      data: '',
    }),
  );
});

describe('Render AddPreJoiningDetails for creating and updating wages', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('Should validate fail when amounts in the number fields have more than 2 digits post decimal', async () => {
    await act(async () => {
      render(renderAddPreJoiningDetailsPage());
    });

    await act(async () => {
      const allSpinButtons = screen.getAllByRole('spinbutton');
      const amtField = allSpinButtons.find((input) => input.name === 'amount_160');
      if (amtField) {
        fireEvent.change(amtField, { target: { value: '2.3445' } });
      }
    });

    await waitFor(() => {
      expect(
        screen.getByText('Please enter the amount with at most 2 decimal places'),
      ).toBeInTheDocument();
    });
  });

  it('Should validate patch body to only include fields that were updated in UI', async () => {
    await act(async () => {
      render(renderAddPreJoiningDetailsPage());
    });
    await act(async () => {
      const allSpinButtons = screen.getAllByRole('spinbutton');
      const amtField1 = allSpinButtons.find((input) => input.name === 'amount_160');
      if (amtField1) {
        fireEvent.change(amtField1, { target: { value: '550' } });
      }
      const amtField2 = allSpinButtons.find((input) => input.name === 'amount_156');
      if (amtField2) {
        fireEvent.change(amtField2, { target: { value: '680' } });
      }
    });

    const patchSpy = jest.spyOn(seafarerService, 'patchSeafarerPreJoiningDetails');

    const payload = {
      seafarer_joining_spendings: [
        {
          id: 2,
          payhead_id: 156,
          amount: 680,
          remarks: 'this is remarks',
        },
        {
          id: 3,
          payhead_id: 160,
          amount: 550,
          remarks: 'this is remarks',
        },
      ],
    };
    await act(async () => {
      const submitBtn = screen.getByTestId('form-seafarer-save-button');
      fireEvent.click(submitBtn);
    });
    await waitFor(async () => {
      expect(patchSpy).toHaveBeenCalledWith(
        seafarerId.toString(),
        seafarerStatusHistoryId,
        payload,
      );
    });
  });

  it('Should validate pass if the input fields are disabled for "signed_on"/"travelling" seafarers', async () => {
    jest.spyOn(seafarerService, 'getSeafarerPreJoiningDetails').mockImplementation(() =>
      Promise.resolve({
        status: 200,
        data: getSignedOnSeafarerPreJoiningDetails,
      }),
    );
    await act(async () => {
      render(renderAddPreJoiningDetailsPage());
    });
    await waitFor(async () => {
      const allSpinButtons = screen.getAllByRole('spinbutton');
      allSpinButtons.forEach((spinButton) => {
        expect(spinButton).toBeDisabled();
      });
    });
  });

  it('Should validate pass if the input fields are enabled for "crew_assignment_approved" seafarers', async () => {
    await act(async () => {
      render(renderAddPreJoiningDetailsPage());
    });
    await waitFor(() => {
      const allSpinButtons = screen.getAllByRole('spinbutton');
      allSpinButtons.forEach((spinButton) => {
        expect(spinButton).not.toBeDisabled();
      });
    });
  });

  it('Should hide Allotment Detail section for non Filipinos Seafarers', async () => {
    await act(async () => {
      render(renderAddPreJoiningDetailsPage());
    });
    await waitFor(() => {
      const amtField1 = screen.queryByRole('spinbutton', { name: /monthly_allotment/i });
      const amtField2 = screen.queryByRole('spinbutton', { name: /first_allotment/i });

      expect(amtField1).not.toBeInTheDocument();
      expect(amtField2).not.toBeInTheDocument();
    });
  });

  it('Should show Allotment Detail section for Filipinos Seafarers', async () => {
    const filipinoSeafarer = cloneDeep(seafarerGetResponse.data);
    filipinoSeafarer.seafarer_person.nationality = {
      alpha2_code: 'PH',
      alpha3_code: 'PHL',
      id: 181,
      ref_id: null,
      value: 'Philippine, Filipino',
    };
    jest.spyOn(seafarerService, 'getSeafarer').mockImplementation(() =>
      Promise.resolve({
        status: 200,
        data: filipinoSeafarer,
      }),
    );
    await act(async () => {
      render(renderAddPreJoiningDetailsPage());
    });
    await waitFor(() => {
      const allSpinButtons = screen.getAllByRole('spinbutton');
      const amtField1 = allSpinButtons.find((input) => input.name === 'monthly_allotment');
      expect(amtField1).toBeInTheDocument();
      const amtField2 = allSpinButtons.find((input) => input.name === 'first_allotment');
      expect(amtField2).toBeInTheDocument();
    });
  });
  it('should show 403 page when seafarer|edit|wages role is absent', async () => {
    await act(async () => {
      render(renderPageWithoutRole());
    });
    expect(screen.getByText('403')).toBeInTheDocument();
  });
});
