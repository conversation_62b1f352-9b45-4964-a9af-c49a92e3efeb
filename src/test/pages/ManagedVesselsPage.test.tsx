import React, { ReactNode } from 'react';
import '@testing-library/jest-dom';
import { render, fireEvent, waitFor, act, screen } from '@testing-library/react';
import { AccessProvider } from '@src/component/common/Access';
import ManagedVesselsPage, {
  getValue,
  handleSorting,
  updateVesselListWithActionRequired,
  updateVesselListWithItinerary,
} from '../../pages/ManagedVesselsPage';
import {
  ACTION_NOT_REQUIRED,
  ACTION_REQUIRED,
  MANAGED_VESSELS_ACTION_REQUIRED_STATUS,
} from '../../constants/managedVessels';

jest.mock('../../styleGuide');
jest.mock('../../service/user-service');
jest.mock('../../service/vessel-service');
jest.mock('../../service/keycloak-service');
jest.mock('../../service/travel-service');
jest.mock('../../service/seafarer-service');

const roleConfig = {
  seafarer: {
    view: {
      crewPlannerSeafarer: true,
    },
  },
};

function renderWithRoleConfig(child: ReactNode, roleConfig: object) {
  return act(async () => {
    return render(<AccessProvider config={roleConfig}>{child}</AccessProvider>);
  });
}
describe('ManagedVesselsPage', () => {
  beforeEach(async () => {
    await renderWithRoleConfig(
      <ManagedVesselsPage ga4react={{ event: () => {}, pageview: () => {} }} />,
      roleConfig,
    );
  });

  jest.clearAllMocks();

  test('renders the page correctly', async () => {
    expect(screen.getByTestId('managed-vessels-table')).toBeInTheDocument();
  });

  test('updates the search name', async () => {
    const searchInput = screen.getByTestId('managed-vessels-search-name');
    await act(async () => {
      fireEvent.click(searchInput);
      fireEvent.change(searchInput, { target: { value: 'test' } });
    });
    expect(searchInput.value).toBe('test');
  });

  test('updates the search type dropdown', async () => {
    const typeDropdown = screen.getByTestId('dropdown-select-vessel-category');
    await act(async () => {
      fireEvent.click(typeDropdown);
      fireEvent.change(typeDropdown, { target: { value: 'General Cargo' } });
    });
    await waitFor(() => expect(typeDropdown.value).toBe('General Cargo'));
  });

  test('updates the search owner dropdown', async () => {
    const ownerDropdown = screen.getByTestId('dropdown-select-owner');
    await act(async () => {
      fireEvent.click(ownerDropdown);
      fireEvent.change(ownerDropdown, { target: { value: 'Great Field Limited' } });
    });
    await waitFor(() => expect(ownerDropdown.value).toBe('Great Field Limited'));
  });

  test('updates the search tech group dropdown', async () => {
    const techGroupDropdown = screen.getByTestId('dropdown-select-tech-group');
    await act(async () => {
      fireEvent.click(techGroupDropdown);
      fireEvent.change(techGroupDropdown, { target: { value: 'Celsius Tech' } });
    });
    await waitFor(() => expect(techGroupDropdown.value).toBe('Celsius Tech'));
  });

  test('updates the search vessel status dropdown', async () => {
    const statusDropdown = screen.getByTestId('dropdown-select-vessel-status');
    await act(async () => {
      fireEvent.click(statusDropdown);
      fireEvent.change(statusDropdown, {
        target: { value: MANAGED_VESSELS_ACTION_REQUIRED_STATUS[0] },
      });
    });
    await waitFor(() =>
      expect(statusDropdown.value).toBe(MANAGED_VESSELS_ACTION_REQUIRED_STATUS[0]),
    );
  });
});

describe('getValue', () => {
  test('should return the correct values for a simple object property', () => {
    const a = { name: 'John' };
    const b = { name: 'Jane' };
    const sortField = 'name';
    const [aValue, bValue] = getValue(a, b, sortField);
    expect(aValue).toBe('John');
    expect(bValue).toBe('Jane');
  });

  test('should return the correct values for a nested object property', () => {
    const a = { user: { name: 'John' } };
    const b = { user: { name: 'Jane' } };
    const sortField = 'user.name';
    const [aValue, bValue] = getValue(a, b, sortField);
    expect(aValue).toBe('John');
    expect(bValue).toBe('Jane');
  });
});

describe('handleSorting', () => {
  test('should sort the list correctly for a simple sorting field', () => {
    const initSort = [{ id: 'name', desc: false }];
    const processedVesselList = [{ name: 'John' }, { name: 'Jane' }, { name: 'Bob' }];

    const sortedList = handleSorting(initSort, processedVesselList);

    expect(sortedList).toEqual([{ name: 'Bob' }, { name: 'Jane' }, { name: 'John' }]);
  });

  test('should sort the list correctly for a nested sorting field', () => {
    const initSort = [{ id: 'user.name', desc: true }];
    const processedVesselList = [
      { user: { name: 'John' } },
      { user: { name: 'Jane' } },
      { user: { name: 'Bob' } },
    ];

    const sortedList = handleSorting(initSort, processedVesselList);

    expect(sortedList).toEqual([
      { user: { name: 'John' } },
      { user: { name: 'Jane' } },
      { user: { name: 'Bob' } },
    ]);
  });
});

describe('updateVesselListWithItinerary', () => {
  test('should update the vessel list with itinerary data', async () => {
    const vesselList = [
      { id: '1', name: 'Vessel 1' },
      { id: '2', name: 'Vessel 2' },
      { id: '3', name: 'Vessel 3' },
    ];

    const vesselItinerary = [
      { vessel_ownership_id: '1', start_date: '2023-06-01', end_date: '2023-06-15' },
      { vessel_ownership_id: '3', start_date: '2023-07-01', end_date: '2023-07-15' },
    ];

    let updatedVesselList;
    await act(async () => {
      updatedVesselList = updateVesselListWithItinerary(vesselItinerary, vesselList);
    });

    expect(updatedVesselList).toEqual([
      {
        id: '1',
        name: 'Vessel 1',
        itinerary: { vessel_ownership_id: '1', start_date: '2023-06-01', end_date: '2023-06-15' },
      },
      { id: '2', name: 'Vessel 2' },
      {
        id: '3',
        name: 'Vessel 3',
        itinerary: { vessel_ownership_id: '3', start_date: '2023-07-01', end_date: '2023-07-15' },
      },
    ]);
  });

  test('should not modify vessels without itinerary data', async () => {
    const vesselList = [
      { id: '1', name: 'Vessel 1' },
      { id: '2', name: 'Vessel 2' },
      { id: '3', name: 'Vessel 3' },
    ];

    const vesselItinerary = [
      { vessel_ownership_id: '1', start_date: '2023-06-01', end_date: '2023-06-15' },
    ];

    let updatedVesselList;
    await act(async () => {
      updatedVesselList = updateVesselListWithItinerary(vesselItinerary, vesselList);
    });

    expect(updatedVesselList).toEqual([
      {
        id: '1',
        name: 'Vessel 1',
        itinerary: { vessel_ownership_id: '1', start_date: '2023-06-01', end_date: '2023-06-15' },
      },
      { id: '2', name: 'Vessel 2' },
      { id: '3', name: 'Vessel 3' },
    ]);
  });
});

describe('updateVesselListWithActionRequired', () => {
  const vesselList = [
    {
      id: 1,
      name: 'Vessel 1',
      vessel: {
        id: 1,
      },
    },
    {
      id: 2,
      name: 'Vessel 2',
      vessel: {
        id: 2,
      },
    },
    {
      id: 3,
      name: 'Vessel 3',
      vessel: {
        id: 3,
      },
    },
  ];

  it('should update the vessel list with action required information', async () => {
    const planningActionResult = [
      { vesselId: 1, actionRequired: true },
      { vesselId: 2, actionRequired: false },
    ];

    let updatedVesselList;
    await act(async () => {
      updatedVesselList = updateVesselListWithActionRequired(
        planningActionResult,
        vesselList,
        ACTION_REQUIRED,
      );
    });

    expect(updatedVesselList).toHaveLength(1);
    expect(updatedVesselList[0]).toEqual({
      id: 1,
      name: 'Vessel 1',
      actionRequired: true,
      vessel: {
        id: 1,
      },
    });
  });

  it('should filter the vessel list based on the search vessel status', async () => {
    const planningActionResult = [
      { vesselId: 1, actionRequired: true },
      { vesselId: 2, actionRequired: false },
      { vesselId: 3, actionRequired: true },
    ];

    let updatedVesselList;
    await act(async () => {
      updatedVesselList = updateVesselListWithActionRequired(
        planningActionResult,
        vesselList,
        ACTION_REQUIRED,
      );
    });
    expect(updatedVesselList).toHaveLength(2);
    expect(updatedVesselList).toEqual([
      {
        id: 1,
        name: 'Vessel 1',
        actionRequired: true,
        vessel: {
          id: 1,
        },
      },
      {
        id: 3,
        name: 'Vessel 3',
        actionRequired: true,
        vessel: {
          id: 3,
        },
      },
    ]);

    await act(async () => {
      updatedVesselList = updateVesselListWithActionRequired(
        planningActionResult,
        vesselList,
        ACTION_NOT_REQUIRED,
      );
    });
    expect(updatedVesselList).toHaveLength(1);
    expect(updatedVesselList).toEqual([
      {
        id: 2,
        name: 'Vessel 2',
        actionRequired: false,
        vessel: {
          id: 2,
        },
      },
    ]);
  });
});
