import moment from 'moment';
import defaultFilters from '../../util/advance-search/advance-search-default-filter';

jest.mock('../../util/advance-search/search-types', () => () => [
  { type: 'data_of_contract_expry' },
  { type: 'sign_on_ranks' },
  { type: 'tech_group' },
  { type: 'offices' },
  { type: 'vessel' },
  { type: 'owner' },
  { type: 'nationalities' },
  { type: 'available_date' },
  { type: 'account_status' },
  { type: 'target_rank' },
  { type: 'with_fml_vessel_experience' },
  { type: 'target_vessel_type' },
  { type: 'duration_with_company' },
  { type: 'engine_type' },
  { type: 'duration_in_target_rank' },
  { type: 'vessel_size' },
  { type: 'duration_on_target_vessel_type' },
  { type: 'last_contact_date' },
]);

describe('defaultFilters', () => {
  it('should return correct filters for "contract-expiry" with approximate date check', () => {
    const filters = defaultFilters('contract-expiry');

    // Define the current date and 3 months in the future for date approximation
    const currentDate = moment();
    const threeMonthsFutureDate = moment().add(3, 'months');

    expect(filters).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          type: { type: 'data_of_contract_expry' },
          subtype: {
            startDate: expect.any(Date),
            endDate: expect.any(Date),
          },
          defaultTab: 'contract-expiry',
        }),
        expect.objectContaining({
          type: { type: 'sign_on_ranks' },
          defaultTab: 'contract-expiry',
        }),
        // Additional checks for other filters as needed...
      ])
    );

    // Ensure the dates fall within the expected range
    const contractExpiryFilter = filters.find(f => f.type.type === 'data_of_contract_expry');
    expect(moment(contractExpiryFilter.subtype.startDate).isSame(currentDate, 'day')).toBe(true);
    expect(moment(contractExpiryFilter.subtype.endDate).isSame(threeMonthsFutureDate, 'day')).toBe(true);
  });

    it('should return correct filters for "available-seafarers"', () => {
    const filters = defaultFilters('available-seafarers');

    expect(filters).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          type: { type: 'target_rank' },
          subtype: [
            {
              id: 0,
              value: 'All Ranks',
            },
          ],
          defaultTab: 'available-seafarers',
        }),
        expect.objectContaining({
          type: { type: 'offices' },
          subtype: [
            {
              id: 0,
              value: 'All',
              ship_part_id: 0,
            },
          ],
          defaultTab: 'available-seafarers',
        }),
        expect.objectContaining({
          type: { type: 'last_contact_date' },
          subtype: [
            {
              id: 0,
              value: 'All',
            },
          ],
          defaultTab: 'available-seafarers',
        }),
      ])
    );
  });
});
