import parse from 'html-react-parser';
import { parseHTML } from '../../util/sharepoint';

jest.mock('html-react-parser');

describe('parseHTML', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should not parse HTML if webpart data title does not include "modern script editor"', () => {
    const mockWebPartData = {
      title: 'Other Script Editor',
      properties: { script: '<div>No Parse</div>' }
    };

    const data = [
      {
        props: {
          children: [
            { props: {} }, // First child
            { 
              props: {
                children: [ /* Placeholder */ ],
                'data-sp-webpartdata': JSON.stringify(mockWebPartData)
              }
            }
          ]
        }
      }
    ];

    parseHTML(data);

    expect(parse).not.toHaveBeenCalled();
    expect(data[0].props.children[0]).toEqual({"props": {}});
    expect(data[0].props.children[1]).toEqual(data[0].props.children[1]);
  });

  test('should handle empty data gracefully', () => {
    const data = undefined;

    parseHTML(data);
    expect(parse).not.toHaveBeenCalled(); // parse should not be called
  });

  test('should not crash when children length is not 2', () => {
    const data = [
      {
        props: {
          children: [
            { props: {} }, // First child
          ]
        }
      }
    ];

    parseHTML(data);

    expect(parse).not.toHaveBeenCalled(); // parse should not be called
    expect(data[0].props.children.length).toBe(1); // Children length should remain unchanged
  });

  test('should not crash when data-sp-webpartdata is missing', () => {
    const data = [
      {
        props: {
          children: [
            { props: {} }, // First child
            { 
              props: {
                children: [ /* Placeholder */ ]
              }
            }
          ]
        }
      }
    ];

    parseHTML(data);

    expect(parse).not.toHaveBeenCalled();
  });
});
