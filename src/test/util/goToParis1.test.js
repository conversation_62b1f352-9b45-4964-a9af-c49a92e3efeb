import './envSetupForTest';
import { goToParis1, goToParis1SeafarerExp, goToParis1SeafarerDocument, goToParis1SeafarerRecommend } from '../../util/paris-link';

describe('goToParis1 functions', () => {
  const originalOpen = window.open;

  beforeAll(() => {
    window.open = jest.fn();
  });

  afterAll(() => {
    // Restore the original window.open function
    window.open = originalOpen;
  });

  afterEach(() => {
    // Clear all mock calls after each test
    jest.clearAllMocks();
  });

  test('goToParis1 with ref_id and shipPartyId opens correct URL', () => {
    const ref_id = '123';
    const shipPartyId = 'abc';
    goToParis1(ref_id, shipPartyId);
    expect(window.open).toHaveBeenCalledWith(
      'https://test-paris-one.com//FMLLoginKeycloak?targeturl=/fml/fml/PARIS?display=crewoverview&crewid=123',
      '_blank'
    );
  });

  test('goToParis1 with ref_id and without shipPartyId opens correct URL', () => {
    const ref_id = '123';
    goToParis1(ref_id);
    expect(window.open).toHaveBeenCalledWith(
      'https://test-paris-one.com//fml/PARIS?display=crewoverview&crewid=123',
      '_blank'
    );
  });

  test('goToParis1 without ref_id opens base URL', () => {
    goToParis1();
    expect(window.open).toHaveBeenCalledWith(
      'https://test-paris-one.com',
      '_blank'
    );
  });

  test('goToParis1SeafarerExp with ref_id and shipPartyId opens correct URL', () => {
    const ref_id = '123';
    const shipPartyId = 'abc';
    goToParis1SeafarerExp(ref_id, shipPartyId);
    expect(window.open).toHaveBeenCalledWith(
      'https://test-paris-one.com/fml//FMLLoginKeycloak?targeturl=/fml/PARIS?display=experience&crewid=123',
      '_blank'
    );
  });

  test('goToParis1SeafarerExp with ref_id and without shipPartyId opens correct URL', () => {
    const ref_id = '123';
    goToParis1SeafarerExp(ref_id);
    expect(window.open).toHaveBeenCalledWith(
      'https://test-paris-one.com/fml//PARIS?display=experience&crewid=123',
      '_blank'
    );
  });

  test('goToParis1SeafarerExp without ref_id opens base URL', () => {
    goToParis1SeafarerExp();
    expect(window.open).toHaveBeenCalledWith(
      'https://test-paris-one.com',
      '_blank'
    );
  });

  test('goToParis1SeafarerDocument with ref_id and shipPartyId opens correct URL', () => {
    const ref_id = '123';
    const shipPartyId = 'abc';
    goToParis1SeafarerDocument(ref_id, shipPartyId);
    expect(window.open).toHaveBeenCalledWith(
      'https://test-paris-one.com/fml//FMLLoginKeycloak?targeturl=/fml/PARIS?display=documents&crewid=123',
      '_blank'
    );
  });

  test('goToParis1SeafarerDocument with ref_id and without shipPartyId opens correct URL', () => {
    const ref_id = '123';
    goToParis1SeafarerDocument(ref_id);
    expect(window.open).toHaveBeenCalledWith(
      'https://test-paris-one.com/fml//PARIS?display=documents&crewid=123',
      '_blank'
    );
  });

  test('goToParis1SeafarerDocument without ref_id opens base URL', () => {
    goToParis1SeafarerDocument();
    expect(window.open).toHaveBeenCalledWith(
      'https://test-paris-one.com',
      '_blank'
    );
  });

  test('goToParis1SeafarerRecommend with ref_id and shipPartyId opens correct URL', () => {
    const ref_id = '123';
    const shipPartyId = 'abc';
    goToParis1SeafarerRecommend(ref_id, shipPartyId);
    expect(window.open).toHaveBeenCalledWith(
      'https://test-paris-one.com/fml//FMLLoginKeycloak?targeturl=/fml/PARIS?display=assignment&pitype=assign&crewid=123',
      '_blank'
    );
  });

  test('goToParis1SeafarerRecommend with ref_id and without shipPartyId opens correct URL', () => {
    const ref_id = '123';
    goToParis1SeafarerRecommend(ref_id);
    expect(window.open).toHaveBeenCalledWith(
      'https://test-paris-one.com/fml//PARIS?display=assignment&pitype=assign&crewid=123',
      '_blank'
    );
  });

  test('goToParis1SeafarerRecommend without ref_id opens base URL', () => {
    goToParis1SeafarerRecommend();
    expect(window.open).toHaveBeenCalledWith(
      'https://test-paris-one.com',
      '_blank'
    );
  });
});
