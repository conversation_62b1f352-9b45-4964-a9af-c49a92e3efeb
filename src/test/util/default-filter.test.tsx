import defaultFilters, { INITIAL_LOAD_FILTERS } from '../../util/CrewPlanner/default-filter';
import { seafarerStatus } from '../../model/constants';
import { ON_LEAVE_SEAFARER, SEAFARERS_TO_RELIEVE } from '../../constants/crewPlanner';

jest.mock('../../util/advance-search/search-types', () => jest.fn(() => [
  { type: 'account_status', name: 'Account Status' },
  { type: 'journey_status', name: 'Journey Status' },
  { type: 'hkid', name: 'HKID' },
]));

describe('defaultFilters', () => {
  it('should return empty array when key is SEAFARERS_TO_RELIEVE', () => {
    const filters = defaultFilters(SEAFARERS_TO_RELIEVE);
    expect(filters).toEqual([]);
  });

  it('should return correct filters for ON_LEAVE_SEAFARER', () => {
    const filters = defaultFilters(ON_LEAVE_SEAFARER);
    expect(filters).toEqual([
      {
        type: { type: 'account_status', name: 'Account Status' },
        subtype: [
          {
            id: 'active',
            value: 'Active',
          },
        ],
        defaultTab: ON_LEAVE_SEAFARER,
      },
      {
        type: { type: 'journey_status', name: 'Journey Status' },
        subtype: [
          {
            id: seafarerStatus.ON_LEAVE,
            value: 'On Leave',
          },
        ],
        defaultTab: ON_LEAVE_SEAFARER,
      },
    ]);
  });

  it('should return empty array for any other key', () => {
    const filters = defaultFilters('UNKNOWN_KEY');
    expect(filters).toEqual([]);
  });
});

describe('INITIAL_LOAD_FILTERS', () => {
  it('should contain initial filter with type HKID', () => {
    expect(INITIAL_LOAD_FILTERS).toEqual([
      {
        type: { type: 'hkid', name: 'HKID' },
        subtype: '',
      },
    ]);
  });
});
