import defaultFilters from '../../util/advance-search/seafarer-report-default-filters';
import { reportTabNames, reportFilterKeys } from '../../types/seafarerReports';
import SEARCH_TYPES from '../../util/advance-search/search-types';

jest.mock('../../service/user-service');
jest.mock('../../styleGuide');
jest.mock('../../util/advance-search/search-types', () => ({
  __esModule: true,
  default: jest.fn(() => [
    { type: 'planned_wages_unit' },
    { type: 'vessel' },
    { type: 'recommend_date' },
    { type: 'reports_vessel' },
    { type: 'approval_date' },
    { type: 'reports_ranks' },
    { type: 'recommended' },
    { type: 'approval_status' },
    { type: 'date_of_joining' },
    { type: 'reports_nationalities' },
    { type: 'reporting_offices' },
    { type: 'report_owners' },
    { type: 'vessel_with_id' },
    { type: 'commencement_of_contract' },
  ]),
}));

describe('defaultFilters', () => {
  beforeEach(() => {
    SEARCH_TYPES.mockClear();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('should return filters for MODELLER tab', () => {
    const result = defaultFilters(reportTabNames.MODELLER);
    
    expect(result).toHaveLength(2);
    expect(result[0]).toEqual(expect.objectContaining({
      type: expect.objectContaining({ type: 'planned_wages_unit' }),
      subtype: expect.any(String),
      defaultTab: reportTabNames.MODELLER,
    }));
    expect(result[1]).toEqual(expect.objectContaining({
      type: expect.objectContaining({ type: 'vessel' }),
      subtype: [{ id: 0, value: 'All Vessels' }],
      defaultTab: reportTabNames.MODELLER,
    }));
  });

  test('should return filters for APPROVAL_REPORT tab', () => {
    const result = defaultFilters(reportTabNames.APPROVAL_REPORT);
    
    expect(result).toHaveLength(6);
    expect(result[0]).toEqual(expect.objectContaining({
      type: expect.objectContaining({ type: 'recommend_date' }),
      subtype: { startDate: expect.any(Date), endDate: expect.any(Date) },
      defaultTab: reportTabNames.APPROVAL_REPORT,
    }));
    expect(result[1].type.type).toBe(reportFilterKeys.reports_vessel);
    expect(result[3].type.type).toBe(reportFilterKeys.reports_ranks);
    expect(result[4].type.type).toBe(reportFilterKeys.recommended);
    expect(result[5].type.type).toBe(reportFilterKeys.approval_status);
  });

  test('should return filters for SIGNED_ON tab', () => {
    const result = defaultFilters(reportTabNames.SIGNED_ON);
    
    expect(result).toHaveLength(5);
    expect(result[0]).toEqual(expect.objectContaining({
      type: expect.objectContaining({ type: 'date_of_joining' }),
      subtype: { startDate: expect.any(Date), endDate: expect.any(Date) },
      defaultTab: reportTabNames.SIGNED_ON,
    }));
  });

  test('should return filters for DG_SHIPPING_LIST tab', () => {
    const result = defaultFilters(reportTabNames.DG_SHIPPING_LIST);
    
    expect(result).toHaveLength(1);
    expect(result[0]).toEqual(expect.objectContaining({
      type: expect.objectContaining({ type: 'commencement_of_contract' }),
      subtype: { startDate: expect.any(Date), endDate: expect.any(Date) },
      defaultTab: reportTabNames.DG_SHIPPING_LIST,
    }));
  });

  test('should return empty array for unknown tab name', () => {
    const result = defaultFilters('UNKNOWN_TAB');
    expect(result).toEqual([]);
  });
});
