import { filterPages, getVisiblePages, generateQueryParams } from '../../util/pagination';

describe('filterPages', () => {
  it('should filter pages that are greater than total pages', () => {
    expect(filterPages([1, 2, 3, 4, 5], 3)).toEqual([1, 2, 3]);
  });

  it('should return the same pages if all are within total pages', () => {
    expect(filterPages([1, 2, 3], 5)).toEqual([1, 2, 3]);
  });

  it('should return an empty array if visiblePages is empty', () => {
    expect(filterPages([], 5)).toEqual([]);
  });
});

describe('getVisiblePages', () => {
  it('should return pages up to the total if total is less than 7', () => {
    expect(getVisiblePages(1, 5)).toEqual([1, 2, 3, 4, 5]);
  });

  it('should return first, previous, current, next, and last page when page is in the middle of range', () => {
    expect(getVisiblePages(6, 10)).toEqual([1, 5, 6, 7, 10]);
  });

  it('should return first and last four pages if current page is near the end', () => {
    expect(getVisiblePages(9, 10)).toEqual([1, 7, 8, 9, 10]);
  });

  it('should return default page sequence when current page is low', () => {
    expect(getVisiblePages(2, 10)).toEqual([1, 2, 3, 4, 5, 10]);
  });
});

describe('generateQueryParams', () => {
  it('should generate query params based on given paginate parameters', () => {
    const paginateParams = { pageSize: 10, pageIndex: 1, sortBy: [{ id: 'name', desc: true }] };
    expect(generateQueryParams(3, 'vessel', paginateParams)).toBe('&limit=10&offset=1&ship_party_type_id=3&name=vessel&orderBy=name desc');
  });

  it('should omit ship_party_type_id param if it is 0', () => {
    const paginateParams = { pageSize: 5, pageIndex: 2, sortBy: [{ id: 'id', desc: false }] };
    expect(generateQueryParams(0, '', paginateParams)).toBe('&limit=5&offset=2&orderBy=id asc');
  });

  it('should handle missing optional fields in paginateParams', () => {
    const paginateParams = { pageSize: 15, pageIndex: 3, sortBy: [] };
    expect(generateQueryParams(1, '', paginateParams)).toBe('&limit=15&offset=3&ship_party_type_id=1');
  });

  it('should return an empty string if paginateParams is empty', () => {
    const paginateParams = { pageSize: undefined, pageIndex: undefined, sortBy: [] };
    expect(generateQueryParams(0, '', paginateParams)).toBe('');
  });
});
