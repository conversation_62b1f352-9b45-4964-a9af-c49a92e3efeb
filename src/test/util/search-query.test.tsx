import {
  generateQuickSearch,
  addSortOrPaginateParams,
  addSortOrPaginateParamsForReports,
  getDateQuery,
  getValueFromCriteriaItem,
  putValueToCriteriaItem,
  getQueryItem,
  mapSearchCriteriaToQueryString,
  mapQueryStringToSearchCriteria,
} from '../../util/advance-search/search-query';

jest.mock('../../service/user-service');
jest.mock('../../service/travel-service');
jest.mock('../../styleGuide');
describe('Search Utility Functions', () => {
  describe('generateQuickSearch', () => {
    it('should generate query string from quick search params', () => {
      const params = { name: '<PERSON>', age: 30 };
      const result = generateQuickSearch(params);
      expect(result).toBe('name=John%20Doe&age=30');
    });

    it('should ignore empty values in quick search params', () => {
      const params = { name: '<PERSON>', age: '' };
      const result = generateQuickSearch(params);
      expect(result).toBe('name=John%20Doe');
    });
  });

  describe('addSortOrPaginateParams', () => {
    it('should add sorting and pagination parameters to query string', () => {
      const sortPaginateData = {
        pageSize: 10,
        pageIndex: 2,
        sortBy: [{ id: 'name', desc: false }],
      };
      const queryParams = 'existingQuery';
      const result = addSortOrPaginateParams(sortPaginateData, queryParams);
      expect(result).toBe('existingQuery&limit=10&offset=2&orderBy=name asc');
    });

    it('should use offset value when pageIndex is not provided', () => {
      const sortPaginateData = { pageSize: 10, offset: 5, sortBy: [] };
      const queryParams = 'existingQuery';
      const result = addSortOrPaginateParams(sortPaginateData, queryParams);
      expect(result).toBe('existingQuery&limit=10&offset=5');
    });
  });

  describe('addSortOrPaginateParamsForReports', () => {
    it('should add sorting and pagination parameters to report query string', () => {
      const sortPaginateData = { pageSize: 20, pageIndex: 1, sortBy: [{ id: 'date', desc: true }] };
      const queryParams = 'existingQuery';
      const result = addSortOrPaginateParamsForReports(sortPaginateData, queryParams);
      expect(result).toBe('existingQuery&limit=20&offset=20&orderBy=date desc');
    });
  });

  describe('getDateQuery', () => {
    it('should return formatted date query', () => {
      const item = {
        subtype: {
          startDate: '2022-01-01',
          endDate: '2022-12-31',
        },
      };
      const result = getDateQuery(item);
      expect(result).toEqual({ min: '2022-01-01', max: '2022-12-31' });
    });

    it('should handle invalid dates gracefully', () => {
      const item = { subtype: { startDate: null, endDate: null } };
      const result = getDateQuery(item);
      expect(result).toBeUndefined();
    });
  });

  describe('getValueFromCriteriaItem', () => {
    it('should extract value for dropdown input type', () => {
      const item = {
        type: { inputType: 'dropdown', type: 'vessel_size' },
        subtype: [{ min: 10, max: 20 }],
      };
      const result = getValueFromCriteriaItem(item);
      expect(result).toBe('10,20');
    });

    it('should handle checkbox input type', () => {
      const item = {
        type: { inputType: 'checkbox' },
        subtype: [
          { name: 'Option 1', checked: true },
          { name: 'Option 2', checked: false },
        ],
      };
      const result = getValueFromCriteriaItem(item);
      expect(result).toBe('Option 1');
    });
  });

  describe('putValueToCriteriaItem', () => {
    it('should set value for dropdown input type', () => {
      const item = { type: { inputType: 'dropdown' }, subtype: [] };
      const value = 'value1';
      const result = putValueToCriteriaItem(item, value);
      expect(result.subtype).toEqual({ value: 'value1' });
    });

    it('should handle number range input type', () => {
      const item = { type: { inputType: 'number_range' }, subtype: {} };
      const value = { min: 5, max: 10 };
      const result = putValueToCriteriaItem(item, value);
      expect(result.subtype).toEqual(value);
    });
  });

  describe('getQueryItem', () => {
    it('should return query item for LIKE query type', () => {
      const item = { type: { queryType: 'LIKE', queryKey: 'name' }, subtype: 'John' };
      const result = getQueryItem(item);
      expect(result).toEqual({ key: 'name', value: 'John' });
    });

    it('should return query item for RANGE query type', () => {
      const item = { type: { queryType: 'RANGE', queryKey: 'age' }, subtype: { min: 20, max: 30 } };
      const result = getQueryItem(item);
      expect(result).toEqual({
        key: 'age',
        value: {
          max: 30,
          min: 20,
        },
      });
    });
  });

  describe('mapSearchCriteriaToQueryString', () => {
    it('should map search criteria to query string', () => {
      const searchCriteria = [
        { type: { queryKey: 'name', queryType: 'LIKE' }, subtype: 'John' },
        { type: { queryKey: 'age', queryType: 'RANGE' }, subtype: { min: 20, max: 30 } },
      ];
      const result = mapSearchCriteriaToQueryString(searchCriteria);
      expect(result).toBe('name=John&age=%5Bobject%20Object%5D');
    });
  });

  describe('mapQueryStringToSearchCriteria', () => {
    it('should convert query string to search criteria', () => {
      const queryString = 'name=John&age=20,30';
      const dropDownData = {}; // Mock dropDownData if needed
      const result = mapQueryStringToSearchCriteria(queryString, dropDownData);
      expect(result).toEqual([
        {
          subtype: 'John',
          type: {
            inputType: 'text',
            name: 'First Name',
            queryKey: 'seafarer_person.first_name',
            queryType: 'like',
            section: '',
            type: 'first_name',
            validTabs: [
              'all',
              'passed',
              'under_screening',
              'rejected',
              'archived',
              'contract-expiry',
              'seafarers-to-relieve',
              'on-leave-seafarers',
            ],
          },
        },
        {
          subtype: { value: '20,30' },
          type: {
            inputType: 'dropdown',
            name: 'Age',
            notMultiple: true,
            queryKey: 'seafarer_age',
            queryType: 'match',
            section: '',
            type: 'seafarer_age',
            validTabs: ['available-seafarers'],
          },
        },
      ]);
    });
  });
});
