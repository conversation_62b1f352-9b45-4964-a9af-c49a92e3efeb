import { ReactWrapper } from "enzyme";

export const simulateChangeEvent = (wrapper: ReactWrapper, id: string,name: string,value: string) => {
    wrapper
    .find(`[data-testid="${id}"]`)
    .at(0).simulate('change', {
      // you must add this next line as (Formik calls e.persist() internally)
      persist: () => {},
      // simulate changing e.target.name and e.target.value
      target: {
        name,
        value
      },
    });
    return wrapper.find(`[data-testid="${id}"]`).at(0).props().value;
  }