import { render } from '@testing-library/react';
import { MemoryHistory, LocationState } from 'history';
import React, { ReactElement, ReactNode } from 'react';
import { Route, Router, BrowserRouter } from 'react-router-dom';
import { act } from 'react-dom/test-utils';
import { AccessProvider } from '@src/component/common/Access';

function renderWithRoute(
  history: MemoryHistory<LocationState>,
  child: ReactNode,
  path: string,
  config: object = {},
) {
  return act(async () => {
    render(
      <Router history={history}>
        <Route exact path={path}>
          <AccessProvider config={config}>{child}</AccessProvider>
        </Route>
      </Router>,
    );
  });
}

const renderWithBrowserRouter = (ui: ReactElement, { route = '/' } = {}) => {
  window.history.pushState({}, 'Test page', route);

  return render(ui, { wrapper: BrowserRouter });
};

export { renderWithRoute, renderWithBrowserRouter };
