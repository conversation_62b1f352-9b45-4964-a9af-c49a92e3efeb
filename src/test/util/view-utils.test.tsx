import {
  getShortDate,
  sortOnGivenOrder,
  checkUserBelongToSameShipParty,
  filterByExcludedKeys,
  filterByKeys,
  joinContacts,
  formatAmount,
} from '../../util/view-utils';
import dropdowndata from '../resources/drop-down-data.json';
import { shipPartyType } from '../../model/constants';

describe('view-utils', () => {
  describe('getShortDate', () => {
    it('should return short date in expected format', () => {
      expect(getShortDate('2020-02-09T09:21:16.938Z')).toEqual('09 Feb, 2020');
    });
  });

  describe('sortOnGivenOrder', () => {
    it('should return sorted JSON based on another array order', () => {
      const givenOrder = ['mango', 'cherry', 'banana'];
      const json = [
        { id: 1, name: 'cherry' },
        { id: 2, name: 'banana' },
        { id: 3, name: 'mango' },
      ];
      const expectedSortedJson = [
        { id: 3, name: 'mango' },
        { id: 1, name: 'cherry' },
        { id: 2, name: 'banana' },
      ];
      expect(sortOnGivenOrder(json, givenOrder, 'name')).toEqual(expectedSortedJson);
    });
  });

  describe('checkUserBelongToSameShipParty', () => {
    it('should return true when manning agent belongs to the same group', () => {
      const seafarer = {
        id: 11,
        office_id: 1,
      };
      const keycloak = {
        shipPartyId: 1727,
        shipPartyType: shipPartyType.MANNING_AGENT,
      };
      const userIsBelongToSameShipParty = checkUserBelongToSameShipParty(dropdowndata.offices, seafarer, keycloak);
      expect(userIsBelongToSameShipParty).toEqual(true);
    });

    it('should return false when manning agent does not belong to the same group', () => {
      const seafarer = {
        id: 11,
        office_id: 3,
      };
      const keycloak = {
        shipPartyId: 1721,
        shipPartyType: shipPartyType.MANNING_AGENT,
      };
      const userIsBelongToSameShipParty = checkUserBelongToSameShipParty(dropdowndata.offices, seafarer, keycloak);
      expect(userIsBelongToSameShipParty).toEqual(false);
    });
  });

  describe('filterByExcludedKeys', () => {
    it('should exclude specified keys from an object', () => {
      const obj = { a: 1, b: 2, c: 3 };
      const keysToExclude = ['b'];
      const result = filterByExcludedKeys(obj, keysToExclude);
      expect(result).toEqual({ a: 1, c: 3 });
    });
  });

  describe('filterByKeys', () => {
    it('should include only specified keys in an object', () => {
      const obj = { a: 1, b: 2, c: 3 };
      const keysToInclude = ['a', 'c'];
      const result = filterByKeys(obj, keysToInclude);
      expect(result).toEqual({ a: 1, c: 3 });
    });

    it('should return an empty object if no keys are specified', () => {
      const obj = { a: 1, b: 2, c: 3 };
      const keysToInclude: any = [];
      const result = filterByKeys(obj, keysToInclude);
      expect(result).toEqual({});
    });
  });

  describe('joinContacts', () => {
    it('should join contact strings with a comma', () => {
      const contacts = [{ contact: '123' }, { contact: '456' }];
      expect(joinContacts(contacts)).toEqual('123,456');
    });
  });

  describe('formatAmount', () => {
    it('should return formatted amount with default unit if none specified', () => {
      const amount = 1000;
      expect(formatAmount(amount, 'USD')).toEqual('USD 1000'); // Assuming USD as default currency unit
    });

    it('should return formatted amount with specified currency unit', () => {
      const amount = 1000;
      const unit = 'EUR';
      expect(formatAmount(amount, unit)).toEqual('EUR 1000');
    });
  });
});
