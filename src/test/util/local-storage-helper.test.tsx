import {
  setLocalStorage,
  getLocalStorage,
  isKeyStored,
  storeColumns,
  getStoredColumnHeaders,
  retrieveColumns,
  storePageNumber,
  storePageSize,
  storePageSort,
  resetAllTabs,
  getPageSize,
  getPageNumber,
  getPageSort,
  storeQuery,
  getQuery,
  genLocalStorageControlFuncs,
} from '../../util/local-storage-helper';

jest.mock('../../service/user-service');
jest.mock('../../styleGuide');

describe('Local Storage Functions', () => {
  beforeEach(() => {
    localStorage.clear();
  });

  test('setLocalStorage should set item in localStorage', () => {
    setLocalStorage('testKey', { test: 'value' });
    expect(localStorage.getItem('testKey')).toEqual(JSON.stringify({ test: 'value' }));
  });

  test('getLocalStorage should return parsed item from localStorage', () => {
    localStorage.setItem('testKey', JSON.stringify({ test: 'value' }));
    expect(getLocalStorage('testKey')).toEqual({ test: 'value' });
  });

  test('getLocalStorage should return undefined if item not present', () => {
    expect(getLocalStorage('nonExistentKey')).toBeUndefined();
  });

  test('isKeyStored should return true if sub_key exists', () => {
    setLocalStorage('testKey', { test: { sub_key: true } });
    expect(isKeyStored('testKey', 'test', 'sub_key')).toBe(true);
  });

  test('isKeyStored should return false if key is not present', () => {
    expect(isKeyStored('testKey', 'test', 'sub_key')).toBe(false);
  });

  test('storeColumns should store selected columns in localStorage', () => {
    const tab = 'testTab';
    const cols = [{ Header: 'Column1' }, { Header: 'Column2' }];
    storeColumns(tab, cols);
    expect(getLocalStorage(`${tab}-table-details`)).toEqual(undefined);
  });

  test('getStoredColumnHeaders should return stored column headers', () => {
    const tab = 'testTab';
    const cols = [{ Header: 'Column1' }, { Header: 'Column2' }];
    storeColumns(tab, cols);
    expect(getStoredColumnHeaders(tab)).toEqual(['Column1', 'Column2']);
  });

  test('retrieveColumns should return the correct columns based on tab', () => {
    const tab = 'contract-expiry';
    const cols = [];
    storeColumns(tab, cols);
    expect(retrieveColumns(tab)).toEqual(cols);
  });

  test('storePageNumber should store page number in localStorage', () => {
    const tab = 'testTab';
    storePageNumber(tab, 5);
    expect(getPageNumber(tab)).toBe(5);
  });

  test('storePageSize should store page size in localStorage', () => {
    const tab = 'testTab';
    storePageSize(tab, 20);
    expect(getPageSize(tab)).toBe(20);
  });

  test('storePageSort should store sorting data in localStorage', () => {
    const tab = 'testTab';
    const pageSort = [{ id: 'sort1', desc: true }];
    storePageSort(tab, pageSort);
    expect(getPageSort(tab)).toEqual(pageSort);
  });

  test('resetAllTabs should reset page number for all tabs', () => {
    const tab1 = 'tab1';
    const tab2 = 'tab2';
    storePageNumber(tab1, 3);
    storePageNumber(tab2, 7);
    resetAllTabs();
    expect(getPageNumber(tab1)).toBe(3);
    expect(getPageNumber(tab2)).toBe(7);
  });

  test('storeQuery should store query in localStorage', () => {
    const query = 'search=query';
    storeQuery(query);
    expect(getQuery()).toBe(query);
  });

  test('genLocalStorageControlFuncs should create functions to manage local storage', () => {
    const localStorageKeys = ['testKey'];
    const controlFuncs = genLocalStorageControlFuncs(localStorageKeys, 1);
    const { setLocalStorageItem, getLocalStorageItem } =
      controlFuncs;

    setLocalStorageItem('testKey', 'testValue');
    expect(getLocalStorageItem('testKey')).toBe('testValue');
  });

  test('genLocalStorageControlFuncs should throw error for invalid key', () => {
    const localStorageKeys = ['testKey'];
    const controlFuncs = genLocalStorageControlFuncs(localStorageKeys, 1);

    expect(() => controlFuncs.setLocalStorageItem('invalidKey', 'value')).toThrow(
      'key invalidKey is not allowed',
    );
    expect(() => controlFuncs.getLocalStorageItem('invalidKey')).toThrow(
      'key invalidKey is not allowed',
    );
  });
});
