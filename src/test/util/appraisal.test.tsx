import {
    showColorClass,
    getGradeColor,
    showPassFailColorClass,
    showGradeInText,
    renderToolTipText,
    getMasterAppaisalGradeColorClass,
    getAppaisalToolTipText,
    isSubmittedColor,
  } from '../../util/appraisal';
  
  describe('showColorClass', () => {
    it('should return "exceed-expectation" for averageScore >= 80', () => {
      expect(showColorClass(85)).toBe('exceed-expectation');
    });
  
    it('should return "meet-expectation" for averageScore between 60 and 79', () => {
      expect(showColorClass(70)).toBe('meet-expectation');
    });
  
    it('should return "below-expectation" for averageScore < 60', () => {
      expect(showColorClass(50)).toBe('below-expectation');
    });
  });
  
  describe('getGradeColor', () => {
    it('should return "font-green" for averageScore between 12 and 20', () => {
      expect(getGradeColor(15)).toBe('font-green');
    });
  
    it('should return "font-orange" for averageScore between 8 and 11', () => {
      expect(getGradeColor(10)).toBe('font-orange');
    });
  
    it('should return "font-red" for averageScore between 0 and 7', () => {
      expect(getGradeColor(5)).toBe('font-red');
    });
  });
  
  describe('showPassFailColorClass', () => {
    it('should return "font-green" if isSuccess is true', () => {
      expect(showPassFailColorClass(true)).toBe('font-green');
    });
  
    it('should return "font-red" if isSuccess is false', () => {
      expect(showPassFailColorClass(false)).toBe('font-red');
    });
  });
  
  describe('showGradeInText', () => {
    it('should return "Outstanding" for averageScore == 20', () => {
      expect(showGradeInText(20)).toBe('Outstanding');
    });
  
    it('should return "Very Good" for averageScore between 16 and 19', () => {
      expect(showGradeInText(18)).toBe('Very Good');
    });
  
    it('should return "Good" for averageScore between 12 and 15', () => {
      expect(showGradeInText(13)).toBe('Good');
    });
  
    it('should return "Fair" for averageScore between 8 and 11', () => {
      expect(showGradeInText(9)).toBe('Fair');
    });
  
    it('should return "Poor" for averageScore between 4 and 7', () => {
      expect(showGradeInText(6)).toBe('Poor');
    });
  
    it('should return "Very Poor" for averageScore between 0 and 3', () => {
      expect(showGradeInText(2)).toBe('Very Poor');
    });
  });
  
  describe('renderToolTipText', () => {
    it('should return "Exceed Expectation" for averageScore >= 80', () => {
      expect(renderToolTipText(85)).toBe('Exceed Expectation');
    });
  
    it('should return "Meet Expectation" for averageScore between 60 and 79', () => {
      expect(renderToolTipText(70)).toBe('Meet Expectation');
    });
  
    it('should return "Below Expectation" for averageScore < 60', () => {
      expect(renderToolTipText(50)).toBe('Below Expectation');
    });
  });
  
  describe('getMasterAppaisalGradeColorClass', () => {
    it('should return "exceed-expectation font-weight-bold" for grade >= 12', () => {
      expect(getMasterAppaisalGradeColorClass(15)).toBe('exceed-expectation font-weight-bold');
    });
  
    it('should return "meet-expectation font-weight-bold" for grade between 8 and 11', () => {
      expect(getMasterAppaisalGradeColorClass(10)).toBe('meet-expectation font-weight-bold');
    });
  
    it('should return "below-expectation font-weight-bold" for grade < 8', () => {
      expect(getMasterAppaisalGradeColorClass(5)).toBe('below-expectation font-weight-bold');
    });
  });
  
  describe('getAppaisalToolTipText', () => {
    it('should return the correct tooltip text', () => {
      expect(getAppaisalToolTipText()).toBe(
        'Green: Exceed Expectations\r\nOrange: Meets Expectations\r\nRed: Below Expectations'
      );
    });
  });
  
  describe('isSubmittedColor', () => {
    it('should return "exceed-expectation" for "done" or "pending_office_comment"', () => {
      expect(isSubmittedColor('done')).toBe('exceed-expectation');
      expect(isSubmittedColor('pending_office_comment')).toBe('exceed-expectation');
    });
  
    it('should return "below-expectation" for any other status', () => {
      expect(isSubmittedColor('other_status')).toBe('below-expectation');
    });
  });
  