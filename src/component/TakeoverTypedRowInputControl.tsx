import React, { useState } from 'react';
import { Col, Form, Button } from 'react-bootstrap';

import TakeoverDropDownControl from './TakeoverDropDownControl';
import TakeoverInputArrayControl from './TakeoverInputArrayControl';

import styleGuide from '../styleGuide';
const { Icon } = styleGuide;

const RemoveButton = ({ onRemoveItem }) => (
  <Icon icon="remove" size={30} className="remove_icon" onClick={onRemoveItem} />
);
const TakeoverTypedRowInputControl = (props) => {
  const {
    arrayId,
    dropDownName,
    textName,
    onInputArrayChange,
    onInputArrayRemoveRow,
    vessel,
    errors,
    options,
    errorText,
    label,
  } = props;

  const [minRow, setMinRow] = useState(1);

  const addNewRow = (event) => {
    const arrayData = vessel?.[arrayId] ?? [];
    setMinRow(arrayData.length + 1);
  };

  // No sense in moving up this
  // NOSONAR
  return (
    <>
      <Form.Row>
        <Form.Group as={Col} md={2}>
          <Form.Label>{label}</Form.Label>
        </Form.Group>
      </Form.Row>
      <TakeoverInputArrayControl
        onInputArrayChange={onInputArrayChange}
        onInputArrayRemoveRow={onInputArrayRemoveRow}
        vessel={vessel}
        arrayId={arrayId}
        minRow={minRow}
        onRow={({ rowId, rowData, onRowInputChange, onRemoveRow }) => {
          const onRemoveItem = (event) => {
            onRemoveRow(event);
            const arrayData = vessel?.[arrayId] ?? [];
            setMinRow(arrayData.length);
          };

          const errorExist = errors?.[arrayId][rowId];

          return (
            <Form.Row key={rowId}>
              <Form.Group as={Col} md={5}>
                <TakeoverDropDownControl
                  label=""
                  name={dropDownName}
                  vessel={rowData}
                  onInputChange={onRowInputChange}
                  errors={errors}
                  options={options}
                />
              </Form.Group>
              <Form.Group as={Col} md={5}>
                <Form.Control
                  type="text"
                  name={textName}
                  value={rowData[textName] || ''}
                  onChange={onRowInputChange}
                  isInvalid={!!errorExist}
                />
                <Form.Control.Feedback type="invalid">{errorText}</Form.Control.Feedback>
              </Form.Group>
              <Form.Group as={Col} md={2}>
                {rowId != 0 ? <RemoveButton onRemoveItem={onRemoveItem} /> : null}
              </Form.Group>
            </Form.Row>
          );
        }}
      />
      <Button variant="outline-primary" onClick={addNewRow}>
        Add
      </Button>
    </>
  );
};

export default TakeoverTypedRowInputControl;
