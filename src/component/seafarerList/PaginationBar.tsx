import React from 'react';
import { Form } from 'react-bootstrap';

const PageNum = ({ active, disabled, children, onClick }) => {
  const className = `page-num page-num-${active ? 'active' : 'inactive'} page-num-${
    disabled ? 'disabled' : 'enabled'
  }`;
  return (
    <div
      className={className}
      onClick={!disabled ? onClick : undefined}
      onKeyDown={(event) => {
        if ((event.key === 'Enter' || event.key === ' ') && !disabled) {
          event.preventDefault();
          onClick?.(event);
        }
      }}
      tabIndex={0}
    >
      {children}
    </div>
  );
};

const PaginationBar = ({
  className = '',
  visiblePages,
  pageSwitch,
  pageSizeSwitch,
  canPreviousPage,
  canNextPage,
  pageSize,
  pageIndex,
  pageSizeOptions = [10, 50, 100, 300],
}) => {
  return (
    <div className={`d-flex p-2 ${className}`}>
      <div className="page-number-border border-0">
        <PageNum onClick={() => pageSwitch(pageIndex - 1)} disabled={!canPreviousPage}>
          {'<'}
        </PageNum>
        {visiblePages?.filter(Boolean).map((page, index, array) => (
          <PageNum
            key={page}
            active={page - 1 === pageIndex}
            disabled={page - 1 === pageIndex}
            onClick={() => pageSwitch(page - 1)}
          >
            {array[index - 1] + 2 < page + 1 ? `...${page}` : page}
          </PageNum>
        ))}
        <PageNum onClick={() => pageSwitch(pageIndex + 1)} disabled={!canNextPage}>
          {'>'}
        </PageNum>
      </div>
      <Form inline>
        <Form.Control
          as="select"
          value={pageSize}
          className="ml-3"
          onChange={(e) => {
            pageSizeSwitch(Number(e.target.value));
          }}
        >
          {pageSizeOptions.map((pageSize) => (
            <option key={pageSize} value={pageSize}>
              Show {pageSize}
            </option>
          ))}
        </Form.Control>
      </Form>
    </div>
  );
};

export default PaginationBar;
