/* eslint-disable react/display-name */
/* eslint-disable react/prop-types */
import React from 'react';
import { Button, OverlayTrigger, Popover } from 'react-bootstrap';
import styleGuide from '../../styleGuide';
import Table from '../InfiniteScrollTable';

const { Icon } = styleGuide;
const { PARIS_ONE_HOST } = process.env;

const generateColumns = (selectedColumns, roleConfig, eventTracker, visitUpdateSeafarer) => {
  return [
    {
      Header: 'No.',
      id: 'id',
      type: 'item',
      accessor: (row, index) => {
        return <span data-testid={`fml-seafarer-table-sr-no-${index}`}>{row.id ?? '---'}</span>;
      },
      sortType: (a, b) => {
        if (a && b) {
          const aName = a.id ? a.id.toLowerCase() : '';
          const bName = b.id ? b.id.toLowerCase() : '';
          if (aName < bName) return -1;
          if (aName > bName) return 1;
        }
        return 0;
      },
      maxWidth: 90,
      sticky: window.innerWidth > 960 ? 'left' : null,
    },
    {
      Header: 'PARIS 1.0',
      id: 'paris1',
      type: 'item',
      accessor: (row) => (
        <Button
          variant="link"
          onClick={(e) => {
            e.stopPropagation();
          }}
        >
          <a
            target="_blank"
            href={getParis1Link(roleConfig.shipPartyId, row.ref_id)}
            type="absolute"
            rel="noreferrer"
          >
            PARIS 1.0
          </a>
        </Button>
      ),
      maxWidth: 90,
      sticky: window.innerWidth > 960 ? 'left' : null,
      disableSortBy: true,
    },
    ...selectedColumns,
    {
      Header() {
        return <div className="text-center">Actions</div>;
      },
      id: 'actions',
      accessor: (row) => (
        <Button
          variant="link"
          style={{
            textAlign: 'center',
            cursor: roleConfig.seafarer.editSeafarer ? 'auto' : 'none',
            width: '100%',
          }}
          onClick={(e) => {
            e.stopPropagation();
          }}
        >
          <OverlayTrigger
            rootClose
            trigger={roleConfig.seafarer.editSeafarer ? 'click' : []}
            onToggle={() => eventTracker('actions', 'Actions')}
            key="bottom"
            placement="bottom"
            overlay={
              <Popover>
                <Popover.Content className="text-primary">
                  <ul className="List__PopoverMenu">
                    <li
                      onClick={visitUpdateSeafarer.bind(
                        this,
                        row.id,
                        row.ref_id,
                        row.seafarer_person,
                      )}
                      onKeyDown={(event) => {
                        if (event.key === 'Enter' || event.key === ' ') {
                          visitUpdateSeafarer.bind(this, row.id, row.ref_id, row.seafarer_person)();
                        }
                      }}
                      tabIndex={0}
                    >
                      Edit
                    </li>
                  </ul>
                </Popover.Content>
              </Popover>
            }
          >
            <div>
              {' '}
              <Icon icon="more" size={20} className="default" style={{ cursor: 'pointer' }} />
            </div>
          </OverlayTrigger>
        </Button>
      ),
      disableSortBy: true,
      maxWidth: 80,
      sticky: window.innerWidth > 960 ? 'right' : null,
    },
  ];
};

const getParis1Link = (shipPartyId, refId) => {
  const link = shipPartyId ? '/FMLLoginKeycloak?targeturl=/fml' : '';
  return `${PARIS_ONE_HOST}/fml${link}/PARIS?display=crewoverview&crewid=${refId}`;
};

const SeafarerTable = React.memo(
  ({
    tabName,
    seafarers,
    fetchData,
    visitSeafarer,
    eventTracker,
    visitUpdateSeafarer,
    selectedColumns,
    loading,
    roleConfig,
    quickSearchParams,
    advancedSearchParams,
    init_sort,
    hasMoreData,
  }) => {
    const columns = generateColumns(selectedColumns, roleConfig, eventTracker, visitUpdateSeafarer);

    return (
      <div className="seafarer-table" data-testid="seafarer-table">
        <Table
          tabName={tabName}
          columns={columns}
          data={seafarers}
          fetchData={fetchData}
          eventTracker={eventTracker}
          visitSeafarer={visitSeafarer}
          loading={loading}
          quickSearchParams={quickSearchParams}
          advancedSearchParams={advancedSearchParams}
          init_sort={init_sort}
          hasMoreData={hasMoreData}
        />
      </div>
    );
  },
);

export default SeafarerTable;
