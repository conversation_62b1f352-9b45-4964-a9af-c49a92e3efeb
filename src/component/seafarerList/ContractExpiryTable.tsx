/* eslint-disable react/display-name */
/* eslint-disable react/prop-types */
import React from 'react';

import ReplaceButton from '../CrewList/ReplacementButton';

import { capitalizeArgs } from '../../model/utils';
import parentHKIDLink from '../common/HKIDLink';
import ReplaceWith from '../CrewList/ReplaceWith';
import CrewListLink from '../CrewList/CrewListLink';
import VesselNameLink from '../CrewList/VesselNameLink';

import Table from '../InfiniteScrollTable';

const generateColumns = (selectedColumns, parentHKIDLink, getFullName) => {
  return [
    {
      Header: 'Sign off',
      id: 'sign-off',
      columns: [
        {
          Header: 'No.',
          id: 'id',
          type: 'item',
          accessor: (row) => {
            return row.id ?? '---';
          },
          sortType: (a, b) => {
            if (a && b) {
              const aName = a.id ? a.id.toLowerCase() : '';
              const bName = b.id ? b.id.toLowerCase() : '';
              if (aName < bName) return -1;
              if (aName > bName) return 1;
            }
            return 0;
          },
          minWidth: 90,
          sticky: window.innerWidth > 960 ? 'left' : null,
        },
        ...selectedColumns.filter((i) => ['Vessel', 'HKID', 'Name'].includes(i.Header)),
      ],
      sticky: window.innerWidth > 960 ? 'left' : null,
    },
    {
      Header: '',
      id: 'empty',
      columns: [...selectedColumns.filter((i) => !['Vessel', 'HKID', 'Name'].includes(i.Header))],
    },

    {
      Header: 'Replacement',
      id: 'replacement',
      type: 'item',
      columns: [
        {
          Header: 'HKID',
          id: 'replacement-hkid',
          accessor: (row) => {
            return (
              <div>
                <ReplaceWith
                  replaced_by={row?.seafarer_person?.seafarer_status_history[0]?.replaced_by}
                  type="hkid"
                />
              </div>
            );
          },
          disableSortBy: true,
          minWidth: 30,
        },
        {
          Header: 'Name',
          id: 'replacement-name',
          accessor: function replacementName(row) {
            return (
              <div>
                {' '}
                <ReplaceWith
                  replaced_by={row?.seafarer_person?.seafarer_status_history[0]?.replaced_by}
                  type="name"
                />
              </div>
            );
          },
          disableSortBy: true,
          minWidth: 200,
        },
        {
          Header: 'Nationality',
          id: 'replacement-nationality',
          accessor: (row) => {
            return (
              <div>
                {' '}
                <ReplaceWith
                  replaced_by={row?.seafarer_person?.seafarer_status_history[0]?.replaced_by}
                  type="nationality"
                />
              </div>
            );
          },
          disableSortBy: true,
          minWidth: 50,
        },
      ],
    },
    {
      Header: 'Recommended',
      id: 'recommended',
      type: 'item',
      columns: [
        {
          Header: 'HKID',
          id: 'recommended-hkid',
          accessor: (row) => {
            return row?.recommended_replacement?.map((item, index) => {
              return <div key={item.id}>{parentHKIDLink(item)}</div>;
            });
          },
          disableSortBy: true,
          width: 120,
        },
        {
          Header: 'Name',
          id: 'recommended-name',
          accessor: (row) => {
            return row?.recommended_replacement?.map((item) => {
              return <div key={item.id}>{getFullName(item?.seafarer_person)}</div>;
            });
          },
          minWidth: 200,
          disableSortBy: true,
        },
        {
          Header: 'Nationality',
          id: 'recommended-nationality',
          accessor: (row) => {
            return row?.recommended_replacement?.map((item) => {
              return <div key={item.id}>{item?.seafarer_person?.nationality?.value}</div>;
            });
          },
          width: 120,
          disableSortBy: true,
        },
      ],
    },
  ];
};

const ContractExpiryTable = React.memo(
  ({
    tabName,
    seafarers,
    fetchData,
    visitSeafarer,
    eventTracker,
    selectedColumns,
    loading,
    quickSearchParams,
    advancedSearchParams,
    init_sort,
    hasMoreData,
  }) => {
    const getFullName = (person) => {
      const firstName = person?.first_name ?? '';
      const lastName = person?.last_name ?? '';
      const middleName = person?.middle_name ?? '';
      const fullName = capitalizeArgs(firstName, middleName, lastName) || '---';
      return (
        <div>
          <b>{fullName} </b>
        </div>
      );
    };

    selectedColumns.forEach((e) => {
      if (e.id === 'replacement') {
        e.accessor = (row) => {
          return (
            <div>
              {row?.id ? <ReplaceButton seafarerId={row.id} eventTracker={eventTracker} /> : '---'}
            </div>
          );
        };
      } else if (e.id === 'crew_list_link') {
        e.accessor = (row) => {
          return (
            <CrewListLink
              vesselId={row?.seafarer_person?.seafarer_status_history[0]?.vessel_id}
              eventTracker={eventTracker}
            />
          );
        };
      } else if (e.id === 'hkid') {
        e.accessor = (row) => parentHKIDLink(row, eventTracker);
      } else if (e.id === 'vessel_name') {
        e.accessor = (row) => {
          return (
            <VesselNameLink
              ownershipId={row?.seafarer_person?.seafarer_status_history[0]?.vessel_ownership_id}
              vesselName={row?.seafarer_person?.seafarer_status_history[0]?.vessel_name}
              eventTracker={eventTracker}
            />
          );
        };
      } else {
        return e;
      }
    });

    let columns = generateColumns(selectedColumns, parentHKIDLink, getFullName);

    if (loading) {
      const removeOnLoad = ['No.'];
      columns = columns.filter((col_obj) => {
        if (col_obj.Header && removeOnLoad.includes(col_obj.Header)) return false;
        return true;
      });
    }
    return (
      <div className="seafarer-table">
        <Table
          tableClassName="contract-expiry-list-table"
          tabName={tabName}
          columns={columns}
          data={seafarers}
          fetchData={fetchData}
          eventTracker={eventTracker}
          visitSeafarer={visitSeafarer}
          loading={loading}
          quickSearchParams={quickSearchParams}
          advancedSearchParams={advancedSearchParams}
          init_sort={init_sort}
          hasMoreData={hasMoreData}
        />
      </div>
    );
  },
);

export default ContractExpiryTable;
