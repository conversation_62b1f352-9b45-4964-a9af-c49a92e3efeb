import React from 'react';
import moment from 'moment';
import { <PERSON><PERSON>, OverlayTrigger, Tooltip } from 'react-bootstrap';
import { useHistory } from 'react-router-dom';
import { seafarerStatusService } from 'paris2-seafarer-status';
import seafarerService from '../../service/seafarer-service';
import Spinner from '../common/Spinner';
import {
  dataQuality,
  screeningForColumnMap,
  screeningStatusColumnLabel,
  screeningStatus,
} from '../../model/constants';
import { valueOrDash } from '../../model/utils';
import { formatYesNo, getSeafarerStatus } from '../../util/commonUtil';


const accountStatusJson = seafarerStatusService.getAccountStatus();
const journeyStatusJson = seafarerStatusService.getJourneyStatus();
const examStatusJson = seafarerStatusService.getExamStatus();

function highlightText(cellValue, searchedKeyword = '') {
  cellValue = cellValue.toString();
  if (searchedKeyword === '') return cellValue;

  const index = cellValue.toLowerCase().indexOf(searchedKeyword);
  if (index !== -1) {
    return (
      <>
        {cellValue.substring(0, index)}
        <span className="search-text-highlight">
          {cellValue.substring(index, index + searchedKeyword.length)}
        </span>
        {cellValue.substring(index + searchedKeyword.length)}
      </>
    );
  }
  return cellValue;
}

function addCircle(seafarer) {
  if (!seafarer.data_quality) {
    return <Spinner alignClass="justify-content-flex-start" />;
  }
  let className = '';
  let tooltipText = '';

  if (seafarer.data_quality === dataQuality.MANDATORY_DATA_MISSING) {
    className = 'red dot';
    tooltipText = 'Missing field(s) in mandatory data';
  }
  if (seafarer.data_quality === dataQuality.DATA_INVALID) {
    className = 'orange dot';
    tooltipText = 'Format-error in mandatory field(s)';
  }
  if (seafarer.data_quality === dataQuality.CLEAN) {
    className = 'green dot';
  }
  return seafarer.data_quality === dataQuality.CLEAN ? (
    <span className={className} />
  ) : (
    <OverlayTrigger
      placement="bottom"
      className="overlay-trigger"
      overlay={<Tooltip className="quality-tooltip">{tooltipText}</Tooltip>}
    >
      <span className={className} />
    </OverlayTrigger>
  );
}

function parentHKIDLink(row) {
  const ButtonLink = ({ row }) => {
    const history = useHistory();

    const getSeafarerData = async () => {
      try {
        const activeKey = '';
        const queryParams = `hkid=${row.parent_hkid}`;
        const { data } = await seafarerService.getSeafarers(activeKey, queryParams, false);
        if (data?.results?.length == 1) {
          const seafarerId = data.results[0].id;
          history.push(`/seafarer/details/${seafarerId}/general`);
        } else {
          throw new Error(`Could not find seafarer with hkid: ${row.parent_hkid}`);
        }
      } catch (error) {
        console.log(`Error: ${error}`);
      }
    };

    const clickHandler = (event) => {
      event.stopPropagation();
      getSeafarerData();
    };

    if (row.parent_hkid) {
      return (
        <Button className="button-link" variant="link" onClick={clickHandler}>
          {row.parent_hkid}
        </Button>
      );
    }
    return '---';
  };

  return <ButtonLink row={row} />;
}

function getScreeningLabel(value) {
  const label = screeningStatusColumnLabel[value];
  if (label) {
    return label;
  }
  return '---';
}

const items = [
  {
    type: 'header',
    Header: 'BASIC',
  },
  {
    type: 'item',
    Header: 'Quality',
    id: 'data_quality',
    name: 'data_quality',
    accessor: (row) => addCircle(row),
    width: 50,
    order: 0,
  },
  {
    type: 'item',
    Header: 'HKID',
    id: 'hkid',
    name: 'hkid',
    accessor: (row) => (
      <span data-testid={`fml-seafarer-general-details-${row?.hkid}`}>
        {row.hkid ? highlightText(row.hkid, row.keyword) : '---'}
      </span>
    ),
    width: 100,
    order: 1,
    isLongServiceExportField: true,
  },
  {
    type: 'text',
    Header: 'Name',
    id: 'seafarer_person.first_name',
    name: 'seafarer_person.first_name',
    accessor: (row) => (
      <b>
        {row.seafarer_person && (row.seafarer_person.first_name || row.seafarer_person.last_name)
          ? highlightText(
              (row.seafarer_person?.first_name ?? '') +
                ' ' +
                (row.seafarer_person?.last_name ?? ''),
              row.keyword,
            )
          : '---'}
      </b>
    ),
    width: 250,
    order: 2,
    isLongServiceExportField: true,
  },
  {
    type: 'text',
    Header: 'Rank',
    id: 'seafarer_rank.value',
    name: 'seafarer_rank.value',
    accessor: (row) => valueOrDash(row, ['seafarer_rank', 'unit']),
    width: 100,
    order: 3,
    isLongServiceExportField: true,
  },
  {
    type: 'item',
    Header: 'Main Profile HKID',
    id: 'parent_hkid',
    name: 'parent_hkid',
    accessor: (row) => parentHKIDLink(row),
    width: 100,
    order: 0,
  },
  {
    type: 'text',
    Header: 'Nationality',
    id: 'seafarer_person:nationality.value',
    name: 'seafarer_person.nationality.value',
    accessor: (row) => valueOrDash(row.seafarer_person, ['nationality', 'value']),
    width: 130,
    order: 4,
    isLongServiceExportField: true,
  },
  {
    type: 'text',
    Header: 'Reporting Office',
    id: 'seafarer_reporting_office.value',
    name: 'seafarer_reporting_office.value',
    accessor: (row) => valueOrDash(row, ['seafarer_reporting_office', 'value']),
    width: 180,
    order: 5,
  },
  {
    type: 'date',
    Header: 'Date of Birth',
    id: 'seafarer_person.date_of_birth',
    name: 'seafarer_person.date_of_birth',
    accessor: (row) =>
      row.seafarer_person.date_of_birth
        ? moment(row.seafarer_person.date_of_birth).format('DD MMM YYYY')
        : '---',
    width: 150,
    order: 6,
  },
  {
    type: 'date',
    Header: 'Availability Date',
    id: 'seafarer_contact_log.availability_date',
    name: 'seafarer_contact_log[0].availability_date',
    accessor: (row) =>
      row.seafarer_contact_log[0]?.availability_date
        ? moment(row.seafarer_contact_log[0]?.availability_date).format('DD MMM YYYY')
        : '---',
    sortType: (a, b) => {
      if (
        a.seafarer_contact_log[0]?.availability_date &&
        b.seafarer_contact_log[0]?.availability_date
      ) {
        const aDate = a.seafarer_contact_log[0]?.availability_date
          ? moment(a.seafarer_contact_log[0]?.availability_date).valueOf()
          : '';
        const bDate = b.seafarer_contact_log[0]?.availability_date
          ? moment(b.seafarer_contact_log[0]?.availability_date).valueOf()
          : '';
        if (aDate < bDate) return -1;
        if (aDate > bDate) return 1;
      }
      return 0;
    },
    width: 170,
    order: 7,
  },
  {
    type: 'text',
    Header: 'Availability Remark',
    id: 'seafarer_contact_log.availability_remarks',
    name: 'seafarer_contact_log[0].availability_remarks',
    accessor: (row) => valueOrDash(row.seafarer_contact_log?.[0], ['availability_remarks']),
    width: 250,
    order: 8,
  },
  {
    type: 'text',
    Header: 'Account Status',
    id: 'seafarer_person.current_account_status',
    name: 'seafarer_person.current_account_status',
    accessor: (row) =>
      getSeafarerStatus(accountStatusJson, row.seafarer_person.current_account_status),
    width: 180,
    order: 9,
  },
  {
    type: 'text',
    Header: 'Journey Status',
    id: 'seafarer_person.current_journey_status',
    name: 'seafarer_person.current_journey_status',
    accessor: (row) =>
      getSeafarerStatus(journeyStatusJson, row.seafarer_person.current_journey_status),
    width: 180,
    order: 10,
  },
  {
    type: 'text',
    Header: 'Examination',
    id: 'seafarer_person.current_exam_status',
    name: 'seafarer_person.current_exam_status',
    accessor: (row) =>
      getSeafarerStatus(examStatusJson, row.seafarer_person.current_exam_status),
    width: 180,
    order: 11,
  },
  {
    type: 'text',
    Header: 'Screening',
    id: 'seafarer_person.screening_status',
    name: 'seafarer_person.screening_status',
    accessor: (row) => getScreeningLabel(row.seafarer_person.screening_status),
    width: 180,
    order: 12,
  },
  {
    type: 'text',
    Header: 'Country',
    id: 'seafarer_person:addresses:country.value',
    name: 'seafarer_person:addresses:country.value',
    accessor: (row) =>
      row.seafarer_person.addresses.length > 0
        ? row.seafarer_person.addresses
            .filter((i) => i.country)
            .map((i) => i.country.value)
            .join(', ')
        : '---',
    width: 150,
    order: 12,
  },
  {
    type: 'text',
    Header: 'Village / Town / City',
    id: 'seafarer_person:addresses.city',
    name: 'seafarer_person:addresses.city',
    accessor: (row) =>
      row.seafarer_person.addresses.length > 0
        ? row.seafarer_person.addresses
            .filter((i) => i.city)
            .map((i) => i.city)
            .join(', ')
        : '---',
    width: 200,
    order: 13,
  },
  {
    type: 'date',
    Header: 'Created Date',
    id: 'seafarer_person.created_at',
    name: 'seafarer_person.created_at',
    accessor: (row) =>
      row.seafarer_person.created_at
        ? moment(row.seafarer_person.created_at).format('DD MMM YYYY')
        : '---',
    width: 150,
    order: 14,
  },
  {
    type: 'text',
    Header: 'Created By',
    id: 'seafarer_person.created_by_username',
    name: 'seafarer_person.created_by_username',
    accessor: (row) => {
      if (row.seafarer_person.created_by) {
        return row.seafarer_person.created_by;
      } else if (row.seafarer_person.created_by === '') {
        return '---';
      }
      if (row.seafarer_person.created_by_hash) {
        return <Spinner alignClass="justify-content-flex-start" />;
      }
      return '---';
    },
    width: 180,
    order: 15,
  },
  {
    type: 'text',
    Header: 'Gender',
    id: 'seafarer_person.gender',
    name: 'seafarer_person.gender',
    accessor: (row) => valueOrDash(row.seafarer_person, ['gender']),
    width: 120,
    order: 16,
  },
  {
    type: 'text',
    Header: 'Place Of Birth',
    id: 'seafarer_person.place_of_birth',
    name: 'seafarer_person.place_of_birth',
    accessor: (row) => valueOrDash(row.seafarer_person, ['place_of_birth']),
    width: 180,
    order: 17,
  },
  {
    type: 'text',
    Header: 'Country Of Birth',
    id: 'seafarer_person:country_of_birth.value',
    name: 'seafarer_person.country_of_birth.value',
    accessor: (row) => valueOrDash(row.seafarer_person, ['country_of_birth', 'value']),
    width: 180,
    order: 18,
  },
  {
    type: 'text',
    Header: 'Passport Number',
    id: 'seafarer_person:passports.number',
    name: 'seafarer_person.passports',
    handleExportOptionalFn: (passports) => {
      return (
        passports
          ?.filter((i) => i?.number)
          .map((i) => i?.number)
          .join(', ') ?? ''
      );
    },
    accessor: (row) =>
      row.seafarer_person.passports.length > 0
        ? row.seafarer_person.passports
            .filter((i) => i.number)
            .map((i) => i.number)
            .join(', ')
        : '---',
    width: 180,
    order: 19,
  },
  {
    type: 'text',
    Header: "Seaman's Book Number",
    id: 'seafarer_person:seaman_books.number',
    name: 'seafarer_person.seaman_books',
    handleExportOptionalFn: (seamanBooks) => {
      return (
        seamanBooks
          ?.filter((i) => i?.number)
          .map((i) => i?.number)
          .join(', ') ?? ''
      );
    },
    accessor: (row) =>
      row.seafarer_person.seaman_books.length > 0
        ? row.seafarer_person.seaman_books
            .filter((i) => i.number)
            .map((i) => i.number)
            .join(', ')
        : '---',
    width: 240,
    order: 20,
  },
  {
    type: 'text',
    Header: 'Framo Experience',
    id: 'framo_experience',
    name: 'framo_experience',
    handleExportOptionalFn: (item: boolean) => formatYesNo(item),
    accessor: (row) => formatYesNo(row?.framo_experience),
    width: 120,
    order: 21,
  },
  {
    type: 'item',
    Header: 'Cargo Handling Experience',
    id: 'cargo_experience',
    name: 'cargo_experience',
    accessor: (row) => valueOrDash(row, ['cargo_experience']),
    width: 250,
    order: 22,
  },
  {
    type: 'text',
    Header: 'Additional Experience',
    id: 'additional_experience',
    name: 'additional_experience',
    accessor: (row) => valueOrDash(row, ['additional_experience']),
    width: 250,
    order: 23,
  },
  {
    type: 'text',
    Header: 'NTBE',
    id: 'not_to_be_employed',
    name: 'not_to_be_employed',
    handleExportOptionalFn: (item) => formatYesNo(item),
    accessor: (row) => formatYesNo(row?.not_to_be_employed),
    width: 120,
    order: 25,
  },
  {
    type: 'text',
    Header: 'NTBE Reason',
    id: 'not_to_be_employed_reason',
    name: 'not_to_be_employed_reason',
    accessor: (row) => valueOrDash(row, ['not_to_be_employed_reason']),
    width: 250,
    order: 26,
  },
  {
    type: 'text',
    Header: 'Nearest Airport',
    id: 'seafarer_person.nearest_airport',
    name: 'seafarer_person.nearest_airport',
    accessor: (row) => valueOrDash(row.seafarer_person, ['nearest_airport']),
    width: 180,
    order: 27,
  },
  {
    type: 'item',
    Header: 'Height (cm)',
    id: 'seafarer_person.height',
    name: 'seafarer_person.height',
    accessor: (row) => valueOrDash(row.seafarer_person, ['height']),
    width: 100,
    order: 28,
  },
  {
    type: 'item',
    Header: 'Weight (kg)',
    id: 'seafarer_person.weight',
    name: 'seafarer_person.weight',
    accessor: (row) => valueOrDash(row.seafarer_person, ['weight']),
    width: 100,
    order: 29,
  },
  {
    type: 'item',
    Header: 'Overall Size',
    id: 'seafarer_person.overall_size',
    name: 'seafarer_person.overall_size',
    accessor: (row) => valueOrDash(row.seafarer_person, ['overall_size']),
    width: 100,
    order: 30,
  },
  {
    type: 'item',
    Header: 'T-shirt Size',
    id: 'seafarer_person.tshirt_size',
    name: 'seafarer_person.tshirt_size',
    accessor: (row) => valueOrDash(row.seafarer_person, ['tshirt_size']),
    width: 100,
    order: 31,
  },
  {
    type: 'item',
    Header: 'Jacket Size',
    id: 'seafarer_person.jacket_size',
    name: 'seafarer_person.jacket_size',
    accessor: (row) => valueOrDash(row.seafarer_person, ['jacket_size']),
    width: 100,
    order: 32,
  },
  {
    type: 'item',
    Header: 'Shoe Size (USA)',
    id: 'seafarer_person.shoe_size',
    name: 'seafarer_person.shoe_size',
    accessor: (row) => valueOrDash(row.seafarer_person, ['shoe_size']),
    width: 100,
    order: 33,
  },
  {
    type: 'item',
    Header: 'Smoking',
    id: 'seafarer_person.smoking',
    name: 'seafarer_person.smoking',
    accessor: (row) => formatYesNo(row?.seafarer_person?.smoking),
    width: 100,
    order: 33,
  },
  {
    type: 'item',
    Header: 'Vegetarian',
    id: 'seafarer_person.vegetarian',
    name: 'seafarer_person.vegetarian',
    accessor: (row) => formatYesNo(row?.seafarer_person?.vegetarian),
    width: 100,
    order: 34,
  },
  {
    type: 'date',
    Header: 'Latest contact date',
    id: 'seafarer_contact_log.contact_date',
    name: 'seafarer_contact_log[0].contact_date',
    accessor: (row) =>
      row.seafarer_contact_log[0]?.contact_date
        ? moment(row.seafarer_contact_log[0]?.contact_date).format('DD MMM YYYY')
        : '---',
    sortType: (a, b) => {
      if (a.seafarer_contact_log[0]?.contact_date && b.seafarer_contact_log[0]?.contact_date) {
        const aDate = a.seafarer_contact_log[0]?.contact_date
          ? moment(a.seafarer_contact_log[0]?.contact_date).valueOf()
          : '';
        const bDate = b.seafarer_contact_log[0]?.contact_date
          ? moment(b.seafarer_contact_log[0]?.contact_date).valueOf()
          : '';
        if (aDate < bDate) return -1;
        if (aDate > bDate) return 1;
      }
      return 0;
    },
    width: 170,
    order: 35,
  },
  {
    type: 'date',
    Header: 'Next contact date',
    id: 'seafarer_contact_log.next_contact_date',
    name: 'seafarer_contact_log[0].next_contact_date',
    accessor: (row) =>
      row.seafarer_contact_log[0]?.next_contact_date
        ? moment(row.seafarer_contact_log[0]?.next_contact_date).format('DD MMM YYYY')
        : '---',
    sortType: (a, b) => {
      if (
        a.seafarer_contact_log[0]?.next_contact_date &&
        b.seafarer_contact_log[0]?.next_contact_date
      ) {
        const aDate = a.seafarer_contact_log[0]?.next_contact_date
          ? moment(a.seafarer_contact_log[0]?.next_contact_date).valueOf()
          : '';
        const bDate = b.seafarer_contact_log[0]?.next_contact_date
          ? moment(b.seafarer_contact_log[0]?.next_contact_date).valueOf()
          : '';
        if (aDate < bDate) return -1;
        if (aDate > bDate) return 1;
      }
      return 0;
    },
    width: 170,
    order: 36,
  },
  {
    type: 'item',
    Header: 'Documents in hand',
    id: 'seafarer_contact_log.docs_in_hand',
    name: 'seafarer_contact_log[0].docs_in_hand',
    handleExportOptionalFn: (document) => {
      let documentStatus;
      if (document === true) {
        documentStatus = 'Yes';
      } else if (document === false) {
        documentStatus = 'No';
      } else {
        documentStatus = '---';
      }
      return documentStatus;
    },
    accessor: (row) =>
      row.seafarer_contact_log[0]?.docs_in_hand ? 'Yes' : 'No',
    width: 120,
    order: 37,
  },
];

export const getScreenForColumnValue = (value) => {
  switch (value) {
    case screeningForColumnMap.DATA_EDIT:
      return 'Data Edit';
    case screeningForColumnMap.NEW_APPLICANT:
      return 'New Applicant';
    case screeningForColumnMap.VESSEL_RECOMMENDATION:
      return 'Vessel Recommendation';
    default:
      return '';
  }
};

const defaultRejectedScreeningColumns = [
  {
    type: 'date',
    Header: 'Screening submitted date & time',
    id: 'seafarer_person:seafarer_screening.created_at',
    name: 'seafarer_person.seafarer_screening.created_at',
    accessor: (row) =>
      row.seafarer_person?.seafarer_screening?.created_at
        ? moment(row.seafarer_person.seafarer_screening.created_at).format('DD MMM YYYY')
        : '---',
    width: 120,
    order: 4,
  },
  {
    type: 'item',
    Header: 'Approval group',
    id: 'seafarer_person:seafarer_screening.approval_group',
    name: 'seafarer_person.seafarer_screening.approval_group',
    accessor: (row) => {
      return row?.seafarer_person?.seafarer_screening?.approval_group ?? '---';
    },
    width: 120,
    order: 4.1,
  },
  {
    type: 'item',
    Header: 'Screening for',
    id: 'seafarer_person:seafarer_screening.screening_for',
    name: 'seafarer_person.seafarer_screening.screening_for',
    accessor: 'seafarer_person.seafarer_screening.screening_for',
    Cell: (cell) => {
      return <span>{getScreenForColumnValue(cell.value)}</span>;
    },
    width: 120,
    order: 4.2,
  },
];

const defaultUnderScreeningColumns = [
  {
    type: 'date',
    Header: 'Screening submitted date & time',
    id: 'seafarer_person:seafarer_screening.created_at',
    name: 'seafarer_person.seafarer_screening.created_at',
    accessor: (row) =>
      row.seafarer_person?.seafarer_screening?.created_at
        ? moment(row.seafarer_person.seafarer_screening.created_at).format('DD MMM YYYY')
        : '---',
    width: 120,
    order: 4,
  },
  {
    type: 'item',
    Header: 'Pending approval group',
    id: 'seafarer_person:seafarer_screening.approval_group',
    name: 'seafarer_person.seafarer_screening.approval_group',
    accessor: (row) => row.seafarer_person?.seafarer_screening?.approval_group ?? '---',
    width: 120,
    order: 4.1,
  },
  {
    type: 'item',
    Header: 'Screening for',
    id: 'seafarer_person:seafarer_screening.screening_for',
    name: 'seafarer_person.seafarer_screening.screening_for',
    accessor: 'seafarer_person.seafarer_screening.screening_for',
    Cell: (cell) => {
      return <span>{getScreenForColumnValue(cell.value)}</span>;
    },
    width: 120,
    order: 4.2,
  },
];

export const getDefaultScreeningColumns = (activeKey) => {
  if (activeKey === screeningStatus.REJECTED) {
    return defaultRejectedScreeningColumns;
  }
  if (activeKey === screeningStatus.UNDER_SCREENING) {
    return defaultUnderScreeningColumns;
  }
  return [];
};

export default items;
