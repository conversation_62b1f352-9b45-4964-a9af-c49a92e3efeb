/* eslint-disable react/display-name */
/* eslint-disable react/prop-types */
import React, { useMemo } from 'react';
import { useTable, useSortBy, usePagination, useFlexLayout } from 'react-table';
import { v4 as uuid } from 'uuid';
import Spinner from '../common/Spinner';
import styleGuide from '../../styleGuide';
const { Icon } = styleGuide;
import { capitalizeArgs } from '../../model/utils';
import parentHKIDLink from '../common/HKIDLink';
import ReplaceWith from '../../component/CrewList/ReplaceWith';
import { columnSortEventName, columnSortIconName } from '../../util/view-utils';

const generateColumns = (selectedColumns, parentHKIDLink, getFullName) => {
  return [
    {
      Header: 'Sign off',
      id: 'sign-off',
      columns: [
        {
          Header: 'No.',
          id: 'id',
          type: 'item',
          accessor: (row) => {
            return row.id ?? '---';
          },
          sortType: (a, b) => {
            if (a && b) {
              const aName = a.id ? a.id.toLowerCase() : '';
              const bName = b.id ? b.id.toLowerCase() : '';
              if (aName < bName) return -1;
              if (aName > bName) return 1;
            }
            return 0;
          },
          minWidth: 80,
        },
        ...selectedColumns.filter((i) => ['Vessel', 'HKID', 'Name'].includes(i.Header)),
      ],
    },
    {
      Header: '',
      id: 'empty',
      columns: [...selectedColumns.filter((i) => !['Vessel', 'HKID', 'Name'].includes(i.Header))],
    },
    {
      Header: 'Replacement',
      id: 'replacement',
      type: 'item',
      columns: [
        {
          Header: 'HKID',
          id: 'replacement-hkid',
          accessor: (row) => {
            return (
              <div>
                <ReplaceWith
                  replaced_by={row?.seafarer_person?.seafarer_status_history[0]?.replaced_by}
                  type="hkid"
                />
              </div>
            );
          },
          disableSortBy: true,
          minWidth: 80,
        },
        {
          Header: 'Name',
          id: 'replacement-name',
          accessor: function replacementName(row) {
            return (
              <div>
                {' '}
                <ReplaceWith
                  replaced_by={row?.seafarer_person?.seafarer_status_history[0]?.replaced_by}
                  type="name"
                />
              </div>
            );
          },
          disableSortBy: true,
          minWidth: 200,
        },
        {
          Header: 'Nationality',
          id: 'replacement-nationality',
          accessor: (row) => {
            return (
              <div>
                <ReplaceWith
                  replaced_by={row?.seafarer_person?.seafarer_status_history[0]?.replaced_by}
                  type="nationality"
                />
              </div>
            );
          },
          disableSortBy: true,
          minWidth: 120,
        },
      ],
    },
    {
      Header: 'Recommended',
      id: 'recommended',
      type: 'item',
      columns: [
        {
          Header: 'HKID',
          id: 'singOff-recommended-hkid',
          accessor: (row) => {
            const nationality = row?.recommended_replacement?.map((item) => {
              return <div key={item.id}>{parentHKIDLink(item)}</div>;
            });
            return nationality;
          },
          disableSortBy: true,
          minWidth: 80,
        },
        {
          Header: 'Name',
          id: 'singOff-recommended-name',
          accessor: (row) => {
            const nationality = row?.recommended_replacement?.map((item) => {
              return <div key={item.id}>{getFullName(item?.seafarer_person)}</div>;
            });
            return nationality;
          },
          disableSortBy: true,
          minWidth: 200,
        },
        {
          Header: 'Nationality',
          id: 'singOff-recommended-nationality',
          accessor: (row) => {
            const nationality = row?.recommended_replacement?.map((item, index) => {
              return <div key={item.id}> {item?.seafarer_person?.nationality?.value}</div>;
            });
            return nationality;
          },
          disableSortBy: true,
          minWidth: 120,
        },
      ],
    },
  ];
};

const ToReplaceSeafarer = ({
  seafarers,
  visitSeafarer,
  eventTracker,
  selectedColumns,
  loading,
  roleConfig,
  init_sort,
  seafarersTotalCount,
  tableRef,
}) => {
  const getFullName = (person) => {
    const firstName = person?.first_name ?? '';
    const lastName = person?.last_name ?? '';
    const middleName = person?.middle_name ?? '';
    const fullName = capitalizeArgs(firstName, middleName, lastName) || '---';
    return (
      <div>
        <b>{fullName} </b>
      </div>
    );
  };
  let columns = generateColumns(selectedColumns, parentHKIDLink, getFullName);

  if (loading) {
    const removeOnLoad = ['No.'];
    columns = columns.filter((col_obj) => {
      if (col_obj.Header && removeOnLoad.includes(col_obj.Header)) return false;
      return true;
    });
  }
  return (
    <div className="seafarer-table">
      <Table
        columns={columns}
        data={seafarers}
        eventTracker={eventTracker}
        visitSeafarer={visitSeafarer}
        loading={loading}
        init_sort={init_sort}
        seafarersTotalCount={seafarersTotalCount}
        tableRef={tableRef}
      />
    </div>
  );
};

const Table = React.memo(
  ({
    columns,
    data,
    visitSeafarer,
    eventTracker,
    loading,
    init_sort,
    seafarersTotalCount,
    tableRef,
  }) => {
    const defaultColumn = useMemo(
      () => ({
        width: 120,
      }),
      [],
    );

    const { getTableProps, getTableBodyProps, headerGroups, prepareRow, page } = useTable(
      {
        columns,
        data,
        defaultColumn,
        initialState: { sortBy: init_sort },
        manualPagination: true,
        manualSortBy: true,
        autoResetPage: false,
        autoResetSortBy: false,
      },
      useSortBy,
      usePagination,
      useFlexLayout,
    );

    return (
      <>
        <hr className="hr-1px" />
        {loading || seafarersTotalCount > 0 ? (
          <div {...getTableProps()} className="table  contract-expiry-list-table" ref={tableRef}>
            <div className="header">
              {headerGroups.map((headerGroup, index) => (
                <div
                  key={uuid()}
                  {...headerGroup.getHeaderGroupProps()}
                  id={`top-header-${index}`}
                  className="tr "
                >
                  {headerGroup.headers.map((column, index2) => {
                    const thProps = column.getHeaderProps(column.getSortByToggleProps());
                    return (
                      <div
                        key={column.id}
                        {...thProps}
                        className="th"
                        id={`as-${index} + ${index2}`}
                      >
                        {column.render('Header')}
                        <span>
                          {column.canSort && (
                            <Icon
                              icon={columnSortIconName(column)}
                              size={20}
                              className="default"
                              onClick={() => eventTracker('sortBy', columnSortEventName(column))}
                            />
                          )}
                        </span>
                      </div>
                    );
                  })}
                </div>
              ))}
            </div>
            {loading && <Spinner alignClass={`load-spinner`} />}
            <div {...getTableBodyProps()} className="body">
              {page.map((row) => {
                prepareRow(row);
                const seafarerId = row.original.id ?? undefined;
                return (
                  <div
                    key={seafarerId}
                    {...row.getRowProps()}
                    className="tr"
                    onClick={visitSeafarer.bind(this, seafarerId)}
                    onKeyDown={(event) => {
                      if (event.key === 'Enter' || event.key === ' ') {
                        visitSeafarer.bind(this, seafarerId)();
                      }
                    }}
                    tabIndex={0}
                  >
                    {row.cells.map((cell) => {
                      const tdProps = cell.getCellProps();
                      return (
                        <div key={tdProps.key} {...tdProps} className="td">
                          {cell.render('Cell')}
                        </div>
                      );
                    })}
                  </div>
                );
              })}
            </div>
          </div>
        ) : (
          <div className="no-result-found">
            {' '}
            <Icon icon="alert" className="alert-icon-no-search" />
            <div>
              {' '}
              <b>No result match your criteria for sign off table</b>
            </div>{' '}
          </div>
        )}
      </>
    );
  },
);

export default ToReplaceSeafarer;
