import React from 'react';
import { ButtonToolbar, Dropdown, Button } from 'react-bootstrap';
import TableColumnsButton from './TableColumnsButton';
import items, { getDefaultScreeningColumns } from './MenuList';
import { contractExpiryColumns, availableSeafarerColumn } from '../CrewList/MenuList';
import { screeningStatus, SCREENING_STATUS } from '../../model/constants';

const ButtonsBar = ({
  selectedColumns,
  onSelectColumn,
  canCreateSeafarer,
  handleAddSeafarer,
  eventTracker,
  activeKey,
  handleLongServiceExportToExcel,
  isLongServiceExcelExporting,
  isLongServiceFilterSelected,
}) => {
  let itemColumns;
  if (activeKey === 'contract-expiry') {
    itemColumns = [
      {
        type: 'header',
        Header: '',
      },
      ...contractExpiryColumns,
    ];
  } else if (activeKey === 'available-seafarers') {
    const columns = [...availableSeafarerColumn];
    itemColumns = [
      {
        type: 'header',
        Header: '',
      },
      ...columns,
    ];
  } else if (SCREENING_STATUS.includes(activeKey)) {
    itemColumns = [...items, ...getDefaultScreeningColumns(activeKey)];
  } else {
    itemColumns = items;
  }
  const showLongServiceExportToExcelButton = [
    screeningStatus.ALL,
    screeningStatus.PASSED,
    screeningStatus.UNDER_SCREENING,
    screeningStatus.REJECTED,
    screeningStatus.ARCHIVED,
  ].includes(activeKey);

  return (
    <ButtonToolbar className="toolbar-allignment">
      <TableColumnsButton
        items={itemColumns}
        selectedColumns={selectedColumns}
        onSelectColumn={onSelectColumn}
      />
      {showLongServiceExportToExcelButton && isLongServiceFilterSelected && (
        <Button
          variant="outline-primary"
          className="mr-2"
          onClick={handleLongServiceExportToExcel}
          data-testid="export-long-service-to-excel-btn"
          disabled={isLongServiceExcelExporting}
        >
          Export Long Service Report
        </Button>
      )}
      {canCreateSeafarer() && (
        <Dropdown
          id="AdvancedSearchDropdown"
          alignRight
          onToggle={(isOpen) => isOpen && eventTracker('moreDropdown', 'More')}
        >
          <Dropdown.Toggle variant="outline-primary" id="dropdown-more">
            More
          </Dropdown.Toggle>
          <Dropdown.Menu>
            <Dropdown.Item data-testid="fml-add-seafarer" onClick={handleAddSeafarer.bind(this)}>
              Add a Seafarer
            </Dropdown.Item>
          </Dropdown.Menu>
        </Dropdown>
      )}
    </ButtonToolbar>
  );
};

export default ButtonsBar;
