import React from 'react';
import CountryNationalityDropDownControl from '../AddSeafarer/CountryNationalityDropDownControl';
import { Container, Col, Row, Form } from 'react-bootstrap';

const QuickSearchComponent = ({ nationalities, parameters, onSearchChanged, disabled }) => {
  const { first_name, last_name, hkid, nationality_id } = parameters;

  const onInputChange = (event) => {
    const field = event.target.name;
    let value = event.target.value;
    if (value === '') value = null;

    onSearchChanged({
      first_name: field === 'first_name' ? value : first_name,
      last_name: field === 'last_name' ? value : last_name,
      hkid: field === 'hkid' ? value : hkid,
      nationality_id: field === 'nationality_id' ? value : nationality_id,
    });
  };

  return (
    <Container className="mt-3">
      <Row className="no-print">
        <Col>
          <Form.Control
            type="text"
            name="first_name"
            value={first_name}
            placeholder="First Name"
            disabled={disabled}
            onChange={onInputChange}
          />
        </Col>
        <Col>
          <Form.Control
            type="text"
            name="last_name"
            value={last_name}
            placeholder="Last Name"
            onChange={onInputChange}
            disabled={disabled}
          />
        </Col>
        <Col>
          <Form.Control
            type="text"
            name="hkid"
            value={hkid}
            placeholder="HKID"
            onChange={onInputChange}
            disabled={disabled}
          />
        </Col>
        <Col>
          <CountryNationalityDropDownControl
            name={'nationality_id'}
            placeholder={'Nationality'}
            selectedValue={nationality_id}
            dropDownValues={nationalities}
            onInputChange={onInputChange}
            disabled={disabled}
          />
        </Col>
      </Row>
    </Container>
  );
};

export default QuickSearchComponent;
