import React from 'react';
import { Dropdown, SafeAnchor } from 'react-bootstrap';
import styleGuide from '../../styleGuide';
const { Icon } = styleGuide;

const CustomItem = React.forwardRef((props, ref) => {
  return (
    <SafeAnchor className="dropdown-item" onClick={props.onClick}>
      {props.checked ? (
        <Icon
          icon="checked"
          size={20}
          className="default"
          style={{ verticalAlign: 'top', color: '#17A2B8', marginRight: '5px' }}
        />
      ) : (
        <div
          style={{
            display: 'inline-block',
            width: '20px',
            marginRight: '5px',
          }}
        >
          {' '}
        </div>
      )}
      {props.children}
    </SafeAnchor>
  );
});

const TableColumnsButton = ({ selectedColumns, onSelectColumn, items, size = 'md' }) => {
  const selectedColumnsIds = selectedColumns?.map(col => col.id);
  return (
    <Dropdown
      className="mr-2"
      alignRight
      onSelect={(eventKey, event) => {
        onSelectColumn(items[eventKey]);
      }}
    >
      <Dropdown.Toggle size={size} variant="outline-primary" id="dropdown-table-columns">
        Table Columns
      </Dropdown.Toggle>
      <Dropdown.Menu>
        {items?.map((item, idx) => {
          const checked = selectedColumnsIds.includes(item?.id);
          const indexKey = item.id;
          if (item.type === 'header') {
            return <Dropdown.Header key={indexKey + '_header'}>{item.Header}</Dropdown.Header>;
          } else if (item.type === 'divider') {
            return <Dropdown.Divider key={indexKey + 'divider'} />;
          } else {
            return (
              <Dropdown.Item key={indexKey} as={CustomItem} checked={checked} eventKey={idx}>
                {item.Header}
              </Dropdown.Item>
            );
          }
        })}
      </Dropdown.Menu>
    </Dropdown>
  );
};

export default TableColumnsButton;
