.timeline-card-body {
  height: 75vh;
  padding: 0 1rem;
}
.timeline {
  position: relative;

  &-empty {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    font-size: 14px;
    
    &-body {
      text-align: center;
      width: 300px;
    }

    .btn {
      font-size: 14px;
    } 
  }
}
.timeline-seperator {
  width: 20px;
  position: relative;
  cursor: pointer;
  &:hover {
    .add-port{
      opacity: 1;
      transition: opacity 0.3s ease-in-out;
    }
  }
  &::before {
    content: '';
    position: absolute;
    width: 1px;
    background-color: #1F4A70;
    top: 0;
    bottom: 0;
    left: 10px;
  }
  &::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    border: 4px solid #1F4A70;
    background-color: #fff;
    border-radius: 50%;
    top: 24px;
    left: 0;
    z-index: 10;
  }
  .add-port {
    width: 20px;
    height: 20px;
    background-color: #fff;
    border-radius: 50%;
    z-index: 10;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
    position: absolute;
    top: 75%;
    left: 50%;
    transform: translateX(-50%); 
  }
}
.timeline-event {
  width: 100%;
  position: relative;
  display: flex;
  justify-content: flex-start;
}
.timeline-label {
  width: 110px;
  padding: 22px 10px;
  box-sizing: border-box;
  font-size: 14px;
  color: #1F4A70;
  text-align: left;

  &-meta {
      font-size: 12;
      color: #6C757D
  }
}
.date-not-available {
  padding: 22px 20px;
  text-align: right;
}
.timeline-port {
  flex: 1;
  padding: 20px 0 20px 20px;
  .seafarer-card {
    margin-left: 0;
    margin-right: 0;
    .col-2 {
      max-width: 8.3333333333%;
    }
  }
}
.port-name {
  font-size: 16px;
  color: #1F4A70;
  margin-bottom: 4px;
}
.port-deleted {
  color: #DC3545;
}
.clubbing-status {
  font-size: 14px;
  margin-bottom: 4px;
}

.btn-icon {
  cursor: pointer;
}
.timeline-seperator-loader {
  width: 20px;
  position: relative;
  cursor: pointer;

  &:hover {
    .add-port {
      opacity: 1;
      transition: opacity 0.3s ease-in-out;
    }
  }

  &::before {
    content: '';
    position: absolute;
    width: 1px;
    background-color: #1F4A70;
    top: 0;
    bottom: 0;
    left: 10px;
  }
  
        &::after {
          content: '';
          position: absolute;
          width: 20px;
          height: 20px;
          border-radius: 50%;
          top: 24px;
          left: 0;
          z-index: 10;
        }
        }