/* eslint-disable react/display-name */
/* eslint-disable react/prop-types */
import React, { useMemo, useEffect } from 'react';
import { Row } from 'react-bootstrap';
import { useTable, useSortBy, usePagination, useFlexLayout } from 'react-table';
import { useSticky } from 'react-table-sticky';
import Spinner from '../common/Spinner';
import styleGuide from '../../styleGuide';
import {
  getPageTableState,
  storePageNumber,
  storePageSort,
  storePageSize,
} from '../../util/local-storage-helper';
import PaginationBar from '../seafarerList/PaginationBar';
import TrainingCoursesTableColumns from './MenuList.tsx';
const { Icon } = styleGuide;

const TrainingCoursesTable = React.memo(
  ({
    data,
    fetchData,
    pageCount,
    eventTracker,
    loading,
    roleConfig,
    quickSearchParams,
    advancedSearchParams,
    init_sort,
    totalCount,
    tableRef,
    tabName,
  }) => {
    return (
      <div className="seafarer-table">
        <Table
          columns={TrainingCoursesTableColumns}
          data={data}
          fetchData={fetchData}
          pageCount={pageCount}
          eventTracker={eventTracker}
          loading={loading}
          quickSearchParams={quickSearchParams}
          advancedSearchParams={advancedSearchParams}
          init_sort={init_sort}
          totalCount={totalCount}
          tableRef={tableRef}
          tabName={tabName}
        />
      </div>
    );
  },
);
const Table = React.memo(
  ({
    columns,
    data,
    fetchData,
    pageCount: controlledPageCount,
    eventTracker,
    loading,
    quickSearchParams,
    advancedSearchParams,
    init_sort,
    totalCount,
    tableRef,
    tabName,
  }) => {
    const defaultColumn = useMemo(
      () => ({
        minWidth: 120,
        width: 120,
      }),
      [],
    );
    const {
      getTableProps,
      getTableBodyProps,
      headerGroups,
      prepareRow,
      page,
      canPreviousPage,
      canNextPage,
      pageCount,
      gotoPage,
      setPageSize,
      state: { pageIndex, pageSize, sortBy },
    } = useTable(
      {
        columns,
        data,
        defaultColumn,
        initialState: { sortBy: init_sort },
        manualPagination: true,
        manualSortBy: true,
        autoResetPage: false,
        autoResetSortBy: false,
        pageCount: controlledPageCount,
      },
      useSortBy,
      usePagination,
      useFlexLayout,
      useSticky,
    );
    const filterPages = (visiblePages, totalPages) =>
      visiblePages.filter((page) => page <= totalPages);
    const getVisiblePages = (page, total) => {
      if (total < 7) {
        return filterPages([1, 2, 3, 4, 5, 6], total);
      }
      if (page % 5 >= 0 && page > 4 && page + 2 < total) {
        return [1, page - 1, page, page + 1, total];
      }
      if (page % 5 >= 0 && page > 4 && page + 2 >= total) {
        return [1, total - 3, total - 2, total - 1, total];
      }
      return [1, 2, 3, 4, 5, total];
    };
    const visiblePages = getVisiblePages(pageIndex, pageCount);
    const resetPage = async (page_no = 0, page_size = 10) => {
      setPageSize(page_size);
      await fetchData({ pageSize: page_size, sortBy, pageIndex: page_no });
      gotoPage(page_no);
    };
    useEffect(() => {
      storePageNumber(tabName, 0);
    }, []);
    useEffect(() => {
      storePageSort(sortBy);
    }, [sortBy]);
    useEffect(() => {
      (async function reset_page() {
        const { pageIndex, pageSize } = getPageTableState(tabName);
        await resetPage(pageIndex, pageSize);
      })();
    }, [sortBy, quickSearchParams, advancedSearchParams]);
    const pageSwitch = async (page_no) => {
      await fetchData({ pageSize, sortBy, pageIndex: page_no });
      gotoPage(page_no);
      storePageNumber(tabName, page_no);
      eventTracker('pageSwitch', page_no);
    };
    const pageSizeSwitch = async (page_size) => {
      //Internally pageIndex gets recalibrated as follows
      const new_index = Math.floor((pageIndex * pageSize) / page_size);
      setPageSize(page_size);
      await fetchData({ pageIndex: new_index, sortBy, pageSize: page_size });
      storePageSize(tabName, page_size);
      storePageNumber(tabName, new_index);
      eventTracker('pageSizeSwitch', page_size);
    };
    return (
      <>
        <hr className="hr-1px" />
        <Row>
          <PaginationBar
            className="top-pagination-bar"
            pageSwitch={pageSwitch}
            pageSizeSwitch={pageSizeSwitch}
            canPreviousPage={canPreviousPage}
            canNextPage={canNextPage}
            visiblePages={visiblePages}
            pageSize={pageSize}
            pageIndex={pageIndex}
          />
          <div className="seafarer-list-count">
            <b>{totalCount}</b> Results
          </div>
        </Row>
        {!loading ? (
          <>
            <div {...getTableProps()} className="table sticky" ref={tableRef}>
              <div className="header">
                {headerGroups.map((headerGroup, index) => (
                  <div key={index} {...headerGroup.getHeaderGroupProps()} className="tr">
                    {headerGroup.headers.map((column, index2) => {
                      const thProps = column.getHeaderProps(column.getSortByToggleProps());
                      return (
                        <div key={index2} {...thProps} className="th">
                          {column.render('Header')}
                          <span>
                            {column.canSort &&
                              (column.isSorted ? (
                                column.isSortedDesc ? (
                                  <Icon
                                    icon="sort-ascending"
                                    size={20}
                                    className="default"
                                    onClick={() =>
                                      eventTracker('sortBy', `None - ${column.render('Header')}`)
                                    }
                                  />
                                ) : (
                                  <Icon
                                    icon="sort-descending"
                                    size={20}
                                    className="default"
                                    onClick={() =>
                                      eventTracker('sortBy', `Desc - ${column.render('Header')}`)
                                    }
                                  />
                                )
                              ) : (
                                <Icon
                                  icon="sort-off"
                                  size={20}
                                  className="default"
                                  onClick={() =>
                                    eventTracker('sortBy', `ASC - ${column.render('Header')}`)
                                  }
                                />
                              ))}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                ))}
              </div>
              {totalCount > 0 ? (
                <>
                  <div {...getTableBodyProps()} className="body">
                    {page.map((row) => {
                      prepareRow(row);
                      const seafarerId = row.original.id ?? undefined;
                      return (
                        <div key={seafarerId} {...row.getRowProps()} className="tr">
                          {row.cells.map((cell, index2) => {
                            const tdProps = cell.getCellProps();
                            return (
                              <div key={index2} {...tdProps} className="td">
                                {cell.render('Cell')}
                              </div>
                            );
                          })}
                        </div>
                      );
                    })}
                  </div>
                  <Row>
                    <PaginationBar
                      pageSwitch={pageSwitch}
                      pageSizeSwitch={pageSizeSwitch}
                      canPreviousPage={canPreviousPage}
                      canNextPage={canNextPage}
                      visiblePages={visiblePages}
                      pageSize={pageSize}
                      pageIndex={pageIndex}
                    />
                  </Row>
                </>
              ) : (
                <div>
                  {' '}
                  <Icon icon="alert" className="alert-icon-no-search-training" />
                  <div>
                    {' '}
                    <b className="no-records-text">No Records Found.</b>
                  </div>{' '}
                </div>
              )}
            </div>
          </>
        ) : (
          <Spinner alignClass={`load-spinner`} />
        )}
      </>
    );
  },
);
export default TrainingCoursesTable;
