/* eslint-disable react/display-name */
import React from 'react';
import moment from 'moment-timezone';
import { ACTIVITY_STATUS, STATUS } from '../../constants/trainingCourses';
import { Dash } from '../../model/utils';

function highlightText(cellValue: string, searchedKeyword = '') {
  cellValue = cellValue.toString();
  if (searchedKeyword === '') return cellValue;
  let index = cellValue.toLowerCase().indexOf(searchedKeyword);
  if (index !== -1) {
    return (
      <>
        {cellValue.substring(0, index)}
        <span className="search-text-highlight">
          {cellValue.substring(index, index + searchedKeyword.length)}
        </span>
        {cellValue.substring(index + searchedKeyword.length)}
      </>
    );
  }
  return cellValue;
}
const TrainingCoursesTableColumns = [
  {
    Header: 'Training Category',
    id: 'category',
    accessor: (row) => {
      return <div>{row?.category ? highlightText(row?.category, row?.keyword) : Dash}</div>;
    },
    order: 2,
    disableSortBy: false,
    minWidth: 50,
  },
  {
    Header: 'Sub Category',
    id: 'subcategory',
    accessor: (row) => {
      return <div>{row?.subcategory ? highlightText(row?.subcategory, row?.keyword) : Dash}</div>;
    },
    order: 2,
    disableSortBy: false,
    minWidth: 50,
  },
  {
    Header: 'Course Name',
    id: 'course',
    accessor: (row) => {
      return (
        <div>
          {row?.course
            ? highlightText(row?.course, row?.keyword) +
              (row?.activityname ? ` (${row?.activityname})` : '')
            : Dash}
        </div>
      );
    },
    order: 2,
    disableSortBy: false,
    minWidth: 300,
  },
  {
    Header: 'Course Status',
    id: 'status',
    accessor: (row) => {
      return (
        <div
          style={{
            color:
              row?.status === STATUS.COMPLETED
                ? 'green'
                : row?.status === STATUS.PENDING
                ? 'orange'
                : '',
          }}
        >
          {row?.status || Dash}
        </div>
      );
    },
    order: 2,
    disableSortBy: false,
    minWidth: 30,
  },
  {
    Header: 'Importance',
    id: 'requiredcourse',
    accessor: (row) => {
      return <div>{row?.requiredcourse ?? Dash}</div>;
    },
    order: 2,
    disableSortBy: false,
    minWidth: 30,
  },
  {
    Header: 'Document Type',
    id: 'documenttype',
    accessor: (row) => {
      return <div>{row?.documenttype || Dash}</div>;
    },
    order: 2,
    disableSortBy: false,
    minWidth: 50,
  },
  {
    Header: 'Test Result',
    id: 'passedtest',
    accessor: (row) => {
      return (
        <div
          style={{
            color:
              row?.passedtest === ACTIVITY_STATUS.PASSED
                ? 'green'
                : row?.passedtest === ACTIVITY_STATUS.FAILED
                ? 'red'
                : '',
          }}
        >
          {row?.passedtest ?? Dash}
        </div>
      );
    },
    order: 2,
    disableSortBy: false,
    minWidth: 30,
  },
  {
    Header: 'Score',
    id: 'score',
    accessor: (row) => {
      return <div>{row?.score || Dash}</div>;
    },
    order: 2,
    disableSortBy: false,
    minWidth: 30,
  },
  {
    Header: 'Date Completed',
    id: 'datecompleted',
    accessor: (row) => {
      return (
        <div>{row?.datecompleted ? moment(row.datecompleted).format('DD MMM YYYY') : Dash}</div>
      );
    },
    order: 2,
    disableSortBy: false,
    minWidth: 50,
  },
];
export default TrainingCoursesTableColumns;
