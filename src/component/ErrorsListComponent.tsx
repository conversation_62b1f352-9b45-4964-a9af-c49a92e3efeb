import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON> } from 'react-bootstrap';
import { v4 as uuid } from 'uuid';
import './scss/error-list.scss';
import {
  ERROR_MAPPINGS,
  BASIC_LINK,
  PASSPORT_SECTION,
  SEAMAN_BOOK_SECTION,
  PERSONAL_DETAILS_SECTION,
  BANK_ACCOUNT_LINK,
} from '../constants/formSections';

const getBankAccountsError = (bankAccounts) => {
  if (!bankAccounts || bankAccounts.length === 0) return [];
  const map = {};
  bankAccounts.forEach((bankAccount) => {
    if (!bankAccount) return;
    Object.entries(bankAccount).forEach(([key, value]) => {
      if (typeof value === 'object' && value !== null) {
        Object.entries(value).forEach(([valueKey, valueValue]) => {
          const subKey = `${key}_${valueKey}`;
          map[subKey] = ERROR_MAPPINGS[subKey] ?? valueValue;
        });
      } else {
        switch (key) {
          case 'number':
            map[key] = ERROR_MAPPINGS['bank_account_number'] ?? value;
            break;
          case 'account_holder_date_of_birth':
            map[key] = {
              message: `${value} (Bank Accounts / Bank Account)`,
              link: BANK_ACCOUNT_LINK,
            };
            break;
          default:
            map[key] = ERROR_MAPPINGS[key] ?? value;
        }
      }
    });
  });
  return Object.values(map);
};

function checkErrorType(type) {
  if (type === 'passport') {
    return '(Basic / Passport Details)';
  } else if (type === 'seaman_book') {
    return `(Basic / Seaman's Book)`;
  }
  return '(Basic / Personal Details)';
}

function validateDateFieldError(dateOfExpiry, documentType) {
  let subSection = PERSONAL_DETAILS_SECTION;
  if (documentType === 'passport') {
    subSection = PASSPORT_SECTION;
  } else if (documentType === 'seaman_book') {
    subSection = SEAMAN_BOOK_SECTION;
  }

  return {
    message: `${dateOfExpiry} ${checkErrorType(documentType)}`,
    link: BASIC_LINK,
    subSection,
  };
}

function getErrorsFromDictionary(dictionary, documentType) {
  if (!dictionary) {
    return [];
  }
  return Object.keys(dictionary)
    .filter(
      (key) => !['passports', 'seaman_books', 'bank_accounts', 'seafarer_contacts'].includes(key),
    )
    .map((key) => {
      if (key === 'number' && documentType) {
        return ERROR_MAPPINGS[`${documentType}_${key}`] ?? dictionary[key];
      }
      if (
        key === 'date_of_expiry' ||
        key === 'date_of_issue' ||
        key === 'date_of_birth' ||
        key === 'account_holder_date_of_birth'
      ) {
        return validateDateFieldError(dictionary[key], documentType);
      }
      if (documentType) {
        return ERROR_MAPPINGS[`${documentType}_${key}`] ?? dictionary[key];
      }
      return ERROR_MAPPINGS[key] ?? dictionary[key];
    });
}

function getErrorsList(dictionary) {
  const personData = dictionary.seafarer_person ?? [];
  const person = getErrorsFromDictionary(personData);
  const passports = getErrorsFromDictionary(
    personData.passports ? personData.passports[0] : [],
    'passport',
  );
  const seamanBooks = getErrorsFromDictionary(
    personData.seaman_books ? personData.seaman_books[0] : [],
    'seaman_book',
  );
  const seafarerContacts = getErrorsFromDictionary(
    personData.seafarer_contacts ? personData.seafarer_contacts[0] : [],
  );
  const familyMembers = getErrorsFromDictionary(
    personData.family_members && typeof personData.family_members != 'string' ? personData.family_members[0] : [],
    'family_member',
  );

  const bankAccounts = getBankAccountsError(personData.bank_accounts);
  return [
    ...Object.keys(dictionary)
      .filter(
        (key) =>
          ![
            'passports',
            'seaman_books',
            'seafarer_person',
            'seafarer_contacts',
            'family_members',
          ].includes(key),
      )
      .map((key) => {
        let message = ERROR_MAPPINGS[key];
        if (message === undefined) {
          message = dictionary[key];
        }
        return message;
      }),
    person,
    passports,
    seamanBooks,
    bankAccounts,
    seafarerContacts,
    familyMembers,
  ]
    .flat()
    .filter((error) => error);
}

const ErrorsListComponent = ({
  errors,
  onValidateLink,
  csstyle,
  headertext = 'Correct the following and other tabs highlighted in red:',
  liststyle,
  isDisplayLinkType = true,
}) => {
  if (!errors || Object.keys(errors).length === 0) {
    return null;
  }

  let allErrors = getErrorsList(errors);
  console.log("error",allErrors,errors)
  if (allErrors.length === 0) {
    return null;
  }

  const handleNavigate = (link, subSection) => onValidateLink(link, subSection);

  const alertStyle = csstyle ?? {};
  const listStyle = liststyle ?? {};
  return (
    <Alert
      style={alertStyle}
      className="takeover_error_list"
      variant="danger"
      data-testid="form-error-list"
    >
      <Alert.Heading>{headertext}</Alert.Heading>
      <ul>
        {allErrors.map((error) => {
          if (typeof error !== 'string') {
            const { message, link, subSection } = error;
            if (!message) return null;
            if (!isDisplayLinkType) {
              return (
                <li key={uuid()} style={{ float: 'none', ...listStyle }}>
                  {message}
                </li>
              );
            }
            return (
              <li key={uuid()} style={{ float: 'none' }}>
                <Button
                  variant="link"
                  className="error-message"
                  onClick={handleNavigate.bind(this, link, subSection)}
                >
                  {message}
                </Button>
              </li>
            );
          }
          return (
            <li key={uuid()} style={{ float: 'none', ...listStyle }}>
              {error}
            </li>
          );
        })}
      </ul>
    </Alert>
  );
};

export default ErrorsListComponent;
