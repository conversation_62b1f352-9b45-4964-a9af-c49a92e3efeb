/* eslint-disable react/prop-types */
import React from 'react';
import { Typeahead } from 'react-bootstrap-typeahead';
import PropTypes from 'prop-types';

const ReferenceDropDownControl = ({
  dropDownValues,
  name,
  onInputChange,
  selectedValue,
  disabled,
  isInvalid,
  testID,
}) => {
  let options = dropDownValues ?? [];
  if (selectedValue && !options.includes(selectedValue)) {
    options = [selectedValue, ...options];
  }

  const onChange = (value) => {
    const result = { target: { name, value: value[0] } };
    onInputChange(result);
  };

  const selected = selectedValue ? [selectedValue] : [];

  return (
    <Typeahead
      id={name}
      onChange={onChange}
      inputProps={{'data-testid': testID}}
      options={options}
      placeholder="Please select"
      selected={selected}
      disabled={disabled}
      isInvalid={isInvalid}
    />
  );
};

ReferenceDropDownControl.propTypes = {
  dropDownValues: PropTypes.array,
  name: PropTypes.string,
  onInputChange: PropTypes.func,
  selectedValue: PropTypes.string,
  isInvalid: PropTypes.bool,
  disabled: PropTypes.bool,
  testID: PropTypes.string,
};

export default ReferenceDropDownControl;
