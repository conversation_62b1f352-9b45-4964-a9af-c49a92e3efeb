/* eslint-disable react/prop-types */
import React from 'react';
import { Row, Button } from 'react-bootstrap';
import PropTypes from 'prop-types';
import styleGuide from '../../styleGuide';
const { Icon } = styleGuide;

const AddAnotherComponent = ({ onClick }) => {
  return (
    <div>
      <hr className="dashed_line" />
      <Button data-testid="fml-seafarer-contact-address-add-another" variant="outline-primary" size="sm" className="pl-3 pr-3" onClick={onClick}>
        Add Another
      </Button>
    </div>
  );
};

AddAnotherComponent.propTypes = {
  onClick: PropTypes.func,
};

const SectionTitleComponent = ({ title }) => {
  return (
    <div>
      <hr className="section_line mt-5" />
      <h5 className="add_seafarer_page__section-title mb-3">{title ?? ''}</h5>
    </div>
  );
};

SectionTitleComponent.propTypes = {
  title: PropTypes.string,
};

const SectionTitleComponentWithRemove = ({ title, onClick, isEditable, isRemoveAble }) => {
  return (
    <div>
      <hr className="section_line mt-5" />
      <div className="container pl-0 ml-0">
        <div className="row row-cols-auto">
          <div className="col-auto pr-3 my-auto">
            <h5 className="add_seafarer_page__section-title mb-1 text-uppercase">{title ?? ''}</h5>
          </div>
          <div className="col-1 pl-0">
            {isEditable && isRemoveAble && (
              <Button variant="outline-primary" size="sm" className="pl-3 pr-3" onClick={onClick}>
                Remove
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

SectionTitleComponentWithRemove.propTypes = {
  title: PropTypes.string,
  onClick: PropTypes.func,
  isEditable: PropTypes.bool,
  isRemoveAble: PropTypes.bool,
};

const SubSectionTitleComponent = ({ title }) => {
  return (
    <div className="mb-3 mt-4">
      <hr className="solid-gray-line" />
      <h5 className="add_seafarer_page__subsection-title">{title ?? ''}</h5>
    </div>
  );
};

SubSectionTitleComponent.propTypes = {
  title: PropTypes.string,
};

const SubSectionTitleRemoveComponent = ({ title, onClick, isEditable }) => {
  return (
    <div>
      <hr className="dashed_line mt-2" />
      <h5 className="add_seafarer_page__subsection-title-remove mr-3">{title ?? ''}</h5>
      {isEditable && (
        <Button variant="outline-primary" size="sm" className="pl-3 pr-3" onClick={onClick}>
          Remove
        </Button>
      )}
    </div>
  );
};

SubSectionTitleRemoveComponent.propTypes = {
  title: PropTypes.string,
  onClick: PropTypes.func,
  isEditable: PropTypes.bool,
};

const SelectedFileField = (props) => {
  return (
    <Row>
      <div className="selected-file-wrapper">
        <Button variant="link" className="selected-file-link" onClick={props.onClick}>
          {props.fileName}
        </Button>
      </div>
      {props.disabled ? null : (
        <Icon icon="remove" size={30} className="remove_icon" onClick={props.onRemoveClick} />
      )}
    </Row>
  );
};

SelectedFileField.propTypes = {
  fileName: PropTypes.string,
  onClick: PropTypes.func,
  disabled: PropTypes.bool,
  onRemoveClick: PropTypes.func,
};

const SelectFileButton = (props) => {
  const hiddenFileInput = React.useRef(null);

  const onClick = () => {
    if (props?.isBankAccount && !props?.bankAccountNumber) {
      props.setIsBankAccountNumberMandatory(true);
      return;
    }
    props?.isBankAccount && props?.setIsBankAccountNumberMandatory(false);
    hiddenFileInput.current.click();
  };

  const handleChange = (event) => {
    const fileUploaded = event.target.files[0];
    if (props?.fileIndex === undefined) {
      props.onSelectFile(fileUploaded);
    } else {
      props.onSelectFile(fileUploaded, props?.fileIndex);
    }
  };

  return (
    <div>
      <Button variant="outline-primary" size="sm" className="pl-3 pr-3" onClick={onClick} data-testid="fml-upload-document" disabled={props?.disabled}>
        Upload
      </Button>
      <input
        type="file"
        name="document"
        accept=".jpg,.jpeg,.png,.pdf"
        ref={hiddenFileInput}
        onChange={handleChange}
        style={{ display: 'none' }}
      />
    </div>
  );
};

SelectFileButton.propTypes = {
  isBankAccount: PropTypes.bool,
  bankAccountNumber: PropTypes.string,
  fileIndex: PropTypes.string,
  setIsBankAccountNumberMandatory: PropTypes.func,
  onSelectFile: PropTypes.func,
};

export default {
  SelectFileButton,
  SelectedFileField,
  AddAnotherComponent,
  SectionTitleComponent,
  SectionTitleComponentWithRemove,
  SubSectionTitleComponent,
  SubSectionTitleRemoveComponent,
};
