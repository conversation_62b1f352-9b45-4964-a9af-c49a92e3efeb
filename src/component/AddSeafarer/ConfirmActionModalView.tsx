/* eslint-disable react/prop-types */
import React from 'react';
import Modal from 'react-bootstrap/Modal';
import Button from 'react-bootstrap/Button';
import Spinner from '../common/Spinner';
import PropTypes from 'prop-types';

function ConfirmActionModalView(props) {
  const propsToModal = { ...props };
  delete propsToModal.isDisableConfirmDeleteBtn;
  delete propsToModal.onConfirm;
  delete propsToModal.isModalLoading;

  return (
    <Modal
      {...propsToModal}
      onHide={props.onClose}
      aria-labelledby="contained-modal-title-vcenter"
      centered
      data-testid="confirm-action-modal"
    >
      <Modal.Header closeButton>
        <Modal.Title id="contained-modal-title-vcenter">{props.title}</Modal.Title>
      </Modal.Header>
      {props.isModalLoading ? (
        <div className="mt-5">
          {' '}
          <Spinner />{' '}
        </div>
      ) : (
        <Modal.Body>
          <p>{props.message}</p>
        </Modal.Body>
      )}
      <Modal.Footer>
        <Button onClick={props.onClose}>Cancel</Button>
        <Button
          variant="secondary"
          onClick={props.onConfirm}
          disabled={props.isDisableConfirmDeleteBtn}
          className="confirm-btn"
        >
          {props.confirmBtnText ?? 'Confirm'}
        </Button>
      </Modal.Footer>
    </Modal>
  );
}

ConfirmActionModalView.propTypes = {
  title: PropTypes.string,
  message: PropTypes.node,
  onClose: PropTypes.func,
  onConfirm: PropTypes.func,
  isModalLoading: PropTypes.bool,
  isDisableConfirmDeleteBtn: PropTypes.bool,
};

export default ConfirmActionModalView;
