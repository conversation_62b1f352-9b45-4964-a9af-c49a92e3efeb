import React from 'react';
import { Form, Col } from 'react-bootstrap';
import CountryNationalityDropDownControl from '../AddSeafarer/CountryNationalityDropDownControl';
import PropTypes from 'prop-types';

const AddressDetailFields = ({ address, countries, handleBlur, onChange, error, isEditable }) => {
  const handleChange = (event) => {
    const field = event.target.name;
    const addressCopy = { ...address };
    addressCopy[field] = event.target.value;

    if (field === 'country_id') {
      delete addressCopy['country'];
    }

    onChange(addressCopy);
  };

  return (
    <>
      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Postal / Zip Code</Form.Label>
          <Form.Control
            type="text"
            name="postal_zip_code"
            value={address.postal_zip_code ?? ''}
            isInvalid={!!error.postal_zip_code}
            onChange={handleChange}
            disabled={!isEditable}
            data-testid="fml-seafarer-address-postal-zip-code"
          />
          <Form.Control.Feedback type="invalid">{error.postal_zip_code}</Form.Control.Feedback>
        </Form.Group>

        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Country</Form.Label>
          <CountryNationalityDropDownControl
            name={'country_id'}
            selectedValue={address.country_id ?? ''}
            dropDownValues={countries}
            onBlur={handleBlur}
            isInvalid={!!error.country_id}
            onInputChange={handleChange}
            disabled={!isEditable}
            testID={'data-testid="fml-seafarer-address-country'}
          />
        </Form.Group>
      </Form.Row>

      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>State / Province / District</Form.Label>
          <Form.Control
            type="text"
            name="state"
            value={address.state ?? ''}
            isInvalid={!!error.state}
            onChange={handleChange}
            disabled={!isEditable}
            data-testid="fml-seafarer-address-state-province-district"
          />
          <Form.Control.Feedback type="invalid">{error.state}</Form.Control.Feedback>
        </Form.Group>

        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Village / Town / City</Form.Label>
          <Form.Control
            type="text"
            name="city"
            value={address.city ?? ''}
            isInvalid={!!error.city}
            onChange={handleChange}
            disabled={!isEditable}
            data-testid="fml-seafarer-address-village-town-city"
          />
        </Form.Group>
        <Form.Control.Feedback type="invalid">{error.city}</Form.Control.Feedback>
      </Form.Row>

      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Building Name / Estate</Form.Label>
          <Form.Control
            type="text"
            name="building"
            value={address.building ?? ''}
            isInvalid={!!error.building}
            onChange={handleChange}
            disabled={!isEditable}
            data-testid="fml-seafarer-address-building-name-estate"
          />
          <Form.Control.Feedback type="invalid">{error.building}</Form.Control.Feedback>
        </Form.Group>

        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Rest of the Address</Form.Label>
          <Form.Control
            type="text"
            name="other_address"
            value={address.other_address ?? ''}
            isInvalid={!!error.other_address}
            onChange={handleChange}
            disabled={!isEditable}
            data-testid="fml-seafarer-address-rest-of-address"
          />
          <Form.Control.Feedback type="invalid">{error.other_address}</Form.Control.Feedback>
        </Form.Group>
      </Form.Row>
    </>
  );
};

AddressDetailFields.propTypes = {
  address: PropTypes.object,
  countries: PropTypes.array,
  handleBlur: PropTypes.func,
  onChange: PropTypes.func,
  error: PropTypes.object,
  isEditable: PropTypes.bool,
};

export default AddressDetailFields;
