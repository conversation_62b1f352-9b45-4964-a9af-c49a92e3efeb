import React, { useState } from 'react';
import { Form, Col } from 'react-bootstrap';
import FleetDatePicker from '../AddSeafarer/FleetDatePicker';
import CountryNationalityDropDownControl from '../AddSeafarer/CountryNationalityDropDownControl';
import ConfirmActionModalView from './ConfirmActionModalView';
import AddSeafarerOtherComponents from './AddSeafarerOtherComponents';
const { SelectedFileField, SelectFileButton } = AddSeafarerOtherComponents;
import Spinner from '../../component/common/Spinner';
import { COMMON_MESSAGES } from '../../constants/common-labels-and-messages';
import PropTypes from 'prop-types';

import ImageController from '../../controller/image-upload-controller';
const imageController = new ImageController();

const PassportComponent = (props) => {
  const {
    passport,
    countries,
    onPassportChange,
    errors,
    handleBlur,
    hasDuplicates,
    isSpinnerActive,
    isEditable,
    index,
  } = props;
  const [removeModalShow, setRemoveModalShow] = useState(false);

  const onDateOfIssueChange = (value) => {
    const item = passport;
    item.date_of_issue = value;
    onPassportChange(item);
  };

  const onDateOfExpiryChange = (value) => {
    const item = passport;
    item.date_of_expiry = value;
    onPassportChange(item);
  };

  const onInputChange = (event) => {
    const item = passport;

    const field = event.target.name;
    const value = event.target.value;

    item[field] = value;

    if (field === 'country_id') {
      delete item.country;
    }

    if (field === 'number') {
      item[field] = value.trim();
      props.onPassportNumberChange(item, index);
    }

    onPassportChange(item);
  };

  const handleSelectedFile = (file) => {
    if (file === undefined) {
      return;
    }

    const item = passport;
    item['file'] = file;
    onPassportChange(item);
  };

  let fileName;
  const file = passport.file;
  if (file) {
    fileName = file.name;
  }

  let document;
  const docs = passport.document ?? [];
  if (docs.length > 0) {
    document = docs[0];
    fileName = docs[0].name;
  }

  const handleRemoveFile = () => {
    setRemoveModalShow(false);
    const item = passport;
    if (item.document && item.document.length > 0) {
      item.idToRemove = item.document[0].id;
    }
    delete item.file;
    delete item.document;
    onPassportChange(item);
  };

  const downloadFile = async () => {
    window.open(`https://${window.location.hostname}/seafarer/document/${document.id}/passport`);
  };

  const isNoCopy = errors?.file === 'A file is required';
  const isFileSizeTooLarge = errors.file === 'File too large';
  const isUnsupportedFileType = errors.file === 'Unsupported Format';
  const isInvalidDateofExpiry = !!errors?.date_of_expiry;
  const isInvalidDateofIssue = !!errors?.date_of_issue;

  const selectFileButton = isEditable ? (
    <SelectFileButton onSelectFile={handleSelectedFile} />
  ) : null;

  return (
    <div>
      <Form.Row>
        <Form.Group as={Col} md="6">
          <Form.Label data-testid="fml-upload-passport">
            Upload copy of passport* (pdf, jpg, png, max size 12MB)
          </Form.Label>
          {fileName !== undefined ? (
            <SelectedFileField
              fileName={fileName}
              onClick={downloadFile}
              disabled={!isEditable}
              onRemoveClick={() => setRemoveModalShow(true)}
            />
          ) : (
            selectFileButton
          )}
          {isNoCopy && (
            <Form.Control.Feedback type="invalid" className={'document-copy-feedback'}>
              Please provide a copy of Passport.
            </Form.Control.Feedback>
          )}
          {isFileSizeTooLarge && (
            <Form.Control.Feedback type="invalid" className={'document-copy-feedback'}>
              {COMMON_MESSAGES.MESSAGE_FILE_SIZE_EXCEED}
            </Form.Control.Feedback>
          )}
          {isUnsupportedFileType && (
            <Form.Control.Feedback type="invalid" className={'document-copy-feedback'}>
              File Extension is not supported
            </Form.Control.Feedback>
          )}
        </Form.Group>

        <Form.Group as={Col} md="5">
          <Form.Label>Passport Number*</Form.Label>
          <Form.Control
            type="text"
            name="number"
            value={passport.number}
            onChange={onInputChange}
            isInvalid={!!errors.number || hasDuplicates}
            disabled={!isEditable}
            data-testid="passport-number"
          />
          <Form.Control.Feedback type="invalid">
            {hasDuplicates ? 'Duplicate seafarer' : 'Please enter a valid Passport Number.'}
          </Form.Control.Feedback>
        </Form.Group>

        <Form.Group as={Col} md="1">
          <div className="small-spinner">{isSpinnerActive ? <Spinner /> : null}</div>
        </Form.Group>
      </Form.Row>

      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Place of Issue*</Form.Label>
          <Form.Control
            type="text"
            name="place_of_issue"
            value={passport.place_of_issue}
            onChange={onInputChange}
            isInvalid={!!errors.place_of_issue}
            disabled={!isEditable}
            data-testid="fml-seafarer-passport-placeofissue"
          />
          <Form.Control.Feedback type="invalid">Please enter Place of Issue.</Form.Control.Feedback>
        </Form.Group>

        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Country of Issue*</Form.Label>
          <CountryNationalityDropDownControl
            name={'country_id'}
            selectedValue={passport.country_id}
            dropDownValues={countries}
            onBlur={handleBlur}
            isInvalid={!!errors.country_id}
            onInputChange={onInputChange}
            disabled={!isEditable}
            testid="form-seafarer-passport-country-field"
          />
          <Form.Control.Feedback
            type="invalid"
            className={errors.country_id ? 'set-display-block' : ''}
          >
            Please enter Country of Issue.
          </Form.Control.Feedback>
        </Form.Group>
      </Form.Row>

      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Date of Issue*</Form.Label>
          <FleetDatePicker
            name="date_of_issue"
            id="passport_date_of_issue"
            value={passport.date_of_issue}
            onChange={onDateOfIssueChange}
            isInvalid={isInvalidDateofIssue}
            disabled={!isEditable}
            testID="fml-passport-dateofissue"
          />
          <Form.Control.Feedback
            type="invalid"
            className={isInvalidDateofIssue ? 'set-display-block' : ''}
          >
            {errors.date_of_issue}
          </Form.Control.Feedback>
        </Form.Group>

        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Date of Expiry*</Form.Label>
          <FleetDatePicker
            name="date_of_expiry"
            id="passport_date_of_expiry"
            value={passport.date_of_expiry}
            onChange={onDateOfExpiryChange}
            isInvalid={isInvalidDateofExpiry}
            disabled={!isEditable}
            testID="fml-passport-dateofexpiry"
          />
          <Form.Control.Feedback
            type="invalid"
            className={isInvalidDateofExpiry ? 'set-display-block' : ''}
          >
            {errors.date_of_expiry}
          </Form.Control.Feedback>
        </Form.Group>
      </Form.Row>
      <ConfirmActionModalView
        show={removeModalShow}
        onClose={() => setRemoveModalShow(false)}
        onConfirm={handleRemoveFile}
        title={'Confirm Deleting the File?'}
        message={'Are you sure deleting the uploaded file?'}
      />
    </div>
  );
};

PassportComponent.propTypes = {
  passport: PropTypes.object,
  countries: PropTypes.array,
  onPassportChange: PropTypes.func,
  errors: PropTypes.object,
  handleBlur: PropTypes.func,
  hasDuplicates: PropTypes.bool,
  isSpinnerActive: PropTypes.bool,
  isEditable: PropTypes.bool,
  index: PropTypes.number,
};

export default PassportComponent;
