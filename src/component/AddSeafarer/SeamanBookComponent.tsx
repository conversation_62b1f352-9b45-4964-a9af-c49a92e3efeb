/* eslint-disable react/prop-types */
import React, { useState } from 'react';
import { Form, Col } from 'react-bootstrap';
import FleetDatePicker from '../AddSeafarer/FleetDatePicker';
import ReferenceDropDownControl from '../AddSeafarer/ReferenceDropDownControl';
import CountryNationalityDropDownControl from '../AddSeafarer/CountryNationalityDropDownControl';
import ConfirmActionModalView from './ConfirmActionModalView';
import AddSeafarerOtherComponents from './AddSeafarerOtherComponents';
const { SelectedFileField, SelectFileButton } = AddSeafarerOtherComponents;
import Spinner from '../../component/common/Spinner';
import { RANK_VALUES } from '../../constants/formSections';
import { rankValues } from '../../model/utils';
import { COMMON_MESSAGES } from '../../constants/common-labels-and-messages';
import PropTypes from 'prop-types';

import ImageController from '../../controller/image-upload-controller';
const imageController = new ImageController();

const SeamanBookComponent = (props) => {
  const {
    seamanBook,
    countries,
    ports,
    errors,
    handleIsOriginalClick,
    onSeamanBookChange,
    handleBlur,
    hasDuplicates,
    isSpinnerActive,
    isEditable,
    rankId,
    ranks,
    index
  } = props;
  const [removeModalShow, setRemoveModalShow] = useState(false);

  const handleClick = () => {
    handleIsOriginalClick(seamanBook);
  };

  const onDateOfIssueChange = (value) => {
    const item = seamanBook;
    item.date_of_issue = value;
    if (rankName === RANK_VALUES.SUPY && (item.date_of_issue === '' || item.date_of_issue === null)) {
      delete item.date_of_issue;
    }
    onSeamanBookChange(item);
  };

  const onDateOfExpiryChange = (value) => {
    const item = seamanBook;
    item.date_of_expiry = value;
    if (rankName === RANK_VALUES.SUPY && (item.date_of_expiry === '' || item.date_of_expiry === null)) {
      delete item.date_of_expiry;
    }
    onSeamanBookChange(item);
  };

  const onInputChange = (event) => {
    const item = seamanBook;
    const field = event.target.name;

    item[field] = event.target.value;

    if (rankName === RANK_VALUES.SUPY && (item[field] === '' || item[field] === null)) {
      delete item[field];
    }

    if (field === 'country_id') {
      delete item.country;
      delete item.port_of_issue;
    }

    if (field === 'number') {
      props.onSeamansBookNumberChange(item, index);
    }

    if (field === 'has_no_date_of_expiry') {
      item[field] = event.target.checked;
      item['date_of_expiry'] = null;
    }

    onSeamanBookChange(item);
  };

  const handleSelectedFile = (file) => {
    if (file === undefined) {
      return;
    }

    const item = seamanBook;
    item['file'] = file;
    onSeamanBookChange(item);
  };

  let fileName;
  const file = seamanBook.file;
  if (file) {
    fileName = file.name;
  }

  let document;
  const docs = seamanBook.document ?? [];
  if (docs.length > 0) {
    document = docs[0];
    fileName = docs[0].name;
  }

  const handleRemoveFile = () => {
    setRemoveModalShow(false);
    const item = seamanBook;
    if (item.document && item.document.length > 0) {
      item.idToRemove = item.document[0].id;
    }
    delete item.file;
    delete item.document;
    onSeamanBookChange(item);
  };

  const downloadFile = async () => {
    window.open(
      `https://${window.location.hostname}/seafarer/document/${document.id}/seamans_book`,
    );
  };

  const isNoCopy = errors.file === 'A file is required';
  const isFileSizeTooLarge = errors?.file === 'File too large';
  const isUnsupportedFileType = errors?.file === 'Unsupported Format';
  const isInvalidDateofExpiry = !!errors?.date_of_expiry;
  const isInvalidDateofIssue = !!errors?.date_of_issue;
  const rankName = rankValues(rankId, ranks);

  const selectFileButton = isEditable ? (
    <SelectFileButton onSelectFile={handleSelectedFile} />
  ) : null;

  return (
    <div>
      <Form.Row>
        <Form.Group as={Col} md="6">
          <Form.Label data-testid="fml-seafarer-upload-seaman-book">
            {rankName == RANK_VALUES.SUPY
              ? 'Upload copy of Seaman’s Book (pdf, jpg, png, max size 12MB)'
              : 'Upload copy of Seaman’s Book* (pdf, jpg, png, max size 12MB)'}
          </Form.Label>
          {fileName !== undefined ? (
            <SelectedFileField
              fileName={fileName}
              onClick={downloadFile}
              disabled={!isEditable}
              onRemoveClick={() => setRemoveModalShow(true)}
            />
          ) : (
            selectFileButton
          )}
          {isNoCopy && (
            <Form.Control.Feedback type="invalid" className={'document-copy-feedback'}>
              Please provide a copy of Seaman’s Book.
            </Form.Control.Feedback>
          )}
          {isFileSizeTooLarge && (
            <Form.Control.Feedback type="invalid" className={'document-copy-feedback'}>
              {COMMON_MESSAGES.MESSAGE_FILE_SIZE_EXCEED}
            </Form.Control.Feedback>
          )}
          {isUnsupportedFileType && (
            <Form.Control.Feedback type="invalid" className={'document-copy-feedback'}>
              File Extension is not supported
            </Form.Control.Feedback>
          )}
        </Form.Group>

        <Form.Group as={Col} md="5">
          <Form.Label>Is National</Form.Label>
          <Form.Check
            type="radio"
            label={<span>Yes, this is the <strong>national</strong> Seaman's Book.</span>}
            defaultChecked={seamanBook.is_original}
            value={seamanBook.is_original}
            onClick={handleClick}
            name="is_original"
            id={`is_original${seamanBook.id || seamanBook.localId}`}
            disabled={!isEditable}
            data-testid="fml-seafarer-isoriginal"
          />
        </Form.Group>
      </Form.Row>

      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>
            {rankName == RANK_VALUES.SUPY ? 'Seaman’s Book Number' : 'Seaman’s Book Number*'}
          </Form.Label>
          <Form.Control
            type="text"
            name="number"
            value={seamanBook.number}
            onChange={onInputChange}
            isInvalid={!!errors.number || hasDuplicates}
            disabled={!isEditable}
            data-testid="seaman-book-number"
          />
          <Form.Control.Feedback type="invalid">
            {hasDuplicates ? 'Duplicate seafarer' : "Please enter valid Seaman's Book Number."}
          </Form.Control.Feedback>
        </Form.Group>

        <Form.Group as={Col} md="1">
          <div className="small-spinner">{isSpinnerActive ? <Spinner /> : null}</div>
        </Form.Group>

        <Form.Group as={Col} md={{ span: 5, offset: 0 }}>
          <Form.Label>{rankName == RANK_VALUES.SUPY ? 'Country' : 'Country*'}</Form.Label>
          <CountryNationalityDropDownControl
            name={'country_id'}
            selectedValue={seamanBook.country_id}
            dropDownValues={countries}
            onInputChange={onInputChange}
            onBlur={handleBlur}
            isInvalid={!!errors.country_id}
            disabled={!isEditable}
            testID="form-seafarer-seamanbook-country-field"
          />
          <Form.Control.Feedback
            type="invalid"
            className={errors.country_id ? 'set-display-block' : ''}
          >
            Please enter Country of Issue.
          </Form.Control.Feedback>
        </Form.Group>
      </Form.Row>

      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>
            {rankName == RANK_VALUES.SUPY ? 'Port of Issue' : 'Port of Issue*'}
          </Form.Label>
          <ReferenceDropDownControl
            name={'port_of_issue'}
            selectedValue={seamanBook.port_of_issue}
            dropDownValues={ports}
            onInputChange={onInputChange}
            onBlur={handleBlur}
            isInvalid={!!errors.port_of_issue}
            disabled={!isEditable}
            testID='fml-seafarer-port-of-issue'
          />
          <Form.Control.Feedback
            type="invalid"
            className={errors.port_of_issue ? 'set-display-block' : ''}
            data-testid="form-seafarer-seamanbook-port-field"
          >
            Please select Port of Issue.
          </Form.Control.Feedback>
        </Form.Group>

        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>
            {rankName == RANK_VALUES.SUPY ? 'Date of Issue' : 'Date of Issue*'}
          </Form.Label>
          <FleetDatePicker
            name="date_of_issue"
            id="seaman_date_of_issue"
            value={seamanBook.date_of_issue}
            onChange={onDateOfIssueChange}
            isInvalid={isInvalidDateofIssue}
            disabled={!isEditable}
            testID='fml-seafarer-date-of-issue'
          />
          <Form.Control.Feedback
            type="invalid"
            className={isInvalidDateofIssue ? 'set-display-block' : ''}
          >
            {errors.date_of_issue}
          </Form.Control.Feedback>
        </Form.Group>
      </Form.Row>

      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>
            {rankName == RANK_VALUES.SUPY ? 'Date of Expiry' : 'Date of Expiry*'}
          </Form.Label>
          {rankName != RANK_VALUES.SUPY && (
            <Form.Check
              type="checkbox"
              name="has_no_date_of_expiry"
              label="No Date of Expiry on Seamen's Book"
              disabled={!isEditable}
              checked={seamanBook.has_no_date_of_expiry}
              onChange={onInputChange}
            />
          )}
          <FleetDatePicker
            name="date_of_expiry"
            id="seaman_date_of_expiry"
            value={seamanBook.date_of_expiry}
            onChange={onDateOfExpiryChange}
            isInvalid={isInvalidDateofExpiry}
            disabled={!isEditable || seamanBook.has_no_date_of_expiry}
          />
          <Form.Control.Feedback
            type="invalid"
            className={isInvalidDateofExpiry ? 'set-display-block' : ''}
          >
            {errors.date_of_expiry}
          </Form.Control.Feedback>
        </Form.Group>
      </Form.Row>
      <ConfirmActionModalView
        show={removeModalShow}
        onClose={() => setRemoveModalShow(false)}
        onConfirm={handleRemoveFile}
        title={'Confirm Deleting the File?'}
        message={'Are you sure deleting the uploaded file?'}
      />
    </div>
  );
};

SeamanBookComponent.propTypes = {
  seamanBook: PropTypes.object,
  countries: PropTypes.array,
  ports: PropTypes.array,
  errors: PropTypes.object,
  handleIsOriginalClick: PropTypes.func,
  onSeamanBookChange: PropTypes.func,
  handleBlur: PropTypes.func,
  hasDuplicates: PropTypes.func,
  isSpinnerActive: PropTypes.bool,
  isEditable: PropTypes.bool,
  rankId: PropTypes.string,
  ranks: PropTypes.array,
  index: PropTypes.number,
};

export default SeamanBookComponent;
