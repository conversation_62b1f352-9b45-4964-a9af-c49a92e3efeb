import React, { useState, forwardRef } from 'react';
import { Form, Col, InputGroup } from 'react-bootstrap';
import { v4 as uuid } from 'uuid';

import AddSeafarerOtherComponents from './AddSeafarerOtherComponents';
import CountryNationalityDropDownControl from '../AddSeafarer/CountryNationalityDropDownControl';
import ConfirmActionModalView from './ConfirmActionModalView';
import PhoneInput from 'react-phone-input-2';
import PropTypes from 'prop-types';
import { COMMON_LABELS, COMMON_MESSAGES } from '../../constants/common-labels-and-messages';

const {
  AddAnotherComponent,
  SectionTitleComponent,
  SubSectionTitleComponent,
  SubSectionTitleRemoveComponent,
  SelectedFileField,
  SelectFileButton,
} = AddSeafarerOtherComponents;

import ImageController from '../../controller/image-upload-controller';
const imageController = new ImageController();

const defaultModal = {
  isVisible: false,
  type: undefined,
  uuid: undefined,
  index: undefined,
  title: undefined,
  message: undefined,
};

const AddSeafarerPersonalParticularsForm = forwardRef((props, ref) => {
  const { seafarer, dropDownData, handleBlur, onSeafarerChange, roleConfig, errors } = props;

  const countries = dropDownData.countries ?? [];
  const person = seafarer?.seafarer_person || {};
  const currentFamilyMembers = person.family_members ?? [{ localId: uuid() }];

  const isFamilyMembersHidden = roleConfig.seafarer.hidden.familyMembers;
  const isEditable = roleConfig.seafarer.edit.personalParticulars;
  const isContactDetailsHidden = seafarer.id && !seafarer.can_view_contacts;

  if (currentFamilyMembers.length === 0) {
    currentFamilyMembers.push({ localId: uuid() });
  }

  const [modal, setModal] = useState(defaultModal);

  const onInputChange = (event) => {
    const changedPerson = { ...person };
    const field = event.target.name;
    const value = event.target.value;
    changedPerson[field] = value;

    const numericFields = [
      'weight',
      'height',
      'number_of_children',
      'overall_size',
      'tshirt_size',
      'jacket_size',
      'shoe_size',
    ];

    if (numericFields.includes(field)) {
      const numericValue = value.replace(/\D/g, '');
      const n = Number(numericValue);
      if (n != 0) {
        changedPerson[field] = n;
      } else {
        changedPerson[field] = '';
        if (field === 'number_of_children') {
          changedPerson[field] = 0;
        }
      }
    }

    const changedSeafarer = {
      ...seafarer,
      seafarer_person: { ...changedPerson },
    };
    onSeafarerChange(changedSeafarer);
  };

  // Family members
  const handleAddFamilyMember = () => {
    const newFamilyMembers = [...currentFamilyMembers, { localId: uuid() }];
    const changedSeafarer = {
      ...seafarer,
      seafarer_person: { ...person, family_members: newFamilyMembers },
    };
    onSeafarerChange(changedSeafarer);
  };

  const handleRemoveFamilyMember = () => {
    const oldFamilyMembers = [...currentFamilyMembers];

    const updatedFamilyMembers = [...oldFamilyMembers].filter((item) => {
      return item.localId !== modal.uuid;
    });

    const changedSeafarer = {
      ...seafarer,
      seafarer_person: { ...person, family_members: updatedFamilyMembers },
    };

    onSeafarerChange(changedSeafarer);
  };

  const handleChangeFamilyMember = (familyMember) => {
    const oldFamilyMembers = [...currentFamilyMembers];
    const newFamilyMembers = oldFamilyMembers.map((item) => {
      if ((item.id && familyMember.id && item.id === familyMember.id) || (item.localId === familyMember.localId && familyMember.localId)) {
        item = familyMember;
      }
      return item;
    });

    const changedSeafarer = {
      ...seafarer,
      seafarer_person: { ...person, family_members: newFamilyMembers },
    };

    onSeafarerChange(changedSeafarer);
  };

  const handleRemoveModal = (index, uuid, type) => {
    const entity = type === 'family_member' ? 'Family Member' : 'Seafarer Photo';
    setModal({
      type,
      index,
      title: `Confirm Removing ${entity} ${index + 1}?`,
      message: `Are you sure removing ${entity} ${index + 1}?`,
      isVisible: true,
      uuid,
    });
  };

  const handleRemoveItem = () => {
    const { type } = modal;
    if (type == 'family_member') {
      handleRemoveFamilyMember();
    }
    if (type == 'profile_photo') {
      removeFile();
    }
    setModal(defaultModal);
  };

  // Seafarer's Photo
  const handleSelectedFile = (file) => {
    if (file === undefined) {
      return;
    }

    const changedSeafarer = { ...seafarer };
    changedSeafarer.seafarer_person.photo = file;
    onSeafarerChange(changedSeafarer);
  };

  let fileName;
  const file = seafarer.seafarer_person.photo;
  if (file) {
    fileName = file.name;
  }

  let document;
  const docs = seafarer.seafarer_person.photos ?? [];
  if (docs.length > 0) {
    document = docs[0];
    fileName = docs[0].name;
  }

  const downloadFile = async () => {
    window.open(`https://${window.location.hostname}/seafarer/document/${document.id}/seafarer_image`);
  };

  const removeFile = () => {
    const changedSeafarer = { ...seafarer };

    if (seafarer.seafarer_person.photos && seafarer.seafarer_person.photos.length > 0) {
      changedSeafarer.seafarer_person.photoIdToRemove = seafarer.seafarer_person.photos[0].id;
    }

    delete changedSeafarer.seafarer_person.photo;
    delete changedSeafarer.seafarer_person.photos;
    onSeafarerChange(changedSeafarer);
  };

  const photoSelectButton = isEditable ? (
    <SelectFileButton onSelectFile={handleSelectedFile} />
  ) : null;

  const addFamilyMemberComponent = isEditable ? (
    <AddAnotherComponent onClick={handleAddFamilyMember} />
  ) : null;

  return (
    <div className="add_seafarer_page">
      <div className="mt-5">*Required Fields</div>

      <SectionTitleComponent title={'Personal particulars'.toUpperCase()} />

      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Height</Form.Label>
          <InputGroup>
            <Form.Control
              type="text"
              name="height"
              onChange={onInputChange}
              value={person.height}
              isInvalid={!!errors?.seafarer_person?.height}
              disabled={!isEditable}
              data-testid="fml-seafarer-personal-particulars-height"
            />
            <InputGroup.Append>
              <InputGroup.Text className="unit-of-measure">cm</InputGroup.Text>
            </InputGroup.Append>
            <Form.Control.Feedback type="invalid" className={'document-copy-feedback'}>
              {errors?.seafarer_person?.height}
            </Form.Control.Feedback>
          </InputGroup>
        </Form.Group>

        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Weight</Form.Label>
          <InputGroup>
            <Form.Control
              type="text"
              name="weight"
              value={person.weight}
              isInvalid={!!errors?.seafarer_person?.weight}
              onChange={onInputChange}
              disabled={!isEditable}
              data-testid="fml-seafarer-personal-particulars-weight"
            />
            <InputGroup.Append>
              <InputGroup.Text className="unit-of-measure">kg</InputGroup.Text>
            </InputGroup.Append>
          </InputGroup>
          <Form.Control.Feedback type="invalid" className={'document-copy-feedback'}>
            {errors?.seafarer_person?.weight}
          </Form.Control.Feedback>
        </Form.Group>
      </Form.Row>

      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Overall Size</Form.Label>
          <Form.Control
            type="text"
            name="overall_size"
            value={person.overall_size}
            isInvalid={!!errors?.seafarer_person?.overall_size}
            onChange={onInputChange}
            disabled={!isEditable}
            data-testid="fml-seafarer-personal-particulars-overall-size"
          />
          <Form.Control.Feedback type="invalid" className={'document-copy-feedback'}>
            {errors?.seafarer_person?.overall_size}
          </Form.Control.Feedback>
        </Form.Group>

        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>T-shirt Size</Form.Label>
          <Form.Control
            type="text"
            name="tshirt_size"
            value={person.tshirt_size}
            isInvalid={!!errors?.seafarer_person?.tshirt_size}
            onChange={onInputChange}
            disabled={!isEditable}
            data-testid="fml-seafarer-personal-particulars-tshirt-size"
          />
          <Form.Control.Feedback type="invalid" className={'document-copy-feedback'}>
            {errors?.seafarer_person?.tshirt_size}
          </Form.Control.Feedback>
        </Form.Group>
      </Form.Row>

      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Jacket Size</Form.Label>
          <Form.Control
            type="text"
            name="jacket_size"
            value={person.jacket_size}
            isInvalid={!!errors?.seafarer_person?.jacket_size}
            onChange={onInputChange}
            disabled={!isEditable}
            data-testid="fml-seafarer-personal-particulars-jacket-size"
          />
          <Form.Control.Feedback type="invalid" className={'document-copy-feedback'}>
            {errors?.seafarer_person?.jacket_size}
          </Form.Control.Feedback>
        </Form.Group>

        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Shoe Size</Form.Label>
          <InputGroup>
            <Form.Control
              type="text"
              name="shoe_size"
              value={person.shoe_size}
              isInvalid={!!errors?.seafarer_person?.shoe_size}
              onChange={onInputChange}
              disabled={!isEditable}
              data-testid="fml-seafarer-personal-particulars-shoe-size"
            />
            <InputGroup.Append>
              <InputGroup.Text className="unit-of-measure">USA</InputGroup.Text>
            </InputGroup.Append>
          </InputGroup>
          <Form.Control.Feedback type="invalid" className={'document-copy-feedback'}>
            {errors?.seafarer_person?.shoe_size}
          </Form.Control.Feedback>
        </Form.Group>
      </Form.Row>

      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Smoking</Form.Label>
          <Form.Control
            as="select"
            name="smoking"
            value={person.smoking}
            selected={person.smoking}
            onChange={onInputChange}
            onBlur={handleBlur}
            disabled={!isEditable}
            data-testid="fml-seafarer-personal-particulars-smoking"
          >
            <option value={'unknown'}>Unknown</option>
            <option value={'yes'}>Yes</option>
            <option value={'no'}>No</option>
            ))
          </Form.Control>
        </Form.Group>

        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Vegetarian</Form.Label>
          <Form.Control
            as="select"
            name="vegetarian"
            value={person.vegetarian}
            selected={person.vegetarian}
            onChange={onInputChange}
            onBlur={handleBlur}
            disabled={!isEditable}
            data-testid="fml-seafarer-personal-particulars-vegetarian"
          >
            <option value={'unknown'}>Unknown</option>
            <option value={'yes'}>Yes</option>
            <option value={'no'}>No</option>
            ))
          </Form.Control>
        </Form.Group>
      </Form.Row>

      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Nearest Airport</Form.Label>
          <Form.Control
            type="text"
            name="nearest_airport"
            value={person.nearest_airport}
            isInvalid={!!errors?.seafarer_person?.nearest_airport}
            onChange={onInputChange}
            disabled={!isEditable}
            data-testid="fml-seafarer-personal-particulars-nearest-airport"
          />
          <Form.Control.Feedback type="invalid">{errors?.seafarer_person?.nearest_airport}</Form.Control.Feedback>
        </Form.Group>

        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label data-testid="fml-seafarer-personal-particulars-seafarer-photo">{`Seafarer’s Photo ${COMMON_LABELS.LABEL_FILE_SIZE_5MB_AND_EXTENSION}`}</Form.Label>
          {fileName !== undefined ? (
            <SelectedFileField
              fileName={fileName}
              onClick={downloadFile}
              disabled={!isEditable}
              onRemoveClick={handleRemoveModal.bind(this, null, null, 'profile_photo')}
              
            />
          ) : photoSelectButton}
          {errors?.seafarer_person?.photo === 'File too large' && (
            <Form.Control.Feedback type="invalid" className={'document-copy-feedback'}>
              {COMMON_MESSAGES.MESSAGE_FILE_SIZE_EXCEED_5MB}
            </Form.Control.Feedback>
          )}
          {errors?.seafarer_person?.photo === 'Unsupported Format' && (
            <Form.Control.Feedback type="invalid" className={'document-copy-feedback'}>
              {COMMON_MESSAGES.MESSAGE_FILE_EXTENSION_NOT_SUPPORT}
            </Form.Control.Feedback>
          )}
        </Form.Group>
      </Form.Row>

      <SectionTitleComponent title={'Marital status, spouse and children'.toUpperCase()} />

      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Marital Status</Form.Label>
          <Form.Control
            as="select"
            name="marital_status"
            value={person.marital_status}
            selected={person.marital_status}
            onChange={onInputChange}
            onBlur={handleBlur}
            disabled={!isEditable}
            data-testid="fml-seafarer-personal-particulars-marital-status"
          >
            <option value={'single'}>Single</option>
            <option value={'partner'}>Partner</option>
            <option value={'married'}>Married</option>
            <option value={'divorced'}>Divorced</option>
            <option value={'legally_separated'}>Legally Separated</option>
            ))
          </Form.Control>
        </Form.Group>

        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Surname of Spouse</Form.Label>
          <Form.Control
            type="text"
            name="surname_of_spouse"
            value={person.surname_of_spouse}
            isInvalid={!!errors?.seafarer_person?.surname_of_spouse}
            onChange={onInputChange}
            disabled={!isEditable}
            data-testid="fml-seafarer-personal-particulars-surname-spouse"
          />
          <Form.Control.Feedback type="invalid">{errors?.seafarer_person?.surname_of_spouse}</Form.Control.Feedback>
        </Form.Group>
      </Form.Row>

      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Given names of Spouse</Form.Label>
          <Form.Control
            type="text"
            name="name_of_spouse"
            value={person.name_of_spouse}
            isInvalid={!!errors?.seafarer_person?.name_of_spouse}
            onChange={onInputChange}
            disabled={!isEditable}
            data-testid="fml-seafarer-personal-particulars-given-spouse"
          />
          <Form.Control.Feedback type="invalid">{errors?.seafarer_person?.name_of_spouse}</Form.Control.Feedback>
        </Form.Group>

        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Number of Children</Form.Label>
          <Form.Control
            type="number"
            name="number_of_children"
            value={person.number_of_children}
            isInvalid={!!errors?.seafarer_person?.number_of_children}
            onChange={onInputChange}
            disabled={!isEditable}
            data-testid="fml-seafarer-personal-particulars-number-children"
          />
          <Form.Control.Feedback type="invalid" className={'document-copy-feedback'}>
            {errors?.seafarer_person?.number_of_children}
          </Form.Control.Feedback>
        </Form.Group>
      </Form.Row>

      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Children’s Name and Date of Birth</Form.Label>
          <Form.Control
            as="textarea"
            rows="3"
            maxLength={1000}
            name="children_names"
            isInvalid={!!errors?.seafarer_person?.children_names}
            value={person.children_names}
            onChange={onInputChange}
            disabled={!isEditable}
            data-testid="fml-seafarer-personal-particulars-children-name"
          />
          <Form.Control.Feedback type="invalid">{errors?.seafarer_person?.children_names}</Form.Control.Feedback>
        </Form.Group>
      </Form.Row>

      <div id="Personal Particulars Next of Kin" ref={ref}>
        {isFamilyMembersHidden ? null : (
          <SectionTitleComponent title={'Next of kin'.toUpperCase()} />
        )}

        {isFamilyMembersHidden
          ? null
          : currentFamilyMembers.map((familyMember, index) => {
              const sectionTitle = `OTHER NEXT OF KIN ${index + 1}`;

              let error = {};
              if (errors?.seafarer_person?.family_members) {
                error = errors.seafarer_person.family_members[index] ?? {};
              }

              return (
                <div key={familyMember.localId}>
                  {index === 0 ? null : (
                    <SubSectionTitleRemoveComponent
                      index={index}
                      title={sectionTitle}
                      isEditable={true}
                      onClick={handleRemoveModal.bind(
                        this,
                        index,
                        familyMember.localId,
                        'family_member',
                      )}
                      disabled={!isEditable}
                    />
                  )}
                  <FamilyMemberComponent
                    countries={countries}
                    familyMember={familyMember}
                    onChangeFamilyMember={handleChangeFamilyMember}
                    error={error}
                    isEditable={isEditable}
                    isContactDetailsHidden={isContactDetailsHidden}
                  />
                </div>
              );
            })}
        {currentFamilyMembers.length < 5 ? addFamilyMemberComponent : null}
      </div>

      <ConfirmActionModalView
        show={modal.isVisible}
        onClose={() => setModal(defaultModal)}
        onConfirm={handleRemoveItem}
        title={modal.title}
        message={modal.message}
      />
    </div>
  );
});

AddSeafarerPersonalParticularsForm.propTypes = {
  seafarer: PropTypes.object,
  errors: PropTypes.object,
  handleBlur: PropTypes.func,
  onSeafarerChange: PropTypes.func,
  dropDownData: PropTypes.object,
  roleConfig: PropTypes.object,
};

const FamilyMemberComponent = (props) => {
  const {
    familyMember,
    countries,
    onChangeFamilyMember,
    handleBlur,
    error,
    isEditable,
    isContactDetailsHidden,
  } = props;

  let address = familyMember?.address ? familyMember.address : {};

  const onInputChange = (event) => {
    const changedFamilyMember = { ...familyMember };
    const field = event.target.name;
    const value = event.target.value;
    changedFamilyMember[field] = value;
    if (field === 'email') {
      changedFamilyMember[field] = value.trim();
    }
    onChangeFamilyMember(changedFamilyMember);
  };

  const onInputAddressChange = (event) => {
    const changedAddress = { ...address };
    const field = event.target.name;
    const value = event.target.value;
    changedAddress[field] = value;
    const changedFamilyMember = { ...familyMember, address: { ...changedAddress } };
    onChangeFamilyMember(changedFamilyMember);
  };

  const onPhoneChange = (field, value) => {
    onInputChange({ target: { name: field, value: value.trim() } });
  };

  return (
    <>
      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Surname of Next of Kin</Form.Label>
          <Form.Control
            type="text"
            name="surname"
            value={familyMember.surname}
            isInvalid={!!error.surname}
            onChange={onInputChange}
            disabled={!isEditable}
            data-testid="fml-seafarer-personal-particulars-nextofkin-surname"
          />
          <Form.Control.Feedback type="invalid">{error?.surname}</Form.Control.Feedback>
        </Form.Group>

        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Given Name of Next of Kin</Form.Label>
          <Form.Control
            type="text"
            name="name"
            value={familyMember.name}
            isInvalid={!!error.name}
            onChange={onInputChange}
            disabled={!isEditable}
            data-testid="fml-seafarer-personal-particulars-nextofkin-given-name"
          />
          <Form.Control.Feedback type="invalid">{error?.name}</Form.Control.Feedback>
        </Form.Group>
      </Form.Row>

      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Relationship</Form.Label>
          <Form.Control
            type="text"
            name="relationship"
            value={familyMember.relationship}
            isInvalid={!!error.relationship}
            onChange={onInputChange}
            disabled={!isEditable}
            data-testid="fml-seafarer-personal-particulars-nextofkin-relationship"
          />
          <Form.Control.Feedback type="invalid">{error?.relationship}</Form.Control.Feedback>
        </Form.Group>

        {!isContactDetailsHidden && (
          <Form.Group as={Col} md={{ span: 5, offset: 1 }}  data-testid="fml-seafarer-personal-particulars-nextofkin-phonenumber">
            <Form.Label data-testid="fml-seafarer-personal-particulars-nextofkin-phonenumber-label">Telephone Number</Form.Label>
            <PhoneInput
              preferredCountries={['in', 'ph', 'hk', 'cn', 'kr', 'lk', 'id', 'vn', 'ua']}
              value={familyMember.telephone}
              countryCodeEditable={true}
              enableSearch
              onBlur={handleBlur}
              inputClass={!!error.telephone && 'tel-input-error'}
              buttonClass={!!error.telephone && 'border-error-color'}
              searchPlaceholder="Search"
              disableSearchIcon
              placeholder=""
              onChange={onPhoneChange.bind(this, 'telephone')}
              disabled={!isEditable}
            />
            <Form.Control.Feedback
              type="invalid"
              className={!!error.telephone && 'set-display-block'}
            >
              Invalid Number.
            </Form.Control.Feedback>
          </Form.Group>
        )}
      </Form.Row>

      {!isContactDetailsHidden && (
        <>
          {' '}
          <Form.Row>
            <Form.Group as={Col} md="5" data-testid="fml-seafarer-personal-particulars-nextofkin-mobilenumber">
              <Form.Label>Mobile Number</Form.Label>
              <PhoneInput
                preferredCountries={['in', 'ph', 'hk', 'cn', 'kr', 'lk', 'id', 'vn', 'ua']}
                value={familyMember.mobilephone}
                countryCodeEditable={true}
                enableSearch
                onBlur={handleBlur}
                inputClass={!!error.mobilephone && 'tel-input-error'}
                buttonClass={!!error.mobilephone && 'border-error-color'}
                searchPlaceholder="Search"
                disableSearchIcon
                placeholder=""
                onChange={onPhoneChange.bind(this, 'mobilephone')}
                disabled={!isEditable}
              />
              <Form.Control.Feedback
                type="invalid"
                className={!!error.mobilephone && 'set-display-block'}
              >
                Invalid Number.
              </Form.Control.Feedback>
            </Form.Group>

            <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
              <Form.Label>Email Address</Form.Label>
              <Form.Control
                type="text"
                name="email"
                value={familyMember.email}
                isInvalid={!!error.email}
                onBlur={handleBlur}
                onChange={onInputChange}
                disabled={!isEditable}
                data-testid="fml-seafarer-personal-particulars-nextofkin-emailaddress"
              />
              <Form.Control.Feedback type="invalid">Invalid email address.</Form.Control.Feedback>
            </Form.Group>
            </Form.Row>
          <Form.Row>
            <Form.Group as={Col} md="5">
              <Form.Label>Percentage</Form.Label>
              <Form.Control
                type="number"
                name="percentage"
                value={familyMember.percentage}
                isInvalid={!!error?.percentage}
                onChange={(e) =>
                  onInputChange({
                    target: {
                      name: 'percentage',
                      value: e.target.value ? parseInt(e.target.value) : '',
                    },
                  })
                }
                disabled={!isEditable}
                data-testid="fml-seafarer-personal-particulars-percentage"
              />
              <Form.Control.Feedback type="invalid" className={'document-copy-feedback'}>
                {error?.percentage}
              </Form.Control.Feedback>
            </Form.Group>
          </Form.Row>
          <SubSectionTitleComponent title={'Next of Kin’s Address'.toUpperCase()} />
          <Form.Row>
            <Form.Group as={Col} md="5">
              <Form.Label>Postal / Zip Code</Form.Label>
              <Form.Control
                type="text"
                name="postal_zip_code"
                value={address.postal_zip_code}
                isInvalid={!!error?.address?.postal_zip_code}
                onChange={onInputAddressChange}
                disabled={!isEditable}
                data-testid="fml-seafarer-personal-particulars-nextofkin-postal-zipcode"
              />
              <Form.Control.Feedback type="invalid">
                {error?.address?.postal_zip_code}
              </Form.Control.Feedback>
            </Form.Group>

            <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
              <Form.Label>Country</Form.Label>
              <CountryNationalityDropDownControl
                name={'country_id'}
                selectedValue={address.country_id}
                dropDownValues={countries}
                onBlur={handleBlur}
                onInputChange={onInputAddressChange}
                disabled={!isEditable}
                testID='fml-seafarer-personal-particulars-nextofkin-country'
              />
            </Form.Group>
          </Form.Row>
          <Form.Row>
            <Form.Group as={Col} md="5">
              <Form.Label>State / Province / District</Form.Label>
              <Form.Control
                type="text"
                name="address1"
                value={address.address1}
                isInvalid={!!error?.address?.address1}
                onChange={onInputAddressChange}
                disabled={!isEditable}
                data-testid="fml-seafarer-personal-particulars-nextofkin-state-province"
              />
              <Form.Control.Feedback type="invalid">
                {error?.address?.address1}
              </Form.Control.Feedback>
            </Form.Group>

            <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
              <Form.Label>Village / Town / City</Form.Label>
              <Form.Control
                type="text"
                name="address2"
                value={address.address2}
                isInvalid={!!error?.address?.address2}
                onChange={onInputAddressChange}
                disabled={!isEditable}
                data-testid="fml-seafarer-personal-particulars-nextofkin-village-town"
              />
              <Form.Control.Feedback type="invalid">
                {error?.address?.address2}
              </Form.Control.Feedback>
            </Form.Group>
          </Form.Row>
          <Form.Row>
            <Form.Group as={Col} md="5">
              <Form.Label>Building Name / Estate</Form.Label>
              <Form.Control
                type="text"
                name="address3"
                value={address.address3}
                isInvalid={!!error?.address?.address3}
                onChange={onInputAddressChange}
                disabled={!isEditable}
                data-testid="fml-seafarer-personal-particulars-nextofkin-building"
              />
              <Form.Control.Feedback type="invalid">
                {error?.address?.address3}
              </Form.Control.Feedback>
            </Form.Group>

            <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
              <Form.Label>Flat / Room / House Number/ Street</Form.Label>
              <Form.Control
                type="text"
                name="address4"
                value={address.address4}
                isInvalid={!!error?.address?.address4}
                onChange={onInputAddressChange}
                disabled={!isEditable}
                data-testid="fml-seafarer-personal-particulars-nextofkin-flat-room"
              />
              <Form.Control.Feedback type="invalid">
                {error?.address?.address4}
              </Form.Control.Feedback>
            </Form.Group>
          </Form.Row>{' '}
        </>
      )}
    </>
  );
};

FamilyMemberComponent.propTypes = {
  familyMember: PropTypes.object,
  countries: PropTypes.object,
  onChangeFamilyMember: PropTypes.func,
  handleBlur: PropTypes.func,
  error: PropTypes.object,
  isEditable: PropTypes.bool,
  isContactDetailsHidden: PropTypes.bool,
};

export default AddSeafarerPersonalParticularsForm;
