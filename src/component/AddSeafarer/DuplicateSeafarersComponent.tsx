import React from 'react';
import { Container } from 'react-bootstrap';
import { getDuplicateReason } from '../../util/form-utils';
import PropTypes from 'prop-types';

const SeafarerLinks = ({ seafarers, reason }) =>
  seafarers.map((seafarer, index) => {
    const seafarerId = seafarer.id;

    const hkid = seafarer.hkid;
    const rank = seafarer['seafarer_rank.value'];
    const office = seafarer['seafarer_reporting_office.value'];
    const officeString = office ? `Office: ${office}` : null;
    const number = seafarer.number ? `${reason}: ${seafarer.number}` : null;

    const fileds = [hkid, rank, officeString, number].filter((item) => {
      return item !== null;
    });

    let link = fileds.join(' / ');
    if (link === '' || link === null) {
      link = 'seafarer';
    }

    return (
      <li key={seafarerId}>
        <a
          key={seafarerId}
          style={{ color: '#1F4A70' }}
          href={`/seafarer/details/${seafarerId}/general`}
          target={'_blank'}
          rel="noopener noreferrer"
        >
          <u>{link}</u>
        </a>
      </li>
    );
  });

SeafarerLinks.propTypes = {
  seafarers: PropTypes.object,
  reason: PropTypes.string,
};

const DuplicateSeafarersComponent = ({ seafarers = [], currentSearchType, title }) => {
  if (seafarers.length === 0) {
    return null;
  }

  const reason = getDuplicateReason(currentSearchType);

  return (
    <Container className="duplicate-seafarers">
      <div className="title">Duplicate {title}</div>
      <div className="subtitle">
        Seafarer(s) with the same <span className="reason">{reason}</span> already exists:
      </div>
      <ul className="links">
        <SeafarerLinks seafarers={seafarers} reason={reason} />
      </ul>
    </Container>
  );
};

DuplicateSeafarersComponent.propTypes = {
  seafarers: PropTypes.array,
  currentSearchType: PropTypes.any,
  title: PropTypes.string,
};

export default DuplicateSeafarersComponent;
