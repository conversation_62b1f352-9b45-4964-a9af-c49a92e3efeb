import React, { useState } from 'react';
import { useHistory } from 'react-router-dom';
import { Form, Col, Button } from 'react-bootstrap';

import AddSeafarerOtherComponents from './AddSeafarerOtherComponents';
const { SectionTitleComponent } = AddSeafarerOtherComponents;

import AddDocumentModal from '../document/AddDocumentModal';
import PropTypes from 'prop-types';

import * as documentTypes from '../../constants/documentTypes';

const dropdownType = 'dropdown';

const AddSeafarerExperienceForm = (props) => {
  const {
    seafarer,
    handleBlur,
    onSeafarerChange,
    dropDownData,
    setModalMessage,
    handleClose,
    setFieldValue,
    roleConfig,
    errors,
  } = props;
  const seafarerPersonId = seafarer.seafarer_person_id;
  const isEditable = roleConfig.seafarer.edit.seafarerExperience;

  //seafarer document
  const [showDocumentModal, setShowDocumentModal] = useState(false);
  const [iceExpereinceDocumentCount, setIceExpereinceDocumentCount] = useState(
    seafarer.ice_conditions_experience ? 1 : 0,
  );

  const history = useHistory();

  const onInputChange = (event, type = undefined) => {
    const changedSeafarer = {...seafarer};
    const field = event.target.name;
    const value = event.target.value;
    const isDropdownBool = type == dropdownType;
    changedSeafarer[field] = isDropdownBool ? JSON.parse(value) : value;
    onSeafarerChange(changedSeafarer);
  };

  const onClickUploadIceExperienceBtn = () => {
    setShowDocumentModal(true);
  };

  const onClickEditIceExperienceBtn = () => {
    if (handleClose) {
      handleClose('PAGE_CLOSE_TRIGGER_FROM_EXPERIENCE_FORM');
    }
  };

  const onAddDocumentModelSubmitCallback = (values, err) => {
    //render the add ice experience as edit
    if (values) {
      setIceExpereinceDocumentCount(1);
    } else {
      //form submit for creating the document failed
      setModalMessage({
        header: 'Create Document Fail.',
        message: err?.message ?? 'Create Ice/Polar Code Sea Service Document Fail.',
      });

      throw Error('Create Ice/Polar Code Sea Service Document Fail.');
    }
  };

  const onSubmitCreateIceExperienceDocumentForm = async (values) => {
    //is this neeeded?
    setFieldValue('ice_experience_doc', values);

    const changedSeafarer = {...seafarer};
    const field = 'ice_experience_doc';
    changedSeafarer[field] = values;
    onSeafarerChange(changedSeafarer);
  };

  return (
    <div className="add_seafarer_page">
      <div className="mt-5">*Required Fields</div>

      <SectionTitleComponent title={'Experience Details'.toUpperCase()} />

      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Ice/Polar Code Sea Service</Form.Label>
          <div>
            {isEditable &&
              (iceExpereinceDocumentCount >= 1 ? (
                <span>
                  <Button
                    variant="outline-primary"
                    size="sm"
                    className="pl-3 pr-3"
                    onClick={onClickEditIceExperienceBtn}
                    data-testid="edit-ice-experience-btn"
                  >
                    Edit
                  </Button>

                  <span className="pl-3 pr-3">1 or more document(s) uploaded</span>
                </span>
              ) : (
                <Button
                  variant="outline-primary"
                  size="sm"
                  className="pl-3 pr-3"
                  onClick={onClickUploadIceExperienceBtn}
                  data-testid="upload-ice-experience-btn"
                >
                  Upload
                </Button>
              ))}
          </div>
        </Form.Group>
      </Form.Row>
      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Framo Experience</Form.Label>
          <Form.Control
            as="select"
            name="framo_experience"
            value={seafarer.framo_experience}
            selected={seafarer.framo_experience}
            onChange={(e) => onInputChange(e, dropdownType)}
            onBlur={handleBlur}
            disabled={!isEditable}
            data-testid="framo_experience_field"
          >
            <option value="">Please select</option>
            <option value={true}>Yes</option>
            <option value={false}>No</option>
            ))
          </Form.Control>
        </Form.Group>

        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Show FML Years Experience in Biodata</Form.Label>
          <Form.Control
            as="select"
            name="show_fml_experience"
            value={seafarer.show_fml_experience}
            selected={seafarer.show_fml_experience}
            onChange={(e) => onInputChange(e, dropdownType)}
            onBlur={handleBlur}
            disabled={!isEditable}
            data-testid="show_fml_experience_field"
          >
            <option value={true}>Yes</option>
            <option value={false}>No</option>
            ))
          </Form.Control>
        </Form.Group>
      </Form.Row>

      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Show Cargo Handling Experience in Biodata</Form.Label>
          <Form.Control
            as="select"
            name="show_cargo_experience"
            value={seafarer.show_cargo_experience}
            selected={seafarer.show_cargo_experience}
            onChange={(e) => onInputChange(e, dropdownType)}
            onBlur={handleBlur}
            data-testid="show_cargo_experience"
            disabled={!isEditable}
          >
            <option value={true}>Yes</option>
            <option value={false}>No</option>
            ))
          </Form.Control>
        </Form.Group>

        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Additional Experience</Form.Label>
          <Form.Control
            as="textarea"
            maxLength={1000}
            rows="3"
            name="additional_experience"
            value={seafarer.additional_experience ?? ''}
            isInvalid={!!errors?.additional_experience}
            onChange={onInputChange}
            data-testid="additional-experience-text-area"
            disabled={!isEditable}
          />
          <Form.Control.Feedback type="invalid">{errors?.additional_experience}</Form.Control.Feedback>
        </Form.Group>
      </Form.Row>

      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Cargo Handling Experience</Form.Label>
          <Form.Control
            as="textarea"
            maxLength={1000}
            rows="3"
            name="cargo_experience"
            value={seafarer.cargo_experience ?? ''}
            isInvalid={!!errors?.cargo_experience}
            onChange={onInputChange}
            data-testid="cargo_handling_experience_field"
            disabled={!isEditable}
          />
          <Form.Control.Feedback type="invalid">{errors?.cargo_experience}</Form.Control.Feedback>
        </Form.Group>
      </Form.Row>
      {showDocumentModal ? (
        <AddDocumentModal
          targetId={documentTypes.DOC_FORM_IDS.USER_DEFINED_DOCUMENT}
          setShowDocumentModal={setShowDocumentModal}
          onSubmitCallback={onAddDocumentModelSubmitCallback}
          seafarerPersonId={seafarerPersonId}
          dropdownData={dropDownData}
          history={history}
          disableDocumentTypeDropdown={true}
          overrideOnSubmitRedirectPath={null}
          onSubmit={onSubmitCreateIceExperienceDocumentForm}
        ></AddDocumentModal>
      ) : (
        ''
      )}
    </div>
  );
};

AddSeafarerExperienceForm.propTypes = {
  seafarer: PropTypes.object,
  errors: PropTypes.object,
  handleBlur: PropTypes.func,
  onSeafarerChange: PropTypes.func,
  dropDownData: PropTypes.object,
  roleConfig: PropTypes.object,
  setModalMessage: PropTypes.func,
  handleClose: PropTypes.func,
  setFieldValue: PropTypes.func,
};

export default AddSeafarerExperienceForm;
