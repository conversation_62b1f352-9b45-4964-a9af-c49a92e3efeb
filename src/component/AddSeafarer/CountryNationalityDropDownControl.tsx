import React from 'react';
import { Typeahead, Menu, MenuItem } from 'react-bootstrap-typeahead';
import PropTypes from 'prop-types';

const DropDownSearchControl = ({
  dropDownValues,
  name,
  onInputChange,
  selectedValue,
  disabled,
  isInvalid,
  placeholder = 'Please select',
  testID,
}) => {
  const onChange = (option) => {
    const id = option.length > 0 ? option[0].id : undefined;
    const result = { target: { name, value: id } };
    onInputChange(result);
  };

  const getOptionById = (id) => {
    const result = (dropDownValues ?? []).filter((item) => {
      return item.id === id;
    });
    return result.length > 0 ? result[0] : undefined;
  };

  const selected = getOptionById(selectedValue);

  function getFlag(isoCode) {
    return typeof String.fromCodePoint !== 'undefined'
      ? isoCode
          ?.toUpperCase()
          ?.replace(/./g, (char) => String.fromCodePoint(char.charCodeAt(0) + 127397))
      : isoCode;
  }

  const getLabel = (option) => {
    const title = option.value ?? option;
    const code = option.alpha2_code;
    if (code) {
      const flag = getFlag(code);
      return `${title} (${flag})`;
    }
    return title;
  };

  const getLowerCode = (result) => {
    return result?.toLowerCase();
  };

  const styleFlag = {
    width: '24px',
    height: '100%',
    padding: '0 0 0 8px',
    borderRadius: '3px 0 0 3px',
  };
  return (
    <div style={{ position: 'relative' }}>
      <Typeahead
        id={name}
        labelKey={getLabel}
        onChange={onChange}
        options={dropDownValues}
        inputProps={{ 'data-testid': testID }}
        placeholder={placeholder}
        selected={selected ? [selected] : []}
        disabled={disabled}
        isInvalid={isInvalid}
        className="flag-item"
        renderMenu={(results, menuProps) => {
          return (
            <Menu className="react-tel-input" id={menuProps.id} key={menuProps.id}>
              {results.map((result, index) => (
                <MenuItem key={result.id + result.alpha2_code} option={result} position={index}>
                  {result && (
                    <div>
                      <div style={{ display: 'inline-block' }}>
                        <div className={`flag ${getLowerCode(result.alpha2_code)}`} />
                      </div>
                      <div style={{ display: 'inline-block', marginLeft: '10px' }}>
                        {result?.paginationOption === true
                          ? 'Display additional results...'
                          : `${getLabel(result)}`}
                      </div>
                    </div>
                  )}
                </MenuItem>
              ))}
            </Menu>
          );
        }}
      />
      <div style={{ position: 'absolute', top: '0', bottom: '0' }}>
        <div className="react-tel-input" style={styleFlag}>
          {selected && (
            <div
              style={{ position: 'absolute', top: '50%', marginTop: '-5px' }}
              className={`flag ${getLowerCode(selected.alpha2_code)}`}
            />
          )}
        </div>
      </div>
    </div>
  );
};

DropDownSearchControl.propTypes = {
  dropDownValues: PropTypes.array,
  name: PropTypes.string,
  onInputChange: PropTypes.func,
  selectedValue: PropTypes.number,
  disabled: PropTypes.bool,
  isInvalid: PropTypes.bool,
  placeholder: PropTypes.string,
  testID: PropTypes.string,
};

export default DropDownSearchControl;
