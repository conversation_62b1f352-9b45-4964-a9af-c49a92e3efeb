import React, { useState, useEffect, useRef, useLayoutEffect, useMemo } from 'react';
import ReactDOM from 'react-dom';
import { Form, Col, Container, Row, Modal, Button } from 'react-bootstrap';
import FleetDatePicker from '../AddSeafarer/FleetDatePicker';
import AddSeafarerOtherComponents from './AddSeafarerOtherComponents';
import ConfirmActionModalView from './ConfirmActionModalView';
import CountryNationalityDropDownControl from '../AddSeafarer/CountryNationalityDropDownControl';
import { ERROR_MAPPINGS } from '../../constants/formSections';
import { COMMON_LABELS, COMMON_MESSAGES } from '../../constants/common-labels-and-messages';
import BankNamesDropDownControl from '../AddSeafarer/BankNamesDropDownControl.tsx';
import <PERSON>Field from './PhoneField';
import styleGuide from '../../styleGuide';
import { <PERSON>readcrumbHeader } from '../BreadcrumpHeader';
import BottomButton from '../advanced_search/BottomButton';
import { Formik, validateYupSchema, yupToFormErrors } from 'formik';
import { useHistory, useParams } from 'react-router-dom';
import AddSeafarerController from '../../controller/add-seafarer-controller';
import { cloneDeep, omitBy, orderBy } from 'lodash';
import Spinner from '../common/Spinner';
import ErrorsListComponent from '../ErrorsListComponent';
import seafarerSchema from '../../model/SeafarerSchemaValidation';
import seafarerService from '../../service/seafarer-service';
import { capitalizeArgs, Dash } from '../../model/utils';
import AccessHandlerWrapper from '../common/AccessHandlerWrapper';
import { scrollToSection } from '../../util/view-utils';
import ErrorAlert from '../common/ErrorAlert';
import PropTypes from 'prop-types';
import { useAccess } from '../common/Access';
const { Icon } = styleGuide;

const ADDRESS_KEYS = ['account_holder_address', 'bank_address'];

const PROCEED_OTHER_TAB_FAILED_MESSAGE = {
  header: 'Fill in required fields before proceed',
  message: 'Please fill in all the required fields',
};

const ERROR_FAILED_TO_SAVE_MESSAGE = {
  header: 'Save seafarer bank details failed',
  message: 'Save seafarer bank details failed',
};
const {
  AddAnotherComponent,
  SectionTitleComponent,
  SubSectionTitleComponent,
  SubSectionTitleRemoveComponent,
  SelectedFileField,
  SelectFileButton,
} = AddSeafarerOtherComponents;

const removeSpecialCharacters = (text) => {
  return text ? text.replace(/[`~!@#$%^&*()_|+=?;:'",.<>{}[\]\\/-]/gi, '') : '';
};

//#region util functions
export const applyPersonAccountInfo = (newBankAccount, seafarer_person) => {
  if (newBankAccount.seafarer_is_account_holder) {
    Object.assign(newBankAccount, {
      account_holder_first_name: seafarer_person.first_name,
      account_holder_middle_name: seafarer_person.middle_name,
      account_holder_last_name: seafarer_person.last_name,
      account_holder_date_of_birth: seafarer_person.date_of_birth,
      account_holder_gender: seafarer_person.gender,
      account_holder_nationality_id: seafarer_person.nationality_id,
    });
  }
};

export const getBankAccounts = (seafarer_person) => {
  const bankAccounts = seafarer_person?.bank_accounts ?? [
    {
      localId: 1,
    },
  ];

  if (bankAccounts.length === 0) {
    bankAccounts.push({
      localId: 1,
    });
  }

  let primaryAccountExists = false;
  bankAccounts.forEach((account) => {
    // apply person info
    if (account.seafarer_is_account_holder === undefined) {
      account.seafarer_is_account_holder = true;
    }
    applyPersonAccountInfo(account, seafarer_person);
    // give each account a local id
    if (!account.localId) {
      account.localId = getNextId(bankAccounts);
    }
    if (account.is_primary_payroll_account === undefined) {
      account.is_primary_payroll_account = false;
    }
    if (account.is_primary_payroll_account) primaryAccountExists = true;
  });

  // set primary account
  if (!primaryAccountExists) {
    bankAccounts[0].is_primary_payroll_account = true;
  }

  return bankAccounts;
};

export const getNextId = (bankAccounts) => {
  const arr = bankAccounts.map(({ localId }) => localId).filter((r) => r);
  if (arr.length === 0) {
    return 1;
  }
  return Math.max(...arr) + 1;
};

const isIndiaAccount = (bankAccount, countries) => {
  const INDIA_COUNTRY_ID = countries.find((i) => i.alpha2_code === 'IN')?.id;

  if (!bankAccount.bank_address) {
    bankAccount.ifsc_number = null;
    return false;
  }
  const { country_id } = bankAccount.bank_address;
  return country_id === INDIA_COUNTRY_ID;
};

const updateAddressWithInput = ({ inputField, inputValue, addressKey, bankAccount }) => {
  const newAddress = {};
  const address = bankAccount[addressKey];
  if (address) {
    Object.assign(newAddress, address);
  }
  const subKey = inputField.substr(addressKey.length + 1);
  newAddress[subKey] = inputValue;
  bankAccount[addressKey] = newAddress;
};

const mapInputChangeToBankAccount = ({ bankAccount, field, value }, countries, nationalities) => {
  const changedAccount = {
    ...bankAccount,
  };

  const addressKey = ADDRESS_KEYS.find((key) => field.startsWith(key));
  if (addressKey) {
    updateAddressWithInput({
      inputField: field,
      inputValue: value,
      addressKey,
      bankAccount: changedAccount,
    });
  } else {
    changedAccount[field] = value;
  }

  if (!isIndiaAccount(bankAccount, countries)) {
    delete changedAccount.ifsc_number;
  }

  return changedAccount;
};

const mapBankAccountToInputValues = (person, bankAccount) => {
  const inputValues = {
    localId: bankAccount.bankAccount,
  };
  if (inputValues.seafarer_is_account_holder) {
    [
      'account_holder_first_name',
      'account_holder_middle_name',
      'account_holder_last_name',
      'account_holder_date_of_birth',
      'account_holder_gender',
      'account_holder_nationality_id',
    ].forEach((key) => {
      const personKey = key.substring('account_holder_'.length);
      inputValues[key] = inputValues[key] ?? person[personKey];
    });
  }
  Object.entries(bankAccount).forEach(([key, value]) => {
    inputValues[key] = value;
  });

  if (!inputValues.account_holder_address) {
    inputValues.account_holder_address = {};
  }

  if (!inputValues.bank_address) {
    inputValues.bank_address = {};
  }

  return inputValues;
};

const mapInputErrors = (errors) => {
  if (
    !errors?.seafarer_person?.bank_accounts ||
    errors?.seafarer_person?.bank_accounts?.length === 0
  ) {
    return [];
  }
  return errors.seafarer_person.bank_accounts.map((account) => {
    if (!account) return {};
    return Object.entries(account).reduce((map, [key, value]) => {
      if (ADDRESS_KEYS.includes(key)) {
        ['postal_zip_code', 'country_id', 'address1', 'address2', 'address3', 'address4'].forEach(
          (addressField) => {
            map[`${key}_${addressField}`] = value[addressField];
          },
        );
      } else {
        map[key] = value;
      }
      return map;
    }, {});
  });
};

//#endregion util functions
let cloneSeafarerSchema = cloneDeep(seafarerSchema);

/* eslint-disable react/prop-types */
const AddSeafarerBankAccounts = () => {
  const history = useHistory();
  const { roleConfig } = useAccess();
  let { seafarerId } = useParams();
  const [loading, setLoading] = useState(false);
  const [seafarer, setSeafarer] = useState({
    seafarer_person: {},
  });
  const person = seafarer?.seafarer_person ? seafarer.seafarer_person : {};
  const firstName = person.first_name ?? '';
  const lastName = person.last_name ?? '';
  const middleName = person.middle_name ?? '';
  const fullName = capitalizeArgs(firstName, middleName, lastName) || Dash;
  const rank = seafarer?.seafarer_rank || {};
  const rankTitle = rank?.unit ? `(${rank?.unit})` : Dash;
  const hkid = seafarer.hkid ? `(${seafarer.hkid})` : Dash;
  const hasRoleAccess = roleConfig.seafarer.edit.bankAccount;
  const bankAccounts = seafarer.seafarer_person?.bank_accounts ?? [];

  const headingTitle = `${fullName} ${rankTitle} ${hkid} / Edit Bank Accounts`;

  const defaultDropDownValue = {
    countries: [],
    nationalities: [],
    offices: [],
    banks: [],
  };
  const [dropDownData, setDropDownData] = useState(defaultDropDownValue);
  const [modalMessage, setModalMessage] = useState(null);
  const [modalErrorData, setModalErrorData] = useState(null);
  const [userDefinedData, setUserDefinedData] = useState(null);
  const [error, setError] = useState();
  const [subSection, setSubSection] = useState(null);
  const [removeModalShow, setRemoveModalShow] = useState(false);
  const [removeModalTitle, setRemoveModalTitle] = useState(null);
  const [removeModalMessage, setRemoveModalMessage] = useState(null);
  const [removeItemKey, setRemoveItemKey] = useState<{ key?: number; index?: number }>({});
  const bankAccSectionRefs = useRef([]);
  const bankAccountsRef = useRef(null);

  const { current: controller } = useRef(
    new AddSeafarerController({ isEditBankAccountDetails: true }),
  );

  useEffect(() => {
    bankAccSectionRefs.current = bankAccSectionRefs.current.slice(0, bankAccounts.length);
  }, [bankAccounts]);

  const breadCrumbsItems = useMemo(
    () => [
      {
        title: 'Seafarer',
        label: 'To List Page',
        link: '/seafarer/passed',
      },
      {
        title: headingTitle ?? '- - -',
        label: 'Details',
        link: '#',
      },
    ],
    [headingTitle],
  );

  useLayoutEffect(() => {
    if (bankAccounts.length <= 1) {
      window.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
      return;
    }
    const dom = ReactDOM.findDOMNode(bankAccSectionRefs.current[bankAccounts.length - 1]);
    const heightOffset = -50;
    const y = dom?.getBoundingClientRect().top + window.scrollY + heightOffset;

    window.scrollTo({
      top: y,
      behavior: 'smooth',
    });
  }, [bankAccounts.length]);

  useLayoutEffect(() => {
    if (!subSection) return;
    handleScroll();
  }, [subSection]);

  const handleScroll = () => {
    const ErrorScrollMapping = {
      'bank-accounts': bankAccountsRef,
    };
    scrollToSection(ErrorScrollMapping[subSection]);
    setSubSection(null);
  };

  const onClose = () => {
    history.push(`/seafarer/details/${seafarerId}/account-details`);
  };
  const selectedBank = useRef([]);
  const isBankAccountEditable = roleConfig.seafarer.edit.bankAccount;

  function loadSeafarer(id) {
    (async () => {
      setLoading(true);
      try {
        const [
          seafarerData,
          dropDownDataResponse,
          seafarerReportingOfficeDropDownData,
          manningAgentDropDownData,
          bankNameResponse,
        ] = await Promise.all([
          controller.loadSeafarer(id),
          controller.loadDropDownData(),
          controller.loadSeafarerReportingOfficeDropDownData(),
          controller.loadManningAgentDropDownData(),
          controller.loadBankNames(),
        ]);

        if (id && Object.keys(seafarerData).length === 0) {
          setError('404 page error');
          return;
        }

        seafarerData?.seafarer_person?.bank_accounts?.forEach((account) => {
          applyPersonAccountInfo(account, seafarerData.seafarer_person);
        });

        setSeafarer(seafarerData);

        controller.seafarerChanges = seafarerData;
        controller.initialValues = cloneDeep(seafarerData);
        dropDownDataResponse['offices'] = seafarerReportingOfficeDropDownData;
        dropDownDataResponse['manningAgents'] = manningAgentDropDownData;
        dropDownDataResponse['bankNames'] = bankNameResponse.data;
        await loadDocsData(seafarerId, '?type=user_defined_document', dropDownDataResponse);

        setDropDownData(dropDownDataResponse);
      } catch (error) {
        setError(error.message);
        console.log('## error', error);
      } finally {
        setLoading(false);
      }
    })();
  }
  const _setUserDefinedData = (responseObj, userDefinedDocumentTypes = []) => {
    const idOfCancelledBankAccountChequeType = userDefinedDocumentTypes.find(
      (doc) => doc.value === 'Bank Account Cancelled Cheque',
    )?.id;
    if (responseObj.user_defined_document && idOfCancelledBankAccountChequeType) {
      responseObj.user_defined_document = responseObj.user_defined_document.filter(
        (doc) =>
          doc.seafarer_doc_user_defined_document.user_defined_document_type_id ===
          idOfCancelledBankAccountChequeType,
      );
      if (responseObj.user_defined_document.length) {
        responseObj.user_defined_document = orderBy(
          responseObj.user_defined_document,
          ['created_at'],
          ['desc'],
        );
        setUserDefinedData(responseObj.user_defined_document[0]);
      }
    }
  };
  const loadDocsData = async (seafarerPersonId, queryParam, dropDownDataResponse) => {
    const { data } = await seafarerService.getSeafarerDocumentsList(seafarerPersonId, queryParam);
    let responseObj = {};
    for (const dataObj of data) {
      if (responseObj[dataObj.type]) {
        responseObj[dataObj.type].push(dataObj);
      } else {
        responseObj[dataObj.type] = [dataObj];
      }
    }
    _setUserDefinedData(responseObj, dropDownDataResponse.userDefinedDocumentTypes);
  };

  useEffect(() => {
    loadSeafarer(seafarerId);
  }, [seafarerId]);
  const handleBankAccountHolderChange = () => {
    const changedSeafarer = seafarer;
    const person = changedSeafarer.seafarer_person || {};
    const bankAccounts = getBankAccounts(person);
    person.bank_accounts = bankAccounts;
    setSeafarer({ ...changedSeafarer });
  };
  const onSubmitSeafarer = async () => {
    setLoading(true);
    try {
      const { id } = await controller.onSubmitSeafarer();
      if (id) {
        history.push(`/seafarer/details/${id}/account-details`);
        localStorage.removeItem('seafarer-form');
      }
    } catch (error) {
      console.log('## error', error);
      setModalErrorData(error?.response?.data);
      setModalMessage(ERROR_FAILED_TO_SAVE_MESSAGE);
    } finally {
      setLoading(false);
    }
  };

  const onHideModalMessage = () => setModalMessage(null);
  const getBankAccountsValidationSchema = () => {
    cloneSeafarerSchema.fields = omitBy(
      cloneSeafarerSchema.fields,
      (_, key) => key !== 'seafarer_person',
    );
    cloneSeafarerSchema.fields.seafarer_person.fields = omitBy(
      cloneSeafarerSchema.fields.seafarer_person.fields,
      (_, key) => key !== 'bank_accounts',
    );

    return cloneSeafarerSchema;
  };
  const schema = getBankAccountsValidationSchema();

  // custom submit logic to not block saving when there's only bank account errors
  const viewFile = async () => {
    const document = {
      id: userDefinedData?.[`seafarer_doc_${userDefinedData.type}`]?.seafarer_document_id,
    };
    const fileName = userDefinedData.doc_path;
    const documentType = userDefinedData.type;

    if (documentType && document && fileName) {
      window.open(
        `https://${window.location.hostname}/seafarer/document/${document.id}/${documentType}`,
      );
    }
  };
  const basicToAccountHolderMap = {
    first_name: 'first_name',
    middle_name: 'middle_name',
    last_name: 'last_name',
    nationality_id: 'nationality_id',
    date_of_birth: 'date_of_birth',
    gender: 'gender',
  };

  const onSeafarerChange = (changedSeafarer, setFieldValue, field = null, value = null) => {
    setFieldValue?.('seafarer_person', changedSeafarer.seafarer_person);
    if (field) {
      if (basicToAccountHolderMap[field]) {
        handleBankAccountHolderChange();
      }
      if (value !== null && value !== undefined) {
        setFieldValue(field, value);
      }
    }
    controller.seafarerChanges = changedSeafarer;
    setSeafarer(changedSeafarer);
  };

  useEffect(() => {
    const bankAccounts = getBankAccounts(seafarer.seafarer_person);
    seafarer.seafarer_person.bank_accounts = bankAccounts;
    onSeafarerChange({ ...seafarer }, null);
  }, [loading, error]);

  return (
    <AccessHandlerWrapper hasRoleAccess={hasRoleAccess}>
      <Container>
        {error ? <ErrorAlert message={error} /> : ''}

        {loading ? (
          <div className="spinner-container">
            <Spinner />
          </div>
        ) : (
          <Formik
            validate={async (value) => {
              try {
                await validateYupSchema(value, schema, true, {
                  dropDownData: dropDownData,
                  value,
                  selectedBank: selectedBank.current,
                });
              } catch (err) {
                return yupToFormErrors(err); //for rendering validation errors
              }
              return {};
            }}
            enableReinitialize={true}
            initialValues={seafarer}
            validateOnChange={true}
            validateOnBlur={true}
            validateOnMount={!!seafarerId}
          >
            {({
              handleSubmit,
              handleChange,
              values,
              touched,
              isValid,
              errors,
              setFieldValue,
              validateForm,
            }) => {
              const mappedErrors = mapInputErrors(errors);

              const countries = dropDownData.countries ?? [];
              const nationalities = dropDownData.nationalities ?? [];

              const handleAddBankAccount = () => {
                const localId = getNextId(bankAccounts);
                const newBankAccount = {
                  is_primary_payroll_account: false,
                  seafarer_is_account_holder: true,
                  localId,
                };
                const changedSeafarer = {
                  ...seafarer,
                };
                applyPersonAccountInfo(newBankAccount, changedSeafarer.seafarer_person);
                changedSeafarer.seafarer_person.bank_accounts = [...bankAccounts, newBankAccount];
                onSeafarerChange(changedSeafarer, setFieldValue);
              };

              const handleRemoveBankAccount = (index, key) => {
                setRemoveItemKey({ key, index });
                setRemoveModalTitle(`Confirm Removing Bank Account ${index}?`);
                setRemoveModalMessage(`Are you sure removing Bank Account ${index}?`);
                setRemoveModalShow(true);
              };

              const handleRemoveItem = () => {
                setRemoveModalShow(false);
                const oldBankAccounts = [...seafarer.seafarer_person.bank_accounts];
                const updatedBankAccount = [...oldBankAccounts].filter((bank) => {
                  if (bank?.id) {
                    return bank.id !== removeItemKey.key;
                  } else {
                    return bank.localId !== removeItemKey.key;
                  }
                });
                const changedSeafarer = {
                  ...seafarer,
                };
                changedSeafarer.seafarer_person.bank_accounts = updatedBankAccount;
                if (selectedBank.current?.length && removeItemKey.index)
                  selectedBank.current.splice(removeItemKey.index, 1);
                onSeafarerChange(changedSeafarer, setFieldValue);
              };

              const onBankAccountChange = (bankAccount) => {
                const changedAccounts = [...bankAccounts];
                const index = changedAccounts.findIndex(
                  ({ localId }) => localId && localId === bankAccount.localId,
                );
                if (index === -1) {
                  console.error('account localId not exists');
                  return;
                }
                changedAccounts[index] = bankAccount;

                const changedSeafarer = {
                  ...seafarer,
                };
                changedSeafarer.seafarer_person.bank_accounts = changedAccounts;
                onSeafarerChange(changedSeafarer, setFieldValue);
              };

              const isHidden = roleConfig.seafarer.hidden.bankAccount;

              if (isHidden) {
                return (
                  <div className="spinner-container">
                    <div>Not available</div>
                  </div>
                );
              }

              const onSubmitValues = () => {
                onValidateForm().then(({ isFormValid }) => {
                  if (isFormValid) {
                    onSubmitSeafarer();
                  }
                });
              };
              const onValidateForm = async () => {
                const errors = await validateForm();

                const isFormCompleted = Object.keys(errors).length === 0;
                const isBankAccountFormCompleted = !errors?.seafarer_person?.bank_accounts || Object.keys(errors.seafarer_person.bank_accounts).length === 0;

                const validatingErrors = { ...errors };

                let isFormValid = true;
                if (Object.keys(validatingErrors).length > 0) {
                  isFormValid = false;
                  setModalMessage(PROCEED_OTHER_TAB_FAILED_MESSAGE);
                }

                return {
                  isFormValid,
                  isFormCompleted,
                  isBankAccountFormCompleted,
                };
              };
              const onValidateLink = (link, subSection) => {
                setSubSection(link || subSection);
              };

              return (
                <Container>
                  <Row className="closeIcon">
                    <BreadcrumbHeader
                      items={breadCrumbsItems}
                      activeItem={headingTitle}
                      onClick={() => {}}
                    />
                    <Icon icon="close" size={25} onClick={onClose} />
                  </Row>
                  <ErrorsListComponent
                    headertext="Correct the following highlighted in red:"
                    errors={errors}
                    onValidateLink={onValidateLink}
                  />
                  <Form noValidate onSubmit={handleSubmit}>
                    <div className="add_seafarer_page">
                      <div className="mt-5">*Required Fields</div>
                      <SectionTitleComponent />
                      <p style={{ fontWeight: 'bold' }}>Bank Account Cancelled Cheque</p>
                      {userDefinedData ? (
                        <Button variant="link" className="link-underline" onClick={viewFile}>
                          Bank Document
                        </Button>
                      ) : (
                        ''
                      )}
                      <SectionTitleComponent title={'Bank Account'.toUpperCase()} />

                      {bankAccounts.map((bankAccount, index) => {
                        const sectionTitle = `OTHER BANK ACCOUNT ${index}`;
                        const bankAccountErros = mappedErrors[index];

                        const key = bankAccount?.id ? bankAccount.id : bankAccount.localId;
                        return (
                          <div key={key} ref={(el) => (bankAccSectionRefs.current[index] = el)}>
                            {index === 0 ? null : (
                              <SubSectionTitleRemoveComponent
                                onClick={handleRemoveBankAccount.bind(this, index, key)}
                                title={sectionTitle}
                                isEditable={true}
                              />
                            )}
                            <BankAccountComponent
                              seafarer={seafarer}
                              countries={countries}
                              nationalities={nationalities}
                              bankAccount={bankAccount}
                              selectedBank={selectedBank}
                              errors={bankAccountErros}
                              onSeafarerChange={onSeafarerChange}
                              onBankAccountChange={onBankAccountChange}
                              isEditable={isBankAccountEditable}
                              dropDownData={dropDownData}
                              bankAccountIndex={index}
                              bankAccountsRef={bankAccountsRef}
                              setFieldValue={setFieldValue}
                            />
                          </div>
                        );
                      })}

                      {isBankAccountEditable ? (
                        <AddAnotherComponent onClick={handleAddBankAccount} />
                      ) : null}

                      <ConfirmActionModalView
                        show={removeModalShow}
                        onClose={() => setRemoveModalShow(false)}
                        onConfirm={handleRemoveItem}
                        title={removeModalTitle}
                        message={removeModalMessage}
                      />
                    </div>
                    <BottomButton
                      title={'Save'}
                      testID="form-seafarer-save-button"
                      onClick={onSubmitValues}
                    />
                    <Modal show={modalMessage !== null} onHide={onHideModalMessage} centered>
                      <Modal.Header className="modal-header">
                        {modalMessage?.header}
                      </Modal.Header>
                      <Modal.Body>
                        {modalMessage?.message}
                        {modalErrorData && <div>Error Message : {modalErrorData}</div>}
                      </Modal.Body>
                      <Modal.Footer>
                        <Button variant="secondary" onClick={onHideModalMessage}>
                          Close
                        </Button>
                      </Modal.Footer>
                    </Modal>
                  </Form>
                </Container>
              );
            }}
          </Formik>
        )}
      </Container>
    </AccessHandlerWrapper>
  );
};

const BankAccountComponent = (props) => {
  const {
    seafarer = {},
    bankAccount = {},
    countries,
    nationalities,
    errors = {},
    handleBlur,
    onBankAccountChange,
    onSeafarerChange,
    isEditable,
    selectedBank,
    dropDownData,
    bankAccountIndex,
    bankAccountsRef,
    setFieldValue,
  } = props;

  const isIndia = isIndiaAccount(bankAccount, countries);

  const [removeModalShow, setRemoveModalShow] = useState(false);
  const [isBankAccountNumberMandatory, setIsBankAccountNumberMandatory] = useState(false);
  const bankNameList = useRef([...(dropDownData.bankNames ?? [])]);

  const {
    seafarer_is_account_holder,
    is_primary_payroll_account,
    relationship_with_beneficiary,
    account_holder_first_name,
    account_holder_middle_name,
    account_holder_last_name,
    account_holder_date_of_birth,
    account_holder_birth_place,
    account_holder_gender,
    account_holder_nationality_id,
    account_holder_address,
    number,
    bank_name,
    bank_address,
    pay_mode,
    ifsc_number,
    swift_code,
    intermediary_swift_code,
    intermediary_bank_name,
    intermediary_bank_address,
    intermediary_bank_account,
    special_remittence_instrcution,
    account_holder_contact_1,
    account_holder_contact_2,
    iban_number,
    remarks,
    cnaps,
    account_type,
    fcnr_months,
  } = mapBankAccountToInputValues(seafarer.seafarer_person, bankAccount);

  const getSwiftCode = (bankName) => {
    return bankName
      ? dropDownData.bankNames.find((bank) => bank.bank_name === bankName)?.swift_code_pattern
      : '';
  };
  useEffect(() => {
    if (bankAccount.bank_address?.country_id && bank_name) {
      filterBankNames(bankAccount.bank_address?.country_id);
      const bank = bankNameList.current.find((bank) => bank.bank_name === bank_name);
      selectedBank.current[bankAccountIndex] = bank;
    }
    if (account_holder_nationality_id === 109) {
      bankAccount.pay_mode = 'NEFT';
    } else {
      bankAccount.pay_mode = 'TT';
    }
    if (bankAccount.bank_name) {
      bankAccount.swift_code = getSwiftCode(bankAccount.bank_name);
    }
  }, []);

  //#region functions
  const onPrimaryAccountButtonClick = (event) => {
    const changedSeafarer = {
      ...seafarer,
    };
    changedSeafarer.seafarer_person.bank_accounts.forEach((account) => {
      account.is_primary_payroll_account = parseInt(event.target.value, 10) === account.localId;
    });
    onSeafarerChange(changedSeafarer, setFieldValue);
  };
  const onIsAccountHolderChange = (event) => {
    let newBankAccount = mapInputChangeToBankAccount(
      {
        bankAccount,
        field: event.target.name,
        value: event.target.checked,
      },
      countries,
      nationalities,
    );
    if (newBankAccount.seafarer_is_account_holder) {
      applyPersonAccountInfo(newBankAccount, seafarer.seafarer_person);
    }
    onBankAccountChange(newBankAccount);
  };

  const filterBankNames = (countryId) => {
    const bankCountryCode = countries.find((country) => country.id == countryId)?.alpha2_code;
    bankNameList.current = dropDownData.bankNames.filter(
      (bankName) => bankName.country_code === bankCountryCode,
    );
  };

  const resetFields = (newBankAccount) => {
    newBankAccount.number = null;
    newBankAccount.ifsc_number = null;
    newBankAccount.swift_code = null;
  };

  const onInputChange = (event) => {
    if (event.target.name === 'cnaps' && event.target.value.length > 35) {
      return false;
    }
    const newBankAccount = mapInputChangeToBankAccount(
      {
        bankAccount,
        field: event.target.name,
        value:
          event.target.name === 'cnaps'
            ? event.target.value && +event.target.value
            : event.target.value,
      },
      countries,
      nationalities,
    );
    if (event.target.name === 'bank_address_country_id') {
      newBankAccount.bank_name = '';
      if (!event.target.value) {
        selectedBank.current[newBankAccount.localId - 1] = undefined;
        newBankAccount.bank_address.country_id = null;
      }
      resetFields(newBankAccount);
      filterBankNames(event.target.value);
    }
    if (event.target.name === 'bank_name') {
      selectedBank.current[newBankAccount.localId - 1] = event.target.data;
      resetFields(newBankAccount);
      newBankAccount.swift_code = getSwiftCode(event.target.value);
    }

    if (event.target.name === 'account_holder_nationality_id') {
      if (event.target.value === 109) {
        newBankAccount.pay_mode = 'NEFT';
      } else {
        newBankAccount.pay_mode = 'TT';
      }
    }
    onBankAccountChange(newBankAccount);
  };

  const preventMinus = (e) => {
    if (e.code === 'Minus') {
      e.preventDefault();
    }
  };

  const preventPasteNegative = (e) => {
    const clipboardData = e.clipboardData || window.clipboardData;
    const pastedData = parseFloat(clipboardData.getData('text'));

    if (pastedData < 0) {
      e.preventDefault();
    }
  };

  const onDateOfBirthChange = (value) => {
    const changedAccount = { ...bankAccount };
    changedAccount.account_holder_date_of_birth = value;
    onBankAccountChange(changedAccount);
  };

  let fileName;
  const file = bankAccount.file;
  if (file) {
    fileName = file.name;
  }

  const docs = bankAccount.document ?? [];
  if (docs.length > 0) {
    fileName = docs[0].name;
  }

  const handleSelectedFile = (file) => {
    if (file === undefined) {
      return;
    }
    bankAccount.file = file;

    onBankAccountChange({ ...bankAccount });
  };

  const handleRemoveFile = () => {
    setRemoveModalShow(false);

    const item = bankAccount;
    if (item.document && item.document.length > 0) {
      item.idToRemove = item.document[0].id;
    }
    delete item.file;
    delete item.document;

    onBankAccountChange(item);
  };

  const handleTelePhoneChange = (value, name) => {
    const newBankAccount = mapInputChangeToBankAccount(
      {
        bankAccount,
        field: name,
        value: value.trim(),
      },
      countries,
      nationalities,
    );
    onBankAccountChange(newBankAccount);
  };

  const downloadFile = async () => {
    if (docs.length > 0) {
      window.open(
        `https://${window.location.hostname}/seafarer/document/${docs[0].id}/bank_account`,
      );
    }
  };

  //#endregion functions

  const fallbackStmt = (isEditable ? (
    <SelectFileButton
      onSelectFile={handleSelectedFile}
      bankAccountNumber={number}
      setIsBankAccountNumberMandatory={setIsBankAccountNumberMandatory}
      isBankAccount={true}
    />
  ) : null)

  return (
    <div>
      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Bank Account Holder</Form.Label>
          <Form.Check
            type="checkbox"
            name="seafarer_is_account_holder"
            onChange={onIsAccountHolderChange}
            checked={seafarer_is_account_holder}
            label="Seafarer is the account holder."
            id={`seafarer_is_account_holder-${bankAccount.localId}`}
            disabled={!isEditable}
          />
        </Form.Group>

        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Primary Payroll Account</Form.Label>
          <Form.Check
            type="checkbox"
            label="This account is primary payroll account"
            checked={is_primary_payroll_account}
            value={bankAccount.localId}
            onChange={onPrimaryAccountButtonClick}
            name="is_primary_payroll_account"
            id={`is_primary_payroll_account-${bankAccount.localId}`}
            disabled={!isEditable}
          />
        </Form.Group>
      </Form.Row>

      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>First Name*</Form.Label>
          <Form.Control
            type="text"
            name="account_holder_first_name"
            value={removeSpecialCharacters(account_holder_first_name)}
            onChange={onInputChange}
            isInvalid={!!errors.account_holder_first_name}
            disabled={!isEditable || seafarer_is_account_holder}
            maxLength="35"
          />
          <Form.Control.Feedback type="invalid">
            {errors.account_holder_first_name}
          </Form.Control.Feedback>
        </Form.Group>

        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Middle Name</Form.Label>
          <Form.Control
            type="text"
            name="account_holder_middle_name"
            value={removeSpecialCharacters(account_holder_middle_name)}
            onChange={onInputChange}
            isInvalid={!!errors.account_holder_middle_name}
            disabled={!isEditable || seafarer_is_account_holder}
            maxLength="35"
          />
          <Form.Control.Feedback type="invalid">
            {errors.account_holder_middle_name}
          </Form.Control.Feedback>
        </Form.Group>
      </Form.Row>

      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Last Name</Form.Label>
          <Form.Control
            type="text"
            name="account_holder_last_name"
            value={removeSpecialCharacters(account_holder_last_name)}
            onChange={onInputChange}
            isInvalid={!!errors.account_holder_last_name}
            disabled={!isEditable || seafarer_is_account_holder}
            maxLength="35"
          />
          <Form.Control.Feedback type="invalid">
            {errors.account_holder_last_name}
          </Form.Control.Feedback>
        </Form.Group>

        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Gender</Form.Label>
          <Form.Control
            as="select"
            name="account_holder_gender"
            value={account_holder_gender ?? ''}
            onChange={onInputChange}
            onBlur={handleBlur}
            isInvalid={!!errors.account_holder_gender}
            disabled={!isEditable || seafarer_is_account_holder}
          >
            <option value="">Please select</option>
            <option value="male">Male</option>
            <option value="female">Female</option>
            ))
          </Form.Control>
          <Form.Control.Feedback type="invalid">Please select Gender.</Form.Control.Feedback>
        </Form.Group>
      </Form.Row>

      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Date of Birth</Form.Label>
          <FleetDatePicker
            name="account_holder_date_of_birth"
            value={account_holder_date_of_birth ?? ''}
            onChange={onDateOfBirthChange}
            isInvalid={!!errors.account_holder_date_of_birth}
            disabled={!isEditable || seafarer_is_account_holder}
          />
          <Form.Control.Feedback
            type="invalid"
            className={errors.account_holder_date_of_birth ? 'set-display-block' : ''}
          >
            {errors.account_holder_date_of_birth}
          </Form.Control.Feedback>
        </Form.Group>
        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Place of Birth</Form.Label>
          <Form.Control
            type="text"
            name="account_holder_birth_place"
            value={removeSpecialCharacters(account_holder_birth_place)}
            onChange={onInputChange}
            disabled={!isEditable}
            maxLength="35"
          />
        </Form.Group>

        <Form.Group as={Col} md="5">
          <Form.Label>Nationality</Form.Label>
          <CountryNationalityDropDownControl
            name={'account_holder_nationality_id'}
            selectedValue={account_holder_nationality_id}
            dropDownValues={nationalities}
            onBlur={handleBlur}
            isInvalid={!!errors.account_holder_nationality_id}
            onInputChange={onInputChange}
            disabled={!isEditable || seafarer_is_account_holder}
          />
          <Form.Control.Feedback
            type="invalid"
            className={errors.account_holder_nationality_id ? 'set-display-block' : ''}
          >
            Please select Nationality.
          </Form.Control.Feedback>
        </Form.Group>
        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Relationship with Beneficiary</Form.Label>
          <Form.Control
            type="text"
            name="relationship_with_beneficiary"
            value={removeSpecialCharacters(relationship_with_beneficiary)}
            onChange={onInputChange}
            disabled={seafarer_is_account_holder || !isEditable}
            isInvalid={!!errors.relationship_with_beneficiary && !seafarer_is_account_holder}
            maxLength="35"
          />
          <Form.Control.Feedback type="invalid">
            {errors.relationship_with_beneficiary ===
            ERROR_MAPPINGS['relationship_with_beneficiary'].message
              ? 'Please enter Relationship with Beneficiary.'
              : errors.relationship_with_beneficiary}
          </Form.Control.Feedback>
        </Form.Group>
      </Form.Row>

      <SubSectionTitleComponent title={'ACCOUNT HOLDER’S ADDRESS'} />

      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Country</Form.Label>
          <CountryNationalityDropDownControl
            name={'account_holder_address_country_id'}
            selectedValue={account_holder_address.country_id ?? 0}
            dropDownValues={countries}
            onBlur={handleBlur}
            isInvalid={!!errors.account_holder_address_country_id}
            onInputChange={onInputChange}
            disabled={!isEditable}
          />
        </Form.Group>
        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Flat / Room / House Number/ Street</Form.Label>
          <Form.Control
            type="text"
            name="account_holder_address_address4"
            value={removeSpecialCharacters(account_holder_address.address4)}
            isInvalid={!!errors.account_holder_address_address4}
            onChange={onInputChange}
            disabled={!isEditable}
            maxLength="35"
          />
        </Form.Group>
        <Form.Group as={Col} md="5">
          <Form.Label>Building Name / Estate</Form.Label>
          <Form.Control
            type="text"
            name="account_holder_address_address3"
            value={removeSpecialCharacters(account_holder_address.address3)}
            isInvalid={!!errors.account_holder_address_address3}
            onChange={onInputChange}
            disabled={!isEditable}
            maxLength="35"
          />
        </Form.Group>
        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>State / Province / District</Form.Label>
          <Form.Control
            type="text"
            name="account_holder_address_address1"
            value={removeSpecialCharacters(account_holder_address.address1)}
            isInvalid={!!errors.account_holder_address_address1}
            onChange={onInputChange}
            disabled={!isEditable}
            maxLength="35"
          />
        </Form.Group>
        <Form.Group as={Col} md="5">
          <Form.Label>Village / Town / City</Form.Label>
          <Form.Control
            type="text"
            name="account_holder_address_address2"
            value={removeSpecialCharacters(account_holder_address.address2)}
            isInvalid={!!errors.account_holder_address_address2}
            onChange={onInputChange}
            disabled={!isEditable}
            maxLength="35"
          />
        </Form.Group>
        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Postal / Zip Code</Form.Label>
          <Form.Control
            type="text"
            name="account_holder_address_postal_zip_code"
            value={removeSpecialCharacters(account_holder_address.postal_zip_code)}
            isInvalid={!!errors.account_holder_address_postal_zip_code}
            onChange={onInputChange}
            disabled={!isEditable}
            maxLength="35"
          />
          <Form.Control.Feedback type="invalid">
            {errors.account_holder_address_postal_zip_code}
          </Form.Control.Feedback>
        </Form.Group>
        <Form.Group as={Col} md="5">
          <Form.Label>Contact number-1</Form.Label>
          <PhoneField
            phone={account_holder_contact_1 ? account_holder_contact_1.trim() : ''}
            error={errors.account_holder_contact_1}
            handleBlur={handleBlur}
            onChange={(value) => handleTelePhoneChange(value, 'account_holder_contact_1')}
            disabled={!isEditable}
            index={0}
          />
        </Form.Group>
        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Contact number-2</Form.Label>
          <PhoneField
            phone={account_holder_contact_2 ? account_holder_contact_2.trim() : ''}
            error={errors.account_holder_contact_2}
            handleBlur={handleBlur}
            onChange={(value) => handleTelePhoneChange(value, 'account_holder_contact_2')}
            disabled={!isEditable}
            index={0}
          />
        </Form.Group>
      </Form.Row>

      <SubSectionTitleComponent title={'BANK ACCOUNT DETAILS'} />

      <Form.Row ref={bankAccountsRef}>
        <Form.Group as={Col} md="5">
          <Form.Label>Bank Country</Form.Label>
          <CountryNationalityDropDownControl
            name={'bank_address_country_id'}
            selectedValue={bank_address.country_id ?? 0}
            dropDownValues={countries}
            onBlur={handleBlur}
            isInvalid={!!errors.bank_address_country_id}
            onInputChange={onInputChange}
            disabled={!isEditable}
          />
          <Form.Control.Feedback
            type="invalid"
            className={errors.bank_address_country_id ? 'set-display-block' : ''}
          >
            Please enter Bank Country.
          </Form.Control.Feedback>
        </Form.Group>
        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Bank Name*</Form.Label>
          <BankNamesDropDownControl
            name="bank_name"
            selectedValue={bank_name ?? ''}
            bankNameList={bankNameList.current}
            onBlur={handleBlur}
            onInputChange={onInputChange}
            isInvalid={!!errors.bank_name}
            disabled={!isEditable || !bank_address.country_id}
          />
          <Form.Control.Feedback type="invalid">{errors.bank_name}</Form.Control.Feedback>
        </Form.Group>

        <Form.Group as={Col} md="5">
          <Form.Label>Bank Account Number</Form.Label>
          <Form.Control
            type="text"
            name="number"
            value={
              !selectedBank.current[bankAccountIndex]?.account_digit
                ? removeSpecialCharacters(number?.replace(/\s/g, ''))
                : number?.replace(/\s/g, '')
            }
            onChange={onInputChange}
            isInvalid={!!errors.number}
            disabled={!isEditable || !bank_name}
            pattern="^[\\d*#+]+$"
          />
          <Form.Control.Feedback type="invalid">{errors.number}</Form.Control.Feedback>
        </Form.Group>

        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Flat / Room / House Number/ Street</Form.Label>
          <Form.Control
            type="text"
            name="bank_address_address1"
            value={removeSpecialCharacters(bank_address.address1)}
            onChange={onInputChange}
            isInvalid={!!errors.bank_address_address1}
            disabled={!isEditable}
            maxLength="35"
          />
          <Form.Control.Feedback type="invalid">
            {errors.bank_address_address1}
          </Form.Control.Feedback>
        </Form.Group>
      </Form.Row>

      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Building Name / Estate</Form.Label>
          <Form.Control
            type="text"
            name="bank_address_address2"
            value={removeSpecialCharacters(bank_address.address2)}
            onChange={onInputChange}
            isInvalid={!!errors.bank_address_address2}
            disabled={!isEditable}
            maxLength="35"
          />
          <Form.Control.Feedback type="invalid">
            {errors.bank_address_address2}
          </Form.Control.Feedback>
        </Form.Group>
        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Village / Town / City</Form.Label>
          <Form.Control
            type="text"
            name="bank_address_address3"
            value={removeSpecialCharacters(bank_address.address3)}
            onChange={onInputChange}
            isInvalid={!!errors.bank_address_address3}
            disabled={!isEditable}
            maxLength="35"
          />
          <Form.Control.Feedback type="invalid">
            {errors.bank_address_address3}
          </Form.Control.Feedback>
        </Form.Group>
      </Form.Row>
      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>State / Province / District</Form.Label>
          <Form.Control
            type="text"
            name="bank_address_address4"
            value={removeSpecialCharacters(bank_address.address4)}
            onChange={onInputChange}
            isInvalid={!!errors.bank_address_address4}
            disabled={!isEditable}
            maxLength="50"
          />
          <Form.Control.Feedback type="invalid">
            {errors.bank_address_address4}
          </Form.Control.Feedback>
        </Form.Group>
        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Postal / Zip Code</Form.Label>
          <Form.Control
            type="text"
            name="bank_address_postal_zip_code"
            value={removeSpecialCharacters(bank_address.postal_zip_code)}
            onChange={onInputChange}
            isInvalid={!!errors.bank_address_postal_zip_code}
            disabled={!isEditable}
            maxLength="35"
          />
          <Form.Control.Feedback type="invalid">
            {errors.bank_address_postal_zip_code}
          </Form.Control.Feedback>
        </Form.Group>
      </Form.Row>

      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>IFSC Number{isIndia ? '*' : ''}</Form.Label>
          <Form.Control
            disabled={!isEditable || !bank_name || !isIndia}
            type="text"
            name="ifsc_number"
            value={removeSpecialCharacters(ifsc_number?.replace(/\s/g, ''))}
            onChange={onInputChange}
            isInvalid={!!errors.ifsc_number && isIndia}
            maxLength="11"
          />
          <Form.Control.Feedback type="invalid">{errors.ifsc_number}</Form.Control.Feedback>
        </Form.Group>

        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>SWIFT Code</Form.Label>
          <Form.Control
            type="text"
            name="swift_code"
            value={swift_code ?? ''}
            onChange={onInputChange}
            isInvalid={!!errors.swift_code}
            disabled={true}
          />
          <Form.Control.Feedback type="invalid">{errors.swift_code}</Form.Control.Feedback>
        </Form.Group>
      </Form.Row>

      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Intermediary Bank Name</Form.Label>
          <BankNamesDropDownControl
            name="intermediary_bank_name"
            selectedValue={intermediary_bank_name ?? ''}
            bankNameList={bankNameList.current}
            onBlur={handleBlur}
            onInputChange={onInputChange}
            isInvalid={!!errors.intermediary_bank_name}
            disabled={!isEditable || !bank_address.country_id}
          />
        </Form.Group>
        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Intermediary Bank SWIFT Code</Form.Label>
          <Form.Control
            type="text"
            name="intermediary_swift_code"
            value={removeSpecialCharacters(
              intermediary_swift_code?.replace(/\s/g, ''),
            )}
            onChange={onInputChange}
            disabled={!isEditable}
            maxLength="11"
          />
        </Form.Group>
        <Form.Group as={Col} md="5">
          <Form.Label>Intermediary Bank Address</Form.Label>
          <Form.Control
            type="text"
            name="intermediary_bank_address"
            value={removeSpecialCharacters(intermediary_bank_address)}
            onChange={onInputChange}
            disabled={!isEditable}
            maxLength="35"
          />
        </Form.Group>
        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Intermediary Bank Account Number</Form.Label>
          <Form.Control
            type="text"
            name="intermediary_bank_account"
            value={removeSpecialCharacters(
              intermediary_bank_account?.replace(/\s/g, ''),
            )}
            onChange={onInputChange}
            disabled={!isEditable}
            maxLength="35"
          />
        </Form.Group>
      </Form.Row>
      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Pay Mode</Form.Label>
          <Form.Control
            type="text"
            name="pay_mode"
            value={pay_mode ?? ''}
            onChange={onInputChange}
            disabled={!isEditable}
            maxLength="10"
          />
        </Form.Group>
        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Special Remittance instructions</Form.Label>
          <Form.Control
            type="text"
            name="special_remittence_instrcution"
            value={removeSpecialCharacters(special_remittence_instrcution)}
            onChange={onInputChange}
            disabled={!isEditable}
            maxLength="35"
          />
        </Form.Group>
      </Form.Row>
      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Account Type</Form.Label>
          <Form.Control
            as="select"
            name="account_type"
            value={account_type ?? ''}
            onChange={onInputChange}
            onBlur={handleBlur}
            disabled={!isEditable}
          >
            <option value="" disabled>
              Please select
            </option>
            <option value="nre">NRE</option>
            <option value="nro">NRO</option>
            <option value="saving">SAVING</option>
            <option value="fcnr">FCNR</option>
            <option value="current">CURRENT</option>
          </Form.Control>
        </Form.Group>
        {account_type === 'fcnr' && (
          <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
            <Form.Label>Select number of months</Form.Label>
            <Form.Control
              as="select"
              name="fcnr_months"
              value={account_type === 'fcnr' ? fcnr_months : ''}
              onChange={onInputChange}
              onBlur={handleBlur}
              disabled={!isEditable}
              isInvalid={!!errors.fcnr_months}
            >
              <option value="">Please select</option>
              <option value="6">6 months</option>
              <option value="12">12 months</option>
            </Form.Control>
            <Form.Control.Feedback type="invalid">{errors.fcnr_months}</Form.Control.Feedback>
          </Form.Group>
        )}
      </Form.Row>

      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Remarks</Form.Label>
          <Form.Control
            type="text"
            name="remarks"
            value={removeSpecialCharacters(remarks)}
            onChange={onInputChange}
            disabled={!isEditable}
            maxLength="100"
          />
        </Form.Group>
        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>IBAN Number</Form.Label>
          <Form.Control
            type="text"
            name="iban_number"
            value={iban_number ? iban_number.replace(/[^a-z0-9]/gi, '') : ''}
            onChange={onInputChange}
            isInvalid={!!errors.iban_number}
            disabled={!isEditable}
            maxLength="35"
          />
          <Form.Control.Feedback type="invalid">{errors.iban_number}</Form.Control.Feedback>
        </Form.Group>
        {bank_address.country_id === 44 && account_holder_nationality_id === 44 && (
          <Form.Group as={Col} md="5">
            <Form.Label>CNAPS</Form.Label>
            <Form.Control
              type="number"
              name="cnaps"
              value={cnaps ?? ''}
              onPaste={preventPasteNegative}
              onChange={onInputChange}
              onKeyDown={preventMinus}
              disabled={!isEditable}
              maxLength="35"
              min="0"
            />
          </Form.Group>
        )}
        <Form.Group
          as={Col}
          md={
            bank_address.country_id === 44 && account_holder_nationality_id === 44
              ? { span: 5, offset: 1 }
              : '5'
          }
        >
          <Form.Label>{`Upload copy of Bank Account ${COMMON_LABELS.LABEL_FILE_SIZE_5MB_AND_EXTENSION}`}</Form.Label>
          {fileName !== undefined ? (
            <SelectedFileField
              fileName={fileName}
              onClick={downloadFile}
              onRemoveClick={() => setRemoveModalShow(true)}
              disabled={!isEditable}
            />
          ) : fallbackStmt}
          {isBankAccountNumberMandatory && (
            <Form.Control.Feedback type="invalid" className={'document-copy-feedback'}>
              Please Enter Bank Account Number
            </Form.Control.Feedback>
          )}
          {errors.file === 'File too large' && (
            <Form.Control.Feedback type="invalid" className={'document-copy-feedback'}>
              {COMMON_MESSAGES.MESSAGE_FILE_SIZE_EXCEED_5MB}
            </Form.Control.Feedback>
          )}
          {errors.file === 'Unsupported Format' && (
            <Form.Control.Feedback type="invalid" className={'document-copy-feedback'}>
              {COMMON_MESSAGES.MESSAGE_FILE_EXTENSION_NOT_SUPPORT}
            </Form.Control.Feedback>
          )}
        </Form.Group>
      </Form.Row>
      <ConfirmActionModalView
        show={removeModalShow}
        onClose={() => setRemoveModalShow(false)}
        onConfirm={handleRemoveFile}
        title={'Confirm Deleting the File?'}
        message={'Are you sure deleting the uploaded file?'}
      />
    </div>
  );
};

BankAccountComponent.propTypes = {
  seafarer: PropTypes.object,
  bankAccount: PropTypes.object,
  countries: PropTypes.object,
  nationalities: PropTypes.object,
  errors: PropTypes.object,
  handleBlur: PropTypes.func,
  onBankAccountChange: PropTypes.func,
  onSeafarerChange: PropTypes.func,
  isEditable: PropTypes.bool,
  selectedBank: PropTypes.object,
  dropDownData: PropTypes.object,
  bankAccountIndex: PropTypes.number,
  bankAccountsRef: PropTypes.object,
  setFieldValue: PropTypes.func,
};

export default AddSeafarerBankAccounts;
