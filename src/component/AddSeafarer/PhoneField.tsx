import React, { useRef, useState } from 'react';
import PhoneInput from 'react-phone-input-2';
import { Form, Col } from 'react-bootstrap';
import 'react-phone-input-2/lib/style.css';
import './scss/contact-details-form.scss';
import styleGuide from '../../styleGuide';
import PropTypes from 'prop-types';

const { Icon } = styleGuide;

const displayFormatGuide = (sample) => {
  if (!sample) return null;
  const result = sample.split('.').join('X');
  return result;
};

const MAXIMUM_PHONE_LENGTH = 16;

const PhoneField = ({ phone, onChange, index, onRemove, error, handleBlur, disabled }) => {
  const [expectedFormat, setExpectedFormat] = useState(null);

  const handleRemove = () => onRemove(index);
  const handleChange = (newValue, country) => {
    if (country.format) setExpectedFormat(country.format);
    onChange(newValue, index);
  };
  const hasReachedMaximum = phone.length > MAXIMUM_PHONE_LENGTH;

  const ref = useRef(null);
  let initialCountry = null;
  if (ref?.current?.props?.country) initialCountry = ref.current.props.country;

  return (
    <Form.Row data-testid="fml-seafarer-contact-add-telephone-number">
      <Form.Group as={Col} md={10}>
        <PhoneInput
          ref={ref}
          preferredCountries={['in', 'ph', 'hk', 'cn', 'kr', 'lk', 'id', 'vn', 'ua']}
          value={phone}
          countryCodeEditable={true}
          enableSearch
          onBlur={handleBlur}
          enableLongNumbers={!expectedFormat || hasReachedMaximum}
          inputClass={error && 'tel-input-error'}
          searchPlaceholder="Search"
          buttonClass={error && 'border-error-color'}
          disableSearchIcon
          disabled={disabled}
          placeholder=""
          onChange={handleChange}
        />
        <Form.Control.Feedback type="invalid" className={error && 'set-display-block'}>
          {!initialCountry && !expectedFormat
            ? 'Invalid Number, Cannot detect country of area code'
            : `Invalid number, please fill in as the following format \n ${displayFormatGuide(
                expectedFormat,
              )}`}
        </Form.Control.Feedback>
      </Form.Group>
      {index !== 0 && !disabled && (
        <Form.Group as={Col} md={2}>
          <Icon icon="remove" size={30} className="remove_icon" onClick={handleRemove} />
        </Form.Group>
      )}
    </Form.Row>
  );
};

PhoneField.propTypes = {
  phone: PropTypes.string,
  onChange: PropTypes.func,
  onRemove: PropTypes.func,
  error: PropTypes.object,
  handleBlur: PropTypes.func,
  disabled: PropTypes.bool,
  index: PropTypes.number,
};

export default PhoneField;
