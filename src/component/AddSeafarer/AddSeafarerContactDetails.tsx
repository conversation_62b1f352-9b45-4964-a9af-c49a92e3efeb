import React, { forwardRef } from 'react';
import { Form, Col, Button, Row } from 'react-bootstrap';
import { v4 as uuid } from 'uuid';

import AddressDetailFields from './AddressDetailFields';
import PhoneField from './PhoneField';
import AddSeafarerOtherComponents from './AddSeafarerOtherComponents';
import PropTypes from 'prop-types';
import styleGuide from '../../styleGuide';

const { Icon } = styleGuide;

const ADD_ACTION_TYPES = {
  telephone: 'ADD_TELEPHONE_FIELD',
  mobile: 'ADD_MOBILE_FIELD',
  email: 'ADD_EMAIL_FIELD',
  address: 'ADD_ADDRESS_FIELD',
};

const REMOVE_ACTION_TYPES = {
  telephone: 'REMOVE_TELEPHONE_FIELD',
  mobile: 'REMOVE_MOBILE_FIELD',
  email: 'REMOVE_EMAIL_FIELD',
  address: 'REMOVE_ADDRESS_FIELD',
};

const {
  AddAnotherComponent,
  SectionTitleComponent,
  SubSectionTitleRemoveComponent,
} = AddSeafarerOtherComponents;

function removeContact(contacts, contact) {
  return contacts.filter((c) => {
    if (c.id) return c.id !== contact.id;
    return c.localId !== contact.localId;
  });
}

const AddSeafarerContactDetails = forwardRef((props, ref) => {
  const { seafarer, handleBlur, onSeafarerChange, dropDownData, errors, roleConfig } = props;

  const countries = dropDownData.countries ?? [];

  const personalData = seafarer.seafarer_person || {};
  const telephone_numbers = personalData.telephone_numbers || [];
  const mobile_numbers = personalData.mobile_numbers || [];
  const email_addresses = personalData.email_addresses || [];
  const addresses = personalData.addresses || [];

  const isHidden = roleConfig.seafarer.hidden.contactDetails;
  const isEditable = roleConfig.seafarer.edit.contactDetails;

  const handleTelePhoneChange = (value, index) => {
    const newTelNumber = [...telephone_numbers];
    newTelNumber[index].contact = value.trim();
    const changedSeafarer = { ...seafarer };
    changedSeafarer.seafarer_person.telephone_numbers = newTelNumber;
    onSeafarerChange(changedSeafarer);
  };

  const cannotAdd = (type) => {
    switch (type) {
      case ADD_ACTION_TYPES.telephone: {
        const totalField = telephone_numbers.length;
        const lastItem = telephone_numbers[totalField - 1];
        return totalField === 5 || (lastItem && lastItem.contact === '');
      }
      case ADD_ACTION_TYPES.mobile: {
        const totalField = mobile_numbers.length;
        const lastItem = mobile_numbers[totalField - 1];
        return totalField === 5 || (lastItem && lastItem.contact === '');
      }
      case ADD_ACTION_TYPES.email: {
        const totalField = email_addresses.length;
        const lastItem = email_addresses[totalField - 1];
        return totalField === 5 || (lastItem && lastItem.contact === '');
      }
      case ADD_ACTION_TYPES.address: {
        const totalField = addresses.length;
        const lastItem = addresses[totalField - 1];
        if (lastItem === undefined) return false;
        return totalField === 5 || Object.keys(lastItem).length <= 1;
      }
      default:
        return true;
    }
  };

  const handleAddTypeMapping = (actionType) => {
    switch (actionType) {
      case ADD_ACTION_TYPES.telephone: {
        const newTelephoneNumber = [
          ...telephone_numbers,
          { localId: uuid(), contact_type: 'telephone_number', contact: '' },
        ];
        const updatedSeafarer = { ...seafarer };
        updatedSeafarer.seafarer_person.telephone_numbers = newTelephoneNumber;
        return updatedSeafarer;
      }
      case ADD_ACTION_TYPES.mobile: {
        const newMobileNums = [
          ...mobile_numbers,
          { localId: uuid(), contact_type: 'mobile_number', contact: '' },
        ];
        const updatedSeafarer = { ...seafarer };
        updatedSeafarer.seafarer_person.mobile_numbers = newMobileNums;
        return updatedSeafarer;
      }
      case ADD_ACTION_TYPES.email: {
        const newEmails = [
          ...email_addresses,
          { localId: uuid(), contact_type: 'email', contact: '' },
        ];
        const updatedSeafarer = { ...seafarer };
        updatedSeafarer.seafarer_person.email_addresses = newEmails;
        return updatedSeafarer;
      }
      case ADD_ACTION_TYPES.address: {
        const newAddresses = [...addresses, { localId: uuid() }];
        const updatedSeafarer = { ...seafarer };
        updatedSeafarer.seafarer_person.addresses = newAddresses;
        return updatedSeafarer;
      }
      default:
        return { ...seafarer };
    }
  };

  const handleAddField = (type) => {
    if (cannotAdd(type)) return;
    const changedSeafarer = handleAddTypeMapping(type);
    onSeafarerChange(changedSeafarer);
  };

  const handleRemoveMapping = (actionType, contact) => {
    switch (actionType) {
      case REMOVE_ACTION_TYPES.telephone: {
        const updatedTelephoneNums = removeContact(telephone_numbers, contact);
        const changedSeafarer = { ...seafarer };
        changedSeafarer.seafarer_person.telephone_numbers = updatedTelephoneNums;
        return changedSeafarer;
      }
      case REMOVE_ACTION_TYPES.mobile: {
        const updatedMobiles = removeContact(mobile_numbers, contact);
        const changedSeafarer = { ...seafarer };
        changedSeafarer.seafarer_person.mobile_numbers = updatedMobiles;
        return changedSeafarer;
      }
      case REMOVE_ACTION_TYPES.email: {
        const updatedEmails = removeContact(email_addresses, contact);
        const changedSeafarer = { ...seafarer };
        changedSeafarer.seafarer_person.email_addresses = updatedEmails;
        return changedSeafarer;
      }
      case REMOVE_ACTION_TYPES.address: {
        const updatedAddress = removeContact(addresses, contact);
        const changedSeafarer = { ...seafarer };
        changedSeafarer.seafarer_person.addresses = updatedAddress;
        return changedSeafarer;
      }
      default:
        return { ...seafarer };
    }
  };

  const handleRemoveField = (type, contact) => {
    const changedSeafarer = handleRemoveMapping(type, contact);
    onSeafarerChange(changedSeafarer);
  };

  const handleMobileChange = (value, index) => {
    const newMobileNums = [...mobile_numbers];
    newMobileNums[index].contact = value.trim();
    const changedSeafarer = { ...seafarer };
    changedSeafarer.seafarer_person.mobile_numbers = newMobileNums;
    onSeafarerChange(changedSeafarer);
  };

  const onEmailChange = (event, index) => {
    const value = event.target.value;
    const newEmails = [...email_addresses];
    newEmails[index].contact = value.trim();
    const changedSeafarer = { ...seafarer };
    changedSeafarer.seafarer_person.email_addresses = newEmails;
    onSeafarerChange(changedSeafarer);
  };

  const handleAddressChange = (currentAddress) => {
    const oldAddresses = [...addresses];
    const newAddresses = oldAddresses.map((address) => {
      if ((address.id && currentAddress.id && address.id === currentAddress.id) || (address.localId === currentAddress.localId && currentAddress.localId)) {
        address = currentAddress;
      }
      return address;
    });

    const changedSeafarer = { ...seafarer };
    changedSeafarer.seafarer_person.addresses = newAddresses;

    onSeafarerChange(changedSeafarer);
  };

  if (isHidden) {
    return (
      <div className="spinner-container">
        <div>Not available</div>
      </div>
    );
  }
  return (
    <div className="add_seafarer_page">
      <div className="mt-5">*Required Fields</div>

      <SectionTitleComponent title={'Contact Details'.toUpperCase()} />

      <Row ref={ref}>
        <Col md={5} data-testid="fml-seafarer-global-label-telephone-number">
          <Form.Row>
            <Form.Label data-testid="fml-seafarer-contact-label-telephone-number">Telephone Number</Form.Label>
          </Form.Row >
          {telephone_numbers.map((telephone, index) => {
            let error = {};
            if (errors?.seafarer_person?.telephone_numbers) {
              error = errors.seafarer_person.telephone_numbers[index] ?? {};
            }
            const removeTelephone = () =>
              handleRemoveField(REMOVE_ACTION_TYPES.telephone, telephone);
            const phoneFieldKey = `phone_field_${index}`;
            return (
              <PhoneField
                key={phoneFieldKey}
                phone={telephone.contact || ''}
                error={error.contact}
                handleBlur={handleBlur}
                index={index}
                onChange={handleTelePhoneChange}
                onRemove={removeTelephone}
                disabled={!isEditable}
              />
            );
          })}
          {isEditable ? (
            <Button
              variant="outline-primary"
              size="sm"
              className="pl-3 pr-3"
              data-testid="fml-seafarer-phone-number-add-another"
              onClick={() => handleAddField(ADD_ACTION_TYPES.telephone)}
            >
              Add Another
            </Button>
          ) : null}
        </Col>
        <Col md={{ span: 5, offset: 1 }} data-testid="fml-seafarer-global-label-mobile-number">
          <Form.Row>
            <Form.Label data-testid="fml-seafarer-contact-label-mobile-number">Mobile Number</Form.Label>
          </Form.Row>
          {mobile_numbers.map((mobile, index) => {
            let error = {};
            if (errors?.seafarer_person?.mobile_numbers) {
              error = errors.seafarer_person.mobile_numbers[index] ?? {};
            }
            const removeMobile = () => handleRemoveField(REMOVE_ACTION_TYPES.mobile, mobile);
            return (
              <PhoneField
                key={mobile.id}
                phone={mobile.contact}
                error={error.contact}
                handleBlur={handleBlur}
                index={index}
                onChange={handleMobileChange}
                onRemove={removeMobile}
                disabled={!isEditable}
              />
            );
          })}
          {isEditable ? (
            <Button
              variant="outline-primary"
              size="sm"
              className="pl-3 pr-3"
              data-testid="fml-seafarer-mobile-number-add-another"
              onClick={() => handleAddField(ADD_ACTION_TYPES.mobile)}
            >
              Add Another
            </Button>
          ) : null}
        </Col>
      </Row>

      <div className="mt-5"></div>

      <Row>
        <Col md={5}>
          <Form.Row>
            <Form.Label>Email Address*</Form.Label>
          </Form.Row>
          {email_addresses.map((email, index) => {
            let error = {};
            if (errors?.seafarer_person?.email_addresses) {
              error = errors.seafarer_person.email_addresses[index] ?? {};
            }
            const removeEmail = () => handleRemoveField(REMOVE_ACTION_TYPES.email, email);
            return (
              <Form.Row key={email.localId}>
                <Form.Group as={Col} md={10}>
                  <Form.Control
                    type="text"
                    name="email"
                    value={email.contact}
                    onChange={(inputEv) => onEmailChange(inputEv, index)}
                    isInvalid={!!error.contact}
                    onBlur={handleBlur}
                    disabled={!isEditable}
                    data-testid="fml-seafarer-contact-add-email-address"
                  />
                  <Form.Control.Feedback type="invalid">{error.contact}</Form.Control.Feedback>
                </Form.Group>
                {index !== 0 && isEditable && (
                  <Form.Group as={Col} md={2}>
                    <Icon icon="remove" size={30} className="remove_icon" onClick={removeEmail} />
                  </Form.Group>
                )}
              </Form.Row>
            );
          })}
          {isEditable ? (
            <Button
              variant="outline-primary"
              size="sm"
              className="pl-3 pr-3"
              data-testid="fml-seafarer-email-address-add-another"
              onClick={() => handleAddField(ADD_ACTION_TYPES.email)}
            >
              Add Another
            </Button>
          ) : null}
        </Col>
      </Row>

      <SectionTitleComponent title={'Address'.toUpperCase()} />
      {addresses.map((address, index) => {
        const sectionTitle = `OTHER ADDRESS ${index}`;
        const removeAddress = () => handleRemoveField(REMOVE_ACTION_TYPES.address, address);

        let error = {};
        if (errors?.seafarer_person?.addresses) {
          error = errors.seafarer_person.addresses[index] ?? {};
        }

        return (
          <div key={address.id}>
            {index !== 0 && (
              <SubSectionTitleRemoveComponent
                onClick={removeAddress}
                title={sectionTitle}
                isEditable={isEditable}
              />
            )}
            <AddressDetailFields
              address={address}
              countries={countries}
              handleBlur={handleBlur}
              error={error}
              onChange={handleAddressChange}
              isEditable={isEditable}
            />
          </div>
        );
      })}
      {isEditable ? (
        <AddAnotherComponent  onClick={() => handleAddField(ADD_ACTION_TYPES.address)} />
      ) : null}
    </div>
  );
});

AddSeafarerContactDetails.propTypes = {
  seafarer: PropTypes.object,
  countries: PropTypes.object,
  errors: PropTypes.object,
  handleBlur: PropTypes.func,
  onSeafarerChange: PropTypes.func,
  dropDownData: PropTypes.object,
  roleConfig: PropTypes.object
};

export default AddSeafarerContactDetails;
