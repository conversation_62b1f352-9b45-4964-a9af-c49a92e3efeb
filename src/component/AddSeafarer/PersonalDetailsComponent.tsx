import React from 'react';
import { Form, Col } from 'react-bootstrap';
import CountryNationalityDropDownControl from '../AddSeafarer/CountryNationalityDropDownControl';
import FleetDatePicker from '../AddSeafarer/FleetDatePicker';
import Spinner from '../../component/common/Spinner';
import {
  FIRST_NAME_FIELD,
  MIDDLE_NAME_FIELD,
  LAST_NAME_FIELD,
  DATE_OF_BIRTH_FIELD,
  GENDER_FIELD,
  PLACE_OF_BIRTH_FIELD,
  COUNTRY_OF_BIRTH_FIELD,
  NATIONALITY_FIELD,
} from '../../constants/test-id/seafarer-form-test-id';
import PropTypes from 'prop-types';

const PersonalDetailsComponent = (props) => {
  const {
    person,
    countries,
    nationalities,
    errors,
    onPersonChange,
    onPersonalDetailsChange,
    hasDuplicates,
    namesAreClear,
    handleBlur,
    isSpinnerActive,
    isEditable,
  } = props;

  const onDateOfBirthChange = (value) => {
    const item = {...person};
    item.date_of_birth = value;
    onPersonalDetailsChange(item);
    onPersonChange(item);
  };

  const onInputChange = (event) => {
    const field = event.target.name;
    let value = event.target.value;

    if (!shouldChangeFieldValue(field, value)) {
      return;
    }

    const item = {...person};

    if (
      (field === 'first_name' || field === 'last_name' || field === 'middle_name') &&
      value === null
    ) {
      value = '';
    }

    item[field] = value;

    if (field === 'nationality_id') {
      delete item.nationality;
    }

    if (field === 'first_name' || field === 'last_name') {
      onPersonalDetailsChange(item);
    }

    onPersonChange(item, field);
  };

  const shouldChangeFieldValue = (field, value) => {
    if (field === 'first_name' || field === 'last_name' || field === 'middle_name') {
      if (!isNameValid(value)) {
        return false;
      }
    }
    return true;
  };

  const isNameValid = (value) => {
    if (value === '') {
      return true;
    }
    const regex = /^[a-z '-]+$/i;
    return regex.exec(value);
  };

  const isInvalidDate = !!errors.date_of_birth;

  const getClassForNames = () => {
    let className = null;

    if (hasDuplicates) {
      className = 'warning';
    }

    if (namesAreClear) {
      className = 'clear-name';
    }

    return className;
  };

  return (
    <div>
      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>First Name*</Form.Label>
          <Form.Control
            type="text"
            name="first_name"
            value={person.first_name}
            onChange={onInputChange}
            className={getClassForNames()}
            isInvalid={!!errors.first_name || hasDuplicates}
            disabled={!isEditable}
            data-testid={FIRST_NAME_FIELD}
          />
          <Form.Control.Feedback className={hasDuplicates ? 'warning' : null} type="invalid">
            {hasDuplicates ? 'Duplicate seafarer' : errors.first_name}
          </Form.Control.Feedback>
        </Form.Group>

        <Form.Group as={Col} md="1">
          <div className="small-spinner">{isSpinnerActive ? <Spinner /> : null}</div>
        </Form.Group>

        <Form.Group as={Col} md="5">
          <Form.Label>Middle Name</Form.Label>
          <Form.Control
            type="text"
            name="middle_name"
            value={person.middle_name}
            onChange={onInputChange}
            isInvalid={!!errors.middle_name}
            disabled={!isEditable}
            data-testid={MIDDLE_NAME_FIELD}
          />
          <Form.Control.Feedback type="invalid">{errors.middle_name}</Form.Control.Feedback>
        </Form.Group>
      </Form.Row>

      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Last Name*</Form.Label>
          <Form.Control
            type="text"
            name="last_name"
            value={person.last_name}
            onChange={onInputChange}
            className={getClassForNames()}
            isInvalid={!!errors.last_name || hasDuplicates}
            disabled={!isEditable}
            data-testid={LAST_NAME_FIELD}
          />
          <Form.Control.Feedback className={hasDuplicates ? 'warning' : null} type="invalid">
            {hasDuplicates ? 'Duplicate seafarer' : errors.last_name}
          </Form.Control.Feedback>
        </Form.Group>

        <Form.Group as={Col} md="1">
          <div className="small-spinner">{isSpinnerActive ? <Spinner /> : null}</div>
        </Form.Group>

        <Form.Group as={Col} md="5">
          <Form.Label>Date of Birth*</Form.Label>
          <FleetDatePicker
            name="date_of_birth"
            value={person.date_of_birth}
            onChange={onDateOfBirthChange}
            isInvalid={isInvalidDate}
            hasDuplicates={hasDuplicates}
            disabled={!isEditable}
            isValid={namesAreClear}
            testID={DATE_OF_BIRTH_FIELD}
          />
          <Form.Control.Feedback
            type="invalid"
            className={(() => {
              if (isInvalidDate) {
                return 'set-display-block';
              }
              if (hasDuplicates) {
                return 'set-display-block warning';
              }
              return '';
            })()}
          >
            {hasDuplicates ? 'Duplicate seafarer' : errors.date_of_birth}
          </Form.Control.Feedback>
        </Form.Group>

        <Form.Group as={Col} md="1">
          <div className="small-spinner">{isSpinnerActive ? <Spinner /> : null}</div>
        </Form.Group>
      </Form.Row>

      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Gender*</Form.Label>
          <Form.Control
            as="select"
            name="gender"
            value={person.gender ?? ''}
            selected={person.gender}
            onChange={onInputChange}
            onBlur={handleBlur}
            isInvalid={!!errors.gender}
            disabled={!isEditable}
            data-testid={GENDER_FIELD}
          >
            <option value="">Please select</option>
            <option value="male">Male</option>
            <option value="female">Female</option>
          </Form.Control>
          <Form.Control.Feedback type="invalid">Please select Gender.</Form.Control.Feedback>
        </Form.Group>

        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Place of Birth*</Form.Label>
          <Form.Control
            type="text"
            name="place_of_birth"
            value={person.place_of_birth}
            onChange={onInputChange}
            isInvalid={!!errors.place_of_birth}
            disabled={!isEditable}
            data-testid={PLACE_OF_BIRTH_FIELD}
          />
          <Form.Control.Feedback type="invalid">{errors.place_of_birth}</Form.Control.Feedback>
        </Form.Group>
      </Form.Row>

      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Country of Birth*</Form.Label>
          <CountryNationalityDropDownControl
            name={'country_of_birth_id'}
            selectedValue={person.country_of_birth_id}
            dropDownValues={countries}
            onBlur={handleBlur}
            isInvalid={!!errors.country_of_birth_id}
            onInputChange={onInputChange}
            disabled={!isEditable}
            testID={COUNTRY_OF_BIRTH_FIELD}
          />
          <Form.Control.Feedback
            type="invalid"
            className={errors?.country_of_birth_id ? 'set-display-block' : ''}
          >
            Please enter Country of Birth.
          </Form.Control.Feedback>
        </Form.Group>

        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Nationality*</Form.Label>
          <CountryNationalityDropDownControl
            name={'nationality_id'}
            selectedValue={person.nationality_id}
            dropDownValues={nationalities}
            onInputChange={onInputChange}
            onBlur={handleBlur}
            isInvalid={!!errors.nationality_id}
            disabled={!isEditable}
            testID={NATIONALITY_FIELD}
          />
          <Form.Control.Feedback
            type="invalid"
            className={errors.nationality_id ? 'set-display-block' : ''}
          >
            Please select Nationality.
          </Form.Control.Feedback>
        </Form.Group>
      </Form.Row>
    </div>
  );
};

PersonalDetailsComponent.propTypes = {
  person: PropTypes.object,
  countries: PropTypes.array,
  nationalities: PropTypes.array,
  onPersonChange: PropTypes.func,
  onPersonalDetailsChange: PropTypes.func,
  errors: PropTypes.object,
  handleBlur: PropTypes.func,
  hasDuplicates: PropTypes.bool,
  isSpinnerActive: PropTypes.bool,
  isEditable: PropTypes.bool,
  namesAreClear: PropTypes.bool,
};

export default PersonalDetailsComponent;
