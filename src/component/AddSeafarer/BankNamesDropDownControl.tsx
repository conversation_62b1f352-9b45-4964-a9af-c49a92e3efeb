import React from 'react';
import { Typeahead } from 'react-bootstrap-typeahead';

const BankNamesDropDownControl = ({
  bankNameList,
  selectedValue,
  onInputChange,
  name,
  isInvalid,
  disabled,
}) => {
  const onBankNameChange = (option) => {
    const bankName = option.length > 0 ? option[0].bank_name : undefined;
    const result = { target: { name: name, value: bankName, data: option[0] } };
    onInputChange(result);
  };

  const getOptionById = (bankName) => {
    const result = (bankNameList ?? []).filter((item) => {
      return item.bank_name === bankName;
    });
    return result.length > 0 ? result[0] : undefined;
  };

  const selected = getOptionById(selectedValue);
  return (
    <Typeahead
      id="bank-name"
      labelKey="bank_name"
      onChange={onBankNameChange}
      options={bankNameList}
      placeholder="Please select"
      selected={selected ? [selected] : []}
      disabled={disabled}
      isInvalid={isInvalid}
    />
  );
};

export default BankNamesDropDownControl;
