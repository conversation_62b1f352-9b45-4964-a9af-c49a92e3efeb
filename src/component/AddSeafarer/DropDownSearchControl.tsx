/* eslint-disable react/display-name */
import React, { Fragment, useRef } from 'react';
import { Menu, MenuItem, Typeahead } from 'react-bootstrap-typeahead';
import { Icon } from '../../styleGuide';
import PropTypes from 'prop-types';
import _ from 'lodash';
import { Button } from 'react-bootstrap';

const OBJECT_ID_VALUE_DROPDOWN_VALUE_TYPE = 'OBJECT_ID_VALUE_DROPDOWN_VALUE_TYPE';
const PRIMITIVE_DROPDOWN_VALUE_TYPE = 'PRIMITIVE_DROPDOWN_VALUE_TYPE';

const DropDownSearchControl = ({
  dropDownValues,
  name,
  onInputChange,
  selectedValue,
  disabled,
  isInvalid,
  testID,
  multiple = false,
  placeholder = 'Please select',
}) => {
  const typeaheadRef = useRef(null);
  let dropDownValueType;

  if (dropDownValues?.length) {
    const dropDownValue = dropDownValues[0];

    if (typeof dropDownValue === 'object' && dropDownValue.constructor === Object) {
      //plain object
      if (Object.hasOwn(dropDownValue, 'id') && Object.hasOwn(dropDownValue, 'value')) {
        dropDownValueType = OBJECT_ID_VALUE_DROPDOWN_VALUE_TYPE;
      }
    } else if (typeof dropDownValue !== 'object') {
      //primitive type
      dropDownValueType = PRIMITIVE_DROPDOWN_VALUE_TYPE;
    }
  } else {
    //for empty array
    dropDownValueType = PRIMITIVE_DROPDOWN_VALUE_TYPE;
  }

  if (dropDownValueType === undefined) {
    throw Error('format of dropDownValues is not supported, dropDownValues: ', dropDownValues);
  }

  let options, onChange, selected;

  if (dropDownValueType === OBJECT_ID_VALUE_DROPDOWN_VALUE_TYPE) {
    options = (dropDownValues ?? []).map((item) => {
      return item.value;
    });

    onChange = (value) => {
      if (multiple) {
        let ids = value.map((item) => getIdOfSelectedOption(item));
        const isExists = selectedValue?.find((val) => val === ids[ids.length - 1]);
        if (isExists) {
          // Remove unselected value from dropdown
          ids = ids.filter((id) => id !== isExists);
        }
        const results = { target: { name: name, value: ids } };
        onInputChange(results);
      } else {
        const id = getIdOfSelectedOption(value[0]);
        const result = { target: { name: name, value: id } };
        onInputChange(result);
      }
    };

    const getIdOfSelectedOption = (value) => {
      const result = (dropDownValues ?? []).filter((item) => {
        return item.value === value;
      });
      return result.length > 0 ? result[0].id : undefined;
    };

    const getOptionById = (id) => {
      const result = (dropDownValues ?? []).filter((item) => {
        return typeof item.id === 'object' ? _.isEqual(item.id, id) : item.id === id;
      });
      return result.length > 0 ? result[0].value : undefined;
    };

    if (multiple) {
      selected = selectedValue?.map((item) => getOptionById(item));
    } else {
      const selectedOption = getOptionById(selectedValue);
      selected = selectedOption ? [selectedOption] : [];
    }
  } else if (dropDownValueType === PRIMITIVE_DROPDOWN_VALUE_TYPE) {
    options = dropDownValues;

    onChange = (value) => {
      if (multiple) {
        throw Error('Not supporting primitve dropdown values for multiple select for now');
      } else {
        const result = { target: { name: name, value: value[0] } };
        onInputChange(result);
      }
    };

    selected = selectedValue ? [selectedValue] : [];
  }

  const validateArgument = () => {
    if (dropDownValues?.length && options === undefined) {
      throw Error('options cannot be undefined');
    }

    if (onChange === undefined) {
      throw Error('onChange cannot be undefined');
    }

    if (selectedValue !== undefined && selected === undefined) {
      throw Error('selected cannot be undefined');
    }
  };

  validateArgument();

  /**
   * Custom Dropdown Input/Menu design
   */
  const customDropdownDesignProps = {};
  if (multiple) {
    // selected = selectedValue.map((item) => getOptionById(item));
    // Custom Dropdown Input design
    customDropdownDesignProps.renderToken = (option, { onRemove }, index) => {
      return (
        <span className="spantoken" key={`${option} ${index}`}>
          {' '}
          {`${option}${selected?.length !== index + 1 ? ', ' : ''}`}
        </span>
      );
    };
    // Custom Dropdown menu design
    customDropdownDesignProps.renderMenu = (results, menuProps, state) => {
      const items = _.uniq([...(state?.selected ?? []), ...results]).map((option, index) => {
        const menuItemProps = {
          option,
        };
        if (option?.paginationOption) {
          return (
            <Fragment key="pagination-item">
              <Menu.Divider />
              <MenuItem
                {...menuItemProps}
                className="rbt-menu-pagination-option"
                label={'Display additional results...'}
              >
                {'Display additional results...'}
              </MenuItem>
            </Fragment>
          );
        }
        return (
          <MenuItem
            key={`${option?.toString()}`}
            checked={true}
            {...menuItemProps}
            position={index}
          >
            <>
              {state.selected.includes(option) ? (
                <Icon
                  icon="checked"
                  size={20}
                  className="float-left screening-page__moved-tick check-icon"
                />
              ) : (
                <span className="empty-icon-padding"></span>
              )}
              {option?.toString()}
            </>
          </MenuItem>
        );
      });
      return <Menu id={menuProps.id}>{items}</Menu>;
    };
    // Show all the options including selected in dropdown menu
    customDropdownDesignProps.filterBy = (option, props) => {
      return true;
    };
    customDropdownDesignProps.className = 'multi-dropdown';
  }
  return (
    <div className="col mx-0 px-0">
      <Typeahead
        id={name}
        key={name}
        inputProps={{ 'data-testid': testID }}
        ref={typeaheadRef}
        onChange={onChange}
        options={options}
        placeholder={placeholder}
        selected={selected}
        disabled={disabled}
        isInvalid={isInvalid}
        multiple={multiple}
        {...customDropdownDesignProps}
      />
      <Button
        variant="link"
        className="typeable-dropdown-icon-style"
        onClick={() => {
          // @ts-ignore
          if (typeaheadRef) typeaheadRef?.current?.focus();
        }}
      >
        {Icon && <Icon icon="dropdown" size={20} />}
      </Button>
    </div>
  );
};

DropDownSearchControl.propTypes = {
  dropDownValues: PropTypes.array,
  name: PropTypes.string,
  onInputChange: PropTypes.func,
  selectedValue: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  disabled: PropTypes.bool,
  isInvalid: PropTypes.bool,
  placeholder: PropTypes.string,
  testID: PropTypes.string,
  multiple: PropTypes.bool,
};

export default DropDownSearchControl;
