import React, { useRef } from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { stringAsDate, dateAsString } from '../../model/utils';
import PropTypes from 'prop-types';
import { Calendar4 } from 'react-bootstrap-icons';

const FleetDatePicker = ({
  name,
  value,
  onChange,
  onBlur,
  placeholder = 'Please select',
  isInvalid,
  hasDuplicates,
  isValid,
  disabled,
  testID,
  isClearable,
  maxDate = null,
  minDate = null,
  calendarIcon = false,
}) => {
  const showCalenderIcon =
    (calendarIcon && !isInvalid && !hasDuplicates) && !(isClearable && !!value);
  const datePickerRef = useRef<DatePicker>(null);
  const openDatePicker = () => {
    if (datePickerRef.current) {
      datePickerRef.current.setFocus();
    }
  };

  const onDateChange = (date) => {
    onChange(dateAsString(date));
  };

  let className = 'form-control';
  let wrapperClassName = '';

  if (isInvalid) {
    className = 'form-control is-invalid';
  }

  if (hasDuplicates) {
    className = 'form-control is-invalid warning';
  }

  if (disabled) {
    className = 'date-picker-disabled';
    wrapperClassName = 'date-picker-wrapper-disabled';
  }

  if (isValid) {
    className = 'form-control clear-name';
  }

  return (
    <div className={`position-relative ${wrapperClassName}`}>
      <DatePicker
        ref={datePickerRef}
        name={name}
        customInput={<input data-testid={testID} type="text" />}
        isClearable={isClearable}
        peekNextMonth
        showMonthDropdown
        dateFormatCalendar=" "
        showYearDropdown
        dropdownMode="select"
        selected={stringAsDate(value)}
        onChange={onDateChange}
        onBlur={onBlur}
        placeholderText={placeholder}
        dateFormat="d MMM yyyy"
        className={className}
        autoComplete={'none'}
        disabled={disabled}
        maxDate={maxDate}
        minDate={minDate}
      />
      {showCalenderIcon && (
        <Calendar4
          className="position-absolute position-middle"
          size={18}
          onClick={openDatePicker}
        />
      )}
    </div>
  );
};

FleetDatePicker.propTypes = {
  name: PropTypes.string,
  value: PropTypes.string,
  onChange: PropTypes.func,
  placeholder: PropTypes.string,
  isInvalid: PropTypes.bool,
  hasDuplicates: PropTypes.bool,
  isValid: PropTypes.bool,
  disabled: PropTypes.bool,
  testID: PropTypes.string,
  isClearable: PropTypes.bool,
  maxDate: PropTypes.any,
};

export default FleetDatePicker;
