import React, { useState, useRef, forwardRef } from 'react';
import { Form, Col } from 'react-bootstrap';
import { v4 as uuid } from 'uuid';
import { useDebouncedCallback } from 'use-debounce';

import PersonalDetailsComponent from './PersonalDetailsComponent';
import PassportComponent from './PassportComponent';
import SeamanBookComponent from './SeamanBookComponent';
import ConfirmActionModalView from './ConfirmActionModalView';
import DropDownSearchControl from '../AddSeafarer/DropDownSearchControl';

import AddSeafarerOtherComponents from './AddSeafarerOtherComponents';
import DuplicatesController from '../../controller/duplicates-controller';

import { searchType, shipPartyType } from '../../model/constants';
import { RANK_FIELD, REPORTING_OFFICE_FIELD } from '../../constants/test-id/seafarer-form-test-id';
import PropTypes from 'prop-types';

const {
  AddAnotherComponent,
  SectionTitleComponent,
  SectionTitleComponentWithRemove,
  SubSectionTitleRemoveComponent,
} = AddSeafarerOtherComponents;

const defaultModal = {
  isVisible: false,
  type: undefined,
  uuid: undefined,
  index: undefined,
  title: undefined,
  message: undefined,
};

const BasicForm = forwardRef((props, ref) => {
  const {
    seafarer,
    dropDownData,
    errors,
    handleBlur,
    onSeafarerChange,
    roleConfig,
    seafarerId,
    portsMap,
    cacheDuplicates,
    cachedDuplicates,
    keycloak,
    originalSeafarer
  } = props;

  const { ranks, offices, manningAgents } = dropDownData;
  const { basicRef, personalDetailsRef, passportRef, seamanBookRef } = ref;
  const isEditMode = !!seafarerId;
  const personData = (seafarer?.seafarer_person) || {};
  const currentPassports = personData.passports ?? [{}];
  const currentSeamansBooks = personData.seaman_books ?? [{}];

  const { current: duplicatesController } = useRef(new DuplicatesController());
  const [loadingPassport, setLoadingPassport] = useState(null);
  const [loadingSeamansBook, setLoadingSeamansBook] = useState(null);
  const [loadingPersonalDetails, setLoadingPersonalDetails] = useState(null);
  const [namesAreClear, setNamesAreClear] = useState(null);

  const [modal, setModal] = useState(defaultModal);

  const countries = dropDownData.countries ?? [];
  const nationalities = dropDownData.nationalities ?? [];

  const cachedPassport = cachedDuplicates.passport ?? {};
  const cachedSeamansBook = cachedDuplicates.seamansBook ?? {};
  const cachedPersonalDetails = cachedDuplicates.personalDetails ?? {};

  const handleAddPassport = () => {
    const newPassports = [...currentPassports, { localId: uuid() }];
    const changedSeafarer = {
      ...seafarer,
      seafarer_person: { ...personData, passports: newPassports },
    };
    onSeafarerChange(changedSeafarer);
  };

  const handleAddSeamansBook = () => {
    const newBooks = [...currentSeamansBooks, { localId: uuid() }];
    const changedSeafarer = {
      ...seafarer,
      seafarer_person: { ...personData, seaman_books: newBooks },
    };
    onSeafarerChange(changedSeafarer, 'seaman_books');
  };

  const handleRemoveModal = (index, uuid, type) => {
    const entity = type === 'seamans_book' ? 'Seaman’s Book' : 'Passport';
    setModal({
      type,
      index,
      title: `Confirm Removing ${entity} ${index}?`,
      message: `Are you sure removing ${entity} ${index}?`,
      isVisible: true,
      uuid,
    });
  };

  const handlePassportRemove = () => {
    const oldPassports = [...currentPassports];
    const updatedPassport = [...oldPassports].filter((p) => {
      if (p?.id)
        return p.id !== modal.uuid;
      else
        return p.localId !== modal.uuid;
    });

    const changedSeafarer = {
      ...seafarer,
      seafarer_person: { ...personData, passports: updatedPassport },
    };

    onSeafarerChange(changedSeafarer);
  };

  const handleBookRemove = () => {
    const oldBooks = [...currentSeamansBooks];
    const updatedBooks = [...oldBooks].filter((sb) => {
      if (sb?.id)
        return sb.id !== modal.uuid;
      else
        return sb.localId !== modal.uuid;
    });

    const changedSeafarer = {
      ...seafarer,
      seafarer_person: { ...personData, seaman_books: updatedBooks },
    };

    onSeafarerChange(changedSeafarer, 'seaman_books');
  };

  const handleRemoveItem = () => {
    const { type } = modal;
    if (type == 'passport') {
      handlePassportRemove();
    }
    if (type == 'seamans_book') {
      handleBookRemove();
    }
    setModal(defaultModal);
  };

  const onPersonChange = (data, field) => {
    const changedSeafarer = seafarer;
    changedSeafarer.seafarer_person = data;
    onSeafarerChange(changedSeafarer, field);
  };

  const onPassportChange = (passport) => {
    const oldPassports = [...currentPassports];
    const newPassports = oldPassports.map((p) => {
      if ((p.id && passport.id && p.id === passport.id) || (p.localId === passport.localId && passport.localId)) {
        p = passport;
      }
      return p;
    });

    const changedSeafarer = {
      ...seafarer,
      seafarer_person: { ...personData, passports: newPassports },
    };

    onSeafarerChange(changedSeafarer);
  };

  const onSeamanBookChange = (seamanBook) => {
    const oldBooks = [...currentSeamansBooks];
    const newBooks = oldBooks.map((oldSeamanBook) => {
      if ((oldSeamanBook.id && seamanBook.id && oldSeamanBook.id === seamanBook.id) || (oldSeamanBook.localId === seamanBook.localId && seamanBook.localId)) {
        oldSeamanBook = seamanBook;
      }
      return oldSeamanBook;
    });

    const changedSeafarer = {
      ...seafarer,
      seafarer_person: { ...personData, seaman_books: newBooks },
    };

    onSeafarerChange(changedSeafarer, 'seaman_books');
  };

  const onInputChange = (event) => {
    const changedSeafarer = seafarer;
    const field = event.target.name;
    const value = event.target.value;
    changedSeafarer[field] = value;
    onSeafarerChange(changedSeafarer, field, value);
  };

  const isBelongToSameNumber = (number, type, index) => {
    if (
      number === null ||
      originalSeafarer?.seafarer_person?.passports[index]?.number === null ||
      originalSeafarer?.seafarer_person?.seaman_books[index]?.number === null
    ) {
      return false;
    }
    return type === 'passport' ? number?.toUpperCase() === originalSeafarer?.seafarer_person?.passports[index]?.number?.toUpperCase() :
      originalSeafarer?.seafarer_person?.seaman_books.some(book => number?.toUpperCase() === book?.number?.toUpperCase());
  }

  // Check Seafarer duplicates based on Passport number
  const onPassportNumberChange = async (passport, index) => {
    if (isEditMode && passport?.seafarer_person_id && isBelongToSameNumber(passport.number, 'passport', index)) {
      return;
    }
    passport.number = passport.number.toUpperCase();

    cacheDuplicates([], searchType.PASSPORT, null);

    const cachedValue = duplicatesController.getCachedPassportsResult(trimmed(passport.number));
    if (cachedValue) {
      cacheDuplicates(cachedValue, searchType.PASSPORT, passport);
      return;
    }

    await findDuplicatesForPassport(passport);
  };

  const checkDuplicatesForPassport = (passport, index) => {
    const cachedValue = duplicatesController.getCachedPassportsResult(trimmed(passport.number));
    if (isEditMode && passport?.seafarer_person_id && isBelongToSameNumber(passport.number, 'passport', index)) {
      return false;
    }

    if (cachedValue && cachedValue.length > 0) {
      return true;
    }
    return cachedPassport?.document?.number && trimmed(cachedPassport.document.number) === trimmed(passport.number);
  };

  const findDuplicatesForPassport = useDebouncedCallback(async (passport) => {
    setLoadingPassport(passport);
    const result = await duplicatesController.loadSeafarersByPassportNumber(
      trimmed(passport.number),
      seafarer,
    );
    if (result.length > 0) {
      cacheDuplicates(result, searchType.PASSPORT, passport);
    }
    setLoadingPassport(null);
  }, 700);

  const isLoadingPassport = (passport) => {
    if (loadingPassport === null) {
      return false;
    }

    const loadingNumber = trimmed(loadingPassport.number);

    return loadingNumber === trimmed(passport.number) && loadingNumber !== '';
  };

  // Check Seafarer duplicates based on Seaman's Book number
  const onSeamansBookNumberChange = async (seamanBook, index) => {
    if (isEditMode && (seamanBook?.seafarer_person_id || seamanBook?.localId) && isBelongToSameNumber(seamanBook.number, 'seaman_book', index)) {
      return;
    }
    seamanBook.number = seamanBook.number.toUpperCase();

    cacheDuplicates([], searchType.SEAMANS_BOOK, null);

    const cachedValue = duplicatesController.getCachedSeamansBooksResult(
      trimmed(seamanBook.number),
    );
    if (cachedValue) {
      cacheDuplicates(cachedValue, searchType.SEAMANS_BOOK, seamanBook);
      return;
    }

    await findDuplicatesForSeamansBook(seamanBook);
  };

  const checkDuplicatesForSeamansBook = (seamanBook, index) => {
    if (isEditMode && (seamanBook?.seafarer_person_id || seamanBook?.localId) && isBelongToSameNumber(seamanBook.number, 'seaman_book', index)) {
      return;
    }
    const cachedValue = duplicatesController.getCachedSeamansBooksResult(
      trimmed(seamanBook.number),
    );

    if (cachedValue && cachedValue.length > 0) {
      return true;
    }

    return cachedSeamansBook?.document?.number && trimmed(cachedSeamansBook.document.number) === trimmed(seamanBook?.number);
  };

  const findDuplicatesForSeamansBook = useDebouncedCallback(async (seamanBook) => {
    setLoadingSeamansBook(seamanBook);
    const result = await duplicatesController.loadSeafarersBySeamansBookNumber(
      trimmed(seamanBook.number),
      seafarer,
    );
    if (result.length > 0) {
      cacheDuplicates(result, searchType.SEAMANS_BOOK, seamanBook);
    }
    setLoadingSeamansBook(null);
  }, 700);

  const isLoadingSeamansBook = (seamanBook) => {
    if (loadingSeamansBook === null) {
      return false;
    }

    const loadingNumber = trimmed(loadingSeamansBook.number);

    return loadingNumber === trimmed(seamanBook.number) && loadingNumber !== '';
  };

  // Check Seafarer Duplicates based on Personal Details (First name, Last name and Date of Birth)
  const onPersonalDetailsChange = async (person) => {
    if (isEditMode) {
      return;
    }

    setNamesAreClear(false);
    cacheDuplicates([], searchType.PERSONAL_DETAILS, null);

    const personalDetails = getPersonalDetails(person);

    const cachedValue = duplicatesController.getCachedPersonalDetailsResult(personalDetails);
    if (cachedValue) {
      cacheDuplicates(cachedValue, searchType.PERSONAL_DETAILS, personalDetails);
      if (cachedValue?.length === 0) {
        setNamesAreClear(true);
      }
      return;
    }

    await findDuplicatesForPersonalDetails(personalDetails);
  };

  const checkDuplicatesForPersonalDetails = (person) => {
    const personalDetails = getPersonalDetails(person);
    const cachedValue = duplicatesController.getCachedPersonalDetailsResult(personalDetails);
    if (cachedValue && cachedValue.length > 0) {
      return true;
    }

    const cachedData = cachedPersonalDetails.document ?? {};
    const sameFirstName =
      trimmed(cachedData.first_name) === trimmed(personalDetails.first_name) &&
      trimmed(personalDetails.first_name) !== '';

    const sameLastName =
      trimmed(cachedData.last_name) === trimmed(personalDetails.last_name) &&
      trimmed(personalDetails.last_name) !== '';

    const dateOfBirth =
      trimmed(cachedData.date_of_birth) === trimmed(personalDetails.date_of_birth) &&
      trimmed(personalDetails.date_of_birth) !== '';

    return sameFirstName && sameLastName && dateOfBirth;
  };

  const findDuplicatesForPersonalDetails = useDebouncedCallback(async (personalDetails) => {
    setLoadingPersonalDetails(personalDetails);

    const result = await duplicatesController.loadSeafarersByPersonalDetails(
      personalDetails,
      seafarer,
    );

    if (result) {
      if (result.length > 0) {
        cacheDuplicates(result, searchType.PERSONAL_DETAILS, personalDetails);
        setNamesAreClear(false);
      } else {
        setNamesAreClear(true);
      }
    }
    setLoadingPersonalDetails(null);
  }, 700);

  const isLoadingPersonalDetails = (person) => {
    if (person === null) {
      return false;
    }

    const personalDetails = getPersonalDetails(person);
    const key = JSON.stringify(personalDetails);
    const loadingKey = JSON.stringify(loadingPersonalDetails);

    return loadingKey === key;
  };

  const getPersonalDetails = (person) => {
    if (person === undefined) {
      return {};
    }

    return {
      first_name: trimmed(person.first_name),
      last_name: trimmed(person.last_name),
      date_of_birth: trimmed(person.date_of_birth),
    };
  };

  const trimmed = (value) => {
    if (value) {
      return value.trim();
    }
    return '';
  };

  const onIsOriginalCheckboxClick = (currentBook) => {
    const oldBooks = [...currentSeamansBooks];
    const newBooks = oldBooks.map((book) => {
      if (book.id && currentBook.id) {
        book.is_original = currentBook.id === book.id;
      } else {
        book.is_original = currentBook.localId === book.localId;
      }
      return book;
    });

    const changedSeafarer = {
      ...seafarer,
      seafarer_person: { ...personData, seaman_books: newBooks },
    };
    onSeafarerChange(changedSeafarer, 'seaman_books');
  };

  const getOfficeDropdown = () => {
    if (keycloak && keycloak.ship_party_type == shipPartyType.MANNING_AGENT) {
      return [offices.find((office) => office.ship_party_id === keycloak.ship_party_id)];
    }
    return offices;
  };

  const roles = roleConfig.seafarer;
  const isPersonalDetailsEditable = roles.edit.personalDetails;
  const isPassportEditable = roles.edit.passport;
  const isSeamanBookEditable = roles.edit.seamansBook;
  const isRankEditable = roles.edit.rank || (!isEditMode);
  const isSeafarerSignedOn = seafarer?.seafarer_person?.current_journey_status === 'signed_on';
  const isOfficeEditable = roles.edit.reportingOffice || (!isEditMode);
  return (
    <div className="add_seafarer_page">
      <div className="mt-5 mb-4">*Required Fields</div>

      <Form.Row ref={basicRef}>
        <Form.Group as={Col} md="5">
          <Form.Label>Rank*</Form.Label>
          <DropDownSearchControl
            name={'rank_id'}
            selectedValue={seafarer.rank_id}
            dropDownValues={ranks}
            onBlur={handleBlur}
            isInvalid={!!errors.rank_id}
            onInputChange={onInputChange}
            disabled={!isRankEditable || isSeafarerSignedOn}
            testID={RANK_FIELD}
          />
          <Form.Control.Feedback type="invalid" className={errors.rank_id ? 'set-display-block' : ''}>Please select a Rank.</Form.Control.Feedback>
        </Form.Group>

        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Reporting Office*</Form.Label>
          <DropDownSearchControl
            name={'office_id'}
            selectedValue={seafarer.office_id}
            dropDownValues={getOfficeDropdown()}
            onBlur={handleBlur}
            isInvalid={!!errors.office_id}
            onInputChange={onInputChange}
            disabled={!isOfficeEditable}
            testID={REPORTING_OFFICE_FIELD}
          />
          <Form.Control.Feedback type="invalid" className={errors.office_id ? 'set-display-block' : ''}>
            Please select Reporting Office.
          </Form.Control.Feedback>
        </Form.Group>
      </Form.Row>
      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Manning Agency</Form.Label>
          <DropDownSearchControl
            name={'manning_agent_id'}
            selectedValue={seafarer.manning_agent_id}
            dropDownValues={manningAgents?.length ? manningAgents : []}
            onBlur={handleBlur}
            isInvalid={!!errors.manning_agency_id}
            onInputChange={onInputChange}
            testID="manning-agency"
          />
          <Form.Control.Feedback
            type="invalid"
            className={errors.manning_agent_id ? 'set-display-block' : ''}
          >
            Please select Manning Agency.
          </Form.Control.Feedback>
        </Form.Group>
      </Form.Row>

      <div ref={personalDetailsRef} id="Basic Personal Details">
        <SectionTitleComponent title={'Personal Details'} />
        <PersonalDetailsComponent
          countries={countries}
          person={seafarer.seafarer_person || {}}
          errors={errors.seafarer_person ?? {}}
          isEditable={isPersonalDetailsEditable}
          seafarer={seafarer}
          nationalities={nationalities}
          onPersonChange={onPersonChange}
          onPersonalDetailsChange={onPersonalDetailsChange}
          hasDuplicates={checkDuplicatesForPersonalDetails(seafarer.seafarer_person)}
          isSpinnerActive={isLoadingPersonalDetails(seafarer.seafarer_person)}
          namesAreClear={namesAreClear}
        />
      </div>

      <div ref={passportRef} id="Basic Passport Details">
        <SectionTitleComponent title="Passport Details" isEditable={isPassportEditable} />
        {currentPassports.map((passport, index) => {
          const sectionTitle = `OTHER PASSPORT ${index}`;
          let passportErros = {};
          if (errors?.seafarer_person?.passports) {
            passportErros = errors.seafarer_person.passports[index] ?? {};
          }
          const uuid = passport?.id ? passport.id : passport.localId;
          return (
            <div key={uuid}>
              {index === 0 ? null : (
                <SubSectionTitleRemoveComponent
                  onClick={handleRemoveModal.bind(this, index, uuid, 'passport')}
                  title={sectionTitle}
                  isEditable={isPassportEditable}
                />
              )}
              <PassportComponent
                countries={countries}
                passport={passport}
                errors={passportErros}
                isEditable={isPassportEditable}
                onPassportChange={onPassportChange}
                onPassportNumberChange={onPassportNumberChange}
                hasDuplicates={checkDuplicatesForPassport(passport, index)}
                isSpinnerActive={isLoadingPassport(passport)}
                index={index}
              />
            </div>
          );
        })}
        {isPassportEditable ? <AddAnotherComponent onClick={handleAddPassport} /> : null}
      </div>

      <div ref={seamanBookRef} id="Basic Seaman Book">
        {currentSeamansBooks.map((seamansBook, index) => {
          const sectionTitle = `OTHER SEAMAN's BOOK ${index}`;

          let seamanBooksErros = {};
          if (errors?.seafarer_person?.seaman_books) {
            seamanBooksErros = errors.seafarer_person.seaman_books[index] ?? {};
          }

          let ports = [];
          if (seamansBook?.country_id) {
            const country = countries.find(({ id }) => id === seamansBook.country_id);
            if (country && portsMap[country.alpha2_code]) {
              ports = portsMap[country.alpha2_code];
            }
          }
          const uuid = seamansBook?.id ? seamansBook.id : seamansBook.localId;
          return (
            <div key={uuid}>
              {index === 0 ? (
                <SectionTitleComponentWithRemove
                  title="Seaman’s Book"
                  isEditable={isSeamanBookEditable}
                  isRemoveAble={currentSeamansBooks.length > 1}
                  onClick={handleRemoveModal.bind(this, index, uuid, 'seamans_book')}
                  index={index}
                />
              ) : (
                <SubSectionTitleRemoveComponent
                  onClick={handleRemoveModal.bind(this, index, uuid, 'seamans_book')}
                  title={sectionTitle}
                  index={index}
                  isEditable={isSeamanBookEditable}
                />
              )}
              <SeamanBookComponent
                countries={countries}
                ports={ports}
                seamanBook={seamansBook}
                errors={seamanBooksErros}
                isEditable={isSeamanBookEditable}
                onSeamanBookChange={onSeamanBookChange}
                handleIsOriginalClick={onIsOriginalCheckboxClick}
                onSeamansBookNumberChange={onSeamansBookNumberChange}
                hasDuplicates={checkDuplicatesForSeamansBook(seamansBook, index)}
                isSpinnerActive={isLoadingSeamansBook(seamansBook)}
                rankId={seafarer.rank_id}
                ranks={ranks}
                index={index}
              />
            </div>
          );
        })}
        {isSeamanBookEditable ? <AddAnotherComponent testId="SeamanBook-Add-button" onClick={handleAddSeamansBook} /> : null}
      </div>
      <ConfirmActionModalView
        show={modal.isVisible}
        onClose={() => setModal(defaultModal)}
        onConfirm={handleRemoveItem}
        title={modal.title}
        message={modal.message}
      />
    </div>
  );
});

BasicForm.propTypes = {
  seafarer: PropTypes.object,
  dropDownData: PropTypes.object,
  errors: PropTypes.object,
  handleBlur: PropTypes.func,
  onSeafarerChange: PropTypes.func,
  portsMap: PropTypes.object,
  cacheDuplicates: PropTypes.func,
  roleConfig: PropTypes.object,
  seafarerId: PropTypes.number,
  cachedDuplicates: PropTypes.object,
  keycloak: PropTypes.object,
  originalSeafarer: PropTypes.object,
};

export default BasicForm;
