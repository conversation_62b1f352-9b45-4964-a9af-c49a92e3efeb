import React from 'react';
import { Alert } from 'react-bootstrap';
import { TELEPHON<PERSON>, MO<PERSON>LE, EMAIL } from '../../constants/contactTypes';
import { filterByContactType, joinContacts } from '../../util/view-utils';
import PropTypes from 'prop-types';
import './scss/paris1-disabled-textfield.scss';

const LabelContactMap = {
  [TELEPHONE]: 'Telephone Number',
  [MOBILE]: 'Mobile Number',
  [EMAIL]: 'Email',
};

const InvalidContactMessage = ({ contacts }) => {
  const invalidContacts = [];

  const invalidTelephones = joinContacts(contacts.filter(filterByContactType(TELEPHONE)));
  const invalidMobiles = joinContacts(contacts.filter(filterByContactType(MOBILE)));
  const invalidEmails = joinContacts(contacts.filter(filterByContactType(EMAIL)));

  invalidContacts.push({ type: TELEPHONE, value: invalidTelephones });
  invalidContacts.push({ type: MOBI<PERSON>, value: invalidMobiles });
  invalidContacts.push({ type: EMAIL, value: invalidEmails });

  return invalidContacts.map((contact, index) => {
    const { value, type } = contact;
    if (value) {
      return (
        <li key={type}>
          {LabelContactMap[type]}: {value}
        </li>
      );
    }
    return null;
  });
};

InvalidContactMessage.propTypes = {
  contacts: PropTypes.array,
};

const DisplayInvalidContacts = ({ invalidContacts }) => {
  if (Object.keys(invalidContacts).length === 0) return null;
  return (
    <>
      <p className="paris1-disabled-section-subtitle">Contact Details</p>
      <ul>
        {Object.keys(invalidContacts).map((key) => (
          <InvalidContactMessage key={key} contacts={invalidContacts[key]} />
        ))}
      </ul>
    </>
  );
};

DisplayInvalidContacts.propTypes = {
  invalidContacts: PropTypes.object,
};

const DisplayInvalidFamilyContacts = ({ invalidFamilyContact }) => {
  if (Object.keys(invalidFamilyContact).length === 0) return null;
  return (
    <>
      <p className="paris1-disabled-section-subtitle">Personal Particulars</p>
      <ul>
        {invalidFamilyContact.telephone && (
          <li>Next of Kin, Telephone Number: {invalidFamilyContact.telephone}</li>
        )}
        {invalidFamilyContact.mobilephone && (
          <li>Next of Kin, Mobile Number: {invalidFamilyContact.mobilephone}</li>
        )}
        {invalidFamilyContact.email && <li>Next of Kin, Email: {invalidFamilyContact.email}</li>}
      </ul>
    </>
  );
};

DisplayInvalidFamilyContacts.propTypes = {
  invalidFamilyContact: PropTypes.object,
};

const InvalidDataComponent = ({ seafarer, displayContactError, displayFamilyError }) => {
  const person = seafarer.seafarer_person || {};
  const invalidData = person.invalid_data || {};
  const invalidContacts = invalidData.contacts || {};
  const invalidFamilyContact = invalidData.family_members || {};
  if (Object.keys(invalidData).length > 0) {
    return (
      <Alert className="takeover_error_list disabled-paris1-text-field-bloc" variant="danger">
        <Alert.Heading>
          The following data from PARIS 1.0 needs to be filled correctly in the fields,
        </Alert.Heading>
        {displayContactError && <DisplayInvalidContacts invalidContacts={invalidContacts} />}
        {displayFamilyError && (
          <DisplayInvalidFamilyContacts invalidFamilyContact={invalidFamilyContact} />
        )}
      </Alert>
    );
  }
  return null;
};

InvalidDataComponent.propTypes = {
  seafarer: PropTypes.object,
  displayContactError: PropTypes.bool,
  displayFamilyError: PropTypes.bool,
};

export default InvalidDataComponent;
