import React from 'react';

export const travelModalGridColumnDefs = [
  {
    Header: 'No.',
    accessor: 'id',
    Footer: null,
    disableSortBy: true,
    width: 25,
    minWidth: 25,
  },
  {
    Header: 'HKID',
    accessor: 'hkid',
    Cell: ({ cell, row }) => {
      return (
        <a href={`/seafarer/details/${row.original.id}/general`} target="_blank">
          {cell.value}
        </a>
      );
    },
    Footer: null,
    disableSortBy: true,
    width: 30,
    minWidth: 30,
  },
  {
    Header: 'First Name',
    id: 'firstName',
    accessor: (row) => (row.firstName ? row.firstName : '---'),
    Footer: null,
    disableSortBy: true,
    width: 60,
    minWidth: 50,
  },
  {
    Header: 'Middle Name',
    id: 'middleName',
    accessor: (row) => (row.middleName ? row.middleName : '---'),
    Footer: null,
    disableSortBy: true,
    width: 60,
    minWidth: 50,
  },
  {
    Header: 'Last Name',
    id: 'lastName',
    accessor: (row) => (row.lastName ? row.lastName : '---'),
    Footer: null,
    disableSortBy: true,
    width: 60,
    minWidth: 50,
  },
];
