import React, { useEffect, useState } from 'react';
import { useFormik } from 'formik';
import { Button, Col, Modal, Row, Spinner, Form } from 'react-bootstrap';
import { UserInfo } from '@src/types/keycloakUser';
import { toast } from 'react-toastify';
import { AxiosError } from 'axios';
import _ from 'lodash';
import { message, validationSchema } from '../../model/TravelModalFormValidation';
import './travel-modal.scss';
import { Grid } from '../grid/grid';
import { travelModalGridColumnDefs } from './travel-modal-grid-column-defs';
import { sendTravelInformation } from '../../service/travel-service';
import { getShipPartyData } from '../../service/ship-party';
import SubtypeField from '../advanced_search/SubtypeField';
import { onHandleError } from '../../model/utils';
import { MODULES } from '../../util';

type Option = { id: string | number; value: string };

const renderLoaderOnButton = (loading: boolean) => {
  return loading ? <Spinner animation="border" /> : 'Send';
};

const TravelModal = ({
  data: seafarerData,
  show,
  onClose,
  onSave,
  ga4EventTrigger,
  user,
  calledFromModule,
}: {
  data: any;
  show: boolean;
  onClose: Function;
  onSave: Function;
  ga4EventTrigger: Function;
  user: UserInfo;
  calledFromModule: string;
}) => {
  const initialValues = {
    subject: 'Crew Details and Documents',
    sendTo: [],
    messageContent: message,
  };
  if (seafarerData.vesselName) {
    initialValues.subject += ` - ${seafarerData.vesselName}`;
  }
  const [agencies, setAgencies] = useState<Option[]>([]);
  const [loading, setLoading] = useState(false);
  const [agencyLoader, setAgencyLoader] = useState(false);

  useEffect(() => {
    (async function getAgencies() {
      let agenciesList: Option[] = [];
      try {
        setAgencyLoader(true);
        const { data } = await getShipPartyData('ship_party_type_id=23');
        if (data.error) {
          setAgencyLoader(false);
          onHandleError(data.message, setLoading);
          return;
        }
        setAgencyLoader(false);
        const agencyResponse = data.results.map((agency) => {
          return {
            id: agency.id,
            value: agency.name,
          };
        });
        agenciesList = agencyResponse;
      } catch (error) {
        setAgencyLoader(false);
      } finally {
        agenciesList.unshift({ id: 0, value: `Me (${user?.email})` });
        setAgencies(agenciesList);
        setFieldValue('sendTo', [agenciesList[0]?.id]);
      }
    })();
  }, [user?.email]);

  const prepareSeafarersData = (seafarers) => {
    const seafarerList = [];
    seafarers.forEach((seafarer) => seafarerList.push({ id: seafarer.id, hkid: seafarer.hkid }));
    return seafarerList;
  };

  const handleSendTravel = async (valueObj) => {
    try {
      setLoading(true);
      valueObj['seafarerDetails'] = prepareSeafarersData(seafarerData.seafarers);
      const { data } = await sendTravelInformation(valueObj);
      if (data.error) {
        onHandleError(data.message, setLoading);
        return;
      }
      toast.success('Email has been sent');
      onSave();
      setLoading(false);
    } catch (error) {
      const errorObj = error as AxiosError;
      if (errorObj?.response?.data) {
        const errorMessage = errorObj.response.data?.error?.message || errorObj.response?.data;
        toast.error(`Error: ${errorMessage}`);
      }
      setLoading(false);
    }
  };

  const { handleSubmit, values, errors, setFieldValue, handleBlur, handleChange, dirty } =
    useFormik({
      initialValues,
      validationSchema,
      validateOnBlur: true,
      onSubmit: (valueObj) => {
        if (loading) return;
        setLoading(true);
        console.log('onSubmit event');
        ga4EventTrigger(
          'Send Seafarer Data',
          calledFromModule === MODULES.DETAILS_PAGE
            ? 'Seafarer Details - Send Email Modal'
            : 'Vessel Crew List Menu',
          'Send',
        );
        handleSendTravel(valueObj);
      },
    });
  const onSelectAgencies = (agencies) => {
    const uniqueAgencies = _.uniq(agencies);

    if (uniqueAgencies[uniqueAgencies.length - 1] === 0) {
      setFieldValue('sendTo', uniqueAgencies.slice(-1));
      return;
    }

    const selectedAgencies =
      uniqueAgencies.length > 1 ? uniqueAgencies.filter((id) => id !== 0) : uniqueAgencies;

    setFieldValue('sendTo', selectedAgencies);
  };

  const getSelectedAgencies = () => {
    const selectedAgencies: { id: number; value: string }[] = [];
    values.sendTo.forEach((row) => {
      const currentId = parseInt(row);
      const agency = agencies.find((agency) => parseInt(agency.id) === currentId);
      selectedAgencies.unshift({ id: agency.id, value: agency.value });
    });
    return selectedAgencies;
  };

  const handleMessage = (e) => {
    setFieldValue('messageContent', e.target.value);
  };

  return (
    <Modal
      className="travel-modal"
      show={show}
      onHide={onClose}
      centered
      size="lg"
      data-testid="travel-modal"
    >
      <form onSubmit={handleSubmit} autoComplete="off">
        <Modal.Header closeButton>
          <Modal.Title>
            <div>Email Profile Data</div>
            <div>* Required Fields</div>
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Row>
            <Col sm={6}>
              <Row>
                <Col>
                  <Form.Label htmlFor="subject">Subject*</Form.Label>
                  <Form.Control
                    id="subject"
                    type="text"
                    value={values.subject}
                    onChange={handleChange}
                    onBlur={handleBlur}
                  />
                </Col>
              </Row>
              <Row className="margin-top-1rem send-to">
                <Col>
                  <Form.Label htmlFor="sendTo">Send To*</Form.Label>
                  <SubtypeField
                    type={{
                      type: 'send_to',
                      name: 'Send to',
                      inputType: 'dropdown',
                    }}
                    subtype={getSelectedAgencies()}
                    onSubtypeChange={(selectedAgencies) =>
                      onSelectAgencies(selectedAgencies.target.value)
                    }
                    dropDownData={{
                      send_to: agencies,
                    }}
                    title={false}
                    customStyle={{
                      maxWidth: 100,
                    }}
                    disabled={!agencies.length}
                  />
                </Col>
                {agencyLoader && <Spinner animation="border" />}
              </Row>
            </Col>
            <Col sm={6}>
              <Form.Label htmlFor="messageContent">Message Content*</Form.Label>
              <Form.Control
                data-testid="message-content"
                id="messageContent"
                name="messageContent"
                as="textarea"
                value={values.messageContent}
                onChange={handleMessage}
                onBlur={handleBlur}
              />
            </Col>
          </Row>
          <Grid
            columns={travelModalGridColumnDefs}
            data={seafarerData.seafarers}
            showBottomPagination={false}
            gridStyle={{
              marginTop: '2rem',
            }}
            isManualSort={false}
            showTopPagination={false}
          />
        </Modal.Body>
        <Modal.Footer>
          <Button variant="primary" onClick={() => onClose()} className="width-140">
            Cancel
          </Button>

          <Button
            variant="secondary"
            type="submit"
            className={
              Object.keys(errors).length || !dirty ? 'width-140 disabled-gray' : 'width-140'
            }
            disabled={Object.keys(errors).length || !dirty}
          >
            {renderLoaderOnButton(loading)}
          </Button>
        </Modal.Footer>
      </form>
    </Modal>
  );
};

export default TravelModal;
