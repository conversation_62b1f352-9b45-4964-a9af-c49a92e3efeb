.travel-modal {
    label {
      font-weight: bold;
    }
  
    a {
      color: #1f4a70;
      text-decoration: underline !important;
    }
  
    .modal-title {
      div:first-child {
        font-size: 20px;
      }
      div:last-child {
        font-size: 14px;
      }
    }
  
    #messageContent {
      height: calc(100% - 30px);
      font-size: 14px;
    }
  
    .margin-top-1rem {
      margin-top: 1rem;
    }
  
    .spinner-border {
      width: 1rem;
      height: 1rem;
    }
  
    .modal-footer {
      border: 1px solid #cccccc;
    }
  
    .rbt-input-multi {
      height: fit-content;
    }

    .disabled-gray {
      background-color: #DEDEDE;
      color: #666666;
      border-color: #DEDEDE;
    }

    .dropdown-menu a {
      text-decoration: none !important;
    }

    .travel-cc::placeholder {
      color: #AAAAAA;
      font-size: 13px;
    }

    .rbt-input-multi.form-control .rbt-input-wrapper div {
      width: 0px;
      display: flex !important;
    }
    .send-to {
      position: relative;
      .spinner-border {
        position: absolute;
        right: 37px;
        top: 42px;
      }
    }
  }
