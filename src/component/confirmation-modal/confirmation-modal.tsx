import React from 'react';
import { Modal, Button } from 'react-bootstrap';

interface ConfirmationModalInterface {
  show: boolean;
  confirmButtonLabel?: string;
  cancelButtonLabel?: string;
  title: string;
  message?: string;
  onConfirm: () => void;
  onClose?: () => void;
  confirmCustomStyle?: Object;
  cancelCustomStyle?: Object;
  headerCustomStyle?: Object;
  bodyCustomStyle?: Object;
  showCloseButton?: boolean;
  showCancelButton?: boolean;
  disableConfirm?: boolean;
  dialogClassName?: string;
  children?;
  size?: 'sm' | 'lg' | 'xl';
}

export const ConfirmationModal = ({
  show = false,
  confirmButtonLabel = 'Yes',
  cancelButtonLabel = 'Cancel',
  title = 'Modal title',
  message = '',
  onConfirm,
  onClose,
  confirmCustomStyle = {},
  cancelCustomStyle = {},
  headerCustomStyle = {},
  bodyCustomStyle = {},
  showCloseButton = false,
  showCancelButton = true,
  disableConfirm = false,
  children,
  dialogClassName = '',
  size,
}: ConfirmationModalInterface) => {
  return (
    <Modal
      className="modal-container"
      show={show}
      onHide={onClose}
      centered
      dialogClassName={dialogClassName}
      size={size}
    >
      {title && (
        <Modal.Header closeButton={showCloseButton}>
          <Modal.Title style={headerCustomStyle}>{title}</Modal.Title>
        </Modal.Header>
      )}

      {message && (
        <Modal.Body style={bodyCustomStyle}>
          <p>{message}</p>
        </Modal.Body>
      )}

      {children && <Modal.Body style={bodyCustomStyle}>{children}</Modal.Body>}

      <Modal.Footer>
        {showCancelButton && (
          <Button style={cancelCustomStyle} variant="primary" onClick={onClose}>
            {cancelButtonLabel}
          </Button>
        )}
        <Button
          style={confirmCustomStyle}
          variant="secondary"
          disabled={disableConfirm}
          onClick={onConfirm}
          className={disableConfirm ? 'disabled-gray' : ''}
        >
          {confirmButtonLabel}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};
