.modal-container {
  .modal-header {
    align-items: center;
    border-bottom: 0px;
    .btn-close {
      box-sizing: content-box;
      width: 1em;
      height: 1em;
      padding: 0.25em;
      color: #000;
      background: transparent
        url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000'%3E%3Cpath d='M.293.293a1 1 0 0 1 1.414 0L8 6.586 14.293.293a1 1 0 1 1 1.414 1.414L9.414 8l6.293 6.293a1 1 0 0 1-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 0 1-1.414-1.414L6.586 8 .293 1.707a1 1 0 0 1 0-1.414z'/%3E%3C/svg%3E")
        50%/1em auto no-repeat;
      border: 0;
      border-radius: 0.375rem;
      opacity: 0.5;
    }
  }
  .modal-footer {
    border-top: 0px;
  }
  .disabled-gray {
    background-color: #DEDEDE;
    color: #666666;
    border-color: #DEDEDE;
  }
}
