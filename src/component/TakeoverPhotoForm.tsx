import React, { useState, useEffect } from 'react';
import { Form, Figure, Row, Col } from 'react-bootstrap';
import _ from 'lodash';
import Lightbox from 'react-image-lightbox';
import { v4 as uuid } from 'uuid';
import 'react-image-lightbox/style.css';
import NoPhoto from '../../public/icons/no-photo.svg';
import seafarerService from '../service/seafarer-service';
import PhotoUploadModal from './photoUpload/PhotoUploadModal';

const Result = (props) => {
  const { vessel, onSubmitVesselOnly, onCustomInputChange } = props;
  const { photos = [] } = vessel;
  const sortedPhotos = photos.slice().sort((a, b) => a.order - b.order);
  const photoIds = sortedPhotos.map((p) => `${p.image_id}.${p.extension}`);
  const captions = sortedPhotos.map((p) => p.caption);

  const [photoUrlMap, setPhotoUrlMap] = useState({});
  const [lightboxIndex, setLightboxIndex] = useState(0);
  const [lightboxIsOpen, setLightboxIsOpen] = useState(false);

  useEffect(() => {
    (async () => {
      try {
        const response = await seafarerService.requestDownloadUrl(photoIds);
        if (response.data.result) {
          setPhotoUrlMap(response.data.result);
        }
      } catch (error) {}
    })();
  }, []);

  return (
    <div className="add_seafarer_page">
      <Form.Row>
        <h6>PHOTOS</h6>
      </Form.Row>
      {photos.length > 0 ? (
        <Form.Group>
          <Form.Row className="ml-0">
            <PhotoUploadModal
              label="Edit / Upload Photos"
              photos={photos}
              photoUrlMap={photoUrlMap}
              onCustomInputChange={onCustomInputChange}
              setPhotoUrlMap={setPhotoUrlMap}
              onSubmitVesselOnly={onSubmitVesselOnly}
            />
          </Form.Row>
          {_.chunk(sortedPhotos, 6).map((r, rIdx) => (
            <Row className="mt-4" key={uuid()}>
              {r.map((c, cIdx) => (
                <Col className="d-flex justify-content-center" md={2} key={uuid()}>
                  <div className="text-center">
                    <img
                      style={{ maxWidth: '100%', maxHeight: '140px', cursor: 'pointer' }}
                      src={photoUrlMap[photoIds[rIdx * 6 + cIdx]] || NoPhoto}
                      onClick={() => {
                        setLightboxIndex(rIdx * 6 + cIdx);
                        setLightboxIsOpen(true);
                      }}
                      onKeyDown={(event) => {
                        if (event.key === 'Enter' || event.key === ' ') {
                          setLightboxIndex(rIdx * 6 + cIdx);
                          setLightboxIsOpen(true);
                        }
                      }}
                      tabIndex={0}
                    />
                    <div className="p-2">{c.caption}</div>
                  </div>
                </Col>
              ))}
            </Row>
          ))}
          {lightboxIsOpen && (
            <Lightbox
              reactModalStyle={{ overlay: { zIndex: 2000 } }}
              mainSrc={photoUrlMap[photoIds[lightboxIndex]]}
              nextSrc={photoUrlMap[photoIds[(lightboxIndex + 1) % photoIds.length]]}
              prevSrc={
                photoUrlMap[photoIds[(lightboxIndex + photoIds.length - 1) % photoIds.length]]
              }
              onCloseRequest={() => {
                setLightboxIsOpen(false);
              }}
              onMovePrevRequest={() => {
                setLightboxIndex((lightboxIndex + photoIds.length - 1) % photoIds.length);
              }}
              onMoveNextRequest={() => {
                setLightboxIndex((lightboxIndex + 1) % photoIds.length);
              }}
              imageCaption={captions[lightboxIndex]}
            />
          )}
        </Form.Group>
      ) : (
        <Form.Group>
          <div className="mt-5 d-flex justify-content-center">
            <Figure>
              <Figure.Image className="d-block mx-auto" width={90} height={90} src={NoPhoto} />
              <Figure.Caption className="text-center">Nothing here right now</Figure.Caption>
            </Figure>
          </div>
          <div className="mt-2 d-flex justify-content-center">
            <PhotoUploadModal
              label="Upload Photos"
              photos={sortedPhotos}
              photoUrlMap={photoUrlMap}
              onCustomInputChange={onCustomInputChange}
              setPhotoUrlMap={setPhotoUrlMap}
              onSubmitVesselOnly={onSubmitVesselOnly}
            />
          </div>
        </Form.Group>
      )}
    </div>
  );
};

export default Result;
