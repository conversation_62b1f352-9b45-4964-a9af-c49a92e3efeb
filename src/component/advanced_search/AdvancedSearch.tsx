import React, { useState, useEffect } from 'react';
import { Container, Form, Row, Col, Button } from 'react-bootstrap';
import _, { toString } from 'lodash';
import moment from 'moment';
import SearchController from '../../controller/search-controller';
import DropDownSearchMenu from './DropDownSearchMenu';
import SubtypeField from './SubtypeField';
import AccessHandlerWrapper from '../common/AccessHandlerWrapper';
import styleGuide from '../../styleGuide';
import { resetAllTabs } from '../../util/local-storage-helper';
import { SEAFARER_PAGE } from '../../model/TabData';
import { AVAILABLE_SEAFARERS } from '../../model/constants';

const { Icon } = styleGuide;

const Result = ({
  roleConfig,
  setFilters,
  filters,
  eventTracker,
  tab,
  title = 'Advanced Search',
  page = SEAFARER_PAGE,
  dropDownDataProp,
}) => {
  const defaultDropDownValue = {
    nationalities: [],
  };

  const controller = new SearchController();
  const [dropDownData, setDropDownData] = useState(dropDownDataProp ?? defaultDropDownValue);

  const addFilter = () => {
    if (filters.length === 0 || filters[filters.length - 1].type === '') return;
    let newFilter = { type: '' };
    setFilters((oldArray) => [...oldArray, newFilter]);
    eventTracker('addFilter', `Add Filter`);
  };

  const onRemoveItem = (index) => {
    let rowId = index;
    const array = [...filters];
    if (rowId > -1) {
      array.splice(rowId, 1);
    }
    setFilters(array);
    if (array.length === 0) {
      resetAllTabs();
    }
  };

  const onFilterDateFieldChange = (value, index) => {
    let filtersState = [...filters];
    eventTracker(
      'filterSubTypeChange',
      `${toString(filtersState[index]?.type?.name)} - startDate: ${toString(
        value?.startDate ? moment(value?.startDate).format('DD MMM yyyy') : '',
      )} endDate: ${toString(value?.endDate ? moment(value?.endDate).format('DD MMM yyyy') : '')}`,
    );
    filtersState[index] = { type: filtersState[index].type, subtype: value };
    setFilters([...filtersState]);
  };

  const onFilterTypeChange = (event, index) => {
    let element = event[0];
    let filtersState = [...filters];
    if (element === undefined) {
      filtersState[index] = { type: '', subtype: '' };
      setFilters([...filtersState]);
      return;
    }
    eventTracker('filterTypeChange', element?.name);
    let type = controller.getType(element, page);
    if (element.queryIdentifier) {
      type.queryIdentifier = element.queryIdentifier;
    }
    if (index === null || index === undefined) {
      let newFilter = { type, subtype: '' };
      setFilters([newFilter]);
      return;
    }
    if (type) {
      filtersState[index] = { type, subtype: '' };
      setFilters([...filtersState]);
    }
  };

  const onFilterSubtypeChange = (value, index, customValue, inputTypeIndex = null) => {
    let filtersState = [...filters];
    let subtype;
    if (inputTypeIndex !== null) {
      subtype = Object.fromEntries(
        filtersState[index].type.inputType.map((e, i) => {
          const typeOfSubfield = filtersState[index].type.type[i];
          if (inputTypeIndex === i) {
            return [[typeOfSubfield], value];
          } else if (e === 'dropdown') {
            const val =
              filtersState[index].subtype[typeOfSubfield] ??
              dropDownData[filtersState[index].type.type[i]].filter((e) => e.id === 1);
            return [[typeOfSubfield], val];
          } else {
            const val = filtersState[index].subtype[typeOfSubfield] ?? '';
            return [[typeOfSubfield], val];
          }
        }),
      );
    } else {
      subtype = value;
    }
    if (!value && filtersState[index].type.inputType !== 'number_range') {
      if (inputTypeIndex !== null) {
        subtype = Object.fromEntries(
          Object.keys(subtype).map((e, i) => {
            if (inputTypeIndex === i) {
              return [e, ''];
            }
            return [e, subtype[e]];
          }),
        );
        filtersState[index] = { type: filtersState[index].type, subtype };
      } else {
        filtersState[index] = { type: filtersState[index].type, subtype: '' };
      }
      setFilters([...filtersState]);
      return;
    }
    let inputType =
      inputTypeIndex !== null
        ? filtersState[index].type.inputType[inputTypeIndex]
        : filtersState[index].type.inputType;

    if (inputType === 'dropdown') {
      const data = inputTypeIndex
        ? dropDownData[filtersState[index].type.type[inputTypeIndex]]
        : dropDownData[filtersState[index].type.type];
      if (inputTypeIndex) {
        const type = filtersState[index].type.type[inputTypeIndex];
        subtype[type] = value.map((item) => findSubtype(data, item));
        subtype[type] =
          subtype[type]?.[subtype[type].length - 1]?.id === 0
            ? subtype[type].slice(-1)
            : subtype[type]; // if All is selected then deselect remaining values
        subtype[type] =
          subtype[type].length > 1 ? subtype[type].filter((i) => i.id !== 0) : subtype[type]; // to deselect All
        subtype[type] = filtersState[index].type?.notMultiple
          ? subtype[type].slice(-1)
          : subtype[type];
      } else {
        subtype = value.map((item) => findSubtype(data, item));
        subtype = subtype?.[subtype.length - 1]?.id === 0 ? subtype.slice(-1) : subtype; // if All is selected then deselect remaining values
        subtype = subtype.length > 1 ? subtype.filter((i) => i.id !== 0) : subtype; // to deselect All
        subtype = filtersState[index].type?.notMultiple ? subtype.slice(-1) : subtype;
      }
    }

    if (inputType === 'number_range') {
      subtype = customValue;
    }

    if (inputType === 'checkbox') {
      subtype = customValue;
    }

    if (filtersState[index]?.type?.name === 'Years of long service') {
      eventTracker(
        'yearsOfLongService',
        `${_.get(subtype, 'years_of_long_service', '')} years - ${_.get(
          subtype,
          'years_of_long_service_type[0].value',
          '',
        )}`,
      );
    } else {
      eventTracker(
        'filterSubTypeChange',
        `${toString(filtersState[index]?.type?.name)} - ${subtype}`,
      );
    }

    filtersState[index] = { type: filtersState[index].type, subtype };
    setFilters([...filtersState]);
  };

  function findSubtype(array, id) {
    const found = array.find(function (element) {
      if (parseInt(id)) return element.id === parseInt(id);
      else return element.id.toString() === id.toString();
    });
    return found;
  }

  const nonRemovableFilter = (e) => {
    const filterName = e.name;
    if (tab === 'modeller' && filterName === 'Accounting Currency') return false;

    return true;
  };

  const FilterRow = (props) => {
    const customStyle = { span: 2 };
    let currentArray = [...props.filters].map((filter, index) => {
      filter.index = index;
      return filter;
    });
    let newArray = [];
    const columnLength = 2;
    while (currentArray.length) newArray.push(currentArray.splice(0, columnLength));
    return newArray.map((filterRow, index) => {
      const rowKey = `search_filter_${index}`;
      return (
        <Form.Row
          id={rowKey}
          className="advanced_search__filter-row-borderless"
          key={rowKey}
        >
          {filterRow.map((filter) => {
            return (
              <React.Fragment key={filter.type.type}>
                <Form.Group as={Col} md={customStyle.span}>
                  <DropDownSearchMenu
                    defaultSelected={[filter.type]}
                    placeholder="Select Category"
                    onChange={(e) => onFilterTypeChange(e, filter.index)}
                    tab={tab}
                    page={page}
                  />
                </Form.Group>

                {filter.type && (
                  <>
                    {typeof filter.type.inputType === 'object' ? (
                      <Col sm={2} className="mx-0 px-0">
                        <div className="d-flex justify-content-between">
                          {filter.type.inputType.map((e, i) => {
                            return SubtypeField({
                              type: filter.type,
                              subtype: filter.subtype[filter.type.type[i]],
                              disabled: false,
                              onSubtypeChange: (e, customValue) =>
                                onFilterSubtypeChange(e.target.value, filter.index, customValue, i),
                              onDateFieldChange: (value) =>
                                onFilterDateFieldChange(value, filter.index),
                              dropDownData,
                              title: false,
                              onYearFieldChange: (value) =>
                                onFilterDateFieldChange(value, filter.index),
                              min: filter.subtype?.min ?? '',
                              max: filter.subtype?.max ?? '',
                              customStyle: { span: i ? 7 : 4 },
                              index: i,
                              key: i,
                            });
                          })}
                        </div>
                      </Col>
                    ) : (
                      SubtypeField({
                        type: filter.type,
                        subtype: filter.subtype,
                        disabled: false,
                        onSubtypeChange: (e, customValue) =>
                          onFilterSubtypeChange(e.target.value, filter.index, customValue),
                        onDateFieldChange: (value) => onFilterDateFieldChange(value, filter.index),
                        dropDownData,
                        title: false,
                        onYearFieldChange: (value) => onFilterDateFieldChange(value, filter.index),
                        min: filter.subtype?.min ?? '',
                        max: filter.subtype?.max ?? '',
                        customStyle,
                        indexValue: filter.index,
                      })
                    )}
                    <Col sm={1}>
                      {nonRemovableFilter(filter.type) && (
                        <Form.Group className="text-center">
                          <Icon
                            icon="remove"
                            size={30}
                            className="remove"
                            onClick={onRemoveItem.bind(this, filter.index)}
                          />
                        </Form.Group>
                      )}
                    </Col>
                  </>
                )}
              </React.Fragment>
            );
          })}
        </Form.Row>
      );
    });
  };

  useEffect(() => {
    (async () => {
      try {
        //Don't reload dropdown data if already coming form props
        if (dropDownDataProp) {
          setDropDownData(dropDownDataProp);
        } else {
          const { dropDownData: dropDownDataFromSearchController } = await controller.onLoadPage(page);
          setDropDownData(dropDownDataFromSearchController);
        }
      } catch (error) {
        console.error(error);
      }
    })();
  }, [dropDownDataProp]);

  return (
    <AccessHandlerWrapper hasRoleAccess={roleConfig.seafarer.view.general}>
      <div className="advanced_search">
        <Container>
          <Form>
            <Form.Row>
              <Form.Group as={Col} md="6" className="m-0">
                <Form.Label className="advanced_search_header">{title}</Form.Label>
              </Form.Group>
            </Form.Row>

            {FilterRow({ filters })}

            {filters.length === 0 && (
              <Form.Row>
                <Form.Group as={Col} md="2">
                  <DropDownSearchMenu
                    onChange={(e) => onFilterTypeChange(e)}
                    placeholder="Select Category"
                    tab={tab}
                    page={page}
                  />
                </Form.Group>
              </Form.Row>
            )}

            <Button
              variant="outline-primary"
              className="mb-3 add_btn"
              size="sm"
              onClick={addFilter}
            >
              Add
            </Button>
          </Form>
        </Container>
      </div>
    </AccessHandlerWrapper>
  );
};

export default Result;
