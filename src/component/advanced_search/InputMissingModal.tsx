import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON> } from 'react-bootstrap';

const InputMissingModal = (props) => {
  const handleClose = () => props.setShow(false);
  return (
    <Modal className="action-modal" show={props.show} onHide={handleClose} centered>
      <Modal.Header>
        <Modal.Title className="h5">
          Please click on the ‘Add’ button below the category dropdown field.
        </Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <span>
          You haven’t selected any search criteria yet. Please click on the ‘Add’ button below the
          category dropdown field to include your search criteria.
        </span>
      </Modal.Body>
      <Modal.Footer>
        <Button
          style={{
            width: '88px',
            height: '38px',
          }}
          variant="secondary"
          onClick={handleClose}
        >
          OK
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default InputMissingModal;
