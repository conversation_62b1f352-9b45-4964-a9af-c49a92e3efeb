import React from 'react';
import { Form, Row, Col, Button, ButtonGroup } from 'react-bootstrap';
import moment from 'moment';
import DatePicker from 'react-datepicker';
import DropDownSearchControl from '../AddSeafarer/DropDownSearchControl';
import TypeAbleDropDownSearchControl from './TypeAbleDropDownSearchControl';

const SubtypeField = (props) => {
  let index = props.index ?? null;
  let type =
    index !== null && index !== undefined
      ? props.type.inputType[index]
      : props.type.inputType ?? 'type';
  let defaultField = (
    <SubtypeFieldText
      {...props}
      isFilterRow={true}
      subtype={''}
      disabled={props.disabled !== null ? props.disabled : true}
      title={props.title !== null ? props.title : true}
    />
  );

  if (type === 'dropdown') {
    return <SubtypeFieldDropDown {...props} />;
  }

  if (type === 'text') {
    return <SubtypeFieldText {...props} />;
  }

  if (type === 'date') {
    return <SubtypeFieldDate {...props} />;
  }

  if (type === 'date_single') {
    return <SubtypeFieldDateSingle {...props} />;
  }

  if (type === 'year') {
    return <SubtypeFieldYear {...props} />;
  }

  if (type === 'number_range') {
    return <SubtypeFieldNumberRange {...props} />;
  }

  if (type === 'number') {
    return <SubtypeFieldNumber {...props} />;
  }

  if (type === 'checkbox') {
    return <SubtypeFieldCheckBox {...props} />;
  }

  if (type === 'switch') {
    return <SubtypeFieldSwitch {...props} />;
  }

  return defaultField;
};

const SubtypeFieldText = (props) => {
  return (
    <Form.Group as={Col} md={props.customStyle} className="subtype-field">
      {props.title ? <Form.Label>2. Sub-Category</Form.Label> : null}
      <Form.Control
        className={props.isFilterRow ? 'advanced_search__input-disabled' : null}
        disabled={props.disabled}
        onChange={props.onSubtypeChange}
        value={props.subtype}
        data-testid={`fml-seafarer-general-details-${props?.indexValue}`}
      />
    </Form.Group>
  );
};

const SubtypeFieldCheckBox = (props) => {
  const index = props.index ?? null;
  const type =
    index !== null && index !== undefined ? props.type.type[index] : props.type.type ?? 'type';
  let data = props.dropDownData[type] ?? [];
  let options = props.subtype ? [...props.subtype] : [];

  const handleChange = (event) => {
    const index = options.findIndex((option) => option.name === event.target.name);
    if (index === -1) {
      options = [...options, { name: event.target.name, checked: event.target.checked }];
    } else {
      options[index].checked = event.target.checked;
    }
    props.onSubtypeChange(event, [...options]);
  };

  return (
    <Form.Group
      as={Col}
      md={props.customStyle}
      className="subtype-field d-flex justify-content-between"
    >
      {data.map((item) => {
        return (
          <Form.Check
            type="checkbox"
            name={item.name}
            className="checkbox-field"
            onChange={handleChange}
            checked={options.some((subtype) => subtype.name === item.name && subtype.checked)}
            label={
              <>
                <span className={`${item.color} dot`} />
                <span>{item.label}</span>
              </>
            }
            id={item.name}
            key={item.name}
          />
        );
      })}
    </Form.Group>
  );
};

const SubtypeFieldNumber = (props) => {
  let limit = 12;
  const index = props.index ?? null;
  const type =
    index !== null && index !== undefined ? props.type.type[index] : props.type.type ?? 'type';
  if (type === 'imo_number') {
    limit = 7;
  }
  return (
    <Form.Group as={Col} md={props.customStyle} className="subtype-field">
      {props.title ? <Form.Label>2. Sub-Category</Form.Label> : null}
      <Form.Control
        className={props.isFilterRow ? 'advanced_search__input-disabled' : null}
        type="number"
        onInput={(e) => {
          e.target.value = Math.max(0, parseInt(e.target.value)).toString().slice(0, limit);
        }}
        disabled={props.disabled}
        onChange={props.onSubtypeChange}
        value={props.subtype}
      />
    </Form.Group>
  );
};

const SubtypeFieldNumberRange = (props) => {
  let labelClass = props.disabled ? 'label-to-in-row' : 'label-to-on-top';

  let minValue = props.min;
  let maxValue = props.max;

  if (props.isFilterRow) {
    minValue = props.subtype.min;
    maxValue = props.subtype.max;
  }

  const onMinNumberChange = (event) => {
    let value = event.target.value;
    props.onSubtypeChange(event, { min: value, max: maxValue });
  };

  const onMaxNumberChange = (event) => {
    let value = event.target.value;
    props.onSubtypeChange(event, { min: minValue, max: value });
  };

  const limit = 12;

  return (
    <Form.Group as={Col} md={props.customStyle} className="subtype-field">
      <Row className="m-auto">
        <Form.Group as={Col}>
          {props.title ? <Form.Label>2. Sub-Category</Form.Label> : null}
          <Form.Control
            className={props.isFilterRow ? 'advanced_search__input-disabled' : null}
            type="number"
            onInput={(e) => {
              e.target.value = Math.max(0, parseInt(e.target.value)).toString().slice(0, limit);
            }}
            disabled={props.disabled}
            onChange={onMinNumberChange}
            value={minValue}
          />
        </Form.Group>{' '}
        <div className={labelClass}>to</div>
        <Form.Group as={Col}>
          {props.title ? <Form.Label className="hidden-label">.</Form.Label> : null}
          <Form.Control
            className={props.isFilterRow ? 'advanced_search__input-disabled' : null}
            type="number"
            onInput={(e) => {
              e.target.value = Math.max(0, parseInt(e.target.value)).toString().slice(0, limit);
            }}
            disabled={props.disabled}
            onChange={onMaxNumberChange}
            value={maxValue}
          />
        </Form.Group>
      </Row>
    </Form.Group>
  );
};

const SubtypeFieldYear = (props) => {
  const date = getDate(props.subtype);

  return (
    <Form.Group as={Col} md={props.customStyle} className="subtype-field">
      {props.title ? <Form.Label>2. Sub-Category</Form.Label> : null}
      <DatePicker
        className={props.isFilterRow ? 'advanced_search__input-disabled' : null}
        disabled={props.disabled}
        selected={date}
        onChange={props.onYearFieldChange}
        showYearPicker
        placeholderText="Please select"
        dateFormat="yyyy"
      />
    </Form.Group>
  );
};

function getDate(value) {
  if ((typeof value === 'string' || value instanceof String) && value != '') {
    return moment(value).toDate();
  } else {
    return value;
  }
}

const SubtypeFieldDate = (props) => {
  const { subtype = {} } = props;
  const { startDate = null, endDate = null } = subtype;

  const onDatesChange = (type, date) => {
    let value = { startDate: startDate, endDate: endDate };

    if (type === 'startDate') {
      value = { startDate: date, endDate: endDate };
    }

    if (type === 'endDate') {
      value = { startDate: startDate, endDate: date };
    }

    props.onDateFieldChange(value);
  };

  return (
    <Form.Group as={Col} md={props.customStyle} className="p-0">
      {props.title ? <Form.Label>2. Sub-Category</Form.Label> : null}
      <Row className="m-auto">
        <span className="align-self-center pr-1">From</span>
        <Form.Group as={Col} className="pl-0 mb-0">
          <DatePicker
            selectsStart
            className={props.isFilterRow ? 'advanced_search__input-disabled' : null}
            disabled={props.disabled}
            selected={startDate}
            startDate={startDate}
            endDate={endDate}
            showYearDropdown
            onChange={onDatesChange.bind(this, 'startDate')}
            dateFormat="d MMM yyyy"
          />
        </Form.Group>{' '}
        <span className="align-self-center pr-1">To</span>
        <Form.Group as={Col} className="px-0 mb-0">
          <DatePicker
            selectsEnd
            disabled={props.disabled}
            className={props.isFilterRow ? 'advanced_search__input-disabled' : null}
            selected={endDate}
            onChange={onDatesChange.bind(this, 'endDate')}
            startDate={startDate}
            endDate={endDate ?? ''}
            minDate={startDate}
            showYearDropdown
            dateFormat="d MMM yyyy"
          />
        </Form.Group>
      </Row>
    </Form.Group>
  );
};

const SubtypeFieldDropDown = (props) => {
  const index = props.index ?? null;
  const type =
    index !== null && index !== undefined ? props.type.type[index] : props.type.type ?? 'type';
  let data = props.dropDownData[type] ?? [];
  let selectedValue = [];

  if (!!props.subtype && props.subtype.length)
    selectedValue = props.subtype.map((subtype) => subtype.id);

  return (
    <Form.Group as={Col} md={props.customStyle} className="subtype-field">
      {props.title ? <Form.Label>2. Sub-Category</Form.Label> : null}
      {props.type?.isSearchable ? (
        <TypeAbleDropDownSearchControl
          name={type}
          selectedValue={selectedValue}
          dropDownValues={data}
          onInputChange={props.onSubtypeChange}
          disabled={props.disabled}
          multiple={true}
        />
      ) : (
        <DropDownSearchControl
          name={type}
          selectedValue={selectedValue}
          dropDownValues={data}
          onInputChange={props.onSubtypeChange}
          disabled={props.disabled}
          multiple={true}
        />
      )}
    </Form.Group>
  );
};

const SubtypeFieldSwitch = (props) => {
  const index = props.index ?? null;
  const type =
    index !== null && index !== undefined ? props.type.type[index] : props.type.type ?? 'type';
  let data = props.dropDownData[type] ?? [];
  return (
    <Form.Group as={Col} md={props.customStyle} className="subtype-field">
      <ButtonGroup>
        {data.map((d) => {
          return (
            <Button
              key={d?.id}
              className="switch-advance-search"
              value={d?.id}
              onClick={props.onSubtypeChange}
              variant={d?.id === props.subtype ? 'primary' : 'outline-primary'}
            >
              {d?.value}
            </Button>
          );
        })}
      </ButtonGroup>
    </Form.Group>
  );
};
const SubtypeFieldDateSingle = (props) => {
  const date = getDate(props.subtype);
  return (
    <Form.Group as={Col} md={props.customStyle} className="subtype-field">
      {props.title ? <Form.Label>2. Sub-Category</Form.Label> : null}
      <DatePicker
        selectsStart
        className={props.isFilterRow ? 'advanced_search__input-disabled' : null}
        disabled={props.disabled}
        selected={date}
        showYearDropdown
        onChange={props.onDateFieldChange}
        dateFormat="d MMM yyyy"
      />
    </Form.Group>
  );
};

export default SubtypeField;
