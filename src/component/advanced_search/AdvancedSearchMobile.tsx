import React, { useState, useEffect } from 'react';
import { Container, Form, Row, Col, Button } from 'react-bootstrap';
import SearchController from '../../controller/search-controller';
import DropDownSearchMenu from './DropDownSearchMenu';
import ClearAllModalView from './ClearAllModalView';
import SubtypeField from './SubtypeField';
import InputMissingModal from './InputMissingModal';
import AccessHandlerWrapper from '../common/AccessHandlerWrapper';

import styleGuide from '../../styleGuide';
import { resetAllTabs } from '../../util/local-storage-helper';
import Spinner from '../common/Spinner';
import { SEAFARER_PAGE } from '../../model/TabData';
const { Icon } = styleGuide;

const Result = ({
  roleConfig,
  setShowAdvSearchMobile,
  filters,
  setFilters,
  tab,
  page = SEAFARER_PAGE,
}) => {
  const defaultDropDownValue = {
    nationalities: [],
  };
  
  const controller = new SearchController();
  const [loading, setLoading] = useState(true);
  const [dropDownData, setDropDownData] = useState(defaultDropDownValue);
  const [modalShow, setModalShow] = React.useState(false);
  const [inputMissingModalShow, setInputMissingModalShow] = useState(false);

  const addFilter = () => {
    if (filters.length === 0 || filters[filters.length - 1].type === '') return;
    let newFilter = { type: '' };
    setFilters((oldArray) => [...oldArray, newFilter]);
  };

  const handleClearFilters = () => {
    setFilters([]);
    clearFields();
    setModalShow(false);
  };

  const removeEmptyFilters = (filter) => {
    return filter.filter(
      (item) => item.subtype !== null && item.subtype !== undefined && item.subtype !== '',
    );
  };

  const handleSearchResult = () => {
    if (filters.length < 1) {
      setInputMissingModalShow(true);
    } else {
      resetAllTabs();
      setShowAdvSearchMobile(false);
    }
  };

  const handleClose = () => {
    // Reset page numbers on all tabs
    resetAllTabs();
    setShowAdvSearchMobile(false);
  };

  const onRemoveItem = (index) => {
    let rowId = index;
    const array = [...filters];
    if (rowId > -1) {
      array.splice(rowId, 1);
    }
    setFilters(array);
  };

  const onFilterDateFieldChange = (value, index) => {
    let filtersState = [...filters];
    filtersState[index] = { type: filtersState[index].type, subtype: value };
    setFilters([...filtersState]);
  };

  const onFilterTypeChange = (event, index) => {
    let element = event[0];
    let filtersState = [...filters];
    if (element === undefined) {
      filtersState[index] = { type: '', subtype: '' };
      setFilters([...filtersState]);
      return;
    }
    let type = controller.getType(element, page);
    if (index === null || index === undefined) {
      let newFilter = { type, subtype: '' };
      setFilters([newFilter]);
      return;
    }
    if (type) {
      filtersState[index] = { type, subtype: '' };
      setFilters([...filtersState]);
    }
  };

  const onFilterSubtypeChange = (value, index, customValue) => {
    let filtersState = [...filters];
    let subtype = value;
    if (!value && filtersState[index].type.inputType !== 'number_range') {
      filtersState[index] = { type: filtersState[index].type, subtype: '' };
      setFilters([...filtersState]);
      return;
    }

    if (filtersState[index].type.inputType === 'dropdown') {
      subtype = value.map((item) => findSubtype(dropDownData[filtersState[index].type.type], item));
      subtype = subtype?.[subtype.length - 1]?.id === 0 ? subtype.slice(-1) : subtype; // if All is selected then deselect remaining values
      subtype = subtype.length > 1 ? subtype.filter((i) => i.id !== 0) : subtype; // to deselect All
      subtype = filtersState[index].type?.notMultiple ? subtype.slice(-1) : subtype;
    }

    if (filtersState[index].type.inputType === 'number_range') {
      subtype = customValue;
    }

    if (filtersState[index].type.inputType === 'checkbox') {
      subtype = customValue;
    }

    filtersState[index] = { type: filtersState[index].type, subtype };
    setFilters([...filtersState]);
  };

  function findSubtype(array, id) {
    const found = array.find(function (element) {
      if (parseInt(id)) return element.id === parseInt(id);
      else return element.id.toString() === id.toString();
    });
    return found;
  }
  const nonRemovableFilter = (e) => {
    const filterName = e.name;
    if (tab === 'available-seafarers' && filterName === 'Available Date') return false;

    return true;
  };

  const FilterRow = (props) => {
    return props.filters.map((filter, index) => (
      <Form.Row key={filter.type.type} className="advanced_search__filter-row">
        <Form.Group as={Col} xs="12" md="5">
          <DropDownSearchMenu
            defaultSelected={[filter.type]}
            placeholder="Select Category"
            onChange={(e) => onFilterTypeChange(e, index)}
            tab={tab}
            page={page}
          />
        </Form.Group>

        {filter.type && (
          <>
            <Form.Group as={Col} xs={10} md={5}>
              {SubtypeField({
                type: filter.type,
                subtype: filter.subtype,
                disabled: false,
                onSubtypeChange: (e, customValue) =>
                  onFilterSubtypeChange(e.target.value, index, customValue),
                onDateFieldChange: (value) => onFilterDateFieldChange(value, index),
                dropDownData: dropDownData,
                title: false,
                onYearFieldChange: (value) => onFilterDateFieldChange(value, index),
                min: filter.subtype?.min ?? '',
                max: filter.subtype?.max ?? '',
              })}
            </Form.Group>
            {nonRemovableFilter(filter.type) && (
              <Form.Group as={Col} xs={2} md={1}>
                <Icon
                  icon="remove"
                  size={30}
                  className="remove"
                  onClick={onRemoveItem.bind(this, index)}
                />
              </Form.Group>
            )}
          </>
        )}
      </Form.Row>
    ));
  };

  useEffect(() => {
    (async () => {
      setLoading(true);
      try {
        const { dropDownData } = await controller.onLoadPage(page);
        setDropDownData(dropDownData);
      } catch (error) {
        setError(error.message);
        console.error(error);
      }
      setLoading(false);
    })();
  }, []);

  return (
    <AccessHandlerWrapper hasRoleAccess={roleConfig.seafarer.view.general}>
      {loading ? (
        <Spinner alignClass={`load-spinner`} />
      ) : (
        <div className="advanced_search">
          <Container>
            <Row>
              <Col>
                <h5 className="advanced_search__title">
                  Advanced Search{' '}
                  {removeEmptyFilters(filters).length
                    ? `(${removeEmptyFilters(filters).length})`
                    : ''}
                  <Icon icon="close" size={30} onClick={handleClose} />
                </h5>
                <hr className="line1px" />
              </Col>
            </Row>

            <Form>
              <Form.Row>
                <Form.Label className="category">
                  <b>Category</b>
                </Form.Label>
              </Form.Row>

              {FilterRow({ filters: filters })}

              {filters.length === 0 && (
                <Form.Row>
                  <Form.Group as={Col} md="5">
                    <DropDownSearchMenu
                      onChange={(e) => onFilterTypeChange(e)}
                      placeholder="Select Category"
                      tab={tab}
                      page={page}
                    />
                  </Form.Group>
                </Form.Row>
              )}

              <Button
                variant="outline-primary"
                className="w-100 advanced_search__add-button"
                size="sm"
                onClick={addFilter}
              >
                Add
              </Button>

              <Button
                variant="primary"
                className="w-100 advanced_search__show-results-button"
                size="sm"
                onClick={handleSearchResult}
              >
                Show Results
              </Button>
            </Form>
            <InputMissingModal show={inputMissingModalShow} setShow={setInputMissingModalShow} />
          </Container>

          <ClearAllModalView
            show={modalShow}
            onClose={() => setModalShow(false)}
            onConfirm={handleClearFilters}
          />
        </div>
      )}
    </AccessHandlerWrapper>
  );
};

export default Result;
