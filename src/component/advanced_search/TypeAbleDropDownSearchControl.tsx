import React, { Fragment, useRef } from 'react';
// @ts-ignore
import { <PERSON><PERSON><PERSON>, <PERSON>u, MenuItem, Typeahead } from 'react-bootstrap-typeahead';
import _ from 'lodash';
import { Button, OverlayTrigger, Tooltip } from 'react-bootstrap';
import { ChevronDown } from 'react-bootstrap-icons';
import { Icon } from '../../styleGuide';
// eslint-disable-next-line no-unused-vars
import { TypeableDropDownProps } from '../../types/typeAbleDropDown';

const OBJECT_ID_VALUE_DROPDOWN_VALUE_TYPE = 'OBJECT_ID_VALUE_DROPDOWN_VALUE_TYPE';
const PRIMITIVE_DROPDOWN_VALUE_TYPE = 'PRIMITIVE_DROPDOWN_VALUE_TYPE';

const TypeAbleDropDownSearchControl = ({
  dropDownValues = [],
  name,
  onInputChange,
  selectedValue,
  disabled,
  isInvalid,
  testID,
  multiple = false,
  showAllOptionsAfterSelection = false,
  ...props
}: TypeableDropDownProps) => {
  let dropDownValueType;
  const typeaheadRef = useRef(null);
  if (dropDownValues?.length) {
    const dropDownValue = dropDownValues[0];
    if (_.isObject(dropDownValue) && dropDownValue.constructor === Object) {
      // plain object
      if (_.has(dropDownValue, 'id') && _.has(dropDownValue, 'value')) {
        dropDownValueType = OBJECT_ID_VALUE_DROPDOWN_VALUE_TYPE;
      }
    } else {
      if (!(_.isNumber(dropDownValue) || _.isString(dropDownValue))) {
        throw Error('Only supporting primitve dropdown values of type string and number');
      }
      if (multiple) {
        throw Error('Not supporting primitve dropdown values for multiple select for now');
      }
    }
  } else {
    dropDownValueType = PRIMITIVE_DROPDOWN_VALUE_TYPE;
  }

  let options: string[] | any[] = [];
  let onChange: undefined | ((value: string[]) => void);
  let selected: undefined | any[];

  if (dropDownValueType === OBJECT_ID_VALUE_DROPDOWN_VALUE_TYPE) {
    options = dropDownValues.map((item) => {
      return item.value;
    });

    onChange = (value) => {
      if (multiple) {
        let ids = value.map((item) => getIdOfSelectedOption(item));
        const isExists = selectedValue.find((val: number) => val === ids[ids.length - 1]);
        if (isExists) {
          // Remove unselected value from dropdown
          ids = ids.filter((id) => id !== isExists);
        }
        const results = { target: { name, value: ids } };
        onInputChange(results);
      } else {
        const id = getIdOfSelectedOption(value[0]);
        let result = { target: { name, value: id } };
        if (showAllOptionsAfterSelection && id === selectedValue) {
          result = { target: { name, value: null } };
        }
        onInputChange(result);
      }
    };

    const getIdOfSelectedOption = (value: string) => {
      const result = dropDownValues.filter((item) => {
        return item.value === value;
      });
      return result.length > 0 ? result[0].id : undefined;
    };

    const getOptionById = (id: number) => {
      const result = dropDownValues.filter((item) => {
        return _.isObject(item.id) ? _.isEqual(item.id, id) : item.id === id;
      });
      return result.length > 0 ? result[0].value : undefined;
    };

    if (multiple) {
      selected = selectedValue.map((item: number) => getOptionById(item));
    } else {
      const selectedOption = getOptionById(selectedValue);
      selected = selectedOption ? [selectedOption] : [];
    }
  } else if (dropDownValueType === PRIMITIVE_DROPDOWN_VALUE_TYPE) {
    options = dropDownValues;

    onChange = (value) => {
      const result = { target: { name, value: value[0] } };
      onInputChange(result);
    };

    selected = selectedValue ? [selectedValue] : [];
  }

  const validateArgument = () => {
    if (dropDownValues?.length && options === undefined) {
      throw Error('options cannot be undefined');
    }

    if (onChange === undefined) {
      throw Error('onChange cannot be undefined');
    }

    if (selectedValue !== undefined && selected === undefined) {
      throw Error('selected cannot be undefined');
    }
  };

  validateArgument();

  const isMatch = (input: string, option: string) => {
    return option.toLowerCase().indexOf(input.toLowerCase()) !== -1;
  };
  /**
   * Custom Dropdown Input/Menu design
   */
  const customDropdownDesignProps: any = {};

  const onClickHandler = () => {
    // @ts-ignore
    if (showAllOptionsAfterSelection && typeaheadRef.current) {
      typeaheadRef.current.setState({ text: '' });
      if (!typeaheadRef.current?.state?.showMenu) {
        typeaheadRef.current.toggleMenu();
      }
    }
  };

  // Custom Dropdown Input design
  customDropdownDesignProps.renderInput = (
    { inputRef, referenceElementRef, ...inputProps }: any,
    state: { text: string; selected: string[] },
  ) => {
    const text = [...state.selected]
      .filter((e) => {
        if (state.text) {
          const regex = /^All\s+.*$/;
          return !regex.exec(e);
        }
        return true;
      })
      .reverse()
      .map((i) => i?.toString())
      .filter(Boolean)
      .join(', ');
    const addSpace = '\xa0'.repeat(state.text.length * 2 + 1); // '\xa0' represents &nbsp; (non-breaking space)
    delete inputProps.inputClassName;
    return (
      <div className="rbt-input-multi form-control">
        <div className="rbt-input-wrapper pr-2">
          <input
            {...inputProps}
            ref={(node) => {
              inputRef(node);
              referenceElementRef(node);
            }}
            value={`${state.text}`}
            className="rbt-input-main custom-input"
            onClick={onClickHandler}
          />
          {multiple && (
            <div className="spantoken">
              {addSpace}
              {text}
            </div>
          )}
        </div>
        <Button
          variant="link"
          className="typeable-dropdown-icon-style"
          onClick={() => {
            onClickHandler();
            if (typeaheadRef) typeaheadRef?.current?.focus();
          }}
        >
          <ChevronDown size={20} />
        </Button>
      </div>
    );
  };
  // Custom Dropdown menu design
  customDropdownDesignProps.renderMenu = (
    results: any[],
    menuProps: any,
    state: { text: string; selected: string[] },
  ) => {
    let filteredResults = results;
    if (state.text !== '') {
      filteredResults = options.filter((option) => {
        if (_.isString(option)) return isMatch(state.text, option);
        return true;
      });
    }
    const items = _.uniq([...state.selected, ...filteredResults])
      .filter((i) => !_.isUndefined(i) && !_.isNull(i))
      .map((option, index: number) => {
        const menuItemProps = {
          option,
        };
        if (option?.paginationOption) {
          return (
            <Fragment key="pagination-item">
              <Menu.Divider />
              <MenuItem
                {...menuItemProps}
                className="rbt-menu-pagination-option"
                label="Display additional results..."
              >
                Display additional results...
              </MenuItem>
            </Fragment>
          );
        }
        return (
          <OverlayTrigger
            key={option?.toString()}
            placement="bottom"
            overlay={<Tooltip id={`tooltip-${option?.toString()}`}>{option?.toString()}</Tooltip>}
          >
            <MenuItem {...menuItemProps} checked position={index}>
              <>
                {state.selected.includes(option) ? (
                  <Icon
                    icon="checked"
                    size={20}
                    className="float-left screening-page__moved-tick check-icon"
                  />
                ) : (
                  <span className="empty-icon-padding" />
                )}
                <Highlighter search={state.text}>{option?.toString() ?? ''}</Highlighter>
              </>
            </MenuItem>
          </OverlayTrigger>
        );
      });
    return (
      <Menu id={menuProps.id} style={{ width: '100%' }}>
        {items}
      </Menu>
    );
  };
  if (multiple) {
    customDropdownDesignProps.className = 'multi-dropdown';
  }
  // Show all the options including selected in dropdown menu
  customDropdownDesignProps.filterBy = () => {
    return true;
  };
  const parseSelection = (selected) => {
    return selected
      ?.flat()
      .map((item: any) => {
        if (typeof item === 'number') {
          return null;
        }
        return item;
      })
      .filter(Boolean);
  };
  return (
    <Typeahead
      id={name}
      key={name}
      ref={typeaheadRef}
      inputProps={{ 'data-testid': testID }}
      onChange={onChange}
      options={options}
      placeholder={selected?.length ? '' : props?.placeholder ?? 'Please select'}
      selected={parseSelection(selected)}
      disabled={disabled}
      isInvalid={isInvalid}
      multiple={multiple}
      {...customDropdownDesignProps}
    />
  );
};

export default TypeAbleDropDownSearchControl;
