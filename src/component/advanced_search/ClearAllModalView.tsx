import React from 'react';
import Modal from 'react-bootstrap/Modal';
import Button from 'react-bootstrap/Button';

function ClearAllModalView(props) {
  return (
    <Modal
      {...props}
      onHide={props.onClose}
      aria-labelledby="contained-modal-title-vcenter"
      centered
    >
      <Modal.Header closeButton>
        <Modal.Title id="contained-modal-title-vcenter">Confirm Clearing All?</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <p>Are you sure to clear all search criteria?</p>
      </Modal.Body>
      <Modal.Footer>
        <Button onClick={props.onClose}>Cancel</Button>
        <Button variant="secondary" onClick={props.onConfirm}>
          Confirm
        </Button>
      </Modal.Footer>
    </Modal>
  );
}

export default ClearAllModalView;
