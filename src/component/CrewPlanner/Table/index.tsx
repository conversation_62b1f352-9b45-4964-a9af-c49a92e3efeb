import React, { useMemo, useEffect } from 'react';
import { Row } from 'react-bootstrap';
import { useTable, useSortBy, usePagination, useFlexLayout } from 'react-table';
import { useSticky } from 'react-table-sticky';
import Spinner from '@src/component/common/Spinner';
import {
  getPageTableState,
  storePageNumber,
  storePageSort,
  storePageSize,
} from '@src/util/local-storage-helper';
import { columnSortEventName, columnSortIconName } from '@src//util/view-utils';
import styleGuide from '@src/styleGuide';
import PaginationBar from '../../seafarerList/PaginationBar';
import './style.scss';

const pageSizeOptions = [10, 20, 50, 100, 300];
const { Icon } = styleGuide;

const defaultPageSize = 10;
const Table = React.memo(function TableMemo({
  className,
  tabName,
  columns,
  data,
  fetchData = () => {},
  pageCount: controlledPageCount,
  eventTracker = () => {},
  loading,
  advancedSearchParams,
  init_sort = [],
  seafarersTotalCount,
  tableRef,
  noPagination,
  resetPageNumberOnChange,
  isMangedSeafarer = false,
  isPaginationBotton = false,
}) {
  const defaultColumn = useMemo(
    () => ({
      minWidth: 120,
      width: 120,
    }),
    [],
  );

  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    prepareRow,
    page,
    canPreviousPage,
    canNextPage,
    pageCount,
    gotoPage,
    setPageSize,
    state: { pageIndex, pageSize, sortBy },
  } = useTable(
    {
      columns,
      data,
      defaultColumn,
      initialState: { sortBy: init_sort },
      manualPagination: true,
      manualSortBy: true,
      autoResetPage: false,
      autoResetSortBy: false,
      pageCount: controlledPageCount,
    },
    useSortBy,
    usePagination,
    useFlexLayout,
    useSticky,
  );

  const filterPages = (visiblePages, totalPages) =>
    visiblePages.filter((page) => page <= totalPages);
  const getVisiblePages = (page, total) => {
    if (total < 7) {
      return filterPages([1, 2, 3, 4, 5, 6], total);
    }
    if (page % 5 >= 0 && page >= 4 && page + 2 < total) {
      return [1, page, page + 1, page + 2, total];
    }
    if (page % 5 >= 0 && page > 4 && page + 2 >= total) {
      return [1, total - 3, total - 2, total - 1, total];
    }
    return [1, 2, 3, 4, 5, total];
  };
  const visiblePages = getVisiblePages(pageIndex, pageCount);

  const resetPage = (page_no = 0, page_size = 20) => {
    setPageSize(page_size);
    fetchData({ pageSize: page_size, sortBy, pageIndex: page_no });
    gotoPage(page_no);
  };

  useEffect(() => {
    !noPagination && storePageSort(tabName, sortBy);
  }, [sortBy]);

  useEffect(() => {
    const { pageIndex, pageSize } = getPageTableState(tabName, defaultPageSize);
    resetPage(pageIndex, pageSize);
  }, [tabName, sortBy, advancedSearchParams]);

  useEffect(() => {
    const { pageSize } = getPageTableState(tabName, defaultPageSize);
    resetPage(0, pageSize);
  }, [resetPageNumberOnChange]);

  const pageSwitch = (page_no) => {
    fetchData({ pageSize, sortBy, pageIndex: page_no });
    gotoPage(page_no);
    storePageNumber(tabName, page_no);
    eventTracker('pageSwitch', page_no);
  };
  const pageSizeSwitch = (page_size) => {
    // Internally pageIndex gets recalibrated as follows
    const new_index = Math.floor((pageIndex * pageSize) / page_size);
    setPageSize(page_size);
    fetchData({ pageIndex: new_index, sortBy, pageSize: page_size });
    storePageSize(tabName, page_size);
    storePageNumber(tabName, new_index);
    eventTracker('pageSizeSwitch', page_size);
  };
  const renderBody = () => {
    if (loading) {
      return <Spinner alignClass="load-spinner" />;
    }

    return page.map((row, index) => {
      prepareRow(row);
      return (
        <div key={row.id} {...row.getRowProps()} className="tr">
          {row.cells.map((cell) => {
            const tdProps = cell.getCellProps();
            return (
              <div
                key={tdProps.key}
                {...tdProps}
                className="td"
                title={typeof cell.value === 'string' ? cell.value : undefined}
              >
                {cell.render('Cell')}
              </div>
            );
          })}
        </div>
      );
    });
  };
  return (
    <>
      {noPagination ? (
        ''
      ) : (
        <Row>
          <PaginationBar
            className="top-pagination-bar pagination-bar"
            pageSwitch={pageSwitch}
            pageSizeSwitch={pageSizeSwitch}
            canPreviousPage={canPreviousPage}
            canNextPage={canNextPage}
            visiblePages={visiblePages}
            pageSize={pageSize}
            pageIndex={pageIndex}
            pageSizeOptions={pageSizeOptions}
          />
          <div className="seafarer-list-count">
            <b>{seafarersTotalCount}</b> Results
          </div>
        </Row>
      )}
      <div
        {...getTableProps()}
        className={
          isMangedSeafarer
            ? 'table sticky-table table-responsive-managed-seafarer crew-planner-table table-'
            : 'table sticky-table table-responsive crew-planner-table table-'
        }
        ref={tableRef}
      >
        <div className="header">
          {headerGroups.map((headerGroup, index) => (
            <div
              {...headerGroup.getHeaderGroupProps()}
              id={`top-header-${index}`}
              className="tr"
            >
              {headerGroup.headers.map((column, index2) => {
                const thProps = column.getHeaderProps(column.getSortByToggleProps());
                return (
                  <div key={column.id} {...thProps} className="th" id={`as-${index} + ${index2}`}>
                    {column.render('Header')}
                    <span>
                      {column.canSort && (
                        <Icon
                          icon={columnSortIconName(column)}
                          size={20}
                          className="default"
                          onClick={() => eventTracker('sortBy', columnSortEventName(column))}
                        />
                      )}
                    </span>
                  </div>
                );
              })}
            </div>
          ))}
        </div>
        <div {...getTableBodyProps()} className="body">
          {loading || seafarersTotalCount > 0 ? (
            renderBody()
          ) : (
            <div className="no-result-found mt-4">
              <Icon icon="alert" className="alert-icon-no-search" />
              <div>
                <b>No result match your criteria</b>
              </div>
            </div>
          )}
        </div>
      </div>
      {isPaginationBotton && (
        <Row>
          <PaginationBar
            className="top-pagination-bar pagination-bar"
            pageSwitch={pageSwitch}
            pageSizeSwitch={pageSizeSwitch}
            canPreviousPage={canPreviousPage}
            canNextPage={canNextPage}
            visiblePages={visiblePages}
            pageSize={pageSize}
            pageIndex={pageIndex}
            pageSizeOptions={pageSizeOptions}
          />
        </Row>
      )}
    </>
  );
});
export default Table;
