import { STATUS } from '@src/constants/crewPlanner';
import { Icon } from '@src/styleGuide';
import React, { SyntheticEvent } from 'react';
import { Button, OverlayTrigger, Tooltip } from 'react-bootstrap';
import { ExclamationTriangle } from 'react-bootstrap-icons';
import classNames from 'classnames';
import { checkMissingPersonnel, checkAdditionalRequest } from '@src/pages/CrewPlanner/utils';

interface SeafarerNameProps {
  row: any;
  id: number;
  style?: React.CSSProperties;
  className?: string;
  eventTracker?: Function;
  remark?: string;
}

const renderRemark = (remark: string) => {
  if (!remark) return '';
  return (
    <OverlayTrigger placement="top" overlay={<Tooltip id="tooltip-top">{remark}</Tooltip>}>
      <span className="d-inline-block ml-1">
        <Icon icon="alert" size={15} />
      </span>
    </OverlayTrigger>
  );
};
const getName = (seafarer) =>
  [
    seafarer?.seafarer_person?.first_name,
    seafarer?.seafarer_person?.middle_name,
    seafarer?.seafarer_person?.last_name,
  ]
    .filter(Boolean)
    .join(' ');

// NOSONAR
function SeafarerName({
  row,
  id,
  className = '',
  eventTracker,
  remark = '',
  style = {},
}: Readonly<SeafarerNameProps>) {
  const openSeafarerGeneral = (event: SyntheticEvent) => {
    event.stopPropagation();
    if (!id) return;
    if (
      eventTracker?.name === 'contractExpEventTracker' ||
      eventTracker?.name === 'avlSeafarerEventTracker'
    ) {
      eventTracker?.(
        'seafarerNameLink',
        `${row?.seafarer_person?.first_name ?? ''} ${row?.seafarer_person?.middle_name ?? ''} ${
          row?.seafarer_person?.last_name ?? ''
        }`,
      );
    } else {
      eventTracker?.(
        'seafarerNameLink',
        `${getName(row)}`,
      );
    }
    window.open(`/seafarer/details/${id}/general`, '_blank');
  };
  const isPlanRejected = row?.crew_planning?.crew_planning_status_lookup?.desc === STATUS.rejected;
  const isMissingPersonnal = checkMissingPersonnel(row);
  const isAdditionalCrewRequest = checkAdditionalRequest(row);
  return (
    <>
      {isPlanRejected && (
        <OverlayTrigger
          placement="right"
          overlay={
            <Tooltip id="rejected-seafarer-warning">Previously Rejected by POD Manager</Tooltip>
          }
        >
          <ExclamationTriangle
            width={16}
            style={{ color: '#FFC107', cursor: 'pointer', marginLeft: '3px', overflow: 'unset' }}
            size={24}
          />
        </OverlayTrigger>
      )}
      <Button
        onClick={(e) => openSeafarerGeneral(e)}
        variant="link"
        style={!isPlanRejected ? style : {}}
        className={classNames(className, 'button-link btn btn-link text-align-left')}
        title={getName(row)}
      >
        {getName(row)}
      </Button>
      {!isMissingPersonnal && !isAdditionalCrewRequest && renderRemark(remark)}
    </>
  );
}
export default SeafarerName;
