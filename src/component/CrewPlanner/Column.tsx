import React from 'react';
import moment from 'moment-timezone';
import { Column } from 'react-table';
import { OverlayTrigger, Tooltip } from 'react-bootstrap';
import {
  checkAdditionalRequest,
  checkMissingPersonnel,
  getScoringColor,
} from '@src/pages/CrewPlanner/utils';
import { Pen } from 'react-bootstrap-icons';
import { covertDaysToYear, Dash, dateAsString, stringAsDate } from '../../model/utils';
import {
  CREW_PLANNING_TYPE,
  DOCUMENT_SCORE_GRADE,
  NAME_STATUS_MAPPING,
  ON_LEAVE_SEAFARER,
  SEAFARERS_TO_RELIEVE,
  STATUS,
} from '../../constants/crewPlanner';
import VesselNameLink from '../CrewList/VesselNameLink';
import SeafarerName from './SeafarerName';
import DueDate from './DueDate';
import { ShowYearsMonthsFromYears } from './ShowYears';
import styleGuide from '../../styleGuide';
import Spinner from '../common/Spinner';
import { getBadgeColor, getIsClubbedOrUnclubbed, getFullName, getRank } from './utils';
import parentHKIDLink from '../common/HKIDLink';
import { seafarerStatusService } from 'paris2-seafarer-status';

const journeyStatusJson = seafarerStatusService.getJourneyStatus();
const { Icon } = styleGuide;

export const getStatusByKey = (status: string, mapping) => {
  return (
    <span className={`box ${mapping[status]}-box`} title={status}>
      {status ?? '---'}
    </span>
  );
};

export const StatusColorMapping = {
  Unclubbed: 'yellow',
  Unplanned: 'red',
  Clubbed: 'green',
  'Club Confirmed': 'green',
};

export const getStatus = (status: string) => {
  if (status) {
    return [STATUS.clubbed, STATUS.unclubbed, STATUS.club_confirmed].includes(status)
      ? status
      : STATUS.unplanned;
  }
  return STATUS.unplanned;
};
export const getColorClassForDueDate = (value, status) => {
  if (!value) return;
  if (status === STATUS.clubbed || status === STATUS.unclubbed || status === STATUS.club_confirmed)
    return 'font-green';
  const now = moment();
  const dateOfExpiry = moment(value);
  if (dateOfExpiry.isBefore(now)) {
    return 'font-red';
  }
  if (dateOfExpiry.isAfter(moment().add(30, 'days'))) {
    return 'font-blue';
  }
  return 'font-orange';
};
export const getSeafarerToRelieveColumns = ({
  eventTracker,
  handleRemark = () => {},
  handleSelectedSeafarer,
  selectedSeafarer,
  selectedFilters,
  activeKey,
  hasEditAccess,
}) => {
  const { isClubbedOrUnclubbed, crewPlanningStatus } = getIsClubbedOrUnclubbed(selectedFilters);
  const handleEventTracker = (row) => {
    if (checkAdditionalRequest(row)) {
      eventTracker('additionalCrewSelected');
    } else if (checkMissingPersonnel(row)) {
      eventTracker('missingPersonnelSelected');
    } else {
      eventTracker('onBoardSeafarerSelected', getFullName(row));
    }
  };

  const getOrder = () => {
    let order;

    if (activeKey === ON_LEAVE_SEAFARER) {
      order = 7;
    } else if (isClubbedOrUnclubbed) {
      order = 6;
    } else {
      order = 5;
    }
    return order;
  };

  const getOrderDueDate = () => {
    let order;

    if (activeKey === ON_LEAVE_SEAFARER) {
      order = 8;
    } else if (isClubbedOrUnclubbed) {
      order = 5;
    } else {
      order = 6;
    }
    return order;
  };

  return [
    {
      Header: isClubbedOrUnclubbed ? 'Onboard Seafarer' : 'Seafarer Name',
      id: 'seafarer_person.first_name',
      sticky: window.innerWidth > 960 ? 'left' : null,
      order: 1,
      width: 250,
      accessor: function nameAccessor(row) {
        const showCheckbox = ![STATUS.club_confirmed, STATUS.clubbed].includes(crewPlanningStatus);
        return (
          <div className="d-flex align-items-center">
            {showCheckbox && (
              <input
                type="checkbox"
                data-testid="checkbox-for-seafarer"
                className="checkbox-secondary"
                checked={row?.id === selectedSeafarer?.id}
                onChange={() => {
                  handleSelectedSeafarer(row);
                  handleEventTracker(row);
                }}
              />
            )}
            {checkAdditionalRequest(row) && (
              <span className="text-danger" style={{ marginLeft: showCheckbox ? 27 : 8 }}>
                <b>Additional crew</b>
              </span>
            )}
            {checkMissingPersonnel(row) && (
              <span className="text-danger" style={{ marginLeft: showCheckbox ? 27 : 8 }}>
                <b>Missing Personnel</b>
              </span>
            )}
            {row?.seafarer_person && (
              <span
                title={`${row?.seafarer_person?.first_name} ${row?.seafarer_person?.last_name}`}
              >
                {row?.seafarer_person?.first_name || row?.seafarer_person?.last_name ? (
                  <SeafarerName
                    id={row?.id}
                    style={showCheckbox ? { marginLeft: 19 } : {}}
                    row={row}
                    eventTracker={eventTracker}
                  />
                ) : (
                  '---'
                )}
              </span>
            )}
          </div>
        );
      },
      disableSortBy: false,
    },
    {
      type: 'number',
      Header: 'HKID',
      id: 'hkid',
      name: 'hkid',
      accessor: (row) => parentHKIDLink(row),
      order: 2,
      disableSortBy: false,
      minWidth: 80,
      sticky: window.innerWidth > 960 ? 'left' : null,
    },
    isClubbedOrUnclubbed && {
      Header: 'Reliever Seafarer',
      id: 'crew_planning.reliever.first_name',
      sticky: window.innerWidth > 960 ? 'left' : null,
      order: 2,
      disableSortBy: true,
      width: 200,
      accessor: function nameAccessor(row) {
        const reliever = row?.crew_planning?.reliever;
        return row?.crew_planning?.crew_planning_status_lookup?.desc !== STATUS.rejected &&
          reliever ? (
          <SeafarerName id={reliever.id} row={reliever} className="m-0" />
        ) : (
          '---'
        );
      },
    },
    isClubbedOrUnclubbed && {
      type: 'text',
      Header: 'Journey Status',
      id: 'crew_planning:reliever:seafarer_person:current_journey_status',
      name: 'crew_planning:reliever:seafarer_person:current_journey_status',
      accessor: function journeyStatusAccessor(row) {
        const journeyStatus =
          journeyStatusJson[
          row?.crew_planning?.reliever?.seafarer_person?.current_journey_status?.value
          ?? row?.crew_planning?.reliever?.seafarer_person?.current_journey_status
            ]?.name
          ?? Dash
        ;
        return <div title={journeyStatus}>{journeyStatus}</div>;
      },
      width: 150,
      order: 12,
      showToOwner: true,
      disableSortBy: true,
    },
    {
      type: 'text',
      Header: 'Rank',
      id: 'seafarer_person:seafarer_status_history:seafarer_rank.sortpriority',
      name: 'seafarer_person.seafarer_status_history[0].seafarer_rank.sortpriority',
      accessor: function rankAccessor(row) {
        const rank =
          row?.seafarer_person?.seafarer_status_history?.[0]?.seafarer_rank?.value ??
          row.rank.value ??
          row.rank ??
          '---';
        return <div title={rank}>{rank}</div>;
      },
      width: 150,
      order: 3,
      showToOwner: true,
      disableSortBy: true,
    },
    {
      Header: 'Status',
      id: 'status',
      accessor: (row) =>
        getStatusByKey(
          NAME_STATUS_MAPPING[getStatus(row?.crew_planning?.crew_planning_status_lookup?.desc)],
          StatusColorMapping,
        ),
      width: 150,
      order: 4,
      disableSortBy: true,
    },
    {
      type: 'text',
      Header: 'Vessel',
      id: 'seafarer_person:seafarer_status_history.vessel_name',
      name: 'seafarer_person.seafarer_status_history[0].vessel_name',
      accessor: (row) => {
        const vesselOwnershipId =
          row?.seafarer_person?.seafarer_status_history?.[0]?.vessel_ownership_id ??
          row.vessel_ownership_id;
        const vesselName =
          row?.seafarer_person?.seafarer_status_history?.[0]?.vessel_name ?? row.vessel_name;
        return (
          <VesselNameLink
            className="text-align-left"
            ownershipId={vesselOwnershipId}
            vesselName={vesselName}
            eventTracker={eventTracker}
          />
        );
      },
      width: 200,
      order: activeKey === ON_LEAVE_SEAFARER ? 5 : 7,
      disableSortBy: false,
    },
    {
      Header: 'Vessel Category',
      id: 'vessel_category',
      width: 175,
      accessor: (row) =>
        row?.seafarer_person?.seafarer_status_history?.[0]?.vessel_type ??
        row.vessel_type?.value ??
        row.vessel_type ??
        '---',
      order: activeKey === ON_LEAVE_SEAFARER ? 6 : 8,
      disableSortBy: true,
    },
    !isClubbedOrUnclubbed && {
      type: 'date',
      Header: 'Joining Date',
      id: 'seafarer_person.seafarer_status_history[0].status_date',
      name: 'seafarer_person.seafarer_status_history[0].status_date',
      accessor: function endOfContractAccessor(row) {
        const value = row.seafarer_person?.seafarer_status_history?.[0].status_date;
        return <DueDate colorClass="" value={value} />;
      },
      disableSortBy: true,
      width: 175,
      order: activeKey === ON_LEAVE_SEAFARER ? 7 : 5,
      showToOwner: true,
    },
    {
      type: 'date',
      Header: 'Due Off Date',
      id: 'seafarer_person:seafarer_status_history.expected_contract_end_date',
      name: 'seafarer_person.seafarer_status_history[0].expected_contract_end_date',
      accessor: function endOfContractAccessor(row) {
        const value =
          row?.seafarer_person?.seafarer_status_history?.[0]?.expected_contract_end_date;
        const { colorClass, diff } = getBadgeColor(
          value,
          row?.crew_planning?.crew_planning_status_lookup?.desc,
        );
        return (
          <>
            <DueDate value={value} />
            {value ? (
              <span title={`${diff}D`} className={`oval ${colorClass}`}>{`${diff}D`}</span>
            ) : (
              ''
            )}
          </>
        );
      },
      disableSortBy: false,
      width: 175,
      order: getOrderDueDate(),
      showToOwner: true,
    },
    isClubbedOrUnclubbed && {
      type: 'date',
      Header: 'Available Date',
      id: 'seafarer_contact_log[0].availability_date',
      name: 'seafarer_contact_log[0].availability_date',
      accessor: function endOfContractAccessor(row) {
        const value = row?.crew_planning?.reliever?.seafarer_contact_log?.[0]?.availability_date;
        const { colorClass, diff } = getBadgeColor(
          value,
          row?.crew_planning?.crew_planning_status_lookup?.desc,
        );
        return (
          <>
            <DueDate value={value} />
            {value ? (
              <span title={`${diff}D`} className={`oval ${colorClass}`}>{`${diff}D`}</span>
            ) : (
              ''
            )}
          </>
        );
      },
      disableSortBy: true,
      width: 175,
      order: getOrder(),
      showToOwner: true,
    },
    {
      type: 'text',
      Header: 'Nationality',
      id: 'seafarer_person:nationality.value',
      name: 'seafarer_person.nationality.value',
      accessor: function addedByAccessor(row) {
        return (
          <div title={row?.seafarer_person?.nationality?.value ?? '---'}>
            {row?.seafarer_person?.nationality?.value ?? '---'}
          </div>
        );
      },
      order: 11,
      disableSortBy: false,
      minWidth: 120,
      showToOwner: true,
    },
    !isClubbedOrUnclubbed && {
      Header: 'Reporting Office',
      id: 'reporting_office',
      accessor: (row) => row.seafarer_reporting_office?.value ?? '---',
      width: 175,
      order: 9,
      disableSortBy: true,
    },
    {
      Header: 'Vessel Owner',
      id: 'vessel_owner',
      accessor: (row) => {
        return row.vessel_owner ?? row?.owner?.value ?? '---';
      },
      width: 175,
      order: 10,
      disableSortBy: true,
    },
    {
      type: 'text',
      Header: 'Tech Group',
      id: 'seafarer_person:seafarer_status_history.vessel_tech_group',
      name: 'seafarer_person.seafarer_status_history[0].vessel_tech_group',
      accessor: (row) =>
        row?.seafarer_person?.seafarer_status_history?.[0]?.vessel_tech_group ??
        row?.fleet_staff?.tech_group ??
        row?.tech_group ??
        '---',
      width: 175,
      disableSortBy: true,
      order: 11,
    },
    {
      Header: 'Onboard Seafarer Remarks',
      id: 'remarks',
      sticky: 'right',
      width: 270,
      order: 17,
      disableSortBy: true,
      accessor: (row) => {
        const remarkText = row?.crew_planning_remarks?.[0]?.remarks;
        return (
          <div className="remark-cell w-100" title={remarkText}>
            <span className="remark-text mr-1">{remarkText}</span>
            {hasEditAccess ? (
              <Pen
                size={18}
                className="remark-pencil-icon paris2-icon btn-icon"
                onClick={() => {
                  handleRemark(row, 'crew_planning_remarks[0]', SEAFARERS_TO_RELIEVE, row.id);
                }}
              />
            ) : (
              ''
            )}
            {!remarkText && <span className="remark-empty">---</span>}
          </div>
        );
      },
    },
  ]
    .filter(Boolean)
    .sort((a, b) => (a.order > b.order ? 1 : -1));
};

export const getAvailableSeafarersColumns = ({
  eventTracker,
  handleRemark = () => {},
  handleSelectedSeafarer,
  selectedSeafarer,
  selectedFilters,
  activeKey,
  vesselType,
  suggestedSeafarersLoading,
  isSuggestedSeafarer,
  hasEditAccess,
}): Column[] => {
  const { isClubbedOrUnclubbed, crewPlanningStatus } = getIsClubbedOrUnclubbed(selectedFilters);
  return [
    {
      Header: isClubbedOrUnclubbed ? 'Reliever Seafarer' : 'Seafarer Name',
      id: 'seafarer_person.first_name',
      sticky: window.innerWidth > 960 ? 'left' : null,
      width: 250,
      order: 1,
      accessor: function nameAccessor(row) {
        const showCheckBox = ![STATUS.club_confirmed, STATUS.clubbed].includes(crewPlanningStatus);
        return (
          <div className="d-flex align-items-center">
            {showCheckBox && (
              <input
                type="checkbox"
                className="checkbox-secondary"
                data-testid="checkbox-for-seafarer"
                checked={selectedSeafarer?.id === row?.id}
                onChange={() => {
                  handleSelectedSeafarer(row);
                  eventTracker(
                    'relieverSeafarerSelected',
                    `${row?.seafarer_person?.first_name} ${row?.seafarer_person?.last_name}`,
                  );
                }}
              />
            )}
            <SeafarerName
              id={row?.id}
              row={row}
              style={showCheckBox ? { marginLeft: 19 } : {}}
              eventTracker={eventTracker}
            />
          </div>
        );
      },
      disableSortBy: false,
    },
    isClubbedOrUnclubbed && {
      Header: 'Onboard Seafarer',
      id: 'crew_planning.seafarer',
      width: 200,
      order: 2,
      sticky: window.innerWidth > 960 ? 'left' : null,
      disableSortBy: true,
      accessor: function nameAccessor(row) {
        const seafarer = row?.crew_planning?.seafarer;
        const relieverId = row?.crew_planning?.reliever_id;
        if (
          !seafarer &&
          relieverId &&
          row?.crew_planning?.planning_type === CREW_PLANNING_TYPE.missing_personnel
        ) {
          return (
            <span className="text-danger" style={{ marginLeft: 8 }}>
              <b>Missing Personnel</b>
            </span>
          );
        }
        if (
          !seafarer &&
          relieverId &&
          row?.crew_planning?.planning_type === CREW_PLANNING_TYPE.add_rank
        ) {
          return (
            <span className="text-danger" style={{ marginLeft: 8 }}>
              <b>Additional crew</b>
            </span>
          );
        }

        return row?.crew_planning?.crew_planning_status_lookup?.desc !== STATUS.rejected &&
          seafarer ? (
          <SeafarerName id={seafarer.id} row={seafarer} />
        ) : (
          '---'
        );
      },
    },
    {
      type: 'number',
      Header: 'HKID',
      id: 'hkid',
      name: 'hkid',
      accessor: (row) => parentHKIDLink(row),
      order: 2,
      disableSortBy: false,
      minWidth: 80,
      sticky: window.innerWidth > 960 ? 'left' : null,
    },
    isClubbedOrUnclubbed && {
      type: 'text',
      Header: 'Journey Status',
      id: 'seafarer_person:current_journey_status',
      name: 'seafarer_person:current_journey_status',
      accessor: function journeyStatusAccessor(row) {
        const journeyStatus =
          journeyStatusJson[
          row?.seafarer_person?.current_journey_status?.value
          ?? row?.seafarer_person?.current_journey_status
            ]?.name
          ?? Dash
        ;
        return <div title={journeyStatus}>{journeyStatus}</div>;
      },
      width: 150,
      order: 12,
      showToOwner: true,
      disableSortBy: true,
    },
    {
      type: 'text',
      Header: 'Rank',
      id: 'seafarer_rank.value',
      name: 'seafarer_rank.value',
      accessor: (row) => row?.rank ?? row?.seafarer_rank?.value ?? '---',
      disableSortBy: true,
      width: 150,
      order: 3,
    },
    {
      Header: 'Status',
      id: 'status',
      accessor: (row) =>
        getStatusByKey(
          NAME_STATUS_MAPPING[getStatus(row?.crew_planning?.crew_planning_status_lookup?.desc)],
          StatusColorMapping,
        ),
      width: 150,
      order: 4,
      disableSortBy: true,
    },
    !isClubbedOrUnclubbed && {
      Header: 'Sign Off Date',
      type: 'date',
      id: 'seafarer_experience.end_date',
      name: 'latest_experience.end_date',
      width: 150,
      order: 5,
      disableSortBy: true,
      accessor: (row) =>
        row?.latest_experience?.end_date
          ? dateAsString(stringAsDate(row.latest_experience.end_date))
          : '---',
    },
    isClubbedOrUnclubbed && {
      Header: 'Due Off Date',
      type: 'date',
      id: 'seafarer_experience.end_date',
      name: 'latest_experience.end_date',
      width: 150,
      order: 5,
      disableSortBy: false,
      accessor: (row) => {
        const value =
          row?.crew_planning?.seafarer?.seafarer_person?.seafarer_status_history?.[0]
            ?.expected_contract_end_date;
        const { colorClass, diff } = getBadgeColor(
          value,
          row?.crew_planning?.crew_planning_status_lookup?.desc,
        );

        return (
          <>
            <DueDate value={value} />
            {value ? (
              <span title={`${diff}D`} className={`oval ${colorClass}`}>{`${diff}D`}</span>
            ) : (
              ''
            )}
          </>
        );
      },
    },
    {
      type: 'date',
      Header: 'Available Date',
      id: 'seafarer_contact_log.availability_date',
      name: 'seafarer_person.seafarer_status_history[0].expected_contract_end_date',
      accessor: function avalilabilityDateAccessor(row) {
        const value = row?.seafarer_contact_log?.[0]?.availability_date;
        const { colorClass, diff } = getBadgeColor(
          value,
          row?.crew_planning?.crew_planning_status_lookup?.desc,
        );
        return (
          <>
            <DueDate value={value} />
            {value ? (
              <span title={`${diff}D`} className={`oval ${colorClass}`}>{`${diff}D`}</span>
            ) : (
              ''
            )}
          </>
        );
      },
      disableSortBy: false,
      width: 175,
      order: 6,
      showToOwner: true,
    },
    !isClubbedOrUnclubbed && {
      Header: 'Last Contacted',
      id: 'seafarer_contact_log.contact_date',
      name: 'seafarer_contact_log[0].contact_date',
      accessor: (row) =>
        row?.seafarer_contact_log?.[0]?.contact_date
          ? dateAsString(stringAsDate(row?.seafarer_contact_log[0]?.contact_date))
          : '---',
      width: 180,
      order: 7,
    },
    {
      type: 'text',
      Header: 'Nationality',
      id: 'seafarer_person:nationality.value',
      name: 'seafarer_person.nationality.value',
      accessor: function addedByAccessor(row) {
        return (
          <div title={row?.seafarer_person?.nationality?.value ?? '---'}>
            {row?.seafarer_person?.nationality?.value ?? '---'}
          </div>
        );
      },
      order: 11,
      disableSortBy: false,
      minWidth: 120,
      showToOwner: true,
    },
    // {
    //   Header: 'Recommendation Status',
    //   id: 'recommended_status',
    //   accessor: (row) =>
    //     getStatusByKey(
    //       getJourneyStatus(row?.crew_plan?.seafarer_journey_status),
    //       RecommendedStatusColorMapping,
    //     ),
    //   disableSortBy: true,
    //   width: 300,
    // },

    !isClubbedOrUnclubbed && {
      Header: 'Exp. in Rank',
      type: 'number',
      id: 'duration_in_target_rank',
      name: 'experience_summary.duration_in_target_rank',
      handleExportOptionalFn: covertDaysToYear,
      accessor: function yearsInRankAccessor(row) {
        const ocimf = row?.ocimf?.find(
          (o) => o?.group_by_name === 'rank' && o?.group_value === getRank(row),
        )?.years;
        return ocimf ? (
          <div>
            <ShowYearsMonthsFromYears value={ocimf} />
          </div>
        ) : (
          '---'
        );
      },
      disableSortBy: true,
      width: 180,
      order: 8,
    },

    !isClubbedOrUnclubbed && {
      Header:
        activeKey === ON_LEAVE_SEAFARER
          ? 'Exp in Previous Vessel Category'
          : 'Exp in Vessel Category',
      type: 'text',
      id: 'target_vessel_type.value',
      name: 'experience_summary.duration_on_target_vessel_type',
      width: 205,
      accessor: function yearsOnVesselTypeAccessor(row) {
        const ocimf = row?.ocimf?.find(
          (o) =>
            o?.group_by_name === 'vessel_type' &&
            o?.group_value === (vesselType ?? row?.latest_experience?.vessel_type),
        )?.years;
        return ocimf ? (
          <div>
            <ShowYearsMonthsFromYears value={ocimf} />
          </div>
        ) : (
          '---'
        );
      },
      order: 9,
      disableSortBy: true,
    },
    !isClubbedOrUnclubbed && {
      Header: 'Exp. in Company',
      id: 'duration_with_company',
      name: 'experience_summary.duration_with_company',
      handleExportOptionalFn: covertDaysToYear,
      accessor: function yearswithCompanyAccessor(row) {
        const ocimf = row?.ocimf?.find((o) => o?.group_by_name === 'company')?.years;
        return ocimf ? (
          <div>
            <ShowYearsMonthsFromYears value={ocimf} />
          </div>
        ) : (
          '---'
        );
      },
      width: 180,
      order: 10,
      disableSortBy: true,
    },

    !isClubbedOrUnclubbed && {
      Header: 'Reporting Office',
      id: 'seafarer_reporting_office.value',
      name: 'seafarer_reporting_office.value',
      accessor: (row) => row?.seafarer_reporting_office?.value ?? '---',
      disableSortBy: true,
      width: 180,
      order: 11,
    },

    !isClubbedOrUnclubbed && {
      Header: 'Previous Vessel Owner',
      id: 'previous_vessel_owner',
      accessor: (row) => row?.last_vessel_owner ?? '---',
      width: 210,
      order: 12,
      disableSortBy: true,
    },
    isClubbedOrUnclubbed && {
      Header: 'Vessel Owner',
      id: 'previous_vessel_owner',
      accessor: (row) =>
        row?.crew_planning?.seafarer?.seafarer_person?.seafarer_status_history?.[0]?.owner_name ??
        row?.crew_planning?.owner?.value ??
        '---',
      width: 210,
      order: 9,
      disableSortBy: true,
    },
    !isClubbedOrUnclubbed && {
      Header: 'Previous Vessel',
      id: 'seafarer_experience.vessel_name',
      name: 'latest_experience.vessel_name',
      accessor: function previousVesselAccessor(row) {
        const vesselOwnershipId =
          row?.latest_experience?.vessel_ownership_id ?? row.vessel_ownership_id;
        const vesselName = row?.latest_experience?.vessel_name ?? row.vessel_name;
        return vesselOwnershipId ? (
          <VesselNameLink
            className="text-align-left"
            eventTracker={eventTracker}
            ownershipId={vesselOwnershipId}
            vesselName={vesselName}
          />
        ) : (
          <span style={{ padding: '0 0.4rem' }}>
            {row?.latest_experience?.vessel_name ?? '---'}
          </span>
        );
      },
      width: 200,
      order: 13,
      disableSortBy: true,
    },
    isClubbedOrUnclubbed && {
      Header: 'Vessel',
      id: 'seafarer_experience.vessel_name',
      name: 'latest_experience.vessel_name',
      accessor: function previousVesselAccessor(row) {
        const vessel =
          row?.crew_planning?.seafarer?.seafarer_person?.seafarer_status_history?.[0] ??
          row?.crew_planning;
        const vesselOwnershipId = vessel?.vessel_ownership_id ?? vessel?.ownership_id;
        return vesselOwnershipId ? (
          <VesselNameLink
            className="text-align-left"
            eventTracker={eventTracker}
            ownershipId={vesselOwnershipId}
            vesselName={vessel?.vessel_name}
          />
        ) : (
          <span style={{ padding: '0 0.4rem' }}>{vessel?.vessel_name ?? '---'}</span>
        );
      },
      width: 200,
      order: 8,
      disableSortBy: true,
    },
    isClubbedOrUnclubbed && {
      Header: 'Vessel Category',
      id: 'vessel_category',
      width: 175,
      accessor: (row) =>
        row?.crew_planning?.seafarer?.seafarer_person?.seafarer_status_history?.[0]?.vessel_type ??
        row?.crew_planning?.vessel_type?.value ??
        '---',
      disableSortBy: true,
      order: 9,
    },
    isClubbedOrUnclubbed && {
      type: 'text',
      Header: 'Tech Group',
      id: 'seafarer_person:seafarer_status_history.vessel_tech_group',
      name: 'seafarer_person.seafarer_status_history[0].vessel_tech_group',
      accessor: (row) =>
        row?.crew_planning?.seafarer?.seafarer_person?.seafarer_status_history?.[0]
          ?.vessel_tech_group ??
        row?.crew_planning?.fleet_staff?.tech_group ??
        '---',
      width: 175,
      disableSortBy: true,
      order: 15,
    },
    isSuggestedSeafarer && {
      Header: (
        <div>
          Average Score <br /> [0-100]
        </div>
      ),
      id: 'average_score',
      sticky: 'right',
      width: 150,
      disableSortBy: true,
      accessor: (row) => {
        let content;

        if (suggestedSeafarersLoading) {
          content = <Spinner customClass="seafarer-score-loader" />;
        } else if (row?.score?.score) {
          content = (
            <OverlayTrigger
              placement="right"
              overlay={
                <Tooltip id="score-tooltip" className="score-tooltip">
                  <div className="tooltip-scores-overlay">
                    <div className="tooltip-scores-row">
                      <div className="tooltip-score-title">Experience Score</div>
                      <div className={getScoringColor(row?.score?.score_object?.experience_score)}>
                        {row?.score?.score_object?.experience_score?.toFixed(1)}
                      </div>
                    </div>
                    <div className="tooltip-scores-row">
                      <div className="tooltip-score-title">Appraisal Score</div>
                      <div className={getScoringColor(row?.score?.score_object?.appraisal_score)}>
                        {row?.score?.score_object?.appraisal_score?.toFixed(1)}
                      </div>
                    </div>
                  </div>
                </Tooltip>
              }
            >
              <div className={getScoringColor(row?.score?.score)}>
                {row?.score?.score?.toFixed(1)}
              </div>
            </OverlayTrigger>
          );
        } else {
          content = '--';
        }

        return content;
      },
      order: 16,
    },
    isSuggestedSeafarer && {
      Header: 'Document Score',
      id: 'document_score',
      sticky: 'right',
      width: 150,
      disableSortBy: true,
      accessor: (row) => {
        const missingDocsCount = row?.score?.score_object?.missing_documents?.length;
        const documentGrade = row?.score?.score_object?.documents_grade;
        let content;

        if (suggestedSeafarersLoading) {
          content = <Spinner customClass="seafarer-score-loader" />;
        } else if (row?.score?.score) {
          content = (
            <OverlayTrigger
              placement="left"
              overlay={
                <Tooltip id="score-tooltip" className="score-tooltip">
                  <div className="tooltip-scores-overlay">
                    {missingDocsCount ? (
                      <>
                        <div className="mb-2" style={{ textAlign: 'start' }}>
                          Following {missingDocsCount} document(s) are missing:
                        </div>
                        {row?.score?.score_object?.missing_documents?.map((doc: string) => (
                          <div key={doc} className="tooltip-scores-row">
                            -{doc}
                          </div>
                        ))}
                      </>
                    ) : (
                      <div className="tooltip-scores-row">No documents missing</div>
                    )}
                  </div>
                </Tooltip>
              }
            >
              <div className={getScoringColor(documentGrade)}>
                {documentGrade}{' '}
                {documentGrade === DOCUMENT_SCORE_GRADE.Fail ? `[${missingDocsCount}]` : ''}
              </div>
            </OverlayTrigger>
          );
        } else {
          content = '--';
        }

        return content;
      },
      order: 17,
    },
    !isClubbedOrUnclubbed && {
      Header: 'Reliever Seafarer Remarks',
      id: 'remarks',
      sticky: 'right',
      width: 270,
      order: 18,
      disableSortBy: true,
      accessor: (row) => {
        const remarkText = row?.crew_planning_remarks?.[0]?.remarks;
        return (
          <div className="remark-cell w-100" title={remarkText}>
            <div className="remark-text">{remarkText}</div>
            {hasEditAccess ? (
              <Pen
                size={18}
                className="remark-pencil-icon paris2-icon btn-icon"
                onClick={() => {
                  handleRemark(row, 'crew_planning_remarks[0]', ON_LEAVE_SEAFARER, row.id);
                }}
              />
            ) : (
              ''
            )}
            {!remarkText && <span className="remark-empty">---</span>}
          </div>
        );
      },
    },
    isClubbedOrUnclubbed && {
      Header: 'Onboard Seafarer Remarks',
      id: 'remarks',
      sticky: 'right',
      width: 270,
      order: 18,
      disableSortBy: true,
      accessor: (row) => {
        const remarkText = row?.crew_planning?.seafarer?.crew_planning_remarks?.[0]?.remarks;
        return (
          <div className="remark-cell w-100" title={remarkText}>
            <div className="remark-text mr-1">{remarkText}</div>
            {hasEditAccess ? (
              <Pen
                size={18}
                className="remark-pencil-icon paris2-icon btn-icon"
                onClick={() => {
                  handleRemark(
                    row?.crew_planning?.seafarer,
                    'crew_planning.seafarer.crew_planning_remarks[0]',
                    ON_LEAVE_SEAFARER,
                    row.id,
                    row?.crew_planning?.planning_type,
                  );
                }}
              />
            ) : (
              ''
            )}
            {!remarkText && <span className="remark-empty">---</span>}
          </div>
        );
      },
    },
  ]
    .filter(Boolean)
    .sort((a, b) => (a.order > b.order ? 1 : -1));
};
