import React from 'react';
import { ExclamationTriangle } from 'react-bootstrap-icons';
import Spinner from '../common/Spinner';

interface KpiCardProps {
  isLoading: boolean;
  number: number | null;
  title: string;
}

const KpiCard: React.FC<KpiCardProps> = ({ isLoading, number, title }) => {
  return (
    <div className="kpi-card">
      <div className="summerized-number">
        <span className="number">
          {isLoading ? (
            <Spinner />
          ) : (
            number ?? <ExclamationTriangle width={20} style={{ color: '#FFC107' }} size={30} />
          )}
        </span>
      </div>
      <div className="kpi-title">{title}</div>
    </div>
  );
};

export default KpiCard;
