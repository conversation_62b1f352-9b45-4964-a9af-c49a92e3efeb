/* eslint-disable react/display-name */
/* eslint-disable react/prop-types */
import React from 'react';
import { Container } from 'react-bootstrap';
import moment from 'moment-timezone';
import { CREW_PLANNING_STATUS_MAPPING, STATUS } from '@src/constants/crewPlanner';
import { capitalizeFirstLetter } from '@src/model/utils';
import { Icon } from '@src/styleGuide';
import { getAvailableSeafarersColumns, getSeafarerToRelieveColumns } from './Column';
import DynamicFilter, { FilterConfig } from './DynamicFilter';
import GroupButtonSelector from '../common/GroupButtonSelector';
import InfinteScrollTable from '../common/InfiniteScrollTable';
import { getIsClubbedOrUnclubbed } from './utils';

export const getAvailableSeafarerFilter = (selectedRanks = [], isClubbedOrUnclubbed = false) =>
  [
    {
      type: 'search',
      props: {
        placeholder: 'Search by Name or HKID',
      },
      name: 'search',
      queryKey: 'keyword',
      className: 'input-col first-col',
      inputType: 'string',
    },
    {
      type: 'drop_down',
      props: {
        placeholder: 'Select Rank',
        isSearchable: true,
        multiple: true,
      },
      name: 'rank',
      dataKey: 'seafarer.ranks',
      queryKey: 'target_rank.value',
      apiQueryValueSelector: (value, data) =>
        value?.map((v) => data.find((d) => d.id === v)?.value) ?? [],
      inputType: 'array_number',
      className: 'input-col',
      global: true,
      seperator: ',',
      defaultValueOnLoad: selectedRanks?.join(','),
    },
    {
      type: 'drop_down',
      props: {
        placeholder: 'Select Previous Owner',
        isSearchable: true,
        multiple: true,
      },
      name: 'owner',
      dataKey: 'vessel.owners',
      queryKey: 'seafarer_experience.owner_name',
      requiredQueryKey: { key: 'past_years', value: 10 },
      apiQueryValueSelector: (value, data) => {
        return value?.map((v) => data.find((d) => d.id === v)?.value) ?? [];
      },
      inputType: 'array_number',
      className: 'input-col',
      global: true,
      seperator: '|',
      hidden: isClubbedOrUnclubbed,
    },
    {
      type: 'drop_down',
      props: {
        placeholder: 'Select Previous Vessel Category',
        isSearchable: true,
      },
      name: 'vessel_category',
      dataKey: 'vessel.vesselTypes',
      queryKey: 'target_vessel_type.value',
      requiredQueryKey: { key: 'past_years', value: 10 },
      apiQueryValueSelector: (value, data) => data.find((d) => d.id === value)?.value,
      inputType: 'string',
      className: 'input-col',
      global: true,
      hidden: isClubbedOrUnclubbed,
      // seperator: '|',
    },
    {
      type: 'date_range',
      props: {
        placeholderText: 'Date Range',
        dateFormat: 'd MMM yyyy',
        isClearable: true,
      },
      name: 'due_off_date',
      dataKey: 'tech_group.tech_group',
      queryKey: 'seafarer_contact_log.availability_date',
      inputType: 'array_date',
      dateFormat: 'YYYY-MM-DD',
      apiQueryValueSelector: (value) =>
        value.map((v) => (v ? moment(v).format('YYYY-MM-DD') : null)),
      className: 'input-col',
      defaultValueOnLoad: `${moment().subtract(3, 'months').format('YYYY-MM-DD')},
      ${moment().add(2, 'months').format('YYYY-MM-DD')}`,
    },
    {
      name: 'crew_planning_status',
      shouldNotRenderInFilters: true,
      queryKey: 'crew_planning_status',
      inputType: 'array_string',
      className: 'input-col',
      global: true,
    },
  ] as FilterConfig[];
const coldStartContent = (
  <div
    className="table sticky-table table-responsive crew-planner-table table- crew-planner-initial-page"
    style={{ height: 300 }}
  >
    <div>
      <Icon icon="crew-invert" size={100} />
    </div>
    <p>Select a On-Leave Seafarer to view recommendation for relieving Onboarding Seafarer</p>
  </div>
);
const getName = (status) => {
  if ([STATUS.clubbed, STATUS.club_confirmed].includes(status)) {
    return 'Clubbed Seafarers due for Onboarding';
  }
  if ([STATUS.unclubbed].includes(status)) {
    return 'Unclubbed Seafarers due for Onboarding';
  }
  return '1. Select the Seafarer to be the Reliever';
};
const AvailableSeafarer = React.memo(
  ({
    selectedSeafarer,
    relieverSectionData,
    fetchRelieverData,
    handlePaginationOfReliever,
    handleSelectSeafarerToRelieve,
    reliverSectionLoading,
    selectedSeafarerToBeReliever,
    setSelectedSeafarerToBeReliever,
    relieverSectionPagination,
    filterData,
    setApiQuery,
    selectedFilters,
    onFilterChange,
    onCrewPlanningFilterChange,
    eventTracker,
    handleAction,
    handleRemark,
    tabName,
    selectedRanks,
    fetchData,
    hasMoreData,
    loading,
    data,
    seafarersTotalCount,
    handleSearch,
    KPI,
    setIsAPIQueryInitialized,
    searchNotClicked,
    hasEditAccess,
  }) => {
    const { crewPlanningStatus, isClubbedOrUnclubbed } = getIsClubbedOrUnclubbed(selectedFilters);
    const vesselType = filterData?.vessel?.vesselTypes?.find(
      (v) => v.id === selectedFilters?.vessel_category,
    )?.value;
    const availableSeafarercolumns = getAvailableSeafarersColumns({
      eventTracker,
      handleAction,
      handleRemark,
      handleSelectedSeafarer: handleSelectSeafarerToRelieve,
      selectedSeafarer,
      selectedFilters,
      activeKey: tabName,
      vesselType,
      hasEditAccess,
    }).filter((c) => !['Average Score', 'Document Score']?.includes(c?.Header));
    const seafarerTobeRelieveColumns = getSeafarerToRelieveColumns({
      eventTracker,
      handleRemark,
      handleSelectedSeafarer: setSelectedSeafarerToBeReliever,
      selectedSeafarer: selectedSeafarerToBeReliever,
      activeKey: tabName,
      hasEditAccess,
    });

    const showSecondTable = ![STATUS.club_confirmed, STATUS.clubbed, STATUS.unclubbed].includes(
      crewPlanningStatus,
    );
    return (
      <Container>
        <DynamicFilter
          filters={getAvailableSeafarerFilter(selectedRanks, isClubbedOrUnclubbed)}
          data={filterData}
          setApiQuery={setApiQuery}
          selectedFilters={selectedFilters}
          onFilterChange={onFilterChange}
          selectedRanks={selectedRanks}
          eventTracker={eventTracker}
          handleSearch={handleSearch}
          setIsAPIQueryInitialized={setIsAPIQueryInitialized}
        />
        {KPI}
        {!selectedFilters?.rank?.length && (
          <div className="crew-planner-initial-page">
            <div>
              <Icon icon="crew-invert" size={100} />
            </div>
            <p>
              To plan Seafarers to Relief, start by <br />
              {' '}
              selecting the Rank
            </p>
          </div>
        )}
        {selectedFilters?.rank?.length && (
          <div className="crew-planner-table">
            <div className="group-button-selector-wrapper mb-2">
              <p className="m-0">{getName(crewPlanningStatus)}</p>
              <GroupButtonSelector
                buttonLabels={Object.keys(CREW_PLANNING_STATUS_MAPPING)}
                onSelect={onCrewPlanningFilterChange}
                selected={capitalizeFirstLetter(
                  selectedFilters?.crew_planning_status?.[0]?.split(',')?.[0],
                )}
                count={seafarersTotalCount}
              />
            </div>
            <InfinteScrollTable
              noDataText={
                <>
                  Please click Search with the selected filters.
                  <br />
                  If there are no result, please update the filters and click Search again.
                </>
              }
              data={data}
              loading={loading}
              fetchData={fetchData}
              hasMoreData={hasMoreData}
              columns={availableSeafarercolumns}
              height={300}
            />
            {showSecondTable ? (
              <>
                <p className="mb-0">2. Select Seafarer to Relieve</p>
                {!!selectedSeafarer && (
                  <InfinteScrollTable
                    init_sort={relieverSectionPagination.sortBy}
                    loading={reliverSectionLoading}
                    fetchData={handlePaginationOfReliever}
                    data={relieverSectionData.data ?? []}
                    columns={seafarerTobeRelieveColumns}
                    height={300}
                    hasMoreData={
                      relieverSectionData.total
                        ? relieverSectionData.data.length < relieverSectionData.total
                        : true
                    }
                  />
                )}
                {!selectedSeafarer && coldStartContent}
              </>
            ) : null}
          </div>
        )}
      </Container>
    );
  },
);

export default AvailableSeafarer;
