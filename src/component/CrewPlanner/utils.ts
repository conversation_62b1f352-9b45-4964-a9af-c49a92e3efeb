import { CREW_PLANNING_STATUS, CREW_PLANNING_TYPE, STATUS } from '@src/constants/crewPlanner';
import { UIPlanningStatus, StatusColor } from '@src/constants/status';
import { seafarerStatus } from '@src/model/constants';
import { checkAdditionalRequest, checkMissingPersonnel } from '@src/pages/CrewPlanner/utils';
import moment from 'moment-timezone';
import { capitalizeArgs } from '../../model/utils';
import { UserInfo } from '@src/types/keycloakUser';

/*
    helper file for common logics
*/

// constants
export const URGENT_PLANNING_REQUIRED_DAYS = 30;

export const getRank = (seafarer: any) => {
  if (checkMissingPersonnel(seafarer)) return seafarer?.rank;
  if (checkAdditionalRequest(seafarer)) return seafarer?.rank?.value;
  return (
    seafarer?.seafarer_rank?.value ??
    seafarer?.seafarer_person?.seafarer_status_history?.[0]?.seafarer_rank.value
  );
};

export const getFullName = (seafarer: any) => {
  const { seafarer_person: person } = seafarer || {};
  const firstName = person?.first_name ?? '';
  const lastName = person?.last_name ?? '';
  const middleName = person?.middle_name ?? '';
  return capitalizeArgs(firstName, middleName, lastName) || null;
};

export function getDueDate(seafarer: any, auditLog: any) {
  const expectedContractEndDate = seafarer?.seafarer_person?.seafarer_status_history?.[0]?.expected_contract_end_date;

  let seafarerDueOffDateFromAuditLog: string | null = null;

  // need to loop the audit log
  // because when status = 6, due off date is not present in db
  seafarerDueOffDateFromAuditLog = auditLog?.map((item: any) => item?.seafarer_due_off_date) // Extract the name property
    .find((seafarer_due_off_date: null | string) => seafarer_due_off_date !== null); // Find the first non-null value

  return expectedContractEndDate ?? seafarerDueOffDateFromAuditLog;
}

export function getAvailbilityDate(seafarer: any, auditLog: any) {
  const relieverAvailableDate = seafarer?.seafarer_contact_log?.[0]?.availability_date;

  let relieverAvailableDateFromAuditLog: string | null = null;

  // need to loop the audit log
  // because when status = 6, reliever_availability_date date is not present in db
  relieverAvailableDateFromAuditLog = auditLog?.map((item: any) => item?.reliever_availability_date) // Extract the name property
    .find((reliever_availability_date: null | string) => reliever_availability_date !== null); // Find the first non-null value

  return relieverAvailableDate ?? relieverAvailableDateFromAuditLog;
}

export const getPendingTooltipStatus = (reliever: any) => {
  const latestStatus =
    reliever?.seafarer_person?.seafarer_status_history?.[0]?.seafarer_journey_status;
  switch (latestStatus) {
    case 'recommended':
      return 'Recommended';
    case 'recommended_with_deviation':
      return 'Recommend with Deviation';
    case 'crew_assignment_rejected':
      return 'Recommendation Rejected';
    default:
      return 'Not Recommended';
  }
};

interface GetStatusArgs {
  toBeReplacedSeafarer: any;
  reliever: any;
  relieverStatus: any;
}

export function getDayDiffFromToday(inputDate: moment.MomentInput | null) {
  const today = moment();
  const inputDateObj = moment(inputDate);
  if (!inputDate || !inputDateObj) {
    return -1;
  }

  return inputDateObj.diff(today, 'days');
}

export const getStatusColorForPODManager = ({
  reliever,
  toBeReplacedSeafarer,
  relieverStatus,
}: GetStatusArgs): StatusColor => {
  const dueDate = getDueDate(toBeReplacedSeafarer, toBeReplacedSeafarer?.crew_planning?.crew_planning_audit_log);
  const planningStatus = toBeReplacedSeafarer?.crew_planning?.planning_status;
  const daysDiffFromToday = getDayDiffFromToday(dueDate);
  const isUrgent = daysDiffFromToday < URGENT_PLANNING_REQUIRED_DAYS;
  const isRelieverTravelling =
    reliever?.seafarer_person?.current_journey_status === seafarerStatus.TRAVELLING;
  const isRelieverSignedOn =
    reliever?.seafarer_person?.current_journey_status === seafarerStatus.SIGNED_ON;

  // Travelling or Signed-on status overrides planning status
  if (isRelieverSignedOn || isRelieverTravelling) {
    return StatusColor.Green;
  }

  // Urgent planning required and no reliever assigned
  if (isUrgent && !reliever) {
    return StatusColor.Red;
  }

  let statusColor = StatusColor.Orange;
  // More than 30 days away, status is blue
  if (!isUrgent) {
    statusColor = StatusColor.Blue;
  }

  // Reliever  unclubbed, or clubbed but not confirmed yet
  if (
    reliever &&
    [CREW_PLANNING_STATUS.clubbed, CREW_PLANNING_STATUS.unclubbed].includes(planningStatus)
  ) {
    statusColor = StatusColor.Orange;
  }

  // Reliever is fully ready and planning status is confirmed
  if (reliever && planningStatus === CREW_PLANNING_STATUS.club_confirmed) {
    statusColor = StatusColor.Green;
  }

  return statusColor;
};

/*
  Common Function to get plan status for UI
*/
interface GetPlanStatusArgs {
  reliever: any;
  toBeReplacedSeafarer: any;
  relieverStatus: any;
  planningStatus: number;
  isHistoryModal: boolean;
}

export function getUIPlanningStatusForPODManager({
  reliever,
  toBeReplacedSeafarer,
  relieverStatus,
  planningStatus,
  isHistoryModal,
}: GetPlanStatusArgs): UIPlanningStatus | undefined {
  const isRelieverTravelling =
    reliever?.seafarer_person?.current_journey_status === seafarerStatus.TRAVELLING;
  const isRelieverSignedOn =
    reliever?.seafarer_person?.current_journey_status === seafarerStatus.SIGNED_ON;
  const isRelieverReady =
    relieverStatus?.isCrewAssignmentApproved && relieverStatus?.isDocumentAndTrainingComplete;
  const isRelievedWithoutReplacement =
    toBeReplacedSeafarer?.crew_planning?.planning_type === CREW_PLANNING_TYPE.offsigner;
  // Completed status have to be the first to return
  if (planningStatus === CREW_PLANNING_STATUS.completed) {
    return UIPlanningStatus.Completed;
  }

  if (planningStatus === CREW_PLANNING_STATUS.expired) {
    return UIPlanningStatus.Expired;
  }

  // Travelling or Signed-on status overrides planning status
  // This logic have to be the first to return
  if (isRelieverSignedOn || isRelieverTravelling) {
    return UIPlanningStatus.Travelling;
  }

  if (!isHistoryModal && !reliever && isRelievedWithoutReplacement) {
    return UIPlanningStatus.Cancel_Relieve_Without_Replacement;
  }
  // Check if replacement is needed
  if (!isHistoryModal && !reliever && !isRelievedWithoutReplacement) {
    return UIPlanningStatus.Request_Replacement;
  }

  // Check if the reliever is not fully ready
  if (!isRelieverReady) {
    return UIPlanningStatus.Pending;
  }

  if (isRelieverReady && planningStatus === CREW_PLANNING_STATUS.clubbed) {
    return UIPlanningStatus.ReadyToConfirm;
  }

  if (isHistoryModal && planningStatus === CREW_PLANNING_STATUS.clubbed) {
    return UIPlanningStatus.ReadyToConfirm;
  }

  if (planningStatus === CREW_PLANNING_STATUS.club_confirmed) {
    return UIPlanningStatus.Confirmed;
  }
}

export const getIsClubbedOrUnclubbed = (selectedFilters) => {
  const crewPlanningStatus = selectedFilters?.crew_planning_status?.[0]?.split(',')?.[0];
  return {
    crewPlanningStatus,
    isClubbedOrUnclubbed: [STATUS.club_confirmed, STATUS.clubbed, STATUS.unclubbed].includes(
      crewPlanningStatus,
    ),
  };
};

export const getBadgeColor = (value: string, status: string) => {
  const diff = Math.ceil(moment(value).diff(moment(), 'days', true));
  let daysFromNow;

  if (diff === 0) {
    daysFromNow = diff;
  } else if (diff < 0) {
    daysFromNow = '+' + Math.abs(diff);
  } else {
    daysFromNow = '-' + diff;
  }

  if (!value) return { diff };
  let colorClass;
  const now = moment();
  const dateOfExpiry = moment(value);
  if (status === STATUS.clubbed || status === STATUS.unclubbed || status === STATUS.club_confirmed)
    colorClass = 'green';
  else if (dateOfExpiry.isBefore(now)) {
    colorClass = 'red';
  } else if (dateOfExpiry.isAfter(moment().add(30, 'days'))) {
    colorClass = 'blue';
  } else {
    colorClass = 'orange';
  }

  return { colorClass, diff: daysFromNow };
};
interface ReplacementEmailContentParams {
  rank: string;
  vesselName: string;
  isMissingRank?: boolean;
  isAdditionalCrew?: boolean;
  user: UserInfo;
  recipients: string[];
}
export const generateReplacementEmailContent = ({
  rank,
  vesselName,
  isMissingRank,
  isAdditionalCrew,
  user,
  recipients,
}: ReplacementEmailContentParams) => {
  const recipientsNames = recipients?.join(', ') ?? '[Select Recipients]';
  const currentUserFullName = `${user.firstName} ${user.lastName}`;
  const messageMissingRank = `
Dear ${recipientsNames},

I have noticed that seafarer ${rank} is missing at ${vesselName}.

Can you look for a replacement?

Feel free to reach out if you have any questions or need assistance.

Thank you.

Best regards,

${currentUserFullName}`;

  const messageAdditionalCrew = `
Dear ${recipientsNames},

I have noticed that we require additional ${rank} at ${vesselName}

Can you look for a candidate?

Feel free to reach out if you have any questions or need assistance.

Thank you.

Best regards,

${currentUserFullName}
 `;

  const signedOnMessage = `
Dear ${recipientsNames},

There is a need for replacement for ${rank} at ${vesselName}

Can you look for a replacement?

Feel free to reach out if you have any questions or need assistance.

Thank you.

Best regards,

${currentUserFullName}
 `;
  if (isMissingRank) {
    return messageMissingRank;
  }
  if (isAdditionalCrew) {
    return messageAdditionalCrew;
  }
  return signedOnMessage;
};
