import React, { ReactNode, useMemo } from 'react';
import { get } from 'lodash';
import classNames from 'classnames';
import { Dash, dateAsString } from '@src/model/utils';
import moment from 'moment-timezone';
import { Col, OverlayTrigger, Row, Tooltip } from 'react-bootstrap';
import Avatar from '@src/component/common/Avatar';
import { Icon } from '@src/styleGuide';
import { STATUS_COLORS } from '@src/model/constants';
import { displayExp } from '../CrewManagement/CrewPlannerDetail/columns-map';
import { getScoreDifferenceColor } from '../../pages/CrewPlanner/utils';

const renderValidity = (date: string) => {
  if (!date) return Dash;
  const dateStr = dateAsString(date);
  const isExpired = moment(date).isBefore(moment());
  return (
    <div className="status-indicator">
      <span
        className={classNames('dot ml-0', {
          'dot-green': !isExpired,
          'dot-red': isExpired,
        })}
      ></span>
      <span>Till {dateStr}</span>
    </div>
  );
};

export const getSeamanBook = (books: any[]) => {
  const filteredNationalBook = books?.filter((book) => book?.is_original);
  if (filteredNationalBook?.length) return filteredNationalBook;
  // its always sorted by expiry desc from the backend
  return [books?.[0]];
};

export const getDetailColumns = (crew: any, isReplacer: boolean, vesselType: string, currentRank: string): any[] => {
  const rankOcimf = crew?.ocimf?.find((o) => o?.group_by_name === 'rank' && o?.group_value === currentRank)?.years;
  const typeOcimf = crew?.ocimf?.find(
    (o) => o?.group_by_name === 'vessel_type' && o?.group_value === vesselType,
  )?.years;
  const companyOcimf = crew?.ocimf?.find((o) => o?.group_by_name === 'company')?.years;

  const columns = [
    {
      title: 'Rank Experience',
      order: 1,
      value: displayExp(
        rankOcimf,
        crew?.experience_summary?.duration_in_target_rank_years,
        crew?.experience_summary?.duration_in_target_rank,
      ),
    },
    {
      title: 'Exp. in Vessel Category',
      order: 2,
      value: displayExp(
        typeOcimf,
        crew?.experience_summary?.duration_on_target_vessel_type_years,
        crew?.experience_summary?.duration_on_target_vessel_type,
      ),
    },
    {
      title: 'Years in Company',
      order: 3,
      value: displayExp(
        companyOcimf,
        crew?.experience_summary?.duration_with_company_years,
        crew?.experience_summary?.duration_with_company,
      ),
    },
    {
      title: isReplacer ? 'Sign Off Date' : 'Joining Date',
      order: 5,
      value:
        dateAsString(
          get(
            crew,
            isReplacer
              ? 'latest_experience.end_date'
              : 'seafarer_person.seafarer_status_history[0].status_date',
          ),
        ) ?? Dash,
    },
    {
      title: 'Reporting Office',
      order: 10,
      value: get(crew, 'seafarer_reporting_office.value', Dash),
    },
    {
      title: 'Training Requirement',
      order: 6,
      value: 'Coming soon',
    },
    {
      title: 'Passport Validity',
      order: 8,
      value: renderValidity(crew?.seafarer_person?.passports?.[0]?.date_of_expiry),
    },
    {
      title: "Seaman's Book Validity",
      order: 9,
      value: renderValidity(
        getSeamanBook(crew?.seafarer_person?.seaman_books)?.[0]?.date_of_expiry,
      ),
    },
  ];
  return columns;
};

interface SeafarerProfileCardProps {
  firstName: string;
  middleName: string;
  lastName: string;
  metadata: string;
  id?: number;
}

interface CrewPlannerCrewDetailProps {
  crew: any;
  isSignedOnCrew?: boolean;
  isOnBoardingWith?: boolean;
  experienceScore?: string | null;
  vesselType?: string;
}

export interface DetailMeta {
  title: string;
  order?: number;
  value: string | ReactNode;
}

const getRank = (seafarer: any) => {
  return (
    seafarer?.seafarer_person?.seafarer_status_history?.[0]?.seafarer_rank?.value ??
    seafarer?.seafarer_rank?.value ??
    seafarer?.rank?.value ??
    seafarer.rank
  );
};

const getRespectiveDate = (crew: any, isSignedOnCrew = false) => {
  return isSignedOnCrew
    ? crew?.seafarer_person?.seafarer_status_history?.[0]?.expected_contract_end_date
    : crew?.seafarer_contact_log[0]?.availability_date;
};

function getDateDifferenceColor(variableDate: string) {
  const today = moment();
  const targetDate = moment(variableDate);
  const diffInDays = targetDate.diff(today, 'days');

  if (diffInDays <= 30) {
    return STATUS_COLORS.RED;
  } else if (diffInDays <= 60) {
    return STATUS_COLORS.ORANGE;
  } else if (diffInDays <= 90) {
    return STATUS_COLORS.GREEN;
  } else {
    return STATUS_COLORS.BLUE;
  }
}

const SeafarerProfileCard = ({
  firstName,
  middleName,
  lastName,
  metadata,
  id,
}: SeafarerProfileCardProps) => {
  return (
    <div className="seafarer-profile-card-crew-recommendation">
      <Avatar firstName={firstName} lastName={lastName} emptyText="?" />
      <div
        className="seafarer-recommend-modal-name-wrapper"
        onKeyDown={(event) => {
          if (event.key === 'Enter' || event.key === ' ') {
            window.open(`/seafarer/details/${id}/general`, '_blank');
          }
        }}
        onClick={() => window.open(`/seafarer/details/${id}/general`, '_blank')}
      >
        {firstName} {middleName} {lastName}
      </div>
      <b className={classNames('text-center', { 'mt-2': !firstName })}>{metadata ?? '---'}</b>
    </div>
  );
};
export const CrewPlannerCrewDetail = ({
  crew,
  isSignedOnCrew,
  isOnBoardingWith,
  experienceScore,
  vesselType,
}: CrewPlannerCrewDetailProps) => {
  const { seafarer_person, crew_planning_remarks } = crew;
  const seafarerDetailList = useMemo(() => {
    return getDetailColumns(crew, !isOnBoardingWith && !isSignedOnCrew, vesselType, getRank(crew));
  }, [crew]);

  const remark = crew_planning_remarks?.[0]?.remarks;
  const isMissingPersonalOrAdditionalCrew = crew.isMissingPersonnel || crew.isAdditionalRequest;
  return (
    <div
      className={`crew-recommend-modal-seafarer-details-wrapper ${
        isSignedOnCrew ? 'red-border' : ''
      }`}
    >
      <div
        className={classNames('d-flex align-items-center mb-3', {
          'justify-content-between': !isMissingPersonalOrAdditionalCrew,
        })}
      >
        <div className="seafarer-profile-card-crew-recommendation-wrapper">
          <SeafarerProfileCard
            firstName={seafarer_person?.first_name}
            middleName={seafarer_person?.middle_name}
            lastName={seafarer_person?.last_name}
            metadata={getRank(crew)}
            id={crew?.id}
          />
        </div>
        <div className={classNames({ 'd-grid': !isMissingPersonalOrAdditionalCrew })}>
          <div style={{ display: 'flex' }}>
            {!isOnBoardingWith && (
              <div
                className={`planning-relieve-date-bg ml-4 mr-2 my-1 px-1 mb-4 ${getDateDifferenceColor(
                  getRespectiveDate(crew, isSignedOnCrew),
                )}-box-planning-relieve`}
              >
                {`${
                  isSignedOnCrew
                    ? 'Planning to Relieve on: ' +
                      moment(getRespectiveDate(crew, isSignedOnCrew)).format('DD MMM YYYY')
                    : 'Availability Date: ' + moment(getRespectiveDate(crew)).format('DD MMM YYYY')
                }`}
              </div>
            )}
            {isOnBoardingWith && (
              <div
                className={`planning-relieve-date-bg ml-4 mr-2 my-1 px-1 mb-4 ${getScoreDifferenceColor(
                  experienceScore,
                )}`}
              >
                {`Joint Experience Score: ${experienceScore}`}
              </div>
            )}
            {!isMissingPersonalOrAdditionalCrew && (
              <OverlayTrigger
                placement={'top'}
                overlay={<Tooltip id={'tooltip-top'}>{remark ?? '---'}</Tooltip>}
              >
                <span className="d-inline-block alert-icon-crew-recommendation-modal">
                  <Icon icon="alert" size={15} />
                </span>
              </OverlayTrigger>
            )}
          </div>
          {!isMissingPersonalOrAdditionalCrew && (
            <Row className="mx-4" md={4}>
              {seafarerDetailList?.map((item, index) => {
                return (
                  <Col key={item.title} className={`p-0 ${index > 4 ? 'mb-0' : 'mb-3'}`}>
                    <div className="font-weight-bold">{item.title}</div>
                    <div>{item.value}</div>
                  </Col>
                );
              })}
            </Row>
          )}
        </div>
      </div>
    </div>
  );
};
