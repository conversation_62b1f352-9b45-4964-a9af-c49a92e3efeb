import moment from 'moment-timezone';
import React, { useEffect } from 'react';
import { But<PERSON>, Modal } from 'react-bootstrap';
import crewPlannerService from '@src/service/crew-planner';
import vesselService from '@src/service/vessel-service';
import { useCrewMangementContext } from '@src/pages/CrewManagementPage';
import { keyBy } from 'lodash';
import Table from '../common/Paris2Table';
import {
  getAvailbilityDate,
  getDueDate,
  getFullName,
  getUIPlanningStatusForPODManager,
} from './utils';
import { UIPlanningStatus } from '../../constants/status';
import { CREW_PLANNING_STATUS } from '../../constants/crewPlanner';

const DDMMMYYYY = 'DD MMM YYYY';

const YYYYMMDD = 'YYYY-MM-DD';

const ClubbingHistory = ({ show, handleClose }) => {
  const [clubbingHistory, setClubbingHistory] = React.useState([]);
  const [loader, setLoader] = React.useState<boolean>(false);
  const { vesselData } = useCrewMangementContext();
  const vesselId = vesselData?.vessel?.id;
  const vesselOwnershipId = vesselData?.id;
  const getClubbingHistory = async () => {
    try {
      setLoader(true);
      const fromDate = moment().subtract(1, 'year').format(YYYYMMDD);
      const toDate = moment().add(1, 'year').format(YYYYMMDD);
      const today = moment().endOf('day').format('YYYY-MM-DD HH:mm:ss.SSS');
      const [simulatedItinerary, vesselItinerary, crewPlanning] = await Promise.all([
        crewPlannerService.getSimulateItinerary(vesselId, `${fromDate},${toDate}`),
        vesselService.getVesselItinerary(vesselOwnershipId, `${fromDate},${toDate}`),
        crewPlannerService.queryCrewPlan([], [], [vesselOwnershipId], fromDate, today, [
          CREW_PLANNING_STATUS.clubbed,
          CREW_PLANNING_STATUS.club_confirmed,
          CREW_PLANNING_STATUS.completed,
          CREW_PLANNING_STATUS.expired,
        ]),
      ]);
      const vesselItineraryData = vesselItinerary?.data?.results ?? [];
      const simulatedItineraryData = simulatedItinerary?.data ?? [];

      const itineraryById = keyBy(vesselItineraryData, 'id');
      const simulatedItineraryById = keyBy(simulatedItineraryData, 'id');
      const clubbingHistoryData = crewPlanning.map((crew) => {
        const iti = crew.itinerary_id
          ? itineraryById[crew.itinerary_id]
          : simulatedItineraryById[crew.simulate_itinerary_id];
        return {
          id: crew.id,
          date_of_crew_change: iti?.estimated_arrival ?? iti?.date,
          port: iti?.port,
          rank: crew.seafarer?.seafarer_rank?.value,
          onboard_seafarer_hkid: crew.seafarer?.hkid,
          onboard_seafarer_id: crew.seafarer?.id,
          onboard_seafarer_name: getFullName(crew.seafarer),
          due_off_date: getDueDate(crew.seafarer, crew?.crew_planning_audit_log),
          replace_seafarer_hkid: crew.reliever?.hkid,
          replace_seafarer_id: crew.reliever?.id,
          replace_seafarer_name: crew.reliever && getFullName(crew.reliever),
          available_date: crew.reliever && getAvailbilityDate(crew.reliever, crew?.crew_planning_audit_log),
          seafarer: crew.seafarer,
          reliever: crew.reliever,
          planningStatus: crew.planning_status,
        };
      });
      clubbingHistoryData.sort((a, b) => new Date(b.date_of_crew_change) - new Date(a.date_of_crew_change));
      setClubbingHistory(clubbingHistoryData);
    } catch (e) {
      console.log('Error in getClubbingHistory', e);
    } finally {
      setLoader(false);
    }
  };

  useEffect(() => {
    if (show && vesselId && vesselOwnershipId) getClubbingHistory();
  }, [show, vesselId, vesselOwnershipId]);

  const displaySeafarerName = (id, name) => {
    return (
      <a href={`/seafarer/details/${id}/general`} target="_blank" rel="noreferrer">
        {name ?? '---'}
      </a>
    );
  };

  const formatStatus = (
    reliever: any,
    toBeReplacedSeafarer: any,
    relieverStatus: any,
    planningStatus: any,
  ) => {
    const uiPlanStatus = getUIPlanningStatusForPODManager({
      reliever,
      toBeReplacedSeafarer,
      relieverStatus,
      planningStatus,
      isHistoryModal: true,
    });

    if (uiPlanStatus === UIPlanningStatus.Completed) {
      return <div className="seafarer-card-success-status">Completed</div>;
    }
    if (uiPlanStatus === UIPlanningStatus.Expired) {
      return <div className="seafarer-card-expired-status">Expired</div>;
    }
    if (
      uiPlanStatus === UIPlanningStatus.Pending ||
      uiPlanStatus === UIPlanningStatus.ReadyToConfirm
    ) {
      return <div className="seafarer-card-pending-status">Pending</div>;
    }
    if (uiPlanStatus === UIPlanningStatus.Travelling) {
      return <div className="seafarer-card-success-status">Travelling</div>;
    }
    if (uiPlanStatus === UIPlanningStatus.Confirmed) {
      return <div className="seafarer-card-success-status">Confirmed</div>;
    }
    return '---';
  };

  const columns = [
    {
      Header: 'Date of Crew Change',
      id: 'date_of_crew_change',
      accessor: (row) => {
        return row.date_of_crew_change
          ? moment(row.date_of_crew_change).format(DDMMMYYYY)
          : '---';
      },
      disableSortBy: false,

      width: 200,
    },
    {
      Header: 'Port',
      id: 'port',
      accessor: (row) => {
        return row.port ?? '---';
      },
      disableSortBy: true,
      width: 184,
    },
    {
      Header: 'Rank',
      id: 'rank',
      accessor: (row) => {
        return row.rank ?? '---';
      },
      disableSortBy: true,
      width: 120,
    },
    {
      Header: 'Onboard Seafarer HKID',
      id: 'onboard_seafarer_hkid',
      accessor: (row) => {
        return row.onboard_seafarer_hkid ?? '---';
      },
      disableSortBy: true,
      width: 200,
    },
    {
      Header: 'Onboard Seafarer',
      id: 'onboard_seafarer',
      accessor: (row) => {
        return displaySeafarerName(row?.onboard_seafarer_id, row?.onboard_seafarer_name);
      },
      disableSortBy: true,
      width: 200,
    },
    {
      Header: 'Due Off Date',
      id: 'due_off_date',
      accessor: (row) => {
        return row.due_off_date ? moment(row.due_off_date).format(DDMMMYYYY) : '---';
      },
      disableSortBy: true,
      width: 200,
    },
    {
      Header: 'Reliever Seafarer HKID',
      id: 'replace_seafarer_hkid',
      accessor: (row) => {
        return row.replace_seafarer_hkid ?? '---';
      },
      disableSortBy: true,
      width: 200,
    },
    {
      Header: 'Reliever Seafarer',
      id: 'replace_seafarer',
      accessor: (row) => {
        return displaySeafarerName(row?.replace_seafarer_id, row?.replace_seafarer_name);
      },
      disableSortBy: true,
      width: 200,
    },
    {
      Header: 'Available Date',
      id: 'available_date',
      accessor: (row) => {
        return row.available_date ? moment(row.available_date).format(DDMMMYYYY) : '---';
      },
      disableSortBy: true,
      width: 100,
    },

    {
      Header: 'Status',
      id: 'status',
      accessor: (row) => {
        return formatStatus(row?.reliever, row?.seafarer, null, row?.planningStatus);
      },
      disableSortBy: true,
      width: 100,
    },
  ];

  return (
    <Modal
      size="xl"
      show={show}
      onHide={() => {
        handleClose();
      }}
    >
      <Modal.Header className="modal-header clubbing-history-title">
        {vesselData.name ?? ''} Crew Planning History
      </Modal.Header>
      <Modal.Body>
        <Table
          className="clubbing-history-table"
          autoSort
          totalCount={clubbingHistory?.length}
          loading={loader}
          columns={columns}
          data={clubbingHistory ?? []}
          pagination={false}
        />
      </Modal.Body>
      <Modal.Footer>
        <Button
          variant="primary"
          onClick={() => {
            handleClose();
          }}
        >
          Close
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default ClubbingHistory;
