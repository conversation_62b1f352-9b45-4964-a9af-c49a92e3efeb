import React from 'react';
import KpiCard from './KpiCard';
import KpiCardVesselBudget from './KpiCardVesselBudget';

interface KpiData {
  isLoading: boolean;
  number: number | null;
  title: string;
}

interface VesselBudgetKpi {
  isLoading: boolean;
  value: string | null;
  title: string;
}

interface PlannerKPIProps {
  kpiData: KpiData[] | VesselBudgetKpi[];
}

const PlannerKPI: React.FC<PlannerKPIProps> = ({ kpiData }) => {
  const isKpi = Object.keys(kpiData?.[0] ?? {}).includes('number')
  return (
    <div className={isKpi ? 'planner-kpi-container' : 'vessel-budget-kpi-container'}>
      <div className="flex-between">
        {kpiData.map((data) =>
          isKpi ? (
            <KpiCard
              key={data.title}
              isLoading={data.isLoading}
              number={(data as KpiData).number}
              title={data.title}
            />
          ) : (
            <KpiCardVesselBudget
              key={data.title}
              value={(data as VesselBudgetKpi).value}
              isLoading={data.isLoading}
              title={data.title}
            />
          ),
        )}
      </div>
    </div>
  );
};

export default PlannerKPI;
