import React, { useState, useEffect } from 'react';
import { Button, Modal } from 'react-bootstrap';
import SubmitButton from '../common/SubmitButton';

const PlannerRemarkModal = ({
  loadingConfirm,
  show,
  seafarer,
  handleCloseRemark,
  handleConfirmRemark,
  getRemarks,
  isEdit,
}) => {
  const [remarks, setRemarks] = useState(null);
  const [errorRemarks, setErrorRemarks] = useState(false);
  const [isDisableSave, setIsDisableSave] = useState(true);

  useEffect(() => {
    setRemarks(getRemarks(seafarer));
  }, [seafarer]);

  useEffect(() => {
    setIsDisableSave(remarks === getRemarks(seafarer));
  }, [remarks]);

  const validateRemark = () => {
    if (remarks?.length > 250) {
      setErrorRemarks(true);
      return;
    }
    handleConfirmRemark(seafarer, remarks, isEdit);
    setRemarks(null);
    setErrorRemarks(false);
  };

  return (
    <Modal
      show={show}
      onHide={() => {
        handleCloseRemark();
        setRemarks(null);
      }}
      centered
    >
      <Modal.Header className="modal-header">{isEdit ? 'Edit' : 'Add'} Remarks</Modal.Header>
      <Modal.Body>
        <span>You may leave any remarks below:</span>
        <form className="mt-2">
          <div className="form-group">
            <textarea
              className="form-control"
              rows={3}
              value={remarks}
              onChange={(e) => {
                setIsDisableSave(false);
                setRemarks(e.target.value);
              }}
            />
            {errorRemarks && (
              <div className="invalid-feedback d-block">Remark cannot exceed 250 characters.</div>
            )}
          </div>
        </form>
      </Modal.Body>
      <Modal.Footer>
        <Button
          variant="primary"
          onClick={() => {
            handleCloseRemark();
            setRemarks(null);
          }}
        >
          Cancel
        </Button>
        <SubmitButton
          isLoading={loadingConfirm}
          variant="secondary"
          onClick={() => {
            validateRemark();
          }}
          disabled={isDisableSave || loadingConfirm}
        >
          Save
        </SubmitButton>
      </Modal.Footer>
    </Modal>
  );
};

export default PlannerRemarkModal;
