import { get, isEmpty } from 'lodash';

export const isEmptyObject = (obj) =>
  Object.keys(obj ?? {}).length === 0 && obj.constructor === Object;
export const generateFilterObjectFromApiQuery = (apiQuery, filters) => {
  const urlParams = new URLSearchParams(apiQuery);
  const urlFilterValues = {};
  filters.forEach((filter) => {
    let value = urlParams.get(filter.queryKey);
    let finalValue;
    if (!value && filter.defaultValueOnLoad) {
      value = filter.defaultValueOnLoad;
    }
    if (value !== null) {
      if (filter.inputType?.includes('array')) {
        finalValue = value
          .split(',')
          .filter(Boolean)
          .map((v) => {
            if (filter.inputType === 'array_number') {
              return parseInt(v, 10);
            }
            return v;
          });
      } else if (filter.inputType === 'string') {
        finalValue = value;
      }
    }

    if (finalValue) urlFilterValues[filter.name] = finalValue;
  });
  return urlFilterValues;
};
export const generateApiQueryFromFilterObject = (filterValues, filters) => {
  let searchParams = '';
  Object.entries(filterValues).forEach(([key, v]) => {
    const value = v;
    const filter = filters.find((filter) => filter.name === key);
    if (!filter || !value) return;
    if (filter.inputType?.includes('array')) {
      const result = value.join(',');
      searchParams += result ? `&${filter.queryKey}=${result}` : '';
    } else if (filter.inputType === 'string' && typeof value === 'string') {
      searchParams += `&${filter.queryKey}=${value}`;
    }
  });
  return searchParams;
};
export const processApiQuery = ({ lookupData, filters, values, onGlobal }) => {
  const searchParams = new URLSearchParams('');
  Object.entries(values).forEach(([key, v]) => {
    let value = v;
    const filter = filters.find((filter) => filter.name === key);
    if (!filter || filter.hidden) return;
    if (onGlobal && !filter.global) return;
    if (typeof filter.apiQueryValueSelector === 'function') {
      value = filter.apiQueryValueSelector(value, get(lookupData, filter.dataKey, []));
    }
    if (isEmpty(value)) return;

    if (filter.inputType?.includes('array')) {
      value = value.join(filter.seperator ?? ',');
    }
    if (filter.requiredQueryKey) {
      searchParams.append(filter.requiredQueryKey.key, filter.requiredQueryKey.value);
    }
    searchParams.append(filter.queryKey, value);
  });
  return `&${searchParams.toString()}`;
};

export const cleanUpFilter = (filter) => {
  const cleanedFilter = { ...filter };
  Object.keys(cleanedFilter).forEach((key) => {
    if (
      !cleanedFilter[key] ||
      (Array.isArray(cleanedFilter[key]) && !cleanedFilter[key].filter(Boolean).length)
    ) {
      delete cleanedFilter[key];
    }
    if (key === 'rank') {
      cleanedFilter[key] = cleanedFilter[key]?.filter(Boolean);
    }
  });
  return cleanedFilter;
};

export const getFilterValuesFromIds = (lookupData, name: string, val: any[]) => {
  if (!Array.isArray(val)) {
    val = [val];
  }
  switch (name) {
    case 'rank':
      return lookupData?.seafarer?.ranks?.filter((rank) => val.includes(rank.id))?.map((rank) => rank.value);
    case 'owner':
      return lookupData?.vessel?.owners?.filter((owner) => val.includes(owner.id))?.map((owner) => owner.value);
    case 'vessel_category':
      return lookupData?.vessel?.vesselTypes?.filter((category) => val.includes(category.id))?.map((category) => category.value);
    case 'tech_group':
      return lookupData?.tech_group?.tech_group?.filter((group) => val.includes(group.id))?.map((group) => group.value);
    default:
      return val;
  }
};
