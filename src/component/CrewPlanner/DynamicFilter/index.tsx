// src/components/DynamicFilter.js
import React, { useState, useEffect, useRef } from 'react';
import { Col, Row, Button } from 'react-bootstrap';
import { get, isEqual } from 'lodash';
import { useDebouncedCallback } from 'use-debounce';
import cx from 'classnames';
import ReactDatePicker from 'react-datepicker';
import moment from 'moment-timezone';
import 'react-datepicker/dist/react-datepicker.css';
import { Icon } from '@src/styleGuide';
import { useHistory, useLocation } from 'react-router-dom';
import TypeAbleDropDownSearchControl from '@src/component/advanced_search/TypeAbleDropDownSearchControl';
import SearchInput from '@src/component/common/Form/SearchInput';
import DropDownSearchControl from '../../AddSeafarer/DropDownSearchControl';
import {
  cleanUpFilter,
  generateApiQueryFromFilterObject,
  generateFilterObjectFromApiQuery,
  isEmptyObject,
  processApiQuery,
  getFilterValuesFromIds,
} from './helper';
import './style.scss';

type FilterType = 'search' | 'drop_down' | 'date_range';

interface FilterProps {
  placeholder?: string;
  isSearchable?: boolean;
  multiple?: boolean;
  placeholderText?: string;
  dateFormat?: string;
  isClearable?: boolean;
}

export interface FilterConfig {
  type?: FilterType;
  debounce?: boolean;
  props?: FilterProps;
  name: string;
  queryKey: string;
  className: string;
  inputType: 'string' | 'array_number' | 'number' | 'array_date' | 'array_string';
  dataKey?: string;
  apiQueryValueSelector?: (value: any, data?: any[]) => any;
  global?: boolean;
  seperator?: string;
  defaultValueOnLoad?: string;
  shouldNotRenderInFilters?: boolean;
  hidden?: boolean;
}
export const RenderFilter = ({
  selectedValue,
  type,
  debounce,
  onInputChange,
  dropDownData,
  dateFormat,
  props,
  name,
}) => {
  const [value, setValue] = useState(selectedValue);
  useEffect(() => {
    if (!isEqual(value, selectedValue)) {
      setValue(selectedValue);
    }
  }, [selectedValue]);
  const onDebounceInputChange = useDebouncedCallback(onInputChange, 1000);
  useEffect(() => {
    if (debounce) onDebounceInputChange(name, value);
    else onInputChange(name, value);
  }, [value]);
  const ref = useRef(null);

  if (type === 'drop_down') {
    return props.isSearchable ? (
      <span className="subtype-field">
        <TypeAbleDropDownSearchControl
          onInputChange={(e) => setValue(e.target.value)}
          dropDownValues={dropDownData}
          selectedValue={value ?? (props.multiple ? [] : '')}
          name={name}
          showAllOptionsAfterSelection
          {...props}
        />
      </span>
    ) : (
      <DropDownSearchControl
        onInputChange={(e) => setValue(e.target.value)}
        dropDownValues={dropDownData}
        selectedValue={value}
        name={name}
        {...props}
      />
    );
  }

  if (type === 'search') {
    return (
      <SearchInput
        id="search-bar"
        data-testid="search-bar"
        type="text"
        name={name}
        placeholder="Type keywords to filter"
        value={value}
        onChange={(e) => setValue(e.target.value)}
        {...props}
      />
    );
  }
  if (type === 'date_range') {
    return (
      <div className="col mx-0 px-0">
        <ReactDatePicker
          selectsRange
          onChange={(update) => {
            setValue(
              dateFormat ? update?.map((v) => (v ? moment(v).format('YYYY-MM-DD') : null)) : update,
            );
          }}
          ref={ref}
          startDate={value?.[0] ? new Date(value?.[0]) : null}
          endDate={value?.[1] ? new Date(value?.[1]) : null}
          name={name}
          {...props}
        />
        {!value?.filter(Boolean)?.length ? (
          <Button
            variant="link"
            className="typeable-dropdown-icon-style"
            onClick={() => {
              // @ts-ignore
              if (ref) ref?.current?.setFocus();
            }}
          >
            {Icon && <Icon icon="dropdown" size={20} />}
          </Button>
        ) : (
          ''
        )}
      </div>
    );
  }
};
interface Props {
  filters: FilterConfig[];
  data: any;
  setApiQuery: (query: string) => void;
  selectedFilters: any;
  onFilterChange: (filters: any) => void;
  selectedRanks: number[];
  handleSearch: () => void;
  setIsAPIQueryInitialized: () => void;
}

const DynamicFilter: React.FC<Props> = ({
  filters = [],
  data,
  setApiQuery,
  selectedFilters,
  onFilterChange = () => {},
  selectedRanks = [],
  eventTracker,
  handleSearch,
  setIsAPIQueryInitialized = (arg: boolean) => { },
}) => {
  const location = useLocation();
  const urlFilter = location.search;
  const history = useHistory();
  const [filterValues, setFilterValues] = useState(selectedFilters ?? {});
  const handleInputChange = (name, val) => {
    if (val) {
      if (name === 'rank') {
        eventTracker?.('rankSelection', getFilterValuesFromIds(data, name, val));
      } else if (name === 'vessel') {
        eventTracker?.('vesselSearch', null);
      } else {
        eventTracker?.('filter', getFilterValuesFromIds(data, name, val));
      }
    }
    setFilterValues((prev) => cleanUpFilter({ ...prev, [name]: val }));
  };

  useEffect(() => {
    const urlFilterValues = generateFilterObjectFromApiQuery(urlFilter, filters);
    if (!isEmptyObject(urlFilterValues) && isEmptyObject(filterValues)) {
      setFilterValues(cleanUpFilter(urlFilterValues));
    }
    setIsAPIQueryInitialized(true);
  }, []);
  useEffect(() => {
    const urlFilterValues = generateFilterObjectFromApiQuery(urlFilter, filters);
    const crewPlanningStatusFilter = urlFilterValues?.crew_planning_status;
    let finalFilterValues = {};
    if (crewPlanningStatusFilter) {
      finalFilterValues = { ...filterValues, crew_planning_status: crewPlanningStatusFilter };
    } else {
      finalFilterValues = { ...filterValues, crew_planning_status: ['unplanned'] };
    }
    onFilterChange(finalFilterValues);
    if (isEmptyObject(data)) return;
    setApiQuery(processApiQuery({ lookupData: data, values: finalFilterValues, filters }));
    window.history.replaceState(
      {},
      null,
      `${history.location.pathname}?${generateApiQueryFromFilterObject(
        finalFilterValues,
        filters,
      )}`,
    );
  }, [filterValues, data]);

  return (
    <Row className="dynamic-filter mx-0" md={6}>
      {filters.map((props) => {
        if (props.hidden) {
          return null;
        }
        return (
          !props.shouldNotRenderInFilters && (
            <Col key={props.name} className={cx(props.className, 'p-0')} xs="auto">
              <RenderFilter
                filterValues={filterValues}
                selectedValue={filterValues[props.name]}
                onInputChange={handleInputChange}
                dropDownData={get(data, props.dataKey, [])}
                {...props}
              />
            </Col>
          )
        );
      })}
      <Col md={1} className="p-0">
        <Button
          variant="primary"
          onClick={handleSearch}
          className="float-end"
          style={{ width: '100%' }}
        >
          Search
        </Button>
      </Col>
    </Row>
  );
};

export default DynamicFilter;
