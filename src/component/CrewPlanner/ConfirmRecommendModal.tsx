import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, But<PERSON>, Modal } from 'react-bootstrap';
import { getCrewList, getCrewPlanningScore, getOcimfData } from '@src/service/seafarer-service';
import { RANK_HIERARCHY } from '@src/constants/crewPlanner';
import moment from 'moment-timezone';
import { getVesselIdFromSeafarer, getVesselType } from '@src/pages/CrewPlanner/utils';
import { set } from 'lodash';
import { renderLoader } from '../CrewManagement/CrewManagementAllCrewDetails';
import { CrewPlannerCrewDetail } from './CrewRecommendModalSeafarerDetails';
import { getRank } from './utils';
import SubmitButton from '../common/SubmitButton';
import { checkOcimfExist } from '../CrewManagement/CrewPlannerDetail';

const ConfirmRecommendModal = ({
  loadingConfirm,
  show,
  seafarer,
  reliever,
  handleCloseRecommend,
  handleConfirmRecommend,
  ringfencingOwners = [],
}) => {
  const isSameSeafarer = seafarer?.id === reliever?.id;
  const [onBoardingWithSeaFarers, setOnBoardingWithSeaFarers] = useState([]);
  const [isLoadingOnboardSeafarers, setIsLoadingOnboardSeafarers] = useState(false);
  const [currentSeafarer, setCurrentSeafarer] = useState(null);
  const [currentReliever, setCurrentReliever] = useState(null);
  const [jointExperienceScore, setJointExperienceScore] = useState(null);

  const vesselType = getVesselType(seafarer);
  const vesselId = getVesselIdFromSeafarer(seafarer) || getVesselIdFromSeafarer(reliever);
  const currentRank = getRank(seafarer);

  useEffect(() => {
    setIsLoadingOnboardSeafarers(true);
    setCurrentSeafarer(null);
    setCurrentReliever(null);
    (async () => {
      if (vesselId && seafarer && reliever && show) {
        const seafarerPersonIds = [
          !checkOcimfExist(seafarer) ? seafarer?.seafarer_person_id : null,
          !checkOcimfExist(reliever) ? reliever?.seafarer_person_id : null,
        ].filter(Boolean);
        const aggregatedResponses: any[] = [];
        const ocimfPromises = seafarerPersonIds?.map((id) => {
          return getOcimfData(
            {
              rank_id: seafarer?.rank_id ?? reliever.rank_id,
              rank: currentRank,
              vessel_id: vesselId,
              vessel_type: vesselType,
            },
            id,
          );
        });
        const ocimfResponses = await Promise.all(ocimfPromises);
        ocimfResponses?.forEach((res) => {
          aggregatedResponses.push(...(res?.data?.response ?? []));
        });
        [seafarer, reliever].forEach((crew) => {
          if (crew?.ocimf) {
            set(
              crew,
              'experience_summary.duration_in_target_rank_years',
              crew?.ocimf?.filter((o) => o?.group_by_name === 'rank' && o?.group_value === currentRank)?.[0]?.years ?? 0,
            );
            set(
              crew,
              'experience_summary.duration_on_target_vessel_type_years',
              crew?.ocimf?.filter((o) => o?.group_value === vesselType)?.[0]?.years ?? 0,
            );
            set(
              crew,
              'experience_summary.duration_with_company_years',
              crew?.ocimf?.filter((o) => o?.group_by_name === 'company')?.[0]?.years ?? 0,
            );
          } else if (!crew?.ocimf) {
            const ocimfResponse = aggregatedResponses.find((r) => r.seafarerId === crew.id);
            if (ocimfResponse) {
              set(
                crew,
                'experience_summary.duration_in_target_rank_years',
                ocimfResponse?.yearsInCurrentRank,
              );
              set(
                crew,
                'experience_summary.duration_on_target_vessel_type_years',
                ocimfResponse?.yearsOnCurrentTypeOfVessel,
              );
              set(
                crew,
                'experience_summary.duration_with_company_years',
                ocimfResponse?.yearsWithCompany,
              );
            }
          }
        });
        setCurrentSeafarer(seafarer);
        setCurrentReliever(reliever);
        if (Object.keys(RANK_HIERARCHY).includes(currentRank)) {
          const crewList = await getCrewList(
            `seafarer_rank.value=${
              RANK_HIERARCHY[currentRank]
            }&crew_list_status_date=${moment().format('yyyy-MM-DD')}&vessel_id=${vesselId}`,
          );
          crewList?.data?.results?.forEach((crew) => {
            const ocimf = aggregatedResponses?.filter((aggRes) => aggRes?.seafarerId === crew?.id);
            if (ocimf?.length) {
              set(
                crew,
                'experience_summary.duration_in_target_rank',
                ocimf?.[0]?.yearsInCurrentRank * 365,
              );
              set(
                crew,
                'experience_summary.duration_on_target_vessel_type',
                ocimf?.[0]?.yearsOnCurrentTypeOfVessel * 365,
              );
              set(
                crew,
                'experience_summary.duration_with_company',
                ocimf?.[0]?.yearsWithCompany * 365,
              );
            }
          });
          setOnBoardingWithSeaFarers(crewList?.data?.results);
        }
        setIsLoadingOnboardSeafarers(false);
      }
    })();
  }, [show]);
  const getRingfencingMessage = () => {
    return (
      <div style={{ fontSize: '14px' }}>
        <div style={{ fontWeight: 'bold' }}>
          {`The Selected Seafarer as Reliever is a preferred resource from ${ringfencingOwners.join(
            ', ',
          )}. Action to be taken: Please consult with your line manager before confirmation of plan.`}
        </div>
      </div>
    );
  };

  const experienceScore = currentReliever?.score?.experience ?? null;

  useEffect(() => {
    const loadCrewPlanningScore = async () => {
      const payload = {
        vessel_id: vesselId,
        vessel_type: vesselType,
        seafarer_ids: [currentReliever?.id],
        rank: currentRank,
      };
      const response = await getCrewPlanningScore(payload);
      if (response) {
        const jointExp = response.data?.[0]?.experience_grade;
        setJointExperienceScore(jointExp ?? 'N/A');
      }
    };

    if (!experienceScore && currentReliever) {
      loadCrewPlanningScore();
    }
  }, [currentReliever]);

  return (
    <Modal
      size="lg"
      show={show}
      onHide={() => {
        handleCloseRecommend();
      }}
      className="confirm-recommend-modal"
    >
      <Modal.Header className="modal-header">Planning Relieve</Modal.Header>
      <Modal.Body>
        {ringfencingOwners?.length ? <Alert variant="danger">{getRingfencingMessage()}</Alert> : ''}
        <Alert variant="info">
          Confirm to Plan the details to Relieve the Seafarer Onboard. Please do the Crew Assignment
          Recommendation process in a separate step.
        </Alert>
        {!isLoadingOnboardSeafarers && !!currentSeafarer && (
          <>
            <div className="planning-relieve-modal-headings">Onboard Seafarer:</div>
            <CrewPlannerCrewDetail crew={currentSeafarer} isSignedOnCrew vesselType={vesselType} />
          </>
        )}
        {!isLoadingOnboardSeafarers && !!currentReliever && (
          <>
            <div className="planning-relieve-modal-headings">Reliever Seafarer:</div>
            <CrewPlannerCrewDetail crew={currentReliever} vesselType={vesselType} />
          </>
        )}
        {isLoadingOnboardSeafarers ? renderLoader() : ''}
        {!isLoadingOnboardSeafarers && !!onBoardingWithSeaFarers?.length && (
          <div>
            <div className="planning-relieve-modal-headings">
              Seafarer will be joining Onboard with:
            </div>
            <div>
              The planner needs to confirm the Experience Details with the combined Crew Onboard for
              the Vessel Category.
            </div>
            {onBoardingWithSeaFarers?.map((onBoardingSeafarer) => (
              <CrewPlannerCrewDetail
                key={onBoardingSeafarer?.id}
                crew={onBoardingSeafarer}
                isOnBoardingWith
                experienceScore={experienceScore || jointExperienceScore || 'N/A'}
                vesselType={vesselType}
              />
            ))}
          </div>
        )}
        {isSameSeafarer && <Alert variant="danger">You Cannot Relieve Same Seafarer</Alert>}
      </Modal.Body>
      <Modal.Footer>
        <Button
          variant="primary"
          onClick={() => {
            setOnBoardingWithSeaFarers([]);
            handleCloseRecommend();
          }}
        >
          Cancel
        </Button>
        <SubmitButton
          isLoading={loadingConfirm}
          variant="secondary"
          onClick={() => {
            handleConfirmRecommend(currentSeafarer, currentReliever);
          }}
          disabled={isSameSeafarer || loadingConfirm || isLoadingOnboardSeafarers}
        >
          Confirm
        </SubmitButton>
      </Modal.Footer>
    </Modal>
  );
};

export default ConfirmRecommendModal;
