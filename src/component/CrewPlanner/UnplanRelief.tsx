import React from 'react';
import { <PERSON><PERSON>, Button, Modal } from 'react-bootstrap';
import SeafarerCard from './SeafarerCard';
import { SEAFARERS_TO_RELIEVE } from '../../constants/crewPlanner';
import SubmitButton from '../common/SubmitButton';

const UnplanRelief = ({
  show,
  seafarer,
  handleCloseUnplanRelief,
  handleConfirmUnplan,
  loadingConfirm,
  activeKey,
}) => {
  const isToRelieve = activeKey === SEAFARERS_TO_RELIEVE;
  const primarySeafarer = isToRelieve ? seafarer : seafarer?.crew_planning?.seafarer;
  const replacerSeafarer = isToRelieve ? seafarer?.crew_planning?.reliever : seafarer;

  const handleClose = () => handleCloseUnplanRelief();
  const handleConfirm = () =>
    handleConfirmUnplan(replacerSeafarer, seafarer?.crew_planning?.id, seafarer);

  return (
    <Modal size="lg" show={show} onHide={handleClose}>
      <Modal.Header closeButton>Unplan Relieve</Modal.Header>
      <Modal.Body>
        <Alert variant="danger">
          You're cancelling a relief,{' '}
          <b className="ml-1">please also cancel the Recommendation process</b>
        </Alert>
        <SeafarerCard
          toBeReplacedSeafarer={primarySeafarer}
          reliever={replacerSeafarer}
          visitSeafarerDetail={null}
          setSelectedActionSeafarer={null}
          renderRemarks
        />
      </Modal.Body>
      <Modal.Footer>
        <Button variant="primary" onClick={handleClose}>
          Cancel
        </Button>
        <SubmitButton
          variant="secondary"
          isLoading={loadingConfirm}
          onClick={handleConfirm}
          disabled={loadingConfirm}
        >
          Confirm
        </SubmitButton>
      </Modal.Footer>
    </Modal>
  );
};

export default UnplanRelief;
