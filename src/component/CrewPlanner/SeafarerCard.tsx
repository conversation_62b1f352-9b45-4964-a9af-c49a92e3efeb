import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Col, OverlayTrigger, Popover, Row, Tooltip } from 'react-bootstrap';
import { ArrowRight, ThreeDotsVertical, Airplane } from 'react-bootstrap-icons';
import classNames from 'classnames';
import { dateAsString, stringAsDate } from '@src/model/utils';
import { useCrewMangementContext } from '@src/pages/CrewManagementPage';
import { UIPlanningStatus } from '@src/constants/status';
import { checkMissingPersonnel, checkAdditionalRequest } from '@src/pages/CrewPlanner/utils';
import Avatar from '../common/Avatar';
import SeafarerName from './SeafarerName';
import {
  CREW_PLANNING_RIGHT_SIDE,
  CREW_PLANNING_LEFT_SIDE,
  STATUS,
  CREW_PLANNING_TYPE,
  CREW_PLANNING_ALLOW_UNCLUB,
  CREW_PLANNING_NON_ACTIONABLE_STATUS,
} from '../../constants/crewPlanner';
import {
  URGENT_PLANNING_REQUIRED_DAYS,
  getAvailbilityDate,
  getDayDiffFromToday,
  getDueDate,
  getPendingTooltipStatus,
  getUIPlanningStatusForPODManager,
  getRank,
  getStatusColorForPODManager,
  getFullName,
} from './utils';


const actionButtonList = ({
  toBeReplacedSeafarer,
  visitSeafarerDetail,
  haveReject,
  setSelectedActionSeafarer,
  setActiveOverlaySeafarerId,
  isRelieverTravelling,
  isRelieverSignedOn,
  isAdditionalCrewCancel,
  eventTracker,
  isFromTimeline,
  hasEditAccess,
  onReplacementClick,
}: {
  toBeReplacedSeafarer: any;
  visitSeafarerDetail: Function | null;
  haveReject: boolean;
  setSelectedActionSeafarer: Function | null;
  setActiveOverlaySeafarerId: Function | null;
  isRelieverTravelling: boolean;
  isRelieverSignedOn: boolean;
  isAdditionalCrewCancel: boolean;
  eventTracker: Function | null;
  isFromTimeline: boolean;
  hasEditAccess: boolean;
  onReplacementClick: Function | null;
}) => {
  const planningStatus = toBeReplacedSeafarer?.crew_planning?.planning_status;
  const isRelievedWithoutReplacement =
    toBeReplacedSeafarer?.crew_planning?.planning_type === CREW_PLANNING_TYPE.offsigner;
  const isUnclubButtonVisible =
    !isRelieverTravelling &&
    !isRelieverSignedOn &&
    CREW_PLANNING_ALLOW_UNCLUB.includes(planningStatus);
  const isRejectButtonVisible = haveReject && CREW_PLANNING_LEFT_SIDE.includes(planningStatus);
  const isExtendButtonVisible = !haveReject && CREW_PLANNING_LEFT_SIDE.includes(planningStatus);
  const isRelieveWithoutReplacementVisible =
    !haveReject &&
    !isRelievedWithoutReplacement &&
    CREW_PLANNING_RIGHT_SIDE.includes(planningStatus);
  const isCancelRelieveWithoutReplacementVisible =
    !haveReject &&
    isRelievedWithoutReplacement &&
    CREW_PLANNING_RIGHT_SIDE.includes(planningStatus);
  const isNonActionableStatus = CREW_PLANNING_NON_ACTIONABLE_STATUS.includes(planningStatus);
  const handleRejectButton = () => {
    setSelectedActionSeafarer({ seafarer: toBeReplacedSeafarer, action: STATUS.rejected });
    setActiveOverlaySeafarerId(null);
  };
  const handleUnclubButton = () => {
    eventTracker('clickUnclub');
    setSelectedActionSeafarer({ seafarer: toBeReplacedSeafarer, action: STATUS.unclubbed });
    setActiveOverlaySeafarerId(null);
  };

  const handleCancelAdditionalCrew = () => {
    setSelectedActionSeafarer({ seafarer: toBeReplacedSeafarer, action: 'cancel_additional_crew' });
    setActiveOverlaySeafarerId(null);
  };

  const handleRelieveWithoutReplacement = () => {
    setSelectedActionSeafarer({
      seafarer: toBeReplacedSeafarer,
      action: 'relieve_without_replacement',
    });
    setActiveOverlaySeafarerId(null);
  };

  const handleCancelRelieveWithoutReplacement = () => {
    setSelectedActionSeafarer({
      seafarer: toBeReplacedSeafarer,
      action: 'cancel_relieve_without_replacement',
    });
    setActiveOverlaySeafarerId(null);
  };
  const isMissingPersonnel = checkMissingPersonnel(toBeReplacedSeafarer);
  const isAdditionalCrewRequest = checkAdditionalRequest(toBeReplacedSeafarer);
  const showViewDetail =
    !((isMissingPersonnel || isAdditionalCrewRequest) && !toBeReplacedSeafarer?.crew_planning) &&
    !isNonActionableStatus;
  const handleViewDetails = () => {
    visitSeafarerDetail?.bind(
      this,
      toBeReplacedSeafarer.id,
      toBeReplacedSeafarer.ref_id,
      toBeReplacedSeafarer.seafarer_person,
    )();
    if (eventTracker) {
      eventTracker('viewDetails', getFullName(toBeReplacedSeafarer));
    }
  };

  return (
    !isNonActionableStatus && (
      <div style={{ display: 'grid' }}>
        {showViewDetail && (
          <div
            className="li-item"
            onClick={handleViewDetails}
            onKeyDown={(event) => {
              if (event.key === 'Enter' || event.key === ' ') {
                handleViewDetails();
              }
            }}
            tabIndex={0}
          >
            View Details
          </div>
        )}
        {isExtendButtonVisible && hasEditAccess && (
          <div className="li-item" onClick={() => { }} onKeyDown={(event) => { }} tabIndex={0}>
            Extend Contract
          </div>
        )}
        {isAdditionalCrewCancel && hasEditAccess && (
          <div
            className="li-item"
            onClick={handleCancelAdditionalCrew}
            onKeyDown={(event) => {
              if (event.key === 'Enter' || event.key === ' ') {
                handleCancelAdditionalCrew();
              }
            }}
            tabIndex={0}
          >
            Cancel
          </div>
        )}
        {!haveReject && !isRelievedWithoutReplacement && hasEditAccess && (
          <div
            className="li-item"
            onClick={(e) => onReplacementClick(e, eventTracker, toBeReplacedSeafarer, isFromTimeline)}
            onKeyDown={(event) => {
              if (event.key === 'Enter' || event.key === ' ')
                onReplacementClick(event, eventTracker, toBeReplacedSeafarer, isFromTimeline);
            }}
            tabIndex={0}
          >
            Request Replacement
          </div>
        )}
        {isRejectButtonVisible && hasEditAccess && (
          <div
            className="li-item"
            onClick={handleRejectButton}
            onKeyDown={(event) => {
              if (event.key === 'Enter' || event.key === ' ') {
                handleRejectButton();
              }
            }}
            tabIndex={0}
          >
            Reject
          </div>
        )}
        {isUnclubButtonVisible && hasEditAccess && (
          <div
            className="li-item"
            onClick={handleUnclubButton}
            onKeyDown={(event) => {
              if (event.key === 'Enter' || event.key === ' ') {
                handleUnclubButton();
              }
            }}
            tabIndex={0}
          >
            Unclub
          </div>
        )}
        {isRelieveWithoutReplacementVisible && hasEditAccess && (
          <div
            className="li-item"
            onClick={() => handleRelieveWithoutReplacement()}
            onKeyDown={(event) => {
              if (event.key === 'Enter' || event.key === ' ') handleRelieveWithoutReplacement();
            }}
            tabIndex={0}
          >
            Relieve without replacement
          </div>
        )}
        {isCancelRelieveWithoutReplacementVisible && hasEditAccess && (
          <div
            className="li-item"
            onClick={(e) => handleCancelRelieveWithoutReplacement()}
            onKeyDown={(event) => {
              if (event.key === 'Enter' || event.key === ' ') handleCancelRelieveWithoutReplacement();
            }}
            tabIndex={0}
          >
            Cancel Relieve without replacement
          </div>
        )}
      </div>
    )
  );
};

interface SeafarerCardProps {
  toBeReplacedSeafarer: any;
  reliever: any;
  packedStyle: boolean;
  visitSeafarerDetail: Function | null;
  isEditable: boolean;
  relieverStatus: any;
  isRelieverTravelling: boolean;
  isRelieverSignedOn: boolean;
  isFromTimeline: boolean;
  onConfirmClub: Function;
  eventTracker?: Function;
  renderRemarks?: boolean;
  hasEditAccess?: boolean;
}

const SeafarerCard = ({
  toBeReplacedSeafarer,
  reliever,
  packedStyle,
  visitSeafarerDetail,
  isEditable,
  relieverStatus,
  isRelieverTravelling,
  isRelieverSignedOn,
  isFromTimeline,
  onConfirmClub,
  eventTracker,
  renderRemarks,
  hasEditAccess,
}: SeafarerCardProps) => {


  const {
    onRequestReplacement,
    setActiveOverlaySeafarerId,
    activeOverlaySeafarerId,
    setSelectedActionSeafarer,
  } = useCrewMangementContext();
  const dueDate = getDueDate(toBeReplacedSeafarer, toBeReplacedSeafarer?.crew_planning?.crew_planning_audit_log);
  const seafarerRank = getRank(toBeReplacedSeafarer);
  const relieverRank = getRank(reliever);

  const relieverAvailableDate = getAvailbilityDate(reliever, toBeReplacedSeafarer?.crew_planning?.crew_planning_audit_log);

  const datDiffFromToday = getDayDiffFromToday(dueDate);
  const isUrgent = datDiffFromToday < URGENT_PLANNING_REQUIRED_DAYS;
  const isOverlayVisible = activeOverlaySeafarerId === toBeReplacedSeafarer?.id;
  const isSeafarerMissingPersonnel = checkMissingPersonnel(toBeReplacedSeafarer);
  const isAdditionalCrewRequest = checkAdditionalRequest(toBeReplacedSeafarer);

  const planningStatus = toBeReplacedSeafarer?.crew_planning?.planning_status;

  const handleOverlayTriggerClick = () => {
    if (eventTracker) {
      eventTracker('clickEllipsis');
    }
    setActiveOverlaySeafarerId(isOverlayVisible ? null : toBeReplacedSeafarer?.id);
  };

  const onReplacementClick = (
    e: any,
    eventTracker,
    toBeReplacedSeafarer,
    isFromTimeline = false,
  ) => {
    if (isFromTimeline) {
      eventTracker('requestReplacementTimeline', getFullName(toBeReplacedSeafarer));
    } else {
      eventTracker('requestReplacement', getFullName(toBeReplacedSeafarer));
    }
    onRequestReplacement?.({
      seafarerRank,
      isSeafarerMissingPersonnel,
      isAdditionalCrewRequest,
    });
    setActiveOverlaySeafarerId(null);
    e.preventDefault();
  };

  const statusColor = getStatusColorForPODManager({
    reliever,
    relieverStatus,
    toBeReplacedSeafarer,
  });

  const planStatus = getUIPlanningStatusForPODManager({
    reliever,
    toBeReplacedSeafarer,
    relieverStatus,
    planningStatus: toBeReplacedSeafarer?.crew_planning?.planning_status,
    isHistoryModal: false,
  });
  const stateValueMap = [
    {
      status: UIPlanningStatus.ReadyToConfirm,
      render: () => (
        <Button
          className="seafarer-card-button"
          variant="outline-primary"
          onClick={(e) => {
            e.stopPropagation();
            onConfirmClub({ seafarer: toBeReplacedSeafarer, action: STATUS.club_confirmed });
          }}
        >
          Confirm
        </Button>
      ),
    },
    {
      status: UIPlanningStatus.Pending,
      render: () => (
        <OverlayTrigger
          placement="top"
          overlay={
            <Tooltip id="tooltip-travel-planned">{getPendingTooltipStatus(reliever)}</Tooltip>
          }
        >
          <div className="seafarer-card-pending-status">Pending</div>
        </OverlayTrigger>
      ),
    },
    {
      status: UIPlanningStatus.Confirmed,
      render: () => (
        <div className="travelling-icon travelling-icon-orange">
          <OverlayTrigger
            placement="top"
            overlay={<Tooltip id="tooltip-travel-planned">Travel Planned</Tooltip>}
          >
            <Airplane size={14} color="#ffa221" />
          </OverlayTrigger>
        </div>
      ),
    },
    {
      status: UIPlanningStatus.Travelling,
      render: () => (
        <div className="travelling-icon">
          <OverlayTrigger
            placement="top"
            overlay={
              <Tooltip id="tooltip-travel-planned">
                {isRelieverSignedOn
                  ? 'Signed-on, but the due seafarer is not signed-off'
                  : 'Travelling'}
              </Tooltip>
            }
          >
            <Airplane size={14} color="#28A748" />
          </OverlayTrigger>
        </div>
      ),
    },
    {
      status: UIPlanningStatus.Request_Replacement,
      render: () =>
        hasEditAccess && (
          <Button
            variant="outline-primary"
            className="seafarer-card-button"
            onClick={(e) =>
              onReplacementClick(e, eventTracker, toBeReplacedSeafarer, isFromTimeline)
            }
            disabled={CREW_PLANNING_NON_ACTIONABLE_STATUS.includes(planningStatus)}
          >
            Request Replacement
          </Button>
        ),
    },
    {
      status: UIPlanningStatus.Completed,
      render: () => <div className="seafarer-card-success-status">Completed</div>,
    },
    {
      status: UIPlanningStatus.Expired,
      render: () => <div className="seafarer-card-expired-status">Expired</div>,
    },
  ];

  const renderStatus = () => {
    const valueMap = stateValueMap.find((item) => item.status === planStatus);
    return valueMap?.render?.();
  };
  const isEmptyRank = (isSeafarerMissingPersonnel || isAdditionalCrewRequest) && !reliever;
  const isAdditionalCrewCancel = isAdditionalCrewRequest && !reliever;
  const containerClassName = classNames({
    'seafarer-card': packedStyle,
    'unplan-relief-details': !packedStyle,
    [`seafarer-card-status-${statusColor}`]: isEditable,
  });
  const renderMenu = () => {
    if (!isEditable) return null;
    const actionList = actionButtonList({
      toBeReplacedSeafarer,
      visitSeafarerDetail,
      haveReject: !!reliever,
      setSelectedActionSeafarer,
      setActiveOverlaySeafarerId,
      isRelieverTravelling,
      isRelieverSignedOn,
      isAdditionalCrewCancel,
      eventTracker,
      isFromTimeline,
      hasEditAccess,
      onReplacementClick,
    });
    const overlayMenu = (
      <Popover>
        <Popover.Content className="text-primary">{actionList}</Popover.Content>
      </Popover>
    );
    const isActionListEmpty = !actionList?.props?.children || actionList?.props?.children?.filter(Boolean)?.length === 0;
    return (
      <Row className="align-items-center p-0 d-flex flex-column justify-content-center seafarer-card-action-button">
        {hasEditAccess && !isActionListEmpty && (
          <Button
            variant="link"
            type="button"
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            <OverlayTrigger
              rootClose
              trigger="click"
              show={isOverlayVisible}
              onToggle={() => handleOverlayTriggerClick()}
              key="bottom"
              placement="bottom"
              overlay={overlayMenu}
            >
              <div>
                <ThreeDotsVertical size={20} />
              </div>
            </OverlayTrigger>
          </Button>
        )}
      </Row>
    );
  };
  const renderEmptyRank = () => {
    return (
      <Col className={containerClassName}>
        <Row className="seafarer-card-content">
          <Row className="seafarer-card-content-main">
            <Col xs={2} sm={2} md={2} lg={2} className="p-0">
              <Avatar
                firstName={toBeReplacedSeafarer?.seafarer_person?.first_name}
                lastName={toBeReplacedSeafarer?.seafarer_person?.last_name}
                emptyText={isSeafarerMissingPersonnel || isAdditionalCrewRequest ? '?' : '---'}
              />
            </Col>
            <Col className="p-0 d-flex align-items-center">
              <b>
                {seafarerRank ?? '---'} {isAdditionalCrewRequest && ' (Additional)'}
              </b>
            </Col>
          </Row>
          {renderMenu()}
        </Row>
      </Col>
    );
  };


  if (isEmptyRank) return renderEmptyRank();
  return (
    <Col className={containerClassName}>
      <Row className="seafarer-card-content">
        <Row className="seafarer-card-content-main">
          {(!isAdditionalCrewRequest || (isAdditionalCrewRequest && !isFromTimeline)) && (
            <>
              <Col xs={2} sm={2} md={2} lg={2} className="p-0">
                <Avatar
                  firstName={toBeReplacedSeafarer?.seafarer_person?.first_name}
                  lastName={toBeReplacedSeafarer?.seafarer_person?.last_name}
                  emptyText={isSeafarerMissingPersonnel || isAdditionalCrewRequest ? '?' : '---'}
                />
              </Col>
              <Col className="p-0">
                <SeafarerName
                  id={toBeReplacedSeafarer?.id}
                  row={toBeReplacedSeafarer}
                  className={packedStyle ? 'seafarer-card-text' : ''}
                  eventTracker={eventTracker}
                  remark={
                    renderRemarks
                      ? toBeReplacedSeafarer?.crew_planning_remarks?.[0]?.remarks ?? '---'
                      : ''
                  }
                />
                <div style={{ padding: '0 0.4rem' }}>
                  <b>
                    {
                      seafarerRank ||
                      ((reliever?.crew_planning?.planning_type === CREW_PLANNING_TYPE.add_rank ||
                        reliever?.crew_planning?.planning_type ===
                        CREW_PLANNING_TYPE.missing_personnel)
                        ? relieverRank
                        : '---')}
                  </b>
                  {!isSeafarerMissingPersonnel && !isAdditionalCrewRequest && (
                    <div style={{ fontSize: '0.7rem', color: '#6C757D', marginTop: '5px' }}>
                      Due off: {dueDate ? dateAsString(stringAsDate(dueDate)) : '---'}
                    </div>
                  )}
                </div>
              </Col>
              <Col xs={1} sm={1} md={1} className="p-0" style={{ margin: 'auto 0' }}>
                <ArrowRight size={30} color="#1F4A70" />
              </Col>
            </>
          )}
          <Col xs={2} sm={2} md={2} lg={2} className="p-0">
            <Avatar
              firstName={reliever?.seafarer_person?.first_name}
              lastName={reliever?.seafarer_person?.last_name}
            />
          </Col>
          <Col className="p-0 flex-between">
            <div>
              {reliever ? (
                <SeafarerName
                  id={reliever.id}
                  row={reliever}
                  className="seafarer-card-text"
                  eventTracker={eventTracker}
                  remark={
                    renderRemarks ? reliever?.crew_planning_remarks?.[0]?.remarks ?? '---' : ''
                  }
                />
              ) : (
                <div style={{ padding: '0 0.4rem' }}>---</div>
              )}
              <div style={{ padding: '0 0.4rem' }}>
                <b>{relieverRank ?? '---'}</b>
                <div style={{ fontSize: '0.7rem', color: '#6C757D', marginTop: '5px' }}>
                  Available:{' '}
                  {relieverAvailableDate
                    ? dateAsString(stringAsDate(relieverAvailableDate))
                    : '---'}
                </div>
              </div>
            </div>
            <div style={{ display: 'flex' }}>
              {isFromTimeline && isAdditionalCrewRequest && (
                <span className="seafarer-card-additional-crew">Additional crew</span>
              )}
              {isFromTimeline && renderStatus()}
            </div>
          </Col>
        </Row>
        {renderMenu()}
      </Row>
      {isEditable && isUrgent && !reliever && (
        <Row>
          <Alert variant="danger">
            <div>
              <b>Urgent Planning Required: </b>
              {datDiffFromToday >= 0
                ? `Seafarer due off in ${datDiffFromToday} Days`
                : `Seafarer overdue by ${Math.abs(datDiffFromToday)} Days`}
            </div>
          </Alert>
        </Row>
      )}
    </Col>
  );
};

export default SeafarerCard;
