import React from 'react';
import { dateAsString, stringAsDate, isDateValid } from '../../model/utils';

interface Props {
  value: string;
  colorClass: string | undefined;
  bold: boolean;
}

const DueDate = ({ value, colorClass = '', bold }: Props) => {
  let cClass = colorClass;
  if (!value || !isDateValid(value)) {
    return '---';
  }
  const dt = dateAsString(stringAsDate(value));
  if (bold) cClass += ' font-bold';
  return (
    <span data-testid="due_date_span" className={cClass} title={dt}>
      {dt}
    </span>
  );
};

export default DueDate;
