import React from 'react';
import { <PERSON><PERSON>, But<PERSON>, Modal } from 'react-bootstrap';

const ConfirmClubModal = ({ show, handleCloseConfirm, handleConfirmClub }) => {
  return (
    <Modal
      size="lg"
      show={show}
      onHide={() => {
        handleCloseConfirm();
      }}
    >
      <Modal.Header className="modal-header">Confirm Clubbing</Modal.Header>
      <Modal.Body>
        <Alert variant="info">
          By confirming to club, you will be redirected to the Reliever Seafarer Profile where you can set the travel status
        </Alert>
      </Modal.Body>
      <Modal.Footer>
        <Button
          variant="primary"
          onClick={() => {
            handleCloseConfirm();
          }}
        >
          Cancel
        </Button>
        <Button
          variant="secondary"
          onClick={() => {
            handleConfirmClub();
          }}
        >
          Confirm
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default ConfirmClubModal;
