import React from 'react';
import { normalizeDuration } from '../../model/utils';

const ShowYearsMonthsFromDays = ({ value }: { value: number | string }) => {
  if (!value) {
    return <div title="---">---</div>;
  }
  const { years, months } = normalizeDuration({ days: parseInt(value, 10) });
  if (!years && !months) {
    return <div title="---">---</div>;
  }
  let title = '';
  if (years) {
    title += `${years} years`;
  }
  if (months) {
    if (title) {
      title += ' ';
    }
    title += `${months} months`;
  }
  return <div title={title}>{title}</div>;
};

export const ShowYearsMonthsFromYears = ({ value }: { value: number | string }) => {
  if (!value) {
    return <div title="---">---</div>;
  }
  // Convert years to months
  const totalMonths = value * 12;

  // Calculate years and remaining months
  const formattedYears = Math.floor(totalMonths / 12);
  const formattedMonths = Math.round(totalMonths % 12);

  if (!formattedYears && !formattedMonths) {
    return <div title="---">---</div>;
  }

  const title = [
    formattedYears ? `${formattedYears} years` : '',
    formattedMonths ? `${formattedMonths} months` : '',
  ]
    .filter(Boolean)
    .join(' ');

  // Construct the formatted string
  return <div title={title}>{title}</div>;
};

export default ShowYearsMonthsFromDays;
