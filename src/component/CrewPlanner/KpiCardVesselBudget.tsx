import React from 'react';
import Spinner from '../common/Spinner';

interface KpiCardProps {
  isLoading: boolean;
  value: string | null;
  title: string;
}

const KpiCardVesselBudget: React.FC<KpiCardProps> = ({ isLoading, value, title }) => {
  return (
    <div className="kpi-card vessel-budget-kpi-card">
    <div className="kpi-title">{title}</div>
      <div className="vessel-budget-value">
        <span className="number">
          {isLoading ? (
            <Spinner />
          ) : (
            value ?? <div className='kpi-card-hyphen'>-</div>
          )}
        </span>
      </div>
    </div>
  );
};

export default KpiCardVesselBudget;
