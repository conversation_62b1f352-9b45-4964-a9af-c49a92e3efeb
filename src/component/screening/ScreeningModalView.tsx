import React, { useState, useEffect } from 'react';
import { Button, Modal } from 'react-bootstrap';
import { approvalStatuses } from '../../model/constants';
import { performAction } from './action';
import FileUploadComponents from '../common/FileUploadModalView';
const { FileUploadModalView } = FileUploadComponents;

export const ScreeningModalView = (props) => {
  const [remarks, setRemarks] = useState(null);
  const [fileUploadModalShow, setFileUploadModalShow] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [errorRemarks, setErrorRemarks] = useState(false);
  const [isFileTypeSelected, setIsFileTypeSelected] = useState(true);
  const [isFileSizeValid, setIsFileSizeValid] = useState(true);
  const [isFileTypeValid, setIsFileTypeValid] = useState(true);

  useEffect(() => {
    if (props.actionType === 'reject') {
      setErrorRemarks(true);
    }
  }, [props.actionType])

  const selectedFilesLabel = () => `${selectedFiles.length} files selected.`;

  const openFileModal = () => {
    setFileUploadModalShow(true);
  };

  const onSelectedFilesChange = (files) => {
    setIsFileTypeSelected(true);
    setIsFileSizeValid(true);
    setIsFileTypeValid(true);
    for (const file of files) {
      if (!file.document_type) {
        setIsFileTypeSelected(false);
      }
      if (file.size > 5000000) {
        setIsFileSizeValid(false);
      }
      if (!['image/png', 'image/jpeg', 'application/pdf'].includes(file.type)) {
        setIsFileTypeValid(false);
      }
    }
    setSelectedFiles([...files]);
  };

  const onConfirm = () => {
    props.closeScreeningModal();
    performAction(props, getAction(), null, selectedFiles, setSelectedFiles, remarks);
  };

  const getAction = () => {
    switch (props.actionType) {
      case 'approve':
        return approvalStatuses.APPROVED;
      case 'forward':
        return approvalStatuses.FORWARDED;
      case 'reject':
        return approvalStatuses.REJECTED;
      case 'rework':
        return approvalStatuses.PENDING;
      case 'reapply':
        return approvalStatuses.REAPPLIED;
      default:
        throw Error('Action does not match!');
    }
  };

  const getTitle = () => {
    switch (props.actionType) {
      case 'approve':
        return 'Confirm Approve?';
      case 'forward':
        return 'Confirm Forwarding to Supervisor?';
      case 'reject':
        return 'Confirm Reject?';
      case 'rework':
        return 'Confirm Rework?';
      case 'reapply':
        return 'Confirm Reapply?';
      default:
        return '';
    }
  };

  const getMessage = () => {
    switch (props.actionType) {
      case 'approve':
        return 'Are you sure to approve acceptance of this seafarer? You may leave any remarks below:';
      case 'forward':
        return 'Are you sure forwarding this seafarer to supervisor for further review? State reason below:';
      case 'reject':
        return 'Are you sure to reject acceptance of this seafarer? State reason below:';
      case 'rework':
        return `Are you sure reworking the screening status?`;
      case 'reapply':
        return 'Are you sure to reapply screening for this seafarer? You may leave any remarks below:';
      default:
        return '';
    }
  };

  return (
    <>
      <Modal
        className="action-modal"
        show={props.show}
        onHide={() => props.closeScreeningModal()}
        centered
      >
        <Modal.Header>
          <Modal.Title className="h5">{getTitle()}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <span>{getMessage()}</span>
          {props.actionType !== 'rework' && (
            <form className="mt-2">
              <div className="form-group">
                <textarea
                  className="form-control"
                  rows="3"
                  onChange={(e) => setRemarks(e.target.value)}
                ></textarea>
                {errorRemarks && !remarks && (
                  <div className="invalid-feedback d-block">Please enter Remarks.</div>
                )}
              </div>
              {selectedFiles.length !== 0 ? (
                <div className="selected-files-label">{selectedFilesLabel()}</div>
              ) : null}
              <Button className="btn-sm" variant="outline-secondary" onClick={openFileModal}>
                File Upload
              </Button>
              {selectedFiles.length > 0 && !isFileTypeSelected && (
                <div className="invalid-feedback d-block">Please select file type</div>
              )}
              {selectedFiles.length > 0 && !isFileSizeValid && (
                <div className="invalid-feedback d-block">Please check file size</div>
              )}
              {selectedFiles.length > 0 && !isFileTypeValid && (
                <div className="invalid-feedback d-block">Please check file type</div>
              )}
            </form>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="primary" onClick={() => props.closeScreeningModal()}>
            Cancel
          </Button>
          <Button
            variant="secondary"
            onClick={onConfirm}
            disabled={
              (errorRemarks && !remarks) ||
              (selectedFiles.length > 0 && (!isFileTypeSelected || !isFileSizeValid))
            }
          >
            Confirm
          </Button>
        </Modal.Footer>
      </Modal>
      <FileUploadModalView
        show={fileUploadModalShow}
        onClose={() => setFileUploadModalShow(false)}
        selectedFiles={selectedFiles}
        onSelectedFilesChange={onSelectedFilesChange}
        roleConfig={props.roleConfig}
      />
    </>
  );
};

export default ScreeningModalView;
