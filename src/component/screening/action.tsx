import React from 'react';
import screeningService from '../../service/screening-service';
import ImageController from '../../controller/image-upload-controller';
const imageController = new ImageController();
import { approvalStatuses } from '../../model/constants';

export async function performAction(
  parentProps,
  approvalStatus,
  setError,
  selectedFiles,
  setSelectedFiles,
  remarks = null,
) {
  parentProps.setLoadingFor({ rowId: parentProps.approvalId });
  try {
    if (selectedFiles.length > 0) {
      await imageController.uploadCaseReportImages(parentProps.approvalId, selectedFiles);
    }

    await updateScreeningStatus(
      parentProps.approvalId,
      approvalStatus,
      remarks,
      parentProps.seafarerId,
    );
    if(approvalStatus == approvalStatuses.APPROVED){
      parentProps.setChangedStatus(Math.random());
    }
  } catch (error) {
    setError(error);
  }

  async function updateScreeningStatus(approvalId, approvalStatus, remarks, seafarerId) {
    const response = await screeningService.updateScreeningData(
      approvalId,
      approvalStatus,
      remarks,
      seafarerId,
    );

    if (response) {
      parentProps.setLoadingFor({ rowId: null });

      if (response.status == 200) {
        setSelectedFiles([]);
        parentProps.renderTable();
      } else {
        throw Error('Action not performed correctly!');
      }
    } else {
      throw Error('Action not performed correctly!');
    }
  }
}

export default { performAction };
