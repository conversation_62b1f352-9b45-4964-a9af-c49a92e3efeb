/* eslint-disable react/prop-types */
import React, { useEffect, useRef, useState } from 'react';

import WcoScreeningDetailsController from '../../controller/wco-screening-details-controller';

import styleGuide from '../../styleGuide';
import Spinner from '../../component/common/Spinner';

const { Icon } = styleGuide;

const WCO_CASE_STATUS_SUCCESS = 'success';
const WCO_CASE_STATUS_ERROR = 'error';

const TEXT_ERROR = 'Unable to generate status';
const TEXT_NO_MATCH = 'The Seafarer has no criminal record found';
const TEXT_HAS_MATCHES = 'matches found';

const Header = () => (
  <h6 className="screening-page__section-header">2. AUTOMATED SCREENING PROCESS</h6>
);

const WcoStatus = ({ matchCount, status }) => {
  let text = TEXT_ERROR;
  let icon = 'close';
  if (status === WCO_CASE_STATUS_SUCCESS) {
    text = matchCount > 0 ? `${matchCount} ${TEXT_HAS_MATCHES}` : TEXT_NO_MATCH;
    icon = matchCount > 0 ? 'close' : 'checked';
  }

  const className = `wco-status ${
    status === WCO_CASE_STATUS_ERROR || matchCount > 0 ? 'error' : 'success'
  }`;
  return (
    <div className={className}>
      <Icon icon={icon} size={20} /> {text}
    </div>
  );
};

const WcoScreeningDetails = ({ seafarerId, seafarerPersonId }) => {
  const { current: controller } = useRef(new WcoScreeningDetailsController());

  const [wcoCase, setWcoCase] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    return () => controller.close();
  }, []);

  useEffect(() => {
    (async () => {
      if (seafarerPersonId) {
        setIsLoading(true);
        try {
          await controller.loadCase(seafarerId, seafarerPersonId);
          setWcoCase(controller.wcoCase);
        } catch (error) {
          console.error(error);
        } finally {
          setIsLoading(false);
        }
      }
    })();
  }, [seafarerPersonId]);

  if (isLoading) {
    return (
      <>
        <Header />
        <Spinner />
      </>
    );
  }

  if (!wcoCase) {
    return <Header />;
  }

  return (
    <>
      <Header />
      <table className="table">
        <thead>
          <tr>
            <th>Case Created</th>
            <th>Case ID</th>
          </tr>
        </thead>
        <tr>
          <td>{wcoCase.created_at}</td>
          <td>{wcoCase.wco_case_id}</td>
        </tr>
      </table>
      <hr className="table-section" />
      <h7 className="screening-page__table-section-header">Client/Submitted Data</h7>
      <table className="table">
        <thead>
          <tr>
            <th>Name</th>
            <th>Gender</th>
            <th>Nationality</th>
            <th>Country</th>
            <th>Date of Birth</th>
            <th>Country of Birth</th>
          </tr>
        </thead>
        <tr>
          <td>{wcoCase.submittedValues.name}</td>
          <td>{wcoCase.submittedValues.gender}</td>
          <th>{wcoCase.submittedValues.nationality}</th>
          <td>{wcoCase.submittedValues.countryLocation}</td>
          <td>{wcoCase.submittedValues.dateOfBirth}</td>
          <td>{wcoCase.submittedValues.placeOfBirth}</td>
        </tr>
      </table>
      <hr className="table-section" />
      <h7 className="screening-page__table-section-header">World Check One Data</h7>
      <div className="screening-page__table-section-header-textarea">
        {' '}
        Powered by Refinitiv World-Check
      </div>
      <WcoStatus matchCount={wcoCase.results.length} status={wcoCase.status} />
      {wcoCase.status === WCO_CASE_STATUS_SUCCESS && (
        <table className="table">
          <thead>
            <tr>
              <th>Match Strength</th>
              <th>Name</th>
              <th>Gender</th>
              <th>Nationality</th>
              <th>Country</th>
              <th>Date of Birth</th>
              <th>Country of Birth</th>
              <th>Category</th>
              <th>Provider Type</th>
              <th>Reference ID</th>
            </tr>
          </thead>
          {wcoCase.results.map(
            ({
              matchStrength,
              name,
              category,
              providerType,
              referenceId,
              gender,
              dateOfBirth,
              countryLocation,
              placeOfBirth,
              nationality,
            }) => (
              <tr key={referenceId}>
                <td>{matchStrength}</td>
                <td>{name}</td>
                <td>{gender}</td>
                <td>{nationality}</td>
                <td>{countryLocation}</td>
                <td>{dateOfBirth}</td>
                <td>{placeOfBirth}</td>
                <td>{category}</td>
                <td>{providerType}</td>
                <td>{referenceId}</td>
              </tr>
            ),
          )}
        </table>
      )}
    </>
  );
};

export default WcoScreeningDetails;
