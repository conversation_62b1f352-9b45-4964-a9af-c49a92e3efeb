import React from 'react';
import { seafarerStatusService } from 'paris2-seafarer-status';
import { formatDate } from '../../../util/view-utils';

const journeyStatusJson = seafarerStatusService.getJourneyStatus();

const getSeafarerStatus = (jsonData, status) => {
  if (status && jsonData[status]) {
    return jsonData[status].name;
  }
  return '---';
};

const renderSeafarerNameCell = ({ row }) => (
  <a href={`/seafarer/details/${row.original.seafarerId}/general`} target="_blank" rel="noreferrer">
    {row.original.name}
  </a>
);

const renderVesselNameCell = ({ row }) => (
  <a href={`/vessel/ownership/details/${row.original.vessel_ownership_id}/general`} target="_blank" rel="noreferrer">
    {row.original.vessel_name}
  </a>
);

const renderDateCell = ({ cell }) => <span>{cell.value ? formatDate(cell.value) : ''}</span>;
const renderContractMonthCell = ({ cell }) => <span>{(cell.value && cell.value >=0) ? cell.value : ''}</span>;

const baseColumns = [
  {
    Header: 'HKID',
    accessor: 'hkid',
    width: 100,
    maxWidth: 100,
    minWidth: 100,
    type: 'text',
    sticky: 'left',
  },
  {
    Header: 'Onboard Seafarer',
    accessor: 'name',
    Cell: renderSeafarerNameCell,
    width: 170,
    minWidth: 170,
    maxWidth: 180,
    type: 'text',
    sticky: 'left',
  },
  {
    Header: 'Rank',
    accessor: 'rank',
    width: 120,
    minWidth: 120,
    maxWidth: 240,
    type: 'text',
    disableSortBy: true,
  },
  {
    Header: 'Vessel Name',
    accessor: 'vessel_name',
    Cell: renderVesselNameCell,
    width: 150,
    minWidth: 150,
    maxWidth: 200,
    type: 'text',
    disableSortBy: true,
  },
  {
    Header: 'Nationality',
    accessor: 'nationality',
    width: 150,
    minWidth: 150,
    maxWidth: 200,
    type: 'text',
    disableSortBy: true,
  },
  {
    Header: 'Reporting Office',
    accessor: 'reportingOffice',
    width: 150,
    minWidth: 150,
    maxWidth: 200,
    type: 'text',
    disableSortBy: true,
  },
  {
    Header: 'Joining Date',
    accessor: 'dateOfJoining',
    Cell: renderDateCell,
    width: 170,
    minWidth: 170,
    maxWidth: 240,
    type: 'date',
    disableSortBy: true,
  },
  {
    Header: 'Sign Off Date',
    accessor: 'endDate',
    Cell: renderDateCell,
    width: 170,
    minWidth: 170,
    maxWidth: 240,
    type: 'date',
    disableSortBy: true,
  },
  {
    Header: 'Length of the Contract (in Months)',
    accessor: 'months',
    Cell: renderContractMonthCell,
    width: 200,
    minWidth: 200,
    maxWidth: 200,
    type: 'number',
    disableSortBy: true,
  },
  {
    Header: 'Status',
    accessor: (row) => getSeafarerStatus(journeyStatusJson, row.currentJourneyStatus),
    width: 140,
    minWidth: 140,
    maxWidth: 190,
    type: 'text',
    disableSortBy: true,
    customDataCellStyle: {
      wordBreak: 'normal',
    },
  },
  {
    Header: 'Remarks',
    accessor: 'signOffReason',
    Cell: ({ cell }) => <span>{cell.value ?? 'N/A'}</span>,
    width: 140,
    minWidth: 140,
    maxWidth: 190,
    type: 'text',
    disableSortBy: true,
    customDataCellStyle: {
      wordBreak: 'normal',
    },
  },
];

const getColumnDefs = ({ selectedRows, onSelectRow, data }) => {
  const columns = baseColumns.map((column) => ({
    ...column,
    Footer: null,
  }));
  return columns;
};

export default getColumnDefs;
