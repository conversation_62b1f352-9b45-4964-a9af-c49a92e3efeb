import { Grid } from '../../grid/grid';
import React, { useEffect, useState } from 'react';
import getColumnDefs from './signed-off-column-defs';
import './signed-off.scss';

interface SignedOffReportsProps {
  fetchData: () => void;
  quickSearchParams: any;
  advancedSearchParams: any;
  data: any[];
  loading: boolean;
  totalCount: number;
  selectedColumns: any[];
  selectedRows: any[];
  onSelectRow: (row: any) => void;
  hasExportToExcelAccess: boolean;
}

const SignedOffReports = ({
  fetchData,
  quickSearchParams,
  advancedSearchParams,
  data,
  loading,
  totalCount,
  selectedColumns,
  selectedRows,
  onSelectRow,
  hasExportToExcelAccess,
}) => {
  const [columns, setColumns] = useState<any[]>([]);

  useEffect(() => {
    const columnDefs = getColumnDefs({
      selectedRows,
      onSelectRow,
      data,
      hasExportToExcelAccess,
    });
    const stickyColumnsCount = columnDefs.filter((col) => col.sticky).length;

    setColumns([...columnDefs.slice(0, stickyColumnsCount), ...selectedColumns]);
  }, [selectedColumns, selectedRows, data, hasExportToExcelAccess, onSelectRow]);

  return (
    <div className="signed-off container">
      <Grid
        isLoading={loading}
        columns={columns}
        data={data}
        showTopPagination
        totalCount={totalCount}
        showResultsCount
        fetchData={fetchData}
        defaultPageSize={10}
        hasStickyColumn
        resultsCount={totalCount}
        showPaginationLabel={false}
        quickSearchParams={quickSearchParams}
        advancedSearchParams={advancedSearchParams}
        pageSizeOptions={[10, 50, 100, 300]}
      />
    </div>
  );
};

export default SignedOffReports;