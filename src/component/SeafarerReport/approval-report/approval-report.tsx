import { Grid } from '../../grid/grid';
import React, { useEffect, useState } from 'react';
import approvalReportColumnDefs from './approval-report-column-defs';
import './approval-report.scss';

const ApprovalReport = ({
  fetchData,
  quickSearchParams,
  advancedSearchParams,
  data,
  loading,
  totalCount,
  selectedColumns,
  selectedRows,
  onSelectRow,
  hasExportToExcelAccess,
}) => {
  const stickyColumnsCount = approvalReportColumnDefs({
    selectedRows,
    onSelectRow,
    data,
    hasExportToExcelAccess,
  }).filter((col) => col.sticky).length;

  const [columns, setColumns] = useState<any[]>([]);

  useEffect(() => {
    setColumns([
      ...approvalReportColumnDefs({
        selectedRows,
        onSelectRow,
        data,
        hasExportToExcelAccess,
      }).slice(0, stickyColumnsCount),
      ...selectedColumns,
    ]);
  }, [selectedColumns, selectedRows]);

  return (
    <div className="approval-report container">
      <Grid
        isLoading={loading}
        columns={columns}
        data={data}
        showTopPagination={true}
        totalCount={totalCount}
        showResultsCount={true}
        resultsCount={totalCount}
        showPaginationLabel={false}
        fetchData={fetchData}
        defaultPageSize={10}
        hasStickyColumn={true}
        quickSearchParams={quickSearchParams}
        advancedSearchParams={advancedSearchParams}
        pageSizeOptions={[10, 50, 100, 300]}
      />
    </div>
  );
};

export default ApprovalReport;
