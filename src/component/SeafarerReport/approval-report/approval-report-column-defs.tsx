import React from 'react';
import { formatDate } from '../../../util/view-utils';
import {
  approvalStatusClassName,
  seafarerScreeningStatusReports,
} from '../../../types/seafarerReports';
import Checkbox, { CheckboxHeader } from '../../../component/common/checkbox';

const approvalReportColumnDefs = ({ selectedRows, onSelectRow, data, hasExportToExcelAccess }) => {
  const approvalReportDefs = [
    {
      Header: 'No.',
      Footer: null,
      disableSortBy: true,
      accessor: 'seafarerId',
      width: 60,
      maxWidth: 100,
      minWidth: 60,
      sticky: 'left',
      type: 'text',
      name: 'seafarerId',
    },
    {
      Header: 'HKID',
      Footer: null,
      accessor: 'hkid',
      Cell: ({ row }) => {
        return (
          <a href={`/seafarer/details/${row.original.seafarerId}/general`} target="_blank">
            {row.original.hkid}
          </a>
        );
      },
      width: 90,
      maxWidth: 120,
      minWidth: 90,
      sticky: 'left',
      type: 'text',
      name: 'hkid',
    },
    {
      Header: 'Name',
      Footer: null,
      accessor: 'name',
      width: 210,
      maxWidth: 240,
      minWidth: 210,
      sticky: 'left',
      type: 'text',
      name: 'name',
    },
    {
      Header: 'Vessel',
      Footer: null,
      accessor: 'vessel',
      Cell: ({ row }) => {
        return (
          <a
            href={`/vessel/ownership/details/${row.original.vesselOwnershipId}/general`}
            target="_blank"
          >
            {row.original.vessel}
          </a>
        );
      },
      width: 180,
      maxWidth: 240,
      minWidth: 180,
      order: 0,
      type: 'text',
      name: 'vessel',
    },
    {
      Header: 'Rank',
      Footer: null,
      accessor: 'rank',
      width: 120,
      maxWidth: 240,
      order: 1,
      type: 'text',
      name: 'rank',
    },
    {
      Header: 'Screening Status',
      Footer: null,
      accessor: 'screeningStatus',
      Cell: ({ cell }) => {
        return <span>{seafarerScreeningStatusReports[cell.value]}</span>;
      },
      width: 140,
      maxWidth: 240,
      minWidth: 140,
      order: 2,
      type: 'text',
      name: 'screeningStatus',
    },
    {
      Header: 'Approval Status',
      Footer: null,
      accessor: 'approvalStatus',
      Cell: ({ cell }) => {
        return (
          <span className={`approval-status-${approvalStatusClassName[cell.value]}`}>
            {cell.value}
          </span>
        );
      },
      width: 140,
      maxWidth: 240,
      minWidth: 140,
      order: 3,
      type: 'text',
      name: 'approvalStatus',
    },
    {
      Header: 'Approval Date',
      Footer: null,
      accessor: 'approvedDate',
      Cell: ({ cell }) => {
        return <span>{cell.value ? formatDate(cell.value) : ''}</span>;
      },
      width: 160,
      maxWidth: 240,
      minWidth: 160,
      order: 4,
      type: 'date',
      name: 'approvedDate',
    },
    {
      Header: 'Approved By',
      Footer: null,
      accessor: 'approvedBy',
      width: 160,
      maxWidth: 240,
      minWidth: 160,
      order: 5,
      type: 'text',
      name: 'approvedBy',
    },
    {
      Header: 'Nationality',
      Footer: null,
      accessor: 'nationality',
      width: 160,
      maxWidth: 240,
      minWidth: 160,
      order: 6,
      type: 'text',
      name: 'nationality',
    },

    {
      Header: 'Reporting Office',
      Footer: null,
      accessor: 'reportingOffice',
      width: 200,
      maxWidth: 240,
      minWidth: 200,
      order: 7,
      type: 'text',
      name: 'reportingOffice',
    },
    {
      Header: 'Recommended',
      Footer: null,
      accessor: 'recommended',
      width: 180,
      maxWidth: 240,
      minWidth: 180,
      order: 8,
      type: 'text',
      name: 'recommended',
    },
    {
      Header: 'Recommend Date',
      Footer: null,
      accessor: 'recommendedDate',
      Cell: ({ cell }) => {
        return <span>{cell.value ? formatDate(cell.value) : ''}</span>;
      },
      width: 180,
      maxWidth: 240,
      minWidth: 180,
      order: 8,
      type: 'date',
      name: 'recommendedDate',
    },

    {
      Header: 'Recommend By',
      Footer: null,
      accessor: 'recommendedBy',
      width: 200,
      maxWidth: 240,
      minWidth: 200,
      order: 9,
      type: 'text',
      name: 'recommendedBy',
    },
    {
      Header: 'Tech Group',
      Footer: null,
      accessor: 'techGroup',
      width: 200,
      maxWidth: 240,
      minWidth: 200,
      order: 10,
      type: 'text',
      name: 'techGroup',
    },
  ];

  if (hasExportToExcelAccess) {
    approvalReportDefs.unshift({
      id: 'checkbox',
      type: 'item',
      accessor: (row) => {
        return row.id ?? '---';
      },
      Cell: ({ row }) => {
        return <Checkbox row={row} selectedRows={selectedRows} onSelectRow={onSelectRow} />;
      },
      Header: () => {
        return <CheckboxHeader data={data} selectedRows={selectedRows} onSelectRow={onSelectRow} />;
      },
      sticky: 'left',
      width: 50,
      maxWidth: 60,
      minWidth: 50,
      Footer: null,
      disableSortBy: true,
    });
  }

  return approvalReportDefs;
};

export default approvalReportColumnDefs;
