import React from 'react';
import { Breadcrumb } from 'react-bootstrap';
import { Link } from 'react-router-dom';

interface Props {
  items: { title: string | JSX.Element; label: string; link: string }[];
  activeItem: string;
  onClick: (e: any) => void;
}

const BreadcrumbHeader = ({ items = [], activeItem, onClick }: Props) => {
  return (
    <Breadcrumb className="bread-crump-wrapper">
      {items.map((item) => {
        const isItemActive = item?.label === activeItem;
        const className = isItemActive ? 'breadcrumb-text' : 'inactive-breadcrumb-text';
        return (
          item && (
            <Breadcrumb.Item
              key={item.label}
              className={className}
              active={true}
              onClick={() => onClick(item.label)}
            >
              {isItemActive ? (
                <strong>{item.title}</strong>
              ) : (
                <Link to={item.link}>
                  <strong>{item.title}</strong>
                </Link>
              )}
            </Breadcrumb.Item>
          )
        );
      })}
    </Breadcrumb>
  );
};

export { BreadcrumbHeader };
