import { BreadcrumbHeader } from './BreadcrumbHeader';
import React, { useMemo } from 'react';
/*global Props, vesselName, isEdit, vesselOwnerShipId, eventTracker*/
/*eslint no-undef: "error"*/
interface Props {
  vesselName: string | undefined;
  isEdit: boolean;
  vesselOwnerShipId: number | string | undefined;
  // eslint-disable-next-line no-undef
  eventTracker: (type: string, value: string) => void;
}

const NavigationBar = ({
  vesselName = '',
  isEdit,
  vesselOwnerShipId = '',
  eventTracker,
}: Props) => {
  const breadCrumbsItems = useMemo(
    () =>
      isEdit
        ? [
            {
              title: <>Seafarer Reports &#8226; Modeller</>,
              label: 'To Seafarer Reports Modeller',
              link: '/seafarer-reports/modeller',
            },
            {
              title: vesselName ?? '- - -',
              label: 'Vessel Plan',
              link: `/seafarer-reports/modeller/${vesselOwnerShipId}`,
            },
            {
              title: 'Edit Plan',
              label: 'Edit Plan',
              link: '#',
            },
          ]
        : [
            {
              title: <>Seafarer Reports &#8226; Modeller</>,
              label: 'To Seafarer Reports Modeller',
              link: '/seafarer-reports/modeller',
            },
            {
              title: vesselName ?? '- - -',
              label: 'Vessel Plan',
              link: '#',
            },
          ],
    [vesselName],
  );
  return (
    <BreadcrumbHeader
      items={breadCrumbsItems}
      activeItem={isEdit ? 'Edit Plan' : 'Vessel Plan'}
      onClick={() => {
        eventTracker?.('breadCrumb', vesselName);
      }}
    />
  );
};

export default NavigationBar;
