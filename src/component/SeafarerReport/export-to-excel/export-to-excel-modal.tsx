import React, { useState } from 'react';
import { Form } from 'react-bootstrap';

import { ConfirmationModal } from '../../confirmation-modal/confirmation-modal';
import { ExportToExcelPayload } from '../../../pages/SeafarerReports';

const ExportToExcelModal = ({
  email,
  filteredCount,
  handleSubmit,
  onClose,
  selectedRowsCount,
}: {
  email: string;
  filteredCount: number;
  handleSubmit: (fn: (arg0: boolean) => void, payload: ExportToExcelPayload) => void;
  onClose: () => void;
  selectedRowsCount: number;
}) => {
  const [showExportToEmailModal, setShowExportToEmailModal] = useState(Boolean(selectedRowsCount));
  const [remarks, setRemarks] = useState('');
  const [exportButtonLoading, setExportButtonLoading] = useState(false);

  const handleExportSubmitClick = () => {
    setExportButtonLoading(true);
    handleSubmit(setExportButtonLoading, { remarks: remarks, exportedBy: email });
  };

  if (showExportToEmailModal) {
    return (
      <ConfirmationModal
        show={true}
        title={`Export ${
          selectedRowsCount > 0 ? selectedRowsCount : filteredCount
        } results to Excel`}
        confirmButtonLabel="Confirm"
        onConfirm={handleExportSubmitClick}
        dialogClassName="reports-export-to-excel-modal"
        cancelButtonLabel="Cancel"
        onClose={onClose}
        disableConfirm={exportButtonLoading}
      >
        <div>
          <Form.Label htmlFor="wages">The exported file will be sent to your email</Form.Label>
          <Form.Control
            id="email"
            name="email"
            type="text"
            value={email}
            disabled={true}
          ></Form.Control>
          <Form.Label htmlFor="remarks">Remarks (optional)</Form.Label>
          <Form.Control
            id="remarks"
            name="remarks"
            as="textarea"
            value={remarks}
            onChange={(e) => setRemarks(e.target.value)}
            maxLength={300}
            placeholder="Optional details about your export..."
          ></Form.Control>
        </div>
      </ConfirmationModal>
    );
  } else {
    return (
      <ConfirmationModal
        show={true}
        title={`Do you want to export all records?`}
        message="Please select the records you want to export, or click the top checkbox to export all records"
        confirmButtonLabel="Export All"
        onConfirm={() => setShowExportToEmailModal(true)}
        dialogClassName="reports-export-to-excel-modal"
        cancelButtonLabel="Cancel"
        onClose={onClose}
      />
    );
  }
};

export default ExportToExcelModal;
