import React from 'react';
import { formatDate } from '../../../util/view-utils';
import Checkbox, { CheckboxHeader } from '../../../component/common/checkbox';

const signedOnReportsColumnDefs = ({ selectedRows, onSelectRow, data, hasExportToExcelAccess }) => {
  const signedOnColumns = [
    {
      Header: 'No.',
      Footer: null,
      disableSortBy: true,
      accessor: 'seafarerId',
      width: 80,
      maxWidth: 100,
      minWidth: 80,
      sticky: 'left',
      type: 'text',
      name: 'seafarerId',
    },
    {
      Header: 'HKID',
      Footer: null,
      accessor: 'hkid',
      Cell: ({ row }) => {
        return (
          <a href={`/seafarer/details/${row.original.seafarerId}/general`} target="_blank">
            {row.original.hkid}
          </a>
        );
      },
      width: 100,
      maxWidth: 100,
      minWidth: 100,
      sticky: 'left',
      type: 'text',
      name: 'hkid',
    },
    {
      Header: 'Name',
      Footer: null,
      accessor: 'name',
      width: 210,
      minWidth: 210,
      maxWidth: 240,
      sticky: 'left',
      type: 'text',
      name: 'name',
    },
    {
      Header: 'Vessel',
      Footer: null,
      accessor: 'vessel',
      Cell: ({ row }) => {
        return (
          <a
            href={`/vessel/ownership/details/${row.original.vesselOwnershipId}/general`}
            target="_blank"
          >
            {row.original.vessel}
          </a>
        );
      },
      width: 130,
      maxWidth: 240,
      minWidth: 130,
      order: 0,
      type: 'text',
      name: 'vessel',
    },
    {
      Header: 'Rank',
      Footer: null,
      accessor: 'rank',
      width: 120,
      minWidth: 120,
      maxWidth: 240,
      order: 1,
      type: 'text',
      name: 'rank',
    },
    {
      Header: 'Date of joining',
      Footer: null,
      accessor: 'dateOfJoining',
      Cell: ({ cell }) => {
        return <span>{cell.value ? formatDate(cell.value) : ''}</span>;
      },
      width: 170,
      minWidth: 170,
      maxWidth: 240,
      order: 2,
      type: 'date',
      name: 'dateOfJoining',
    },
    {
      Header: 'Nationality',
      Footer: null,
      accessor: 'nationality',
      width: 130,
      minWidth: 130,
      maxWidth: 240,
      order: 3,
      type: 'text',
      name: 'nationality',
    },

    {
      Header: 'Reporting Office',
      Footer: null,
      accessor: 'reportingOffice',
      width: 220,
      minWidth: 220,
      maxWidth: 240,
      order: 4,
      type: 'text',
      name: 'reportingOffice',
    },
    {
      Header: 'Port',
      Footer: null,
      accessor: 'port',
      width: 160,
      minWidth: 160,
      maxWidth: 240,
      order: 5,
      type: 'text',
      name: 'port',
    },
    {
      Header: 'Owner',
      Footer: null,
      accessor: 'owner',
      width: 210,
      minWidth: 210,
      maxWidth: 240,
      order: 6,
      type: 'text',
      name: 'owner',
      disableSortBy: true,
    },
  ];

  if (hasExportToExcelAccess) {
    signedOnColumns.unshift({
      id: 'checkbox',
      type: 'item',
      accessor: (row) => {
        return row.id ?? '---';
      },
      Cell: ({ row }) => {
        return <Checkbox row={row} selectedRows={selectedRows} onSelectRow={onSelectRow} />;
      },
      Header: () => {
        return <CheckboxHeader data={data} selectedRows={selectedRows} onSelectRow={onSelectRow} />;
      },
      sticky: 'left',
      width: 50,
      maxWidth: 60,
      minWidth: 50,
      Footer: null,
      disableSortBy: true,
    });
  }

  return signedOnColumns;
};

export default signedOnReportsColumnDefs;
