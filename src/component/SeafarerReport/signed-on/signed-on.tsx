import { Grid } from '../../grid/grid';
import React, { useEffect, useState } from 'react';
import signedOnReportsColumnDefs from './signed-on-column-defs';
import './signed-on.scss';

const SignedOnReports = ({
  fetchData,
  quickSearchParams,
  advancedSearchParams,
  data,
  loading,
  totalCount,
  selectedColumns,
  selectedRows,
  onSelectRow,
  hasExportToExcelAccess,
}) => {
  const stickyColumnsCount = signedOnReportsColumnDefs({
    selectedRows,
    onSelectRow,
    data,
    hasExportToExcelAccess,
  }).filter((col) => col.sticky).length;

  const [columns, setColumns] = useState<any[]>([]);

  useEffect(() => {
    setColumns([
      ...signedOnReportsColumnDefs({
        selectedRows,
        onSelectRow,
        data,
        hasExportToExcelAccess,
      }).slice(0, stickyColumnsCount),
      ...selectedColumns,
    ]);
  }, [selectedColumns, selectedRows]);

  return (
    <div className="signed-on container">
      <Grid
        isLoading={loading}
        columns={columns}
        data={data}
        showTopPagination={true}
        totalCount={totalCount}
        showResultsCount={true}
        fetchData={fetchData}
        defaultPageSize={10}
        hasStickyColumn={true}
        resultsCount={totalCount}
        showPaginationLabel={false}
        quickSearchParams={quickSearchParams}
        advancedSearchParams={advancedSearchParams}
        pageSizeOptions={[10, 50, 100, 300]}
      />
    </div>
  );
};

export default SignedOnReports;
