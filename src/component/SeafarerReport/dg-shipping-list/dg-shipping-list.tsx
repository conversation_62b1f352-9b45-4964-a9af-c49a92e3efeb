import { Grid, GridColumns } from '../../grid/grid';
import React, { useEffect, useState } from 'react';
import dgShippingListColumnDefs from './dg-shipping-list-column-defs';
import './dg-shipping-list.scss';

const DgShippingList = ({
  fetchData,
  quickSearchParams,
  advancedSearchParams,
  data,
  loading,
  totalCount,
  selectedColumns,
  selectedRows,
  onSelectRow,
  hasExportToExcelAccess,
}) => {
  const stickyColumnsCount = dgShippingListColumnDefs({
    selectedRows,
    onSelectRow,
    data,
    hasExportToExcelAccess,
  }).filter((col) => col.sticky).length;

  const [columns, setColumns] = useState<GridColumns[]>([]);

  useEffect(() => {
    setColumns([
      ...dgShippingListColumnDefs({
        selectedRows,
        onSelectRow,
        data,
        hasExportToExcelAccess,
      }).slice(0, stickyColumnsCount),
      ...selectedColumns,
    ]);
  }, [selectedColumns, selectedRows]);

  return (
    <div className="dg-shipping-list container">
      <Grid
        isLoading={loading}
        columns={columns}
        data={data}
        showTopPagination={true}
        totalCount={totalCount}
        showResultsCount={true}
        resultsCount={totalCount}
        showPaginationLabel={false}
        fetchData={fetchData}
        defaultPageSize={10}
        hasStickyColumn={true}
        quickSearchParams={quickSearchParams}
        advancedSearchParams={advancedSearchParams}
        pageSizeOptions={[10, 50, 100, 300]}
      />
    </div>
  );
};

export default DgShippingList;
