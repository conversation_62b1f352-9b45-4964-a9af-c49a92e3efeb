import { formatDate } from '../../../util/view-utils';
import React from 'react';
import { customSortDateFields } from '../../../model/utils';
import Checkbox, { CheckboxHeader } from '../../../component/common/checkbox';

const dgShippingListColumnDefs = ({ selectedRows, onSelectRow, data, hasExportToExcelAccess }) => {
  const dgShippingColumns = [
    {
      Header: 'No.',
      Footer: null,
      disableSortBy: true,
      accessor: 'seafarerId',
      width: 80,
      maxWidth: 100,
      minWidth: 80,
      sticky: 'left',
      type: 'text',
      name: 'seafarerId',
    },
    {
      Header: 'Name of Seafarer',
      Footer: null,
      accessor: 'name',
      width: 210,
      minWidth: 210,
      maxWidth: 240,
      sticky: 'left',
      type: 'text',
      name: 'name',
    },
    {
      Header: 'HKID of Seafarer',
      Footer: null,
      accessor: 'hkid',
      Cell: ({ row }) => {
        return (
          <a href={`/seafarer/details/${row.original.seafarerId}/general`} target="_blank">
            {row.original.hkid}
          </a>
        );
      },
      width: 140,
      minWidth: 140,
      maxWidth: 180,
      sticky: 'left',
      type: 'text',
      name: 'hkid',
    },
    {
      Header: 'INDoS No.',
      Footer: null,
      accessor: 'indosNo',
      width: 130,
      maxWidth: 240,
      minWidth: 130,
      type: 'text',
      name: 'indosNo',
      order: 0,
    },
    {
      Header: 'CDC No.',
      Footer: null,
      accessor: 'cdcNo',
      width: 150,
      maxWidth: 240,
      minWidth: 150,
      type: 'text',
      name: 'cdcNo',
      order: 1,
    },
    {
      Header: 'Rank',
      Footer: null,
      accessor: 'rank',
      width: 120,
      maxWidth: 240,
      minWidth: 120,
      type: 'text',
      name: 'rank',
      order: 2,
    },
    {
      Header: 'Name of Vessel',
      Footer: null,
      accessor: 'vessel',
      Cell: ({ row }) => {
        return (
          <a
            href={`/vessel/ownership/details/${row.original.vesselOwnershipId}/general`}
            target="_blank"
          >
            {row.original.vessel}
          </a>
        );
      },
      width: 190,
      maxWidth: 240,
      minWidth: 190,
      type: 'text',
      name: 'vessel',
      order: 3,
    },
    {
      Header: 'Flag of Vessel',
      Footer: null,
      accessor: 'vesselFlag',
      width: 140,
      maxWidth: 240,
      minWidth: 140,
      type: 'text',
      name: 'vesselFlag',
      order: 4,
    },
    {
      Header: 'Date of Commencement of Contract',
      Footer: null,
      accessor: 'contractStartDate',
      Cell: ({ cell }) => {
        return <span>{cell.value ? formatDate(cell.value) : ''}</span>;
      },
      sortType: (row1, row2) => customSortDateFields(row1, row2, 'contractStartDate'),
      width: 220,
      maxWidth: 240,
      minWidth: 220,
      type: 'date',
      name: 'contractStartDate',
      order: 5,
    },
    {
      Header: 'Date of sign on',
      Footer: null,
      accessor: 'signOnDate',
      Cell: ({ cell }) => {
        return <span>{cell.value ? formatDate(cell.value) : ''}</span>;
      },
      sortType: (row1, row2) => customSortDateFields(row1, row2, 'signOnDate'),
      width: 220,
      maxWidth: 240,
      minWidth: 220,
      type: 'date',
      name: 'signOnDate',
      order: 6,
    },
    {
      Header: 'Date of signing off',
      Footer: null,
      accessor: 'signOffDate',
      Cell: ({ cell }) => {
        return <span>{cell.value ? formatDate(cell.value) : ''}</span>;
      },
      sortType: (row1, row2) => customSortDateFields(row1, row2, 'signOffDate'),
      width: 220,
      maxWidth: 240,
      minWidth: 220,
      type: 'date',
      name: 'signOffDate',
      order: 7,
    },
    {
      Header: 'Date of Completion of Contract / arriving India',
      Footer: null,
      accessor: 'contractEndDate',
      Cell: ({ cell }) => {
        return <span>{cell.value ? formatDate(cell.value) : ''}</span>;
      },
      sortType: (row1, row2) => customSortDateFields(row1, row2, 'contractEndDate'),
      width: 240,
      maxWidth: 240,
      minWidth: 240,
      type: 'date',
      name: 'contractEndDate',
      order: 8,
    },
    {
      Header: 'Remarks if any',
      Footer: null,
      accessor: 'remarks',
      width: 240,
      maxWidth: 240,
      minWidth: 240,
      type: 'text',
      name: 'remarks',
      order: 9,
    },
  ];

  if (hasExportToExcelAccess) {
    dgShippingColumns.unshift({
      id: 'checkbox',
      type: 'item',
      accessor: (row) => {
        return row.id ?? '---';
      },
      Cell: ({ row }) => {
        return <Checkbox row={row} selectedRows={selectedRows} onSelectRow={onSelectRow} />;
      },
      Header: () => {
        return <CheckboxHeader data={data} selectedRows={selectedRows} onSelectRow={onSelectRow} />;
      },
      sticky: 'left',
      width: 50,
      maxWidth: 60,
      minWidth: 50,
      Footer: null,
      disableSortBy: true,
    });
  }

  return dgShippingColumns;
};

export default dgShippingListColumnDefs;
