import {
  AccountingCurrency,
  DEFAULT_CURRENCY_MODELLER,
} from '../../../controller/search-controller';
import React from 'react';

export const modellerFooterItems = (row, plannedWagesUnit = DEFAULT_CURRENCY_MODELLER) => [
  {
    Header: (
      <div className="text-left pl-4">
        <span className="ml-1">Total</span>
      </div>
    ),
    target_id: 'id',
    value: '',
    width: 80,
  },
  {
    Header: 'Planned',
    target_id: 'planned_number',
    value: row?.planned_total_number,
    type: 'text',
    width: 140,
  },
  {
    Header: 'Actual',
    target_id: 'actual_number',
    value: row?.actual_total_number,
    type: 'text',
    width: 140,
  },
  {
    Header: `Planned Wages (${AccountingCurrency.find((a) => a.id === plannedWagesUnit)?.value})`,
    target_id: 'planned_wages',
    value: row?.planned_wages,
    type: 'text',
    width: 208,
  },
  {
    Header: `Actual Wages (${AccountingCurrency.find((a) => a.id === plannedWagesUnit)?.value})`,
    target_id: 'actual_wages',
    value: row?.actual_wages,
    type: 'text',
    width: 190,
  },
  {
    Header: '\xa0'.repeat(5),
    target_id: 'modeller-report-tbl-action',
    value: '\xa0'.repeat(5),
    width: 40,
  },
];

export const vesselPlanFooterItems = (row, plannedWagesUnit = DEFAULT_CURRENCY_MODELLER) => [
  {
    Header: (
      <div className="text-left">
        <span>Total</span>
      </div>
    ),
    target_id: 'vessel-plan-no',
    name: row.last_updated_by_user_info !== undefined ? row.last_updated_by_user_info : '- - -',
    value: row.updated_at,
    width: 400,
  },
  {
    Header: 'Planned',
    target_id: 'vessel-plan-planned-number',
    value: row?.planned_number ?? 0,
    type: 'text',
    width: 140,
  },
  {
    Header: 'Actual',
    target_id: 'vessel-plan-actual-number',
    value: row?.actual_number,
    type: 'text',
    width: 140,
  },
  {
    Header: `Planned Wages (${AccountingCurrency.find((a) => a.id === plannedWagesUnit)?.value})`,
    target_id: 'vessel-plan-planned-wages',
    value: row?.planned_wages ?? '0.00',
    type: 'text',
    width: 208,
  },
  {
    Header: `Actual Wages (${AccountingCurrency.find((a) => a.id === plannedWagesUnit)?.value})`,
    target_id: 'vessel-plan-actual-wages',
    value: row?.actual_wages,
    type: 'text',
    width: 190,
  },
];
