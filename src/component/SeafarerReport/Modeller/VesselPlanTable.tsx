/* eslint-disable react/display-name */
/* eslint-disable react/prop-types */
import React from 'react';
import { useTable, useSortBy, useFlexLayout } from 'react-table';
import { useSticky } from 'react-table-sticky';
import { v4 as uuid } from 'uuid';
import Spinner from '../../common/Spinner';
const { Icon } = styleGuide;
import styleGuide from '../../../styleGuide';
import { columnSortIconName } from '../../../util/view-utils';

const VesselPlanTable = ({ columns, data, loading, dataTestId = '', tableRef, isEditVesselPlan = false }) => {
  const { getTableProps, getTableBodyProps, headerGroups, prepareRow, rows } = useTable(
    {
      columns,
      data,
    },
    useSortBy,
    useFlexLayout,
    useSticky,
  );

  return (
    <>
      <div className="seafarer-table" data-testid={dataTestId}>
        <div {...getTableProps()} className={`table vessel-plan sticky`} ref={tableRef}>
          <div className="header">
            {headerGroups.map((headerGroup) => (
              <div key={uuid()} {...headerGroup.getHeaderGroupProps()} className="tr">
                {headerGroup.headers.map((column) => {
                  const thProps = column.getHeaderProps(column.getSortByToggleProps());
                  return (
                    <div
                      key={column.id}
                      {...thProps}
                      className={`th ${column?.className ?? ''}`}
                      id={column?.id}
                    >
                      {column.render('Header')}
                      <span>
                        {column.canSort && (
                          <Icon icon={columnSortIconName(column)} size={20} className="default" />
                        )}
                      </span>
                    </div>
                  );
                })}
              </div>
            ))}
          </div>
          {data.length > 0 ? (
            <>
              {loading && <Spinner alignClass={`load-spinner`} />}
              <div {...getTableBodyProps()} className="body">
                {rows.map((row) => {
                  prepareRow(row);
                  return (
                    <div key={row.id} {...row.getRowProps()} className="tr">
                      {row.cells.map((cell) => {
                        const tdProps = cell.getCellProps();
                        return (
                          <div
                            key={tdProps.key}
                            {...tdProps}
                            className={`td ${isEditVesselPlan ? '' : 'sort-icon-compensation'}`}
                          >
                            {cell.render('Cell')}
                          </div>
                        );
                      })}
                    </div>
                  );
                })}
              </div>
            </>
          ) : (
            <div className="no-result-found">
              {' '}
              <Icon icon="alert" className="alert-icon-no-search" />
              <div>
                {' '}
                <b>No result found</b>
              </div>{' '}
            </div>
          )}
        </div>
      </div>
      <div className="footer-compensation" />
    </>
  );
};

export default VesselPlanTable;
