/* eslint-disable react/display-name */
// eslint-disable-next-line no-unused-vars
import { FooterProps } from '@src/types/seafarerReports';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import { dateAsString } from '../../../model/utils';

const ModellerFooter = ({ columns, tableRef, rightOffset = 32 }: FooterProps) => {
  const [positionMap, setPositionMap] = useState<any>();
  const setFooterColumnsPositon = _.debounce(() => {
    const posData = columns.reduce((a, i) => {
      const headerRef = document.getElementById(i.target_id);
      const pos = headerRef?.getBoundingClientRect().right;
      if (pos) {
        return { ...a, [i.target_id]: pos };
      }
      return a;
    }, {});
    setPositionMap(posData);
  }, 0);

  useEffect(() => {
    const timer = setTimeout(setFooterColumnsPositon, 0);
    return () => {
      clearTimeout(timer);
    };
  }, []);

  useEffect(() => {
    window.addEventListener('resize', setFooterColumnsPositon);
    // @ts-ignore
    tableRef?.current?.addEventListener('scroll', setFooterColumnsPositon);
    return () => {
      setFooterColumnsPositon.cancel();
      window.removeEventListener('resize', setFooterColumnsPositon);
      // @ts-ignore
      tableRef?.current?.removeEventListener('scroll', setFooterColumnsPositon);
    };
  });

  return (
    <div className="modeller-footer px-0">
      {columns.map((i) => {
        const width = i.width;
        const base = _.get(positionMap, `[${i.target_id}]`, 10);
        if (i.target_id === 'vessel-plan-no') {
          return (
            <div
              key={i.target_id}
              className="pl-2 modeller-footer-columns"
              data-testid={`footer-${i.target_id}`}
              style={{
                left: `${base - 30 - rightOffset }px`,
                width: `${width}px`,
              }}
            >
              <div className="text-left">{i.Header}</div>
              <div className="text-left footer-item-edited-by">{`Edited by ${
                i.name
              } on ${dateAsString(i.value)}`}</div>
            </div>
          );
        } else {
          return (
            <div
              key={i.target_id}
              className="pl-2 modeller-footer-columns"
              data-testid={`footer-${i.target_id}`}
              style={{
                left: `${base - width - rightOffset}px`,
                width: `${width}px`,
              }}
            >
              <div className="justify-content-end">{i.Header}</div>
              <div className="justify-content-end">{i.value}</div>
            </div>
          );
        }
      })}
      <div className="dummy-footer-padding-compensation" />
    </div>
  );
};

export default ModellerFooter;
