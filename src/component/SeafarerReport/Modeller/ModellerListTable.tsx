/* eslint-disable react/display-name */
/* eslint-disable react/prop-types */
import React, { useMemo, useEffect, useRef } from 'react';
import { Row } from 'react-bootstrap';
import { useTable, useSortBy, usePagination, useFlexLayout } from 'react-table';
import { useSticky } from 'react-table-sticky';
import { v4 as uuid } from 'uuid';
import Spinner from '../../common/Spinner';
import styleGuide from '../../../styleGuide';
import {
  getPageTableState,
  storePageNumber,
  storePageSize,
} from '../../../util/local-storage-helper';
import PaginationBar from '../../seafarerList/PaginationBar';
import { viewEditAccessor, viewReportAccessor } from './MenuList';
import { columnSortIconName } from '../../../util/view-utils';
const { Icon } = styleGuide;

const ModellerTable = React.memo(
  ({
    tabName,
    fetchData,
    quickSearchParams,
    advancedSearchParams,
    data,
    pageCount,
    selectedColumns,
    loading,
    init_sort,
    modellerTotalCount,
    tableRef,
    eventTracker,
    roleConfig,
  }) => {
    selectedColumns.forEach((e) => {
      if (e.id === 'modeller-report-tbl-view-report') {
        e.accessor = (row) =>
          viewReportAccessor(row.vessel_ownership_id, row.vessel_name, eventTracker);
      } else if (e.id === 'modeller-report-tbl-action') {
        e.accessor = (row, index) =>
          roleConfig?.seafarer?.edit?.reportModeller ? (
            viewEditAccessor(row.vessel_ownership_id, index, row.vessel_name, eventTracker)
          ) : (
            <div></div>
          );
      } else {
        return e;
      }
    });
    return (
      <div className="seafarer-table">
        <Table
          tabName={tabName}
          fetchData={fetchData}
          quickSearchParams={quickSearchParams}
          advancedSearchParams={advancedSearchParams}
          columns={selectedColumns}
          data={data}
          pageCount={pageCount}
          loading={loading}
          init_sort={init_sort}
          modellerTotalCount={modellerTotalCount}
          tableRef={tableRef}
        />
      </div>
    );
  },
);

const Table = React.memo(
  ({
    tabName,
    columns,
    data,
    fetchData,
    // visitSeafarer,
    pageCount: controlledPageCount,
    // eventTracker,
    loading,
    quickSearchParams,
    advancedSearchParams,
    init_sort,
    modellerTotalCount,
    tableRef,
  }) => {
    const defaultColumn = useMemo(
      () => ({
        minWidth: 80,
        width: 120,
      }),
      [],
    );

    const {
      getTableProps,
      getTableBodyProps,
      headerGroups,
      prepareRow,
      page,
      canPreviousPage,
      canNextPage,
      pageCount,
      gotoPage,
      setPageSize,
      state: { pageIndex, pageSize, sortBy },
    } = useTable(
      {
        columns,
        data,
        defaultColumn,
        initialState: { sortBy: init_sort },
        manualPagination: true,
        manualSortBy: true,
        autoResetPage: false,
        autoResetSortBy: false,
        pageCount: controlledPageCount,
      },
      useSortBy,
      usePagination,
      useFlexLayout,
      useSticky,
    );

    const filterPages = (visiblePages, totalPages) =>
      visiblePages.filter((page) => page <= totalPages);
    const getVisiblePages = (page, total) => {
      if (total < 7) {
        return filterPages([1, 2, 3, 4, 5, 6], total);
      }
      if (page % 5 >= 0 && page >= 4 && page + 2 < total) {
        return [1, page, page + 1, page + 2, total];
      }
      if (page % 5 >= 0 && page > 4 && page + 2 >= total) {
        return [1, total - 3, total - 2, total - 1, total];
      }
      return [1, 2, 3, 4, 5, total];
    };
    const visiblePages = getVisiblePages(pageIndex, pageCount);
    const firstUpdate = useRef(true);

    const resetPage = async (page_no = 0, page_size = 10) => {
      setPageSize(page_size);
      await fetchData({ pageSize: page_size, sortBy, pageIndex: page_no });
      gotoPage(page_no);
    };

    useEffect(() => {
      (async function reset_page() {
        const { pageIndex, pageSize } = getPageTableState(tabName);
        await resetPage(pageIndex, pageSize);
      })();
    }, [sortBy]);

    useEffect(() => {
      if (firstUpdate.current) {
        firstUpdate.current = false;
        return;
      }
      (async function reset_page() {
        const { pageSize } = getPageTableState(tabName);
        await resetPage(0, pageSize);
      })();
    }, [tabName, quickSearchParams, advancedSearchParams]);

    const pageSwitch = async (page_no) => {
      await fetchData({ pageSize, sortBy, pageIndex: page_no });
      gotoPage(page_no);
      storePageNumber(tabName, page_no);
    };
    const pageSizeSwitch = async (page_size) => {
      //Internally pageIndex gets recalibrated as follows
      const new_index = Math.floor((pageIndex * pageSize) / page_size);
      setPageSize(page_size);
      await fetchData({ pageIndex: new_index, sortBy, pageSize: page_size });
      storePageSize(tabName, page_size);
      storePageNumber(tabName, new_index);
    };

    return (
      <>
        <hr className="hr-1px" />
        <Row>
          <PaginationBar
            className="top-pagination-bar"
            pageSwitch={pageSwitch}
            pageSizeSwitch={pageSizeSwitch}
            canPreviousPage={canPreviousPage}
            canNextPage={canNextPage}
            visiblePages={visiblePages}
            pageSize={pageSize}
            pageIndex={pageIndex}
          />
          <div className="seafarer-list-count">
            <b>{modellerTotalCount}</b> Results
          </div>
        </Row>
        {loading || modellerTotalCount > 0 ? (
          <>
            <div {...getTableProps()} className="table sticky" ref={tableRef}>
              <div className="header">
                {headerGroups.map((headerGroup) => (
                  <div key={uuid()} {...headerGroup.getHeaderGroupProps()} className="tr">
                    {headerGroup.headers.map((column) => {
                      const thProps = column.getHeaderProps(column.getSortByToggleProps());
                      return (
                        <div
                          key={column.id}
                          {...thProps}
                          className={`th ${column?.className ?? ''}`}
                          id={column?.id}
                        >
                          {column.render('Header')}
                          <span>
                            {column.canSort && (
                              <Icon
                                icon={columnSortIconName(column)}
                                size={20}
                                className="default"
                              />
                            )}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                ))}
              </div>
              {loading && <Spinner alignClass={`load-spinner`} />}
              <div {...getTableBodyProps()} className="body">
                {page.map((row) => {
                  prepareRow(row);
                  const seafarerId = row.original.id ?? undefined;
                  return (
                    <div key={seafarerId} {...row.getRowProps()} className="tr">
                      {row.cells.map((cell) => {
                        const tdProps = cell.getCellProps();
                        return (
                          <div key={tdProps.key} {...tdProps} className="td sort-icon-compensation">
                            {cell.render('Cell')}
                          </div>
                        );
                      })}
                    </div>
                  );
                })}
              </div>
            </div>
            <div className="footer-compensation" />
          </>
        ) : (
          <div className="no-result-found">
            {' '}
            <Icon icon="alert" className="alert-icon-no-search" />
            <div>
              {' '}
              <b>No result match your criteria</b>
            </div>{' '}
          </div>
        )}
      </>
    );
  },
);

export default ModellerTable;
