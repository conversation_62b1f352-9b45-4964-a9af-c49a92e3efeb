import React, { useEffect, useState, useRef } from 'react';
import { useHistory } from 'react-router-dom';
import _ from 'lodash';
import { Container, Form } from 'react-bootstrap';
import { Formik } from 'formik';
import Spinner from '../../common/Spinner';
import VesselPlanTable from './VesselPlanTable';
import DropDownSearchControl from '../../../../src/component/AddSeafarer/CountryNationalityDropDownControl';
import seafarerReportService from '../../../../src/service/seafarer-report-service';
import BottomButton from '../../../../src/component/advanced_search/BottomButton';
import { vesselEditPlanColumns } from '../../../../src/component/SeafarerReport/Modeller/MenuList';
import { vesselEditPlanFormSchema } from '../../../../src/model/SeafarerSchemaValidation';
import { differenceArraysOfObjects } from '../../../../src/util/form-utils';
import {
  SeafarerNationality,
  SeafarerReportModellerDetails,
  SeafarerReportModellerDetailsErrors,
} from '../../../../src/types/seafarerInterfaces';
import AddSeafarerController from '../../../../src/controller/add-seafarer-controller';
import ErrorDisplayModal from '../../common/ErrorDisplayModal';

// eslint-disable-next-line no-undef
interface Props {
  // eslint-disable-next-line no-undef
  vesselOwnershipId: number;
  // eslint-disable-next-line no-undef
  vesselPlanDetails: SeafarerReportModellerDetails[];
  // eslint-disable-next-line no-undef
  eventTracker: (type: string, value: string) => void;
  // eslint-disable-next-line no-undef
  vesselName: string;
  // eslint-disable-next-line no-undef
  plannedWagesUnit: string;
}

export const EditPlanForm = ({
  vesselOwnershipId,
  vesselPlanDetails,
  vesselName,
  eventTracker,
  plannedWagesUnit,
}: Props) => {
  const [formData, setFormData] = useState<SeafarerReportModellerDetails[]>([]);
  const [initialData, setInitialData] = useState<SeafarerReportModellerDetails[]>([]);
  const [nationality, setNationality] = useState<SeafarerNationality[]>([]);
  const [formErrors, setFormErrors] = useState<SeafarerReportModellerDetailsErrors>();
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitDisabled, setIsSubmitDisabled] = useState(true);
  const [modalMessage, setModalMessage] = useState(null);
  const tableRef = useRef(null);
  const history = useHistory();

  const onHideModalMessage = () => setModalMessage(null);

  const loadDefaultData = async () => {
    setIsLoading(true);
    try {
      const controller = new AddSeafarerController();
      const response = await controller.loadDropDownData();
      const nationalityData = response.nationalities;

      const uniqueRankIds: number[] = [];

      const uniqueVesselPlanDetails = vesselPlanDetails.filter(
        (e: SeafarerReportModellerDetails) => {
          const isDuplicate = uniqueRankIds.includes(e.rank_id);

          if (!isDuplicate) {
            uniqueRankIds.push(e.rank_id);
            return true;
          }

          return false;
        },
      );

      let data = null;
      if (uniqueVesselPlanDetails) {
        uniqueVesselPlanDetails.sort((a: SeafarerReportModellerDetails, b: SeafarerReportModellerDetails) =>
          a.rank.sortpriority - b.rank.sortpriority,
        );
        data = uniqueVesselPlanDetails.map((e: SeafarerReportModellerDetails, i: number) => ({
          ...e,
          id: i + 1,
        }));
      }
        
      if (data && data.length > 0) {
        const errorData = {
          vessel_plan_details: data.map((e, i) => ({
            id: i + 1,
            planned_wages: null,
            planned_number: null,
          })),
        };
        const initData = data.map((e: any) => {
          if (e.planned_number === null) {
            e.planned_number = 0;
          }
          if (e.planned_wages === null) {
            e.planned_wages = '0.00';
          }
          return e;
        });
        setInitialData(_.cloneDeep(data));
        setFormData(initData);
        setFormErrors(errorData);
      }
      if (nationalityData && nationalityData.length > 0) {
        setNationality(nationalityData);
      }
    } catch (err) {
      console.log('err', err);
    } finally {
      setIsLoading(false);
      setIsSubmitDisabled(false);
    }
  };

  useEffect(() => {
    (async () => {
      await loadDefaultData();
    })();
  }, []);

  const onSubmitValues = async (values: any[]) => {
    eventTracker?.('editVesselPlanSaveButton', vesselName);
    const initData = initialData.map((e) => ({
      id: e.id,
      planned_number: e.planned_number,
      planned_wages: e.planned_wages,
      planned_nationality_id: e.planned_nationality_id,
    }));

    const patchBodyFormValues = differenceArraysOfObjects(initData, values);
    const patchBodyModellerDetails = patchBodyFormValues.filter(
      (e) =>
        _.has(e, 'planned_wages') ||
        _.has(e, 'planned_number') ||
        _.has(e, 'planned_nationality_id'),
    );
    const patchBody = {
      seafarer_report_modeller_details: patchBodyModellerDetails,
    };
    try {
      const response = await seafarerReportService.patchVesselPlan(vesselOwnershipId, patchBody);
      if (response && response?.status === 201) {
        history.push(`/seafarer-reports/modeller/${vesselOwnershipId}`);
      }
    } catch (err) {
      console.error(err);
      setModalMessage(_.get(err, 'response.data', 'System Error'));
      setIsSubmitDisabled(false);
    }
  };

  return (
    <>
      <Container className="add-pre-joining-details-container">
        {!isLoading && formData && formData.length > 0 && vesselOwnershipId ? (
          <Formik
            validationSchema={vesselEditPlanFormSchema}
            initialValues={formData}
            onSubmit={onSubmitValues}
          >
            {({ setFieldValue, validateForm, setFieldError }) => {
              const handleChange = (event: React.ChangeEvent<HTMLInputElement>, id: number) => {
                const targetName = event?.target?.name;
                const targetValue = event?.target?.value;

                if (targetName === 'planned_nationality') {
                  setFieldValue(targetName, targetValue || null);
                } else if (targetName === 'planned_wages') {
                  const nullAmount = '0.00';
                  setFieldValue(targetName, targetValue || nullAmount);
                } else {
                  setFieldValue(targetName, targetValue || 0);
                }

                const element = formData.find(
                  (e: SeafarerReportModellerDetails) => e.id === _.toInteger(id),
                );
                const index = element ? formData.indexOf(element) : id - 1;
                const data = _.cloneDeep(formData);
                if (targetName === 'planned_nationality') {
                  const nationalitySelected = nationality
                    ? nationality.find((e: any) => e.id === _.toInteger(targetValue))
                    : undefined;
                  if (nationalitySelected) {
                    data[index].planned_nationality_id = nationalitySelected.id;
                  } else {
                    data[index].planned_nationality_id = null;
                  }
                } else if (targetName === 'planned_wages') {
                  data[index].planned_wages = _.toNumber(targetValue);
                } else {
                  data[index].planned_number = _.toNumber(targetValue);
                }
                setFormData(data);
              };

              const handleSubmit = async () => {
                setIsSubmitDisabled(true);
                const errorObj = formErrors;
                const valuesToBeValidated = formData.map((e: SeafarerReportModellerDetails) => ({
                  id: e.id,
                  rank_id: e.rank_id,
                  planned_wages: e.planned_wages,
                  planned_number: e.planned_number,
                  planned_nationality_id: e.planned_nationality_id,
                }));

                const validationData = {
                  vessel_plan_details: valuesToBeValidated,
                };

                const errors = await validateForm(validationData);

                if (errors && _.has(errors, 'vessel_plan_details')) {
                  _.get(errors, 'vessel_plan_details').forEach((e: any, i: number) => {
                    if (_.has(e, 'planned_wages')) {
                      if (errorObj) {
                        errorObj.vessel_plan_details[i] = e;
                      }
                    }
                  });
                  setFieldError('', '');
                  setFormErrors(errorObj);
                  setIsSubmitDisabled(false);
                } else {
                  await onSubmitValues(valuesToBeValidated);
                }
              };

              const handleFocus = (event: React.FocusEvent<HTMLInputElement>) => {
                event?.target?.select();
              };

              const blockInvalidChar = (e: React.KeyboardEvent<HTMLInputElement>) => {
                let blockedChars = ['e', 'E', '+', '-'];
                if (e?.target?.name === 'planned_number') {
                  blockedChars.push('.');
                }
                if (blockedChars.includes(e.key)) {
                  e.preventDefault();
                }
                const targetValue = e?.target?.value;
                if (targetValue === '0' && ['0'].includes(e.key)) {
                  e.preventDefault();
                }
              };

              const convertColumns = (columns: any) => {
                return columns.map((e: any) => {
                  if (e.name === 'planned_wages') {
                    e.accessor = function plannedWagesAccessor(row: any) {
                      const index = row.id - 1;
                      return (
                        <Form.Row className="">
                          <Form.Control
                            id={row.id}
                            className="update-wages-table-input-field"
                            value={row.planned_wages}
                            name="planned_wages"
                            onFocus={handleFocus}
                            min={0}
                            step={0.01}
                            type="number"
                            placeholder=""
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                              handleChange(e, row.id)
                            }
                            onKeyDown={blockInvalidChar}
                          />
                          {formErrors?.vessel_plan_details[index]?.planned_wages && (
                            <div className="invalid-feedback d-block">
                              {formErrors.vessel_plan_details[index].planned_wages}
                            </div>
                          )}
                        </Form.Row>
                      );
                    };
                  } else if (e.name === 'planned_number') {
                    e.accessor = function plannedNumberAccessor(row: any) {
                      const index = row.id - 1;
                      return (
                        <Form.Row className="justify-content-end">
                          <Form.Control
                            id={row.id}
                            className="update-wages-table-input-field w-50"
                            value={row.planned_number}
                            name="planned_number"
                            onFocus={handleFocus}
                            min={0}
                            step={1}
                            type="number"
                            placeholder=""
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                              handleChange(e, row.id)
                            }
                            onKeyDown={blockInvalidChar}
                          />
                          {formErrors?.vessel_plan_details[index]?.planned_number && (
                            <div className="invalid-feedback d-block">
                              {formErrors.vessel_plan_details[index].planned_number}
                            </div>
                          )}
                        </Form.Row>
                      );
                    };
                  } else if (e.name === 'planned_nationality') {
                    e.accessor = function plannedWagesAccessor(row: any) {
                      return (
                        <Form.Row className="justify-content-center px-1">
                          <DropDownSearchControl
                            name="planned_nationality"
                            selectedValue={row.planned_nationality_id}
                            dropDownValues={nationality}
                            placeholder="Please Select"
                            onInputChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                              handleChange(e, row.id)
                            }
                            isInvalid={false}
                            testID={'new_nationality'}
                            disabled={false}
                          />
                        </Form.Row>
                      );
                    };
                  }
                  return e;
                });
              };

              return (
                <Form>
                  <VesselPlanTable
                    columns={convertColumns(
                      vesselEditPlanColumns({ plannedWagesUnit: plannedWagesUnit }),
                    )}
                    data={formData}
                    loading={false}
                    tableRef={tableRef}
                    dataTestId="vessel-plan-table"
                    isEditVesselPlan={true}
                  />
                  <BottomButton
                    title={'Save'}
                    testID="form-seafarer-save-button"
                    onClick={handleSubmit}
                    disabled={isSubmitDisabled}
                  />
                </Form>
              );
            }}
          </Formik>
        ) : (
          <div className="mt-5">
            {' '}
            <Spinner />{' '}
          </div>
        )}
      </Container>
      <ErrorDisplayModal onHideModalMessage={onHideModalMessage} modalMessage={modalMessage} />
    </>
  );
};
