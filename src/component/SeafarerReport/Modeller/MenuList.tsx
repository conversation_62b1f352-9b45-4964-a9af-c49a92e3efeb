import React, { SyntheticEvent } from 'react';
import styleGuide from '../../../styleGuide';
const { Icon } = styleGuide;
import { Link } from 'react-router-dom';
import VesselNameLink from '../../CrewList/VesselNameLink';
import { Dash } from '../../../model/utils';
import Checkbox, { CheckboxHeader } from '../../../component/common/checkbox';
import {
  AccountingCurrency,
  DEFAULT_CURRENCY_MODELLER,
} from '../../../controller/search-controller';

export const viewReportAccessor = (
  vesselOwnerShipId: number,
  vesselName: string,
  eventTracker: null | ((type: string, value: string) => {}) = null,
) => {
  const clickHandler = (event: SyntheticEvent) => {
    eventTracker?.('modellerReportVesselPlanLink', vesselName);
    event.stopPropagation();
  };
  return (
    <Link to={`modeller/${vesselOwnerShipId}`} onClick={clickHandler}>
      <u>View</u>
    </Link>
  );
};

export const viewEditAccessor = (
  vesselOwnerShipId: number,
  index: number,
  vesselName: string,
  eventTracker: null | ((type: string, value: string) => {}) = null,
) => {
  const clickHandler = (event: SyntheticEvent) => {
    eventTracker?.('modellerReportPencilEditButton', vesselName);
    event.stopPropagation();
  };
  return (
    <Link
      to={`modeller/${vesselOwnerShipId}/edit`}
      className="action-column action-button-edit-color"
      data-testid={`action-${index}-btn`}
      onClick={clickHandler}
    >
      <Icon icon="pencil" size={20} className="mr-3" style={{ cursor: 'pointer' }} />
    </Link>
  );
};

export const getModellerItems = ({
  plannedWagesUnit = DEFAULT_CURRENCY_MODELLER,
  selectedRows,
  onSelectRow,
  data,
  hasExportToExcelAccess,
}) => {
  const modellerColumnDefs = [
    {
      Header: 'No.',
      id: 'id',
      accessor: function numberShowAccessor(row) {
        return <div>{row.id}</div>;
      },
      name: 'id',
      type: 'text',
      order: 1,
      disableSortBy: false,
      width: 80,
    },
    {
      Header: 'Name of Vessel',
      id: 'vessel_name',
      accessor: function nameOfVesselAccessor(row) {
        return (
          <VesselNameLink
            vesselName={row.vessel_name}
            ownershipId={row.vessel_ownership_id}
            eventTracker={() => {}}
          />
        );
      },
      name: 'vessel_name',
      type: 'text',
      order: 2,
      disableSortBy: false,
      minWidth: 180,
      // sticky: 'left',
    },
    {
      Header: 'View Report',
      id: 'modeller-report-tbl-view-report',
      accessor: (row) => viewReportAccessor(row.vessel_ownership_id, row.vessel_name),
      order: 3,
      disableSortBy: true,
      minWidth: 40,
    },
    {
      Header: 'Tech Group',
      id: 'vessel_tech_group',
      accessor: function techGroupAccessor(row) {
        return <div>{row?.vessel_tech_group ?? Dash} </div>;
      },
      name: 'vessel_tech_group',
      type: 'text',
      order: 4,
      disableSortBy: false,
      minWidth: 120,
    },
    {
      Header: 'Planned Number',
      id: 'planned_number',
      accessor: function plannedNumberAccessor(row) {
        return <div className="text-right">{row?.planned_number ?? Dash} </div>;
      },
      name: 'planned_number',
      type: 'text',
      order: 5,
      className: 'text-right',
      disableSortBy: false,
      minWidth: 180,
    },
    {
      Header: 'Actual Number',
      id: 'actual_number',
      className: 'text-right',
      accessor: function actualNumberAccessor(row) {
        return <div className="text-right">{row?.actual_number ?? Dash} </div>;
      },
      name: 'actual_number',
      type: 'text',
      order: 6,
      disableSortBy: false,
      minWidth: 180,
    },
    {
      Header: `Planned Wages (${AccountingCurrency.find((a) => a.id === plannedWagesUnit)?.value})`,
      id: 'planned_wages',
      className: 'text-right',
      accessor: function plannedWagesAccessor(row) {
        return <div className="text-right">{row.planned_wages ?? Dash} </div>;
      },
      name: 'planned_wages',
      type: 'text',
      order: 7,
      disableSortBy: false,
      minWidth: 208,
    },
    {
      Header: `Actual Wages (${AccountingCurrency.find((a) => a.id === plannedWagesUnit)?.value})`,
      id: 'actual_wages',
      className: 'text-right',
      accessor: function actualWagesAccessor(row) {
        return <div className="text-right">{row?.actual_wages ?? Dash} </div>;
      },
      name: 'actual_wages',
      type: 'text',
      order: 8,
      disableSortBy: false,
      minWidth: 190,
    },
    {
      Header: 'Action',
      id: 'modeller-report-tbl-action',
      accessor: (row, index) => viewEditAccessor(row.vessel_ownership_id, index, row.vessel_name),
      disableSortBy: true,
      width: 30,
      sticky: window.innerWidth > 960 ? 'right' : null,
    },
  ];

  if (hasExportToExcelAccess) {
    modellerColumnDefs.unshift({
      id: 'checkbox',
      type: 'item',
      accessor: (row) => {
        return row.id ?? '---';
      },
      Cell: ({ row }) => {
        return <Checkbox row={row} selectedRows={selectedRows} onSelectRow={onSelectRow} />;
      },
      Header: () => {
        return <CheckboxHeader data={data} selectedRows={selectedRows} onSelectRow={onSelectRow} />;
      },
      width: 50,
      maxWidth: 60,
      minWidth: 50,
      Footer: null,
      disableSortBy: true,
    });
  }

  return modellerColumnDefs;
};

export const vesselPlanItems = ({ plannedWagesUnit = DEFAULT_CURRENCY_MODELLER }) => [
  {
    Header: 'No.',
    id: 'vessel-plan-no',
    accessor: function numberShowAccessor(row) {
      return <div>{row?.id ?? Dash}</div>;
    },
    name: 'id',
    type: 'text',
    order: 1,
    disableSortBy: false,
    width: 80,
  },
  {
    Header: 'Rank',
    id: 'vessel-plan-rank',
    accessor: function showRank(row) {
      return <div>{row?.rank?.value ?? Dash}</div>;
    },
    name: 'rank.value',
    type: 'text',
    order: 1,
    disableSortBy: false,
    minWidth: 200,
  },
  {
    Header: 'Planned Number',
    id: 'vessel-plan-planned-number',
    className: 'text-right',
    accessor: function plannedNumberAccessor(row) {
      return (
        <div className="text-right">
          {row?.planned_number && row.planned_number !== 0 ? row.planned_number : Dash}{' '}
        </div>
      );
    },
    name: 'planned_number',
    type: 'text',
    order: 5,
    disableSortBy: false,
    minWidth: 190,
  },
  {
    Header: 'Actual Number',
    id: 'vessel-plan-actual-number',
    className: 'text-right',
    accessor: function actualNumberAccessor(row) {
      return <div className="text-right">{row?.actual_number ?? Dash} </div>;
    },
    name: 'actual_number',
    type: 'text',
    order: 6,
    disableSortBy: false,
    minWidth: 190,
  },
  {
    Header: 'Planned Nationality',
    id: 'vessel-plan-planned-nationality',
    accessor: function plannedNationalityAccessor(row) {
      return <div>{row?.planned_nationality?.value ? row.planned_nationality.value : Dash} </div>;
    },
    name: 'planned_nationality.value',
    type: 'text',
    order: 7,
    disableSortBy: false,
    minWidth: 190,
  },
  {
    Header: 'Actual Nationality',
    id: 'vessel-plan-actual-nationality',
    accessor: function actualNationalityAccessor(row) {
      return <div>{row?.actual_nationality?.value ? row.actual_nationality.value : Dash} </div>;
    },
    name: 'actual_nationality.value',
    type: 'text',
    order: 7,
    disableSortBy: false,
    minWidth: 190,
  },
  {
    Header: `Planned Wages (${AccountingCurrency.find((a) => a.id === plannedWagesUnit)?.value})`,
    id: 'vessel-plan-planned-wages',
    className: 'text-right',
    accessor: function plannedWagesAccessor(row) {
      return (
        <div className="text-right">
          {row?.planned_wages && row.planned_wages != 0 ? row.planned_wages : Dash}{' '}
        </div>
      );
    },
    name: 'planned_wages',
    type: 'text',
    order: 7,
    disableSortBy: false,
    minWidth: 208,
  },
  {
    Header: `Actual Wages (${AccountingCurrency.find((a) => a.id === plannedWagesUnit)?.value})`,
    id: 'vessel-plan-actual-wages',
    className: 'text-right',
    accessor: function actualWagesAccessor(row) {
      return <div className="text-right">{row?.actual_wages ? row.actual_wages : Dash} </div>;
    },
    name: 'actual_wages',
    type: 'text',
    order: 8,
    disableSortBy: false,
    minWidth: 190,
  },
];

export const vesselEditPlanColumns = ({ plannedWagesUnit = DEFAULT_CURRENCY_MODELLER }) => [
  {
    Header: 'No.',
    id: 'vessel-plan-no',
    accessor: function numberShowAccessor(row: any) {
      return <div>{row.id}</div>;
    },
    name: 'id',
    type: 'text',
    order: 1,
    disableSortBy: true,
    width: 80,
  },
  {
    Header: 'Rank',
    id: 'vessel-plan-rank',
    accessor: function showRank(row: any) {
      return <div>{row.rank.value}</div>;
    },
    name: 'rank.value',
    type: 'text',
    order: 2,
    disableSortBy: true,
    width: 180,
  },
  {
    Header: 'Planned Number',
    id: 'vessel-plan-planned-number',
    className: 'text-right',
    accessor: function plannedNumberAccessor(row: any) {
      return <div className="text-center">{row.planned_number} </div>;
    },
    name: 'planned_number',
    type: 'text',
    order: 3,
    disableSortBy: true,
    width: 180,
  },
  {
    Header: 'Planned Nationality',
    id: 'vessel-plan-planned-nationality',
    className: 'text-center',
    accessor: function plannedNationalityAccessor(row: any) {
      return <div>{row?.planned_nationality?.value ? row.planned_nationality.value : '---'} </div>;
    },
    name: 'planned_nationality',
    type: 'text',
    order: 4,
    disableSortBy: true,
    width: 200,
  },
  {
    Header: `Planned Wages (${AccountingCurrency.find((a) => a.id === plannedWagesUnit)?.value})`,
    id: 'vessel-plan-planned-wages',
    className: 'text-left',
    accessor: function plannedWagesAccessor(row: any) {
      return <div className="text-right">{row?.planned_wages ? row.planned_wages : '---'} </div>;
    },
    name: 'planned_wages',
    type: 'text',
    order: 5,
    width: 300,
    disableSortBy: true,
  },
];
