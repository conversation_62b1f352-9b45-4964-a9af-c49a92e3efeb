import React from 'react';
import { Form } from 'react-bootstrap';

const TakeOverDropDownControl = ({ label, name, vessel, onInputChange, errors, options }) => {
  let value = vessel[name];

  if (value === undefined) {
    const objectName = name.replace('_id', '');
    const objectValue = vessel[objectName];

    if (objectValue) {
      value = objectValue.id;
    } else {
      value = '';
    }
  }

  return (
    <>
      {label && <Form.Label>{label}</Form.Label>}
      <Form.Control
        as="select"
        name={name}
        value={value}
        onChange={onInputChange}
        isInvalid={!!errors[name]}
      >
        <option value="">Please Select</option>
        {options.map(({ id, value }) => (
          <option value={id} key={id}>
            {value}
          </option>
        ))}
      </Form.Control>
    </>
  );
};

export default TakeOverDropDownControl;
