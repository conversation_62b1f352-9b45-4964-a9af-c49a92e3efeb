import React from 'react';
import { Row } from 'react-bootstrap';
import './scss/details.scss';

export const GeneralElementID = 'general';
export const ParticularsElementID = 'particulars';
export const ContactElementID = 'contact';
export const DocumentElementID = 'documents';
export const ExperienceElementID = 'experience';

const AnchorTab = ({ handleClick }) => {
  const handleScroll = (e) => handleClick(e.target.id);
  return (
    <Row>
      <ul>
        <li
          id={GeneralElementID}
          onClick={handleScroll}
          onKeyDown={(event) => {
            if (event.key === 'Enter' || event.key === ' ') {
              handleScroll(event);
            }
          }}
          tabIndex={0}
        >
          General Details
        </li>
        <li
          id={ParticularsElementID}
          onClick={handleScroll}
          onKeyDown={(event) => {
            if (event.key === 'Enter' || event.key === ' ') {
              handleScroll(event);
            }
          }}
          tabIndex={0}
        >
          Personal Particulars
        </li>
        <li
          id={ContactElementID}
          onClick={handleScroll}
          onKeyDown={(event) => {
            if (event.key === 'Enter' || event.key === ' ') {
              handleScroll(event);
            }
          }}
          tabIndex={0}
        >
          Contact Details
        </li>
        <li
          id={DocumentElementID}
          onClick={handleScroll}
          onKeyDown={(event) => {
            if (event.key === 'Enter' || event.key === ' ') {
              handleScroll(event);
            }
          }}
          tabIndex={0}
        >
          Documents
        </li>
        <li
          id={ExperienceElementID}
          onClick={handleScroll}
          onKeyDown={(event) => {
            if (event.key === 'Enter' || event.key === ' ') {
              handleScroll(event);
            }
          }}
          tabIndex={0}
        >
          Experience
        </li>
      </ul>
    </Row>
  );
};

export default AnchorTab;
