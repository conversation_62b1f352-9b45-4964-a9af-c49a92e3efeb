import React from 'react';
import TableHeaderRow from './TableHeaderRow';
import PhoneTextField from './PhoneTextField';
import { DIGITS } from '../../constants/contactTypes';
import { v4 as uuid } from 'uuid';
const SPLIT_CHARACTER = ';';
const blockStyle = { display: 'block' };
const borderNone = { borderBottom: 'none' };

const TableRow = ({ data, isSectionHeader }) => {
  return data.map((item) => {
    const classLabel = isSectionHeader
      ? 'details_page__row-name__header'
      : 'details_page__row-name';
    const classValue = isSectionHeader
      ? 'details_page__row-value__header'
      : 'details_page__row-value';

    return (
      <tr key={uuid()}>
        <td className={classLabel}>{item.label}</td>
        {item.phoneAutoFormat ? (
          <td>
            {item.value.split(SPLIT_CHARACTER).map((child) => {
              if (DIGITS.test(child)) return <PhoneTextField key={uuid()} value={child} />;
              return (
                <span key={uuid()} style={blockStyle}>
                  {child}
                </span>
              );
            })}
          </td>
        ) : (
          <>
            {item.emailType ? (
              <td>
                {item.value.split(SPLIT_CHARACTER).map((child) => {
                  return (
                    <span key={uuid()} style={blockStyle}>
                      {child}
                    </span>
                  );
                })}
              </td>
            ) : (
              <td className={classValue}>{item.value}</td>
            )}
          </>
        )}
      </tr>
    );
  });
};

const TableMultipleSection = ({ sections }) => (
  <>
    {(sections ?? []).map((elements) => {
      let firstItem = undefined;
      let items = elements;
      if (items.length > 0) {
        firstItem = items.shift();
      }

      return (
        <tbody key={uuid()}>
          <TableHeaderRow
            label={firstItem.label}
            value={firstItem.value}
            style={{ marginTop: '30px' }}
          />
          <TableRow data={items ?? []} />
          <tr>
            <td colSpan="2" style={borderNone}>
              <br />
            </td>
          </tr>
        </tbody>
      );
    })}
  </>
);

export default TableMultipleSection;
