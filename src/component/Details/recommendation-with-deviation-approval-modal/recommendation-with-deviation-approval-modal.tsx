import React, { KeyboardEvent, useState } from 'react';
import { useFormik } from 'formik';
import { Form, InputGroup } from 'react-bootstrap';
import { seafarerStatus } from '../../../model/constants';
import ErrorAlert from '../../common/ErrorAlert';
import seafarerService from '../../../service/seafarer-service';
import { Seafarer } from '../../../types/seafarerInterfaces';
import { numberWithCommas } from '../../../util/view-utils';
import { ConfirmationModal } from '../../confirmation-modal/confirmation-modal';
import '../scss/recommendation-with-deviation-approval-modal.scss';
import { DEFAULT_CURRENCY_UNIT } from '@src/constants/seafarer-wages';

const RecommendationWithDeviationApprovalModal = ({
  show,
  onClose,
  onConfirm,
  currentRecommendationModalData,
  seafarerDetails,
}: {
  show: boolean;
  onClose: () => void;
  onConfirm: () => void;
  currentRecommendationModalData: Object;
  seafarerDetails: Seafarer;
}) => {
  const [error, setError] = useState('');
  const [disableConfirm, setDisableConfirm] = useState(false);
  const handleApproveDeviationRecommendation = async (formObj) => {
    try {
      setDisableConfirm(true);
      const payload: any = {
        status: seafarerStatus.CREW_ASSIGNMENT_APPROVED,
        remarks: formObj.remarks,
      };

      const { data } = await seafarerService.approveRejectRecommendation(
        payload,
        currentRecommendationModalData?.id,
      );
      if (data.error) {
        setDisableConfirm(false);
        setError(data.message);
      } else {
        onConfirm();
      }
    } catch (error) {
      setDisableConfirm(false);
      setError(`Oops, something went wrong. Please try again. Error: ${error}`);
    }
  };

  const { handleSubmit, values, handleBlur, handleChange } = useFormik({
    initialValues: {
      remarks: '',
      wages: currentRecommendationModalData?.recommended_wages
        ? numberWithCommas(currentRecommendationModalData?.recommended_wages)
        : '',
    },
    onSubmit: (valueObj) => {
      handleApproveDeviationRecommendation(valueObj);
    },
  });
  const currencyUnit = currentRecommendationModalData?.recommended_wages_unit?.toUpperCase() || DEFAULT_CURRENCY_UNIT;
  return (
      <form autoComplete="off">
        <ConfirmationModal
          show={show}
          title={`Confirm Approval of ${seafarerDetails?.seafarer_person?.first_name ?? ''} ${
            seafarerDetails?.seafarer_person?.last_name ?? ''
          }'s Recommended with Deviation for ${currentRecommendationModalData?.vessel_name} ?`}
          confirmButtonLabel="Confirm"
          onConfirm={handleSubmit}
          dialogClassName="recommendation-with-deviation-approval-modal"
          cancelButtonLabel="Cancel"
          onClose={onClose}
          disableConfirm={disableConfirm}
        >
          <div>
            <Form.Label htmlFor="remarks">Remarks</Form.Label>
            <Form.Control
              id="remarks"
              as="textarea"
              value={values.remarks}
              onChange={handleChange}
              onBlur={handleBlur}
              maxLength={300}
            ></Form.Control>
            <Form.Label htmlFor="wages">Wages</Form.Label>
            <InputGroup>
             <InputGroup.Prepend>
               <InputGroup.Text>{currencyUnit}</InputGroup.Text>
              </InputGroup.Prepend>
              <Form.Control
                id="wages"
                className="wages-input"
                type="text"
                value={values.wages}
                onChange={handleChange}
                onKeyDown={(e: KeyboardEvent) => {
                  if (e.key === 'e' || e.key === '-') {
                    e.preventDefault();
                  }
                }}
                onBlur={handleBlur}
                disabled={true}
            />
            </InputGroup>
          </div>
          <div className="recommendation-error-alert">
            {error ? <ErrorAlert message={error} /> : ''}
          </div>
        </ConfirmationModal>
      </form>
  );
};

export default RecommendationWithDeviationApprovalModal;
