@mixin contact-detail {
  min-height: 1.8rem !important;
  border-bottom: 1px solid #efefef !important;
}

.phone-disabled-text-field {
  right: 3.18rem !important;
}

input.contact-detail {
  @include contact-detail();
}

.react-tel-input > input:disabled {
  pointer-events: none;
  background-color: #e9ecef;
}

.react-tel-input > input:disabled + .flag-dropdown {
  pointer-events: none;
  background-color: #e9ecef;
}

p.contact-detail {
  @include contact-detail();
}

ul {
  color: #1f4a70;
  li {
    list-style: none;
    float: left;
    padding-right: 40px;
    font-weight: 500;
    cursor: pointer;
  }
}

#details-page-error {
  ul {
    li {
      cursor: default;
    }
  }
}

.hide-flag-bloc {
  overflow: hidden !important;
  display: none;
}

.nav {
  margin-bottom: 5px;
  padding-bottom: 5px;
}

.seafarer-status-section {
  border-left: 1px solid #707070;
  height: 20%;
}

.seafarer-status {
  color: #aaaaaa;
  font-size: 13px;
}
.status-secondary-line {
  color: #0091b8;
  font: 20px;
  font-weight: bold;
}

.status-secondary-initialize {
  color: #0091b8 !important;
}
.status-secondary-active {
  color: #28a748 !important;
}

.status-secondary-inactive {
  color: #ffa321 !important;
}
.status-secondary-archived {
  color: #d41b56 !important;
}

.status-text-recommended {
  color: #FFA221 !important;
}
.status-text-recommended_with_deviation {
  color: #FFA221 !important;
  text-decoration: underline;
  cursor: pointer;
}

.status-no-permission-recommended_with_deviation-true {
  text-decoration: none;
  cursor: default;
}

.status-text {
  margin-bottom: 0px;
}

.dot-status {
  height: 8px;
  width: 8px;
  background-color: #28a748;
  border-radius: 50%;
  display: inline-block;
  margin-right: 6px;
}

.modal-header-text,
.modal-label {
  color: #1f4a70;
  font-weight: bold;
}

.btn-no-variant {
  background-color: purple;
  color: white;
}

.modal-body .form-row .dropdown button {
  border: solid 1px #ced4da;
}

.dropdown-header-text {
  color: #c6cdd3 !important;
  font-weight: bold;
}

.dropdown-item-text {
  color: #1f4a70;
}

.about-change-status-wrapper {
  display: flex;
  justify-content: space-around;
  align-items: center;
  flex-direction: row;
  background: #edf3f7 0% 0% no-repeat padding-box;
  border-radius: 4px;
  padding: 0.5em;

  div {
    font: normal normal medium 14px/17px Inter, sans-serif;
    color: #1e4a70;
  }

  a,
  a:hover {
    text-decoration: underline !important;
    font: normal normal medium 14px/17px Inter, sans-serif;
    color: #1e4a70;
  }
}

.recommendation-error-alert {
  padding-top: 2rem;
}