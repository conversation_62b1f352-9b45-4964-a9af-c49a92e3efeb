import React, { useState, useEffect } from 'react';
import { orderBy } from 'lodash';
import { useFormik } from 'formik';
import { Col, Form } from 'react-bootstrap';
import moment from 'moment-timezone';
import FleetDatePicker from '../../AddSeafarer/FleetDatePicker';
import * as schema from '../../../model/SeafarerSchemaValidation';
import AutoDismissibleAlert from '../../common/AutoDismissibleAlert';
import DropDownSearchControl from '../../AddSeafarer/DropDownSearchControl';
import { createContractDetails } from '@src/service/seafarer-service';
import { getCountries, getPorts, getPortById } from '../../../service/reference-service';
import useLazyRequest from '@src/hooks/useLazyRequest';
import { useNotification } from '@src/component/common/Notification/useNotification';
import StandardModal from '@src/component/common/Modal';
import { useAccess } from '@src/component/common/Access';
import { NOMINEE_TYPE, NOMINEE_MAPPING } from '@src/constants/pre-joining-details';

const defaultDropDownValue = {
    countries: [],
    repatriation_ports: [],
    embarkation_ports: [],
    nominee_options: [
        { id: 1, value: NOMINEE_MAPPING[NOMINEE_TYPE.SPOUSE] },
        { id: 2, value: NOMINEE_MAPPING[NOMINEE_TYPE.NEXT_OF_KIN] },
    ],
    contract_length_options: Array.from({ length: 15 }, (_, index) => ({
        id: index + 1,
        value: (index + 1).toString(),
    }))
};

const AIRPORT_CODE = 4;

interface FormValues {
    seafarer_id: number;
    expected_contract_start_date?: string;
    contract_length?: number;
    repatriation_country_name?: string;
    repatriation_port_id?: number;
    repatriation_port?: string;
    embarkation_country_name?: string;
    embarkation_port_id?: number;
    embarkation_port?: string;
    nominee?: number;
}
interface Props {
    onClose?: Function;
    show: boolean;
    data: Object;
    onSubmitCompleted: Function;
    eventTracker: (type: string, value: string | number) => void;
}
const SetContractDetailsModal = ({ onClose, show, data, onSubmitCompleted, eventTracker }: Props) => {
    const [error, setError] = useState<string | null>(null);
    const [dropDownData, setDropDownData] = useState(defaultDropDownValue);
    const { notify } = useNotification();
    const { roleConfig } = useAccess();
    const canChangeContractDate = roleConfig?.seafarer?.edit?.contractEndDate || roleConfig?.seafarer?.edit?.seafarerSuperUser;
    const canAccessBackDate = roleConfig?.seafarer?.edit?.seafarerSuperUser;
    const handleClose = () => {
        onClose?.();
    };
    const [updateContractDetails, { loading: isSubmitting }] = useLazyRequest(createContractDetails, {
        onComplete(response) {
            notify.success('Contract Details have been updated successfully!', 'success');
            const isBackdated = moment(response?.data.expected_contract_start_date).isBefore(moment().startOf('day'));
            if (isBackdated) {
                eventTracker('expectedSignedOnBackDate', '');
            }
            onSubmitCompleted?.(response?.data);
        },
        onError(err) {
            if (err.response) {
                setError(err.response.data || 'An error occurred.');
            } else if (err.request) {
                setError('Network error: No response received.');
            } else {
                setError(err.message);
            }
        },
    });

    // If nomineeData is 3, it returns [1, 2]. For any other value, it returns an array containing the nomineeData.
    const getNomineeArray = (nomineeData: number | number[] | null): number[] => {
        if (nomineeData === null) return [];
        if (Array.isArray(nomineeData)) return nomineeData;
        if (nomineeData === 3) return [1, 2];
        return [nomineeData];
    };
    // If the nomineeArray is [1, 2], it returns 3, otherwise returns a single nominee value
    const getNomineeValue = (nomineeArray: number[]): number | undefined => {
        if (nomineeArray.length === 1) {
            return nomineeArray[0];
        } else if (nomineeArray.length === 2) {
            return 3;
        }
        return undefined;
    };
    const formik = useFormik<FormValues>({
        initialValues: {
            seafarer_id: data.seafarer_id,
            expected_contract_start_date: data?.expected_contract_start_date ?? null,
            contract_length: data.contract_length,
            repatriation_port: data.repatriation_port,
            repatriation_port_id: data.repatriation_port_id,
            embarkation_port: data.embarkation_port,
            embarkation_port_id: data.embarkation_port_id,
            nominee: getNomineeArray(data.nominee),
            embarkation_country_name: null,
            repatriation_country_name: null,
        },
        validationSchema: schema.setContractDetailsModalSchema(canAccessBackDate),
        validateOnMount: true,
        onSubmit: (values: FormValues) => {
            const { seafarer_id, expected_contract_start_date, contract_length, repatriation_port_id, embarkation_port_id, nominee } = values;
            const repatriation_port = dropDownData.repatriation_ports.find(port => port.id === repatriation_port_id)?.value;
            const embarkation_port = dropDownData.embarkation_ports.find(port => port.id === embarkation_port_id)?.value;
            updateContractDetails({
                seafarer_id: String(seafarer_id),
                expected_contract_start_date: moment(expected_contract_start_date).format('YYYY-MM-DD'),
                contract_length: Number(contract_length),
                repatriation_port,
                repatriation_port_id,
                embarkation_port,
                embarkation_port_id,
                nominee: getNomineeValue(nominee) ?? 0,
            });
        },
    });
    const genOnDateChange = (fieldName: string) => (value: any) => setFieldValue(fieldName, value);
    const { values, errors, setFieldValue, touched } = formik;
    const updatePortsByCountry = async (countryName: string, portType: string) => {
        if (countryName) {
            try {
                const portsResponse = portType === 'repatriation' ? await getPorts(countryName, AIRPORT_CODE) : await getPorts(countryName);
                const sortedData = orderBy(portsResponse.ports.map(port => ({
                    id: port.id,
                    value: port.name,
                })), ['value'], ['asc']);
                setDropDownData(prevData => ({
                    ...prevData,
                    [`${portType}_ports`]: sortedData,
                }));
            } catch (error) {
                setError('Failed to load port list', error.message);
            }
        } else {
            setFieldValue(`${portType}_port_id`, null);
            setFieldValue(`${portType}_port`, null);
            setDropDownData(prevData => ({
                ...prevData,
                [`${portType}_ports`]: [],
            }));
        }
    };
    const onInputChange = async (event) => {
        const targetName = event?.target?.name;
        let targetValue = event?.target?.value;
        if (targetName == 'repatriation_country_name') {
            await updatePortsByCountry(targetValue, 'repatriation');
        }
        if (targetName == 'embarkation_country_name') {
            await updatePortsByCountry(targetValue, 'embarkation');
        }
        setFieldValue(targetName, targetValue);
    };
    const getMinDate = () => {
        if (canAccessBackDate) {
            return new Date(moment().startOf('day').subtract(1, 'month').toISOString());
        }
        return new Date();
    };
    const isSubmitValid = formik.isValid && formik.dirty && !isSubmitting;

    const loadPorts = async (country_code: string, type?: number) => {
        const portsResponse = await getPorts(country_code, type);
        const sortedPorts = orderBy(portsResponse.ports.map(port => ({
            id: port.id,
            value: port.name,
        })), ['value'], ['asc']);
        return sortedPorts;
    };

    const getPortListByPortId = async (port_id: number, type?: number) => {
        const port = await getPortById(port_id);
        const { country_code } = port;
        const sortedPorts = await loadPorts(country_code, type);
        return {
            country_name: country_code,
            ports: sortedPorts,
        };
    };

    useEffect(() => {
        const loadDropDownData = async () => {
            try {
                const countriesResponse = await getCountries();
                const sortedCountries = orderBy(countriesResponse.countries.map(country => ({
                    id: country.alpha2_code,
                    value: country.value,
                })), ['value'], ['asc']);
                const portDetailsPromises = [
                    values.repatriation_port_id ? getPortListByPortId(values.repatriation_port_id, AIRPORT_CODE) : Promise.resolve(null),
                    values.embarkation_port_id ? getPortListByPortId(values.embarkation_port_id) : Promise.resolve(null),
                ];
                const [repatriationData, embarkationData] = await Promise.all(portDetailsPromises);
                if (repatriationData) {
                    Object.assign(values, {
                        repatriation_country_name: repatriationData.country_name,
                    });
                }
                if (embarkationData) {
                    Object.assign(values, {
                        embarkation_country_name: embarkationData.country_name,
                    });
                }
                const updatedDropDownData = {
                    countries: sortedCountries,
                    repatriation_ports: repatriationData?.ports ?? [],
                    embarkation_ports: embarkationData?.ports ?? [],
                };
                setDropDownData(prevData => ({
                    ...prevData,
                    ...updatedDropDownData,
                }));
            } catch (error) {
                setError('Failed to load country or port list', error.message);
            }
        };
        loadDropDownData();
    }, []);

    return (
        <StandardModal
            title="Update Contract Details"
            okText="Save"
            visible={show && canChangeContractDate}
            onCancel={handleClose}
            size="lg"
            okButtonProps={{
                disabled: !isSubmitValid,
            }}
            onOk={formik.handleSubmit}
            confirmLoading={isSubmitting}
        >
            {error && (
                <AutoDismissibleAlert message={error} variant="danger" onClose={() => setError(null)} />
            )}
            <Form>
                <Form.Row>
                    <Form.Group as={Col} md="6">
                        <Form.Label htmlFor="expected_contract_start_date" className="font-weight-bold">Expected Sign On Date</Form.Label>
                        <FleetDatePicker
                            name="expected_contract_start_date"
                            placeholder="Select Date"
                            onChange={genOnDateChange('expected_contract_start_date')}
                            value={values.expected_contract_start_date}
                            isInvalid={!!(errors.expected_contract_start_date)}
                            minDate={getMinDate()}
                            calendarIcon
                            isClearable
                        />
                        <Form.Control.Feedback
                            type="invalid"
                            className={(errors.expected_contract_start_date) ? 'set-display-block' : ''}
                        >
                            {errors.expected_contract_start_date}
                        </Form.Control.Feedback>
                    </Form.Group>
                    <Form.Group as={Col} md="6">
                        <Form.Label htmlFor="contract_length" className="font-weight-bold">Length of the Contract (in Months)</Form.Label>
                        <DropDownSearchControl
                            name={'contract_length'}
                            selectedValue={values.contract_length}
                            dropDownValues={dropDownData.contract_length_options}
                            onInputChange={onInputChange}
                            isInvalid={!!(errors.contract_length && touched.contract_length)}
                            testID="contract_length"
                            placeholder="Select Length of Contract"
                        />
                        <Form.Control.Feedback
                            type="invalid"
                            className={(errors.contract_length && touched.contract_length) ? 'set-display-block' : ''}
                        >
                            {errors.contract_length}
                        </Form.Control.Feedback>
                    </Form.Group>
                    <Form.Group as={Col} md="6">
                        <Form.Label htmlFor="repatriation_country_name" className="font-weight-bold">Repatriation Port</Form.Label>
                        <DropDownSearchControl
                            name="repatriation_country_name"
                            selectedValue={dropDownData.countries.length ? values.repatriation_country_name : undefined}
                            dropDownValues={dropDownData.countries}
                            onInputChange={onInputChange}
                            isInvalid={!!(errors.repatriation_country_name && touched.repatriation_country_name)}
                            testID="repatriation_country_name"
                            placeholder="Select Country"
                        />
                        <Form.Control.Feedback
                            type="invalid"
                            className={(errors.repatriation_country_name && touched.repatriation_country_name) ? 'set-display-block' : ''}
                        >
                            {errors.repatriation_country_name}
                        </Form.Control.Feedback>
                    </Form.Group>
                    <Form.Group as={Col} md="6">
                        <Form.Label htmlFor="repatriation_port_id" className="form-heading">&nbsp;</Form.Label>
                        <DropDownSearchControl
                            name="repatriation_port_id"
                            selectedValue={dropDownData.repatriation_ports.length ? values.repatriation_port_id : undefined}
                            dropDownValues={dropDownData.repatriation_ports}
                            onInputChange={onInputChange}
                            isInvalid={!!(errors.repatriation_port_id && touched.repatriation_port_id)}
                            testID="repatriation_port_id"
                            placeholder="Select Port"
                        />
                        <Form.Control.Feedback
                            type="invalid"
                            className={(errors.repatriation_port_id && touched.repatriation_port_id) ? 'set-display-block' : ''}
                        >
                            {errors.repatriation_port_id}
                        </Form.Control.Feedback>
                    </Form.Group>
                    <Form.Group as={Col} md="6">
                        <Form.Label htmlFor="embarkation_country_name" className="font-weight-bold">Embarkation Port</Form.Label>
                        <DropDownSearchControl
                            name="embarkation_country_name"
                            selectedValue={dropDownData.countries.length ? values.embarkation_country_name : undefined}
                            dropDownValues={dropDownData.countries}
                            onInputChange={onInputChange}
                            isInvalid={!!(errors.embarkation_country_name && touched.embarkation_country_name)}
                            testID="embarkation_country_name"
                            placeholder="Select Country"
                        />
                        <Form.Control.Feedback
                            type="invalid"
                            className={(errors.embarkation_country_name && touched.embarkation_country_name) ? 'set-display-block' : ''}
                        >
                            {errors.embarkation_country_name}
                        </Form.Control.Feedback>
                    </Form.Group>
                    <Form.Group as={Col} md="6">
                        <Form.Label htmlFor="embarkation_port_id" className="form-heading">&nbsp;</Form.Label>
                        <DropDownSearchControl
                            name="embarkation_port_id"
                            selectedValue={dropDownData.embarkation_ports.length ? values.embarkation_port_id : undefined}
                            dropDownValues={dropDownData.embarkation_ports}
                            onInputChange={onInputChange}
                            isInvalid={!!(errors.embarkation_port_id && touched.embarkation_port_id)}
                            testID="embarkation_port_id"
                            placeholder="Select Port"
                        />
                        <Form.Control.Feedback
                            type="invalid"
                            className={(errors.embarkation_port_id && touched.embarkation_port_id) ? 'set-display-block' : ''}
                        >
                            {errors.embarkation_port_id}
                        </Form.Control.Feedback>
                    </Form.Group>
                    <Form.Group as={Col} md="6">
                        <Form.Label htmlFor="nominee" className="form-heading">Nominee</Form.Label>
                        <DropDownSearchControl
                            name={'nominee'}
                            selectedValue={values.nominee}
                            dropDownValues={dropDownData.nominee_options}
                            onInputChange={onInputChange}
                            isInvalid={!!(errors.nominee && touched.nominee)}
                            testID="nominee"
                            multiple={true}
                            placeholder="Select Nominee"
                        />
                        <Form.Control.Feedback
                            type="invalid"
                            className={(errors.nominee && touched.nominee) ? 'set-display-block' : ''}
                        >
                            {errors.nominee}
                        </Form.Control.Feedback>
                    </Form.Group>
                </Form.Row>
            </Form>
        </StandardModal>
    );
};
export default SetContractDetailsModal;
