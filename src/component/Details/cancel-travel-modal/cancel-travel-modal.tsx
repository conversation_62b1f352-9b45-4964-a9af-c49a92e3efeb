import React, { useState } from 'react';
import { Alert } from 'react-bootstrap';
import ErrorAlert from '../../common/ErrorAlert';
import seafarerService from '../../../service/seafarer-service';
import { Seafarer } from '../../../types/seafarerInterfaces';
import { ConfirmationModal } from '../../confirmation-modal/confirmation-modal';
import '../scss/recommendation-rejection-modal.scss';

const CancelTravelModal = ({
  show,
  onClose,
  onConfirm,
  seafarerDetails,
}: {
  show: boolean;
  onClose: () => void;
  onConfirm: () => void;
  seafarerDetails: Seafarer;
}) => {
  const [error, setError] = useState('');
  const [disableConfirm, setDisableConfirm] = useState(false);

  const handleCancelTravel = async () => {
    try {
      setDisableConfirm(true);
      const payload = {
        seafarer_id: seafarerDetails.id,
      };

      const { data } = await seafarerService.cancelTravelPlan(payload);
      if (data.error) {
        setDisableConfirm(false);
        setError(data.message);
      } else {
        onConfirm();
      }
    } catch (error) {
      setDisableConfirm(false);
      setError(`Oops, something went wrong. Please try again. Error: ${error}`);
    }
  };

  return (
    <ConfirmationModal
      show={show}
      title="Cancel Travel"
      confirmButtonLabel="Proceed"
      onConfirm={handleCancelTravel}
      cancelButtonLabel="Cancel"
      onClose={onClose}
      disableConfirm={disableConfirm}
      size="lg"
    >
      <Alert variant="warning" className="crew-planner-error">
        <p style={{ marginBottom: '0px' }}>
          On cancelling the travel, the seafarer status will be set to{' '}<b>On Leave</b> and <br />
          <b>Recommendation Process needs to be re-initiated</b>. <br /> Do you wish to Proceed?
        </p>
      </Alert>
      {error && (
        <div className="recommendation-error-alert">
          <ErrorAlert message={error} />
        </div>
      )}
    </ConfirmationModal>
  );
};

export default CancelTravelModal;
