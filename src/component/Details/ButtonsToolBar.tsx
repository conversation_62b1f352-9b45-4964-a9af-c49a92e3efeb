/* eslint-disable react/prop-types */
import React from 'react';
import { useHistory } from 'react-router-dom';
import {
  Button,
  ButtonToolbar,
  ButtonGroup,
  DropdownButton,
  Dropdown,
  Tooltip,
  OverlayTrigger,
} from 'react-bootstrap';
import { seafarerStatus } from '../../model/constants';
import { toast } from 'react-toastify';

const INVITABLE_RANKS = ['master', 'chief engineer'];

const ButtonsToolbar = ({
  seafarer,
  isEditPreJoiningEnabled,
  roleConfig,
  visitUpdateVessel,
  handleScreeningHistoryButton,
  isEnableGenerateAppointmentLetter,
  handleGenerateAppointmentLetterButton,
  handleMarkDuplicateButton,
  handleParis2UserAccountButton,
  disableChangeStatusButton,
  visitUpdatePrejoining,
  setModalPopUp,
  setShowDocumentModal,
  handleUpdateWagesButton,
  eventTracker,
  activeTab,
  setShowTravelModal,
  recommendClickAction,
  printBioClickAction,
  enableRecommendationButton,
  visitBankAccountEditForm,
  visitLMS,
  ga4EventTrigger
}) => {
  const history = useHistory();
  const seafarerPerson = seafarer?.seafarer_person;
  const seafarerCurrentStatus = seafarerPerson?.current_journey_status;
  const isEnableUpdateWages =
    seafarerCurrentStatus === seafarerStatus.SIGNED_ON && roleConfig.seafarer.edit.wages;

  const onTravelClick = () => {
    if (
      (seafarer['seafarer_person']['seafarer_status_history'].length &&
        seafarerStatus.CREW_ASSIGNMENT_APPROVED ===
          seafarer['seafarer_person']['seafarer_status_history'].sort((a, b) => b.id - a.id)[0][
            'seafarer_journey_status'
          ]) ||
      seafarerStatus.SIGNED_ON === seafarer['seafarer_person']['current_journey_status']
    ) {
      setShowTravelModal(true);
    } else {
      toast.error('Seafarer is not available to travel.');
    }
  };

  const showRecommendationButton =
    roleConfig.seafarer.editSeafarer && roleConfig.seafarer.edit.recommendation;

  const onRecommendClick = () => {
    recommendClickAction();
  };

  const onPrintBioClick = () => {
    printBioClickAction();
  };

  const canInviteSeafarerToParis2 = roleConfig.seafarer.user && roleConfig.seafarer.user.manage;
  const isBankAccountEditable = roleConfig.seafarer.edit.bankAccount;
  const _visitBankAccountEditForm = () => {
    isBankAccountEditable && visitBankAccountEditForm();
  };
  return (
    <ButtonToolbar className="no-print btn-toolbar" style={{ float: 'right' }}>
      {roleConfig.seafarer.editSeafarer && (
        <>
          <ButtonGroup className="mr-2">
            <div className="about-change-status-wrapper">
              <div className="mr-2">&#9432;</div>
              <a
                href="/seafarer/faq/seafarer-status-instructions"
                onClick={() => {
                  eventTracker('needHelp', 'Help');
                }}
                target="_blank"
              >
                Need help?
              </a>
            </div>
          </ButtonGroup>
          {roleConfig.seafarer.edit.recommendation && (
            <ButtonGroup
              className="mr-2"
              onClick={() => {
                eventTracker('recommend', 'Recommend');
                onRecommendClick();
              }}
              data-testid="recommendation-button"
            >
              <Button
                variant="outline-primary recommend-btn"
                disabled={!enableRecommendationButton}
              >
                Recommend
              </Button>
            </ButtonGroup>
          )}
          <ButtonGroup
            className="mr-2"
            onClick={() => {
              eventTracker('changeStatus', 'Change Status');
              setModalPopUp(true);
            }}
          >
            <Button
              variant="outline-primary change-status-btn"
              disabled={disableChangeStatusButton || !roleConfig?.seafarer?.edit?.status}
              data-testid="fml-change-status"
            >
              Change Status
            </Button>
          </ButtonGroup>
        </>
      )}
      {roleConfig.seafarer.edit?.travel && (
        <ButtonGroup
          className="mr-2"
          onClick={() => {
            ga4EventTrigger('Send Seafarer Data', 'Seafarer Details - Actions Buttons', 'Email Profile Data')
            onTravelClick();
          }}
        >
          <Button variant="outline-primary travel-btn">Email Profile Data</Button>
        </ButtonGroup>
      )}
      {roleConfig?.seafarer?.edit?.seafarerDocument && (
        <ButtonGroup className="mr-2">
          <Button
            variant="outline-primary add-document-btn"
            disabled={!roleConfig?.seafarer?.edit?.seafarerDocument}
            data-testid="fml-add-document"
            onClick={() => {
              eventTracker('addDocument', 'Document');
              // remove the hash to allow scorlling to section after create/update
              history.push(history.location.pathname);
              setShowDocumentModal(true);
            }}
          >
            Add Document
          </Button>
        </ButtonGroup>
      )}
      {roleConfig.seafarer.editSeafarer &&
        (activeTab === 'general' || activeTab === 'experience') && (
          <ButtonGroup className="mr-2" onClick={visitUpdateVessel}>
            <Button variant="outline-primary" data-testid="fml-edit">
              Edit
            </Button>
          </ButtonGroup>
        )}
      {roleConfig.seafarer.training?.edit && activeTab === 'training-courses' && (
        <ButtonGroup className="mr-2" onClick={visitLMS}>
          <Button data-testid="training-edit" variant="outline-primary">
            Edit
          </Button>
        </ButtonGroup>
      )}
      {activeTab === 'account-details' && (
        <ButtonGroup className="mr-2" onClick={_visitBankAccountEditForm}>
          <OverlayTrigger
            {...(isBankAccountEditable ? { show: false } : {})}
            placement="top"
            overlay={
              <Tooltip id="tooltip-top">Your role is not authorised to perform this action</Tooltip>
            }
          >
            <span className="d-inline-block">
              <Button
                disabled={!isBankAccountEditable}
                style={isBankAccountEditable ? {} : { pointerEvents: 'none' }}
                variant="outline-primary"
                data-testid="fml-edit-bank-account"
              >
                Edit Bank Accounts
              </Button>
            </span>
          </OverlayTrigger>
        </ButtonGroup>
      )}
      {roleConfig.seafarer.editSeafarer && activeTab === 'pre-joining' && (
        <ButtonGroup className="mr-2" onClick={visitUpdatePrejoining}>
          <Button variant="outline-primary" disabled={!isEditPreJoiningEnabled}>
            Edit
          </Button>
        </ButtonGroup>
      )}
      <DropdownButton
        onToggle={(isOpen) => {
          isOpen && eventTracker('more', 'More');
        }}
        variant="outline-primary"
        data-testid="fml-actions"
        id="dropdown-basic-button"
        title="Actions"
      >
        {canInviteSeafarerToParis2 && (
          <Dropdown.Item
            onClick={handleParis2UserAccountButton}
            data-testid="fml-paris2.0-user-account"
          >
            PARIS 2.0 User Account
          </Dropdown.Item>
        )}
        <Dropdown.Item
          onClick={handleGenerateAppointmentLetterButton}
          disabled={!isEnableGenerateAppointmentLetter}
          data-testid="fml-add-appointment-letter"
        >
          Generate Appt. Letter
        </Dropdown.Item>
        {roleConfig.seafarer.edit.duplicateHKID && (
          <Dropdown.Item onClick={handleMarkDuplicateButton} data-testid="fml-mark-duplicate-hkid">
            Mark Duplicate HKID
          </Dropdown.Item>
        )}
        {roleConfig.seafarer.screening.view && (
          <Dropdown.Item onClick={handleScreeningHistoryButton} data-testid="fml-screening-history">
            Screening History
          </Dropdown.Item>
        )}
        {showRecommendationButton && (
          <Dropdown.Item onClick={onRecommendClick} disabled={!enableRecommendationButton}>
            Recommend
          </Dropdown.Item>
        )}
        <Dropdown.Item onClick={onPrintBioClick}>Print Bio</Dropdown.Item>
        {(roleConfig.seafarer.view.bankAccount || roleConfig.seafarer.screening.view) && (
          <Dropdown.Item
            onClick={handleUpdateWagesButton}
            disabled={!isEnableUpdateWages}
            data-testid="fml-update-wages-and-promotions"
          >
            Update Wages / Promotions
          </Dropdown.Item>
        )}
      </DropdownButton>
    </ButtonToolbar>
  );
};

export default ButtonsToolbar;
