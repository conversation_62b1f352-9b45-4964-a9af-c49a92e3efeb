import React, { useState } from 'react';
import { useFormik } from 'formik';

import { Alert, Form } from 'react-bootstrap';
import { seafarerStatus } from '../../../model/constants';
import ErrorAlert from '../../common/ErrorAlert';
import seafarerService from '../../../service/seafarer-service';
import { Seafarer } from '../../../types/seafarerInterfaces';
import { ConfirmationModal } from '../../confirmation-modal/confirmation-modal';
import '../scss/recommendation-rejection-modal.scss';

const RecommendationRejectionModal = ({
  show,
  onClose,
  onConfirm,
  currentRecommendationModalData,
  seafarerDetails,
}: {
  show: boolean;
  onClose: () => void;
  onConfirm: () => void;
  currentRecommendationModalData: Object;
  seafarerDetails: Seafarer;
}) => {
  const [error, setError] = useState('');
  const [disableConfirm, setDisableConfirm] = useState(false);

  const handleRecommendationRejection = async (formObj) => {
    try {
      setDisableConfirm(true);
      const payload = {
        status: seafarerStatus.CREW_ASSIGNMENT_REJECTED,
        remarks: formObj.remarks,
      };

      const { data } = await seafarerService.approveRejectRecommendation(
        payload,
        currentRecommendationModalData?.id,
      );
      if (data.error) {
        setDisableConfirm(false);
        setError(data.message);
      } else {
        onConfirm();
      }
    } catch (error) {
      setDisableConfirm(false);
      setError(`Oops, something went wrong. Please try again. Error: ${error}`);
    }
  };

  const { handleSubmit, values, handleBlur, handleChange } = useFormik({
    initialValues: {
      remarks: '',
    },
    onSubmit: (valueObj) => {
      handleRecommendationRejection(valueObj);
    },
  });
  return (
    <form autoComplete="off">
      <ConfirmationModal
        show={show}
        title={`Confirm Rejection of ${seafarerDetails?.seafarer_person?.first_name ?? ''} ${
          seafarerDetails?.seafarer_person?.last_name ?? ''
        }'s Recommendation for ${currentRecommendationModalData?.vessel_name} ?`}
        confirmButtonLabel="Confirm"
        onConfirm={handleSubmit}
        dialogClassName="recommendation-rejection-modal"
        cancelButtonLabel="Cancel"
        onClose={onClose}
        disableConfirm={disableConfirm}
      >
        <div>
          <Form.Label htmlFor="remarks">Remarks</Form.Label>
          <Form.Control
            id="remarks"
            as="textarea"
            value={values.remarks}
            onChange={handleChange}
            onBlur={handleBlur}
            maxLength={300}
          />
        </div>
        {seafarerDetails?.crew_planning?.length > 0 && (
          <Alert variant="danger" className="crew-planner-error">
            After rejecting the recommendation, the action can not be reverted. Please{' '}
            <b>inform the POD Manager before rejecting the Recommendation.</b>
          </Alert>
        )}
        {error && (
          <div className="recommendation-error-alert">
            <ErrorAlert message={error} />
          </div>
        )}
      </ConfirmationModal>
    </form>
  );
};

export default RecommendationRejectionModal;
