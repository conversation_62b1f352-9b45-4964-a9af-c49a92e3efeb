import React, { ReactElement, useState } from 'react';
import moment from 'moment-timezone';
import { patchPeriodOnBoard } from '@src/service/seafarer-service';
import { ContractEndChangeData } from '@src/types/update-contract-modal';
import { useNotification } from '@src/component/common/Notification/useNotification';
import { FormField, Textarea } from '@src/component/common/Form';
import ModalForm from '@src/component/common/ModalForm';
import * as schema from '../../../model/SeafarerSchemaValidation';
import AutoDismissibleAlert from '../../common/AutoDismissibleAlert';
import FleetDatePicker from '../../AddSeafarer/FleetDatePicker';

interface FormValues {
  contract_end_date: string;
  remarks: string;
}

interface Props {
  onClose?: Function;
  data: ContractEndChangeData;
  onSubmitCompleted: Function;
  trigger: ReactElement;
}

const UpdateContractDateModal = ({ onClose, data, onSubmitCompleted, trigger }: Props) => {
  const { seafarerId, contractStartDate } = data;
  const remarkMaxLength = 200;
  const [error, setError] = useState<string | null>(null);
  const { notify } = useNotification();

  const handleSubmit = async (values: FormValues) => {
    try {
      // handle submit
      const response = await patchPeriodOnBoard({
        seafarer_id: seafarerId as string,
        expected_contract_end_date: moment(values.contract_end_date).format('YYYY-MM-DD'),
        seafarer_journey_remarks: values.remarks,
      });
      console.log(response);
      notify.success('Contract End Date has been updated successfully!');
      onSubmitCompleted?.(response?.data);
    } catch (err: any) {
      console.log('Error object:', err);
      if (err.response) {
        console.log('Response data:', err.response.data);
        setError(err.response.data || 'An error occurred.');
      } else if (err.request) {
        console.log('No response received:', err.request);
        setError('Network error: No response received.');
      } else {
        setError(err.message);
      }
      throw new Error('Failed to submit');
    }
  };
  const endOfDay = moment().endOf('day');
  const minContractEndDate = moment(contractStartDate).isBefore(endOfDay)
    ? endOfDay.toDate()
    : new Date(contractStartDate);
  const maxContractEndDate = moment(contractStartDate).add(12, 'months').toDate();
  return (
    <ModalForm
      modalProps={{
        title: 'Change Contract End Date',
        okText: 'Save',
      }}
      trigger={trigger}
      onCancel={onClose}
      formConfig={{
        initialValues: {} as FormValues,
        validationSchema: schema.updateContractEndDateSchema,
      }}
      onSubmitFinish={handleSubmit}
    >
      {error && (
        <AutoDismissibleAlert message={error} variant="danger" onClose={() => setError(null)} />
      )}
      <FormField name="contract_end_date" label="New Contract End Date*">
        <FleetDatePicker
          placeholder="Select Date"
          minDate={minContractEndDate}
          maxDate={maxContractEndDate}
          calendarIcon
          isClearable
        />
      </FormField>
      <FormField name="remarks" label="Remarks">
        <Textarea placeholder="Add Remarks" maxLength={remarkMaxLength} showCount />
      </FormField>
    </ModalForm>
  );
};

export default UpdateContractDateModal;
