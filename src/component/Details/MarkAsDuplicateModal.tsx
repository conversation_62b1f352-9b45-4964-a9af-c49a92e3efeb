import React, { useEffect, useRef, useState } from 'react';
import { Button, Form, Modal } from 'react-bootstrap';
import { useHistory } from 'react-router-dom';
import { useDebouncedCallback } from 'use-debounce';
import { capitalizeArgs } from '../../model/utils';
import httpService from '../../service/http-service';
import seafarerService from '../../service/seafarer-service';
import Spinner from '../common/Spinner';
import { checkUserBelongToSameShipParty } from '../../util/view-utils';

const MarkAsDuplicateModal = (props) => {
  const {
    showDuplicateHKIDModal,
    setShowDuplicateHKIDModal,
    parent_hkid,
    setHasStatusChanged,
    seafarerId,
    dropDownOfficeData,
    roleConfig
  } = props;

  const [parentHKID, setParentHKID] = useState(parent_hkid);
  const [parentName, setParentName] = useState('');
  const [existingParentLink, setExistingParentLink] = useState({ id: '', hkid: '' });
  const [loading, setLoading] = useState(false);
  const [duplicateLinkError, setDuplicateLinkError] = useState('');
  const history = useHistory();
  const ref = useRef(null);
  const [isBelongToSameShipParty, setIsBelongToSameShipParty] = useState(false);

  useEffect(() => {
    (async () => {
      try {
        setLoading(true);
        setDuplicateLinkError('');
        if (parentHKID) {
          ref.current.value = parentHKID;
          const { data } = await seafarerService.getParentSeafarerDetails(parentHKID);
          if (data.results.length === 0) {
            setDuplicateLinkError('No seafarer found with this ID');
          } 
          else if (data.results[0].parent_hkid) {
            const { data: resp } = await seafarerService.getParentSeafarerDetails(data.results[0].parent_hkid);
            if (resp.results.length !== 0) {
              const formData = {
                id: resp.results[0].id, 
                hkid: data.results[0].parent_hkid
              }
              setExistingParentLink(formData)
            }  
            setDuplicateLinkError(`This HKID is already linked to a Main Profile HKID `);
          }
          else {
            const isShipParty = checkUserBelongToSameShipParty(dropDownOfficeData, data.results[0], roleConfig);
            setIsBelongToSameShipParty(isShipParty);
            const fullName = capitalizeArgs(
              data.results[0].seafarer_person.first_name,
              data.results[0].seafarer_person.last_name,
            );
            const rankTitle = data.results[0].seafarer_rank.value;
            const headingTitle = `${fullName} (${rankTitle}) `;
            setParentName(headingTitle);
          }
        } else {
          setDuplicateLinkError('');
          setParentName('');
        }
        setLoading(false);
      } catch (error) {
        if (httpService.axios.isCancel(error)) {
          return;
        }
        setDuplicateLinkError(`${error}`);
        setLoading(false);
      }
    })();
  }, [parentHKID]);

  const handleParentHKIDChange = useDebouncedCallback((value) => {
    setParentHKID(value.trim());
  }, 500);

  const patchParentHKID = async (newParentHKID) => {
    try {
      setLoading(true);
      const hkid = newParentHKID ? parseInt(newParentHKID) : '';
      const reqData = {
        id: seafarerId,
        hkid : newParentHKID === '' ? parent_hkid : hkid,
        removeParent : hkid === ''
      };
      await seafarerService.patchParentHKID(reqData)
      setLoading(false);
      setShowDuplicateHKIDModal(false);
      setHasStatusChanged(Math.random());
    } catch (error) {
        setLoading(false);
        setDuplicateLinkError(error.response?.data ?? error.toString());
    }
  };

  const visitParentSeafarer = () => {
    setShowDuplicateHKIDModal(false);
    history.push(`/seafarer/details/${existingParentLink.id}/general`);
  };

  const getErrorMessage = () => {
    if(duplicateLinkError.includes('This HKID is already linked to a Main Profile HKID')){
      return (
        <span className="parent-seafarer-error">{duplicateLinkError}
          <Button variant="link" className="parent-seafarer-error parent-link" onClick={visitParentSeafarer}>
            {existingParentLink.hkid}
          </Button>
        </span>
      )
    }
    return (
      duplicateLinkError ? (
        <span className="parent-seafarer-error">{duplicateLinkError}</span>
      ) : (
        <span className="parent-seafarer-name">{isBelongToSameShipParty && parentName}</span>
      )
    )
  }
  

  return (
    <Modal show={showDuplicateHKIDModal} centered>
      <Modal.Header>
        <Modal.Title>Mark as Duplicate</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <span className="parent-hkid-label">Main Profile HKID (Seafarer ID)</span>
        <Form.Control
          id="parent-hkid"
          type="text"
          name="parent-hkid-name"
          onChange={(e) => handleParentHKIDChange(e.target.value)}
          ref={ref}
        />
        <div className="px-2 py-1">
          {loading ? (
            <div className="small-spinner pt-1">
              <Spinner alignClass="" />
            </div>
          ) : getErrorMessage()}
        </div>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="primary" onClick={() => setShowDuplicateHKIDModal(false)}>
          Cancel
        </Button>
        <Button
          variant="secondary"
          disabled={duplicateLinkError || loading}
          className="mark-duplicate-savebtn"
          onClick={() => patchParentHKID(parentHKID)}
        >
          Save
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default MarkAsDuplicateModal;
