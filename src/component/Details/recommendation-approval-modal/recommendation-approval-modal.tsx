import React, { KeyboardEvent, useEffect, useState, useRef } from 'react';
import { useFormik } from 'formik';
import { Form, InputGroup } from 'react-bootstrap';
import DatePicker from 'react-datepicker';
import { seafarerStatus } from '../../../model/constants';
import calendarIcon from '../../../../public/icons/calendar-icon.svg';
import ErrorAlert from '../../common/ErrorAlert';
import { dateAsDash } from '../../../model/utils';
import seafarerService from '../../../service/seafarer-service';
import { Seafarer } from '../../../types/seafarerInterfaces';
import { formatDate, numberWithCommas } from '../../../util/view-utils';
import { ConfirmationModal } from '../../confirmation-modal/confirmation-modal';
import '../scss/recommendation-approval-modal.scss';
import { DEFAULT_CURRENCY_UNIT } from '@src/constants/seafarer-wages';

const RecommendationApprovalModal = ({
  show,
  onClose,
  onConfirm,
  seafarerDetails,
  currentRecommendationModalData,
  ranks,
}: {
  show: boolean;
  onClose: () => void;
  onConfirm: () => void;
  seafarerDetails: Seafarer;
  currentRecommendationModalData: Object;
  ranks: Array<{ id: number; value: string; unit: string }>;
}) => {
  const [isLoadingSeafarerList, setIsLoadingSeafarerList] = useState(true);
  const [error, setError] = useState('');
  const [disableConfirm, setDisableConfirm] = useState(false);
  const [replacingSeafarers, setReplacingSeafarers] = useState([]);

  const datePickerRef = useRef(null);

  useEffect(() => {
    (async () => {
      try {
        const queryParams = `vessel_ownership_id=${
          currentRecommendationModalData?.vessel_ownership_id
        }&crew_list_status_date=${dateAsDash(
          new Date(currentRecommendationModalData?.expected_contract_start_date),
        )}`;
        const { data } = await seafarerService.getCrewList(queryParams);
        if (data.results) {
          setReplacingSeafarers(
            data.results.map((seafarer) => {
              return {
                id: seafarer.id,
                seafarer_person_id: seafarer.seafarer_person_id,
                hkid: seafarer.hkid,
                seafarer_person: seafarer.seafarer_person,
                rank_id: seafarer.rank_id,
              };
            }),
          );
        } else {
          setError(data.message);
        }
      } catch (error) {
        setError(`Oops, something went wrong. Please try again. Error: ${error}`);
      } finally {
        setIsLoadingSeafarerList(false);
      }
    })();
  }, []);

  const handleApproveRecommendation = async (formObj) => {
    try {
      setDisableConfirm(true);
      const payload = {
        status: seafarerStatus.CREW_ASSIGNMENT_APPROVED,
        remarks: formObj.remarks,
        expected_contract_start_date: formatDate(formObj.expectedSignOnDate, 'YYYY-MM-DD'),
        replaced_by_id: parseInt(formObj.replacing),
      };

      const { data } = await seafarerService.approveRejectRecommendation(
        payload,
        currentRecommendationModalData?.id,
      );
      if (data.error) {
        setDisableConfirm(false);
        setError(data.message);
      } else {
        onConfirm();
      }
    } catch (error) {
      setDisableConfirm(false);
      setError(`Oops, something went wrong. Please try again. Error: ${error}`);
    }
  };

  const getRankValue = (rankId: number) => {
    if (!rankId) {
      return '';
    }
    const rank = ranks.find((rank) => rank.id === rankId);
    if (rank) {
      return ` (${rank.unit})`;
    }
    return '';
  };

  const { handleSubmit, values, setFieldValue, handleBlur, handleChange } = useFormik({
    initialValues: {
      expectedSignOnDate: new Date(currentRecommendationModalData?.expected_contract_start_date),
      replacing: '',
      remarks: '',
      wages: currentRecommendationModalData?.recommended_wages
        ? numberWithCommas(currentRecommendationModalData?.recommended_wages)
        : '',
    },
    onSubmit: (valueObj) => {
      handleApproveRecommendation(valueObj);
    },
  });

  const replacingSeafarerPlaceholder = () => {
    if (isLoadingSeafarerList) {
      return 'Loading...';
    }
    if (replacingSeafarers.length) {
      return 'Please Select';
    }
    return 'Empty Crew List';
  };
  const currencyUnit = currentRecommendationModalData?.recommended_wages_unit?.toUpperCase() || DEFAULT_CURRENCY_UNIT;

  const openDatePicker = () => {
    if (datePickerRef.current) {
      datePickerRef.current.setFocus();
    }
  };

  return (
    <form autoComplete="off" data-testid="recommendation-approval-modal">
      <ConfirmationModal
        show={show}
        title={`Confirm Approval of ${seafarerDetails?.seafarer_person?.first_name ?? ''} ${
          seafarerDetails?.seafarer_person?.last_name ?? ''
        }'s Recommendation for ${currentRecommendationModalData?.vessel_name} ?`}
        confirmButtonLabel="Confirm"
        onConfirm={handleSubmit}
        dialogClassName="recommendation-approval-modal"
        cancelButtonLabel="Cancel"
        onClose={onClose}
        disableConfirm={disableConfirm}
      >
        <div data-testid="recommendation-approval-modal">
          <Form.Label htmlFor="date" className="font-weight-bold">
            Expected Sign On Date
          </Form.Label>
          <div className="calendar">
            <DatePicker
              ref={datePickerRef}
              selected={values.expectedSignOnDate}
              id="date"
              name="date"
              dateFormat="d MMM yyyy"
              placeholderText="Please select"
              onBlur={handleBlur}
              minDate={new Date()}
              onChange={(selectedDate) => {
                setFieldValue('expectedSignOnDate', selectedDate);
              }}
              onKeyDown={(e: KeyboardEvent) => {
                if (e.key === 'e' || e.key === '-') {
                  e.preventDefault();
                }
              }}
            />
            <img
              src={calendarIcon}
              onClick={openDatePicker}
              onKeyDown={openDatePicker}
              tabIndex={0}
            />
          </div>
          <Form.Label htmlFor="date">Replacing</Form.Label>
          <Form.Control
            as="select"
            value={values.replacing}
            onChange={(e) => {
              setFieldValue('replacing', e.target.value);
            }}
            disabled={!replacingSeafarers.length}
          >
            <option value="">{replacingSeafarerPlaceholder()}</option>
            {replacingSeafarers.map((seafarer) => (
              <option key={seafarer.seafarer_person_id} value={seafarer.seafarer_person_id}>
                {`${seafarer.seafarer_person?.first_name ?? ''} ${
                  seafarer.seafarer_person?.middle_name ?? ''
                } ${seafarer.seafarer_person?.last_name ?? ''}${getRankValue(seafarer.rank_id)} (${
                  seafarer.hkid
                })`}
              </option>
            ))}
          </Form.Control>
          <Form.Label htmlFor="wages">Wages</Form.Label>
          <InputGroup>
          <InputGroup.Prepend>
            <InputGroup.Text style={{ marginBottom: '1rem' }}>{currencyUnit}</InputGroup.Text>
            </InputGroup.Prepend>
            <Form.Control
              id="wages"
              className="wages-input"
              data-testid="wages"
              name="wages"
              type="text"
              value={values.wages}
              onChange={handleChange}
              onKeyDown={(e: KeyboardEvent) => {
                if (e.key === 'e' || e.key === '-') {
                  e.preventDefault();
                }
              }}
              onBlur={handleBlur}
              disabled={true}
            ></Form.Control>
          </InputGroup>
          <Form.Label htmlFor="remarks">Remarks</Form.Label>
          <Form.Control
            id="remarks"
            data-testid="remarks"
            name="remarks"
            as="textarea"
            value={values.remarks}
            onChange={handleChange}
            onBlur={handleBlur}
            maxLength={300}
          />
        </div>
        <div className="recommendation-error-alert">
          {error ? <ErrorAlert message={error} /> : ''}
        </div>
      </ConfirmationModal>
    </form>
  );
};

export default RecommendationApprovalModal;
