import React from 'react';
import { Button } from 'react-bootstrap';
import { useHistory } from 'react-router-dom';
import httpService from '../../service/http-service';
import seafarerService from '../../service/seafarer-service';

const DuplicateChildHKIDInfo = ({ setIsLoading, childHKIDData, setChildHKID, eventTracker }) => {
  const history = useHistory();
  const backToParentHKID = (childhkid) => {
    (async () => {
      try {
        setChildHKID([]);
        setIsLoading(true);
        const { data } = await seafarerService.getParentSeafarerDetails(childhkid);
        if (data.results.length !== 0) {
          history.push(`/seafarer/details/${data.results[0].id}/general`);
        }
        setIsLoading(false);
      } catch (error) {
        if (httpService.axios.isCancel(error)) {
          return;
        }
        console.error(error);
      }
      setIsLoading(false);
    })();
  };

  const displayDuplicateChildHKID = () => {
    return childHKIDData?.map((data) => {
      return (
        <Button
          key={data.hkid}
          variant="link"
          onClick={(e) => {
            eventTracker('viewDuplicate', 'View duplicate seafarer');
            backToParentHKID(data.hkid);
          }}
        >
          {data.hkid}
        </Button>
      );
    });
  };
  return (
    <span className="duplicate_child content"> Duplicate Secondary Profile HKID(s)
      {displayDuplicateChildHKID()}
    </span>
  );
};

export default DuplicateChildHKIDInfo;
