import React, { useState } from 'react';
import { <PERSON><PERSON>, Modal } from 'react-bootstrap';
import moment from 'moment-timezone';
import ReactDatePicker from 'react-datepicker';
import ErrorAlert from '@src/component/common/ErrorAlert';
import { setSeafarerToTravel } from '../../../service/seafarer-service';
import { Seafarer, SeafarerStatusHistory } from '../../../types/seafarerInterfaces';

const SetSeafarerToTravelModal = ({
  show,
  onClose,
  onConfirm,
  seafarerDetails,
  statusHistory,
  roleConfig,
}: {
  roleConfig: any;
  show: boolean;
  onClose: () => void;
  onConfirm: () => void;
  seafarerDetails: Seafarer;
  statusHistory: SeafarerStatusHistory;
}) => {
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [date, setDate] = useState(
    statusHistory?.expected_contract_start_date
      ? new Date(statusHistory.expected_contract_start_date)
      : null,
  );

  const getMinDate = () => {
    if (roleConfig.seafarer.edit.seafarerSuperUser) {
      return new Date(moment().subtract(1, 'month').toISOString());
    }
    return new Date();
  };
  const handleSubmit = async () => {
    try {
      setError('');
      setLoading(true);
      await setSeafarerToTravel({
        seafarer_id: seafarerDetails.id,
        status_date: moment(date).format('YYYY-MM-DD'),
      });
      onConfirm();
    } catch (e) {
      console.log(e?.response?.data);
      setError(` ${e?.response?.data ?? 'Oops, something went wrong. Please try again.'}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal size="lg" className="modal-container" show={show} onHide={onClose}>
      <Modal.Header style={{ borderBottom: '1px solid #DEE2E6' }}>
        <Modal.Title>Setting Seafarer to travel</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <div className="d-flex justify-content-between align-items-center">
          <p className="mb-0">Select Travel Date</p>
          <p style={{ width: '400px' }}>
            <ReactDatePicker
              isClearable
              minDate={getMinDate()}
              onChange={setDate}
              selected={date}
              dateFormat="dd MMM yyyy"
              data-testid="travel-date"
            />
          </p>
        </div>
        {error && (
          <div className="recommendation-error-alert" style={{ zIndex: 0 }}>
            <ErrorAlert style={{ zIndex: 0 }} message={error} />
          </div>
        )}
      </Modal.Body>

      <Modal.Footer style={{ borderTop: '1px solid #DEE2E6' }}>
        <Button variant="primary" onClick={onClose}>
          Cancel
        </Button>

        <Button
          variant="secondary"
          disabled={!date || loading}
          onClick={handleSubmit}
          className={!date || loading ? 'disabled-gray' : ''}
        >
          {loading ? (
            <div style={{ width: '60px' }}>
              <span className="spinner-border spinner-border-sm" />
            </div>
          ) : (
            <>Confirm</>
          )}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default SetSeafarerToTravelModal;
