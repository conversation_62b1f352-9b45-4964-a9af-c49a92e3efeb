import React, { useEffect, useState } from 'react';
import { ConfirmationModal } from '../../confirmation-modal/confirmation-modal';
import '../scss/recommendation-with-deviation-modal.scss';
import { Grid } from '../../grid/grid';
import seafarerService from '../../../service/seafarer-service';
import { isBoolean } from 'lodash';

enum ChecklistAnswer {
  'YES' = 'Yes',
  'NO' = 'No',
  'NA' = 'N/A',
}

const getChecklistAnswer = (cell) => {
  if (isBoolean(cell.value)) {
    return cell.value ? ChecklistAnswer.YES : ChecklistAnswer.NO;
  }
  return ChecklistAnswer[cell.value];
};

const recommendationWithDeviationModalGridColumnDefs = [
  {
    Header: 'Description',
    accessor: 'checklist_name',
    Footer: null,
    disableSortBy: true,
    width: 600,
    Cell: ({ flatRows, row, cell }) => {
      return (
        <div>
          {flatRows.indexOf(row) + 1}. {cell.value}
        </div>
      );
    },
    customHeaderCellStyle: {
      paddingLeft: '0px',
    },
    customDataCellStyle: {
      paddingLeft: '0px',
    },
  },
  {
    Header: 'Status',
    accessor: 'checklist_answer',
    Footer: null,
    disableSortBy: true,
    width: 25,
    Cell: (cell: { value: boolean }) => {
      return <div>{getChecklistAnswer(cell)}</div>;
    },
    customHeaderCellStyle: {
      textAlign: 'end',
    },
    customDataCellStyle: {
      textAlign: 'end',
    },
  },
];

const RecommendationWithDeviationModal = ({
  show,
  onClose,
  currentRecommendationDeviationModalData,
}: {
  show: boolean;
  onClose: () => void;
  currentRecommendationDeviationModalData: {
    id: number;
    has_travel_with_fleet: boolean;
  };
}) => {
  const [checkListData, setCheckListData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    (async () => {
      setIsLoading(true);
      try {
        const { data } = await seafarerService.getRecommendedCheckListById(
          currentRecommendationDeviationModalData?.id,
        );
        if (data.error) {
          setIsLoading(false);
          onClose();
        }
        if (data.response) {
          setCheckListData(data.response);
          const checkListResponse = [...data.response];
          /*
           * Checklist Data migrated from Paris 1.0 will have 11 answers
           * For those cases don't push 'Sailed with Fleet in last 3 years' in Paris 2.0
           */
          if (checkListResponse.length == 10) {
            checkListResponse.push({
              recommendation_checklist_id: -1,
              checklist_name: 'Sailed with Fleet in last 3 years',
              checklist_answer: currentRecommendationDeviationModalData?.has_travel_with_fleet,
            });
          }
          setCheckListData(checkListResponse);
        }
        setIsLoading(false);
      } catch (error) {
        setIsLoading(false);
        onClose();
      }
    })();
  }, []);

  return (
    <form autoComplete="off">
      <ConfirmationModal
        show={show}
        title="Recommended with Deviation"
        confirmButtonLabel="Close"
        onConfirm={onClose}
        dialogClassName="recommendation-with-deviation-modal"
        showCancelButton={false}
        size="xl"
      >
        <Grid
          columns={recommendationWithDeviationModalGridColumnDefs}
          data={checkListData}
          showBottomPagination={false}
          gridStyle={{
            marginTop: '2rem',
          }}
          isManualSort={false}
          showTopPagination={false}
          isLoading={isLoading}
        ></Grid>
      </ConfirmationModal>
    </form>
  );
};

export default RecommendationWithDeviationModal;
