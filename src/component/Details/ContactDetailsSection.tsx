import React from 'react';
import { Col, Row } from 'react-bootstrap';
import './scss/details.scss';
import { filterByContactType } from '../../util/view-utils';
import { TELEPHONE, MOBILE, EMAIL, DIGITS } from '../../constants/contactTypes';
import PhoneTextField from './PhoneTextField';
import { capitalizeArgs, Dash } from '../../model/utils';

const hasMany = (items, i) => items.length > 1 && i !== items.length - 1;

const Details = ({ contacts }) => {
  if (contacts.length === 0) return <p>- - -</p>;
  return contacts.map((t, i) => {
    if (t.contact_type !== EMAIL && DIGITS.test(t.contact)) {
      return (
        <PhoneTextField
          key={t.id}
          value={t.contact}
          customClass={`${hasMany(contacts, i) && 'contact-detail'}`}
        />
      );
    }
    return (
      <p className={`${hasMany(contacts, i) && 'contact-detail'}`} key={t.id}>
        {t.contact}
      </p>
    );
  });
};

const formatAddress = (address) => {
  const country = address.country ? address.country.value : '';
  const formattedAddress =
    capitalizeArgs(
      address.other_address,
      address.building,
      address.city,
      address.state,
      country,
      address.postal_zip_code,
    ) || Dash;
  return formattedAddress;
};

const Addresses = ({ addresses }) => {
  if (addresses.length === 0) return <p>- - -</p>;
  return addresses.map((ad, i) => (
    <p className={`${hasMany(addresses, i) && 'contact-detail'}`} key={ad.id}>
      {formatAddress(ad)}
    </p>
  ));
};

const ContactDetailsSection = ({ contacts = [], addresses, isAddColumnWiseCss = false }) => {
  const telNumbers = contacts.filter(filterByContactType(TELEPHONE)) || [];
  const mobileNums = contacts.filter(filterByContactType(MOBILE)) || [];
  const emails = contacts.filter(filterByContactType(EMAIL)) || [];
  return (
    <>
      {isAddColumnWiseCss ? (
        <>
          <div className="details_page__table_head no-border">
            <div className="font-weight-bold p-2">Contact Details</div>
          </div>
          <Row>
            <Col>
              <table className="table table-hover">
                <tbody>
                  <tr>
                    <td className="details_page__row-name">Telephone Number</td>
                    <td className="details_page__row-value">
                      <Details contacts={telNumbers} />
                    </td>
                  </tr>
                  <tr>
                    <td className="details_page__row-name">Mobile Number</td>
                    <td className="details_page__row-value">
                      <Details contacts={mobileNums} />
                    </td>
                  </tr>
                </tbody>
              </table>
            </Col>
            <Col>
              <table className="table table-hover">
                <tbody>
                  <tr>
                    <td className="details_page__row-name">Email Address</td>
                    <td
                      className="details_page__row-value"
                      data-testid="fml-seafarer-contact-details-email-address-with-column-wise-css"
                    >
                      <Details contacts={emails} />
                    </td>
                  </tr>
                  <tr>
                    <td className="details_page__row-name">Address</td>
                    <td className="details_page__row-value">
                      <Addresses addresses={addresses} />
                    </td>
                  </tr>
                </tbody>
              </table>
            </Col>
          </Row>
        </>
      ) : (
        <Row>
          <Col>
            <table className="table table-hover">
              <thead className="details_page__table_head">
                <tr>
                  <th colSpan="2">Contact Details</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td className="details_page__row-name">Telephone Number</td>
                  <td className="details_page__row-value">
                    <Details contacts={telNumbers} />
                  </td>
                </tr>
                <tr>
                  <td className="details_page__row-name">Mobile Number</td>
                  <td className="details_page__row-value">
                    <Details contacts={mobileNums} />
                  </td>
                </tr>
                <tr>
                  <td className="details_page__row-name">Email Address</td>
                  <td
                    className="details_page__row-value"
                    data-testid="fml-seafarer-contact-details-email-address-without-column-wise-css"
                  >
                    <Details contacts={emails} />
                  </td>
                </tr>
                <tr>
                  <td className="details_page__row-name">Address</td>
                  <td className="details_page__row-value">
                    <Addresses addresses={addresses} />
                  </td>
                </tr>
              </tbody>
            </table>
          </Col>
        </Row>
      )}
    </>
  );
};

export default ContactDetailsSection;
