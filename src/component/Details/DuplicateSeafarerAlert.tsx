import React, { useEffect, useState } from 'react';
import { Al<PERSON>, Button } from 'react-bootstrap';
import { useHistory } from 'react-router-dom';
import httpService from '../../service/http-service';
import seafarerService from '../../service/seafarer-service';

const DuplicateSeafarerAlert = ({ parentHKID, eventTracker }) => {
  const history = useHistory();
  const [seafarerId, setSeafarerId] = useState('');

  useEffect(() => {
    (async () => {
      try {
        const { data } = await seafarerService.getParentSeafarerDetails(parentHKID);
        if (data.results.length !== 0) {
          setSeafarerId(data.results[0].id)
        } 
      } 
      catch (error) {
        if (httpService.axios.isCancel(error)) {
          return;
        }
        console.error(error);
      }
    })();
  }, [parentHKID]);

  const visitParentSeafarer = () => {
    eventTracker('viewDuplicate', 'View duplicate seafarer');
    history.push(`/seafarer/details/${seafarerId}/general`);
  };

  return (
    <Alert variant="danger" className="duplicate_alert">
      <h4 className="duplicate_alert heading">Duplicate Seafarer Profile</h4>
      <span className="duplicate_alert content">Main Profile HKID (Seafarer ID){' '} 
      <Button variant="link" className="parent-link" onClick={visitParentSeafarer}>
        {parentHKID}
      </Button>
      </span>
    </Alert>
  );
};

export default DuplicateSeafarerAlert;
