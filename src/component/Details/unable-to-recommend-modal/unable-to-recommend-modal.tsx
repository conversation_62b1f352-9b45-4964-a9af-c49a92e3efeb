import React from 'react';
import { ConfirmationModal } from '../../confirmation-modal/confirmation-modal';
import '../scss/unable-to-recommend-modal.scss';

const UnableToRecommendModal = ({
  show,
  onConfirm,
  unableToRecommendErrors,
}: {
  show: boolean;
  onConfirm: () => void;
  unableToRecommendErrors: Array<string>;
}) => {
  return (
      <ConfirmationModal
        show={show}
        title="Unable to Recommend"
        showCancelButton={false}
        confirmButtonLabel="OK"
        onConfirm={onConfirm}
        dialogClassName="unable-to-recommend-dialog"
        data-testid="unable-to-recommend-modal"
      >
        <ul>
          {unableToRecommendErrors.map((errMsg) => (
            <>{errMsg}</>
          ))}
        </ul>
      </ConfirmationModal>
  );
};
export default UnableToRecommendModal;
