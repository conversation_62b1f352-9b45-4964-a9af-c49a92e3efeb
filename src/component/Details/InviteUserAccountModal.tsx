/* eslint-disable react/display-name */
import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { Button, Col, Form, Modal, Spinner } from 'react-bootstrap';
import { SlashCircleFill } from 'react-bootstrap-icons';
import { useFlexLayout, useSortBy, useTable } from 'react-table';
import { useSticky } from 'react-table-sticky';
import {
  getSeafarerUserAccount,
  updateUserAccount,
  resendEmailInviteToUser,
} from '../../service/user-service';
import { formatOrdinalDate } from '../../util/view-utils';
import DropDownSearchControl from '../AddSeafarer/DropDownSearchControl';
import { SortIcon } from '../common/SortIcon';
import './scss/invite-user-account-modal.scss';
import ConfirmActionModalView from '../AddSeafarer/ConfirmActionModalView';

const DEFAULT_DATE_FORMAT_FOR_ACCOUNT_FIELDS = 'DD MM yyyy';

const DEFAULT_USER_ACCOUNT_COLUMNS = [
  {
    Header: 'User ID / Email',
    accessor: 'email',
  },
  {
    Header: 'Created Date',
    accessor: (row) =>
      formatOrdinalDate(row.created_timestamp, DEFAULT_DATE_FORMAT_FOR_ACCOUNT_FIELDS),
  },
  {
    Header: 'Last Log in',
    accessor: (row) =>
      formatOrdinalDate(row.last_login_time, DEFAULT_DATE_FORMAT_FOR_ACCOUNT_FIELDS),
  },
  {
    Header: 'Last Deactivated',
    accessor: (row) =>
      formatOrdinalDate(row.last_deactivated_time, DEFAULT_DATE_FORMAT_FOR_ACCOUNT_FIELDS),
  },
];

const UserAccountTable = memo(function UserAccountTable(props) {
  const { getTableProps, getTableBodyProps, headerGroups, rows, prepareRow } = props;
  return (
    <table {...getTableProps()} className="table sticky min-width-auto">
      <thead>
        {headerGroups.map((headerGroup) => (
          <tr {...headerGroup.getHeaderGroupProps()} key={headerGroup}>
            {headerGroup.headers.map((column) => (
              <th {...column.getHeaderProps()} key={column} className="th">
                {column.render('Header')}
                <SortIcon column={column} />
              </th>
            ))}
          </tr>
        ))}
      </thead>
      <tbody {...getTableBodyProps()}>
        {rows.map((row) => {
          prepareRow(row);
          return (
            <tr {...row.getRowProps()} key={row}>
              {row.cells.map((cell) => {
                return (
                  <td {...cell.getCellProps()} key={cell}>
                    {cell.render('Cell')}
                  </td>
                );
              })}
            </tr>
          );
        })}
      </tbody>
    </table>
  );
});

const GenerateColumns = (setDeactiveEmail: Function, resendEmailInvite: Function) => {
  return React.useMemo(
    () => [
      ...DEFAULT_USER_ACCOUNT_COLUMNS,
      {
        Header: 'Deactivate User',
        accessor: (row) => {
          const color = row.enabled ? '#D41B56' : '#EFEFEF';
          const cursorClass = row.enabled ? { cursor: 'pointer' } : {};
          const handleClick = () => setDeactiveEmail(row.email);
          return (
            <div className="cancel-icon" style={{ ...cursorClass, marginRight: '1rem' }}>
              <SlashCircleFill
                size={24}
                color={color}
                onClick={row.enabled ? handleClick : undefined}
              />
            </div>
          );
        },
      },
      {
        Header: 'Activation Email',
        accessor: (row) => {
          const handleResend = () => resendEmailInvite(row.id);
          const canResend = row.enabled || !row.can_resend_activation_email;
          return (
            <Button
              variant="outline-primary"
              className="resend-email-button"
              disabled={canResend}
              onClick={handleResend}
            >
              Resend Email
            </Button>
          );
        },
      },
    ],
    [],
  );
};

const InviteUserAccountModal = ({
  isVisible,
  onClose,
  emails = [],
  containerStyle = {},
  onAddUser,
  seafarerID,
  selectedEmail,
  setSelectedEmail,
  isCreatingAccount,
}) => {
  const [userAccounts, setUserAccounts] = useState([]);
  const [isLoadingUserAccounts, setIsLoadingUserAccounts] = useState(false);
  const [deactiveEmail,setDeactiveEmail] = useState(null);
  const handleGetSeafarerAccount = useCallback(async (seafarer_id) => {
    try {
      const response = await getSeafarerUserAccount(seafarer_id);
      if (response?.data) setUserAccounts(response.data);
    } catch (error) {
      console.log(
        'Something went wrong on fetching seafarer user account. Here is the full error details:',
        error,
      );
    }
  }, []);

  useEffect(() => {
    // If already fetched some data then no need to do a API call
    const enabledAccounts = userAccounts.filter(({ enabled }) => enabled);
    if (enabledAccounts.length > 0 || isCreatingAccount) return;

    if (seafarerID && isVisible) {
      setIsLoadingUserAccounts(true);
      handleGetSeafarerAccount(seafarerID)
        .then(() => {
          setIsLoadingUserAccounts(false);
        })
        .catch(() => {
          setIsLoadingUserAccounts(false);
        });
    }
  }, [seafarerID, isVisible, isCreatingAccount]);

  const handleChange = (event) => {
    const { value } = event.target;
    setSelectedEmail(value);
  };

  const hasOneActiveAccount = useMemo(() => {
    return userAccounts.some((userAccount) => userAccount.enabled === true);
  }, [userAccounts]);

  const handleDeactiveAccount = useCallback(
    async (userEmail) => {
      try {
        const response = await updateUserAccount(userEmail, { enabled: false });
        setDeactiveEmail(null)
        if (response.status === 200) {
          handleGetSeafarerAccount(seafarerID);
        }
      } catch (error) {
        console.log(`Something went wrong on deactivating user of: ${userEmail}`);
      }
    },
    [seafarerID],
  );

  const resendEmailInvite = useCallback(
    async (userID) => {
      try {
        const response = await resendEmailInviteToUser(userID, {});
        if (response.status === 200) {
          handleGetSeafarerAccount(seafarerID);
        }
      } catch (error) {
        console.log(`Something went wrong on resending email invite for user of: ${userID}`);
      }
    },
    [seafarerID],
  );

  const columns = GenerateColumns(handleDeactiveAccount, resendEmailInvite);

  const { getTableProps, getTableBodyProps, headerGroups, rows, prepareRow } = useTable(
    {
      columns,
      data: userAccounts,
    },
    useSortBy,
    useSticky,
    useFlexLayout,
  );

  const tableProps = {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    rows,
    prepareRow,
  };
  return (
    <>
      <ConfirmActionModalView
        show={deactiveEmail}
        onClose={() => setDeactiveEmail(null)}
        onConfirm={() => handleDeactiveAccount(deactiveEmail)}
        title="Disable User Access?"
        message={<p>Are you sure you want to disable user access for {deactiveEmail}? </p>}
        confirmBtnText="Disable User Access"
      />
      <Modal
        show={isVisible}
        onHide={onClose}
        dialogClassName="invitation-user-modal"
        aria-labelledby="contained-modal-title-vcenter"
        style={containerStyle}
        size="lg"
        centered
        scrollable={false}
      >
        <Modal.Header closeButton>
          <Modal.Title>PARIS 2.0 User Account</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <UserAccountTable {...tableProps} />
          {isLoadingUserAccounts && <Spinner animation="border" />}
          {!isLoadingUserAccounts && !hasOneActiveAccount && (
            <div>
              <hr className="dashed_line" />
              <span className="caption">Select Email Address to link to User Account</span>
              <Form.Row style={{ marginTop: '1rem' }}>
                <Form.Group as={Col} md="5">
                  <Form.Label>Email Address</Form.Label>
                  <DropDownSearchControl
                    name={'email'}
                    selectedValue={selectedEmail}
                    dropDownValues={emails}
                    onInputChange={handleChange}
                    testID="email-input-add-account"
                  />
                </Form.Group>
              </Form.Row>
              <Button
                variant="secondary"
                onClick={onAddUser}
                disabled={!selectedEmail || isCreatingAccount}
                style={!selectedEmail ? { backgroundColor: 'gray', borderColor: 'gray' } : {}}
                className="invite-add-button"
              >
                {isCreatingAccount ? <Spinner color="#fff" animation="border" size="sm" /> : 'Add'}
              </Button>
            </div>
          )}
        </Modal.Body>

        <Modal.Footer>
          <Button variant="primary" onClick={onClose}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export { InviteUserAccountModal };
