import { storePageSort } from '@src/util/local-storage-helper';
import React, { useEffect, useMemo, useCallback, useRef, useState } from 'react';
import { Column, useFlexLayout, useSortBy, useTable } from 'react-table';
import { useSticky } from 'react-table-sticky';
import { columnSortEventName, columnSortIconName } from '@src/util/view-utils';
import { Icon } from '@src/styleGuide';
import { useVirtualizer } from '@tanstack/react-virtual';
import classNames from 'classnames';
import Spinner from './common/Spinner';

export const DEFAULT_PAGE_SIZE = 50;
interface InfiniteScrollTableProps<T extends object> {
  tableClassName?: string;
  tabName: string;
  columns: Column<T>[];
  data: T[];
  fetchData: (params: { pageSize: number; sortBy: any; pageIndex: number }) => void;
  visitSeafarer: (id: string, tabName: string) => void;
  eventTracker: (event: string, data: any) => void;
  loading: boolean;
  quickSearchParams: any;
  advancedSearchParams: any;
  init_sort: any;
  hasMoreData: boolean;
  noDataText?: string;
}
const InfiniteScrollTable = React.memo(
  <T extends object>({
    tableClassName,
    tabName,
    columns,
    data,
    fetchData,
    visitSeafarer,
    eventTracker,
    loading,
    quickSearchParams,
    advancedSearchParams,
    init_sort,
    hasMoreData,
    noDataText,
  }: InfiniteScrollTableProps<T>) => {
    const [pageIndex, setPageIndex] = useState(0);
    // Default column settings
    const defaultColumn = useMemo(
      () => ({
        minWidth: 120,
        width: 120,
      }),
      [],
    );

    // Initialize react-table. We still use manualPagination/manualSortBy to allow external fetchData calls.
    const {
      getTableProps,
      getTableBodyProps,
      headerGroups,
      prepareRow,
      rows,
      state: { sortBy },
    } = useTable(
      {
        columns,
        data,
        defaultColumn,
        initialState: { sortBy: init_sort },
        manualPagination: true,
        manualSortBy: true,
        autoResetPage: false,
        autoResetSortBy: false,
      },
      useSortBy,
      useFlexLayout,
      useSticky,
    );

    // We'll use this container ref for both scrolling and virtualization.
    const tableContainerRef = useRef<HTMLDivElement>(null);
    // Ref to avoid duplicate fetches during rapid scroll events.
    const loadingRef = useRef(false);

    // Set up virtualization for the table rows
    const rowVirtualizer = useVirtualizer({
      count: rows.length,
      estimateSize: () => 38, // Estimated row height; adjust as needed.
      getScrollElement: () => tableContainerRef.current,
      overscan: 5,
    });

    // Save sort changes to local storage
    useEffect(() => {
      storePageSort(tabName, sortBy);
    }, [sortBy, tabName]);

    // Reset page on mount or when search/sort changes.
    useEffect(() => {
      const newPageIndex = 0;
      setPageIndex(newPageIndex);
      fetchData({ pageSize: DEFAULT_PAGE_SIZE, sortBy, pageIndex: newPageIndex });
    }, [tabName, sortBy, quickSearchParams, advancedSearchParams, fetchData]);

    // Scroll event handler for infinite loading.
    const handleScroll = useCallback(() => {
      // Get table container position relative to viewport.
      const rect = tableContainerRef.current?.getBoundingClientRect();
      // Check if the bottom of the table is within 5px of the viewport bottom.
      const isBottom = rect?.bottom <= window.innerHeight + 5;
      if (isBottom && hasMoreData && !loading) {
        // Prevent duplicate fetches.
        loadingRef.current = true;
        const newPageIndex = pageIndex + 1;
        setPageIndex(newPageIndex);
        fetchData({ pageSize: DEFAULT_PAGE_SIZE, sortBy, pageIndex: newPageIndex });
      }
    }, [eventTracker, fetchData, loading, pageIndex, sortBy, tabName]);

    // Attach the scroll event to the window.
    useEffect(() => {
      window.addEventListener('scroll', handleScroll);
      return () => window.removeEventListener('scroll', handleScroll);
    }, [handleScroll]);

    // Render the header using react-table
    const renderHeader = () => (
      <div className="header">
        {headerGroups.map((headerGroup, index) => (
          <div
            key={headerGroup.id}
            {...headerGroup.getHeaderGroupProps()}
            id={`top-header-${index}`}
            className="tr "
          >
            {headerGroup.headers.map((column, index2) => {
              const thProps = column.getHeaderProps(column.getSortByToggleProps());
              return (
                <div key={column.id} {...thProps} className="th" id={`as-${index} + ${index2}`}>
                  {column.render('Header')}
                  <span>
                    {column.canSort && (
                      <Icon
                        icon={columnSortIconName(column)}
                        size={20}
                        className="default"
                        onClick={() => eventTracker('sortBy', columnSortEventName(column))}
                      />
                    )}
                  </span>
                </div>
              );
            })}
          </div>
        ))}
      </div>
    );

    // Render each virtualized row.
    const renderRow = (row: any) => {
      prepareRow(row);
      const seafarerId = row.original.id ?? undefined;
      return (
        <div
          key={seafarerId}
          role="row"
          tabIndex={0}
          {...row.getRowProps()}
          className="tr"
          onClick={visitSeafarer.bind(this, seafarerId, tabName)}
          onKeyDown={(event) => {
            if (event.key === 'Enter' || event.key === ' ') {
              visitSeafarer.bind(this, seafarerId, tabName)();
            }
          }}
        >
          {row.cells.map((cell) => {
            const tdProps = cell.getCellProps();
            return (
              <div key={tdProps.key} {...tdProps} className="td">
                {cell.render('Cell')}
              </div>
            );
          })}
        </div>
      );
    };
    InfiniteScrollTable.defaultProps = {
      tableClassName: '',
      noDataText: 'No result match your criteria',
    };
    console.log('testest', data.length);
    // Render the virtualized table body.
    const renderBody = () => {
      return rowVirtualizer.getVirtualItems().map((virtualRow) => {
        const row = rows[virtualRow.index];
        if (!row) return null; // Prevent missing row issues
        console.log('testest', row, virtualRow);
        return renderRow(row, virtualRow);
      });
    };

    // Render empty state if no data.
    const renderEmpty = (noDataText) => {
      return (
        <div style={{ marginTop: '50px' }} className="no-result-found">
          {' '}
          <Icon icon="alert" className="alert-icon-no-search" />
          <div>
            {' '}
            <b>{noDataText ?? 'No result match your criteria'}</b>
          </div>{' '}
        </div>
      );
    };

    const isEmpty = !(loading || data.length > 0);
    useEffect(() => {
      const tableEl = tableContainerRef.current;
      const scrollBarContainer = tableEl?.parentElement?.querySelector(
        '.horizontal-scroll-wrapper',
      );

      if (!tableEl || !scrollBarContainer) return;

      const syncTableScroll = () => {
        scrollBarContainer.scrollLeft = tableEl.scrollLeft;
      };

      tableEl.addEventListener('scroll', syncTableScroll);

      return () => {
        tableEl.removeEventListener('scroll', syncTableScroll);
      };
    }, []);
    return isEmpty ? (
      renderEmpty(noDataText)
    ) : (
      <div className="table-container" style={{ position: 'relative' }}>
        <div
          {...getTableProps()}
          className={classNames(tableClassName, 'table', 'sticky')}
          ref={tableContainerRef}
          data-testid="table-sticky"
          style={{
            overflowY: 'auto',
            overflowX: 'hidden', // Hide native horizontal scroll
          }}
          onScroll={(e) => {
            // Sync horizontal scroll position to the custom scrollbar
            const scrollBarContainer = e.currentTarget.parentElement?.querySelector(
              '.horizontal-scroll-wrapper',
            );
            if (scrollBarContainer) {
              scrollBarContainer.scrollLeft = e.currentTarget.scrollLeft;
            }
          }}
          onWheel={(e) => {
            // Handle touchpad horizontal scrolling
            if (e.deltaX !== 0) {
              e.preventDefault();
              const scrollBarContainer = e.currentTarget.parentElement?.querySelector(
                '.horizontal-scroll-wrapper',
              );
              if (scrollBarContainer) {
                scrollBarContainer.scrollLeft += e.deltaX;
              }
            }
          }}
        >
          {renderHeader()}

          <div {...getTableBodyProps()} className="body">
            {data.length > 0 ? renderBody() : null}
          </div>

          {loading && <Spinner alignClass="load-spinner" />}
        </div>

        {/* Fixed horizontal scrollbar container */}
        <div
          className="fixed-horizontal-scrollbar horizontal-scroll-wrapper"
          onScroll={(e) => {
            if (tableContainerRef.current) {
              tableContainerRef.current.scrollLeft = e.currentTarget.scrollLeft;
            }
          }}
        >
          <div
            style={{
              width: tableContainerRef.current?.firstChild?.scrollWidth || '100%',
              height: '1px',
            }}
          />
        </div>
      </div>
    );
  },
);

InfiniteScrollTable.displayName = 'InfiniteScrollTable';
InfiniteScrollTable.defaultProps = {
  tableClassName: '',
};

export default InfiniteScrollTable;
