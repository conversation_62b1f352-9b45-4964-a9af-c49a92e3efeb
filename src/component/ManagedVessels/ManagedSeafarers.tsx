/* eslint-disable react/display-name */
/* eslint-disable react/prop-types */
import React from 'react';
import { Container } from 'react-bootstrap';
import moment from 'moment-timezone';
import { flatten } from 'lodash';
import { capitalizeFirstLetter } from '@src/model/utils';
import { CREW_PLANNING_STATUS_MAPPING, STATUS } from '@src/constants/crewPlanner';
import { getSeafarerColumns } from './Column';
import DynamicFilter from '../CrewPlanner/DynamicFilter';
import GroupButtonSelector from '../common/GroupButtonSelector';
import InfiniteScrollTable from '../common/InfiniteScrollTable';

export const SeafarerFilter = ({ isClubbedOrUnclubbed } = {}) => [
  {
    type: 'search',
    debounce: true,
    props: {
      placeholder: 'Search by Name or HKID 1',
    },
    name: 'search',
    queryKey: 'keyword',
    className: 'input-col first-col',
    inputType: 'string',
  },
  {
    type: 'drop_down',
    props: {
      placeholder: 'Select Rank',
      isSearchable: true,
      multiple: true,
    },
    name: 'rank',
    dataKey: 'seafarer.ranks',
    queryKey: 'seafarer_person:seafarer_status_history:seafarer_rank.value',
    apiQueryValueSelector: (value, data) =>
      value?.map((v) => data.find((d) => d.id === v)?.value) ?? [],
    inputType: 'array_number',
    className: 'input-col',
    global: true,
    seperator: '|',
  },
  {
    type: 'drop_down',
    props: {
      placeholder: 'Select Vessel',
      isSearchable: true,
      multiple: true,
    },
    name: 'vessel',
    dataKey: 'vessels',
    queryKey: 'seafarer_person:seafarer_status_history.vessel_id',
    inputType: 'array_number',
    className: 'input-col',
    seperator: '|',
    hidden: isClubbedOrUnclubbed,
  },
  {
    type: 'drop_down',
    props: {
      placeholder: 'Select Owner',
      isSearchable: true,
      multiple: true,
    },
    name: 'owner',
    dataKey: 'vessel.owners',
    queryKey: 'vessel_ownership_id',
    apiQueryValueSelector: (value, data) =>
      flatten(value?.map((v) => data.find((d) => d.id === v)?.ownership_ids)) ?? [],
    inputType: 'array_number',
    className: 'input-col',
    global: true,
  },
  {
    type: 'drop_down',
    props: {
      placeholder: 'Select Vessel Category',
      isSearchable: true,
      multiple: true,
    },
    name: 'vessel_category',
    dataKey: 'vessel.vesselTypes',
    queryKey: 'vessel_type',
    apiQueryValueSelector: (value, data) =>
      value?.map((v) => data.find((d) => d.id === v)?.value) ?? [],
    inputType: 'array_number',
    className: 'input-col',
    global: true,
    seperator: '|',
  },
  {
    type: 'date_range',
    props: {
      placeholderText: 'Date Range',
      dateFormat: 'd MMM yyyy',
      isClearable: true,
    },
    name: 'due_off_date',
    dataKey: 'tech_group.tech_group',
    debounce: true,
    queryKey: 'seafarer_person:seafarer_status_history.expected_contract_end_date',
    inputType: 'array_date',
    dateFormat: 'YYYY-MM-DD',
    apiQueryValueSelector: (value) => value.map((v) => (v ? moment(v).format('YYYY-MM-DD') : null)),
    className: 'input-col',
  },
  {
    type: 'drop_down',
    props: {
      placeholder: 'Tech Group',
      isSearchable: true,
      multiple: true,
    },
    name: 'tech_group',
    dataKey: 'tech_group.tech_group',
    queryKey: 'seafarer_person:seafarer_status_history.vessel_tech_group',
    inputType: 'array_string',
    className: 'input-col',
    global: true,
    seperator: '|',
  },
  {
    name: 'crew_planning_status',
    shouldNotRenderInFilters: true,
    queryKey: 'crew_planning_status',
    inputType: 'array_string',
    className: 'input-col',
    global: true,
  },
];

const ManagedSeafarers = React.memo(
  ({
    filterData,
    setApiQuery,
    selectedFilters,
    onFilterChange,
    onCrewPlanningFilterChange,
    eventTracker,
    handleRemark,
    data,
    fetchData,
    hasMoreData,
    loading,
    init_sort,
    handleSearch,
    KPI,
  }) => {
    const seafarerColumns = getSeafarerColumns({
      eventTracker,
      handleRemark,
      selectedFilters,
    });
    const isClubbedOrUnclubbed = [STATUS.club_confirmed, STATUS.clubbed, STATUS.unclubbed].includes(
      selectedFilters?.crew_planning_status?.[0]?.split(',')?.[0],
    );
    return (
      <Container>
        <DynamicFilter
          filters={SeafarerFilter({ isClubbedOrUnclubbed })}
          data={filterData}
          setApiQuery={setApiQuery}
          selectedFilters={selectedFilters}
          onFilterChange={onFilterChange}
          handleSearch={handleSearch}
          eventTracker={eventTracker}
        />
        {KPI}
        <div className="crew-planner-table">
          <div className="group-button-selector-wrapper mb-2">
            <p className="mt-0 mb-0" />
            <GroupButtonSelector
              buttonLabels={Object.keys(CREW_PLANNING_STATUS_MAPPING)}
              onSelect={onCrewPlanningFilterChange}
              selected={capitalizeFirstLetter(
                selectedFilters?.crew_planning_status?.[0]?.split(',')?.[0],
              )}
            />
          </div>
          <InfiniteScrollTable
            hasMoreData={hasMoreData}
            init_sort={init_sort}
            loading={loading}
            fetchData={fetchData}
            data={data ?? []}
            columns={seafarerColumns}
            height="calc(100vh - 350px)"
            sticky
          />
        </div>
      </Container>
    );
  },
);

export default ManagedSeafarers;
