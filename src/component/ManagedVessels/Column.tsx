import React from 'react';
import { checkAdditionalRequest, checkMissingPersonnel } from '@src/pages/CrewPlanner/utils';
import { getStatusByKey, StatusColorMapping, getStatus } from '../CrewPlanner/Column';
import { NAME_STATUS_MAPPING, STATUS } from '../../constants/crewPlanner';
import VesselNameLink from '../CrewList/VesselNameLink';
import SeafarerName from '../CrewPlanner/SeafarerName';
import DueDate from '../CrewPlanner/DueDate';
import { getBadgeColor, getIsClubbedOrUnclubbed, getRank } from '../CrewPlanner/utils';
import parentHKIDLink from '../common/HKIDLink';

export const getSeafarerColumns = ({ eventTracker, handleRemark = () => {}, selectedFilters }) => {
  const { isClubbedOrUnclubbed } = getIsClubbedOrUnclubbed(selectedFilters);

  return [
    {
      type: 'text',
      Header: 'No',
      id: 'number',
      name: 'number',
      accessor: function rankAccessor(row, index) {
        return <div>{index + 1}</div>;
      },
      width: 50,
      order: 1,
      disableSortBy: true,
      sticky: 'left',
    },
    {
      Header: isClubbedOrUnclubbed ? 'Onboard Seafarer' : 'Seafarer Name',
      id: 'seafarer_person.first_name',
      sticky: window.innerWidth > 960 ? 'left' : null,
      order: 2,
      width: 200,
      accessor: function nameAccessor(row) {
        const isMissingPersonnal = checkMissingPersonnel(row);
        const isAdditionalCrewRequest = checkAdditionalRequest(row);
        if (isMissingPersonnal) {
          return (
            <span className="text-danger" style={{ marginLeft: 8 }}>
              <b>Missing Personnel</b>
            </span>
          );
        }
        if (isAdditionalCrewRequest) {
          return (
            <span className="text-danger" style={{ marginLeft: 8 }}>
              <b>Additional crew</b>
            </span>
          );
        }
        return (
          <div className="d-flex align-items-center">
            <span title={`${row?.seafarer_person?.first_name} ${row?.seafarer_person?.last_name}`}>
              {row?.seafarer_person?.first_name || row?.seafarer_person?.last_name ? (
                <SeafarerName id={row?.id} row={row} className="" eventTracker={eventTracker} />
              ) : (
                '---'
              )}
            </span>
          </div>
        );
      },
      disableSortBy: false,
    },
    isClubbedOrUnclubbed && {
      Header: 'Reliever Seafarer',
      id: 'crew_planning.reliever.first_name',
      sticky: window.innerWidth > 960 ? 'left' : null,
      order: 3,
      disableSortBy: true,
      width: 200,
      accessor: function nameAccessor(row) {
        const reliever = row?.crew_planning?.reliever;
        return row?.crew_planning?.crew_planning_status_lookup?.desc !== STATUS.rejected &&
          reliever ? (
          <SeafarerName id={reliever.id} row={reliever} />
        ) : (
          '---'
        );
      },
    },
    {
      type: 'number',
      Header: 'HKID',
      id: 'hkid',
      name: 'hkid',
      accessor: (row) => parentHKIDLink(row),
      order: 3,
      disableSortBy: false,
      minWidth: 80,
      sticky: window.innerWidth > 960 ? 'left' : null,
    },
    {
      type: 'text',
      Header: 'Rank',
      id: 'seafarer_person:seafarer_status_history:seafarer_rank.sortpriority',
      name: 'seafarer_person.seafarer_status_history[0].seafarer_rank.sortpriority',
      accessor: function rankAccessor(row) {
        const rank = getRank(row) ?? '---';
        return <div title={rank}>{rank}</div>;
      },
      width: 150,
      order: 4,
      showToOwner: true,
      disableSortBy: true,
    },
    {
      Header: 'Status',
      id: 'status',
      accessor: (row) =>
        getStatusByKey(
          NAME_STATUS_MAPPING[getStatus(row?.crew_planning?.crew_planning_status_lookup?.desc)],
          StatusColorMapping,
        ),
      width: 150,
      order: 5,
      disableSortBy: true,
    },
    {
      type: 'text',
      Header: 'Vessel',
      id: 'seafarer_person:seafarer_status_history.vessel_name',
      name: 'seafarer_person.seafarer_status_history[0].vessel_name',
      accessor: (row) => {
        const vesselOwnershipId =
          row?.seafarer_person?.seafarer_status_history?.[0]?.vessel_ownership_id ??
          row.vessel_ownership_id;
        const vesselName =
          row?.seafarer_person?.seafarer_status_history?.[0]?.vessel_name ?? row.vessel_name;
        return (
          <VesselNameLink
            className="text-align-left"
            ownershipId={vesselOwnershipId}
            vesselName={vesselName}
            eventTracker={eventTracker}
          />
        );
      },
      width: 200,
      order: 6,
      disableSortBy: false,
    },
    {
      Header: 'Vessel Category',
      id: 'vessel_category',
      width: 175,
      accessor: (row) =>
        row?.seafarer_person?.seafarer_status_history?.[0]?.vessel_type ??
        row.vessel_type?.value ??
        row.vessel_type ??
        '---',
      order: 7,
      disableSortBy: true,
    },
    !isClubbedOrUnclubbed && {
      type: 'date',
      Header: 'Joining Date',
      id: 'seafarer_person.seafarer_status_history[0].status_date',
      name: 'seafarer_person.seafarer_status_history[0].status_date',
      accessor: function endOfContractAccessor(row) {
        const value = row.seafarer_person?.seafarer_status_history?.[0].status_date;
        return <DueDate colorClass="" value={value} />;
      },
      disableSortBy: true,
      width: 175,
      order: 8,
      showToOwner: true,
    },
    {
      type: 'date',
      Header: 'Due Relief',
      id: 'seafarer_person:seafarer_status_history.expected_contract_end_date',
      name: 'seafarer_person.seafarer_status_history[0].expected_contract_end_date',
      accessor: function endOfContractAccessor(row) {
        const value =
          row?.seafarer_person?.seafarer_status_history?.[0]?.expected_contract_end_date;
        const { colorClass, diff } = getBadgeColor(
          value,
          row?.crew_planning?.crew_planning_status_lookup?.desc,
        );
        return (
          <>
            <DueDate value={value} />
            {value ? <span className={`oval ${colorClass}`}>{`${diff}D`}</span> : ''}
          </>
        );
      },
      disableSortBy: false,
      width: 175,
      order: 9,
      showToOwner: true,
    },
    !isClubbedOrUnclubbed && {
      Header: 'Reporting Office',
      id: 'reporting_office',
      accessor: (row) => row.seafarer_reporting_office?.value ?? '---',
      width: 175,
      order: 10,
      disableSortBy: true,
    },
    {
      Header: 'Vessel Owner',
      id: 'vessel_owner',
      accessor: (row) => {
        return row.vessel_owner ?? row?.owner?.value ?? '---';
      },
      width: 175,
      order: 11,
      disableSortBy: true,
    },
    {
      type: 'text',
      Header: 'Tech Group',
      id: 'seafarer_person:seafarer_status_history.vessel_tech_group',
      name: 'seafarer_person.seafarer_status_history[0].vessel_tech_group',
      accessor: (row) =>
        row?.seafarer_person?.seafarer_status_history?.[0]?.vessel_tech_group ??
        row?.fleet_staff?.tech_group ??
        row?.tech_group ??
        '---',
      width: 175,
      disableSortBy: true,
      order: 12,
    },
    {
      type: 'text',
      Header: 'Nationality',
      id: 'seafarer_person:nationality.value',
      name: 'seafarer_person.nationality.value',
      accessor: function addedByAccessor(row) {
        return (
          <div title={row?.seafarer_person?.nationality?.value ?? '---'}>
            {row?.seafarer_person?.nationality?.value ?? '---'}
          </div>
        );
      },
      order: 13,
      disableSortBy: false,
      minWidth: 120,
      showToOwner: true,
    },
    {
      Header: 'Remarks',
      id: 'remarks',
      width: 270,
      order: 14,
      disableSortBy: true,
      accessor: (row) => (
        <div className="remark-cell" title={row?.crew_planning_remarks?.[0]?.remarks || '---'}>
          <div className="remark-text">{row?.crew_planning_remarks?.[0]?.remarks || '---'}</div>
        </div>
      ),
    },
    isClubbedOrUnclubbed && {
      type: 'date',
      Header: 'Available Date',
      id: 'seafarer_contact_log[0].availability_date',
      name: 'seafarer_contact_log[0].availability_date',
      accessor: function endOfContractAccessor(row) {
        const value = row?.crew_planning?.reliever?.seafarer_contact_log?.[0]?.availability_date;
        const { colorClass, diff } = getBadgeColor(
          value,
          row?.crew_planning?.crew_planning_status_lookup?.desc,
        );
        return (
          <>
            <DueDate value={value} />
            {value ? <span className={`oval ${colorClass}`}>{`${diff}D`}</span> : ''}
          </>
        );
      },
      disableSortBy: true,
      width: 175,
      order: isClubbedOrUnclubbed ? 6 : 5,
      showToOwner: true,
    },
    {
      Header: 'Actions',
      id: 'actions',
      sticky: 'right',
      width: 170,
      order: 15,
      disableSortBy: true,
      accessor: (row, index) => {
        const vesselOwnershipId =
          row?.seafarer_person?.seafarer_status_history?.[0]?.vessel_ownership_id ??
          row.vessel_ownership_id;

        return (
          <div>
            <u>
              <a
                href={`/seafarer/crew-planner/vessel/${vesselOwnershipId}`}
                target="_blank"
                onClick={() => eventTracker('viewDetails', row?.name)}
                rel="noreferrer"
              >
                View Details
              </a>
            </u>
          </div>
        );
      },
    },
  ]
    .filter(Boolean)
    .sort((a, b) => (a.order > b.order ? 1 : -1));
};

export default {
  getSeafarerColumns,
};
