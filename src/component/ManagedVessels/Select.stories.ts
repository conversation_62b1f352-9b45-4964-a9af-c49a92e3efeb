import type { Meta, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';
import Select from './Select';

const meta = {
  title: 'Example/Select',
  component: Select,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    backgroundColor: { control: 'color' },
  },
  args: { onClick: fn() },
} satisfies Meta<typeof Select>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Loading: Story = {
  args: {
    value: null,
    options: [],
    labelKey: 'value',
    placeholder: 'Select Vessel Category',
    loading: true,
  },
};

const vesselCateogryList = [
  {
      "id": 2,
      "value": "Great Field Limited"
  },
  {
      "id": 3,
      "value": "Blue Forest Shipping Co."
  },
  {
      "id": 4,
      "value": "Kowa Kaiun Co. Ltd."
  },
  {
      "id": 6,
      "value": "Scottish Ship Owners & Managers Pty Ltd."
  },
  {
      "id": 7,
      "value": "Seven Seas Carriers"
  },
  {
      "id": 8,
      "value": "Shoei Kisen Kaisha Ltd."
  },
  {
      "id": 9,
      "value": "B. Skaugen Shipping AS"
  },
  {
      "id": 10,
      "value": "Spar Shipping AS"
  },
  {
      "id": 12,
      "value": "Vroon BV"
  },
  {
      "id": 13,
      "value": "Santana Maritime Services Inc."
  },
  {
      "id": 14,
      "value": "Gadot Yam Chemical Shipping Ltd."
  },
  {
      "id": 5,
      "value": "Saito Shipping Co. Ltd."
  },
  {
      "id": 17,
      "value": "Goldwin Shipping Limited"
  },
  {
      "id": 18,
      "value": "SEVEN OCEANS CO., LTD"
  },
  {
      "id": 19,
      "value": "STAR Reefers AS (old)"
  },
  {
      "id": 20,
      "value": "Tachibanaya Co. Ltd."
  },
]
export const VesselList: Story = {
  args: {
    value: 'Great Field Limited',
    options: vesselCateogryList,
    labelKey: 'value',
    placeholder: 'Select Vessel Category ',
    loading: false,
  },
};

