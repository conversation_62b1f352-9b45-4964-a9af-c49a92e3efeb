/* eslint-disable react/display-name */
/* eslint-disable react/prop-types */
import React, { useMemo, useEffect } from 'react';
import { useTable, useSortBy, usePagination, useFlexLayout } from 'react-table';
import { useSticky } from 'react-table-sticky';
import { v4 as uuid } from 'uuid';
import Spinner from '../common/Spinner';
import styleGuide from '../../styleGuide';
import { columnSortIconName } from '../../util/view-utils';

const { Icon } = styleGuide;

const generateColumns = (selectedColumns, eventTracker) => {
  return [
    ...selectedColumns,
    {
      Header() {
        return <div>Actions</div>;
      },
      id: 'actions',
      accessor: (row, index) => (
        <div>
          <u>
            <a
              href={`/seafarer/crew-planner/vessel/${row.id}`}
              target="_blank"
              onClick={() => eventTracker('viewDetails', row?.name)}
            >
              View Details
            </a>
          </u>
        </div>
      ),
      disableSortBy: true,
      maxWidth: 50,
      sticky: 'right',
    },
  ];
};

const ManagedVesselsTable = React.memo(
  ({ columns, loading, data, tableRef, init_sort, setInitSort, eventTracker }) => {
    const generatedColumns = generateColumns(columns, eventTracker);

    return (
      <div className="seafarer-table managed-vessels-table-wrapper">
        <Table
          columns={generatedColumns}
          data={data}
          loading={loading}
          tableRef={tableRef}
          init_sort={init_sort}
          setInitSort={setInitSort}
        />
      </div>
    );
  },
);

const Table = React.memo(({ columns, data, loading, tableRef, init_sort, setInitSort }) => {
  const defaultColumn = useMemo(
    () => ({
      minWidth: 120,
      width: 120,
    }),
    [],
  );

  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    prepareRow,
    page,
    state: { sortBy },
  } = useTable(
    {
      columns,
      data,
      defaultColumn,
      manualPagination: true,
      manualSortBy: true,
      autoResetPage: false,
      autoResetSortBy: false,
      initialState: { sortBy: init_sort },
    },
    useSortBy,
    usePagination,
    useFlexLayout,
    useSticky,
  );

  useEffect(() => {
    setInitSort(sortBy);
  }, [sortBy]);

  const noRecords = (
    <div className="no-records">
      <Icon icon="alert" className="alert-icon" style={{ float: 'none' }} />
      Use Filters to search for data. Please use more filters to reduce the load time.
    </div>
  );

  return (
    <div {...getTableProps()} className="table sticky managed-vessels-table" ref={tableRef}>
      <div className="header">
        {headerGroups.map((headerGroup, index) => (
          <div key={uuid()} {...headerGroup.getHeaderGroupProps()} className="tr">
            {headerGroup.headers.map((column, index2) => {
              const thProps = column.getHeaderProps(column.getSortByToggleProps());
              return (
                <div key={column.id} {...thProps} className="th" id={`as-${index} + ${index2}`}>
                  {column.render('Header')}
                  <span>
                    {column.canSort && (
                      <Icon icon={columnSortIconName(column)} size={20} className="default" />
                    )}
                  </span>
                </div>
              );
            })}
          </div>
        ))}
      </div>
      {loading ? (
        <Spinner alignClass="load-spinner" />
      ) : (
        <div {...getTableBodyProps()} className="body">
          {page.length > 0
            ? page.map((row) => {
              prepareRow(row);
              return (
                <div key={row.id} {...row.getRowProps()} className="tr" tabIndex={0}>
                  {row.cells.map((cell) => {
                        const tdProps = cell.getCellProps();
                        return (
                          <div key={tdProps.key} {...tdProps} className="td">
                            {cell.render('Cell')}
                          </div>
                    );
                  })}
                </div>
              );
            })
            : !loading && noRecords}
        </div>
      )}
    </div>
    );
  },
);

export default ManagedVesselsTable;
