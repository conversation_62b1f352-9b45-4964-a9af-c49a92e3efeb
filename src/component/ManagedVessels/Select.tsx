/* eslint-disable react/display-name */
/* eslint-disable react/prop-types */
import React, { ReactNode, useCallback, useRef, useState } from 'react';
import { Check2, ChevronDown, ChevronUp } from 'react-bootstrap-icons';
import {
  Highlighter,
  Menu,
  MenuItem,
  MenuItemProps,
  MenuProps,
  Typeahead,
  TypeaheadRef,
} from 'react-bootstrap-typeahead';
import 'react-bootstrap-typeahead/css/Typeahead.css';
import Spinner from '../common/Spinner';
import './style.scss';
export type Option = Record<string, any>;
type Value = string | number | null;

export interface SelectProps {
  options: Option[];
  value: Value;
  onChange?: (value: Value | null, option?: Option | null) => void;
  labelKey?: string;
  placeholder?: string;
  loading?: boolean;
  clearButton?: boolean | { clearButton: ReactNode };
  disabled?: boolean;
  className?: string;
  style?: React.CSSProperties;
  isFromCrewManagementPage?: boolean;
  testId?: string;
}
/* 
  Common Select component for dropdowns and search
*/
const Select: React.FC<SelectProps> = (props) => {
  const {
    options,
    loading,
    value,
    onChange,
    labelKey,
    placeholder,
    clearButton,
    disabled,
    isFromCrewManagementPage = false,
    testId,
  } = props;
  const typeaheadRef = useRef<TypeaheadRef>(null);
  const [showMenu, setShowMenu] = useState<boolean>(false);

  const getValue = (option: Option) => {
    return option?.value;
  };

  const getLabel = (option: Option) => {
    return option[labelKey ?? 'label'];
  };

  const getOptionByValue = (value: Value) => {
    if (!value) return [];
    const selectedItem = options.find((option) => option?.value === value);
    return selectedItem ? [selectedItem] : [];
  };

  const onSelect = useCallback(
    (selected: Option) => {
      if (selected.length > 0) {
        onChange?.(getValue(selected[0]), selected[0]);
      } else {
        onChange?.(null, null);
      }
    },
    [onChange],
  );

  const renderMenu = (results, menuProps: MenuProps) => {
    const { id, style } = menuProps;
    const sortedResults = results.toSorted((a, b) => {
      if (a.value < b.value) {
        return -1;
      }
      if (a.value > b.value) {
        return 1;
      }
      return 0;
    });
    const items = sortedResults.map((item, index) => {
      return (
        <MenuItem key={`${item?.value}`} option={item} checked position={index}>
          {renderMenuItemChildren(item, menuProps)}
        </MenuItem>
      );
    });
    return (
      <Menu id={id} style={style}>
        {loading && <Spinner animation="grow" size="sm" />}
        {!loading && items}
      </Menu>
    );
  };

  const handleFilterBy = (option: Option, props: any) => {
    if (!props.text) {
      // Display all the options if there's a selection.
      return true;
    }
    // Otherwise filter on some criteria.
    return getLabel(option)?.toLowerCase().includes(props.text.toLowerCase());
  };

  const renderMenuItemChildren = (option: Option, props: MenuItemProps) => {
    const DropdownIcon =
      getValue(option) === value ? (
        <Check2 className="selected-item-icon" size={20} />
      ) : (
        <div className="managed-vessels-dropdown__dropdown-icon" />
      );

    return (
      <div
        className="menu-item-wrapper"
        data-testid={`vessel-dropdown-item-${option.value}`}
        key={`${option.value}`}
      >
        {DropdownIcon}
        <span>
          <Highlighter search={value ?? ''}>{getLabel(option) ?? '- - -'}</Highlighter>
        </span>
      </div>
    );
  };
  const renderRightIcon = () => {
    if (clearButton) return;
    const RightIcon = showMenu ? ChevronUp : ChevronDown;
    return (
      <RightIcon
        className="angle-icon"
        size={20}
        onClick={() => {
          typeaheadRef?.current?.toggleMenu();
          if (!typeaheadRef?.current?.isMenuShown) {
            typeaheadRef?.current?.focus();
          }
        }}
      />
    );
  };

  const typeaHeadCustomProps: any = {
    renderMenu,
    filterBy: handleFilterBy,
  };

  if (!isFromCrewManagementPage) {
    typeaHeadCustomProps['selected'] = getOptionByValue(value);
  }

  if (isFromCrewManagementPage) {
    typeaHeadCustomProps['defaultSelected'] = getOptionByValue(value);
  }
  return (
    <div className="position-relative">
      <Typeahead
        id="typeahead-wrapper"
        ref={typeaheadRef}
        onMenuToggle={setShowMenu}
        inputProps={{ 'data-testid': testId ?? 'dashboard-header-dropdown' }}
        placeholder={placeholder ?? ''}
        labelKey={labelKey}
        onChange={onSelect}
        options={options}
        disabled={disabled}
        clearButton={clearButton}
        {...typeaHeadCustomProps}
        maxResults={options?.length}
      />
      {renderRightIcon()}
    </div>
  );
};

Select.defaultProps = {
  labelKey: 'label',
};
export default Select;
