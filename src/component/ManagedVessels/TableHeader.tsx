/* eslint-disable import/prefer-default-export */
import React from 'react';
import moment from 'moment-timezone';
import VesselNameLink from '../CrewList/VesselNameLink';
import { ACTION_NOT_REQUIRED, ACTION_REQUIRED } from '../../constants/managedVessels';

const formatDate = (item) => {
  if (!item) {
    return '---';
  }
  return moment(item).format('DD MMM YYYY');
};

export const formatPort = (port, country) => {
  if (!port && !country) {
    return '---';
  }
  if (port && country) {
    return `${port}, ${country}`;
  }
  if (country) {
    return country;
  }
  return port;
};

export const managedVesselsHeader = (eventTracker) => {
  const formatStatus = (value) => {
    if (value) {
      return <span className="managed-vessels-action-required">{ACTION_REQUIRED}</span>;
    }
    if (value === false) {
      return <span className="managed-vessels-action-not-required">{ACTION_NOT_REQUIRED}</span>;
    }
    return '---';
  };

  return [
    {
      Header: 'No.',
      id: 'id',
      type: 'item',
      accessor: (row) => {
        return row.id ?? '---';
      },
      sortType: (a, b) => {
        if (a && b) {
          const aName = a.id ? a.id.toLowerCase() : '';
          const bName = b.id ? b.id.toLowerCase() : '';
          if (aName < bName) return -1;
          if (aName > bName) return 1;
        }
        return 0;
      },
      maxWidth: 60,
      minWidth: 60,
      disableSortBy: true,
    },
    {
      type: 'text',
      Header: 'Vessel Name',
      id: 'name',
      name: 'vessel_name',
      accessor: function vesselAccessor(row) {
        return (
          <VesselNameLink
            ownershipId={row?.id}
            vesselName={row?.name}
            eventTracker={() => {
              eventTracker('clickingVesselName', row?.name);
            }}
            className="text-left managed-vessel-table-vessel-name"
          />
        );
      },
      order: 1,
      disableSortBy: false,
    },
    {
      type: 'text',
      Header: 'Vessel Category',
      id: 'vessel_type',
      name: 'vessel_type',
      accessor: (row) => {
        return row.vessel_type.value ?? '---';
      },
      order: 2,
      disableSortBy: true,
    },
    {
      type: 'text',
      Header: 'Status',
      id: 'status',
      name: 'status',
      accessor: (row) => {
        return formatStatus(row.actionRequired);
      },
      order: 3,
      disableSortBy: true,
    },
    {
      type: 'text',
      Header: 'Next Estd. Port',
      id: 'next_estd_port',
      name: 'next_estd_port',
      accessor: (row) => {
        return formatPort(row?.itinerary?.port, row?.itinerary?.country);
      },
      order: 4,
      disableSortBy: true,
    },
    {
      type: 'text',
      Header: 'Estd. Arrival Date',
      id: 'itinerary.estimated_arrival',
      name: 'estd_arrival_date',
      accessor: (row) => {
        return formatDate(row?.itinerary?.estimated_arrival);
      },
      order: 5,
      disableSortBy: false,
      maxWidth: 150,
      minWidth: 150,
    },
    {
      type: 'text',
      Header: 'Estd. Departure Date',
      id: 'itinerary.estimated_departure',
      name: 'estd_departure_date',
      accessor: (row) => {
        return formatDate(row?.itinerary?.estimated_departure);
      },
      order: 6,
      disableSortBy: false,
      maxWidth: 150,
      minWidth: 150,
    },
  ];
};
