import React from 'react';
import { Col, Form } from 'react-bootstrap';
import FleetDatePicker from '../AddSeafarer/FleetDatePicker';
import DropDownSearchControl from '../AddSeafarer/DropDownSearchControl';
import DocumentTypeDropdown from './DocumentTypeDropdown';
import { FormField } from '../common/Form';
import DocumentFileUpload from './DocumentFileUpload';

const DocumentFormOtherCourses = (props) => {
  let {
    targetFormId,
    formsDropdownValues,
    values,
    errors,
    otherCourseTypesDropdownData = [],
    viewDocument,
    onInputChange,
    handleSelectedFile,
    handleRemoveFile,
  } = props;

  const isEditMode = values.id !== undefined;

  return (
    <Form>
      <Form.Row>
        <DocumentTypeDropdown
          targetFormId={targetFormId}
          onInputChange={onInputChange}
          errors={errors}
          disabled={isEditMode}
          formsDropdownValues={formsDropdownValues}
        />
        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Other Courses Type*</Form.Label>
          <DropDownSearchControl
            name={'other_course_type_id'}
            selectedValue={values.other_course_type_id}
            dropDownValues={otherCourseTypesDropdownData}
            onInputChange={onInputChange}
            isInvalid={!!errors.other_course_type_id}
            testID="other-course-type-id"
            disabled={isEditMode}
          />
          <Form.Control.Feedback
            type="invalid"
            className={errors.other_course_type_id ? 'set-display-block' : ''} //force the Feedback to be appear based on errors.country_id
          >
            {errors.other_course_type_id}
          </Form.Control.Feedback>
        </Form.Group>
      </Form.Row>
      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Course Title</Form.Label>
          <Form.Control
            type="text"
            name="course_title"
            value={values.course_title || ''}
            onChange={onInputChange}
            isInvalid={!!errors.course_title}
          />
          <Form.Control.Feedback type="invalid">{errors.course_title}</Form.Control.Feedback>
        </Form.Group>
        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Institute</Form.Label>
          <Form.Control
            type="text"
            name="institute"
            value={values.institute || ''}
            onChange={onInputChange}
            isInvalid={!!errors.institute}
          />
          <Form.Control.Feedback type="invalid">{errors.institute}</Form.Control.Feedback>
        </Form.Group>
      </Form.Row>
      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Certificate No.</Form.Label>
          <Form.Control
            type="text"
            name="certificate_no"
            value={values.certificate_no || ''}
            onChange={onInputChange}
            isInvalid={!!errors.certificate_no}
          />
          <Form.Control.Feedback type="invalid">{errors.certificate_no}</Form.Control.Feedback>
        </Form.Group>
      </Form.Row>
      <Form.Row>
        <FormField name="date_of_issue" label="Date of Issue" md="5">
          <FleetDatePicker />
        </FormField>
        <FormField name="date_of_expiry" label="Date of Expiry" md={{ span: 5, offset: 1 }}>
          <FleetDatePicker />
        </FormField>
      </Form.Row>
      <Form.Row>
        <DocumentFileUpload
          error={errors.file}
          value={values.file}
          onViewFile={viewDocument}
          onRemoveFile={handleRemoveFile}
          onSelectFile={handleSelectedFile}
        />
      </Form.Row>
    </Form>
  );
};

export default DocumentFormOtherCourses;
