import React, { useEffect } from 'react';
import { Col, Form } from 'react-bootstrap';
import { parseDate } from '@src/model/utils';
import FleetDatePicker from '../AddSeafarer/FleetDatePicker';
import DropDownSearchControl from '../AddSeafarer/DropDownSearchControl';
import DocumentTypeDropdown from './DocumentTypeDropdown';
import DocumentFileUpload from './DocumentFileUpload';
import { FormField } from '../common/Form';

const DocumentFormUserDefinedDocument = (props) => {
  const {
    targetFormId,
    formsDropdownValues,
    values,
    errors,
    disableDocumentTypeDropdown,
    userDefinedDocumentTypes,
    viewDocument,
    onInputChange,
    handleSelectedFile,
    handleRemoveFile,
  } = props;

  const isEditMode = values.id !== undefined;

  return (
    <Form>
      <Form.Row>
        <DocumentTypeDropdown
          targetFormId={targetFormId}
          onInputChange={onInputChange}
          errors={errors}
          disabled={isEditMode || disableDocumentTypeDropdown}
          formsDropdownValues={formsDropdownValues}
        />
        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>User Defined Document*</Form.Label>
          <DropDownSearchControl
            name="user_defined_document_type_id"
            selectedValue={values.user_defined_document_type_id}
            dropDownValues={userDefinedDocumentTypes}
            onInputChange={onInputChange}
            // isInvalid={!!errors.other_course_type_id}
            testID="user-defined-doc-type-dropdown"
            disabled={isEditMode}
          />
          <Form.Control.Feedback
            type="invalid"
            className={errors.user_defined_document_type_id ? 'set-display-block' : ''}
          >
            {errors.user_defined_document_type_id}
          </Form.Control.Feedback>
        </Form.Group>
      </Form.Row>
      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Short Remark</Form.Label>
          <Form.Control
            type="text"
            name="short_remark"
            onChange={onInputChange}
            value={values.short_remark || ''}
            isInvalid={!!errors.short_remark}
            maxLength={255}
          // testID={DATE_OF_BIRTH_FIELD}
          />
          <Form.Control.Feedback
            type="invalid"
            className={errors.short_remark ? 'set-display-block' : ''}
          >
            {errors.short_remark}
          </Form.Control.Feedback>
        </Form.Group>
      </Form.Row>
      <Form.Row>
        <FormField name="date_of_issue" label="Date of Issue" md="5">
          <FleetDatePicker />
        </FormField>
        <FormField name="date_of_expiry" label="Date of Expiry" md={{ span: 5, offset: 1 }}>
          <FleetDatePicker
            minDate={values.date_of_issue ? parseDate(values.date_of_issue).toDate() : null}
            disabled={!values.date_of_issue}
          />
        </FormField>
      </Form.Row>
      <Form.Row>
        <DocumentFileUpload
          error={errors.file}
          value={values.file}
          onViewFile={viewDocument}
          onRemoveFile={handleRemoveFile}
          onSelectFile={handleSelectedFile}
        />
      </Form.Row>
    </Form>
  );
};

export default DocumentFormUserDefinedDocument;
