import React, { useState, useEffect } from 'react';
import { Formik } from 'formik';
import {
  Col,
  Button,
  Modal,
  Form,
  OverlayTrigger,
  Tooltip,
  Spinner as BootstrapSpinner,
} from 'react-bootstrap';
import FleetDatePicker from '../AddSeafarer/FleetDatePicker';
import { useParams } from 'react-router-dom';
import DropDownSearchControl from '../AddSeafarer/DropDownSearchControl';
import AddSeafarerOtherComponents from '../AddSeafarer/AddSeafarerOtherComponents';
import { seafarerTrainingSchema } from '../../model/SeafarerSchemaValidation';
import { getVesselV2Dropdown } from '../../service/vessel-service';
import { createTrainingRequirement } from '../../service/seafarer-service';
import styleGuide from '../../styleGuide';
import ConfirmActionModalView from '../AddSeafarer/ConfirmActionModalView';
import { COMMON_MESSAGES } from '../../constants/common-labels-and-messages';
import moment from 'moment';
import Spinner from '../../component/common/Spinner';

const { Icon } = styleGuide;

const { SelectedFileField, SelectFileButton } = AddSeafarerOtherComponents;

const TrainingRequirementForm = (props: {
  values: any;
  errors: any;
  onCloseModal: any;
  genOnDateChange: any;
  setFieldValue: any;
  isFromMasterAppraisal: boolean;
  onSubmitValues: any;
  vesselData: any;
  isSaving: boolean;
  isDisabled: boolean;
}) => {
  // eslint-disable-next-line react/prop-types
  let {
    values,
    errors,
    onCloseModal,
    genOnDateChange,
    setFieldValue,
    isFromMasterAppraisal,
    onSubmitValues,
    vesselData,
    isSaving,
    isDisabled,
  } = props;
  const [
    isRemoveSupportingDocumentConfirmationModalShow,
    setIsRemoveSupportingDocumentConfirmationModalShow,
  ] = useState(false);
  const [
    isRemoveSuperintendentReportConfirmationModalShow,
    setIsRemoveSuperintendentReportConfirmationModalShow,
  ] = useState(false);

  const showToolTip = (isFromMasterAppraisal) => {
    let toolTipString = '';
    if (isFromMasterAppraisal) {
      toolTipString =
        'No deadline is specified when training requirement is from Master Appraisal. However it is recommended to specified a deadline to meet or follow up.';
    } else {
      toolTipString =
        'Deadline is required to make sure the training required has a deadline to meet or follow up.';
    }
    return (
      <OverlayTrigger
        delay={{ show: 250, hide: 400 }}
        overlay={<Tooltip id="add-training-tooltip">{toolTipString}</Tooltip>}
      >
        <span className="d-inline-block">
          <Icon icon="alert" size={15} />
        </span>
      </OverlayTrigger>
    );
  };

  const handleSelectedFile = (file, fileIndex) => {
    if (file === undefined) {
      return;
    }
    setFieldValue(fileIndex, file);
  };

  const onInputVesselChange = (e: { target: { name: string; value: number } }): void => {
    const vesselName = vesselData.filter((vessel) => vessel.id === e.target.value);

    if (vesselName.length > 0) {
      setFieldValue('vessel_name', vesselName[0]?.value);
    }
    return setFieldValue('vessel_ownership_id', e.target.value);
  };

  return (
    <Form>
      <Form.Row className="justify-content-between">
        <Form.Group as={Col} md="5" className="training-req-modal-form-group">
          <Form.Label className="form-heading">Date Training Recommended*</Form.Label>
          <FleetDatePicker
            name="date_of_issue"
            onChange={genOnDateChange('date_of_issue')}
            // eslint-disable-next-line react/prop-types
            value={values.date_of_issue}
            // eslint-disable-next-line react/prop-types
            isInvalid={!!errors.date_of_issue}
            maxDate={new Date()}
          />
          <Form.Control.Feedback
            type="invalid"
            // eslint-disable-next-line react/prop-types
            className={errors.date_of_issue ? 'set-display-block' : ''} //force the Feedback to be appear based on errors.date_of_issue
          >
            {errors.date_of_issue}
          </Form.Control.Feedback>
        </Form.Group>
        <Form.Group as={Col} md="5" className="training-req-modal-form-group">
          <Form.Label className="form-heading">Deadline* &nbsp;</Form.Label>
          <Form.Label>{showToolTip(isFromMasterAppraisal)}</Form.Label>
          <FleetDatePicker
            name="deadline"
            onChange={genOnDateChange('deadline')}
            // eslint-disable-next-line react/prop-types
            value={values.deadline}
            // eslint-disable-next-line react/prop-types
            isInvalid={!!errors.deadline}
          />
          <Form.Control.Feedback
            type="invalid"
            // eslint-disable-next-line react/prop-types
            className={errors.deadline ? 'set-display-block' : ''} //force the Feedback to be appear based on errors.date_of_expiry
          >
            {errors.deadline}
          </Form.Control.Feedback>
        </Form.Group>
      </Form.Row>
      <Form.Row className="justify-content-between">
        <Form.Group as={Col} md="5" className="training-req-modal-form-group">
          <Form.Label className="form-heading">Vessel*</Form.Label>
          <DropDownSearchControl
            name="vessel_ownership_id"
            selectedValue={values.vessel_ownership_id}
            dropDownValues={vesselData}
            onInputChange={(e) => onInputVesselChange(e)}
            testID="vessel_ownership_id"
            disabled={false}
            isInvalid={!!errors.vessel_ownership_id}
          />
          <Form.Control.Feedback
            type="invalid"
            className={errors.vessel_ownership_id ? 'set-display-block' : ''}
          >
            {errors.vessel_ownership_id}
          </Form.Control.Feedback>
        </Form.Group>
        <Form.Group as={Col} md="5" className="training-req-modal-form-group">
          <Form.Label className="form-heading">Date Completed</Form.Label>
          <FleetDatePicker
            name="completed_date"
            // eslint-disable-next-line react/prop-types
            value={values.completed_date}
            onChange={genOnDateChange('completed_date')}
            // eslint-disable-next-line react/prop-types
            isInvalid={!!errors.completed_date}
            disabled={true}
            placeholder="Select date of Completed"
          />
          <Form.Control.Feedback
            type="invalid"
            // eslint-disable-next-line react/prop-types
            className={errors.date_of_expiry ? 'set-display-block' : ''} //force the Feedback to be appear based on errors.date_of_expiry
          >
            {errors.completed_date}
          </Form.Control.Feedback>
        </Form.Group>
      </Form.Row>
      <Form.Row className="justify-content-between">
        <Form.Group as={Col} md="5" className="training-req-modal-form-group">
          <Form.Label className="form-heading">Training Needs*</Form.Label>
          <Form.Control
            title="training_needs"
            as="textarea"
            rows={3}
            // eslint-disable-next-line react/prop-types
            value={values.training_needs}
            onChange={(e) => setFieldValue('training_needs', e.target.value)}
          />
          <Form.Control.Feedback
            type="invalid"
            // eslint-disable-next-line react/prop-types
            className={errors.training_needs ? 'set-display-block' : ''} //force the Feedback to be appear based on errors.training_needs
          >
            {errors.training_needs}
          </Form.Control.Feedback>
        </Form.Group>
        <Form.Group as={Col} md="5" className="training-req-modal-form-group">
          <Form.Label className="form-heading">Training Imparted</Form.Label>
          <Form.Control
            as="textarea"
            rows={3}
            value={values.training_imparted}
            disabled={true}
            title="Training Imparted"
          />
          <Form.Control.Feedback type="invalid">{errors?.training_imparted}</Form.Control.Feedback>
        </Form.Group>
      </Form.Row>
      <Form.Row className="justify-content-between">
        <Form.Group as={Col} md="5" className="training-req-modal-form-group">
          <Form.Label className="form-heading">{`Supporting Document (pdf, jpg, png, max size 12MB)`}</Form.Label>
          {values.supporting_document !== undefined ? (
            <SelectedFileField
              onRemoveClick={() => setIsRemoveSupportingDocumentConfirmationModalShow(true)}
              fileName={values.supporting_document.name}
            />
          ) : (
            <SelectFileButton
              fileIndex="supporting_document"
              onSelectFile={handleSelectedFile}
              isInvalid={!!errors.supporting_document}
              disabled={true}
            />
          )}
          {errors.supporting_document === 'Document is required' && (
            <Form.Control.Feedback type="invalid" className="document-copy-feedback">
              Please provide a document.
            </Form.Control.Feedback>
          )}
          {errors.supporting_document === 'File too large' && (
            <Form.Control.Feedback type="invalid" className="document-copy-feedback">
              {COMMON_MESSAGES.MESSAGE_FILE_SIZE_EXCEED}
            </Form.Control.Feedback>
          )}
          {errors.supporting_document === 'Unsupported Format' && (
            <Form.Control.Feedback type="invalid" className="document-copy-feedback">
              {COMMON_MESSAGES.MESSAGE_FILE_EXTENSION_NOT_SUPPORT}
            </Form.Control.Feedback>
          )}
        </Form.Group>
        <Form.Group as={Col} md="5" className="training-req-modal-form-group">
          <Form.Label className="form-heading">{`Superintendent Report `}</Form.Label>
          {values.superintendent_report !== undefined ? (
            <SelectedFileField
              fileName={values.superintendent_report.name}
              onRemoveClick={() => setIsRemoveSuperintendentReportConfirmationModalShow(true)}
            />
          ) : (
            <SelectFileButton
              fileIndex="superintendent_report"
              onSelectFile={handleSelectedFile}
              isInvalid={!!errors.superintendent_report}
              disabled={true}
            />
          )}
          {errors.superintendent_report === 'Document is required' && (
            <Form.Control.Feedback type="invalid" className="document-copy-feedback">
              Please provide a document.
            </Form.Control.Feedback>
          )}
          {errors.superintendent_report === 'File too large' && (
            <Form.Control.Feedback type="invalid" className="document-copy-feedback">
              {COMMON_MESSAGES.MESSAGE_FILE_SIZE_EXCEED}
            </Form.Control.Feedback>
          )}
          {errors.superintendent_report === 'Unsupported Format' && (
            <Form.Control.Feedback type="invalid" className="document-copy-feedback">
              {COMMON_MESSAGES.MESSAGE_FILE_EXTENSION_NOT_SUPPORT}
            </Form.Control.Feedback>
          )}
          <ConfirmActionModalView
            show={isRemoveSupportingDocumentConfirmationModalShow}
            onClose={() => setIsRemoveSupportingDocumentConfirmationModalShow(false)}
            onConfirm={() => {
              setIsRemoveSupportingDocumentConfirmationModalShow(false);
              setFieldValue('supporting_document', undefined);
            }} //not sure why this trigger Warning: Unknown event handler property `onConfirm`. It will be ignored.
            title={'Confirm Deleting the Supporting Document?'}
            message={'Are you sure deleting the uploaded Supporting Document?'}
          />
          <ConfirmActionModalView
            show={isRemoveSuperintendentReportConfirmationModalShow}
            onClose={() => setIsRemoveSuperintendentReportConfirmationModalShow(false)}
            onConfirm={() => {
              setIsRemoveSuperintendentReportConfirmationModalShow(false);
              setFieldValue('superintendent_report', undefined);
            }} //not sure why this trigger Warning: Unknown event handler property `onConfirm`. It will be ignored.
            title={'Confirm deletion of Superintendent Report'}
            message={'Are you sure you want to delete the uploaded Superintendent Report?'}
          />
        </Form.Group>
      </Form.Row>
      <hr className="grey_line"></hr>
      <Form.Row>
        <div className="ml-auto pr-1">
          <Button
            disabled={isSaving}
            variant="primary"
            className="m-2"
            onClick={() => {
              onCloseModal();
            }}
          >
            Cancel
          </Button>
          <Button
            disabled={isSaving || isDisabled}
            style={isSaving ? { pointerEvents: 'none' } : {}}
            variant="secondary"
            onClick={() => {
              onSubmitValues();
            }}
          >
            Save
            {isSaving && (
              <div className="ml-2">
                <BootstrapSpinner animation="border" size="sm" aria-hidden="true" />
              </div>
            )}
          </Button>
        </div>
      </Form.Row>
    </Form>
  );
};

// eslint-disable-next-line react/prop-types
const AddTrainingRequirementModal = ({
  setShowAddTrainingRequirementModal,
  isFromMasterAppraisal,
  callTrainingRequirement,
  isInvestigationTrainingReq,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [vesselData, setVesselData] = useState([]);
  const [isSaving, setIsSaving] = useState(false);
  const [isDisabled, setIsDisabled] = useState(false);
  const [formSchema, setFormSchema] = useState(null);
  let { seafarerId } = useParams();
  const trainingReqInitialValues = {
    date_of_issue: moment().startOf('day').format(),
    deadline: moment().add(6, 'months').format(),
    vessel_ownership_id: '',
    training_needs: '',
  };

  const onSubmit = async (values) => {
    try {
      const res = await createTrainingRequirement(seafarerId, {
        vessel_name: values.vessel_name,
        training_needs: values.training_needs,
        deadline_date: moment(values.deadline).format('YYYY-MM-DD'),
        recommended_date: moment(values.date_of_issue).format('YYYY-MM-DD'),
        vessel_ownership_id: values.vessel_ownership_id,
        type: isInvestigationTrainingReq ? 1 : 0,
      });
      if (res?.status === 201) {
        setShowAddTrainingRequirementModal(false);
        callTrainingRequirement();
      }
    } catch (err) {
      setIsSaving(false);
      setIsDisabled(false);
      console.error(err);
    }
  };

  const processVesselDropdownData = (vesselArr: []) => {
    const filteredVesselArr = vesselArr
      .filter((item) => item.name !== null && item.name !== undefined)
      .map((item) => ({
        id: item?.id,
        value: `${item?.name}`,
      }));

    filteredVesselArr.unshift({
      id: 0,
      value: 'N/A',
    });
    return filteredVesselArr;
  };

  const loadVesselDropdownData = async () => {
    try {
      setIsLoading(true);
      const params = 'order=name+asc&f=name';
      const vessel = await getVesselV2Dropdown(params);
      setVesselData(processVesselDropdownData(vessel.data.results));
      setIsLoading(false);
    } catch (err) {
      console.error('Error occur while fetching vessel List', err);
    }
  };

  useEffect(() => {
    const schema: any = seafarerTrainingSchema(false);
    setFormSchema(schema);
    loadVesselDropdownData();
  }, []);

  return (
    <Modal id="add-training-requirement-modal" show={true} size="xl">
      <Modal.Header>
        <Modal.Title style={{ borderBottom: '0' }}>
          {isInvestigationTrainingReq
            ? 'Add Investigation of Training Requirement'
            : 'Add Training Requirement'}
          <div className="required-field-text">* Required fields</div>
        </Modal.Title>
      </Modal.Header>
      <Modal.Body>
        {isLoading ? (
          <Spinner alignClass={`load-spinner`} />
        ) : (
          <Formik validationSchema={formSchema} initialValues={trainingReqInitialValues}>
            {(props) => {
              // eslint-disable-next-line react/prop-types
              let { values, setFieldValue, errors, validateForm } = props;
              const genOnDateChange = (fieldName) => (value) => setFieldValue(fieldName, value);
              const onCloseModal = () => {
                setShowAddTrainingRequirementModal(false);
              };
              const onValidateForm = async () => {
                const errors = await validateForm();
                return {
                  isFormValid: !Object.keys(errors).length,
                };
              };
              const onSubmitValues = async () => {
                return onValidateForm().then(({ isFormValid }) => {
                  if (isFormValid) {
                    setIsSaving(true);
                    setIsDisabled(true);
                    onSubmit(values);
                  }
                });
              };

              const formComponentCommonProps = {
                values,
                errors,
                setFieldValue,
                seafarerId,
                onCloseModal,
                genOnDateChange,
                isFromMasterAppraisal,
                onSubmitValues,
                vesselData,
                isSaving,
                isDisabled,
                ...props,
              };
              return <TrainingRequirementForm {...formComponentCommonProps} />;
            }}
          </Formik>
        )}
      </Modal.Body>
    </Modal>
  );
};

export default AddTrainingRequirementModal;
