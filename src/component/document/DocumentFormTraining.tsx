import React from 'react';
import { Col, Form } from 'react-bootstrap';
import FleetDatePicker from '../AddSeafarer/FleetDatePicker';
import DocumentTypeDropdown from './DocumentTypeDropdown';
import DocumentFileUpload from './DocumentFileUpload';
import { FormField } from '../common/Form';

const DocumentFormTraining = (props) => {
  let {
    targetFormId,
    formsDropdownValues,
    values,
    errors,
    viewDocument,
    onInputChange,
    handleSelectedFile,
    handleRemoveFile,
  } = props;

  const isEditMode = values.id !== undefined;

  return (
    <Form>
      <Form.Row>
        <DocumentTypeDropdown
          targetFormId={targetFormId}
          onInputChange={onInputChange}
          errors={errors}
          disabled={isEditMode}
          formsDropdownValues={formsDropdownValues}
        />
        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Course*</Form.Label>
          <Form.Control
            type="text"
            name="course"
            value={values.course || ''}
            onChange={onInputChange}
            isInvalid={!!errors.course}
          />
          <Form.Control.Feedback type="invalid">{errors.course}</Form.Control.Feedback>
        </Form.Group>
      </Form.Row>
      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Institute</Form.Label>
          <Form.Control
            type="text"
            name="institute"
            value={values.institute || ''}
            onChange={onInputChange}
            isInvalid={!!errors.institute}
          />
          <Form.Control.Feedback type="invalid">{errors.institute}</Form.Control.Feedback>
        </Form.Group>
        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Grade</Form.Label>
          <Form.Control
            type="text"
            name="grade"
            value={values.grade || ''}
            onChange={onInputChange}
            isInvalid={!!errors.grade}
          />
          <Form.Control.Feedback type="invalid">{errors.grade}</Form.Control.Feedback>
        </Form.Group>
      </Form.Row>
      <Form.Row>
        <FormField name="start_date" label="Start Date" md="5">
          <FleetDatePicker />
        </FormField>
        <FormField name="end_date" label="End Date" md={{ span: 5, offset: 1 }}>
          <FleetDatePicker />
        </FormField>
      </Form.Row>
      <Form.Row>
        <DocumentFileUpload
          error={errors.file}
          value={values.file}
          onViewFile={viewDocument}
          onRemoveFile={handleRemoveFile}
          onSelectFile={handleSelectedFile}
        />
      </Form.Row>
    </Form>
  );
};

export default DocumentFormTraining;
