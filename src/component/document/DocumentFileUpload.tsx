import React, { useState } from 'react';
import { Col, Form } from 'react-bootstrap';
import AddSeafarerOtherComponents from '../AddSeafarer/AddSeafarerOtherComponents';
import ConfirmActionModalView from '../AddSeafarer/ConfirmActionModalView';
import { COMMON_LABELS, COMMON_MESSAGES } from '../../constants/common-labels-and-messages';

const { SelectedFileField, SelectFileButton } = AddSeafarerOtherComponents;

interface FileUploadProps {
  value: any;
  label?: string;
  error: string;
  onRemoveFile: Function;
  onViewFile: Function;
  onSelectFile: Function;
  md?: string | { span: number; offset: number };
}
const DocumentFileUpload = (props: FileUploadProps) => {
  const {
    label = `Document ${COMMON_LABELS.LABEL_FILE_SIZE_AND_EXTENSION}`,
    value,
    error,
    onRemoveFile,
    onViewFile,
    onSelectFile,
    md = '5',
  } = props;
  const [isRemoveFileConfirmationModalShow, setIsRemoveFileConfirmationModalShow] = useState(false);
  const handleRemoveFile = () => {
    setIsRemoveFileConfirmationModalShow(false);
    onRemoveFile?.();
  };
  return (
    <Form.Group as={Col} md={md}>
      <Form.Label>{label}</Form.Label>
      {value !== undefined ? (
        <SelectedFileField
          fileName={value.name}
          onClick={onViewFile}
          onRemoveClick={() => setIsRemoveFileConfirmationModalShow(true)}
        />
      ) : (
        <SelectFileButton onSelectFile={onSelectFile} />
      )}
      {error === 'Document is required' && (
        <Form.Control.Feedback type="invalid" className="document-copy-feedback">
          Please provide a document.
        </Form.Control.Feedback>
      )}
      {error === 'File too large' && (
        <Form.Control.Feedback type="invalid" className="document-copy-feedback">
          {COMMON_MESSAGES.MESSAGE_FILE_SIZE_EXCEED}
        </Form.Control.Feedback>
      )}
      {error === 'Unsupported Format' && (
        <Form.Control.Feedback type="invalid" className="document-copy-feedback">
          {COMMON_MESSAGES.MESSAGE_FILE_EXTENSION_NOT_SUPPORT}
        </Form.Control.Feedback>
      )}
      <ConfirmActionModalView
        show={isRemoveFileConfirmationModalShow}
        onClose={() => setIsRemoveFileConfirmationModalShow(false)}
        onConfirm={handleRemoveFile}
        title="Confirm Deleting the File?"
        message="Are you sure deleting the uploaded file?"
      />
    </Form.Group>
  );
};

export default DocumentFileUpload;
