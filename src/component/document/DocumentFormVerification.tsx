import React from 'react';
import { Col, Form } from 'react-bootstrap';
import FleetDatePicker from '../AddSeafarer/FleetDatePicker';
import DocumentTypeDropdown from './DocumentTypeDropdown';
import DocumentFileUpload from './DocumentFileUpload';
import { FormField } from '../common/Form';

const DocumentFormVerification = (props) => {
  let {
    targetFormId,
    formsDropdownValues,
    values,
    errors,
    viewDocument,
    onInputChange,
    handleSelectedFile,
    handleRemoveFile,
  } = props;

  const isEditMode = values.id !== undefined;

  return (
    <Form>
      <Form.Row>
        <DocumentTypeDropdown
          targetFormId={targetFormId}
          onInputChange={onInputChange}
          errors={errors}
          disabled={isEditMode}
          formsDropdownValues={formsDropdownValues}
        />
        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Type of Verification*</Form.Label>
          <Form.Control
            type="text"
            name="type_of_verification"
            value={values.type_of_verification || ''}
            onChange={onInputChange}
            isInvalid={!!errors.type_of_verification}
          // testID={DATE_OF_BIRTH_FIELD}
          />
          <Form.Control.Feedback type="invalid">
            {errors.type_of_verification}
          </Form.Control.Feedback>
        </Form.Group>
      </Form.Row>
      <Form.Row>
        <FormField name="date_of_issue" label="Date of Issue" md="5">
          <FleetDatePicker />
        </FormField>
        <FormField name="date_of_expiry" label="Date of Expiry" md={{ span: 5, offset: 1 }}>
          <FleetDatePicker />
        </FormField>
      </Form.Row>
      <Form.Row>
        <DocumentFileUpload
          error={errors.file}
          value={values.file}
          onViewFile={viewDocument}
          onRemoveFile={handleRemoveFile}
          onSelectFile={handleSelectedFile}
        />
      </Form.Row>
    </Form>
  );
};

export default DocumentFormVerification;
