import React from 'react';
import { Form } from 'react-bootstrap';
import FleetDatePicker from '../AddSeafarer/FleetDatePicker';
import DocumentTypeDropdown from './DocumentTypeDropdown';
import DocumentFileUpload from './DocumentFileUpload';
import { FormField } from '../common/Form';

const DocumentFormApprenticeship = (props) => {
  let {
    targetFormId,
    formsDropdownValues,
    values,
    errors,
    viewDocument,
    onInputChange,
    handleSelectedFile,
    handleRemoveFile,
  } = props;

  const isEditMode = values.id !== undefined;

  return (
    <Form>
      <Form.Row>
        <DocumentTypeDropdown
          targetFormId={targetFormId}
          onInputChange={onInputChange}
          errors={errors}
          disabled={isEditMode}
          formsDropdownValues={formsDropdownValues}
        />
        <FormField
          name="apprenticeship"
          label="Apprenticeship"
          md={{ span: 5, offset: 1 }}
          requiredMark
        >
          <Form.Control type="text" />
        </FormField>
      </Form.Row>
      <Form.Row>
        <FormField name="start_date" label="Start Date" md="5">
          <FleetDatePicker />
        </FormField>
        <FormField name="end_date" label="End Date" md={{ span: 5, offset: 1 }}>
          <FleetDatePicker />
        </FormField>
      </Form.Row>
      <Form.Row>
        <DocumentFileUpload
          error={errors.file}
          value={values.file}
          onViewFile={viewDocument}
          onRemoveFile={handleRemoveFile}
          onSelectFile={handleSelectedFile}
        />
      </Form.Row>
    </Form>
  );
};

export default DocumentFormApprenticeship;
