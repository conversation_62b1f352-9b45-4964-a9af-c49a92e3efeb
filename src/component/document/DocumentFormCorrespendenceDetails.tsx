import React from 'react';
import { Col, Form } from 'react-bootstrap';
import FleetDatePicker from '../AddSeafarer/FleetDatePicker';
import DocumentTypeDropdown from './DocumentTypeDropdown';
import DocumentFileUpload from './DocumentFileUpload';
import { FormField } from '../common/Form';

const DocumentFormCorrespondenceDetails = (props) => {
  let {
    targetFormId,
    formsDropdownValues,
    values,
    errors,
    viewDocument,
    onInputChange,
    handleSelectedFile,
    handleRemoveFile,
  } = props;

  const isEditMode = values.id !== undefined;

  return (
    <Form>
      <Form.Row>
        <DocumentTypeDropdown
          targetFormId={targetFormId}
          onInputChange={onInputChange}
          errors={errors}
          disabled={isEditMode}
          formsDropdownValues={formsDropdownValues}
        />
        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Type of Correspondence Details</Form.Label>
          <Form.Control
            type="text"
            name="type_of_correspondence_details"
            value={values.type_of_correspondence_details || ''}
            onChange={onInputChange}
            isInvalid={!!errors.type_of_correspondence_details}
          />
          <Form.Control.Feedback type="invalid">
            {errors.type_of_correspondence_details}
          </Form.Control.Feedback>
        </Form.Group>
      </Form.Row>
      <Form.Row>
        <FormField name="date_of_issue" label="Date of Issue" md="5">
          <FleetDatePicker />
        </FormField>
      </Form.Row>
      <Form.Row>
        <DocumentFileUpload
          error={errors.file}
          value={values.file}
          onViewFile={viewDocument}
          onRemoveFile={handleRemoveFile}
          onSelectFile={handleSelectedFile}
        />
      </Form.Row>
    </Form>
  );
};

export default DocumentFormCorrespondenceDetails;
