/* eslint-disable react/prop-types */
import React from 'react';
import Modal from 'react-bootstrap/Modal';
import Button from 'react-bootstrap/Button';

function DocumentDownloadFailedModal(props) {
  return (
    <Modal
      {...props}
      onHide={props.onClose}
      aria-labelledby="contained-modal-title-vcenter"
      centered
    >
      <Modal.Header>
        <Modal.Title id="contained-modal-title-vcenter">{props.title}</Modal.Title>
      </Modal.Header>
      <Modal.Body>{props.children}</Modal.Body>
      <Modal.Footer>
        <Button onClick={props.onClose}>Ok</Button>
      </Modal.Footer>
    </Modal>
  );
}

export default DocumentDownloadFailedModal;
