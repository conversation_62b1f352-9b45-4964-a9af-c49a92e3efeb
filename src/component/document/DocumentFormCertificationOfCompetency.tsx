import React from 'react';
import { Col, Form } from 'react-bootstrap';
import FleetDatePicker from '../AddSeafarer/FleetDatePicker';
import DropDownSearchControl from '../AddSeafarer/DropDownSearchControl';
import DocumentTypeDropdown from './DocumentTypeDropdown';
import { FormField } from '../common/Form';
import DocumentFileUpload from './DocumentFileUpload';

const DocumentFormCertificationOfCompetency = (props) => {
  let {
    targetFormId,
    formsDropdownValues,
    values,
    errors,
    dropdownData = {},
    viewDocument,
    onInputChange,
    handleSelectedFile,
    handleRemoveFile,
  } = props;

  const isEditMode = values.id !== undefined;

  return (
    <Form>
      <Form.Row>
        <DocumentTypeDropdown
          targetFormId={targetFormId}
          onInputChange={onInputChange}
          errors={errors}
          disabled={isEditMode}
          formsDropdownValues={formsDropdownValues}
        />
        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Flags*</Form.Label>
          <DropDownSearchControl
            name={'country_id'}
            selectedValue={values.country_id}
            dropDownValues={dropdownData?.countries ?? []}
            onInputChange={onInputChange}
            isInvalid={!!errors.country_id}
            testID="flag-id"
          />
          <Form.Control.Feedback
            type="invalid"
            className={errors.country_id ? 'set-display-block' : ''} //force the Feedback to be appear based on errors.country_id
          >
            {errors.country_id}
          </Form.Control.Feedback>
        </Form.Group>
      </Form.Row>
      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Certificate*</Form.Label>
          <DropDownSearchControl
            name={'coc_certificate_id'}
            selectedValue={values.coc_certificate_id}
            dropDownValues={dropdownData?.cocCertificates ?? []}
            onInputChange={onInputChange}
            isInvalid={!!errors.coc_certificate_id}
            testID="coc-certificate-id"
          />
          <Form.Control.Feedback
            type="invalid"
            className={errors.coc_certificate_id ? 'set-display-block' : ''} //force the Feedback to be appear based on errors.country_id
          >
            {errors.coc_certificate_id}
          </Form.Control.Feedback>
        </Form.Group>
        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Certificate No.</Form.Label>
          <Form.Control
            type="text"
            name="certificate_no"
            value={values.certificate_no || ''}
            onChange={onInputChange}
            isInvalid={!!errors.certificate_no}
          />
          <Form.Control.Feedback type="invalid">{errors.certificate_no}</Form.Control.Feedback>
        </Form.Group>
      </Form.Row>
      <Form.Row>
        <FormField name="date_of_issue" label="Date of Issue" md="5">
          <FleetDatePicker />
        </FormField>
        <FormField name="date_of_expiry" label="Date of Expiry" md={{ span: 5, offset: 1 }}>
          <FleetDatePicker />
        </FormField>
      </Form.Row>
      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Is National</Form.Label>
          <div>
            <Form.Check
              inline
              type="radio"
              name="is_original"
              onChange={onInputChange}
              isInvalid={!!errors.is_original}
              label={'Yes'}
              value="true"
              defaultChecked={values.is_original === true}
            ></Form.Check>
            <Form.Check
              inline
              type="radio"
              name="is_original"
              onChange={onInputChange}
              isInvalid={!!errors.is_original}
              label={'No'}
              value="false"
              defaultChecked={values.is_original === false}
            ></Form.Check>
            <Form.Control.Feedback
              type="invalid"
              className={errors.is_original ? 'set-display-block' : ''}
            >
              {errors.is_original}
            </Form.Control.Feedback>
            <div className="col-form-label-sm">
              Please select YES only for the <strong>NATIONAL COC</strong> of the seafarer and NO
              for the flag state endorsement
            </div>
          </div>
        </Form.Group>
        <DocumentFileUpload
          error={errors.file}
          value={values.file}
          onViewFile={viewDocument}
          onRemoveFile={handleRemoveFile}
          onSelectFile={handleSelectedFile}
          md={{ span: 5, offset: 1 }}
        />
      </Form.Row>
    </Form>
  );
};

export default DocumentFormCertificationOfCompetency;
