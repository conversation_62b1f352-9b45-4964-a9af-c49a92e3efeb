import React, { useState } from 'react';
import { Col, Form } from 'react-bootstrap';
import DropDownSearchControl from '../AddSeafarer/DropDownSearchControl';
import FleetDatePicker from '../AddSeafarer/FleetDatePicker';
import AddSeafarerOtherComponents from '../AddSeafarer/AddSeafarerOtherComponents';
import ConfirmActionModalView from '../AddSeafarer/ConfirmActionModalView';
import DocumentTypeDropdown from './DocumentTypeDropdown';
import { COMMON_LABELS, COMMON_MESSAGES } from '../../constants/common-labels-and-messages';
import { FormField } from '../common/Form';

const { SelectedFileField, SelectFileButton } = AddSeafarerOtherComponents;

const DocumentFormVisa = (props) => {
  let {
    targetFormId,
    formsDropdownValues,
    values,
    errors,
    setFieldValue,
    visaRegionDropdownData = [],
    disableDocumentTypeDropdown,
    eventTracker,
    viewDocument,
    onInputChange,
  } = props;

  const isEditMode = values.id !== undefined;

  const [isRemoveFileConfirmationModalShow, setIsRemoveFileConfirmationModalShow] = useState(false);
  const [isVisaCopyNoFileSelected, setIsVisaCopyNoFileSelected] = useState(
    values.file === undefined && !!values.id
  );

  const handleSelectedFile = (file) => {
    setIsVisaCopyNoFileSelected(false);
    if (file === undefined) {
      return;
    }

    setFieldValue('file', file);
    eventTracker('uploadDocument', 'Document');
  };

  const handleRemoveFile = () => {
    setIsVisaCopyNoFileSelected(true);
    setIsRemoveFileConfirmationModalShow(false);
    setFieldValue('file', undefined);
  };

  return (
    <Form>
      <Form.Row>
        <DocumentTypeDropdown
          targetFormId={targetFormId}
          onInputChange={onInputChange}
          errors={errors}
          disabled={isEditMode || disableDocumentTypeDropdown}
          formsDropdownValues={formsDropdownValues}
        />
        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Type of Visa*</Form.Label>
          <Form.Control
            type="text"
            name="type_of_visa"
            onChange={onInputChange}
            value={values.type_of_visa || ''}
            isInvalid={!!errors.type_of_visa}
          />
          <Form.Control.Feedback type="invalid">{errors.type_of_visa}</Form.Control.Feedback>
        </Form.Group>
      </Form.Row>
      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Country*</Form.Label>
          <DropDownSearchControl
            name={'visa_region'}
            selectedValue={values.visa_region}
            dropDownValues={visaRegionDropdownData}
            onInputChange={onInputChange}
            isInvalid={!!errors.visa_region}
            testID="visa_region"
          />
          <Form.Control.Feedback
            type="invalid"
            className={errors.visa_region ? 'set-display-block' : ''} //force the Feedback to be appear based on errors.visa_region
          >
            {errors.visa_region}
          </Form.Control.Feedback>
        </Form.Group>
        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Number*</Form.Label>
          <Form.Control
            type="text"
            name="number"
            value={values.number || ''}
            // onChange={handleChange}
            onChange={onInputChange}
            isInvalid={!!errors.number}
          />
          <Form.Control.Feedback type="invalid">{errors.number}</Form.Control.Feedback>
        </Form.Group>
      </Form.Row>
      <Form.Row>
        <FormField name="date_of_issue" label="Date of Issue" md="5">
          <FleetDatePicker />
        </FormField>
        <FormField name="date_of_expiry" label="Date of Expiry" md={{ span: 5, offset: 1 }}>
          <FleetDatePicker />
        </FormField>
      </Form.Row>
      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Issuing Authority*</Form.Label>
          <Form.Control
            type="text"
            name="issuing_authority"
            value={values.issuing_authority || ''}
            onChange={onInputChange}
            isInvalid={!!errors.issuing_authority}
          />
          <Form.Control.Feedback type="invalid">{errors.issuing_authority}</Form.Control.Feedback>
        </Form.Group>
        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Rejected</Form.Label>
          <Form.Control
            as="select"
            name="rejected"
            value={values.rejected}
            onChange={onInputChange}
            // onBlur={handleBlur}
            isInvalid={!!errors.rejected}
          >
            <option value="">Please select</option>
            <option value={true}>Yes</option>
            <option value={false}>No</option>
          </Form.Control>
          <Form.Control.Feedback type="invalid">{errors.rejected}</Form.Control.Feedback>
        </Form.Group>
      </Form.Row>

      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>{`Upload copy of Visa* ${COMMON_LABELS.LABEL_FILE_SIZE_AND_EXTENSION}`}</Form.Label>
          {values.file !== undefined ? (
            <SelectedFileField
              fileName={values.file.name}
              onClick={viewDocument}
              onRemoveClick={() => setIsRemoveFileConfirmationModalShow(true)}
            />
          ) : (
            <SelectFileButton onSelectFile={handleSelectedFile} isInvalid={!!errors.file} />
          )}
          {(errors.file === 'Copy of Visa is required' || isVisaCopyNoFileSelected) && (
            <Form.Control.Feedback type="invalid" className="document-copy-feedback">
              Please provide a copy of Visa.
            </Form.Control.Feedback>
          )}
          {errors.file === 'File too large' && (
            <Form.Control.Feedback type="invalid" className="document-copy-feedback">
              {COMMON_MESSAGES.MESSAGE_FILE_SIZE_EXCEED}
            </Form.Control.Feedback>
          )}
          {errors.file === 'Unsupported Format' && (
            <Form.Control.Feedback type="invalid" className="document-copy-feedback">
              {COMMON_MESSAGES.MESSAGE_FILE_EXTENSION_NOT_SUPPORT}
            </Form.Control.Feedback>
          )}
          <ConfirmActionModalView
            show={isRemoveFileConfirmationModalShow}
            onClose={() => setIsRemoveFileConfirmationModalShow(false)}
            onConfirm={handleRemoveFile} //not sure why this trigger Warning: Unknown event handler property `onConfirm`. It will be ignored.
            title={'Confirm Deleting the File?'}
            message={'Are you sure deleting the uploaded file?'}
          />
        </Form.Group>
      </Form.Row>
    </Form>
  );
};

export default DocumentFormVisa;
