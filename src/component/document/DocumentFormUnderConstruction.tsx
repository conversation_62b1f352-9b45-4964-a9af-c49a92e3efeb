import React from 'react';
import { Col, Form } from 'react-bootstrap';

const DocumentFormUnderConstruction = (props) => {
  console.log('Executing DocumentFormUnderConstruction props: ', props);
  let { targetFormId, setTargetFormId, formsDropdownValues } = props;

  const onInputChange = (event) => {
    console.log('onInputChange event', event);

    const targetName = event?.target?.name;
    const targetValue = event?.target?.value;
    console.log('onInputChange target, value', targetName, targetValue);

    if (targetName === 'form_id') {
      console.log('setTargetFormId, targetValue: ', targetValue);
      setTargetFormId(parseInt(targetValue, 10));
    }
  };

  return (
    <Form>
      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Document Type*</Form.Label>
          <Form.Control as="select" name={'form_id'} value={targetFormId} onChange={onInputChange}>
            {formsDropdownValues?.map((item) => (
              <optgroup className="dropdown-option-group" label={item.label} key={item.label}>
                {item.child.map((formValue) => (
                  <option className="dropdown-option" value={formValue.id} key={formValue.id}>
                    {formValue.value}
                  </option>
                ))}
              </optgroup>
            ))}
          </Form.Control>
        </Form.Group>
      </Form.Row>
      <div id={'under-construction'}>This form is under construction ...</div>
    </Form>
  );
};

export default DocumentFormUnderConstruction;
