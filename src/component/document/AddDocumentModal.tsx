/* eslint-disable react/jsx-props-no-spreading */
import React, { useState, useEffect, useRef } from 'react';
import { Formik } from 'formik';
import _, { cloneDeep } from 'lodash';
import { Button, Form, Modal } from 'react-bootstrap';
import moment from 'moment';

import { useParams } from 'react-router-dom';
import DocumentFormVisa from './DocumentFormVisa';
import DocumentFormINDoS from './DocumentFormINDoS';
import DocumentFormUnderConstruction from './DocumentFormUnderConstruction';
import DocumentFormEndorsement from './DocumentFormEndorsement';
import DocumentFormVerification from './DocumentFormVerification';
import DocumentFormDrugAndAlcohol from './DocumentFormDrugAndAlcohol';
import DocumentFormMedical from './DocumentFormMedical';
import DocumentFormEducation from './DocumentFormEducation';
import DocumentFormSTCW from './DocumentFormSTCW';
import DocumentFormApprenticeship from './DocumentFormApprenticeship';
import DocumentFormTraining from './DocumentFormTraining';
import DocumentFormPreSeaTraining from './DocumentFormPreSeaTraining';
import DocumentFormOtherCourses from './DocumentFormOtherCourses';
import DocumentFormOtherDocuments from './DocumentFormOtherDocuments';
import DocumentFormCertificationOfCompetency from './DocumentFormCertificationOfCompetency';
import DocumentFormCorrespendenceDetails from './DocumentFormCorrespendenceDetails';
import DocumentFormUserDefinedDocument from './DocumentFormUserDefinedDocument';
import Spinner from '../common/Spinner';
import * as schema from '../../model/SeafarerSchemaValidation';
import * as documentTypes from '../../constants/documentTypes';
import seafarerService, {
  createSeafarerDocument,
  patchSeafarerDocument,
} from '../../service/seafarer-service';
import { dateAsString } from '../../model/utils';
import LastEditedByLine from './LastEditedByLine';
import ConfirmActionModalView from '../AddSeafarer/ConfirmActionModalView';

const { DOC_FORM_IDS } = documentTypes;

const formIdToValidationSchemaMap = {
  [documentTypes.DOC_FORM_IDS.VISA]: schema.seafarerDocTypeVisaSchema,
  [documentTypes.DOC_FORM_IDS.INDOS]: schema.seafarerDocTypeINDoSSchema,
  [documentTypes.DOC_FORM_IDS.ENDORSEMENT]: schema.seafarerDocTypeEndorsementSchema,
  [documentTypes.DOC_FORM_IDS.VERIFICATION]: schema.seafarerDocTypeVerificationSchema,
  [documentTypes.DOC_FORM_IDS.DCE_VERIFICAITON]: schema.seafarerDocTypeDceVerificationSchema,
  [documentTypes.DOC_FORM_IDS.MEDICAL]: schema.seafarerDocTypeMedicalSchema,
  [documentTypes.DOC_FORM_IDS.DRUG_ALCOHOL_TEST]: schema.seafarerDocTypeDrugAndAlcoholTestSchema,
  [documentTypes.DOC_FORM_IDS.STCW]: schema.seafarerDocTypeSTCWSchema,
  [documentTypes.DOC_FORM_IDS.EDUCATION]: schema.seafarerDocTypeEducationSchema,
  [documentTypes.DOC_FORM_IDS.APPRENTICESHIP]: schema.seafarerDocTypeApprenticeshipSchema,
  [documentTypes.DOC_FORM_IDS.TRAINING]: schema.seafarerDocTypeTrainingSchema,
  [documentTypes.DOC_FORM_IDS.CERTIFICATE_OF_COMPETENCY]:
    schema.seafarerDocTypeCertificationOfCompetencySchema,
  [documentTypes.DOC_FORM_IDS.PRE_SEA_TRAINING]: schema.seafarerDocTypePreSeaTrainingSchema,
  [documentTypes.DOC_FORM_IDS.OTHER_COURSE]: schema.seafarerDocTypeOtherCoursesSchema,
  [documentTypes.DOC_FORM_IDS.OTHER_DOCUMENT]: schema.seafarerDocTypeOtherDocumentsSchema,
  [documentTypes.DOC_FORM_IDS.CORRESPONDENCE_DETAILS]:
    schema.seafarerDocTypeCorrespondenceDetailsSchema,
  [documentTypes.DOC_FORM_IDS.USER_DEFINED_DOCUMENT]:
    schema.seafarerDocTypeUserDefinedDocumentSchema,
};

export const formsDropdownValues = [
  {
    label: 'ID DOCUMENTS',
    child: [
      { id: documentTypes.DOC_FORM_IDS.PASSPORT, value: documentTypes.DOC_NAMES.PASSPORT },
      { id: documentTypes.DOC_FORM_IDS.INDOS, value: documentTypes.DOC_NAMES.INDOS },
      { id: documentTypes.DOC_FORM_IDS.VISA, value: documentTypes.DOC_NAMES.VISA },
      { id: documentTypes.DOC_FORM_IDS.SEAMAN_BOOK, value: documentTypes.DOC_NAMES.SEAMAN_BOOK },
    ],
  },
  {
    label: 'ENDORSEMENT & VERIFICATION',
    child: [
      { id: documentTypes.DOC_FORM_IDS.ENDORSEMENT, value: documentTypes.DOC_NAMES.ENDORSEMENT },
      {
        id: documentTypes.DOC_FORM_IDS.DCE_VERIFICAITON,
        value: documentTypes.DOC_NAMES.DCE_VERIFICAITON,
      },
      { id: documentTypes.DOC_FORM_IDS.VERIFICATION, value: documentTypes.DOC_NAMES.VERIFICATION },
    ],
  },
  {
    label: 'OTHER DOCUMENTS',
    child: [
      { id: documentTypes.DOC_FORM_IDS.MEDICAL, value: documentTypes.DOC_NAMES.MEDICAL },
      {
        id: documentTypes.DOC_FORM_IDS.DRUG_ALCOHOL_TEST,
        value: documentTypes.DOC_NAMES.DRUG_ALCOHOL_TEST,
      },
      {
        id: documentTypes.DOC_FORM_IDS.CERTIFICATE_OF_COMPETENCY,
        value: documentTypes.DOC_NAMES.CERTIFICATE_OF_COMPETENCY,
      },
      { id: documentTypes.DOC_FORM_IDS.STCW, value: documentTypes.DOC_NAMES.STCW },
      { id: documentTypes.DOC_FORM_IDS.EDUCATION, value: documentTypes.DOC_NAMES.EDUCATION },
      {
        id: documentTypes.DOC_FORM_IDS.APPRENTICESHIP,
        value: documentTypes.DOC_NAMES.APPRENTICESHIP,
      },
      { id: documentTypes.DOC_FORM_IDS.TRAINING, value: documentTypes.DOC_NAMES.TRAINING },
      {
        id: documentTypes.DOC_FORM_IDS.PRE_SEA_TRAINING,
        value: documentTypes.DOC_NAMES.PRE_SEA_TRAINING,
      },
      {
        id: documentTypes.DOC_FORM_IDS.CORRESPONDENCE_DETAILS,
        value: documentTypes.DOC_NAMES.CORRESPONDENCE_DETAILS,
      },
      { id: documentTypes.DOC_FORM_IDS.OTHER_COURSE, value: documentTypes.DOC_NAMES.OTHER_COURSE },
      {
        id: documentTypes.DOC_FORM_IDS.OTHER_DOCUMENT,
        value: documentTypes.DOC_NAMES.OTHER_DOCUMENT,
      },
      {
        id: documentTypes.DOC_FORM_IDS.USER_DEFINED_DOCUMENT,
        value: documentTypes.DOC_NAMES.USER_DEFINED_DOCUMENT,
      },
    ],
  },
];

const DOC_ID_TO_URL = {
  [DOC_FORM_IDS.VISA]: 'id-documents#visa',
  [DOC_FORM_IDS.INDOS]: 'id-documents#indos',
  [DOC_FORM_IDS.ENDORSEMENT]: 'endorsement#endorsement',
  [DOC_FORM_IDS.VERIFICATION]: 'endorsement#document-verification',
  [DOC_FORM_IDS.DCE_VERIFICAITON]: 'endorsement#dce-verification',
  [DOC_FORM_IDS.MEDICAL]: 'other-documents#medical',
  [DOC_FORM_IDS.DRUG_ALCOHOL_TEST]: 'other-documents#medical',
  [DOC_FORM_IDS.CERTIFICATE_OF_COMPETENCY]: 'other-documents#coc',
  [DOC_FORM_IDS.STCW]: 'other-documents#stcw',
  [DOC_FORM_IDS.EDUCATION]: 'other-documents#education',
  [DOC_FORM_IDS.APPRENTICESHIP]: 'other-documents#apprenticeship',
  [DOC_FORM_IDS.TRAINING]: 'other-documents#training',
  [DOC_FORM_IDS.PRE_SEA_TRAINING]: 'other-documents#pre-sea-training',
  [DOC_FORM_IDS.OTHER_COURSE]: 'other-documents#other-courses',
  [DOC_FORM_IDS.OTHER_DOCUMENT]: 'other-documents#other-documents',
  [DOC_FORM_IDS.USER_DEFINED_DOCUMENT]: 'other-documents#user-defined-document',
  [DOC_FORM_IDS.SEAMAN_BOOK]: 'other-documents#seaman-book',
  [DOC_FORM_IDS.PASSPORT]: 'other-documents#passport',
  [DOC_FORM_IDS.CORRESPONDENCE_DETAILS]: 'other-documents#correspondence-details',
};

const removeHash = (url: string) => url?.split('#')?.[0];

const renderDocumentForm = ({
  renderFormId,
  dropdownData,
  formComponentCommonProps,
  seafarerId,
  history,
}) => {
  switch (renderFormId) {
    case DOC_FORM_IDS.PASSPORT:
      history.push(`/seafarer/${seafarerId}/add/basic#passports`);
      return null;
    case DOC_FORM_IDS.SEAMAN_BOOK:
      history.push(`/seafarer/${seafarerId}/add/basic#seaman-books`);
      return null;
    case DOC_FORM_IDS.VISA:
      return (
        <DocumentFormVisa
          targetFormId={DOC_FORM_IDS.VISA}
          visaRegionDropdownData={dropdownData.visaRegions}
          {...formComponentCommonProps}
        />
      );
    case DOC_FORM_IDS.INDOS:
      return (
        <DocumentFormINDoS
          targetFormId={DOC_FORM_IDS.INDOS}
          {...formComponentCommonProps}
        />
      );
    case DOC_FORM_IDS.ENDORSEMENT:
      return (
        <DocumentFormEndorsement
          targetFormId={DOC_FORM_IDS.ENDORSEMENT}
          endorsementTypeDropdownData={dropdownData.endorsements}
          {...formComponentCommonProps}
        />
      );
    case DOC_FORM_IDS.VERIFICATION:
      return (
        <DocumentFormVerification
          targetFormId={DOC_FORM_IDS.VERIFICATION}
          {...formComponentCommonProps}
        />
      );
    case DOC_FORM_IDS.MEDICAL:
      return (
        <DocumentFormMedical
          targetFormId={DOC_FORM_IDS.MEDICAL}
          medicalCertDropdownData={dropdownData.medicalCertificates}
          {...formComponentCommonProps}
        />
      );
    case DOC_FORM_IDS.DRUG_ALCOHOL_TEST:
      return (
        <DocumentFormDrugAndAlcohol
          targetFormId={DOC_FORM_IDS.DRUG_ALCOHOL_TEST}
          {...formComponentCommonProps}
        />
      );
    case DOC_FORM_IDS.CERTIFICATE_OF_COMPETENCY:
      return (
        <DocumentFormCertificationOfCompetency
          targetFormId={DOC_FORM_IDS.CERTIFICATE_OF_COMPETENCY}
          dropdownData={dropdownData} // Check duplication with formsDropdownValues if needed
          {...formComponentCommonProps}
        />
      );
    case DOC_FORM_IDS.STCW:
      return (
        <DocumentFormSTCW
          targetFormId={DOC_FORM_IDS.STCW}
          stcwTypeDropdownData={dropdownData.stcwLicences}
          {...formComponentCommonProps}
        />
      );
    case DOC_FORM_IDS.EDUCATION:
      return (
        <DocumentFormEducation
          targetFormId={DOC_FORM_IDS.EDUCATION}
          instituteDropdownData={dropdownData.institutes}
          {...formComponentCommonProps}
        />
      );
    case DOC_FORM_IDS.APPRENTICESHIP:
      return (
        <DocumentFormApprenticeship
          targetFormId={DOC_FORM_IDS.APPRENTICESHIP}
          {...formComponentCommonProps}
        />
      );
    case DOC_FORM_IDS.TRAINING:
      return (
        <DocumentFormTraining
          targetFormId={DOC_FORM_IDS.TRAINING}
          {...formComponentCommonProps}
        />
      );
    case DOC_FORM_IDS.PRE_SEA_TRAINING:
      return (
        <DocumentFormPreSeaTraining
          targetFormId={DOC_FORM_IDS.PRE_SEA_TRAINING}
          dropdownValues={dropdownData} // Check duplication with formsDropdownValues if needed
          {...formComponentCommonProps}
        />
      );
    case DOC_FORM_IDS.OTHER_COURSE:
      return (
        <DocumentFormOtherCourses
          targetFormId={DOC_FORM_IDS.OTHER_COURSE}
          otherCourseTypesDropdownData={dropdownData.otherCourseTypes}
          {...formComponentCommonProps}
        />
      );
    case DOC_FORM_IDS.OTHER_DOCUMENT:
      return (
        <DocumentFormOtherDocuments
          targetFormId={DOC_FORM_IDS.OTHER_DOCUMENT}
          otherDocumentTypesDropdownData={dropdownData.otherDocumentsTypes}
          {...formComponentCommonProps}
        />
      );
    case DOC_FORM_IDS.DCE_VERIFICAITON:
      return (
        <DocumentFormVerification
          targetFormId={DOC_FORM_IDS.DCE_VERIFICAITON}
          {...formComponentCommonProps}
        />
      );
    case DOC_FORM_IDS.CORRESPONDENCE_DETAILS:
      return (
        <DocumentFormCorrespendenceDetails
          targetFormId={DOC_FORM_IDS.CORRESPONDENCE_DETAILS}
          {...formComponentCommonProps}
        />
      );
    case DOC_FORM_IDS.USER_DEFINED_DOCUMENT:
      return (
        <DocumentFormUserDefinedDocument
          targetFormId={DOC_FORM_IDS.USER_DEFINED_DOCUMENT}
          userDefinedDocumentTypes={dropdownData.userDefinedDocumentTypes}
          {...formComponentCommonProps}
        />
      );
    default:
      return null;
  }
};

const AddDocumentModal = ({
  setShowDocumentModal,
  onSubmitCallback,
  seafarerPersonId,
  docId,
  targetId = 1,
  history,
  dropdownData = {},
  disableDocumentTypeDropdown,
  overrideOnSubmitRedirectPath,
  onSubmit: overriddenOnSubmit,
  eventTracker,
}) => {
  const { seafarerId } = useParams();
  const [targetFormId, setTargetFormId] = useState(targetId);
  const [isCreationError, setIsCreationError] = useState(false);
  const [seafarerDocument, setSeafarerDocument] = useState({});
  const [showCancelModal, setShowCancelModal] = useState(false);
  const isEditMode = docId !== undefined && docId !== null;
  const [isLoading, setIsLoading] = useState(isEditMode);
  const [isDisabled, setIsDisabled] = useState(false);

  const prevTargetFormIdRef = useRef();
  const { BASE_URL } = process.env;

  useEffect(() => {
    (async () => {
      prevTargetFormIdRef.current = targetFormId;

      if (isEditMode) {
        try {
          const getSeafarerDocumentResponse = await seafarerService.getSeafarerDocument(docId);
          let updated_after_created;

          if (getSeafarerDocumentResponse.status === 200) {
            if (
              moment(getSeafarerDocumentResponse.data.updated_at).diff(
                moment(getSeafarerDocumentResponse.data.created_at),
                'seconds',
              ) < 3
            ) {
              updated_after_created = false;
            } else {
              updated_after_created = true;
            }

            const fileName = getSeafarerDocumentResponse.data.doc_path
              ? _.last(getSeafarerDocumentResponse.data.doc_path.split('/'))
              : undefined;

            const seafarerDocument = fileName
              ? {
                ...getSeafarerDocumentResponse.data,
                file: {
                  name: fileName,
                },
                updated_after_created,
              }
              : { ...getSeafarerDocumentResponse.data, updated_after_created };

            const seafarerDocumentData =
              seafarerDocument.type === documentTypes.ALL_DOC_TYPES.DRUG_ALCOHOL_TEST &&
                seafarerDocument?.vessel_name
                ? {
                  ...seafarerDocument,
                  vessel: {
                    ref_id: seafarerDocument?.vessel_ref_id,
                    name: seafarerDocument?.vessel_name,
                  },
                }
                : {
                  ...seafarerDocument,
                };
            setSeafarerDocument({
              ...seafarerDocumentData,
            });
            setIsLoading(false);
          } else {
            console.error(
              `Get seafarer document with ${getSeafarerDocumentResponse.status} response`,
            );
          }
        } catch (error) {
          console.error(`Get seafarer document failed. Error: ${error}`);
        }
      }
    })();
  }, []);
  const prevTargetFormId = prevTargetFormIdRef.current;

  const getDifference = (changed, initial, parentKey) => {
    const diff = {};
    if (!initial || Object.keys(initial).length === 0) {
      for (const [key, val] of Object.entries(changed)) {
        if (val && val !== '') {
          diff[key] = val;
        }
      }
      return diff;
    }
    if (_.isArray(changed)) {
      return changed
        .map((ele) => {
          const initialArr = initial.find((obj) => {
            return obj.id === ele.id;
          });
          return getDifference(ele, initialArr, parentKey);
        })
        .filter((diffValue) => !_.isEmpty(diffValue));
    }
    for (const [key, val] of Object.entries(changed)) {
      if (_.isObject(val)) {
        parentKey = key;
        const objDiff = getDifference(val, initial[key], parentKey);
        if (Object.keys(objDiff).length > 0) {
          parentKey = key;
          diff[key] = getDifference(val, initial[key], parentKey);
        }
      } else if (
        key === 'date_of_expiry' ||
        key === 'date_of_issue' ||
        key === 'start_date' ||
        key === 'end_date' ||
        key === 'date_of_test' ||
        key === 'date_of_expiry'
      ) {
        const formattedInitialDate = dateAsString(initial[key]);
        const formattedChangedDate = dateAsString(val);
        if (formattedInitialDate !== formattedChangedDate) {
          diff[key] = val;
        }
      } else if (key !== 'id' && val !== initial[key]) {
        diff[key] = val;
      }
    }
    return diff;
  };
  const renderFormId = targetFormId ?? prevTargetFormId;
  const validationSchema = _.get(formIdToValidationSchemaMap, renderFormId);
  const _onSubmit = async (values) => {
    const isEdit = values.seafarer_document_id !== undefined;
    const documentData = cloneDeep(values);
    Object.keys(documentData).forEach(function (key) {
      if (/file/.exec(key)) delete documentData[key];
    });
    const formData = new FormData();
    const docType = documentTypes.DOC_FORM_ID_TO_DB_DOC_TYPE[renderFormId];

    if (renderFormId === 6) {
      documentData.vessel_ref_id = documentData.vessel.ref_id;
      documentData.vessel_name = documentData.vessel.name;
      delete documentData.vessel;
    }

    if (renderFormId === documentTypes.DOC_FORM_IDS.USER_DEFINED_DOCUMENT) {
      documentData.user_defined_document_type_id =
        documentData.user_defined_document_type_id ?? documentTypes.user_defined_document_type_id;
    }

    if (isEdit) {
      delete documentData.id;
      delete documentData.seafarer_person_id;
      delete documentData.type;
      delete documentData.ref_id;
      delete documentData.doc_path;
      delete documentData.is_deleted;
      delete documentData.created_by;
      delete documentData.updated_after_created;
      delete documentData.updated_by;
      delete documentData.created_at;
      delete documentData.created_by_hash;
      delete documentData.updated_at;
      delete documentData.updated_by_hash;
    }

    const hasFile = values?.file;
    const hasFileChange = values?.file?.size > 0;
    let fileData = values?.file;

    if (hasFileChange && values?.file?.name) {
      const splitFileName = values.file.name.split(/\.(?=[^.]+$)/);
      const fileExt = values?.file.name.substring(values?.file?.name.lastIndexOf('.'));
      const newFileName = splitFileName[0].substring(0, 65).concat(fileExt);
      fileData = new File([values.file], newFileName, { type: values.file?.type });
    }

    const payload = {
      seafarer_person_id: seafarerPersonId,
      document_type: docType,
      document_data: documentData,
      hasNoFile: !hasFile,
      hasFileChange,
    };

    formData.append('body', JSON.stringify(payload));
    formData.append('document', fileData);
    try {
      const response = isEdit
        ? await patchSeafarerDocument(values.seafarer_document_id, formData)
        : await createSeafarerDocument(formData);

      if (response.status === 201) {
        if (setShowDocumentModal) {
          setShowDocumentModal(false);
        }
      }
    } catch (error) {
      setIsDisabled(false);
      setIsCreationError(true);
      throw error;
    }
  };
  const onSubmit = overriddenOnSubmit || _onSubmit;
  const viewDocument = () => {
    if (docId) {
      window.open(`${BASE_URL}/seafarer/document/${docId}/${seafarerDocument.type}`);
    }
  };

  return (
    <Modal id="add-document-modal" show size="xl" data-testid="add-document-modal">
      <Modal.Header>
        <Modal.Title style={{ borderBottom: '0' }}>
          Add/Edit Document
          <div className="required-field-text">* Required fields</div>
        </Modal.Title>
      </Modal.Header>
      <Modal.Body>
        {!isLoading ? (
          <Formik
            validationSchema={validationSchema}
            initialValues={seafarerDocument}
            enableReinitialize // set this value so that when switching form, initDocument value update is reflected in the form
            validateOnMount={!!seafarerDocument.id}
          >
            {(props) => {
              const onValidateForm = async () => {
                const errors = await props.validateForm();
                const touchedFields = Object.keys(errors).reduce((acc, field) => {
                  acc[field] = true;
                  return acc;
                }, {});
                props.setTouched(touchedFields);

                return {
                  isFormValid: !Object.keys(errors).length,
                };
              };
              const onSubmitValues = async (redirectToPath?: string) => {
                return onValidateForm().then(({ isFormValid }) => {
                  if (isFormValid) {
                    setIsDisabled(true);

                    return onSubmit(props.values)
                      .then((result) => {
                        if (setShowDocumentModal) {
                          setShowDocumentModal(false);
                        }

                        /**
                         * let the AddDcoumentModal to have the final call on where to redirect
                         * overrideOnSubmitRedirectPath = null means no redirection
                         * */
                        if (overrideOnSubmitRedirectPath !== null) {
                          const targetRedirectPath = overrideOnSubmitRedirectPath || redirectToPath;
                          if (targetRedirectPath) {
                            history.push(targetRedirectPath);
                          }
                        }

                        if (onSubmitCallback) {
                          onSubmitCallback(props.values);
                        }

                        return true;
                      })
                      .catch((err) => {
                        console.error(err);
                        throw err;
                      });
                  }

                  return false;
                });
              };

              const handleResetForm = () => {
                props.resetForm({ values: {} });
              };
              const onCloseModal = (path) => {
                const changes = getDifference(props.values, seafarerDocument);
                if (Object.keys(changes).length === 0) {
                  setShowDocumentModal ? setShowDocumentModal(false) : history.push(path);
                } else {
                  setShowCancelModal(true);
                }
              };
              const closeConfirmModal = () => {
                if (setShowDocumentModal) {
                  setShowDocumentModal(false);
                } else {
                  const docUrl = removeHash(DOC_ID_TO_URL[renderFormId]);

                  history.push(`/seafarer/details/${seafarerId}/${docUrl}`);
                }
              };
              const { values } = props;
              const children = isEditMode ? (
                <LastEditedByLine
                  updated_after_created={values?.updated_after_created}
                  created_by={values?.created_by}
                  updated_by={values?.updated_by}
                  created_at={values?.created_at}
                  updated_at={values?.created_at}
                />
              ) : (
                ''
              );
              const onInputChange = (event) => {
                const targetName = event?.target?.name;
                const targetValue = event?.target?.value;
                if (targetName === 'form_id') {
                  setTargetFormId(parseInt(targetValue, 10));
                  handleResetForm();
                } else if (['is_original', 'rejected', 'is_result_failed'].includes(targetName)) {
                  // for boolean field
                  props.setFieldValue(
                    targetName,
                    targetValue && targetValue !== '' ? JSON.parse(targetValue) : undefined,
                  );
                } else {
                  props.setFieldValue(targetName, targetValue || undefined); // to make sure undefined is set if value is empty string
                }
              };

              const handleSelectedFile = (file) => {
                if (file === undefined) {
                  return;
                }

                props.setFieldValue('file', file);
                eventTracker?.('uploadDocument', 'Document');
              };

              const handleRemoveFile = () => {
                props.setFieldValue('file', undefined);
              };

              const formComponentCommonProps = {
                handleResetForm,
                setTargetFormId,
                formsDropdownValues,
                onSubmitValues,
                showCancelModal,
                setShowCancelModal,
                setShowDocumentModal,
                onCloseModal,
                seafarerId,
                seafarerPersonId,
                isDisabled,
                disableDocumentTypeDropdown,
                eventTracker,
                viewDocument,
                onInputChange,
                handleSelectedFile,
                handleRemoveFile,
                ...props,
              };
              let formElements = renderDocumentForm({
                renderFormId,
                history,
                formComponentCommonProps,
                seafarerId,
                dropdownData,
              });
              if (!formElements) {
                formElements = (
                  <DocumentFormUnderConstruction
                    targetFormId={renderFormId}
                    setTargetFormId={setTargetFormId}
                    formsDropdownValues={formsDropdownValues}
                  />
                );
              }
              return (
                <>
                  {formElements}
                  {children}
                  <hr className="grey_line" />
                  <Form.Row>
                    <Modal.Footer style={{ borderTop: '0', width: '100%' }}>
                      <Button
                        variant="primary"
                        onClick={() => {
                          const docUrl = removeHash(DOC_ID_TO_URL[renderFormId]);

                          onCloseModal(`/seafarer/details/${seafarerId}/${docUrl}`);
                        }}
                      >
                        Cancel
                      </Button>
                      <Button
                        disabled={isDisabled}
                        variant="secondary"
                        className="confirm-btn"
                        onClick={async () => {
                          const docUrl = DOC_ID_TO_URL[renderFormId];
                          const afterSubmitRedirectUrl = `/seafarer/details/${seafarerId}/${docUrl}`;
                          await onSubmitValues(afterSubmitRedirectUrl);
                          if (props?.values?.user_defined_document_type_id === 3) {
                            const action = isEditMode ? 'editAppointmentLetterModalSaveButton' : 'addAppointmentLetterModalSaveButton';
                            eventTracker?.(action, '');
                          }
                        }}
                      >
                        Confirm
                      </Button>
                      <ConfirmActionModalView
                        show={showCancelModal}
                        onClose={() => setShowCancelModal(false)}
                        onConfirm={() => {
                          setShowCancelModal(false);
                          closeConfirmModal();
                        }}
                        title="Are you sure?"
                        message="Your changes will be lost. Are you sure you want to cancel?"
                      />
                    </Modal.Footer>
                  </Form.Row>
                </>
              );
            }}
          </Formik>
        ) : (
          <LoadingComponent />
        )}
      </Modal.Body>
      {isCreationError ? (
        <div className="create-error">Document creation failed. Please try after sometime.</div>
      ) : null}
    </Modal>
  );
};

const LoadingComponent = ({ error }) => {
  return (
    <div>
      {error || (
        <div className="mt-5">
          {' '}
          <Spinner />{' '}
        </div>
      )}
    </div>
  );
};

export default AddDocumentModal;
