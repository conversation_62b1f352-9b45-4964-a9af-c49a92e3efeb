import React from 'react';
import { Row, Col } from 'react-bootstrap';
import { dateAsDayAndTime } from '../../model/utils';
import '../../styleGuide';

const LastEditedByLine = ({
  updated_after_created,
  created_by,
  updated_by,
  created_at,
  updated_at,
}) => {
  const printString = (type, name, date) => {
    let tag, formattedDate;
    if (type === 'edited') {
      tag = `Last Edited`;
    } else if (type === 'created') {
      tag = `Created`;
    } else {
      return '';
    }
    if (name) {
      tag = tag + ` by ${name}`;
    }
    if (date) {
      formattedDate = ` on ${dateAsDayAndTime(date)}`;
    }
    return tag + formattedDate;
  };

  return updated_after_created ? (
    <div>
      <Row>
        <Col>{printString('edited', updated_by, updated_at)}</Col>
      </Row>
    </div>
  ) : (
    <div>
      <Row>
        <Col>{printString('created', created_by, created_at)}</Col>
      </Row>
    </div>
  );
};

export default LastEditedByLine;
