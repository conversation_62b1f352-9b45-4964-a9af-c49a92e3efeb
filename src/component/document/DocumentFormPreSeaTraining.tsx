import React from 'react';
import { Col, Form } from 'react-bootstrap';
import FleetDatePicker from '../AddSeafarer/FleetDatePicker';
import DropDownSearchControl from '../AddSeafarer/DropDownSearchControl';
import DocumentTypeDropdown from './DocumentTypeDropdown';
import DocumentFileUpload from './DocumentFileUpload';
import { FormField } from '../common/Form';

const DocumentFormPreSeaTraining = (props) => {
  let {
    targetFormId,
    formsDropdownValues,
    values,
    errors,
    dropdownValues = {},
    viewDocument,
    onInputChange,
    handleSelectedFile,
    handleRemoveFile,
  } = props;

  const isEditMode = values.id !== undefined;

  return (
    <Form>
      <Form.Row>
        <DocumentTypeDropdown
          targetFormId={targetFormId}
          onInputChange={onInputChange}
          errors={errors}
          disabled={isEditMode}
          formsDropdownValues={formsDropdownValues}
        />
        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Course*</Form.Label>
          <DropDownSearchControl
            name={'pre_sea_training_course_id'}
            selectedValue={values.pre_sea_training_course_id}
            dropDownValues={dropdownValues?.preSeaTrainingCourses ?? []}
            onInputChange={onInputChange}
            isInvalid={!!errors.pre_sea_training_course_id}
            testID="pre-sea-training-course-id"
          />
          <Form.Control.Feedback
            type="invalid"
            className={errors.pre_sea_training_course_id ? 'set-display-block' : ''} //force the Feedback to be appear based on errors.country_id
          >
            {errors.pre_sea_training_course_id}
          </Form.Control.Feedback>
        </Form.Group>
      </Form.Row>
      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Institute*</Form.Label>
          <DropDownSearchControl
            name={'institute_id'}
            selectedValue={values.institute_id}
            dropDownValues={dropdownValues?.institutes ?? []}
            onInputChange={onInputChange}
            isInvalid={!!errors.institute_id}
            testID="institute-id"
          />
          <Form.Control.Feedback
            type="invalid"
            className={errors.institute_id ? 'set-display-block' : ''} //force the Feedback to be appear based on errors.country_id
          >
            {errors.institute_id}
          </Form.Control.Feedback>
        </Form.Group>
        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Grade</Form.Label>
          <Form.Control
            type="text"
            name="grade"
            value={values.grade || ''}
            onChange={onInputChange}
            isInvalid={!!errors.grade}
          />
          <Form.Control.Feedback type="invalid">{errors.grade}</Form.Control.Feedback>
        </Form.Group>
      </Form.Row>
      <Form.Row>
        <FormField name="start_date" label="Start Date" md="5">
          <FleetDatePicker />
        </FormField>
        <FormField name="end_date" label="End Date" md={{ span: 5, offset: 1 }}>
          <FleetDatePicker />
        </FormField>
      </Form.Row>
      <Form.Row>
        <DocumentFileUpload
          error={errors.file}
          value={values.file}
          onViewFile={viewDocument}
          onRemoveFile={handleRemoveFile}
          onSelectFile={handleSelectedFile}
        />
      </Form.Row>
    </Form>
  );
};

export default DocumentFormPreSeaTraining;
