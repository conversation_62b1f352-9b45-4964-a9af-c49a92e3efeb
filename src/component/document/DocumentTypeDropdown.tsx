import React from 'react';
import { Col, Form } from 'react-bootstrap';

export const DocumentTypeDropdown = (props) => {
  return (
    <Form.Group as={Col} md="5">
      <Form.Label>Document Type*</Form.Label>
      <Form.Control
        as="select"
        name={'form_id'}
        value={props.targetFormId}
        onChange={props.onInputChange}
        isInvalid={!!props.errors.rejected}
        disabled={props.disabled}
        data-testid="doc-type-dropdown"
      >
        {props.formsDropdownValues?.map((item) => (
          <optgroup className="dropdown-option-group" label={item.label} key={item.label}>
            {item.child.map((formValue) => (
              <option className="dropdown-option" value={formValue.id} key={formValue.id}>
                {formValue.value}
              </option>
            ))}
          </optgroup>
        ))}
      </Form.Control>
    </Form.Group>
  );
};

export default DocumentTypeDropdown;
