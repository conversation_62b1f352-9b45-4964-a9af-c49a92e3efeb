import React from 'react';
import { Col, Form } from 'react-bootstrap';
import DropDownSearchControl from '../AddSeafarer/DropDownSearchControl';
import FleetDatePicker from '../AddSeafarer/FleetDatePicker';
import DocumentTypeDropdown from './DocumentTypeDropdown';
import DocumentFileUpload from './DocumentFileUpload';
import { FormField } from '../common/Form';

const DocumentFormSTCW = (props) => {
  let {
    targetFormId,
    formsDropdownValues,
    values,
    errors,
    stcwTypeDropdownData = [],
    viewDocument,
    onInputChange,
    handleSelectedFile,
    handleRemoveFile,
  } = props;
  const isEditMode = values.id !== undefined;

  return (
    <Form>
      <Form.Row>
        <DocumentTypeDropdown
          targetFormId={targetFormId}
          onInputChange={onInputChange}
          errors={errors}
          disabled={isEditMode}
          formsDropdownValues={formsDropdownValues}
        />
        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Type of STCW*</Form.Label>
          <DropDownSearchControl
            name={'stcw_licence_id'}
            selectedValue={values.stcw_licence_id}
            dropDownValues={stcwTypeDropdownData}
            onInputChange={onInputChange}
            isInvalid={!!errors.stcw_licence_id}
            testID={'stcw-type'}
          />
          <Form.Control.Feedback
            type="invalid"
            className={errors.stcw_licence_id ? 'set-display-block' : ''}
          >
            {errors.stcw_licence_id}
          </Form.Control.Feedback>
        </Form.Group>
      </Form.Row>
      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Issued By</Form.Label>
          <Form.Control
            type="text"
            name="issued_by"
            value={values.issued_by || ''}
            onChange={onInputChange}
            isInvalid={!!errors.issued_by}
          />
          <Form.Control.Feedback type="invalid">{errors.issued_by}</Form.Control.Feedback>
        </Form.Group>
        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Certificate No.</Form.Label>
          <Form.Control
            type="text"
            name="certificate_no"
            value={values.certificate_no || ''}
            onChange={onInputChange}
            isInvalid={!!errors.certificate_no}
          />
          <Form.Control.Feedback type="invalid">{errors.certificate_no}</Form.Control.Feedback>
        </Form.Group>
      </Form.Row>
      <Form.Row>
        <FormField name="date_of_issue" label="Date of Issue" md="5">
          <FleetDatePicker />
        </FormField>
        <FormField name="date_of_expiry" label="Date of Expiry" md={{ span: 5, offset: 1 }}>
          <FleetDatePicker />
        </FormField>
      </Form.Row>
      <Form.Row>
        <DocumentFileUpload
          error={errors.file}
          value={values.file}
          onViewFile={viewDocument}
          onRemoveFile={handleRemoveFile}
          onSelectFile={handleSelectedFile}
        />
      </Form.Row>
    </Form>
  );
};

export default DocumentFormSTCW;
