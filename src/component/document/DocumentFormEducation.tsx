import React from 'react';
import { Col, Form } from 'react-bootstrap';
import DocumentTypeDropdown from './DocumentTypeDropdown';
import DocumentFileUpload from './DocumentFileUpload';

const DocumentFormEducation = (props) => {
  let {
    targetFormId,
    formsDropdownValues,
    values,
    errors,
    onInputChange,
    handleSelectedFile,
    handleRemoveFile,
    viewDocument,
  } = props;

  const isEditMode = values.id !== undefined;

  return (
    <Form>
      <Form.Row>
        <DocumentTypeDropdown
          targetFormId={targetFormId}
          onInputChange={onInputChange}
          errors={errors}
          disabled={isEditMode}
          formsDropdownValues={formsDropdownValues}
        />
        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Qualification*</Form.Label>
          <Form.Control
            type="text"
            name="qualification"
            value={values.qualification || ''}
            onChange={onInputChange}
            isInvalid={!!errors.qualification}
          />
          <Form.Control.Feedback type="invalid">{errors.qualification}</Form.Control.Feedback>
        </Form.Group>
      </Form.Row>
      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Institute</Form.Label>
          <Form.Control
            type="text"
            name="institute"
            value={values.institute || ''}
            onChange={onInputChange}
            isInvalid={!!errors.institute}
          />
          <Form.Control.Feedback
            type="invalid"
            className={errors.institute ? 'set-display-block' : ''}
          >
            {errors.institute}
          </Form.Control.Feedback>
        </Form.Group>
        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Class</Form.Label>
          <Form.Control
            type="text"
            name="class"
            value={values.class || ''}
            onChange={onInputChange}
            isInvalid={!!errors.class}
          />
          <Form.Control.Feedback type="invalid">{errors.class}</Form.Control.Feedback>
        </Form.Group>
      </Form.Row>
      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Pass Date</Form.Label>
          <Form.Control
            type="text"
            name="pass_date"
            value={values.pass_date || ''}
            onChange={onInputChange}
            isInvalid={!!errors.pass_date}
          />
          <Form.Control.Feedback type="invalid">{errors.pass_date}</Form.Control.Feedback>
        </Form.Group>
        <DocumentFileUpload
          error={errors.file}
          value={values.file}
          onViewFile={viewDocument}
          onRemoveFile={handleRemoveFile}
          onSelectFile={handleSelectedFile}
          md={{ span: 5, offset: 1 }}
        />
      </Form.Row>
    </Form>
  );
};

export default DocumentFormEducation;
