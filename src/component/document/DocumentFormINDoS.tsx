import React from 'react';
import { Col, Form } from 'react-bootstrap';
import FleetDatePicker from '../AddSeafarer/FleetDatePicker';
import DocumentTypeDropdown from './DocumentTypeDropdown';
import DocumentFileUpload from './DocumentFileUpload';
import { FormField } from '../common/Form';

const DocumentFormINDoS = (props) => {
  let {
    targetFormId,
    formsDropdownValues,
    values,
    errors,
    disableDocumentTypeDropdown,
    viewDocument,
    onInputChange,
    handleSelectedFile,
    handleRemoveFile,
  } = props;

  const isEditMode = values.id !== undefined;

  return (
    <Form>
      <Form.Row>
        <DocumentTypeDropdown
          targetFormId={targetFormId}
          onInputChange={onInputChange}
          errors={errors}
          disabled={isEditMode || disableDocumentTypeDropdown}
          formsDropdownValues={formsDropdownValues}
        />
      </Form.Row>
      <Form.Row>
        <Form.Group as={Col} md="5">
          <Form.Label>Issued By</Form.Label>
          <Form.Control
            type="text"
            name="issued_by"
            value={values.issued_by || ''}
            onChange={onInputChange}
            isInvalid={!!errors.issued_by}
          />
          <Form.Control.Feedback type="invalid">{errors.issued_by}</Form.Control.Feedback>
        </Form.Group>
        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
          <Form.Label>Certificate No.*</Form.Label>
          <Form.Control
            type="text"
            name="certificate_no"
            value={values.certificate_no || ''}
            onChange={onInputChange}
            isInvalid={!!errors.certificate_no}
          // testID={DATE_OF_BIRTH_FIELD}
          />
          <Form.Control.Feedback type="invalid">{errors.certificate_no}</Form.Control.Feedback>
        </Form.Group>
      </Form.Row>
      <Form.Row>
        <FormField name="date_of_issue" label="Date of Issue" md="5">
          <FleetDatePicker />
        </FormField>
        <FormField name="date_of_expiry" label="Date of Expiry" md={{ span: 5, offset: 1 }}>
          <FleetDatePicker />
        </FormField>
      </Form.Row>
      <Form.Row>
        <DocumentFileUpload
          error={errors.file}
          value={values.file}
          onViewFile={viewDocument}
          onRemoveFile={handleRemoveFile}
          onSelectFile={handleSelectedFile}
        />
      </Form.Row>
    </Form>
  );
};

export default DocumentFormINDoS;
