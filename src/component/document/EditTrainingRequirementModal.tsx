import React, { useState, useEffect } from 'react';
import { Formik } from 'formik';
import { RouteComponentProps } from 'react-router-dom';
import {
  Col,
  Button,
  Modal,
  Form,
  OverlayTrigger,
  Tooltip,
  Row,
  Spinner as BootstrapSpinner,
} from 'react-bootstrap';
import FleetDatePicker from '../AddSeafarer/FleetDatePicker';
import { useParams } from 'react-router';
import DropDownSearchControl from '../AddSeafarer/DropDownSearchControl';
import AddSeafarerOtherComponents from '../AddSeafarer/AddSeafarerOtherComponents';
import { seafarerTrainingSchema } from '../../model/SeafarerSchemaValidation';
import { getVesselV2Dropdown } from '../../service/vessel-service';
import {
  editTrainingRequirement,
  getSeafarerDocumentUploadLink,
  uploadFileWithUrl,
  getTrainingReqDetailsByTrainingReqId,
} from '../../service/seafarer-service';
import styleGuide from '../../styleGuide';
import ConfirmActionModalView from '../AddSeafarer/ConfirmActionModalView';
import { COMMON_MESSAGES } from '../../constants/common-labels-and-messages';
import moment from 'moment';
import Spinner from '../../component/common/Spinner';
import { TrainingRequirementResult } from '../../../src/types/trainingRequirement';
import { dateAsDayAndTime } from '../../../src/model/utils';
import { ALL_DOC_TYPES } from '../../constants/documentTypes';
import { SeafarerDocument } from '@src/types/seafarerInterfaces';
import AccessHandlerWrapper from '../common/AccessHandlerWrapper';

// eslint-disable-next-line no-undef
interface TrainingReqFormData {
  date_of_issue: string;
  deadline?: string;
  vessel_ownership_id?: number;
  training_needs?: string;
  completed_date?: string;
  training_imparted?: string;
  supporting_document: unknown;
  superintendent_report: unknown;
}

interface EditTrainingRequirementModalProps {
  callTrainingRequirement: () => void;
  history: RouteComponentProps['history'];
  seafarerPersonId: number;
  roleConfig: any;
  isInvestigationTrainingReq: boolean;
}

const { Icon } = styleGuide;

const emptyData = () => {
  return {
    date_of_issue: '',
    deadline: '',
    vessel_ownership_id: 0,
    training_needs: '',
    completed_date: '',
    training_imparted: '',
    supporting_document: undefined,
    superintendent_report: undefined,
  };
};

const { SelectedFileField, SelectFileButton } = AddSeafarerOtherComponents;

const TrainingRequirementForm = (props: {
  values: any;
  errors: any;
  onCloseModal: () => void;
  genOnDateChange: any;
  setFieldValue: any;
  isFromMasterAppraisal: boolean;
  onSubmitValues: any;
  vesselData: any;
  isSaving: boolean;
  isDisabled: boolean;
  isFromSuptAppraisal: boolean;
  traningReqDetails: TrainingRequirementResult;
  documentsToBeDeleted: any;
  documents: any;
  hasTrainingReqEditAccess: boolean;
}) => {
  // eslint-disable-next-line react/prop-types
  let {
    values,
    errors,
    onCloseModal,
    genOnDateChange,
    setFieldValue,
    isFromMasterAppraisal,
    isFromSuptAppraisal,
    onSubmitValues,
    vesselData,
    isSaving,
    isDisabled,
    traningReqDetails,
    documentsToBeDeleted,
    documents,
    hasTrainingReqEditAccess,
  } = props;
  const [
    isRemoveSupportingDocumentConfirmationModalShow,
    setIsRemoveSupportingDocumentConfirmationModalShow,
  ] = useState(false);
  const [
    isRemoveSuperintendentReportConfirmationModalShow,
    setIsRemoveSuperintendentReportConfirmationModalShow,
  ] = useState(false);

  const showToolTip = (isFromMasterAppraisal) => {
    let toolTipString = '';
    if (isFromMasterAppraisal) {
      toolTipString =
        'No deadline is specified when training requirement is from Master Appraisal. However it is recommended to specified a deadline to meet or follow up.';
    } else {
      toolTipString =
        'Deadline is required to make sure the training required has a deadline to meet or follow up.';
    }
    return (
      <OverlayTrigger
        delay={{ show: 250, hide: 400 }}
        overlay={<Tooltip id="add-training-tooltip">{toolTipString}</Tooltip>}
      >
        <span className="d-inline-block">
          <Icon icon="alert" size={15} />
        </span>
      </OverlayTrigger>
    );
  };

  const handleSelectedFile = (file, fileIndex) => {
    if (file === undefined) {
      return;
    }
    documents.push({
      file: file,
      type:
        fileIndex === 'superintendent_report'
          ? ALL_DOC_TYPES.SUPERINTENDENT_REPORT
          : ALL_DOC_TYPES.SUPPORTING_DOCUMENT,
    });
    setFieldValue(fileIndex, file);
  };

  const handleRemoveDocument = (values: any, type: string) => {
    if (type === 'supporting_document') {
      if (values?.supporting_document?.id) {
        documentsToBeDeleted.push({ id: values?.supporting_document?.id, is_delete: true });
      } else {
        for (let i = documents.length - 1; i >= 0; i--) {
          if (documents[i].type === ALL_DOC_TYPES.SUPPORTING_DOCUMENT) {
            documents.splice(i, 1);
          }
        }
      }
      setIsRemoveSupportingDocumentConfirmationModalShow(false);
      setFieldValue(type, undefined);
    } else {
      if (values?.superintendent_report?.id) {
        documentsToBeDeleted.push({
          id: values?.superintendent_report?.id,
          is_delete: true,
        });
      } else {
        for (let i = documents.length - 1; i >= 0; i--) {
          if (documents[i].type === ALL_DOC_TYPES.SUPERINTENDENT_REPORT) {
            documents.splice(i, 1);
          }
        }
      }
      setIsRemoveSuperintendentReportConfirmationModalShow(false);
      setFieldValue(type, undefined);
    }
  };

  const downloadFile = async (document: SeafarerDocument) => {
    if (document?.id !== undefined) {
      window.open(
        `https://${window.location.hostname}/seafarer/document/${document?.id}/${document?.type}`,
      );
    }
  };

  const onInputVesselChange = (e: { target: { name: string; value: number } }): void => {
    const vesselName = vesselData.filter((vessel) => vessel.id === e.target.value);

    if (vesselName.length > 0) {
      setFieldValue('vessel_name', vesselName[0]?.value);
    }
    return setFieldValue('vessel_ownership_id', e.target.value);
  };

  return (
    <Form>
      <Form.Row className="justify-content-between">
        <Form.Group as={Col} md="5" className="training-req-modal-form-group">
          <Form.Label className="form-heading">Date Training Recommended*</Form.Label>
          <FleetDatePicker
            name="date_of_issue"
            onChange={genOnDateChange('date_of_issue')}
            // eslint-disable-next-line react/prop-types
            value={values?.date_of_issue}
            // eslint-disable-next-line react/prop-types
            isInvalid={!!errors.date_of_issue}
            maxDate={new Date()}
            disabled={isFromMasterAppraisal || isFromSuptAppraisal || !hasTrainingReqEditAccess}
            testID="training-req-date-of-issue"
          />
          <Form.Control.Feedback
            type="invalid"
            // eslint-disable-next-line react/prop-types
            className={errors.date_of_issue ? 'set-display-block' : ''} //force the Feedback to be appear based on errors.date_of_issue
          >
            {errors.date_of_issue}
          </Form.Control.Feedback>
        </Form.Group>
        <Form.Group as={Col} md="5" className="training-req-modal-form-group">
          <Form.Label className="form-heading">Deadline* &nbsp;</Form.Label>
          <Form.Label>{showToolTip(isFromMasterAppraisal)}</Form.Label>
          <FleetDatePicker
            name="deadline"
            onChange={genOnDateChange('deadline')}
            // eslint-disable-next-line react/prop-types
            value={values?.deadline}
            // eslint-disable-next-line react/prop-types
            isInvalid={!!errors.deadline}
            disabled={isFromMasterAppraisal || isFromSuptAppraisal || !hasTrainingReqEditAccess}
            testID="training-req-deadline"
          />
          <Form.Control.Feedback
            type="invalid"
            // eslint-disable-next-line react/prop-types
            className={errors.deadline ? 'set-display-block' : ''} //force the Feedback to be appear based on errors.date_of_expiry
          >
            {errors.deadline}
          </Form.Control.Feedback>
        </Form.Group>
      </Form.Row>
      <Form.Row className="justify-content-between">
        <Form.Group as={Col} md="5" className="training-req-modal-form-group">
          <Form.Label className="form-heading">Vessel*</Form.Label>
          <DropDownSearchControl
            name="vessel_ownership_id"
            selectedValue={values?.vessel_ownership_id}
            dropDownValues={vesselData}
            onInputChange={(e) => onInputVesselChange(e)}
            testID="vessel_ownership_id"
            disabled={isFromMasterAppraisal || isFromSuptAppraisal || !hasTrainingReqEditAccess}
            isInvalid={!!errors.vessel_ownership_id}
          />
          <Form.Control.Feedback
            type="invalid"
            className={errors.vessel_ownership_id ? 'set-display-block' : ''}
          >
            {errors.vessel_ownership_id}
          </Form.Control.Feedback>
        </Form.Group>
        <Form.Group as={Col} md="5" className="training-req-modal-form-group">
          <Form.Label className="form-heading">Date Completed*</Form.Label>
          <FleetDatePicker
            name="completed_date"
            // eslint-disable-next-line react/prop-types
            value={values?.completed_date}
            onChange={genOnDateChange('completed_date')}
            // eslint-disable-next-line react/prop-types
            isInvalid={!!errors.completed_date}
            disabled={false}
            placeholder="Select date of Completed"
            minDate={new Date(values.date_of_issue)}
            maxDate={new Date()}
            testID="training-req-date-of-completed"
          />
          <Form.Control.Feedback
            type="invalid"
            // eslint-disable-next-line react/prop-types
            className={errors.completed_date ? 'set-display-block' : ''}
          >
            {errors.completed_date}
          </Form.Control.Feedback>
        </Form.Group>
      </Form.Row>
      <Form.Row className="justify-content-between">
        <Form.Group as={Col} md="5" className="training-req-modal-form-group">
          <Form.Label className="form-heading">Training Needs*</Form.Label>
          <Form.Control
            title="training_needs"
            as="textarea"
            rows={3}
            // eslint-disable-next-line react/prop-types
            value={values?.training_needs}
            onChange={(e) => setFieldValue('training_needs', e.target.value)}
            disabled={isFromMasterAppraisal || isFromSuptAppraisal || !hasTrainingReqEditAccess}
          />
          <Form.Control.Feedback
            type="invalid"
            // eslint-disable-next-line react/prop-types
            className={errors.training_needs ? 'set-display-block' : ''}
          >
            {errors.training_needs}
          </Form.Control.Feedback>
        </Form.Group>
        <Form.Group as={Col} md="5" className="training-req-modal-form-group">
          <Form.Label className="form-heading">Training Imparted*</Form.Label>
          <Form.Control
            as="textarea"
            rows={3}
            value={values?.training_imparted}
            title="training_imparted"
            onChange={(e) => setFieldValue('training_imparted', e.target.value)}
          />
          <Form.Control.Feedback
            type="invalid"
            className={errors.training_imparted ? 'set-display-block' : ''}
          >
            {errors?.training_imparted}
          </Form.Control.Feedback>
        </Form.Group>
      </Form.Row>
      <Form.Row className="justify-content-between">
        <Form.Group as={Col} md="5" className="training-req-modal-form-group">
          <Form.Label className="form-heading">{`Supporting Document (pdf, jpg, png, max size 12MB)`}</Form.Label>
          {values?.supporting_document !== undefined && values?.supporting_document !== null ? (
            <SelectedFileField
              onRemoveClick={() => setIsRemoveSupportingDocumentConfirmationModalShow(true)}
              // to do - add file name in API resposne
              fileName={values?.supporting_document?.name ?? values?.supporting_document?.doc_path}
              onClick={() => downloadFile(values?.supporting_document)}
              disabled={!hasTrainingReqEditAccess}
            />
          ) : (
            <SelectFileButton
              fileIndex="supporting_document"
              onSelectFile={handleSelectedFile}
              isInvalid={!!errors.supporting_document}
              disabled={false}
            />
          )}
          {errors.supporting_document === 'File too large' && (
            <Form.Control.Feedback type="invalid" className="document-copy-feedback">
              {COMMON_MESSAGES.MESSAGE_FILE_SIZE_EXCEED}
            </Form.Control.Feedback>
          )}
          {errors.supporting_document === 'Unsupported Format' && (
            <Form.Control.Feedback type="invalid" className="document-copy-feedback">
              {COMMON_MESSAGES.MESSAGE_FILE_EXTENSION_NOT_SUPPORT}
            </Form.Control.Feedback>
          )}
        </Form.Group>
        <Form.Group as={Col} md="5" className="training-req-modal-form-group">
          <Form.Label className="form-heading">{`Superintendent Report `}</Form.Label>
          {values?.superintendent_report !== undefined && values?.superintendent_report !== null ? (
            <SelectedFileField
              // fileName={values?.superintendent_report?.name} --> to do - add file name
              fileName={
                values?.superintendent_report?.name ?? values?.superintendent_report?.doc_path
              }
              onRemoveClick={() => setIsRemoveSuperintendentReportConfirmationModalShow(true)}
              onClick={() => downloadFile(values?.superintendent_report)}
            />
          ) : (
            <SelectFileButton
              fileIndex="superintendent_report"
              onSelectFile={handleSelectedFile}
              isInvalid={!!errors.superintendent_report}
              disabled={false}
            />
          )}
          {errors.superintendent_report === 'File too large' && (
            <Form.Control.Feedback type="invalid" className="document-copy-feedback">
              {COMMON_MESSAGES.MESSAGE_FILE_SIZE_EXCEED}
            </Form.Control.Feedback>
          )}
          {errors.superintendent_report === 'Unsupported Format' && (
            <Form.Control.Feedback type="invalid" className="document-copy-feedback">
              {COMMON_MESSAGES.MESSAGE_FILE_EXTENSION_NOT_SUPPORT}
            </Form.Control.Feedback>
          )}
          <ConfirmActionModalView
            show={isRemoveSupportingDocumentConfirmationModalShow}
            onClose={() => setIsRemoveSupportingDocumentConfirmationModalShow(false)}
            onConfirm={() => {
              handleRemoveDocument(values, 'supporting_document');
            }}
            title={'Confirm Deleting the Supporting Document?'}
            message={'Are you sure deleting the uploaded Supporting Document?'}
          />
          <ConfirmActionModalView
            show={isRemoveSuperintendentReportConfirmationModalShow}
            onClose={() => setIsRemoveSuperintendentReportConfirmationModalShow(false)}
            onConfirm={() => {
              handleRemoveDocument(values, 'superintendent_report');
            }}
            title={'Confirm deletion of Superintendent Report'}
            message={'Are you sure you want to delete the uploaded Superintendent Report?'}
          />
        </Form.Group>
      </Form.Row>
      <Form.Row>
        <Form.Group as={Col}>
          <Row>
            <Col className="footer-item-edited-by">{`Last Edited by ${
              traningReqDetails?.updated_by_hash ?? '--'
            } on ${dateAsDayAndTime(traningReqDetails?.updated_at) ?? '--'}`}</Col>
          </Row>
        </Form.Group>
      </Form.Row>
      <hr className="grey_line"></hr>
      <Form.Row>
        <div className="ml-auto pr-1">
          <Button
            disabled={isSaving}
            variant="primary"
            className="m-2"
            onClick={() => {
              onCloseModal();
            }}
          >
            Cancel
          </Button>
          <Button
            disabled={isDisabled || isSaving}
            style={isSaving ? { pointerEvents: 'none' } : {}}
            variant="secondary"
            onClick={() => {
              onSubmitValues();
            }}
          >
            Save
            {isSaving && (
              <div className="ml-2">
                <BootstrapSpinner animation="border" size="sm" aria-hidden="true" />
              </div>
            )}
          </Button>
        </div>
      </Form.Row>
    </Form>
  );
};

// eslint-disable-next-line react/prop-types
const EditTrainingRequirementModal = ({
  callTrainingRequirement,
  history,
  seafarerPersonId,
  roleConfig,
  isInvestigationTrainingReq,
}: EditTrainingRequirementModalProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [vesselData, setVesselData] = useState([]);
  const [isDisabled, setIsDisabled] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [traningReqDetails, setTraningReqDetails] = useState<TrainingRequirementResult>();
  const [initialValues, setInitialValues] = useState(emptyData());
  const [formSchema, setFormSchema] = useState(null);
  const [isFromSuptAppraisal, setIsFromSuptAppraisal] = useState(false);
  const [isFromMasterAppraisal, setIsFromMasterAppraisal] = useState(false);
  const hasTrainingReqEditAccess = roleConfig?.seafarer?.edit?.training;
  const hasTrainingReqEditSuptAccess = roleConfig?.seafarer?.edit?.suptTraining;

  let { seafarerId, trainingReqId }: { seafarerId: string; trainingReqId: string } = useParams();

  const trainingReqInitialValues = {
    date_of_issue: moment().startOf('day').format(),
    deadline: moment().add(6, 'months').format(),
    vessel_ownership_id: '',
    training_needs: '',
  };

  const documents: {
    file: File;
    type: string;
  }[] = [];

  const documentsToBeAdded: {
    document_type: string;
    filePath: string;
    seafarer_person_id: number;
    is_delete: boolean;
  }[] = [];

  const documentsToBeDeleted: {
    id: number;
    is_delete: boolean;
  }[] = [];

  const getTrainingDocumentWithFileName = (document: SeafarerTrainingRequirementDocument) => {
    if (document === undefined || document === null) return document;
    const docPath = document?.seafarer_document.doc_path;
    let docName = docPath;
    const filenameRegex = /\d{15}_/;
    const docNameMatches = docPath.split(filenameRegex);
    if (docNameMatches?.[1]) {
      docName = decodeURIComponent(docNameMatches.slice(-1)[0]);
    }
    return {
      ...document.seafarer_document,
      name: docName,
    };
  };

  const processTrainingReqData = (data: TrainingRequirementResult) => {
    const supportingDocument = data?.seafarer_training_requirement_document.find(
      (doc) =>
        doc?.seafarer_document?.type === ALL_DOC_TYPES.SUPPORTING_DOCUMENT &&
        !doc.seafarer_document?.is_deleted,
    );
    const superintendentReport = data?.seafarer_training_requirement_document.find(
      (doc) =>
        doc?.seafarer_document?.type === ALL_DOC_TYPES.SUPERINTENDENT_REPORT &&
        !doc.seafarer_document?.is_deleted,
    );
    return {
      date_of_issue: data?.recommended_date,
      deadline: data?.deadline_date,
      vessel_ownership_id: data?.vessel_ownership_id === null ? 0 : data?.vessel_ownership_id,
      training_needs: data?.training_needs,
      completed_date: data?.completed_date ?? '',
      training_imparted: data?.training_imparted ?? '',
      supporting_document: getTrainingDocumentWithFileName(supportingDocument),
      superintendent_report: getTrainingDocumentWithFileName(superintendentReport),
    };
  };

  const validateEditTraining = (data: TrainingRequirementResult) => {
    const canEditTrainingReq: boolean =
      roleConfig?.seafarer?.edit?.training || roleConfig?.seafarer?.edit?.suptTraining;
    const currentDate = moment();
    const newCompletedDateCreatedAt = data?.completed_date_created_at
      ? moment(data?.completed_date_created_at).add(15, 'days').format()
      : null;

    if (!canEditTrainingReq || currentDate.isAfter(newCompletedDateCreatedAt)) {
      setIsDisabled(true);
    } else {
      setIsDisabled(false);
    }
  };

  const getTrainingReqDetails = async () => {
    setIsLoading(true);
    try {
      const { data } = await getTrainingReqDetailsByTrainingReqId(trainingReqId);
      const trainingData: TrainingReqFormData = processTrainingReqData(data);
      setTraningReqDetails(data);
      validateEditTraining(data);
      setInitialValues(trainingData);
      setIsFromSuptAppraisal(Boolean(data?.supt_appraisal_id));
      setIsFromMasterAppraisal(!!data?.master_appraisal_id);
    } catch (err) {
      console.error(
        `Get seafarer training requirements by Seafarer ID: ${trainingReqId} failed. Error: ${err}`,
      );
    }
    setIsLoading(false);
  };

  const getModifiedValues = (values: any, initialValues: any) => {
    let modifiedValues = {};
    if (values) {
      Object.entries(values).forEach((entry) => {
        let key = entry[0];
        let value = entry[1];

        if (value !== initialValues[key]) {
          modifiedValues[key] = value;
        }
      });
    }
    return modifiedValues;
  };

  const uploadTrainingReqDocuments = async () => {
    await Promise.all(
      documents.map(async (doc) => {
        const docData = {
          seafarer_person_id: seafarerPersonId,
          document_type: doc?.type,
          document_data: doc?.file?.name,
        };

        const fileData = new File([doc?.file], doc?.file?.name, { type: doc?.file?.type });

        if (fileData !== undefined) {
          const createLinkParams = {
            ...docData,
            fileName: fileData.name,
          };
          try {
            const urlResult = await getSeafarerDocumentUploadLink(createLinkParams);
            await uploadFileWithUrl(urlResult.data.url, fileData);
            documentsToBeAdded.push({
              document_type: doc?.type,
              filePath: urlResult?.data?.filePath,
              seafarer_person_id: seafarerPersonId,
              is_delete: false,
            });
          } catch (err) {
            console.error(`Error occur while uploading document of type ${doc?.type}`, err);
          }
        }
      }),
    );
  };

  const onSubmit = async (values: any) => {
    await uploadTrainingReqDocuments();
    const trainingReqDocuments = [...documentsToBeAdded, ...documentsToBeDeleted];

    const modifiedValues = getModifiedValues(values, initialValues);

    const payload = {
      recommended_date:
        modifiedValues?.date_of_issue && moment(modifiedValues?.date_of_issue).format('YYYY-MM-DD'),
      completed_date:
        modifiedValues?.completed_date &&
        moment(modifiedValues?.completed_date).format('YYYY-MM-DD'),
      training_imparted: modifiedValues?.training_imparted,
      training_needs: modifiedValues?.training_needs,
      vessel_ownership_id: modifiedValues?.vessel_ownership_id,
      deadline_date:
        modifiedValues?.deadline && moment(modifiedValues?.deadline).format('YYYY-MM-DD'),
      seafarer_documents: trainingReqDocuments,
      vessel_name: modifiedValues?.vessel_name,
    };
    try {
      const res = await editTrainingRequirement(seafarerId, trainingReqId, payload);
      if (res?.status === 200) {
        callTrainingRequirement();
      }
    } catch (err) {
      console.error('Error occur while submitting edit form', err);
    } finally {
      setIsDisabled(false);
      setIsSaving(false);
      history.push(`/seafarer/details/${seafarerId}/appraisals`);
    }
  };

  const processVesselDropdownData = (vesselArr: []) => {
    const filteredVesselArr = vesselArr
      .filter((item) => item.name !== null && item.name !== undefined)
      .map((item) => ({
        id: item?.id,
        value: `${item?.name}`,
      }));

    filteredVesselArr.unshift({
      id: 0,
      value: 'N/A',
    });

    return filteredVesselArr;
  };

  const loadVesselDropdownData = async () => {
    try {
      setIsLoading(true);
      const params = 'order=name+asc&f=name';
      const vessel = await getVesselV2Dropdown(params);
      setVesselData(processVesselDropdownData(vessel.data.results));
      setIsLoading(false);
    } catch (err) {
      console.error('Error occur while fetching vessel List', err);
    }
  };

  useEffect(() => {
    (async () => {
      await loadVesselDropdownData();
      await getTrainingReqDetails();
      const schema: any = seafarerTrainingSchema(true);
      setFormSchema(schema);
    })();
  }, [seafarerId]);

  return (
    <Modal id="add-training-requirement-modal" show={true} size="xl">
      <AccessHandlerWrapper
        hasRoleAccess={hasTrainingReqEditAccess || hasTrainingReqEditSuptAccess}
      >
        <Modal.Header>
          <Modal.Title style={{ borderBottom: '0' }}>
            Edit {isInvestigationTrainingReq && 'Investigation of'} Training Requirement
            <div className="required-field-text">* Required fields</div>
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {isLoading ? (
            <Spinner alignClass={`load-spinner`} />
          ) : (
            <Formik
              validationSchema={formSchema}
              initialValues={initialValues ?? trainingReqInitialValues}
              enableReinitialize={true}
            >
              {(props) => {
                // eslint-disable-next-line react/prop-types
                let { values, setFieldValue, errors, validateForm } = props;
                const genOnDateChange = (fieldName) => (value) => setFieldValue(fieldName, value);
                const onCloseModal = () => {
                  return history.push(`/seafarer/details/${seafarerId}/appraisals`);
                };
                const onValidateForm = async () => {
                  const errors = await validateForm();
                  return {
                    isFormValid: !Object.keys(errors).length,
                  };
                };
                const onSubmitValues = async () => {
                  return onValidateForm().then(({ isFormValid }) => {
                    if (isFormValid) {
                      setIsSaving(true);
                      setIsDisabled(true);
                      onSubmit(values);
                    }
                  });
                };

                const formComponentCommonProps = {
                  values,
                  errors,
                  setFieldValue,
                  seafarerId,
                  onCloseModal,
                  genOnDateChange,
                  isFromMasterAppraisal,
                  isFromSuptAppraisal,
                  onSubmitValues,
                  vesselData,
                  isSaving,
                  isDisabled,
                  traningReqDetails,
                  documentsToBeDeleted,
                  documents,
                  hasTrainingReqEditAccess,
                  ...props,
                };
                return <TrainingRequirementForm {...formComponentCommonProps} />;
              }}
            </Formik>
          )}
        </Modal.Body>
      </AccessHandlerWrapper>
    </Modal>
  );
};

export default EditTrainingRequirementModal;
