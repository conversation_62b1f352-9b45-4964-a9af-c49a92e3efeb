import React, { useEffect, useState } from 'react';
import { Col, Form } from 'react-bootstrap';
import { getVessels } from '../../service/vessel-service';
import DropDownSearchControl from '../AddSeafarer/DropDownSearchControl';
import FleetDatePicker from '../AddSeafarer/FleetDatePicker';
import Spinner from '../../component/common/Spinner';
import DocumentTypeDropdown from './DocumentTypeDropdown';
import { FormField } from '../common/Form';
import DocumentFileUpload from './DocumentFileUpload';

const DocumentFormDrugAndAlcohol = (props) => {
  let {
    targetFormId,
    formsDropdownValues,
    values,
    errors,
    onInputChange,
    handleSelectedFile,
    handleRemoveFile,
    viewDocument,
  } = props;

  const isEditMode = values.id !== undefined;

  const [vesselDropDownData, setVesselDropDownData] = useState([]);

  const formatVesselDropdownValues = (vessels) => {
    const modifiedData = [];
    vessels?.map((vessel) => {
      modifiedData.push({
        id: {
          ref_id: vessel.ref_id ? vessel.ref_id : vessel.vessel_id,
          name: vessel.name ? vessel.name : '---',
        },
        value: vessel.name ? vessel.name : '---',
      });
    });
    return modifiedData;
  };

  useEffect(() => {
    (async () => {
      try {
        const vessel = await getVessels();
        setVesselDropDownData(formatVesselDropdownValues(vessel?.data));
      } catch (error) {
        console.error(`Get dropdown data failed. Error: ${error}`);
      }
    })();
  }, []);

  return (
    <>
      {vesselDropDownData.length ? (
        <Form>
          <Form.Row>
            <DocumentTypeDropdown
              targetFormId={targetFormId}
              onInputChange={onInputChange}
              errors={errors}
              disabled={isEditMode}
              formsDropdownValues={formsDropdownValues}
            />
            <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
              <Form.Label>Vessel*</Form.Label>
              <DropDownSearchControl
                name={'vessel'}
                selectedValue={values.vessel}
                dropDownValues={vesselDropDownData}
                onInputChange={onInputChange}
                isInvalid={!!errors.vessel}
                testID={'vessel-name'}
              />
              <Form.Control.Feedback
                type="invalid"
                className={errors.vessel ? 'set-display-block' : ''}
              >
                {errors.vessel}
              </Form.Control.Feedback>
            </Form.Group>
          </Form.Row>
          <Form.Row>
            <Form.Group as={Col} md="5">
              <Form.Label>Tester</Form.Label>
              <Form.Control
                type="text"
                name="tester"
                value={values.tester || ''}
                onChange={onInputChange}
                isInvalid={!!errors.tester}
              />
              <Form.Control.Feedback type="invalid">{errors.tester}</Form.Control.Feedback>
            </Form.Group>
            <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
              <Form.Label>Result</Form.Label>
              <Form.Control
                as="select"
                name="is_result_failed"
                value={values.is_result_failed}
                onChange={onInputChange}
                // onBlur={handleBlur}
                isInvalid={!!errors.is_result_failed}
              // testID={DATE_OF_BIRTH_FIELD}
              >
                <option value="">Please select</option>
                <option value={false}>Passed</option>
                <option value={true}>Failed</option>
                ))
              </Form.Control>
              <Form.Control.Feedback type="invalid">
                {errors.is_result_failed}
              </Form.Control.Feedback>
            </Form.Group>
          </Form.Row>
          <Form.Row>
            <FormField name="date_of_test" label="Date of Test" md="5">
              <FleetDatePicker />
            </FormField>
            <FormField name="date_of_expiry" label="Date of Expiry" md={{ span: 5, offset: 1 }}>
              <FleetDatePicker />
            </FormField>
          </Form.Row>
          <Form.Row>
          <DocumentFileUpload
            error={errors.file}
            value={values.file}
            onViewFile={viewDocument}
            onRemoveFile={handleRemoveFile}
            onSelectFile={handleSelectedFile}
          />
          </Form.Row>
        </Form>
      ) : (
        <div className="mt-5">
          {' '}
          <Spinner />{' '}
        </div>
      )}
    </>
  );
};

export default DocumentFormDrugAndAlcohol;
