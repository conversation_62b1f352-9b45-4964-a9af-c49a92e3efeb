import React from 'react';

const TakeoverInputArrayControl = ({
  onInputArrayChange,
  onInputArrayRemoveRow,
  vessel,
  arrayId,
  minRow,
  onRow,
}) => {
  const rows = [];
  const arrayData = vessel?.[arrayId] ?? [];
  const rowCount = arrayData && arrayData.length > minRow ? arrayData.length : minRow;

  for (let i = 0; i < rowCount; i++) {
    const onRowInputChange = (event) => {
      onInputArrayChange(arrayId, i, event);
    };

    const onRemoveRow = (event) => {
      onInputArrayRemoveRow(arrayId, i, event);
    };

    const rowData = arrayData && arrayData.length > i ? arrayData[i] : {};

    rows.push(
      onRow({ rowData, onRowInputChange, onRemoveRow, rowId: i }),
    );
  }
  return <>{rows}</>;
};

export default TakeoverInputArrayControl;
