import React, { useState, useEffect } from 'react';
import {
  Button,
  Modal,
  Form,
  Table,
  Spinner as BootstrapSpinner,
  InputGroup,
} from 'react-bootstrap';
import { Formik, FormikValues } from 'formik';
import { Dash, capitalizeArgs, parseDate } from '../../model/utils';
import _ from 'lodash';
import 'react-datepicker/dist/react-datepicker.css';
import DropDownSearchControl from '../AddSeafarer/DropDownSearchControl';
import * as seafarerService from '../../service/seafarer-service';
import Spinner from '../common/Spinner';
import LastEditedByLine from '../document/LastEditedByLine';
import { useParams } from 'react-router-dom';
import FleetDatePicker from '../AddSeafarer/FleetDatePicker';
import { seafarerWagesUpdateSchema } from '../../model/SeafarerSchemaValidation';
import ErrorDisplayModal from '../common/ErrorDisplayModal';
import {
  Payhead,
  SeafarerWages,
  SeafarerWagesDetails,
  SeafarerWagesDetailsPayload,
  SeafarerWagesPayload,
} from '../../types/wagesInterfaces';
import { SeafarerContacts } from '../../types/seafarerInterfaces';
import {
  ALLOWED_PAYHEAD_CATEGORY_FOR_WAGES,
  DEFAULT_CURRENCY_UNIT,
  HOURLY,
  WAGES_STATUS_PENDING,
  WAGES_STATUS_APPLIED,
} from '../../constants/seafarer-wages';
import moment from 'moment';
import { objectDifference } from '../../util/form-utils';
import AccessHandlerWrapper from '../common/AccessHandlerWrapper';
import { seafarerStatus } from '../../model/constants';
import styleGuide from '../../styleGuide';
import { EMAIL } from '../../constants/contactTypes';
// eslint-disable-next-line no-unused-vars
import { UpdateWagesModalProps } from '../../types/updateWagesModal';
import { formatAmount, getStatusHistoryWithLatestVesselOwnershipData } from '../../util/view-utils';
const { Icon } = styleGuide;

const emptyData = () => {
  return {
    new_rank: 0,
    effective_date: '',
    new_contract_end_date: '',
    remarks: '',
    promotion: false,
    created_at: '',
    updated_at: '',
    created_by: null,
    last_updated_by: null,
    vessel_name: Dash,
    email: null,
  };
};

const UpdateWagesModal = ({
  setShowUpdateWagesModal,
  seafarer,
  history,
  showUpdateWagesModal,
  refreshDetailsPageData,
  eventTracker,
  roleConfig,
  activeVesselData,
}: UpdateWagesModalProps) => {
  const [dropDownData, setDropDownData] = useState([]);
  const [data, setData] = useState(emptyData());
  const [initialData, setInitialData] = useState(emptyData());
  const [isLoading, setIsLoading] = useState(false);
  const [payHeadData, setPayHeadData] = useState<any[]>([]);
  const [basicSalary, setBasicSalary] = useState('0.00');
  const [totalSalary, setTotalSalary] = useState('0.00');
  const [modalMessage, setModalMessage] = useState(null);
  const [formSchema, setFormSchema] = useState(null);
  const [isWagesExist, setIsWagesExist] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [wagesDetails, setWagesDetails] = useState<SeafarerWagesDetails[]>();
  const [wagesId, setWagesId] = useState(Number);
  const [isEffectiveDateRequired, setIsEffectiveDateRequired] = useState(false);
  const { seafarerId } = useParams<{ seafarerId: string }>();
  const [seafarerStatusHistoryId, setSeafarerStatusHistoryId] = useState(Number);
  const prevPathName = history?.location?.state?.from ?? '';
  const [isSignedOn, setIsSignedOn] = useState(false);
  const [isPromotionPending, setIsPromotionPending] = useState(false);
  const [prohibitedMsg, setProhibitedMsg] = useState<string | null>(null);
  const [isEmailDisabled, setIsEmailDisabled] = useState(false);
  const [currencyUnit, setCurrencyUnit] = useState<string>(DEFAULT_CURRENCY_UNIT);

  const fullName =
    capitalizeArgs(
      _.get(seafarer, 'seafarer_person.first_name', ''),
      _.get(seafarer, 'seafarer_person.middle_name', ''),
      _.get(seafarer, 'seafarer_person.last_name', ''),
    ) || Dash;

  const onHideModalMessage = () => setModalMessage(null);

  useEffect(() => {
    (async () => {
      loadDefaultData();
    })();
  }, []);

  useEffect(() => {
    (async () => {
      const effectiveDateFlag = isWagesExist && isSignedOn;
      setIsEffectiveDateRequired(effectiveDateFlag);
      const schema = seafarerWagesUpdateSchema(
        payHeadData,
        effectiveDateFlag,
        isEmailDisabled,
        _.get(seafarer, 'seafarer_rank.id', null),
      );
      setFormSchema(schema);
    })();
  }, [payHeadData, isEmailDisabled]);

  const loadDefaultData = async () => {
    setIsLoading(true);
    try {
      const response = await Promise.allSettled([
        seafarerService.getSeafarerDropDownData('?values=ranks'),
        seafarerService.getSeafarerWages(seafarerId, '?with_history=true'),
      ]);
      if (response[0].status === 'fulfilled') {
        const ranks = response[0]?.value?.ranks;
        setDropDownData(ranks);
      }
      if (response[1].status === 'fulfilled') {
        const allWagesData = response[1]?.value?.data;
        const currentPendingWagesData = allWagesData.find(
          (i) => i?.is_history === false && i.status === WAGES_STATUS_PENDING,
        );
        const currentAppliedWagesData = allWagesData.find(
          (i) => i?.is_history === false && i.status === WAGES_STATUS_APPLIED,
        );

        const targetWagesData =
          currentPendingWagesData ?? currentAppliedWagesData ?? _.get(allWagesData, 0);
        if (
          [seafarerStatus.RECOMMENDED, seafarerStatus.RECOMMENDED_WITH_DEVIATION].includes(
            targetWagesData?.seafarer_status_history?.seafarer_journey_status,
          )
        ) {
          setProhibitedMsg(
            `Wages cannot be set for crew assignment plan status ${targetWagesData?.seafarer_status_history?.seafarer_journey_status}`,
          );
          throw new Error(
            `Wages cannot be set for crew assignment plan status ${targetWagesData?.seafarer_status_history?.seafarer_journey_status}`,
          );
        }
        let payheads;
        if (targetWagesData && Object.hasOwn(targetWagesData, 'seafarer_wages_details')) {
          setIsWagesExist(true);
          const wagesDetailsData = targetWagesData?.seafarer_wages_details;
          payheads = wagesDetailsData.map((e: SeafarerWagesDetails) => e.payhead);
        } else {
          payheads = targetWagesData?.seafarer_wages_details_payheads;
        }
        let displayPayheads = payheads
          ?.filter((e: Payhead) => ALLOWED_PAYHEAD_CATEGORY_FOR_WAGES.includes(e.category))
          .map((i: Payhead) => {
            if (i.nature === HOURLY) {
              return { ...i, head_name: `${i.head_name} (${HOURLY})` };
            }
            return i;
          });
        displayPayheads.sort(
          (a: Payhead, b: Payhead) =>
            a.display_order - b.display_order || a.head_name.localeCompare(b.head_name),
        );
        const signedOnFlag =
          targetWagesData?.seafarer_status_history?.seafarer_journey_status === 'signed_on';
        setIsSignedOn(signedOnFlag);
        setPayHeadData(displayPayheads);
        setCurrencyUnit(
          targetWagesData?.seafarer_status_history?.recommended_wages_unit?.toUpperCase() ||
            DEFAULT_CURRENCY_UNIT,
        );
        extractDataFromResponse(targetWagesData, displayPayheads);
      }
    } catch (error) {
      console.log('Failed to load default data', error);
    } finally {
      setIsLoading(false);
    }
  };

  const extractDataFromResponse = (targetWagesData: SeafarerWages, displayPayheads: Payhead[]) => {
    let wagesData = targetWagesData;
    wagesData = {
      ...wagesData,
      seafarer_status_history: getStatusHistoryWithLatestVesselOwnershipData(
        activeVesselData,
        wagesData?.seafarer_status_history,
      ),
    };
    if (wagesData.status === WAGES_STATUS_APPLIED)
      wagesData = { ...wagesData, seafarer_promotion: [] };

    const isMissingWagesDetails = !!wagesData.seafarer_wages_details_payheads;
    const contacts = _.get(seafarer, 'seafarer_person.seafarer_contacts', null);
    const emails = contacts
      ? contacts.filter((e: SeafarerContacts) => e.contact_type === 'email')
      : null;
    const sortedEmails = [...(emails ?? [])].sort((a: SeafarerContacts, b: SeafarerContacts) =>
      moment(a.created_at).diff(b.created_at),
    );
    const email = emails ? sortedEmails[0] : null;
    if (email && email.contact !== '') {
      setIsEmailDisabled(true);
    }
    if (!isMissingWagesDetails) {
      const statusId = wagesData?.seafarer_status_history?.id;
      const seafarerWagesId = wagesData?.id;
      setSeafarerStatusHistoryId(statusId);
      setWagesId(seafarerWagesId);
      const res = displayPayheads.map((e: Payhead) => {
        const keyName = `payhead_${e.id}`;
        const wageDetail = wagesData.seafarer_wages_details.find((ele) => ele.payhead_id === e.id);
        const nullSalary = 0;
        const amt = wageDetail?.amount ? wageDetail?.amount : nullSalary;
        const result = [keyName.toString(), twoDigitFloat(amt)];
        return result;
      });
      const payheads = Object.fromEntries(res);
      const isPromotion = _.get(wagesData, 'seafarer_promotion[0].is_promotion', false);
      const values = {
        new_rank: isPromotion ? _.get(wagesData, 'seafarer_promotion[0].new_rank_id', null) : null,
        effective_date: _.get(wagesData, 'effective_date', ''),
        new_contract_end_date: isPromotion
          ? _.get(wagesData, 'seafarer_promotion[0].new_contract_end_date', '')
          : '',
        remarks: _.get(wagesData, 'remarks', ''),
        promotion: isPromotion,
        created_at: _.get(wagesData, 'created_at', ''),
        updated_at: _.get(wagesData, 'updated_at', ''),
        created_by: _.get(wagesData, 'created_by', ''),
        last_updated_by: _.get(wagesData, 'last_updated_by', ''),
        vessel_name: _.get(wagesData, 'seafarer_status_history.vessel_name', Dash),
        email: _.get(email, 'contact', null),
        ...payheads,
      };
      if (values.promotion) setIsPromotionPending(true);
      setData(values);
      setInitialData(values);
      setWagesDetails(wagesData.seafarer_wages_details);
      setBasicAndTotalSalary(_.get(wagesData, 'seafarer_wages_details', []), displayPayheads);
    } else {
      const statusId = wagesData?.seafarer_status_history?.id;
      setSeafarerStatusHistoryId(statusId);
      const res = displayPayheads.map((e: any) => {
        const keyName = `payhead_${e.id}`;
        const result = [keyName.toString(), twoDigitFloat(e.default_value)];
        return result;
      });
      const payheads = Object.fromEntries(res);
      const newData = {
        ...data,
        ...payheads,
        vessel_name: _.get(wagesData, 'seafarer_status_history.vessel_name', Dash),
        email: _.get(email, 'contact', null),
      };

      setData(newData);
      setInitialData(newData);
      const nullSalary = 0;
      setBasicSalary(nullSalary.toFixed(2));
      setTotalSalary(nullSalary.toFixed(2));
    }
  };

  const setBasicAndTotalSalary = (data: SeafarerWagesDetails[], displayPayheads: Payhead[]) => {
    const newBasicSalary = data.reduce((total, i) => {
      const payheadDetails = displayPayheads.find((e) => i?.payhead_id === e.id);
      if (payheadDetails?.is_add_to_basic_wages) {
        const parsedAmount = parseFloat(i.amount + '');
        return total + (Number.isNaN(parsedAmount) ? 0 : parsedAmount);
      }
      return total;
    }, 0);
    const newTotalSalary = data.reduce((total, i) => {
      const payheadDetails = displayPayheads.find((e) => i?.payhead_id === e.id);
      if (payheadDetails?.is_add_to_total_wages) {
        const parsedAmount = parseFloat(i.amount + '');
        return total + (Number.isNaN(parsedAmount) ? 0 : parsedAmount);
      }
      return total;
    }, 0);
    setBasicSalary(twoDigitFloat(newBasicSalary));
    setTotalSalary(twoDigitFloat(newTotalSalary));
  };

  const setBasicAndTotalSalaryOnEdit = (id: number, name: string, value: string) => {
    const newData = {
      ...data,
      [name]: value,
    };
    setData(newData);
    const payHeadIds = payHeadData.map((i: Payhead) => i.id);
    const { totalSalarySum, basicSalarySum } = payHeadIds.reduce(
      (salaraysSum, id) => {
        const isAddToBasic = payHeadData.find((e) => e.id === id).is_add_to_basic_wages;
        const isAddToTotal = payHeadData.find((e) => e.id === id).is_add_to_total_wages;
        if (isAddToBasic) {
          salaraysSum.basicSalarySum += _.toNumber(_.get(newData, `payhead_${id}`, 0));
        }
        if (isAddToTotal) {
          salaraysSum.totalSalarySum += _.toNumber(_.get(newData, `payhead_${id}`, 0));
        }
        return salaraysSum;
      },
      {
        totalSalarySum: 0,
        basicSalarySum: 0,
      },
    );
    setBasicSalary(twoDigitFloat(basicSalarySum));
    setTotalSalary(twoDigitFloat(totalSalarySum));
  };

  const twoDigitFloat = (input: any) => {
    if (input !== null && input !== undefined) {
      let num = input.toString();
      if (num.indexOf('.') !== -1 && num.search(/.00$/g) === -1) {
        num = num.slice(0, num.indexOf('.') + 3);
        return Number(num).toFixed(2);
      } else {
        return Number(input).toFixed(2);
      }
    }
  };

  const convertFormValuesToWagesDetails = (values: Object) => {
    return Object.keys(values)
      .filter((e) => {
        return e.split('_')[0] === 'payhead';
      })
      .map((e) => {
        const id = parseInt(e.split('_')[1]);
        let res: SeafarerWagesDetailsPayload = {
          payhead_id: id,
          amount: parseFloat(values[e]),
        };
        if (isWagesExist && wagesDetails) {
          const wagesDetailsData = wagesDetails.find(
            (e: SeafarerWagesDetails) => e.payhead.id === id,
          );
          const wagesDetailsId = wagesDetailsData?.id;
          res = {
            ...res,
            id: wagesDetailsId,
          };
        }
        return res;
      });
  };

  const handleCancelButton = () => {
    eventTracker('updateWagesModalCancelButton', '');
    if (showUpdateWagesModal) {
      setShowUpdateWagesModal(false);
    }
    return history.push(`/seafarer/details/${seafarerId}/pre-joining`);
  };

  const handleSaveButton = async (values: FormikValues) => {
    eventTracker('updateWagesModalSaveButton', '');
    setIsSaving(true);
    if (!isWagesExist) {
      let payload: SeafarerWagesPayload;
      const isPromotion = values.promotion;

      let seafarer_wages_details = convertFormValuesToWagesDetails(values);

      payload = {
        remarks: values.remarks,
        seafarer_wages_details,
        amount_unit: currencyUnit.toLowerCase(),
      };
      if (isPromotion) {
        let seafarer_promotion = {
          new_rank_id: values.new_rank,
          new_contract_end_date: moment(values.new_contract_end_date).format('YYYY-MM-DD'),
          is_promotion: true,
        };
        payload = {
          ...payload,
          seafarer_promotion,
        };
      }

      if (!isEmailDisabled) {
        const seafarer_contacts = {
          seafarer_person_id: _.get(seafarer, 'seafarer_person.id'),
          contact_type: EMAIL,
          contact: values.email,
        };
        payload = {
          ...payload,
          seafarer_contacts,
        };
      }

      try {
        const res = await seafarerService.createSeafarerWages(
          seafarerId,
          seafarerStatusHistoryId,
          payload,
        );

        if (res?.status === 201) {
          refreshDetailsPageData();
          if (showUpdateWagesModal) {
            setShowUpdateWagesModal(false);
          }
          if (prevPathName === `/seafarer/details/${seafarerId}/pre-joining`) {
            return history.push(`/seafarer/details/${seafarerId}/pre-joining`, {
              from: history.location.pathname,
            });
          } else {
            return history.goBack();
          }
        }
      } catch (e) {
        console.error(e);
        setModalMessage(e?.response?.data ?? 'Something went wrong, Please try again later');
        setIsSaving(false);
      }
    } else {
      const patchBodyFormValues = objectDifference(initialData, values);
      if (patchBodyFormValues) {
        let payload: SeafarerWagesPayload = {};
        const isPromotion = values.promotion;
        const seafarer_wages_details = patchBodyFormValues
          ? convertFormValuesToWagesDetails(patchBodyFormValues)
          : null;

        if (values.effective_date) {
          payload = {
            ...payload,
            effective_date: parseDate(values.effective_date)?.format('YYYY-MM-DD'),
          };
        }

        if (isPromotion) {
          let seafarer_promotion = {
            new_rank_id: values.new_rank,
            new_contract_end_date: moment(values.new_contract_end_date).format('YYYY-MM-DD'),
            is_promotion: true,
          };
          payload = {
            ...payload,
            seafarer_promotion,
          };
        }
        if (seafarer_wages_details) {
          payload = {
            ...payload,
            seafarer_wages_details,
          };
        }
        if (Object.hasOwn(patchBodyFormValues, 'remarks')) {
          payload = {
            ...payload,
            remarks: values.remarks,
          };
        }

        if (!isEmailDisabled) {
          const seafarer_contacts = {
            seafarer_person_id: _.get(seafarer, 'seafarer_person.id'),
            contact_type: EMAIL,
            contact: values.email,
          };
          payload = {
            ...payload,
            seafarer_contacts,
          };
        }

        try {
          payload.amount_unit = currencyUnit.toLowerCase();
          const res = await seafarerService.patchSeafarerWages(
            seafarerId,
            seafarerStatusHistoryId,
            wagesId,
            payload,
          );
          if (res?.status === 201) {
            refreshDetailsPageData();
            if (showUpdateWagesModal) {
              setShowUpdateWagesModal(false);
            }
            if (prevPathName === `/seafarer/details/${seafarerId}/pre-joining`) {
              return history.push(`/seafarer/details/${seafarerId}/pre-joining`, {
                from: history.location.pathname,
              });
            } else {
              return history.goBack();
            }
          }
        } catch (e) {
          console.error(e);
          setModalMessage(e?.response?.data ?? 'Something went wrong, Please try again later');
          setIsSaving(false);
        }
      }
    }
  };
  const prohibitedMsgComponent = () => {
    if (prohibitedMsg) {
      return (
        <>
          <div className="no-result-found mb-3 pb-3">
            <Icon icon="alert" className="alert-icon-no-search" style={{ float: 'none' }} />
            <div>
              <b>{prohibitedMsg}</b>
            </div>
          </div>
          <Modal.Footer>
            <div className="d-flex justify-content-end pr-3">
              <Button
                variant="secondary"
                className="m-2"
                onClick={handleCancelButton}
                data-testid="wages-history-modal-cancel-btn"
              >
                Cancel
              </Button>
            </div>
          </Modal.Footer>
        </>
      );
    }
    return null;
  };

  const renderBody = () => {
    if (isLoading) {
      return (
        <div className="pb-5">
          <Spinner />
        </div>
      );
    }
    if (prohibitedMsg) {
      return prohibitedMsgComponent();
    }

    return (
      <Modal.Body className="px-3 py-0">
        <Formik
          validationSchema={formSchema}
          initialValues={initialData}
          onSubmit={handleSaveButton}
        >
          {({ handleBlur, values, errors, setFieldValue, dirty, handleChange, handleSubmit }) => {
            const onInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
              const targetName = event?.target?.name;
              const targetValue = event?.target?.value;
              const targetId = event?.target?.id;

              if (targetName === 'promotion') {
                const checkedValue = event?.target?.checked;
                setFieldValue(targetName, checkedValue ?? undefined);
              } else if (targetName.split('_')[0] === 'payhead') {
                const nullAmount = 0;
                setFieldValue(targetName, targetValue || nullAmount);
                setBasicAndTotalSalaryOnEdit(parseInt(targetId), targetName, targetValue);
              } else {
                setFieldValue(targetName, targetValue || undefined); //to make sure undefined is set if value is empty string
              }
            };

            const handleFocus = (event: React.FocusEvent<HTMLInputElement>) =>
              event.target.select();

            const genOnDateChange = (fieldName) => (value) => setFieldValue(fieldName, value);

            const blockInvalidChar = (e: React.KeyboardEvent<HTMLInputElement>) => {
              if (['e', 'E', '+', '-'].includes(e.key)) {
                e.preventDefault();
              }
              const targetValue = e?.target?.value;
              if (targetValue === '0' && ['0'].includes(e.key)) {
                e.preventDefault();
              }
            };

            return (
              <Form
                onSubmit={(e) => {
                  e.preventDefault();
                  handleSubmit();
                }}
              >
                <div className="update-wages-modal-body">
                  <Form.Row className="m-auto">
                    <Form.Group className="col-sm-5 mb-3">
                      <Form.Label className="form-heading">Effective Date*</Form.Label>
                      <FleetDatePicker
                        name="effective_date"
                        value={values.effective_date}
                        onChange={genOnDateChange('effective_date')}
                        isInvalid={!!errors?.effective_date}
                        disabled={!isEffectiveDateRequired}
                        testID="fml-update-wages-modal-effective-date"
                      />
                      {errors?.effective_date && (
                        <div className="invalid-feedback d-block">{errors.effective_date}</div>
                      )}
                    </Form.Group>
                    <div className="desktop col-1 mb-3"></div>
                    <Form.Group className="col-sm-5 mb-3">
                      <Form.Label className="form-heading">Promotion</Form.Label>
                      <Form.Check
                        name="promotion"
                        checked={values.promotion}
                        className="big-checkbox"
                        type="checkbox"
                        label="Promote to another rank"
                        onChange={onInputChange}
                        onClick={() => {
                          eventTracker('updateWagesModalPromotionCheckBox', '');
                        }}
                        disabled={!isEffectiveDateRequired || isPromotionPending}
                      />
                      {errors?.promotion && (
                        <div className="invalid-feedback d-block">{errors.promotion}</div>
                      )}
                    </Form.Group>
                  </Form.Row>
                  <Form.Row className="m-auto">
                    <div className="col-sm-5">
                      <div className="row">
                        <Form.Group className="col mb-3">
                          <Form.Label className="form-heading">Email*</Form.Label>
                          <Form.Control
                            name="email"
                            defaultValue={values?.email ?? undefined}
                            as="input"
                            onBlur={handleBlur}
                            placeholder=""
                            onChange={onInputChange}
                            disabled={isEmailDisabled}
                          />
                          {errors?.email && (
                            <div className="invalid-feedback d-block">{errors.email}</div>
                          )}
                        </Form.Group>
                      </div>
                      <div className="row">
                        <Form.Group className="col mb-3">
                          <Form.Label className="form-heading">Remarks</Form.Label>
                          <Form.Control
                            name="remarks"
                            defaultValue={values.remarks}
                            as="textarea"
                            onBlur={handleBlur}
                            placeholder=""
                            onChange={onInputChange}
                          />
                          {errors?.remarks && (
                            <div className="invalid-feedback d-block">{errors.remarks}</div>
                          )}
                        </Form.Group>
                      </div>
                    </div>
                    <div className="desktop col-1 mb-3"></div>
                    <div className="col-sm-5">
                      <div className="row">
                        <Form.Group className="col mb-3">
                          <Form.Label className="form-heading">New Rank*</Form.Label>
                          <DropDownSearchControl
                            name="new_rank"
                            selectedValue={values.new_rank}
                            dropDownValues={dropDownData}
                            onInputChange={onInputChange}
                            placeholder=""
                            isInvalid={false}
                            testID={'new_rank'}
                            disabled={!values.promotion || !isSignedOn}
                          />
                          {errors?.new_rank && (
                            <div className="invalid-feedback d-block">{errors.new_rank}</div>
                          )}
                        </Form.Group>
                      </div>
                      <div className="row">
                        <Form.Group className="col mb-3">
                          <Form.Label className="form-heading">New Contract End Date*</Form.Label>
                          <FleetDatePicker
                            name="new_contract_end_date"
                            value={values.new_contract_end_date}
                            onChange={genOnDateChange('new_contract_end_date')}
                            isInvalid={errors?.new_contract_end_date}
                            disabled={!values.promotion || !isSignedOn}
                            testID="fml-update-wages-modal-new-contract-end-date"
                          />
                          {errors?.new_contract_end_date && (
                            <div className="invalid-feedback d-block">
                              {errors.new_contract_end_date}
                            </div>
                          )}
                        </Form.Group>
                      </div>
                    </div>
                  </Form.Row>
                  <hr className="grey_line mb-0" />
                  <div className="document-table-title font-weight-bold py-2">UPDATE WAGES</div>
                  <Form.Row>
                    <div className="col-sm-6">
                      <div className="update-wages-table pt-3">
                        <Table>
                          <thead>
                            <tr className="row px-3">
                              <th className="col-7">Monthly Allowance</th>
                              <th className="col-5">Amount</th>
                            </tr>
                          </thead>
                          <tbody>
                            {payHeadData
                              .filter((item) => item?.type === 'Allowance')
                              .map((i) => {
                                const payheadName = `payhead_${i.id}`;
                                return (
                                  <tr key={i.id} className="row px-3">
                                    <td className="col-7">{i?.head_name}</td>
                                    <td className="col-5">
                                      <InputGroup>
                                        <InputGroup.Prepend>
                                          <InputGroup.Text>{currencyUnit}</InputGroup.Text>
                                        </InputGroup.Prepend>
                                        <Form.Control
                                          id={i?.id}
                                          className="update-wages-table-input-field wages-input"
                                          value={values[payheadName]}
                                          name={payheadName}
                                          onBlur={handleBlur}
                                          onFocus={handleFocus}
                                          min={0}
                                          step={0.01}
                                          type="number"
                                          placeholder=""
                                          onChange={onInputChange}
                                          onKeyDown={blockInvalidChar}
                                          data-testid={`update-wages-modal-payhead-${i?.id}-amount`}
                                        />
                                      </InputGroup>
                                      {errors[payheadName] && (
                                        <div className="invalid-feedback d-block">
                                          {errors[payheadName]}
                                        </div>
                                      )}
                                    </td>
                                  </tr>
                                );
                              })}
                          </tbody>
                        </Table>
                      </div>
                    </div>
                    <div className="col-sm-6">
                      <div className="update-wages-table pt-3">
                        <Table striped>
                          <thead>
                            <tr>
                              <th className="col-7">Monthly Deduction</th>
                              <th className="col-5">Amount</th>
                            </tr>
                          </thead>
                          <tbody>
                            {payHeadData
                              .filter((item) => item?.type === 'Deduction')
                              .map((i) => {
                                const payheadName = `payhead_${i.id}`;
                                return (
                                  <tr key={i.id}>
                                    <td>{i?.head_name}</td>
                                    <td>
                                      <InputGroup>
                                        <InputGroup.Prepend>
                                          <InputGroup.Text>{currencyUnit}</InputGroup.Text>
                                        </InputGroup.Prepend>
                                        <Form.Control
                                          id={i?.id}
                                          className="update-wages-table-input-field wages-input"
                                          value={values[payheadName]}
                                          name={payheadName}
                                          onBlur={handleBlur}
                                          onFocus={handleFocus}
                                          min={0}
                                          step={0.01}
                                          type="number"
                                          placeholder=""
                                          onChange={onInputChange}
                                          onKeyDown={blockInvalidChar}
                                        />
                                      </InputGroup>
                                      {errors[payheadName] && (
                                        <div className="invalid-feedback d-block">
                                          {errors[payheadName]}
                                        </div>
                                      )}
                                    </td>
                                  </tr>
                                );
                              })}
                          </tbody>
                        </Table>
                      </div>
                    </div>
                  </Form.Row>
                  <div className="px-3 last-edited-by-line">
                    {(data.created_by || data.created_at) && (
                      <LastEditedByLine
                        updated_after_created={false}
                        created_by={data.created_by}
                        updated_by={data.last_updated_by}
                        created_at={data.created_at}
                        updated_at={data.updated_at}
                      />
                    )}
                    {(data.last_updated_by || data.updated_at) && (
                      <LastEditedByLine
                        updated_after_created={true}
                        created_by={data.created_by}
                        updated_by={data.last_updated_by}
                        created_at={data.created_at}
                        updated_at={data.updated_at}
                      />
                    )}
                  </div>
                </div>
                <div>
                  <hr className="dark_grey_line mb-0" />
                  <Form.Row className="py-3">
                    <div className="col-sm-7 ml-3 pl-3">
                      <Form.Row>
                        <div className="col-6 col-sm-3 form-heading">Basic Salary</div>
                        <div className="col-5 col-sm-3 mx-2 px-2 form-heading text-right">
                          <span>{formatAmount(basicSalary, currencyUnit)}</span>
                        </div>
                      </Form.Row>
                      <Form.Row>
                        <div className="col-6 col-sm-3 form-heading">Total Salary</div>
                        <div className="col-5 col-sm-3 mx-2 px-2 form-heading text-right">
                          <span>{formatAmount(totalSalary, currencyUnit)}</span>
                        </div>
                      </Form.Row>
                    </div>
                    <div className="col-sm-4 ml-auto">
                      <div className="d-flex justify-content-end pr-3">
                        <Button
                          variant="primary"
                          className="m-2"
                          disabled={isSaving}
                          onClick={handleCancelButton}
                        >
                          Cancel
                        </Button>
                        <Button
                          variant="secondary"
                          className="m-2"
                          type="submit"
                          disabled={isSaving || !dirty}
                          data-testid="fml-update-wages-modal-save"
                        >
                          {isSaving ? (
                            <div className="ml-2">
                              <BootstrapSpinner animation="border" size="sm" aria-hidden="true" />
                            </div>
                          ) : (
                            'Save'
                          )}
                        </Button>
                      </div>
                    </div>
                  </Form.Row>
                </div>
              </Form>
            );
          }}
        </Formik>
      </Modal.Body>
    );
  };

  return (
    <>
      <Modal
        id="update-wages-and-promotions"
        show={true}
        aria-labelledby="update-wages-and-promotions-modal"
        centered
        size="xl"
        backdrop="static"
      >
        <AccessHandlerWrapper hasRoleAccess={roleConfig?.seafarer?.edit?.wages}>
          <Modal.Header>
            <Modal.Title style={{ borderBottom: '0' }} className="pl-2">
              {`Update Wages / Promotions of ${fullName} (${_.get(
                seafarer,
                'seafarer_rank.unit',
                Dash,
              )}) (${_.get(seafarer, 'hkid', Dash)}) of ${data.vessel_name}`}
              <div className="required-field-text">* Required fields</div>
            </Modal.Title>
          </Modal.Header>
          {renderBody()}
        </AccessHandlerWrapper>
      </Modal>
      <ErrorDisplayModal onHideModalMessage={onHideModalMessage} modalMessage={modalMessage} />
    </>
  );
};

export default UpdateWagesModal;
