import React from 'react';
import { Button, Form } from 'react-bootstrap';
import './pagination.scss';

interface PageNumInterface {
  active?: boolean;
  disabled: boolean;
  children: React.ReactNode;
  onClick: () => {};
}

const PageNum = ({ active, disabled, children, onClick }: PageNumInterface) => {
  const className = `page-num page-num-${active ? 'active' : 'inactive'} page-num-${
    disabled ? 'disabled' : 'enabled'
  }`;
  return (
    <Button variant="link" className={className} onClick={onClick} style={{textDecoration: 'none'}}>
      {children}
    </Button>
  );
};

const Pagination = ({
  spanPagination,
  canPreviousPage,
  pageIndex,
  visiblePages,
  canNextPage,
  pageSize,
  pageSizeOptions,
  pageSwitch,
  pageSizeSwitch,
  showResultsCount = false,
  resultsCount = 0,
  showPaginationLabel = true, //Showing 1 - 10 of 98
}) => {
  return (
    <div className="d-flex p-bottom pagination">
      {showPaginationLabel && <span className="span-pagination">{spanPagination}</span>}
      <div className="page-number-border">
        <PageNum
          onClick={() => {
            if (canPreviousPage) {
              return pageSwitch(pageIndex - 1);
            }
          }}
          disabled={!canPreviousPage}
        >
          {'<'}
        </PageNum>
        {visiblePages.map((pageNo, index, array) => (
          <PageNum
            key={pageNo}
            active={pageNo - 1 === pageIndex}
            disabled={pageNo - 1 === pageIndex}
            onClick={() => pageSwitch(pageNo - 1)}
          >
            {array[index - 1] + 2 < pageNo ? `...${pageNo}` : pageNo}
          </PageNum>
        ))}
        <PageNum
          onClick={() => {
            if (canNextPage) {
              return pageSwitch(pageIndex + 1);
            }
          }}
          disabled={!canNextPage}
        >
          {'>'}
        </PageNum>
      </div>
      <Form>
        <Form.Control
          as="select"
          value={pageSize}
          className="ml-3"
          onChange={(e) => {
            pageSizeSwitch(Number(e.target.value));
            // Store in Local Storage
            //   storePageSize(tabName, e.target.value);
          }}
        >
          {pageSizeOptions.map((pageSizeOpt) => (
            <option key={pageSizeOpt} value={pageSizeOpt}>
              Show {pageSizeOpt}
            </option>
          ))}
        </Form.Control>
      </Form>
      {showResultsCount && (
        <div className="results-count">
          <b>{resultsCount}</b> Results
        </div>
      )}
    </div>
  );
};

export default Pagination;
