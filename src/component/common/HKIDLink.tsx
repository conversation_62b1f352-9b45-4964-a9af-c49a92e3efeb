import React, { SyntheticEvent } from 'react';
import { Button } from 'react-bootstrap';

const parentHKIDLink = (row: any, eventTracker: Function, roleConfig) => {
  const ButtonLink = ({ row, roleConfig }) => {
    const clickHandler = (event: SyntheticEvent) => {
      event.stopPropagation();
      if (row?.id) {
        eventTracker('crewHKIDLink', 'Routes to Crew Details via HKID');
        window.open(`/seafarer/details/${row?.id}/general`, '_blank');
      }
    };
    if (roleConfig?.seafarer.view.general && row?.hkid) {
      return (
        <Button className="button-link" variant="link" onClick={clickHandler}>
          {row.hkid}
        </Button>
      );
    } else if (row?.hkid) {
      return <div>{row.hkid}</div>;
    } else {
      return '---';
    }
  };

  return <ButtonLink row={row} roleConfig={roleConfig} />;
};

export default parentHKIDLink;
