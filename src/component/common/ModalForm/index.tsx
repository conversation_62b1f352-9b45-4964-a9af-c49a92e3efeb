import React, { useState, useCallback, ReactElement, ReactNode, useEffect } from 'react';
import { FormikConfig, FormikValues, useFormik } from 'formik';
import StandardModal, { ModalProps } from '@src/component/common/Modal';
import { Form } from '@src/component/common/Form';
import useLazyRequest from '@src/hooks/useLazyRequest';

interface Props<Values extends FormikValues = FormikValues> {
  onClose?: () => void;
  onSubmitFinish: (values: Values) => Promise<any>;
  trigger: ReactElement;
  modalProps: Partial<ModalProps>;
  formConfig: Omit<FormikConfig<Values>, 'onSubmit'>;
  children: ReactNode;
}

const ModalForm = <Values extends FormikValues>({
  onClose,
  onSubmitFinish,
  trigger,
  modalProps,
  formConfig,
  children,
}: Props<Values>) => {
  const [visible, setVisible] = useState<boolean>(false);

  const triggerComponent = React.cloneElement(trigger, {
    onClick: () => setVisible(true),
  });

  const handleClose = useCallback(() => {
    if (onClose) onClose();
    setVisible(false);
  }, [onClose]);

  const [handleSubmit, { loading: isSubmitting }] = useLazyRequest(
    async (values: Values) => onSubmitFinish(values),
    {
      onComplete: handleClose,
    },
  );

  const formik = useFormik({
    ...formConfig,
    onSubmit: handleSubmit,
  });

  useEffect(() => {
    if (!visible) {
      formik.resetForm({});
    }
  }, [visible, formik]);

  const isSubmitValid = formik.isValid && formik.dirty && !isSubmitting;

  return (
    <>
      {triggerComponent}
      {visible && (
        <StandardModal
          visible={visible}
          onCancel={handleClose}
          size="lg"
          {...modalProps}
          okButtonProps={{
            disabled: !isSubmitValid,
          }}
          onOk={formik.handleSubmit}
          confirmLoading={isSubmitting}
        >
          <Form form={formik}>{children}</Form>
        </StandardModal>
      )}
    </>
  );
};

export default ModalForm;
