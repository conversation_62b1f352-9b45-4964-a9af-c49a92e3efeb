import React, { useMemo, useEffect, useCallback, useRef } from 'react';
import { useTable, useSortBy, usePagination, useFlexLayout, Row } from 'react-table';
import classnames from 'classnames';
import { useSticky } from 'react-table-sticky';
import { VirtualItem, useVirtualizer } from '@tanstack/react-virtual';
import Spinner from '@src/component/common/Spinner';
import styleGuide from '@src/styleGuide';
import './style.scss';
import { columnSortIconName } from '@src/util/view-utils';
import { type InfiniteScrollTableProps } from './types';

const { Icon } = styleGuide;

const InfiniteScrollTable: React.FC<InfiniteScrollTableProps> = React.memo(function Table({
  className,
  columns,
  data,
  fetchData,
  hasMoreData,
  init_sort = [],
  height,
  loading,
  emptyContent,
  endMessage,
  sticky = true,
  noDataText,
}) {
  const defaultColumn = useMemo(
    () => ({
      minWidth: 120,
      width: 120,
    }),
    [],
  );
  const tableContainerRef = React.useRef<HTMLTableElement>(null);
  const table = useTable(
    {
      columns,
      data,
      defaultColumn,
      initialState: { sortBy: init_sort },
      manualPagination: true,
      manualSortBy: true,
      autoResetPage: false,
      autoResetSortBy: false,
    },
    useSortBy,
    usePagination,
    useFlexLayout,
    useSticky,
  );
  const { getTableProps, getTableBodyProps, headerGroups, prepareRow, rows, state } = table;
  const { sortBy } = state;
  const loadingRef = useRef(false);
  const rowVirtualizer = useVirtualizer({
    count: rows.length,
    estimateSize: () => 38,
    getScrollElement: () => tableContainerRef.current,
    overscan: 5,
  });

  const handleScroll = useCallback(
    (e) => {
      const { scrollTop, scrollHeight, clientHeight } = e.target;
      const isBottom = scrollHeight - scrollTop <= clientHeight + 5; // Allow a small tolerance

      if (isBottom && hasMoreData && !loading && !loadingRef.current) {
        /* 
          Directly updating ref here to avoid re-render lag or
          missed updates during rapid scroll events. 
        */
        loadingRef.current = true;
        fetchData({ sortBy, fetchMore: true });
      }
    },
    [fetchData, hasMoreData, loading, sortBy],
  );

  /* Using useEffect to update loadingRef ensures it always holds the latest value without causing re-renders. */
  useEffect(() => {
    loadingRef.current = loading;
  }, [loading]);

  useEffect(() => {
    fetchData({ sortBy });
  }, [sortBy, fetchData]);

  // Load more data until the content height exceeds the container height
  useEffect(() => {
    const checkAndLoadMoreData = () => {
      if (tableContainerRef.current) {
        const { scrollHeight, clientHeight } = tableContainerRef.current;
        if (scrollHeight <= clientHeight && hasMoreData && !loading && data?.length > 0) {
          fetchData({ sortBy, fetchMore: true });
        }
      }
    };

    checkAndLoadMoreData();
  }, [data, hasMoreData, loading, sortBy]);

  useEffect(() => {
    // scroll to bottom when loading next page
    if (loading && tableContainerRef.current && data?.length > 0) {
      tableContainerRef.current.scrollTop = tableContainerRef.current.scrollHeight;
    }
  }, [loading, data?.length]);

  const renderHeader = () => {
    return (
      <thead className="header">
        {headerGroups.map((headerGroup, index) => (
          <tr {...headerGroup.getHeaderGroupProps()} id={`top-header-${index}`} className="tr">
            {headerGroup.headers.map((column, index2) => {
              const thProps = column.getHeaderProps(column.getSortByToggleProps());
              return (
                <th {...thProps} className="th">
                  {column.render('Header')}
                  <span>
                    {column.canSort && (
                      <Icon icon={columnSortIconName(column)} size={20} className="default" />
                    )}
                  </span>
                </th>
              );
            })}
          </tr>
        ))}
      </thead>
    );
  };

  const renderRow = (row: Row<Object>, virtualRow: VirtualItem<Element>) => {
    return (
      <tr
        data-index={virtualRow.index}
        ref={(node) => rowVirtualizer.measureElement(node)}
        {...row.getRowProps()}
        className="tr"
        style={{
          display: 'flex',
          position: 'absolute',
          transform: `translateY(${virtualRow.start}px)`, // rowY changes on scroll
          width: '100%',
        }}
      >
        {row.cells.map((cell) => {
          const tdProps = cell.getCellProps();
          return (
            <td
              {...tdProps}
              className="td"
              title={typeof cell.value === 'string' ? cell.value : undefined}
            >
              {cell.render('Cell')}
            </td>
          );
        })}
      </tr>
    );
  };

  const renderBody = () => {
    return rowVirtualizer.getVirtualItems().map((virtualRow) => {
      const row = rows[virtualRow.index];
      prepareRow(row);
      return renderRow(row, virtualRow);
    });
  };

  const renderEmpty = (noDataText) => {
    if (emptyContent)
      return (
        <tr>
          <td colSpan={columns?.length}>{endMessage}</td>
        </tr>
      );

    return (
      <tr>
        <td colSpan={columns?.length} className="no-result-found">
          <Icon icon="alert" className="alert-icon-no-search mt-5" />
          <div>{noDataText ?? <b>No result matches your criteria</b>}</div>
        </td>
      </tr>
    );
  };

  const renderEndMessage = () => {
    const { scrollHeight, clientHeight } = tableContainerRef.current;
    if (scrollHeight <= clientHeight) return null;
    const message = endMessage ?? 'No More Data';
    return (
      <tr
        style={{
          display: 'flex',
          position: 'absolute',
          top: `${rowVirtualizer.getTotalSize()}px`, // Position after all rows
          width: '100%',
        }}
      >
        <td style={{ width: '100%' }} colSpan={columns?.length}>
          <p className="row-end-message">{message}</p>
        </td>
      </tr>
    );
  };

  const isEmpty = !(loading || data.length > 0);
  const showEndMessage = !isEmpty && !loading && !hasMoreData;
  return (
    <div
      style={{ height: height ?? '100%', overflow: 'auto' }}
      ref={tableContainerRef}
      onScroll={handleScroll}
    >
      <table {...getTableProps()} className={classnames(className, 'paris2-table', { sticky })}>
        {renderHeader()}
        <tbody
          className="body"
          style={{
            height: `${rowVirtualizer.getTotalSize()}px`, // tells scrollbar how big the table is
            position: 'relative', // needed for absolute positioning of rows
          }}
          {...getTableBodyProps()}
        >
          {data?.length > 0 && renderBody()}
          {isEmpty && renderEmpty(noDataText)}
          {showEndMessage && renderEndMessage()}
        </tbody>
      </table>
      {loading && <Spinner alignClass={classnames('load-spinner', { 'm-2': data?.length > 0 })} />}
    </div>
  );
});
export default InfiniteScrollTable;
