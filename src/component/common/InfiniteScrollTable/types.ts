import React, { ReactNode } from 'react';
import { Column } from 'react-table';
  
export interface InfiniteScrollTableProps {
  style?: React.CSSProperties,
  className?: string;
  columns: Column[];
  data: any[];
  fetchData: (params: { sortBy: any, fetchMore?: boolean }) => void;
  loading: boolean;
  init_sort?: any[];
  tableRef?: React.Ref<HTMLDivElement>;
  emptyContent?: ReactNode;
  endMessage?: ReactNode;
  hasMoreData?: boolean;
  height?: number | string;
  sticky?: boolean;
}
