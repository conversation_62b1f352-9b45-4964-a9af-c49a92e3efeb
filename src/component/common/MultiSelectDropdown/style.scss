.paris2-multi-select-dropdown {
    margin: 0;
    padding: 0;
    .rbt-menu {
        z-index: 1001;
    }
    .dropdown-menu {
        overflow: hidden !important;
    }
    .rbt-token {
        background-color: #eee !important;
        color: #495057
    }
    .typeable-dropdown-icon-style {
        position: absolute;
        right: 2px;
        top: 5px;
    }

    input[type=checkbox] {
        accent-color: var(--primary);
    }
    .form-check {
        cursor: pointer;

        label {
            font-size: 14px;
        }
    }
    .form-check-input {
        width: 1.2rem;
        height: 1.2rem;
        margin-left: -1.75rem;
        margin-top: .2rem;
        cursor: pointer;
    }

    .menu-item-footer {
        padding: .25rem 1.5rem;
        border-top: 1px solid #e9ecef;
    }
    .angle-icon {
      position: absolute;
      right: 6px;
      padding-left: 2px;
      padding-right: 2px;
      top: 10px;
      color: #495057;
      cursor: pointer;
    }
}
.menu-item-wrapper {
    max-height: 245px,
}