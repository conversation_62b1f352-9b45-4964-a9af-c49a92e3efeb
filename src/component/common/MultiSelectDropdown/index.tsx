/* eslint-disable react/display-name */
import React, { Fragment, useRef, useState } from 'react';
import { Menu, MenuItem, Typeahead, TypeaheadRef } from 'react-bootstrap-typeahead';
import PropTypes from 'prop-types';
import _ from 'lodash';
import { Button, Form } from 'react-bootstrap';
import './style.scss';
import { ChevronDown, ChevronUp } from 'react-bootstrap-icons';

interface MultiSelectDropdownProps {
  options: Option[];
  name: string;
  onInputChange: (e: any) => void;
  selected: any;
  disabled: boolean;
  isInvalid: boolean;
  placeholder: string;
  testID: string;
  multiple: boolean;
  loading?: boolean;
}

export interface Option {
  label: string | number;
  value: string | number;
}

const MultiSelectDropdown = ({
  options,
  name,
  onInputChange,
  selected,
  disabled,
  isInvalid,
  testID,
  loading = false,
  placeholder = 'Please select',
}: MultiSelectDropdownProps) => {
  const multiple = true;
  const typeaheadRef = useRef<TypeaheadRef>(null);
  const [showMenu, setShowMenu] = useState(false);

  const onChange = (selectedValues: Option[]) => {
      const values = selectedValues.map((item) => item.value);
      const duplicateValue = values.find((item, index) => values.indexOf(item) !== index);
      const newSelectedValues = duplicateValue
        ? selectedValues.filter((item) => item.value !== duplicateValue)
        : selectedValues;

      const results = { target: { name, value: newSelectedValues } };
      onInputChange(results);
  };


  const getOptionById = (id): Option => {
    return (options ?? []).find((item) => {
      return item.value === id;
    }) as Option;
  };

  const selectedValue = Array.isArray(selected) ? selected : [selected];
  const selectedOptions = selectedValue.filter(Boolean).map((item) => getOptionById(item));

  const validateArgument = () => {
    if (options?.length && options === undefined) {
      throw Error('options cannot be undefined');
    }

    if (selectedValue !== undefined && selected === undefined) {
      throw Error('selected cannot be undefined');
    }
  };

  validateArgument();

  const handleSelectAll = () => {
    onInputChange({ target: { value: options }})
  }
  /**
   * Custom Dropdown Input/Menu design
   */
  const typeHeadProps = {
    className: 'multi-dropdown',
    renderMenu: (results, menuProps, state) => {
      const items = _.uniqBy([...(state?.selected ?? []), ...results], 'value').map(
        (option, index) => {
          const menuItemProps = {
            option,
          };
          if (option?.paginationOption) {
            return (
              <Fragment key="pagination-item">
                <Menu.Divider />
                <MenuItem
                  {...menuItemProps}
                  className="rbt-menu-pagination-option"
                  label={'Display additional results...'}
                >
                  {'Display additional results...'}
                </MenuItem>
              </Fragment>
            );
          }
          return (
            <MenuItem key={`${option?.value}`} checked={true} {...menuItemProps} position={index}>
              <Form.Check
                type="checkbox"
                label={option?.label}
                data-testid="fml-flag-change-confirm-ensure"
                defaultChecked={state.selected.includes(option)}
              />
            </MenuItem>
          );
        },
      );
      return (
        <Menu {...menuProps}>
          <div className="d-flex flex-column">
            <div
              className="menu-item-wrapper rbt-menu flex-grow-1 overflow-auto"
            >
              {items}
            </div>
            <div className="menu-item-footer">
              <Button variant="link" onClick={handleSelectAll}>Select All</Button>
            </div>
          </div>
        </Menu>
      );
    },
  };
  const renderRightIcon = () => {
    if (loading) return;
    const RightIcon = showMenu ? ChevronUp : ChevronDown;
    return (
      <RightIcon
        className="angle-icon"
        size={20}
        onClick={() => {
          typeaheadRef?.current?.toggleMenu();
          if (!typeaheadRef?.current?.isMenuShown) {
            typeaheadRef?.current?.focus();
          }
        }}
      />
    )
  }
  return (
    <div className="paris2-multi-select-dropdown col position-relative">
      <Typeahead
        id={name}
        key={name}
        open={showMenu}
        onMenuToggle={(e) => console.log('menu toggle', e)}
        inputProps={{
          'data-testid': testID,
        }}
        ref={typeaheadRef}
        onChange={onChange}
        options={options}
        placeholder={placeholder}
        selected={selectedOptions}
        disabled={disabled}
        isInvalid={isInvalid}
        multiple={multiple}
        isLoading={loading}
        onBlur={() => setShowMenu(false)}
        onFocus={() => setShowMenu(true)}
        {...typeHeadProps}
      />
      {renderRightIcon()}
    </div>
  );
};
MultiSelectDropdown.propTypes = {
  options: PropTypes.array,
  name: PropTypes.string,
  onInputChange: PropTypes.func,
  selectedValue: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  disabled: PropTypes.bool,
  isInvalid: PropTypes.bool,
  placeholder: PropTypes.string,
  testID: PropTypes.string,
  multiple: PropTypes.bool,
};

export default MultiSelectDropdown;
