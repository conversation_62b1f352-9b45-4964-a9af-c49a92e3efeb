import React from 'react';
import { Icon } from '../../styleGuide';

const SortIcon = (props) => {
  if (props.column) {
    const { column } = props;
    if (column.canSort) {
      if (column.isSorted) {
        if (column.isSortedDesc) {
          return <Icon icon="sort-ascending" size={20} className="default" />;
        }

        return <Icon icon="sort-descending" size={20} className="default" />;
      }
      return <Icon icon="sort-off" size={20} className="default" />;
    }
  }
  return null;
};

export { SortIcon };
