// eslint-disable-next-line no-unused-vars
import { isObject } from 'lodash';
import { TableBodyProp } from '../../types/tableUtils';
import React from 'react';
import { Table } from 'react-bootstrap';

export const TableBody = ({ data, labelClass, valueClass }: TableBodyProp) => {
  return (
    <Table hover className="mb-0 mb-md-5 border-bottom-table">
      <tbody>
        {data.map((i) => {
          return i?.single ? (
            <tr key={isObject(i.label) ? JSON.stringify(i.label) : i.label.toString()}>
              <td colSpan={2}>
                <div className="row px-0 font-weight-bold form-label">{i.label}</div>
                <div className="row px-0 form-label">{i.value}</div>
              </td>
            </tr>
          ) : (
            !i?.hide && (
              <tr
                key={isObject(i.label) ? JSON.stringify(i.label) : i.label.toString()}
                className="row px-3"
              >
                <td className={`${labelClass ?? 'col-6'} font-weight-bold form-label px-0`}>
                  {i.label}
                </td>
              <td className={`${valueClass ?? 'col-6'} form-label px-0`}>{i.value}</td>
              </tr>
            )
          );
        })}
      </tbody>
    </Table>
  );
};

export const PdfFormatTableBody = ({ data }: TableBodyProp) => {
  return (
    <Table hover className={'mb-0 border-bottom-table'}>
      <tbody>
        {data.map((i) => {
          return i?.single ? (
            <tr key={isObject(i.label) ? JSON.stringify(i.label) : i.label.toString()}>
              <td colSpan={2} className="p-1">
                <div className="row px-1 font-weight-bold pdf-form-label">{i.label}</div>
                <div className="row px-1 pdf-form-value">{i.value}</div>
              </td>
            </tr>
          ) : (
            !i?.hide && (
              <tr key={isObject(i.label) ? JSON.stringify(i.label) : i.label.toString()}>
                <td className="col-6 px-0 font-weight-bold pdf-form-label p-1">{i.label}</td>
                <td className="col-6 px-0 pdf-form-value p-1">{i.value}</td>
              </tr>
            )
          );
        })}
      </tbody>
    </Table>
  );
};

export const MainTitle = ({ title = '', button }: { title: string; button?: React.ReactNode }) => {
  return (
    <div className="main-table-title-with-bordertop mb-3">
      {title}
      {button}
    </div>
  );
};

export const SubTitle = ({ title = '' }: { title: string }) => {
  return <div className="sub-table-title-with-bordertop">{title}</div>;
};
