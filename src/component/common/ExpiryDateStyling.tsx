/* eslint-disable react/display-name */
/* eslint-disable react/prop-types */
import React from 'react';
import { DateTime, Duration } from 'luxon';
import { dateAsString, stringAsDate, isDateValid } from '../../model/utils';
import './common.scss';

const getErrorClass = (value) => {
  const now = DateTime.now();
  const dateOfExpiry = DateTime.fromISO(value);
  if (now.toMillis() > dateOfExpiry.toMillis()) {
    return 'font-red';
  }
  if (now.plus(Duration.fromObject({ days: 30 })) > dateOfExpiry.toMillis()) {
    return 'font-yellow';
  }
  if (now.plus(Duration.fromObject({ days: 60 })) > dateOfExpiry.toMillis()) {
    return 'font-green';
  }
  if (now.plus(Duration.fromObject({ months: 6 })) > dateOfExpiry.toMillis()) {
    return 'font-orange';
  }
  if (now.plus(Duration.fromObject({ years: 1 })) > dateOfExpiry.toMillis()) {
    return 'font-pink';
  }
  return '';
};

const ExpiryDateStyling = ({ value }) => {
  if (!isDateValid(value)) {
    return null;
  }
  const colorClass = getErrorClass(value) + ' font-bold';
  const dt = dateAsString(stringAsDate(value));
  return <span className={colorClass}>{dt}</span>;
};

export { ExpiryDateStyling };
