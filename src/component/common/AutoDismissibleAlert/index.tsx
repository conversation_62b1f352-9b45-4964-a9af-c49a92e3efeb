import React, { useState, useEffect, useImperativeHandle, forwardRef, Ref } from 'react';
import { Alert } from 'react-bootstrap';
import { Variant } from 'react-bootstrap/esm/types';
import './style.scss';

interface Props {
  message: string;
  variant?: Variant;
  dismissTime?: number;
  noAutoDismissOnDanger?: boolean;
  className?: string;
  onClose?: () => void;
  setAlertMessage: (message: { message: string; variant?: Variant }) => void;
}
interface AlertRef {
  hide: () => void;
}
const AutoDismissibleAlert = (
  {
    message,
    variant = 'success',
    className,
    dismissTime = 5000,
    noAutoDismissOnDanger,
    setAlertMessage,
    onClose,
  }: Props,
  ref: Ref<AlertRef>,
) => {
  const [show, setShow] = useState(true);

  const handleClose = () => {
    setAlertMessage?.({ message: '' });
    setShow(false);
    onClose?.();
  };

  useEffect(() => {
    if (noAutoDismissOnDanger && variant === 'danger') {
      return;
    }
    const timer = setTimeout(handleClose, dismissTime);

    return () => clearTimeout(timer);
  }, [dismissTime]);
  useImperativeHandle(
    ref,
    () => {
      return {
        hide: handleClose,
      };
    },
    [],
  );
  return (
    <Alert
      show={show}
      className={className}
      style={{ paddingBottom: '0.75rem' }}
      variant={variant}
      onClose={handleClose}
      dismissible
    >
      {message}
    </Alert>
  );
};

export default forwardRef(AutoDismissibleAlert);
