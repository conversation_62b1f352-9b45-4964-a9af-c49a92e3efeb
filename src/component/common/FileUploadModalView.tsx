import React, { useEffect, useState, useCallback } from 'react';
import Modal from 'react-bootstrap/Modal';
import { Button, Container, Form, Row, Col } from 'react-bootstrap';
import { useDropzone } from 'react-dropzone';
import styleGuide from '../../styleGuide';
const { Icon } = styleGuide;
import { v4 as uuid } from 'uuid';
import ImageController from '../../controller/image-upload-controller';
import { COMMON_MESSAGES } from '../../constants/common-labels-and-messages';

const imageController = new ImageController();

const fileTypes = [
  { type: 'case_report', title: 'Case Report' },
  { type: 'self_declaration', title: 'Self Declaration' },
  { type: 'police_declaration', title: 'Police Declaration' },
  { type: 'other_screening_document', title: 'Other Screening Document' },
];

const SelectedFilesComponent = (props) => {
  if ((props.files ?? []).length === 0) {
    return null;
  }
  return (
    <Container className="selected-files-wrapper">
      <div className="title">Selected Files</div>
      <Row xs={1} md={2}>
        {(props.files ?? []).map((file, index) => {
          return (
            <SelectedFileComponent
              key={file.localId || file}
              file={file}
              index={index}
              onFileChange={props.onFileChange}
              onRemoveFile={props.onRemoveFile.bind(this, file, index)}
              roleConfig={props.roleConfig}
            />
          );
        })}
      </Row>
    </Container>
  );
};

const SelectedFileComponent = (props) => {
  const onInputChange = (event) => {
    const item = props.file;
    item[event.target.name] = event.target.value;
    props.onFileChange(item);
  };

  const getAllowedDocumentTypes = () => {
    const caseReportTypeBasedOnRole = (fileTypes) =>
      !props.roleConfig.seafarer.screening.caseReportUpload
        ? fileTypes.filter((fileType) => fileType.type !== 'case_report')
        : fileTypes;

    const docTypesBasedOnRole = (fileTypes) =>
      !props.roleConfig.seafarer.screening.documentsUpload
        ? fileTypes.filter(
            (fileType) =>
              !['self_declaration', 'police_declaration', 'other_screening_document'].includes(
                fileType.type,
              ),
          )
        : fileTypes;

    return docTypesBasedOnRole(caseReportTypeBasedOnRole(fileTypes));
  };
  return (
    <Col>
      <Row>
        <Col md={10}>
          <Form.Control
            as="select"
            name="document_type"
            onChange={onInputChange}
            value={props.file.document_type ?? ''}
            isInvalid={!props.file.document_type}
          >
            <option value="" disabled>
              Please select
            </option>
            {getAllowedDocumentTypes().map((item) => {
              return (
                <option key={item.type} value={item.type}>
                  {item.title}
                </option>
              );
            })}
          </Form.Control>
        </Col>
      </Row>
      <Row>
        <Col md={12}>
          <div className="selected-file-row">
            <div className="selected-file-row__selected-file-wrapper">
              <div className="selected-file-link">{props.file.name}</div>
            </div>
            <Icon icon="remove" size={30} className="remove_icon" onClick={props.onRemoveFile} />
          </div>
          {props.file.size > 5000000 && (
            <Form.Control.Feedback type="invalid" className="document-copy-feedback">
              {COMMON_MESSAGES.MESSAGE_FILE_SIZE_EXCEED}
            </Form.Control.Feedback>
          )}
          {!['image/png', 'image/jpeg', 'application/pdf'].includes(props.file.type) && (
            <Form.Control.Feedback type="invalid" className="document-copy-feedback">
              {COMMON_MESSAGES.MESSAGE_FILE_EXTENSION_NOT_SUPPORT}
            </Form.Control.Feedback>
          )}
        </Col>
      </Row>
    </Col>
  );
};

const UploadedFilesComponent = (props) => {
  const files = props.files ?? [];

  if (files.length === 0) {
    return null;
  }

  return (
    <Container className="uploaded-files-wrapper">
      <Row xs={1} md={2}>
        {files.map((file, index) => {
          return <UploadedFileComponent key={file.id} file={file} index={index} />;
        })}
      </Row>
    </Container>
  );
};

const UploadedFileComponent = (props) => {
  const type = props.file.document_type;
  let title = type;

  fileTypes.forEach((item) => {
    if (item.type === type) {
      title = item.title;
    }
  });

  const downloadFile = async () => {
    try {
      await imageController.downloadFile('screening', props.file, props.file.name);
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <Col>
      <Row>
        <Col md={10}>
          <div className="uploaded-file-label">{title}</div>
        </Col>
      </Row>
      <Row>
        <Col md={12}>
          <div className="uploaded-file-row">
            <div className="uploaded-file-row__uploaded-file-wrapper">
              <Button variant="link" className="uploaded-file-link" onClick={downloadFile}>
                {props.file.name}
              </Button>
            </div>
          </div>
        </Col>
      </Row>
    </Col>
  );
};

const SelectMessageView = () => {
  return (
    <div className="select-message-view">
      <div className="title">Click or drop files here to upload</div>
      <div className="message">
        <div>(Support format: pdf, jpg, jpeg, png)</div>
        <div>Maximum 10 files. Each file cannot be bigger than 5MB.</div>
      </div>
    </div>
  );
};

const FileUploadModalView = (props) => {
  const maxFiles = 10;
  const [files, setFiles] = useState([]);

  useEffect(() => {
    setFiles(props.selectedFiles ?? []);
  }, []);

  const onDrop = useCallback((acceptedFiles) => {
    const newFiles = (acceptedFiles ?? []).map((file) => {
      file.localId = uuid();
      return file;
    });
    const newArray = [...files, ...newFiles];

    if (newArray.length >= maxFiles) {
      newArray.splice(maxFiles, newArray.length - maxFiles);
    }

    setFiles(newArray);
  }, []);

  const { getRootProps, getInputProps } = useDropzone({
    onDrop: onDrop,
    accept: '.jpg,.jpeg,.png,.pdf',
  });

  const inputProps = getInputProps();

  const onFileChange = (file) => {
    const items = [...files];
    items.forEach((element, index) => {
      if (element.localId === file.localId && file.localId !== '') {
        items[index] = file;
      }
    });

    setFiles(items);
  };

  const onRemoveFile = (file, index) => {
    const items = [...files];
    items.splice(index, 1);
    setFiles(items);
  };

  const onClose = () => {
    props.onSelectedFilesChange(files);
    props.onClose();
  };

  return (
    <Modal
      className="information-modal"
      size="lg"
      {...props}
      onHide={onClose}
      aria-labelledby="contained-modal-title-vcenter"
      centered
    >
      <Modal.Header closeButton>
        <Modal.Title id="contained-modal-title-vcenter">File Upload</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        {files.length >= maxFiles ? null : (
          <div {...getRootProps()}>
            <input {...inputProps} />
            <div className="file-upload-area">
              <SelectMessageView />
            </div>
          </div>
        )}
        <SelectedFilesComponent
          files={files}
          onFileChange={onFileChange}
          onRemoveFile={onRemoveFile}
          roleConfig={props.roleConfig}
        />
      </Modal.Body>
      <Modal.Footer>
        <Button className="pl-4 pr-4" onClick={onClose}>
          Close
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

const UploadedFilesModalView = (props) => {
  return (
    <Modal
      className="information-modal"
      size="lg"
      {...props}
      onHide={props.onClose}
      aria-labelledby="contained-modal-title-vcenter"
      centered
    >
      <Modal.Header closeButton>
        <Modal.Title id="contained-modal-title-vcenter">Uploaded Files</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <UploadedFilesComponent files={props.uploadedFiles} />
      </Modal.Body>
      <Modal.Footer>
        <Button className="pl-4 pr-4" onClick={props.onClose}>
          Close
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default {
  FileUploadModalView,
  SelectedFilesComponent,
  UploadedFilesModalView,
  UploadedFilesComponent,
};
