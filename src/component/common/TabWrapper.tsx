import React, { useEffect } from 'react';
import { Nav } from 'react-bootstrap';
import './common.scss';

export function TabWrapper({ variant = 'pills', ...props }) {
  const { step, activeTab, setActiveTab } = props;
  useEffect(() => {
    if (step !== activeTab) {
      setActiveTab(step || '');
    }
  }, [step, activeTab, setActiveTab]);

  return (
    <Nav
      // eslint-disable-next-line react/jsx-props-no-spreading
      {...(props.activeKey ? { activeKey: props.activeKey } : {})}
      variant={variant}
      onSelect={(k) => props.handleTabSelect(k)}
      className={`${!props?.isFixedWidth ? 'nav-justified' : ''} nav-border`}
    >
      {!props?.isFixedWidth && <hr />}
      {props?.data?.map((data) => {
        return (
          <Nav.Item key={data.eventKey}>
            <Nav.Link eventKey={data.eventKey} href="#" data-testid={data.eventKey}>
              {data.tabName}
            </Nav.Link>
          </Nav.Item>
        );
      })}
    </Nav>
  );
}

export default TabWrapper;
