import React from 'react';
import Button from 'react-bootstrap/Button';
import ButtonGroup from 'react-bootstrap/ButtonGroup';

const GroupButtonSelector = ({ buttonLabels, onSelect, selected = '', count }) => {
  return (
    <ButtonGroup>
      {buttonLabels?.map((label: string) => {
        return (
          <Button
            key={label}
            variant={selected === label ? 'primary' : 'outline-primary'}
            onClick={() => onSelect(label)}
          >
            {label}
            {Number.isInteger(count) && selected === label ? (
              <span className="oval-total-count">{count}</span>
            ) : (
              ''
            )}
          </Button>
        );
      })}
    </ButtonGroup>
  );
};

export default GroupButtonSelector;
