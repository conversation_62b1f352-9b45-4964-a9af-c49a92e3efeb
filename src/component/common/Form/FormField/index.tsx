import { Col, Form, Row } from 'react-bootstrap';
import React, { ReactNode, PropsWithChildren } from 'react';
import { useField } from 'formik';

interface FormFieldProps {
  label: ReactNode;
  children: ReactNode;
  labelProps?: Object | null;
  as?: ReactNode;
  name: string;
  valuePropName?: string;
  getEventFromOnChange?: (value: any) => any;
  trigger?: string;
  noStyle?: boolean;
  help?: string | null;
  getValueFromEvent?: (...args: any[]) => any;
  getValueProps?: (value: any) => any;
  layout?: 'inline' | 'horizontal' | 'vertical';
  md?: string | { span: number; offset: number };
  requiredMark?: boolean;
  labelBold?: boolean;
}

const FormField = ({
  name,
  label,
  children,
  labelProps = {},
  layout = 'vertical',
  valuePropName = 'value',
  trigger = 'onChange',
  noStyle = false,
  help = null,
  getValueFromEvent,
  getValueProps,
  md,
  requiredMark = false,
  labelBold = false,
}: PropsWithChildren<FormFieldProps>) => {
  const asComponent = layout === 'vertical' ? Col : Row;

  const labelStr = `${label}${requiredMark ? '*' : ''}`;
  const labelContent = labelBold ? <b>{labelStr}</b> : labelStr;

  const [field, meta] = useField({
    name,
  });
  const hasError = meta.touched && meta.error;
  const handleTrigger = (value) => {
    const fieldValue = getValueFromEvent?.(value) ?? value;
    field.onChange(name)(fieldValue);
  };
  const component = React.cloneElement(children, {
    id: name,
    [valuePropName]: getValueProps?.(field.value) ?? field.value,
    [trigger]: handleTrigger,
    onBlur: field.onBlur,
    name: field.name,
    'data-testid': name,
    isInvalid: hasError,
  });

  if (noStyle) {
    return component;
  }

  return (
    <Form.Group as={asComponent} md={md}>
      <Form.Label htmlFor={name} {...labelProps}>
        {labelContent}
      </Form.Label>
      {component}
      {hasError && (
        <Form.Control.Feedback type="invalid" className="set-display-block">
          {meta.error}
        </Form.Control.Feedback>
      )}
      {!hasError && !!help && (
        <Form.Text id={name} muted>
          {help}
        </Form.Text>
      )}
    </Form.Group>
  );
};

export default FormField;
