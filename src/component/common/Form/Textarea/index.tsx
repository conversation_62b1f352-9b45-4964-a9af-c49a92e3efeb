import React, { TextareaHTMLAttributes } from 'react';
import { Form, FormControlProps } from 'react-bootstrap';

interface TextareaProps extends FormControlProps {
  maxLength?: number;
  showCount?: boolean; // Indicates whether to display the character count
}
const Textarea: React.FC<TextareaProps & TextareaHTMLAttributes<HTMLTextAreaElement>> = ({
  value = '',
  maxLength = null,
  showCount = false,
  ...rest
}) => {
  const countStr = maxLength ? `${value.length} / ${maxLength}` : `${value.length}`;

  return (
    <>
      <Form.Control as="textarea" rows={4} value={value} maxLength={maxLength} {...rest} />
      {showCount && (
        <Form.Text className="text-right" muted>
          {countStr}
        </Form.Text>
      )}
    </>
  );
};

export default Textarea;
