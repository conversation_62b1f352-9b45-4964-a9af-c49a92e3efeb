import React, { forwardRef, useEffect, useState } from 'react';
import { Search } from 'react-bootstrap-icons';
import cx from 'classnames';
import './style.scss';
import { Form, FormControlProps } from 'react-bootstrap';

const SearchInput = (
  { className, onBlur, onFocus, onChange, value, ...restProps }: FormControlProps,
  ref,
) => {
  const [focused, setFocused] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const handleBlur = (e) => {
    setFocused(false);
    onBlur?.(e);
  };
  const handleFocus = (e) => {
    setFocused(true);
    onFocus?.(e);
  };
  const handleChange = (e) => {
    setInputValue(e.target.value);
    onChange?.(e);
  };
  useEffect(() => {
    setInputValue(value);
  }, [value]);
  const showSearch = !focused && !inputValue;
  return (
    <div className="search-input-wrapper">
      <Search className={`search-icon ${!showSearch ? 'focused' : ''}`} />
      <Form.Control
        ref={ref}
        className={cx(className, showSearch ? 'form-input' : 'form-input-focused')}
        onFocus={handleFocus}
        onBlur={handleBlur}
        onChange={handleChange}
        value={value}
        {...restProps}
      />
    </div>
  );
};

export default forwardRef(SearchInput);
