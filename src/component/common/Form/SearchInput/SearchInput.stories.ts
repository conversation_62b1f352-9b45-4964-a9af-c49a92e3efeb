import type { Meta, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';
import SearchInput from '.';

const meta = {
  title: 'Example/SearchInput',
  component: SearchInput,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    backgroundColor: { control: 'color' },
  },
  args: { onClick: fn() },
} satisfies Meta<typeof SearchInput>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Search: Story = {
  args: {
    value: null,
    options: [],
    labelKey: 'value',
    placeholder: 'Search Vessel',
    loading: true,
  },
};
