import React from 'react';
import { Formik, FormikConfig, FormikProvider, FormikValues } from 'formik';
import { Form } from 'react-bootstrap';

interface FormProps<Values extends FormikValues = FormikValues> {
  form: FormikConfig<Values>;
  children: React.ReactNode;
}

function StandardForm<Values extends FormikValues = FormikValues, ExtraProps = {}>(
  props: (FormikConfig<Values> | FormProps<Values>) & ExtraProps,
) {
  const { children, form, ...restProps } = props;

  const formikBag = 'form' in props ? form : restProps;

  const formComponent = <Form>{children}</Form>;

  // Render the form with FormikProvider if a form prop is passed, otherwise use Formik to initialize
  return 'form' in props ? (
    <FormikProvider value={formikBag}>{formComponent}</FormikProvider>
  ) : (
    <Formik {...formikBag}>{formComponent}</Formik>
  );
}

export default StandardForm;
