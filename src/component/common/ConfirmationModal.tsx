import React from 'react';
import { But<PERSON>, Modal } from 'react-bootstrap';
import './styles/confirmation-modal.scss';

const ConfirmationModal = ({ isVisible, onClose, onConfirm, title, isSubmitting = false }) => (
  <Modal show={isVisible} dialogClassName="confirmation-modal" centered scrollable={false}>
    <Modal.Header>
      <Modal.Title>{title}</Modal.Title>
    </Modal.Header>
    <Modal.Footer>
      <Button variant="primary" onClick={onClose}>
        Close
      </Button>
      {onConfirm && (
        <Button variant="secondary" onClick={onConfirm} disabled={isSubmitting}>
          Confirm
        </Button>
      )}
    </Modal.Footer>
  </Modal>
);

export { ConfirmationModal };
