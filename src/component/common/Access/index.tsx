import UserRoleController from '@src/controller/user-role-controller';
import React, { createContext, useContext, useMemo, ReactNode } from 'react';

type ConfigType = Awaited<ReturnType<UserRoleController['getConfig']>>;

type UserProfile = {
  username: string;
  firstName: string;
  lastName: string;
  email: string;
  emailVerified: true;
  attributes: {
    tc_nova_version: Array<string>;
    is_user_onboarded: Array<string>;
    user_name_hash: Array<string>;
    is_nova_onboarded: Array<string>;
  };
};
export interface AccessContextType {
  roleConfig: ConfigType;
  userProfile: UserProfile;
}

const AccessContext = createContext<AccessContextType | undefined>(undefined);

// AccessProvider component
export const AccessProvider: React.FC<{
  children: ReactNode;
  config: ConfigType;
  profile: UserProfile;
}> = ({ children, config, profile }) => {
  const contextValue = useMemo(
    () => ({
      roleConfig: config,
      userProfile: profile,
    }),
    [config, profile],
  );

  return <AccessContext.Provider value={contextValue}>{children}</AccessContext.Provider>;
};

export const useAccess = (): AccessContextType => {
  const context = useContext(AccessContext);
  if (!context) {
    throw new Error('useAccess must be used within an AccessProvider');
  }
  return context;
};
