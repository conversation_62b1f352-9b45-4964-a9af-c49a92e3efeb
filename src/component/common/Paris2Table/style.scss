.paris2-table {
  font-size: 14px;
  padding: 0 !important;
  overflow: auto;

  [data-sticky-last-left-td] {
    border-right: 1px solid #ccc;
  }

  [data-sticky-first-right-td] {
    border-left: 1px solid #ccc;
  }

  .button-link {
    font-size: 14px;
  }

  .hr-1px {
    border: 1px solid #cccccc;
    margin-bottom: 0;
  }

  &.sticky {
    overflow-x: auto;
    .header {
      position: sticky;
      top: 0;
      z-index: 1;
      background: white;

      .tr {
        display: flex;
      }

      .th {
        position: sticky;
        top: 0;
        z-index: 2;
        background: white;
      }
    }

    .footer {
      width: fit-content;
      bottom: 0;
    }

    .body {
      position: relative;
    }
  }

  border-spacing: 0;
  width: 100%;
  padding-top: 20px;
  min-width: 0 !important;

  .th {
    background-color: #ffffff;
    margin: 0;
    padding: 0.75em 0.7em;
    border-bottom: 2px solid #1f4a70;
    font-weight: bold;
    user-select: none;
    display: flex;
    align-items: center;
  }

  .th:first-child {
    padding-left: 1.5em;
  }

  .body .tr>.td {
    border-bottom: 1px solid #cccccc;
  }

  .body>.tr:hover>.td {
    background-color: #eff8fa;
  }

  .td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
    text-align: left;
    background-color: #ffffff;
    margin: 0;
    padding: 0.4rem 0.7rem;
    display: flex;
    align-items: center;
    a {
      color: #1f4a70;
    }
  }

  .td:first-child {
    padding-left: 1.5em;
  }

  .load-overlay {
    height: 100%;
    width: 100%;
    z-index: 999;
    background-color: rgba(0, 0, 0, 0.2);
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .min-width-auto {
    min-width: auto !important;
  }

  .body-container {
    height: calc(100% - 40px); 
    overflow-y: auto;
    overflow-x: hidden;
  }
}