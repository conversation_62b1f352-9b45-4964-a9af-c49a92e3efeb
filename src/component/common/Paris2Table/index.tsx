import React, { useMemo, useEffect } from 'react';
import { Row } from 'react-bootstrap';
import classNames from 'classnames';
import { useTable, useSortBy, usePagination, useFlexLayout } from 'react-table';
import { useSticky } from 'react-table-sticky';
import Spinner from '@src/component/common/Spinner';
import { columnSortIconName } from '@src/util/view-utils';
import styleGuide from '@src/styleGuide';
import PaginationBar from '../../seafarerList/PaginationBar';
import './style.scss';
import { getVisiblePages } from '@src/util/pagination';

const pageSizeOptions = [10, 20, 50, 100, 300];
const { Icon } = styleGuide;

const defaultPageSize = 10;
const Table = React.memo(function TableMemo({
  className,
  columns,
  data,
  fetchData = () => {},
  pageCount: controlledPageCount,
  loading,
  init_sort = [],
  totalCount,
  tableRef,
  pagination = true,
  autoSort = false,
}) {
  const defaultColumn = useMemo(
    () => ({
      minWidth: 120,
      width: 120,
    }),
    [],
  );

  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    prepareRow,
    page,
    canPreviousPage,
    canNextPage,
    pageCount,
    gotoPage,
    setPageSize,
    state: { pageIndex, pageSize, sortBy },
  } = useTable(
    {
      columns,
      data,
      defaultColumn,
      initialState: { sortBy: init_sort },
      manualPagination: true,
      manualSortBy: !autoSort,
      autoResetPage: false,
      autoResetSortBy: autoSort,
      pageCount: controlledPageCount,
    },
    useSortBy,
    usePagination,
    useFlexLayout,
    useSticky,
  );

  const visiblePages = getVisiblePages(pageIndex, pageCount);

  const resetPage = (page_no = 0, page_size = 20) => {
    setPageSize(page_size);
    fetchData({ pageSize: page_size, sortBy, pageIndex: page_no });
    gotoPage(page_no);
  };

  useEffect(() => {
    resetPage();
  }, [resetPage, sortBy]);

  const pageSwitch = (page_no) => {
    fetchData({ pageSize, sortBy, pageIndex: page_no });
    gotoPage(page_no);
  };
  const pageSizeSwitch = (page_size) => {
    // Internally pageIndex gets recalibrated as follows
    const new_index = Math.floor((pageIndex * pageSize) / page_size);
    setPageSize(page_size);
    fetchData({ pageIndex: new_index, sortBy, pageSize: page_size });
  };
  const renderBody = () => {
    if (loading) {
      return <Spinner alignClass="load-spinner" />;
    }

    return page.map((row, index) => {
      prepareRow(row);
      const rowKey = `tr_${index}`;
      return (
        <div key={rowKey} {...row.getRowProps()} className="tr">
          {row.cells.map((cell) => {
            const tdProps = cell.getCellProps();
            const align = cell.column.align || 'start';
            return (
              <div
                key={tdProps.key}
                {...tdProps}
                className={classNames('td', cell.column.className, `justify-content-${align}`)}
                title={typeof cell.value === 'string' ? cell.value : undefined}
              >
                {cell.render('Cell')}
              </div>
            );
          })}
        </div>
      );
    });
  };
  const renderPagination = (showCount = true) => {
    if (!pagination) return null;
    return (
      <Row>
        <PaginationBar
          className="top-pagination-bar pagination-bar"
          pageSwitch={pageSwitch}
          pageSizeSwitch={pageSizeSwitch}
          canPreviousPage={canPreviousPage}
          canNextPage={canNextPage}
          visiblePages={visiblePages}
          pageSize={pageSize}
          pageIndex={pageIndex}
          pageSizeOptions={pageSizeOptions}
        />
        {showCount && (
          <div className="seafarer-list-count">
            <b>{totalCount}</b> Results
          </div>
        )}
      </Row>
    );
  };
  return (
    <>
      {renderPagination()}
      <div
        {...getTableProps()}
        className={
          className + ' paris2-table table sticky-table table-responsive crew-planner-table table-'
        }
        ref={tableRef}
      >
        <div className="header">
          {headerGroups.map((headerGroup, index) => (
            <div {...headerGroup.getHeaderGroupProps()} id={`top-header-${index}`} className="tr ">
              {headerGroup.headers.map((column, index2) => {
                const thProps = column.getHeaderProps(column.getSortByToggleProps());
                const align = column.align || 'start';
                return (
                  <div
                    {...thProps}
                    className={classNames('th', column.headerClassName, `justify-content-${align}`)}
                    id={`as-${index} + ${index2}`}
                  >
                    {column.render('Header')}
                    <span>
                      {column.canSort && (
                        <Icon icon={columnSortIconName(column)} size={20} className="default" />
                      )}
                    </span>
                  </div>
                );
              })}
            </div>
          ))}
        </div>
        <div {...getTableBodyProps()} className="body">
          {loading || totalCount > 0 ? (
            renderBody()
          ) : (
            <div className="no-result-found mt-4">
              <Icon icon="alert" className="alert-icon-no-search" />
              <div>
                <b>No result match your criteria</b>
              </div>
            </div>
          )}
        </div>
      </div>
      {renderPagination(false)}
    </>
  );
});
export default Table;
