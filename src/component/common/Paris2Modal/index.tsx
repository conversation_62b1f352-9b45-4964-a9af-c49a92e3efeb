import React, { ReactNode } from 'react';
import { createRoot } from 'react-dom/client';
import { ConfirmModal, ErrorModal, InfoModal, type ModalProps } from './Modals';

const renderModal = (content: ReactNode) => {
  const div = document.createElement('div');
  div.id = 'paris2-modal';
  document.body.appendChild(div);

  const root = createRoot(div);

  const handleClose = () => {
    root.unmount();
    document.body.removeChild(div);
  };

  root.render(React.cloneElement(content, { onClose: handleClose }));
};

const ParisModal = {
  info: (args: ModalProps) => renderModal(<InfoModal {...args} />),
  error: (args: ModalProps) => renderModal(<ErrorModal {...args} />),
  confirm: (args: ModalProps & { onConfirm: () => void }) =>
    renderModal(<ConfirmModal {...args} />),
};

export default ParisModal;
