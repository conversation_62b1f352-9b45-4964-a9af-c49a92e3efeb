import React from 'react';
import { Button, Modal } from 'react-bootstrap';
import { ExclamationTriangle } from 'react-bootstrap-icons';

export interface ModalProps {
  onClose?: () => void;
  title: string;
  message?: string;
  cancelText?: string;
}
export const InfoModal: React.FC<ModalProps> = ({
  onClose,
  title,
  message = '',
  cancelText = 'Ok',
}) => (
  <Modal show={true} centered scrollable={false}>
    <Modal.Header>
      <Modal.Title>
        <ExclamationTriangle
          className="text-warning mr-2"
          width={32}
          style={{ color: '#FFC107', cursor: 'pointer' }}
          size={32}
        />
        <span>{title}</span>
      </Modal.Title>
    </Modal.Header>
    <Modal.Body>{message}</Modal.Body>
    <Modal.Footer>
      <Button variant="primary" onClick={onClose}>
        {cancelText}
      </Button>
    </Modal.Footer>
  </Modal>
);

export const ErrorModal: React.FC<ModalProps> = ({
  onClose,
  title,
  message = '',
  cancelText = 'Ok',
}) => (
  <Modal show={true} centered scrollable={false}>
    <Modal.Header>
      <Modal.Title>{title}</Modal.Title>
    </Modal.Header>
    <Modal.Body>{message}</Modal.Body>
    <Modal.Footer>
      <Button variant="danger" onClick={onClose}>
        {cancelText}
      </Button>
    </Modal.Footer>
  </Modal>
);

export const ConfirmModal: React.FC<ModalProps & { onConfirm: () => void }> = ({
  onClose,
  title,
  message = '',
  onConfirm,
  okText = 'Confirm',
  cancelText = 'Cancel',
}) => (
  <Modal show={true} centered scrollable={false}>
    <Modal.Header>
      <Modal.Title>{title}</Modal.Title>
    </Modal.Header>
    <Modal.Body>{message}</Modal.Body>
    <Modal.Footer>
      <Button variant="secondary" onClick={onClose}>
        {cancelText}
      </Button>
      <Button variant="primary" onClick={onConfirm}>
        {okText}
      </Button>
    </Modal.Footer>
  </Modal>
);
