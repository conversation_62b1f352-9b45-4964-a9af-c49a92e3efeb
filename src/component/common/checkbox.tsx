import React from 'react';
import { Button, OverlayTrigger, Tooltip } from 'react-bootstrap';

const Checkbox = ({ row, selectedRows, onSelectRow, children = null }) => {
  const selectedRowIds = selectedRows.map((selectedRow) => selectedRow.id);
  return (
    <Button
      variant="link"
      className="checkbox-container"
      onClick={(e) => {
        e.stopPropagation();
      }}
    >
      <input
        type="checkbox"
        className="checkbox"
        checked={selectedRowIds.includes(row.original.id)}
        onChange={() => {
          const selectedRowsCopy = [...selectedRows];
          const matchedRowIndex = selectedRowIds.findIndex(
            (selectedRowId) => selectedRowId === row.original.id,
          );
          if (matchedRowIndex > -1) {
            selectedRowsCopy.splice(matchedRowIndex, 1);
          } else {
            selectedRowsCopy.push(row.original);
          }
          onSelectRow(selectedRowsCopy);
        }}
      />
      {children}
    </Button>
  );
};

export const CheckboxHeader = ({
  data,
  selectedRows,
  onSelectRow,
  showOverlay = true,
  children = null,
}) => {
  const toggleSelectAll = () => {
    if (selectedRows.length === data.length) {
      onSelectRow([]);
      return;
    }
    const selectedTableRows = [...data];
    onSelectRow(selectedTableRows);
  };
  return (
    <OverlayTrigger
      overlay={
        data.length && selectedRows.length !== data.length && showOverlay ? (
          <Tooltip id="tooltip-disabled">Select all {data.length} results</Tooltip>
        ) : (
          <span></span>
        )
      }
    >
      <span className="checkbox-container">
        <Button
          variant="link"
          onClick={(e) => {
            e.stopPropagation();
          }}
        >
          <input
            type="checkbox"
            className="checkbox"
            checked={data.length && selectedRows.length === data.length}
            onChange={() => {
              toggleSelectAll();
            }}
          />
        </Button>
        {children}
      </span>
    </OverlayTrigger>
  );
};

export default Checkbox;
