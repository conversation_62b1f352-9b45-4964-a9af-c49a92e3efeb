/* eslint-disable react/require-default-props */
import React from 'react';
import { Badge } from 'react-bootstrap';

interface AvatarProps {
  className?: string;
  image?: string;
  firstName?: string;
  lastName?: string;
  emptyText?: string;
}

const Avatar: React.FC<AvatarProps> = ({
  className,
  image,
  firstName,
  lastName,
  emptyText = '---',
}) => {
  let display;
  if (image) {
    display = <img className="avatar" src={image} alt="Avatar" />;
  } else if (firstName && lastName) {
    const badgeChar = firstName[0] + lastName[0];
    display = <Badge className={`avatar ${className}`}>{badgeChar?.toUpperCase()}</Badge>;
  } else if (firstName) {
    display = (
      <Badge className={`avatar ${className}`}>{firstName?.substring(0, 2)?.toUpperCase()}</Badge>
    );
  } else if (lastName) {
    display = (
      <Badge className={`avatar ${className}`}>{lastName?.substring(0, 2)?.toUpperCase()}</Badge>
    );
  } else {
    display = <Badge className={`avatar ${className} empty-avatar`}>{emptyText}</Badge>;
  }

  return <div>{display}</div>;
};

export default Avatar;
