import React from 'react';
import { <PERSON><PERSON>, <PERSON>tonProps, Spinner } from 'react-bootstrap';

interface Props extends ButtonProps {
  title?: string;
  isLoading?: boolean;
}
/* 
    Common Submit Button with loading state
*/
const SubmitButton = ({ title, children, isLoading, disabled = false, ...restProps }: Props) => {
  const style = isLoading ? { width: '5.5em' } : {};
  return (
    <Button
      style={style}
      variant={disabled ? 'light' : 'secondary'}
      type="submit"
      disabled={disabled || isLoading}
      {...restProps}
    >
      {isLoading ? <Spinner animation="border" size="sm" /> : title ?? children}
    </Button>
  );
};

export default SubmitButton;
