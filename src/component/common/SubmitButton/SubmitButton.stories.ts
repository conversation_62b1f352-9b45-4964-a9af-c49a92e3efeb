import type { Meta, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';
import SubmitButton from '.';

const meta = {
  title: 'Example/SubmitButton',
  component: SubmitButton,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    backgroundColor: { control: 'color' },
  },
  args: { onClick: fn() },
} satisfies Meta<typeof SubmitButton>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Loading: Story = {
  args: {
    title: 'Save',
    isLoading: true,
  },
};

export const Normal: Story = {
  args: {
    title: 'Save',
  },
};