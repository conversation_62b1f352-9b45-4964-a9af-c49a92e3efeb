.drawer {
    position: fixed;
    z-index: 1061;
    bottom: 0;
    display: flex;
    flex-direction: column;
    outline: 0;
    width: 648px;
    visibility: hidden;
    transition: transform 0.5s ease-out;
    margin-top: 62px;
    background: #FFFFFF;
    box-shadow: -8px 0px 16px #00000029;
    border: 1px solid #EFEFEF;
    font-size: 14px;

    &-right {
        top: 0;   
        right: 648px;     
        transform: translateX(100%);
    }

    &-open {
        visibility: visible;
    }

    &-header {
        display: flex;
        align-items: center;
        padding: 16px 24px;
        border-bottom: 1px solid #EFEFEF;
        justify-content: space-between;

        .btn-close {
            cursor: pointer;
            color: var(--primary)
        }
    }
    
    &-title {
      margin-bottom: 0;
      line-height: 1.5;
    }

    &-body {
        padding: 18px 24px 0 24px;
        overflow-y: auto;
        flex-grow: 1;
    }

}