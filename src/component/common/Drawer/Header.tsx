import React, { ReactNode } from 'react';
import { Icon } from '../../../styleGuide';

interface HeaderProps {
  title: string | ReactNode;
  extra?: ReactNode;
  onHide?: () => void;
}

const DrawerHeader = ({ title, onHide, extra }: HeaderProps) => {
  return (
    <div className="drawer-header">
      <h5 className="drawer-title">{title}</h5>
      <div className='d-inline'>
        {extra}
        <Icon className="btn-close" icon="close" size={30} onClick={onHide} />
      </div>
    </div>
  );
};

export default DrawerHeader;
