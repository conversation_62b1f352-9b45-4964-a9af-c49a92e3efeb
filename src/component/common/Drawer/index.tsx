import * as React from 'react';
import classNames from 'classnames';
import Header from './Header';
import Body from './Body';
import './style.scss';

enum DrawerDirection {
  Left = 'Left',
  Right = 'Right',
}

type Props = {
  show: boolean;
  children: React.ReactNode;
  direction?: DrawerDirection;
  onHide?: () => void;
};

const Drawer = ({ show, children, direction = DrawerDirection.Right, onHide }: Props) => {
  const className = classNames('drawer', 'drawer-right', {
    'drawer-open': show,
  });
  return <div className={className}>{children}</div>;
};

Drawer.Header = Header;
Drawer.Body = Body;

export { Drawer, DrawerDirection };
