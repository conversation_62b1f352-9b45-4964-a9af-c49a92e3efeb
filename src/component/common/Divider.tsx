/* eslint-disable react/display-name */
/* eslint-disable react/prop-types */
import React from 'react';
import './styles/divider.scss';

const Divider = ({ height = '1', dashed }) => {
  const dashedLineClass = dashed ? 'dashed' : '';
  return (
    <div
      className={`separator ${dashedLineClass}`}
      style={{ paddingTop: `${height}%`, paddingBottom: `${height}%` }}
    />
  );
};

export { Divider };
