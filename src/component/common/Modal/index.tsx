import React, { ReactNode } from 'react';
import { Button, Modal, ButtonProps } from 'react-bootstrap';
import SubmitButton from '../SubmitButton';
import './style.scss';

export interface ModalProps {
  visible: boolean;
  title: string;
  okText?: string;
  cancelText?: string;
  onVisibleChange: (visible: boolean) => void;
  footer?: ReactNode | null;
  children?: React.ReactNode | string;
  onOk?: (e: Event) => void;
  onCancel?: (e: Event) => void;
  style?: React.CSSProperties;
  okButtonProps?: ButtonProps;
  cancelButtonProps?: ButtonProps;
  confirmLoading?: boolean;
  bodyStyle?: React.CSSProperties;
  size?: 'sm' | 'lg';
  centered?: boolean;
  closeButton?: boolean;
}

const StandardModal: React.FC<ModalProps> = (props) => {
  const {
    visible,
    children,
    title,
    onVisibleChange,
    okText = 'Ok',
    cancelText = 'Cancel',
    style = {},
    bodyStyle = {},
    okButtonProps,
    cancelButtonProps,
    onOk,
    onCancel,
    confirmLoading = false,
    footer = true,
    size,
    centered,
    closeButton,
  } = props;
  const handleModalHide = () => {
    onVisibleChange?.(!visible);
  };

  const handleOkClick = (e: any) => {
    onOk?.(e);
  };

  const handleCancelClick = (e: any) => {
    onCancel?.(e);
    handleModalHide();
  };

  const footerDom = (
    <Modal.Footer style={{ borderTop: '0' }}>
      <Button
        variant="primary"
        onClick={handleCancelClick}
        disabled={confirmLoading}
        {...cancelButtonProps}
      >
        {cancelText}
      </Button>
      <SubmitButton
        type="submit"
        onClick={handleOkClick}
        isLoading={confirmLoading}
        {...okButtonProps}
      >
        {okText}
      </SubmitButton>
    </Modal.Footer>
  );

  return (
    <Modal
      size={size}
      show={visible}
      onHide={handleModalHide}
      aria-labelledby="modal"
      centered={centered}
      style={style}
      className="standard-modal"
    >
      <Modal.Header closeButton={closeButton}>
        <Modal.Title id="modal" className="h5 text-truncate" style={{ borderBottom: '0' }}>
          {title}
        </Modal.Title>
      </Modal.Header>
      <Modal.Body style={bodyStyle}>{children}</Modal.Body>
      {footer && footerDom}
    </Modal>
  );
};

export type { ModalProps };
export default StandardModal;
