/* eslint-disable react/prop-types */
import React from 'react';
import Modal from 'react-bootstrap/Modal';
import Button from 'react-bootstrap/Button';

function ErrorDisplayModal(props) {
  return (
    <Modal show={!!props?.modalMessage} onHide={props.onHideModalMessage} centered>
      <Modal.Header className="modal-header">{props?.title || 'Error Message :'}</Modal.Header>
      <Modal.Body>{props?.modalMessage}</Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={props.onHideModalMessage}>
          Close
        </Button>
      </Modal.Footer>
    </Modal>
  );
}

export default ErrorDisplayModal;
