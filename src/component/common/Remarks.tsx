import React, { useState } from 'react';
import { But<PERSON>, Modal } from 'react-bootstrap';

const Remarks = (props) => {
  const [show, setShow] = useState(false);
  const handleClose = () => setShow(false);
  const handleShow = () => setShow(true);

  return (
    <>
      <button type="button" className="btn btn-link p-0 text-left" onClick={handleShow}>
        <u>
          {props.remarksData.length > 40
            ? props.remarksData.substring(0, 40).trim().concat('...')
            : props.remarksData}
        </u>
      </button>

      <Modal className="action-modal" show={show} onHide={handleClose} centered>
        <Modal.Header>
          <Modal.Title className="h5">Remarks</Modal.Title>
        </Modal.Header>
        <Modal.Body>{props.remarksData}</Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleClose}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default Remarks;
