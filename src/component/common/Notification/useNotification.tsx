import { useContext } from 'react';
import { NotificationContextType } from './types';
import NotificationContext from './NotificationContext';

// Custom hook to use notification context
// eslint-disable-next-line import/prefer-default-export
export const useNotification = (): NotificationContextType => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotification must be used within a NotificationProvider');
  }
  return context;
};
