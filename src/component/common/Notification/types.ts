export interface AlertMessage {
  message: string;
  variant: 'info' | 'success' | 'warning' | 'danger';
  className?: string;
}
export interface NotificationContextType {
  notify: {
    info: (message: string, className?: string) => void;
    success: (message: string, className?: string) => void;
    warning: (message: string, className?: string) => void;
    danger: (message: string, className?: string) => void;
  };
  clearNotification: () => void;
}