import React, { useState, ReactNode, useCallback, useMemo } from 'react';
import NotificationContext from './NotificationContext';
import AutoDismissibleAlert from '../AutoDismissibleAlert';
import { AlertMessage } from './types';

enum VariantClassMap {
  info = 'alert-message-info',
  success = 'alert-message-success',
  warning = 'alert-message-warning',
  danger = 'alert-message-danger',
}
type NotifyVariant = 'info' | 'success' | 'warning' | 'danger';

// Notification Provider component
const NotificationProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [alertMessage, setAlertMessage] = useState<AlertMessage | null>(null);

  const baseNotify = useCallback(
    (message: string, variant: NotifyVariant = 'info', className: string = '') => {
      setAlertMessage({ message, variant, className: className || VariantClassMap[variant] });
    },
    [],
  );
  const clearNotification = () => {
    setAlertMessage(null);
  };
  const contextValue = useMemo(() => {
    const notify = {
      info: (message: string, className: string = '') => baseNotify(message, 'info', className),
      success: (message: string, className: string = '') => baseNotify(message, 'success', className),
      warning: (message: string, className: string = '') => baseNotify(message, 'warning', className),
      danger: (message: string, className: string = '') => baseNotify(message, 'danger', className),
    };

    return { notify, clearNotification };
  }, [baseNotify]);

  // NOSONAR
  return (
    <NotificationContext.Provider value={contextValue}>
      {!!alertMessage?.message && (
        <AutoDismissibleAlert
          noAutoDismissOnDanger
          className={alertMessage.className}
          variant={alertMessage.variant}
          message={alertMessage.message}
          onClose={clearNotification}
        />
      )}
      {children}
    </NotificationContext.Provider>
  );
};

export default NotificationProvider;
