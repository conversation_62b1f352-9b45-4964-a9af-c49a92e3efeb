import React from 'react';
import { DateTime, Duration } from 'luxon';
import { dateAsString, stringAsDate, isDateValid } from '../../model/utils';
import './common.scss';
interface Props {
  value: string;
}
const getColorClass = (value: string) => {
  const now = DateTime.now();
  const dateOfExpiry = DateTime.fromISO(value);
  if (now.toMillis() > dateOfExpiry.toMillis()) {
    return 'font-red';
  }
  if (now.plus(Duration.fromObject({ days: 30 })) > dateOfExpiry.toMillis()) {
    return 'font-yellow';
  }
  if (now.plus(Duration.fromObject({ days: 60 })) > dateOfExpiry.toMillis()) {
    return 'font-green';
  }

  return 'font-black';
};

const EndOfContractStyling = ({ value }:Props) => {
  if (!isDateValid(value)) {
    return null;
  }
  let colorClass = getColorClass(value);
  if (colorClass !== 'font-black') {
    colorClass += ' font-bold';
  }
  const dt = dateAsString(stringAsDate(value));
  return <span data-testid="EndOfContractStyling_span" className={colorClass}>{dt}</span>;
};

export { EndOfContractStyling };
