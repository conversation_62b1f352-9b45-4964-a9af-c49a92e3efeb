import React, { SyntheticEvent } from 'react';
import { Button } from 'react-bootstrap';

interface Props {
  ownershipId: number;
  vesselName: string;
  eventTracker: Function;
  className?: string;
}

const VesselNameLink = ({ ownershipId, vesselName, eventTracker, className = '' }: Props) => {
  const routeToVesselDetails = (event: SyntheticEvent, ownershipId: number) => {
    event.stopPropagation();
    if (
      eventTracker.name === 'contractExpEventTracker' ||
      eventTracker.name === 'avlSeafarerEventTracker'
    ) {
      eventTracker('vesselLink', vesselName);
    } else {
      eventTracker('vesselDetailsLink', 'Routes to Vessel Details');
    }
    window.open(`/vessel/ownership/details/${ownershipId}`, '_blank');
  };

  return (
    <Button
      onClick={(e) => routeToVesselDetails(e, ownershipId)}
      variant="link"
      className={`button-link btn btn-link ${className}`}
      title={vesselName}
    >
      {vesselName}
    </Button>
  );
};

export default VesselNameLink;
