/* eslint-disable react/display-name */
/* eslint-disable react/prop-types */
import React, { useMemo, useEffect, useState } from 'react';
import { useTable, useSortBy, usePagination, useFlexLayout } from 'react-table';
import { useSticky } from 'react-table-sticky';
import { v4 as uuid } from 'uuid';
import { Button, OverlayTrigger, Tooltip } from 'react-bootstrap';
import Spinner from '../common/Spinner';
import styleGuide from '../../styleGuide';
import NoRecord from '../common/NoRecords';
import { storePageSort } from '../../util/local-storage-helper';
import {
  columnSortEventName,
  columnSortIconName,
  getOcimfPillColor,
  getRanksForTooltip,
  getTypeOfCompliance,
} from '../../util/view-utils';
import { uniq } from 'lodash';

const { Icon } = styleGuide;

const getOcimfTooltipContent = (row: any, key: string) => {
  if (!row?.ocimf_matrix?.complianceissues?.ocimf?.[key]?.is_compliant) {
    const rankText = getRanksForTooltip(
      row?.ocimf_matrix?.complianceissues?.ocimf?.[key]?.rank_group,
    );
    const typeOfComplianceText = getTypeOfCompliance(key);
    return (
      <div className="ocimf-tooltip-single-content-wrapper">
        <div className="ocimf-tooltip-header">{rankText}</div>
        <div className="ocimf-tooltip-text">{typeOfComplianceText}</div>
      </div>
    );
  } else if (
    row?.ocimf_matrix?.complianceissues?.ocimf?.[key]?.is_compliant &&
    row?.ocimf_matrix?.compliance_issues_badges?.ocimf !== 'green'
  ) {
    return null;
  }
  return 'OCIMF Compliant';
};

const generateColumns = ({
  selectedColumns,
  roleConfig,
  visitUpdateSeafarer,
  tableData,
  selectedRows,
  onSelectRow,
  toggleSelectAll,
  enableOcimf = false,
}) => {
  return [
    {
      id: 'id',
      accessor: (row) => {
        return row.id ?? '---';
      },
      Cell: ({ row, cell }) => {
        const selectedRowIds = selectedRows.map((selectedRow: { id: number }) => selectedRow.id);
        return (
          <div
            className="checkbox-container"
            onClick={(e) => {
              e.stopPropagation();
            }}
            onKeyDown={() => {}}
          >
            <input
              type="checkbox"
              className="checkbox"
              checked={selectedRowIds.includes(row.original.id)}
              onChange={() => {
                const selectedRowsCopy = [...selectedRows];
                const matchedRowIndex = selectedRowIds.findIndex(
                  (selectedRowId: number) => selectedRowId === row.original.id,
                );
                if (matchedRowIndex > -1) {
                  selectedRowsCopy.splice(matchedRowIndex, 1);
                } else {
                  selectedRowsCopy.push(row.original);
                }
                onSelectRow(selectedRowsCopy);
              }}
            />
            <span>{cell.value}</span>
          </div>
        );
      },
      Header: () => {
        return (
          <span className="checkbox-container">
            <input
              data-testid="checkbox"
              type="checkbox"
              className="checkbox"
              checked={selectedRows.length === tableData.length}
              onChange={() => toggleSelectAll()}
            />
            <span>No.</span>
          </span>
        );
      },
      sortType: (a, b) => {
        if (a && b) {
          const aName = a.id ? a.id.toLowerCase() : '';
          const bName = b.id ? b.id.toLowerCase() : '';
          if (aName < bName) return -1;
          if (aName > bName) return 1;
        }
        return 0;
      },
      width: 50,
      sticky: 'left',
      disableSortBy: true,
    },
    ...selectedColumns,
    ...[
      enableOcimf && {
        Header() {
          return <div>Crew Compliance</div>;
        },
        id: 'crew-compliance',
        accessor: (row, index) => (
          <div className="ocimf-status-pills-wrapper">
            {row?.ocimf_matrix?.compliance_issues_badges?.document && (
              <OverlayTrigger
                placement={'bottom'}
                overlay={
                  <Tooltip id={'tooltip-bottom'}>Documents not verified by the Flag Office</Tooltip>
                }
              >
                <div
                  className="ocimf-status-pill"
                  style={{
                    ...getOcimfPillColor(row?.ocimf_matrix?.compliance_issues_badges?.document),
                  }}
                >
                  Document
                </div>
              </OverlayTrigger>
            )}
            {row?.ocimf_matrix?.compliance_issues_badges?.ocimf && (
              <OverlayTrigger
                placement={'bottom'}
                overlay={
                  <Tooltip id={'tooltip-bottom'}>
                    <div className="ocimf-tooltip-wrapper">
                      {uniq(
                        Object.keys(row?.ocimf_matrix?.complianceissues?.ocimf).map((key) => {
                          return getOcimfTooltipContent(row, key);
                        }),
                      ).filter(Boolean)}
                    </div>
                  </Tooltip>
                }
              >
                <div
                  className="ocimf-status-pill"
                  style={{
                    ...getOcimfPillColor(row?.ocimf_matrix?.compliance_issues_badges?.ocimf),
                  }}
                >
                  OCIMF
                </div>
              </OverlayTrigger>
            )}
            {row?.ocimf_matrix?.compliance_issues_badges?.experience && (
              <OverlayTrigger
                placement={'bottom'}
                overlay={
                  <Tooltip id={'tooltip-bottom'}>
                    <div className="ocimf-tooltip-wrapper">
                      "Exp. in Company" is less than 6 months
                    </div>
                  </Tooltip>
                }
              >
                <div
                  className="ocimf-status-pill"
                  style={{
                    ...getOcimfPillColor(row?.ocimf_matrix?.compliance_issues_badges?.experience),
                  }}
                >
                  Experience
                </div>
              </OverlayTrigger>
            )}
          </div>
        ),
        disableSortBy: true,
        width: 240,
        sticky: 'right',
      },
    ].filter(Boolean),
    ...[
      roleConfig.seafarer.editSeafarer && {
        Header() {
          return <div className="text-center">Actions</div>;
        },
        id: 'actions',
        accessor: (row, index) => (
          <div style={{ width: '100%', display: 'flex', justifyContent: 'center' }}>
            <Button
              variant="link"
              className="action-column action-button-edit-color"
              onClick={(e) => {
                e.stopPropagation();
                visitUpdateSeafarer(row.id);
              }}
              data-testid={`edit-${index}-crew-list-btn`}
            >
              <Icon icon="pencil" size={20} className="mr-3" style={{ cursor: 'pointer' }} />
            </Button>
          </div>
        ),
        disableSortBy: true,
        maxWidth: 50,
        sticky: 'right',
      },
    ].filter(Boolean),
  ];
};

const CrewListTable = React.memo(
  ({
    selectedColumns,
    loading,
    data,
    tableRef,
    roleConfig,
    visitUpdateSeafarer,
    eventTracker,
    seafarersTotalCount,
    init_sort,
    setInitSort,
    selectedRows,
    onSelectRow,
    tab,
    enableOcimf,
  }) => {
    const [tableData, setTableData] = useState(data);
    const [showEmptyTable, setShowEmptyTable] = useState(false);

    useEffect(() => {
      setTableData([]);
    }, [tab]);

    useEffect(() => {
      if (data.length) {
        setTableData(data);
      } else {
        setTableData([]);
        setShowEmptyTable(true);
      }
    }, [data]);

    const toggleSelectAll = () => {
      if (selectedRows.length === data.length) {
        onSelectRow([]);
        return;
      }
      const selectedTableRows = [...data];
      onSelectRow(selectedTableRows);
    };

    let columns = generateColumns({
      selectedColumns,
      roleConfig,
      visitUpdateSeafarer,
      tableData,
      selectedRows,
      onSelectRow,
      toggleSelectAll,
      enableOcimf,
    });

    // Remove No, Actions columns while loading
    if (loading) {
      const removeOnLoad = ['No.', 'Actions'];
      columns = columns.filter((col_obj) => {
        if (col_obj.Header && removeOnLoad.includes(col_obj.Header)) return false;
        return true;
      });
    }

    // Remove Actions column, If user doesn't have replace|crew-list role
    if (!roleConfig?.seafarer.replaceCrewList) {
      columns = columns.filter((col_obj) => {
        return col_obj.id !== 'actions';
      });
    }

    return (
      <div className="seafarer-table">
        {!!tableData.length && (
          <Table
            columns={columns}
            data={tableData}
            loading={loading}
            tableRef={tableRef}
            roleConfig={roleConfig}
            eventTracker={eventTracker}
            init_sort={init_sort}
            setInitSort={setInitSort}
            seafarersTotalCount={seafarersTotalCount}
            tab={tab}
          />
        )}
        {showEmptyTable && !tableData.length && (
          <Table
            columns={columns}
            data={[]}
            loading={loading}
            tableRef={tableRef}
            roleConfig={roleConfig}
            eventTracker={eventTracker}
            init_sort={init_sort}
            setInitSort={setInitSort}
            seafarersTotalCount={seafarersTotalCount}
            tab={tab}
          />
        )}
      </div>
    );
  },
);

const Table = React.memo(
  ({
    columns,
    data,
    eventTracker,
    loading,
    tableRef,
    roleConfig,
    init_sort,
    setInitSort,
    seafarersTotalCount,
    tab,
  }) => {
    const defaultColumn = useMemo(
      () => ({
        minWidth: 120,
        width: 120,
      }),
      [],
    );

    const {
      getTableProps,
      getTableBodyProps,
      headerGroups,
      prepareRow,
      page,
      state: { sortBy },
    } = useTable(
      {
        columns,
        data,
        defaultColumn,
        manualPagination: true,
        manualSortBy: true,
        autoResetPage: false,
        autoResetSortBy: false,
        initialState: { sortBy: init_sort },
      },
      useSortBy,
      usePagination,
      useFlexLayout,
      useSticky,
    );

    useEffect(() => {
      storePageSort(tab, sortBy);
      setInitSort(sortBy);
    }, [sortBy]);

    const renderRow = (row) => {
      prepareRow(row);
      const clickHandler = () => {
        window.open(`/seafarer/details/${row?.original?.id}/general`, '_blank');
      };

      return (
        <div
          key={row.id}
          {...row.getRowProps()}
          className="tr"
          onClick={roleConfig.seafarer.view.general ? clickHandler : null}
          onKeyDown={(event) => {
            if (event.key === 'Enter' || event.key === ' ') {
              roleConfig.seafarer.view.general && clickHandler();
            }
          }}
          tabIndex={0}
        >
          {row.cells.map((cell) => {
            const tdProps = cell.getCellProps();
            return (
              <div
                key={tdProps.key}
                {...tdProps}
                className={`td ${row?.original?.is_only_worked_fml ? 'vessel-company-type' : ''}`}
              >
                {cell.render('Cell')}
              </div>
            );
          })}
        </div>
      );
    };

    const renderTableBody = () => {
      if (loading) {
        return <Spinner alignClass="load-spinner" />;
      }

      if (page.length > 0) {
        return (
          <div {...getTableBodyProps()} className="body">
            {page.map((row) => renderRow(row))}
          </div>
        );
      }

      return !loading && <NoRecord />;
    };

    return (
      <div {...getTableProps()} className="table sticky" ref={tableRef}>
        <div className="header">
          {headerGroups.map((headerGroup, index) => (
            <div key={uuid()} {...headerGroup.getHeaderGroupProps()} className="tr">
              {headerGroup.headers.map((column, index2) => {
                const thProps = column.getHeaderProps(column.getSortByToggleProps());
                return (
                  <div key={column.id} {...thProps} className="th" id={`as-${index} + ${index2}`}>
                    {column.render('Header')}
                    <span>
                      {column.canSort && (
                        <Icon
                          icon={columnSortIconName(column)}
                          size={20}
                          className="default"
                          onClick={() => eventTracker('sortBy', columnSortEventName(column))}
                        />
                      )}
                    </span>
                  </div>
                );
              })}
            </div>
          ))}
        </div>
        {renderTableBody()}
      </div>
    );
  },
);

export default CrewListTable;
