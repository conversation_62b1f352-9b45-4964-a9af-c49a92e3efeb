import React from 'react';
import fmlLogo from '../../../public/icons/fml-vessel.svg';
import './CrewList.scss';
const CrewMemberNameStyle = ({ row, customClass = '' }: any) => {
  return (
    <div className="container mx-0 px-0">
      <div className="row">
        {row?.is_only_worked_fml && (
          <div className="col-1 mx-0 px-0">
            <img src={fmlLogo} />
          </div>
        )}
        <div
          className={
            (row?.is_only_worked_fml
              ? 'col-11 mx-0 ps-1 text-start highlight-name'
              : 'col-11 mx-0 ps-1 text-start')
            + customClass
          }
        >
          {(row?.seafarer_person?.first_name ?? '') +
            ' ' +
            (row?.seafarer_person?.middle_name ?? '') +
            ' ' +
            (row?.seafarer_person?.last_name ?? '')}
        </div>
      </div>
    </div>
  );
};
export default CrewMemberNameStyle;
