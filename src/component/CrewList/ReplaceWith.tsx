import React from 'react';
import { Button } from 'react-bootstrap';
import { capitalizeArgs } from '../../model/utils';

interface Props {
  replaced_by: object;
  type: string;
  tableName: string;
  roleConfig?: any;
}

const ReplaceWith = ({ replaced_by, type, tableName = 'contract-expiry', roleConfig }: Props) => {
  const getFullName = (person) => {
    const firstName = person?.first_name ?? '';
    const lastName = person?.last_name ?? '';
    const middleName = person?.middle_name ?? '';
    const fullName = capitalizeArgs(firstName, middleName, lastName) || null;
    return fullName;
  };
  const routeToDetailPage = (e) => {
    e.stopPropagation();
    if (replaced_by?.id) {
      window.open(`/seafarer/details/${replaced_by?.id}/general`, '_blank');
    }
  };

  const replacedByButton = () => {
    if (!replaced_by?.seafarer_person?.first_name) {
      return '---';
    }
    if (tableName === 'crew-list' && roleConfig?.seafarer.view.general) {
      return (
        <Button variant="link" onClick={(e) => routeToDetailPage(e)}>
          {getFullName(replaced_by?.seafarer_person)}
        </Button>
      );
    }
    return <b> {getFullName(replaced_by?.seafarer_person)}</b>;
  };

  return (
    <span>
      {type === 'name' && replacedByButton()}
      {type === 'nationality' &&
        (replaced_by?.seafarer_person?.nationality?.value
          ? replaced_by.seafarer_person.nationality.value
          : '---')}
      {type === 'hkid' &&
        (replaced_by?.hkid ? (
          <Button variant="link" onClick={(e) => routeToDetailPage(e)}>
            {replaced_by?.hkid}
          </Button>
        ) : (
          '---'
        ))}
    </span>
  );
};

export default ReplaceWith;
