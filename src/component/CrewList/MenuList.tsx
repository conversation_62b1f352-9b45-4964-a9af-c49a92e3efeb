/* eslint-disable react/prop-types */
import React from 'react';
import {
  dateAsString,
  stringAsDate,
  covertDaysToYear,
  convertDaysToMonthAndDay,
} from '../../model/utils';
import { EndOfContractStyling } from '../common/EndOfContractStyling';
import parentHKIDLink from '../common/HKIDLink';
import VesselNameLink from '../CrewList/VesselNameLink';
import CrewListLink from '../CrewList/CrewListLink';
import CrewMemberNameStyle from './CrewMemberNameStyle';
import ReplaceButton from './ReplacementButton';
import ShowYearsInRank from './ShowYearsInRank';
import ShowYearsInVesselType from './ShowYearsInVesselType';
import ReplaceWith from './ReplaceWith';
import { seafarerStatusService } from 'paris2-seafarer-status';
import moment from 'moment';
import { seafarerStatus } from '../../model/constants';
import { formatYesNo, getSeafarerStatus, replaceEmptyWithDashes } from '../../util/commonUtil';

const accountStatusJson = seafarerStatusService.getAccountStatus();
const journeyStatusJson = seafarerStatusService.getJourneyStatus();
const examStatusJson = seafarerStatusService.getExamStatus();

export const getAdministrationColor = (val: string) => {
  switch (val) {
    case 'Yes':
      return { color: '#218838', background: '#E8F5EB' };
    case 'No':
      return { color: '#C82333', background: '#FAF2F5' };
    case 'N/A':
      return { color: '#6C757D', background: '#EFEFEF' };
    case 'Applied for':
      return { color: '#F08100', background: '#FFF9E8' };
  }
};

export function convertToYearsAndMonths(decimal: number) {
  let years = Math.floor(decimal);
  let months = Math.round((decimal - years) * 12);
  if (months === 12) {
    years += 1;
    months = 0;
  }
  return `${years}yrs ${months}m`;
}

export const getCrewListPageColumns = (
  isOcimfMatrix = false,
  activeKey = seafarerStatus.SIGNED_ON,
) =>
  [
    {
      type: 'number',
      Header: 'HKID',
      id: 'hkid',
      name: 'hkid',
      accessor: (row) => parentHKIDLink(row),
      order: 1,
      disableSortBy: false,
      minWidth: 80,
      sticky: window.innerWidth > 960 ? 'left' : null,
      showToOwner: true,
      showByDefault: true,
    },
    {
      type: 'text',
      Header: 'Onboard Seafarer',
      id: 'seafarer_person.first_name',
      name: 'seafarer_person.first_name',
      accessor: function nameAccessor(row) {
        return (
          <div>
            {row?.seafarer_person?.first_name || row?.seafarer_person?.last_name ? (
              <CrewMemberNameStyle row={row} customClass=" underline link-underline" />
            ) : (
              '---'
            )}
          </div>
        );
      },
      order: 2,
      disableSortBy: false,
      minWidth: 200,
      sticky: window.innerWidth > 960 ? 'left' : null,
      showToOwner: true,
      showByDefault: true,
    },
    {
      type: 'text',
      Header: 'Rank',
      id: 'seafarer_person:seafarer_status_history:seafarer_rank.sortpriority',
      name: 'seafarer_person.seafarer_status_history[0].seafarer_rank.sortpriority',
      accessor: function rankAccessor(row) {
        return (
          <div>
            {row?.seafarer_person?.seafarer_status_history[0]?.seafarer_rank.value ??
              row?.seafarer_rank?.value ??
              '---'}
          </div>
        );
      },
      order: 3,
      disableSortBy: false,
      minWidth: 50,
      showToOwner: true,
      showByDefault: true,
    },
    ...[
      isOcimfMatrix && {
        type: 'text',
        Header: 'Certificate of Competancy',
        id: 'competancy',
        name: 'competancy',
        accessor: (row) => {
          return <div>{row?.ocimf_matrix?.certcomp}</div>;
        },
        order: 4,
        disableSortBy: true,
        minWidth: 160,
        showToOwner: true,
        showByDefault: true,
      },
      isOcimfMatrix && {
        type: 'text',
        Header: 'Endorsements',
        id: 'endorsements',
        name: 'endorsements',
        accessor: (row) => {
          return <div>{row?.ocimf_matrix?.endorsements?.join(', ')}</div>;
        },
        order: 5,
        disableSortBy: true,
        minWidth: 240,
        showToOwner: true,
        showByDefault: true,
      },
      isOcimfMatrix && {
        type: 'text',
        Header: 'Administration Acceptance',
        id: 'Administration Acceptance',
        name: 'Administration Acceptance',
        accessor: (row) => {
          return (
            <div
              className="ocimf-administration"
              style={{ ...getAdministrationColor(row?.ocimf_matrix?.adminaccept) }}
            >
              {row?.ocimf_matrix?.adminaccept?.split(' ')?.[0]}
            </div>
          );
        },
        order: 6,
        disableSortBy: true,
        minWidth: 150,
        showToOwner: true,
        showByDefault: true,
      },
    ].filter(Boolean),
    {
      type: 'date',
      Header: 'Contract End Date',
      id: 'seafarer_person:seafarer_status_history.expected_contract_end_date',
      name: 'seafarer_person.seafarer_status_history[0].expected_contract_end_date',
      accessor: function endOfContractAccessor(row) {
        return (
          <div>
            {row?.seafarer_person?.seafarer_status_history[0]?.expected_contract_end_date
              ? dateAsString(
                  stringAsDate(
                    row?.seafarer_person?.seafarer_status_history[0]?.expected_contract_end_date,
                  ),
                )
              : '---'}
          </div>
        );
      },
      order: 7,
      disableSortBy: false,
      minWidth: 125,
      showToOwner: true,
      showByDefault: true,
    },
    {
      type: 'text',
      Header: 'Sign On Date',
      id: 'seafarer_person:seafarer_status_history.status_date',
      name: 'seafarer_person.seafarer_status_history[0].status_date',
      accessor: function dateOfJoiningAccessor(row) {
        return (
          <div>
            {row?.seafarer_person?.seafarer_status_history[0]?.status_date
              ? dateAsString(
                  stringAsDate(row?.seafarer_person?.seafarer_status_history[0]?.status_date),
                )
              : '---'}
          </div>
        );
      },
      order: 8,
      disableSortBy: false,
      minWidth: 50,
      showToOwner: true,
      showByDefault: activeKey === seafarerStatus.SIGNED_ON,
    },
    {
      type: 'text',
      Header: 'Length Of Contract',
      id: 'seafarer_length_of_contract',
      name: 'seafarer_length_of_contract',
      accessor: function lengthOfContract(row) {
        return row?.seafarer_person?.seafarer_status_history[0]?.expected_contract_end_date &&
          row?.seafarer_person?.seafarer_status_history[0]?.status_date ? (
          <div className="length-of-contract">
            {moment(
              row?.seafarer_person?.seafarer_status_history[0]?.expected_contract_end_date,
              'YYYY-MM-DD',
            ).diff(
              moment(row?.seafarer_person?.seafarer_status_history[0]?.status_date, 'YYYY-MM-DD'),
              'months',
            )}{' '}
            months
          </div>
        ) : (
          '---'
        );
      },
      order: 9,
      disableSortBy: false,
      minWidth: 125,
      showToOwner: true,
      showByDefault: activeKey === seafarerStatus.SIGNED_ON,
    },
    {
      type: 'text',
      Header: 'Repatriation Port',
      id: 'seafarer_person:seafarer_status_history.repatriation_port',
      name: 'seafarer_person.seafarer_status_history[0].repatriation_port',
      accessor: function repatriationPortAccessor(row) {
        return (
          <div>
            {replaceEmptyWithDashes(
              row?.seafarer_person?.seafarer_status_history[0]?.repatriation_port,
            )}
          </div>
        );
      },
      order: 10,
      disableSortBy: false,
      minWidth: 30,
      showToOwner: true,
      showByDefault: true,
    },
    {
      type: 'text',
      Header: 'Embarkation Port',
      id: 'seafarer_person:seafarer_status_history.embarkation_port',
      name: 'seafarer_person.seafarer_status_history[0].embarkation_port',
      accessor: function embarkationPortAccessor(row) {
        return (
          <div>
            {replaceEmptyWithDashes(
              row?.seafarer_person?.seafarer_status_history[0]?.embarkation_port,
            )}
          </div>
        );
      },
      order: 11,
      disableSortBy: false,
      minWidth: 20,
      showToOwner: true,
      showByDefault: true,
    },
    {
      type: 'text',
      Header: 'Nationality',
      id: 'seafarer_person:nationality.value',
      name: 'seafarer_person.nationality.value',
      accessor: function addedByAccessor(row) {
        return <div>{row?.seafarer_person?.nationality?.value ?? '---'}</div>;
      },
      order: 12,
      disableSortBy: false,
      minWidth: 120,
      showToOwner: true,
      showByDefault: true,
    },
    isOcimfMatrix && {
      type: 'text',
      Header: 'Years with FML',
      id: 'duration_with_company',
      name: 'experience_summary.duration_with_company',
      handleExportOptionalFn: covertDaysToYear,
      accessor: function yearswithCompanyAccessor(row) {
        return isOcimfMatrix ? (
          <div>{covertDaysToYear(row?.experience_summary?.duration_with_company)}</div>
        ) : (
          convertToYearsAndMonths(covertDaysToYear(row?.experience_summary?.duration_with_company))
        );
      },
      order: 14,
      disableSortBy: true,
      minWidth: 50,
      showByDefault: true,
    },
    isOcimfMatrix && {
      type: 'number',
      Header: 'Years in Rank',
      id: 'duration_in_target_rank',
      name: 'experience_summary.duration_in_target_rank',
      handleExportOptionalFn: covertDaysToYear,
      accessor: function yearsInRankAccessor(row) {
        return isOcimfMatrix ? (
          <div>
            <ShowYearsInRank
              value={covertDaysToYear(row?.experience_summary?.duration_in_target_rank)}
            />
          </div>
        ) : (
          <div>
            {convertToYearsAndMonths(
              covertDaysToYear(row?.experience_summary?.duration_in_target_rank),
            )}
          </div>
        );
      },
      order: 13,
      disableSortBy: true,
      minWidth: 30,
      showByDefault: true,
    },
    isOcimfMatrix && {
      type: 'text',
      Header: 'Years on this Vessel Type',
      id: 'target_vessel_type.value',
      name: 'experience_summary.duration_on_target_vessel_type',
      handleExportOptionalFn: covertDaysToYear,
      accessor: function yearsOnVesselTypeAccessor(row) {
        return isOcimfMatrix ? (
          <ShowYearsInVesselType
            value={covertDaysToYear(row?.experience_summary?.duration_on_target_vessel_type)}
          />
        ) : (
          <div>
            {convertToYearsAndMonths(
              covertDaysToYear(row?.experience_summary?.duration_on_target_vessel_type),
            )}
          </div>
        );
      },
      order: 15,
      disableSortBy: true,
      minWidth: 30,
      showByDefault: true,
    },
    isOcimfMatrix && {
      type: 'text',
      Header: 'Years on all Vessel Type',
      id: 'duration_on_all_vessel',
      name: 'experience_summary.duration_on_all_vessel_type',
      handleExportOptionalFn: covertDaysToYear,
      accessor: function yearsOnAllVesselTypeAccessor(row) {
        return isOcimfMatrix ? (
          <div>
            {covertDaysToYear(
              row?.experience_summary?.duration_on_all_vessel_type ??
                row?.experience_summary?.duration_on_all_vessel_type,
            )}
          </div>
        ) : (
          <div>
            {convertToYearsAndMonths(
              covertDaysToYear(row?.experience_summary?.duration_on_all_vessel_type),
            )}
          </div>
        );
      },
      order: 16,
      disableSortBy: true,
      minWidth: 30,
      showByDefault: true,
    },
    isOcimfMatrix && {
      type: 'text',
      Header: 'Total Watch Years',
      id: 'duration_in_watch_years',
      name: 'experience_summary.duration_in_watch_years',
      handleExportOptionalFn: covertDaysToYear,
      accessor: function yearsOnAllVesselTypeAccessor(row) {
        return (
          <div>
            {covertDaysToYear(
              row?.experience_summary?.duration_in_watch_years ??
                row?.experience_summary?.duration_in_watch_years,
            )}
          </div>
        );
      },
      order: 16,
      disableSortBy: true,
      minWidth: 30,
      showByDefault: true,
    },
    {
      type: 'text',
      Header: 'Months on Current Voyage',
      id: 'experience_summary.duration_on_target_vessel',
      name: 'experience_summary.duration_on_target_vessel',
      handleExportOptionalFn: convertDaysToMonthAndDay,
      accessor: function monthsOnVesselTourTypeAccessor(row) {
        return (
          <div>{convertDaysToMonthAndDay(row?.experience_summary?.duration_on_target_vessel)}</div>
        );
      },
      order: 17,
      disableSortBy: true,
      minWidth: 30,
      showByDefault: true,
    },
    {
      type: 'date',
      Header: 'Date of Birth',
      id: 'seafarer_person.date_of_birth',
      name: 'seafarer_person.date_of_birth',
      accessor: function dateOfBirthAccessor(row) {
        return (
          <div>
            {row?.seafarer_person?.date_of_birth
              ? dateAsString(stringAsDate(row?.seafarer_person?.date_of_birth))
              : '---'}
          </div>
        );
      },
      order: 18,
      disableSortBy: false,
      minWidth: 30,
      showToOwner: true,
      showByDefault: true,
    },
    {
      type: 'text',
      Header: 'Place of Birth',
      id: 'seafarer_person.place_of_birth',
      name: 'seafarer_person.place_of_birth',
      accessor: function placeOfBirthAccessor(row) {
        return <div>{row?.seafarer_person?.place_of_birth ?? '---'}</div>;
      },
      order: 19,
      disableSortBy: false,
      minWidth: 30,
      showToOwner: true,
      showByDefault: true,
    },
    {
      Header: 'Replacer Seafarer',
      id: 'replace_with',
      accessor: function ReplaceWithAccessor(row) {
        return (
          <div>
            {
              <ReplaceWith
                replaced_by={row?.seafarer_person?.seafarer_status_history[0]?.replaced_by}
                type="name"
                tableName="crew-list"
              />
            }
          </div>
        );
      },
      order: 20,
      disableSortBy: false,
      minWidth: 50,
      showByDefault: true,
    },
    {
      type: 'text',
      Header: 'Account Status',
      id: 'seafarer_person.current_account_status',
      name: 'seafarer_person.current_account_status',
      accessor: function accountStatusAccessor(row) {
        return (
          <div>
            {getSeafarerStatus(accountStatusJson, row?.seafarer_person?.current_account_status)}
          </div>
        );
      },
      order: 21,
      disableSortBy: false,
      minWidth: 50,
      showByDefault: false,
    },
    {
      type: 'text',
      Header: 'Journey Status',
      id: 'seafarer_person.current_journey_status',
      name: 'seafarer_person.current_journey_status',
      accessor: function accountStatusAccessor(row) {
        return (
          <div>
            {getSeafarerStatus(journeyStatusJson, row?.seafarer_person?.current_journey_status) ??
              '---'}
          </div>
        );
      },
      order: 22,
      disableSortBy: false,
      minWidth: 120,
      showToOwner: true,
      showByDefault: false,
    },
    {
      type: 'text',
      Header: 'Tech Group',
      id: 'seafarer_person:seafarer_status_history.vessel_tech_group',
      name: 'seafarer_person.seafarer_status_history[0].vessel_tech_group',
      accessor: function techGroupAccessor(row) {
        return (
          <div>{row?.seafarer_person?.seafarer_status_history[0]?.vessel_tech_group ?? '---'}</div>
        );
      },
      order: 23,
      disableSortBy: false,
      minWidth: 90,
      showByDefault: false,
    },
    {
      type: 'text',
      Header: 'Reporting Office',
      id: 'seafarer_reporting_office.value',
      name: 'seafarer_reporting_office.value',
      accessor: function reportingOfficeAccessor(row) {
        return <div>{row?.seafarer_reporting_office?.value ?? '---'}</div>;
      },
      order: 24,
      disableSortBy: false,
      minWidth: 50,
      showByDefault: false,
    },
    {
      type: 'text',
      Header: 'Passport No. ',
      id: 'seafarer_person:passports.number',
      name: 'seafarer_person.passports[0].number',
      accessor: function passportNoAccessor(row) {
        return <div>{row?.seafarer_person?.passports[0]?.number ?? '---'}</div>;
      },
      order: 25,
      disableSortBy: false,
      minWidth: 50,
      showToOwner: true,
      showByDefault: false,
    },
    {
      type: 'text',
      Header: "Seaman's Book",
      id: 'seafarer_person:seaman_books.number',
      name: 'seafarer_person.seaman_books[0].number',
      accessor: function seamanBookAccessor(row) {
        return <div>{row?.seafarer_person?.seaman_books[0]?.number ?? '---'}</div>;
      },
      order: 25,
      disableSortBy: false,
      minWidth: 50,
      showToOwner: true,
      showByDefault: false,
    },
    {
      type: 'date',
      Header: 'Availability Date',
      id: 'seafarer_contact_log.availability_date',
      name: 'seafarer_contact_log[0].availability_date',
      accessor: function avalilabilityDateAccessor(row) {
        return (
          <div>
            {row?.seafarer_contact_log[0]?.availability_date
              ? dateAsString(stringAsDate(row?.seafarer_contact_log[0]?.availability_date))
              : '---'}
          </div>
        );
      },
      order: 26,
      disableSortBy: false,
      minWidth: 30,
      showByDefault: false,
    },
    {
      type: 'text',
      Header: 'Availability Remark',
      id: 'seafarer_contact_log.availability_remarks',
      name: 'seafarer_contact_log[0].availability_remarks',
      accessor: function avalilabilityDRemarkAccessor(row) {
        return <div>{row?.seafarer_contact_log[0]?.availability_remarks ?? '---'}</div>;
      },
      order: 27,
      disableSortBy: false,
      minWidth: 30,
      showByDefault: false,
    },
    {
      type: 'text',
      Header: 'Nearest Airport',
      id: 'seafarer_person.nearest_airport',
      name: 'seafarer_person.nearest_airport',
      accessor: function nearestAirportAccessor(row) {
        return <div>{row?.seafarer_person?.nearest_airport ?? '---'}</div>;
      },
      order: 28,
      disableSortBy: false,
      minWidth: 30,
      showByDefault: false,
    },
    {
      type: 'text',
      Header: 'Examination',
      id: 'seafarer_person.current_exam_status',
      name: 'seafarer_person.current_exam_status',
      accessor: function examinationAccessor(row) {
        return (
          <div> {getSeafarerStatus(examStatusJson, row?.seafarer_person?.current_exam_status)}</div>
        );
      },
      order: 29,
      disableSortBy: false,
      minWidth: 30,
      showByDefault: false,
    },
    {
      type: 'text',
      Header: 'Framo Experience',
      id: 'framo_experience',
      name: 'framo_experience',
      handleExportOptionalFn: (item: boolean) => formatYesNo(item),
      accessor: (row) => formatYesNo(row?.framo_experience),
      order: 30,
      disableSortBy: false,
      minWidth: 30,
      showByDefault: false,
    },
    {
      type: 'text',
      Header: 'Cargo Handling Experience',
      id: 'cargo_experience',
      name: 'cargo_experience',
      accessor: (row) => row?.cargo_experience ?? '---',
      order: 31,
      disableSortBy: false,
      minWidth: 30,
      showByDefault: false,
    },
    {
      type: 'text',
      Header: 'Additional Experience',
      id: 'additional_experience',
      name: 'additional_experience',
      accessor: (row) => row?.additional_experience ?? '---',
      order: 32,
      disableSortBy: false,
      minWidth: 30,
      showByDefault: false,
    },
    {
      type: 'text',
      Header: 'NTBE',
      id: 'not_to_be_employed',
      name: 'not_to_be_employed',
      handleExportOptionalFn: (item) => formatYesNo(item),
      accessor: (row) => formatYesNo(row?.not_to_be_employed),
      order: 33,
      disableSortBy: false,
      minWidth: 30,
      showByDefault: false,
    },
    {
      type: 'text',
      Header: 'NTBE Reason',
      id: 'not_to_be_employed_reason',
      name: 'not_to_be_employed_reason',
      accessor: (row) =>
        row?.not_to_be_employed_reason === null ? '---' : row.not_to_be_employed_reason,
      order: 34,
      disableSortBy: false,
      minWidth: 30,
      showByDefault: false,
    },
    {
      type: 'text',
      Header: 'Country of Birth',
      id: 'seafarer_person.country_of_birth.value',
      name: 'seafarer_person.country_of_birth.value',
      accessor: function countryOfBirthAccessor(row) {
        return <div>{row?.seafarer_person?.country_of_birth?.value ?? '---'}</div>;
      },
      order: 35,
      disableSortBy: false,
      minWidth: 30,
      showByDefault: false,
    },
    {
      type: 'text',
      Header: 'Gender',
      id: 'seafarer_person.gender',
      name: 'seafarer_person.gender',
      accessor: function genderAccessor(row) {
        return <div>{row?.seafarer_person?.gender ?? '---'}</div>;
      },
      order: 36,
      disableSortBy: false,
      minWidth: 30,
      showByDefault: false,
    },
    {
      type: 'text',
      Header: 'Vegeterian',
      id: 'seafarer_person.vegetarian',
      name: 'seafarer_person.vegetarian',
      accessor: (row) => formatYesNo(row?.seafarer_person?.vegetarian),
      order: 37,
      disableSortBy: false,
      minWidth: 30,
      showByDefault: false,
    },
    {
      type: 'text',
      Header: 'Smoking',
      id: 'seafarer_person.smoking',
      name: 'seafarer_person.smoking',
      accessor: (row) => formatYesNo(row?.seafarer_person?.smoking),
      order: 38,
      disableSortBy: false,
      minWidth: 50,
      showByDefault: false,
    },
  ].filter(Boolean);

export const getCrewListColumns = () => [
  {
    type: 'number',
    Header: 'HKID',
    id: 'hkid',
    name: 'hkid',
    accessor: (row) => parentHKIDLink(row),
    order: 1,
    disableSortBy: false,
    minWidth: 80,
    sticky: window.innerWidth > 960 ? 'left' : null,
    showToOwner: true,
  },
  {
    type: 'text',
    Header: 'Name',
    id: 'seafarer_person.first_name',
    name: 'seafarer_person.first_name',
    accessor: function nameAccessor(row) {
      return (
        <div>
          {row?.seafarer_person?.first_name || row?.seafarer_person?.last_name ? (
            <CrewMemberNameStyle row={row} />
          ) : (
            '---'
          )}
        </div>
      );
    },
    order: 2,
    disableSortBy: false,
    minWidth: 200,
    sticky: window.innerWidth > 960 ? 'left' : null,
    showToOwner: true,
  },
  {
    type: 'text',
    Header: 'Rank',
    id: 'seafarer_person:seafarer_status_history:seafarer_rank.sortpriority',
    name: 'seafarer_person.seafarer_status_history[0].seafarer_rank.sortpriority',
    accessor: function rankAccessor(row) {
      return (
        <div>
          {row?.seafarer_person?.seafarer_status_history[0]?.seafarer_rank.value ??
            row?.seafarer_rank?.value ??
            '---'}
        </div>
      );
    },
    order: 3,
    disableSortBy: false,
    minWidth: 50,
    showToOwner: true,
  },
  {
    type: 'date',
    Header: 'End of Contract',
    id: 'seafarer_person:seafarer_status_history.expected_contract_end_date',
    name: 'seafarer_person.seafarer_status_history[0].expected_contract_end_date',
    accessor: function endOfContractAccessor(row) {
      return (
        <div>
          {row?.seafarer_person?.seafarer_status_history[0]?.expected_contract_end_date ? (
            <EndOfContractStyling
              value={row?.seafarer_person?.seafarer_status_history[0]?.expected_contract_end_date}
            />
          ) : (
            '---'
          )}
        </div>
      );
    },
    order: 4,
    disableSortBy: false,
    minWidth: 125,
    showToOwner: true,
  },
  {
    Header: 'Replacement',
    id: 'replacement',
    accessor: function replacementAccessor(row) {
      return <div>{row?.id ? <ReplaceButton seafarerId={row.id} /> : '---'}</div>;
    },
    order: 5,
    disableSortBy: true,
    minWidth: 150,
  },
  {
    type: 'text',
    Header: 'Account Status',
    id: 'seafarer_person.current_account_status',
    name: 'seafarer_person.current_account_status',
    accessor: function accountStatusAccessor(row) {
      return (
        <div>
          {getSeafarerStatus(accountStatusJson, row?.seafarer_person?.current_account_status)}
        </div>
      );
    },
    order: 7,
    disableSortBy: false,
    minWidth: 50,
  },
  {
    type: 'text',
    Header: 'Journey Status',
    id: 'seafarer_person.current_journey_status',
    name: 'seafarer_person.current_journey_status',
    accessor: function accountStatusAccessor(row) {
      return (
        <div>
          {getSeafarerStatus(journeyStatusJson, row?.seafarer_person?.current_journey_status) ??
            '---'}
        </div>
      );
    },
    order: 8,
    disableSortBy: false,
    minWidth: 120,
    showToOwner: true,
  },
  {
    type: 'text',
    Header: 'Tech Group',
    id: 'seafarer_person:seafarer_status_history.vessel_tech_group',
    name: 'seafarer_person.seafarer_status_history[0].vessel_tech_group',
    accessor: function techGroupAccessor(row) {
      return (
        <div>{row?.seafarer_person?.seafarer_status_history[0]?.vessel_tech_group ?? '---'}</div>
      );
    },
    order: 9,
    disableSortBy: false,
    minWidth: 90,
  },
  {
    type: 'text',
    Header: 'Reporting Office',
    id: 'seafarer_reporting_office.value',
    name: 'seafarer_reporting_office.value',
    accessor: function reportingOfficeAccessor(row) {
      return <div>{row?.seafarer_reporting_office?.value ?? '---'}</div>;
    },
    order: 10,
    disableSortBy: false,
    minWidth: 50,
  },
  {
    type: 'text',
    Header: 'Nationality',
    id: 'seafarer_person:nationality.value',
    name: 'seafarer_person.nationality.value',
    accessor: function addedByAccessor(row) {
      return <div>{row?.seafarer_person?.nationality?.value ?? '---'}</div>;
    },
    order: 11,
    disableSortBy: false,
    minWidth: 120,
    showToOwner: true,
  },
  {
    type: 'text',
    Header: 'Passport No. ',
    id: 'seafarer_person:passports.number',
    name: 'seafarer_person.passports[0].number',
    accessor: function passportNoAccessor(row) {
      return <div>{row?.seafarer_person?.passports[0]?.number ?? '---'}</div>;
    },
    order: 12,
    disableSortBy: false,
    minWidth: 50,
    showToOwner: true,
  },
  {
    type: 'text',
    Header: "Seaman's Book",
    id: 'seafarer_person:seaman_books.number',
    name: 'seafarer_person.seaman_books[0].number',
    accessor: function seamanBookAccessor(row) {
      return <div>{row?.seafarer_person?.seaman_books[0]?.number ?? '---'}</div>;
    },
    order: 13,
    disableSortBy: false,
    minWidth: 50,
    showToOwner: true,
  },
  {
    type: 'text',
    Header: 'Date of Joining',
    id: 'seafarer_person:seafarer_status_history.status_date',
    name: 'seafarer_person.seafarer_status_history[0].status_date',
    accessor: function dateOfJoiningAccessor(row) {
      return (
        <div>
          {row?.seafarer_person?.seafarer_status_history[0]?.status_date
            ? dateAsString(
                stringAsDate(row?.seafarer_person?.seafarer_status_history[0]?.status_date),
              )
            : '---'}
        </div>
      );
    },
    order: 14,
    disableSortBy: false,
    minWidth: 50,
    showToOwner: true,
  },
  {
    type: 'text',
    Header: 'Repatriation Port',
    id: 'seafarer_person:seafarer_status_history.repatriation_port',
    name: 'seafarer_person.seafarer_status_history[0].repatriation_port',
    accessor: function repatriationPortAccessor(row) {
      return (
        <div>
          {replaceEmptyWithDashes(
            row?.seafarer_person?.seafarer_status_history[0]?.repatriation_port,
          )}
        </div>
      );
    },
    order: 15,
    disableSortBy: false,
    minWidth: 30,
    showToOwner: true,
  },
  {
    type: 'text',
    Header: 'Embarkation Port',
    id: 'seafarer_person:seafarer_status_history.embarkation_port',
    name: 'seafarer_person.seafarer_status_history[0].embarkation_port',
    accessor: function embarkationPortAccessor(row) {
      return (
        <div>
          {replaceEmptyWithDashes(
            row?.seafarer_person?.seafarer_status_history[0]?.embarkation_port,
          )}
        </div>
      );
    },
    order: 16,
    disableSortBy: false,
    minWidth: 20,
    showToOwner: true,
  },
  {
    type: 'text',
    Header: 'Years with FML',
    id: 'duration_with_company',
    name: 'experience_summary.duration_with_company',
    handleExportOptionalFn: covertDaysToYear,
    accessor: function yearswithCompanyAccessor(row) {
      return <div>{covertDaysToYear(row?.experience_summary?.duration_with_company)}</div>;
    },
    order: 19,
    disableSortBy: true,
    minWidth: 50,
  },
  {
    type: 'number',
    Header: 'Years in Rank',
    id: 'duration_in_target_rank',
    name: 'experience_summary.duration_in_target_rank',
    handleExportOptionalFn: covertDaysToYear,
    accessor: function yearsInRankAccessor(row) {
      return (
        <div>
          <ShowYearsInRank
            value={covertDaysToYear(row?.experience_summary?.duration_in_target_rank)}
          />
        </div>
      );
    },
    order: 20,
    disableSortBy: true,
    minWidth: 30,
  },
  {
    type: 'text',
    Header: 'Years on this Vessel Type',
    id: 'target_vessel_type.value',
    name: 'experience_summary.duration_on_target_vessel_type',
    handleExportOptionalFn: covertDaysToYear,
    accessor: function yearsOnVesselTypeAccessor(row) {
      return (
        <div>
          <ShowYearsInVesselType
            value={covertDaysToYear(row?.experience_summary?.duration_on_target_vessel_type)}
          />
        </div>
      );
    },
    order: 21,
    disableSortBy: true,
    minWidth: 30,
  },
  {
    type: 'text',
    Header: 'Years on all Vessel Type',
    id: 'duration_on_all_vessel',
    name: 'experience_summary.duration_on_all_vessel_type',
    handleExportOptionalFn: covertDaysToYear,
    accessor: function yearsOnAllVesselTypeAccessor(row) {
      return (
        <div>
          {covertDaysToYear(
            row?.experience_summary?.duration_on_all_vessel_type ??
              row?.experience_summary?.duration_on_all_vessel_type,
          )}
        </div>
      );
    },
    order: 22,
    disableSortBy: true,
    minWidth: 30,
  },
  {
    type: 'text',
    Header: 'Months on this vessel tour',
    id: 'experience_summary.duration_on_target_vessel',
    name: 'experience_summary.duration_on_target_vessel',
    handleExportOptionalFn: convertDaysToMonthAndDay,
    accessor: function monthsOnVesselTourTypeAccessor(row) {
      return (
        <div>{convertDaysToMonthAndDay(row?.experience_summary?.duration_on_target_vessel)}</div>
      );
    },
    order: 23,
    disableSortBy: true,
    minWidth: 30,
  },
  {
    type: 'date',
    Header: 'Availability Date',
    id: 'seafarer_contact_log.availability_date',
    name: 'seafarer_contact_log[0].availability_date',
    accessor: function avalilabilityDateAccessor(row) {
      return (
        <div>
          {row?.seafarer_contact_log[0]?.availability_date
            ? dateAsString(stringAsDate(row?.seafarer_contact_log[0]?.availability_date))
            : '---'}
        </div>
      );
    },
    order: 24,
    disableSortBy: false,
    minWidth: 30,
  },
  {
    type: 'text',
    Header: 'Availability Remark',
    id: 'seafarer_contact_log.availability_remarks',
    name: 'seafarer_contact_log[0].availability_remarks',
    accessor: function avalilabilityDRemarkAccessor(row) {
      return <div>{row?.seafarer_contact_log[0]?.availability_remarks ?? '---'}</div>;
    },
    order: 25,
    disableSortBy: false,
    minWidth: 30,
  },
  {
    type: 'text',
    Header: 'Nearest Airport',
    id: 'seafarer_person.nearest_airport',
    name: 'seafarer_person.nearest_airport',
    accessor: function nearestAirportAccessor(row) {
      return <div>{row?.seafarer_person?.nearest_airport ?? '---'}</div>;
    },
    order: 26,
    disableSortBy: false,
    minWidth: 30,
  },
  {
    type: 'text',
    Header: 'Examination',
    id: 'seafarer_person.current_exam_status',
    name: 'seafarer_person.current_exam_status',
    accessor: function examinationAccessor(row) {
      return (
        <div> {getSeafarerStatus(examStatusJson, row?.seafarer_person?.current_exam_status)}</div>
      );
    },
    order: 27,
    disableSortBy: false,
    minWidth: 30,
  },
  {
    type: 'text',
    Header: 'Framo Experience',
    id: 'framo_experience',
    name: 'framo_experience',
    handleExportOptionalFn: (item: boolean) => formatYesNo(item),
    accessor: (row) => formatYesNo(row?.framo_experience),
    order: 28,
    disableSortBy: false,
    minWidth: 30,
  },
  {
    type: 'text',
    Header: 'Cargo Handling Experience',
    id: 'cargo_experience',
    name: 'cargo_experience',
    accessor: (row) => row?.cargo_experience ?? '---',
    order: 29,
    disableSortBy: false,
    minWidth: 30,
  },
  {
    type: 'text',
    Header: 'Additional Experience',
    id: 'additional_experience',
    name: 'additional_experience',
    accessor: (row) => row?.additional_experience ?? '---',
    order: 30,
    disableSortBy: false,
    minWidth: 30,
  },
  {
    type: 'text',
    Header: 'NTBE',
    id: 'not_to_be_employed',
    name: 'not_to_be_employed',
    handleExportOptionalFn: (item) => formatYesNo(item),
    accessor: (row) => formatYesNo(row?.not_to_be_employed),
    order: 31,
    disableSortBy: false,
    minWidth: 30,
  },
  {
    type: 'text',
    Header: 'NTBE Reason',
    id: 'not_to_be_employed_reason',
    name: 'not_to_be_employed_reason',
    accessor: (row) =>
      row?.not_to_be_employed_reason === null ? '---' : row.not_to_be_employed_reason,
    order: 32,
    disableSortBy: false,
    minWidth: 30,
  },
  {
    type: 'date',
    Header: 'Date of Birth',
    id: 'seafarer_person.date_of_birth',
    name: 'seafarer_person.date_of_birth',
    accessor: function dateOfBirthAccessor(row) {
      return (
        <div>
          {row?.seafarer_person?.date_of_birth
            ? dateAsString(stringAsDate(row?.seafarer_person?.date_of_birth))
            : '---'}
        </div>
      );
    },
    order: 17,
    disableSortBy: false,
    minWidth: 30,
    showToOwner: true,
  },
  {
    type: 'text',
    Header: 'Place of Birth',
    id: 'seafarer_person.place_of_birth',
    name: 'seafarer_person.place_of_birth',
    accessor: function placeOfBirthAccessor(row) {
      return <div>{row?.seafarer_person?.place_of_birth ?? '---'}</div>;
    },
    order: 18,
    disableSortBy: false,
    minWidth: 30,
    showToOwner: true,
  },
  {
    type: 'text',
    Header: 'Country of Birth',
    id: 'seafarer_person.country_of_birth.value',
    name: 'seafarer_person.country_of_birth.value',
    accessor: function countryOfBirthAccessor(row) {
      return <div>{row?.seafarer_person?.country_of_birth?.value ?? '---'}</div>;
    },
    order: 33,
    disableSortBy: false,
    minWidth: 30,
  },
  {
    type: 'text',
    Header: 'Gender',
    id: 'seafarer_person.gender',
    name: 'seafarer_person.gender',
    accessor: function genderAccessor(row) {
      return <div>{row?.seafarer_person?.gender ?? '---'}</div>;
    },
    order: 34,
    disableSortBy: false,
    minWidth: 30,
  },
  {
    type: 'text',
    Header: 'Vegeterian',
    id: 'seafarer_person.vegetarian',
    name: 'seafarer_person.vegetarian',
    accessor: (row) => formatYesNo(row?.seafarer_person?.vegetarian),
    order: 35,
    disableSortBy: false,
    minWidth: 30,
  },
  {
    type: 'text',
    Header: 'Smoking',
    id: 'seafarer_person.smoking',
    name: 'seafarer_person.smoking',
    accessor: (row) => formatYesNo(row?.seafarer_person?.smoking),
    order: 36,
    disableSortBy: false,
    minWidth: 50,
  },
  {
    Header: 'Replace With',
    id: 'replace_with',
    accessor: function ReplaceWithAccessor(row) {
      return (
        <div>
          {
            <ReplaceWith
              replaced_by={row?.seafarer_person?.seafarer_status_history[0]?.replaced_by}
              type="name"
              tableName="crew-list"
            />
          }
        </div>
      );
    },
    order: 37,
    disableSortBy: false,
    minWidth: 50,
  },
];

const crewList = {
  Header: 'Crew List',
  id: 'crew_list_link',
  accessor: (row) => (
    <CrewListLink vesselId={row?.seafarer_person?.seafarer_status_history[0]?.vessel_id} />
  ),
  width: 120,
  order: 6,
  disableSortBy: true,
};

export const contractExpiryColumns = [
  {
    type: 'text',
    Header: 'Vessel',
    id: 'vessel_name',
    name: 'seafarer_person.seafarer_status_history[0].vessel_name',
    accessor: (row) => (
      <VesselNameLink
        ownershipId={row?.seafarer_person?.seafarer_status_history[0]?.vessel_ownership_id}
        vesselName={row?.seafarer_person?.seafarer_status_history[0]?.vessel_name}
      />
    ),
    width: 120,
    order: 0,
    sticky: window.innerWidth > 960 ? 'left' : null,
  },
  ...getCrewListColumns().slice(0, 2),
  {
    type: 'text',
    Header: 'Rank',
    id: 'seafarer_person:seafarer_status_history:seafarer_rank.value',
    name: 'seafarer_person.seafarer_status_history[0].seafarer_rank.value',
    accessor: function rankAccessor(row) {
      return (
        <div>{row?.seafarer_person?.seafarer_status_history[0]?.seafarer_rank.value ?? '---'}</div>
      );
    },
    order: 3,
    disableSortBy: false,
    minWidth: 50,
  },
  ...getCrewListColumns().slice(3, 37),
];
contractExpiryColumns.splice(5, 0, crewList);
contractExpiryColumns.splice(-1);


const AgeHighlighter = ({ dateOfBirth }) => {
  if (!dateOfBirth) return <>---</>;

  const age = moment().diff(dateOfBirth, 'years');
  let color = 'neutral';
  if (age >= 65) {
    color = 'red';
  } else if (age >= 60) {
    color = 'orange';
  }

  return <span>{dateAsString(stringAsDate(dateOfBirth))} <span className={`oval ${color}`}>{`${age}yrs`}</span></span>;
};

const ExpiryDateHighlighter = ({ date }) => {
  if (!date) return <>---</>;

  const now = moment();
  const targetDate = moment(date);
  const daysDiff = now.diff(targetDate, 'days');

  let color = 'neutral';
  let diffText = '';

  if (daysDiff >= 0) {
    color = 'red';
    diffText = daysDiff === 0 ? '0D' : `-${daysDiff}D`;
  } else {
    const futureDaysDiff = Math.abs(daysDiff);
    if (futureDaysDiff <= 30) {
      color = 'orange';
    }
    diffText = `+${futureDaysDiff}D`;
  }

  return (
    <span>
      {dateAsString(stringAsDate(date))}{' '}
      <span className={`oval ${color}`}>{diffText}</span>
    </span>
  );
};

const PlanStatusHighlighter = ({ status }) => {
  if (!status) return <>---</>;

  let color = '';
  const statusValue = getSeafarerStatus(journeyStatusJson, status) ?? '---';

  switch (status) {
    case 'crew_assignment_approved':
    case 'travelling':
      color = 'status-badge-success';
      break;
    case 'recommended':
    case 'recommended_with_deviation':
      color = 'status-badge-warning';
      break;
    default:
  }

  return <span className={`status-badge ${color}`}>{statusValue}</span>;
};

export const YesOrNoHighlighter = ({ value }) => {
  const text = formatYesNo(value);
  if (text === '---') return <>---</>;

  const color = text === 'Yes' ? 'green' : 'red';

  return <span className={`oval ${color}`}>{text}</span>;
};

export const availableSeafarerColumn: any = [
  {
    ...getCrewListColumns().find(c => c.id === 'hkid'),
    sticky: 'left',
    disableSortBy: false,
    showByDefault: true,
    order: 1,
  },
  {
    ...getCrewListColumns().find(c => c.id === 'seafarer_person.first_name'),
    Header: 'Available Seafarer',
    sticky: 'left',
    disableSortBy: false,
    showByDefault: true,
    order: 2,
  },
  {
    type: 'text',
    Header: 'Rank',
    id: 'seafarer_rank.value',
    name: 'seafarer_rank.value',
    accessor: (row) => row?.seafarer_rank?.value || '---',
    order: 3,
    minWidth: 140,
    showByDefault: true,
    disableSortBy: true,
  },
  {
    type: 'date',
    Header: 'Date of Birth and Age',
    id: 'seafarer_person.date_of_birth',
    name: 'seafarer_person.date_of_birth',
    accessor: (row) => <AgeHighlighter dateOfBirth={row?.seafarer_person?.date_of_birth} />,
    minWidth: 185,
    showToOwner: true,
    showByDefault: true,
    disableSortBy: false,
    order: 4,
  },
  {
    type: 'date',
    Header: 'Available Date',
    id: 'seafarer_contact_log.availability_date',
    name: 'seafarer_contact_log[0].availability_date',
    accessor: (row) => <ExpiryDateHighlighter date={row?.seafarer_contact_log[0]?.availability_date} />,
    minWidth: 185,
    showByDefault: true,
    disableSortBy: false,
    order: 5,
  },
  {
    type: 'date',
    Header: 'Sign-off Date',
    id: 'seafarer_experience.end_date',
    name: 'latest_experience.end_date',
    accessor: (row) => row?.latest_experience?.end_date ? dateAsString(stringAsDate(row.latest_experience.end_date)) : '---',
    minWidth: 135,
    showByDefault: true,
    disableSortBy: false,
    order: 6,
  },
  {
    ...getCrewListColumns().find(c => c.id === 'seafarer_reporting_office.value'),
    showByDefault: true,
    disableSortBy: true,
    minWidth: 140,
    order: 7,
  },
  {
    type: 'text',
    Header: 'Previous Vessel',
    id: 'seafarer_experience.vessel_name',
    name: 'latest_experience.vessel_name',
    accessor: (row) => (
      <VesselNameLink
        ownershipId={row?.latest_experience?.vessel_ownership_id}
        vesselName={row?.latest_experience?.vessel_name}
      />
    ),
    minWidth: 150,
    showByDefault: true,
    disableSortBy: true,
    order: 8,
  },
  {
    type: 'text',
    Header: 'Prev. Vessel Type',
    id: 'seafarer_experience.vessel_type',
    name: 'latest_experience.vessel_type',
    accessor: (row) => row?.latest_experience?.vessel_type ?? '---',
    minWidth: 150,
    showByDefault: true,
    disableSortBy: true,
    order: 9,
  },
  {
    type: 'text',
    Header: 'Previous Owner',
    id: 'seafarer_experience.owner_name',
    name: 'latest_experience.owner_name',
    accessor: (row) => row?.latest_experience?.owner_name ?? '---',
    minWidth: 150,
    showByDefault: true,
    disableSortBy: true,
    order: 10,
  },
  {
    type: 'text',
    Header: 'Previous Group',
    id: 'seafarer_experience.vessel_tech_group',
    name: 'latest_experience.vessel_tech_group',
    accessor: (row) => row?.latest_experience?.vessel_tech_group ?? '---',
    minWidth: 150,
    showByDefault: true,
    disableSortBy: true,
    order: 11,
  },
  {
    type: 'text',
    Header: 'Planned Vessel',
    id: 'crew_plan.vessel_name',
    name: 'crew_plan.vessel_name',
    accessor: (row) => row?.crew_plan?.vessel_name ?? '---',
    minWidth: 150,
    showByDefault: true,
    disableSortBy: true,
    order: 12,
  },
  {
    type: 'text',
    Header: 'Plan Status',
    id: 'crew_plan.seafarer_journey_status',
    name: 'crew_plan.seafarer_journey_status',
    accessor: (row) =>
      <PlanStatusHighlighter status={row?.crew_plan?.seafarer_journey_status} />,
    minWidth: 236,
    showByDefault: true,
    disableSortBy: true,
    order: 13,
  },
  {
    ...getCrewListColumns().find(c => c.id === 'duration_in_target_rank'),
    Header: 'Exp. in Rank',
    showByDefault: true,
    disableSortBy: true,
    order: 14,
  },
  {
    ...getCrewListColumns().find(c => c.id === 'duration_with_company'),
    Header: 'Exp. with FML',
    showByDefault: true,
    disableSortBy: true,
    order: 15,
  },
  {
    ...getCrewListColumns().find(c => c.id === 'target_vessel_type.value'),
    Header: 'Exp. in Vessel Type',
    showByDefault: true,
    disableSortBy: true,
    order: 16,
  },
  {
    ...getCrewListColumns().find(c => c.id === 'duration_on_all_vessel'),
    Header: 'Exp. in All Vessel Type',
    showByDefault: true,
    disableSortBy: true,
    order: 17,
  },
  {
    ...getCrewListColumns().find(c => c.id === 'seafarer_person:nationality.value'),
    showByDefault: true,
    disableSortBy: true,
    order: 18,
  },
  {
    type: 'date',
    Header: 'Passport Expiry',
    id: 'seafarer_person:passports.date_of_expiry',
    name: 'seafarer_person.passports[0].date_of_expiry',
    accessor: (row) => <ExpiryDateHighlighter date={row?.seafarer_person?.passports[0]?.date_of_expiry} />,
    minWidth: 185,
    showByDefault: false,
    disableSortBy: false,
    order: 19,
  },
  {
    type: 'date',
    Header: 'US Visa Expiry',
    id: 'seafarer_person:seafarer_document:seafarer_doc_visa.date_of_expiry',
    name: 'seafarer_person.seafarer_document[0].seafarer_doc_visa.date_of_expiry',
    accessor: (row) => <ExpiryDateHighlighter date={row?.seafarer_person?.seafarer_document?.find(doc => doc.type === 'visa')?.seafarer_doc_visa?.date_of_expiry} />,
    minWidth: 185,
    showByDefault: false,
    disableSortBy: false,
    order: 20,
  },
  {
    type: 'date',
    Header: 'Seamen’s Book Expiry',
    id: 'seafarer_person:seaman_books.date_of_expiry',
    name: 'seafarer_person.seaman_books[0].date_of_expiry',
    accessor: (row) => <ExpiryDateHighlighter date={row?.seafarer_person?.seaman_books[0]?.date_of_expiry} />,
    minWidth: 190,
    showByDefault: false,
    disableSortBy: false,
    order: 21,
  },
  {
    type: 'date',
    Header: 'COC Expiry',
    id: 'seafarer_person:seafarer_document:seafarer_doc_certificate_of_competency.date_of_expiry',
    name: 'seafarer_person.seafarer_document[0].seafarer_doc_certificate_of_competency.date_of_expiry',
    accessor: (row) => <ExpiryDateHighlighter date={row?.seafarer_person?.seafarer_document?.find(doc => doc.type === 'certificate_of_competency')?.seafarer_doc_certificate_of_competency?.date_of_expiry} />,
    minWidth: 185,
    showByDefault: false,
    disableSortBy: true,
    order: 22,
  },
  {
    ...getCrewListColumns().find(c => c.id === 'seafarer_person.gender'),
    showByDefault: false,
    disableSortBy: true,
    order: 23,
  },
  {
    type: 'text',
    Header: 'Document in Hand',
    id: 'seafarer_contact_log.docs_in_hand',
    name: 'seafarer_contact_log[0].docs_in_hand',
    accessor: (row) => <YesOrNoHighlighter value={row?.seafarer_contact_log[0]?.docs_in_hand} />,
    minWidth: 135,
    showByDefault: false,
    disableSortBy: true,
    order: 24,
  },
  {
    type: 'date',
    Header: 'Yellow Fever',
    id: 'seafarer_person:seafarer_document.seafarer_doc_other_document.date_of_expiry',
    name: 'seafarer_person.seafarer_document[0].seafarer_doc_other_document.date_of_expiry',
    accessor: (row) => <ExpiryDateHighlighter date={row?.seafarer_person?.seafarer_document?.find(doc => doc.type === 'other_document')?.seafarer_doc_other_document?.date_of_expiry} />,
    minWidth: 185,
    showByDefault: false,
    disableSortBy: true,
    order: 25,
  },
  {
    type: 'text',
    Header: 'Phone',
    id: 'seafarer_person.seafarer_contacts_phone',
    name: 'seafarer_person.seafarer_contacts_phone',
    accessor: (row) => {
      const contactNumbers = row?.seafarer_person?.seafarer_contacts
        ?.filter(c => ['telephone_number', 'mobile_number'].includes(c.contact_type))
        ?.map((item) => {
          return <div key={item.id}>{item?.contact}</div>;
        });
      return contactNumbers?.length ? contactNumbers : '---';
    },
    minWidth: 125,
    showByDefault: true,
    disableSortBy: true,
    order: 26,
  },
  {
    type: 'text',
    Header: 'Email',
    id: 'seafarer_person.seafarer_contacts_email',
    name: 'seafarer_person.seafarer_contacts_email',
    accessor: (row) => {
      const contactEmails = row?.seafarer_person?.seafarer_contacts
        ?.filter(c => c.contact_type === 'email')
        ?.map((item) => {
          return <div key={item.id}>{item?.contact}</div>;
        });
      return contactEmails?.length ? contactEmails : '---';
    },
    minWidth: 200,
    showByDefault: true,
    disableSortBy: true,
    order: 27,
  },
  {
    type: 'date',
    Header: 'Last Contacted',
    id: 'seafarer_contact_log.contact_date',
    name: 'seafarer_contact_log[0].contact_date',
    accessor: (row) => row?.seafarer_contact_log[0]?.contact_date ? dateAsString(stringAsDate(row.seafarer_contact_log[0].contact_date)) : '---',
    minWidth: 145,
    showByDefault: true,
    disableSortBy: false,
    order: 28,
  },
  {
    type: 'date',
    Header: 'Next Contact Date',
    id: 'seafarer_contact_log.next_contact_date',
    name: 'seafarer_contact_log[0].next_contact_date',
    accessor: (row) => row?.seafarer_contact_log[0]?.next_contact_date ? dateAsString(stringAsDate(row.seafarer_contact_log[0].next_contact_date)) : '---',
    minWidth: 165,
    showByDefault: true,
    disableSortBy: false,
    order: 29,
  },
  {
    type: 'text',
    Header: 'Availability Remarks',
    id: 'seafarer_contact_log.availability_remarks',
    name: 'seafarer_contact_log[0].availability_remarks',
    accessor: (row) => row?.seafarer_contact_log[0]?.availability_remarks ?? '---',
    minWidth: 200,
    sticky: 'right',
    showByDefault: true,
    disableSortBy: true,
    order: 30,
  },
];
