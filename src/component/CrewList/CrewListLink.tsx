import React, { SyntheticEvent } from 'react';
import { Button } from 'react-bootstrap';

interface Props {
  vesselId: number;
  eventTracker: Function;
}

const CrewListLink = ({ vesselId, eventTracker }: Props) => {
  const routeToCrewList = (event: SyntheticEvent, vesselId: number) => {
    event.stopPropagation();
    eventTracker('crewListLink', 'Routes to Crew List');
    window.open(`/seafarer/crew-list/vessel/${vesselId}`, '_blank');
  };

  return (
    <Button
      onClick={(e) => routeToCrewList(e, vesselId)}
      variant="link"
      className="button-link btn btn-link"
    >
      Crew List
    </Button>
  );
};

export default CrewListLink;
