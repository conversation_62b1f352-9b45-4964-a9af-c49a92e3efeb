import React, { useContext } from 'react';
import { TabContext } from '../../pages/List';
import SEARCH_TYPES from '../../util/advance-search/search-types';

const ShowYearsInRank = ({ value }) => {
  const { activeKey, apiQuery } = useContext(TabContext);
  let years = value;
  if (
    activeKey === 'available-seafarers' &&
    !apiQuery.match(SEARCH_TYPES().find((i) => i.type === 'target_rank').queryKey)
  )
    years = '---';
  return <div>{years}</div>;
};

export default ShowYearsInRank;
