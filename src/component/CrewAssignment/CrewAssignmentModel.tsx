import _ from 'lodash';
import React from 'react';
import { seafarerStatusService } from 'paris2-seafarer-status';
import { Button } from 'react-bootstrap';
import { RouteComponentProps } from 'react-router-dom';
import moment from 'moment';
import { capitalizeFirstLetter, Dash, dateAsString } from '../../model/utils';
import CrewListLink from '../CrewList/CrewListLink';
import VesselNameLink from '../CrewList/VesselNameLink';
import { seafarerStatus } from '../../model/constants';
import { WAGES_STATUS } from '../../constants/seafarer-wages';
import { Seafarer, SeafarerStatusHistory } from '../../types/seafarerInterfaces';
import { SeafarerWages } from '../../types/wagesInterfaces';
import { RECOMMENDATION_APPROVAL_TOP_RANKS } from '../../constants/recommendation';
import { RANK_VALUES } from '../../constants/formSections';
import UpdateContractDateModal from '../Details/change-contract-end-date-modal';

const journeyStatusJson = seafarerStatusService.getJourneyStatus();

const recommendationStatus = {
  recommendationApproval: {
    recommended: 'recommendationApproval',
    recommended_with_deviation: 'recommendationDeviationApproval',
  },
  recommendationApprovalExceptTopRanks: {
    recommended: 'recommendationApprovalExceptTopRanks',
    recommended_with_deviation: 'recommendationDeviationApprovalExceptTopRanks',
  },
};
const getWagesText = (wages?: SeafarerWages) => {
  return wages?.amount_total ? `${wages.amount_unit?.toUpperCase()} ${wages.amount_total}` : Dash;
};
interface CrewAssignmentProps {
  data: SeafarerStatusHistory;
  wagesData?: SeafarerWages;
  seafarer: Seafarer;
  history: RouteComponentProps['history'];
  eventTracker: (type: string, value: string | number) => void;
  isUpdateEnabled: boolean;
  handleActionCancel?: (value: number) => void;
  handleActionApprove?: () => void;
  handleActionReject?: () => void;
  handleDeviationStatusClick?: () => void;
  isDisableApproveBtn?: boolean;
  dropdownData?: object;
  roleConfig?: any;
  handleChangeContractDate?: () => void;
  handleSetToTravel?: () => void;
  isSetToTravelVisible: boolean;
  handleCancelTravel?: () => void;
}

const CrewAssignmentModel = ({
  data,
  wagesData,
  seafarer,
  history,
  eventTracker,
  isUpdateEnabled,
  handleActionCancel,
  handleActionApprove,
  handleActionReject,
  handleDeviationStatusClick,
  isDisableApproveBtn = false,
  dropdownData,
  roleConfig,
  handleChangeContractDate,
  handleSetToTravel,
  isSetToTravelVisible,
  handleCancelTravel,
}: CrewAssignmentProps) => {
  let topRanks = [];
  if (dropdownData?.ranks) {
    topRanks = dropdownData?.ranks.filter((rank) =>
      RECOMMENDATION_APPROVAL_TOP_RANKS.includes(rank.value),
    );
  }
  const wageStatus = WAGES_STATUS[`${wagesData?.status}`];
  const canChangeContractDate = roleConfig?.seafarer?.edit?.contractEndDate || roleConfig?.seafarer?.edit?.seafarerSuperUser;

  const ApproveButton = ({ seafarerStatus }) => {
    return (
      <Button
        data-testid="approve-button"
        disabled={isDisableApproveBtn}
        variant="outline-primary"
        size="sm"
        onClick={() => handleActionApprove?.(seafarerStatus)}
        style={{ fontWeight: 500 }}
      >
        Approve
      </Button>
    );
  };
  const RejectButton = () => {
    return (
      <Button
        variant="outline-danger"
        size="sm"
        onClick={() => {
          handleActionReject?.();
        }}
        style={{ fontWeight: 500 }}
      >
        Reject
      </Button>
    );
  };
  const UpdateButton = () => {
    const openUpdateWagesModal = () => {
      eventTracker('updateWagesFromGeneralTab', '');
      history.push('pre-joining/wages/add');
    };
    return (
      <Button
        variant="outline-primary"
        size="sm"
        onClick={openUpdateWagesModal}
        disabled={!isUpdateEnabled}
      >
        Update
      </Button>
    );
  };
  const SetToTravelButton = ({ disabled }) => {
    return (
      <Button
        variant="outline-primary"
        size="sm"
        onClick={handleSetToTravel}
        disabled={disabled}
        data-testid="set-to-travel-button"
      >
        Set to Travel
      </Button>
    );
  };
  const CancelButton = ({ seafarerWagesId }: { seafarerWagesId: number }) => {
    return (
      <Button
        variant="outline-danger"
        size="sm"
        onClick={() => {
          handleActionCancel?.(seafarerWagesId);
        }}
        disabled={!isUpdateEnabled}
      >
        Cancel
      </Button>
    );
  };

  const CancelTravelButton = () => {
    return (
      <Button
        variant="outline-danger"
        size="sm"
        onClick={() => {
          handleCancelTravel?.();
        }}
        style={{ fontWeight: 500 }}
      >
        Cancel Travel
      </Button>
    );
  };

  const onStatusClick = () => {
    if (data?.seafarer_journey_status === seafarerStatus.RECOMMENDED_WITH_DEVIATION) {
      handleDeviationStatusClick?.();
    }
  };

  const hasRecommendationPermission = (journeyStatus, exceptTopRanks = false) => {
    if (exceptTopRanks) {
      return roleConfig?.seafarer?.edit[
        recommendationStatus.recommendationApprovalExceptTopRanks[journeyStatus]
      ];
    }
    return roleConfig?.seafarer?.edit[recommendationStatus.recommendationApproval[journeyStatus]];
  };

  const canApproveRejectBasedOnTechGroup = (journeyStatus) => {
    const isTechGroupUser = roleConfig?.techGroup?.length > 0;
    if (isTechGroupUser) {
      const vesselTechGroup = data?.vessel_tech_group;
      const userTechGroups = roleConfig?.techGroup;
      if (
        vesselTechGroup &&
        journeyStatus === seafarerStatus.RECOMMENDED &&
        userTechGroups.includes(vesselTechGroup)
      ) {
        const approvalKey =
          recommendationStatus.recommendationApprovalExceptTopRanks[journeyStatus] ||
          recommendationStatus.recommendationApproval[journeyStatus];
        return roleConfig?.seafarer?.edit[approvalKey];
      }
      return false;
    }
    return journeyStatus;
  };

  const currentPlanRank = topRanks.find((topRank) => topRank.id === data?.rank_id);
  const showChangeContractDate =
    canChangeContractDate &&
    moment(data?.expected_contract_end_date, moment.ISO_8601, true).isValid() &&
    moment(data?.expected_contract_end_date).isAfter(moment().endOf('day'));

  const endDateColumn = {
    label: 'End Date',
    value: data?.expected_contract_end_date ? (
      <div className="d-flex justify-content-between align-items-center">
        <div>{dateAsString(data.expected_contract_end_date)}</div>
        {showChangeContractDate && (
          <UpdateContractDateModal
            onSubmitCompleted={handleChangeContractDate}
            data={{
              seafarerId: seafarer.id,
              contractStartDate: data.expected_contract_start_date,
              contractEndDate: data.expected_contract_end_date,
            }}
            trigger={<Button size="sm" variant="outline-primary"> Change Date </Button>}
          />
        )}
      </div>
    ) : (
      Dash
    ),
  };

  const crewAssignmentModel = {
    NOT_SIGNED_ON: [
      {
        label: 'Date',
        value: data?.created_at ? dateAsString(data.created_at) : Dash,
      },
      {
        label: 'Vessel',
        value: data?.vessel_ownership_id ? (
          <div className="row justify-content-start">
            <div className="col-auto px-2">
              <VesselNameLink
                ownershipId={data?.vessel_ownership_id}
                vesselName={data?.vessel_name}
                eventTracker={() => { }}
                className="text-left"
              />
            </div>
            <div className="col-auto px-2">
              <CrewListLink vesselId={data?.vessel_id} eventTracker={() => { }} />
            </div>
          </div>
        ) : (
          Dash
        ),
      },
      {
        label: 'Effective Date',
        value: data?.expected_contract_start_date
          ? dateAsString(data.expected_contract_start_date)
          : Dash,
      },
      {
        label: 'Status',
        value: (
          <div className={`status-secondary-${data?.seafarer_account_status?.toLowerCase()}`}>
            <p
              className={`status-text status-text-${data?.seafarer_journey_status?.toLowerCase()} status-no-permission-${data?.seafarer_journey_status?.toLowerCase()}-${data?.no_recommendation_permission
                }`}
              onClick={onStatusClick}
              onKeyDown={(event) => {
                if (event.key === 'Enter' || event.key === ' ') {
                  onStatusClick();
                }
              }}
            >
              {journeyStatusJson[`${data?.seafarer_journey_status}`]?.name ?? Dash}
            </p>
          </div>
        ),
      },
      // Hide in R3 for now, will be enable when working on R2 recommendation
      /* {
        label: 'Action',
        value: (
          <div className="row justify-content-start px-1">
            <div className="col-auto px-2">
              <ApproveButton />
            </div>
            <div className="col-auto px-2">
              <RejectButton />
            </div>
          </div>
        ),
      }, */
      {
        label: 'By',
        value: data?.created_by ?? Dash,
      },
      {
        label: 'Wages',
        value: getWagesText(wagesData),
      },
      {
        label: 'Remark',
        value: (
          <div className="text-justify">
            {capitalizeFirstLetter(data?.seafarer_journey_remarks)
              ? capitalizeFirstLetter(data?.seafarer_journey_remarks)
              : Dash}
          </div>
        ),
      },
    ],
    SIGNED_ON: [
      {
        label: 'Start Date',
        value: data?.expected_contract_start_date
          ? dateAsString(data.expected_contract_start_date)
          : Dash,
      },
      endDateColumn,
      {
        label: 'Vessel',
        value: data?.vessel_ownership_id ? (
          <div className="row justify-content-start">
            <div className="col-auto px-2">
              <VesselNameLink
                ownershipId={data?.vessel_ownership_id}
                vesselName={data?.vessel_name}
                eventTracker={() => { }}
                className="text-left"
              />
            </div>
            <div className="col-auto px-2">
              <CrewListLink vesselId={data?.vessel_id} eventTracker={() => { }} />
            </div>
          </div>
        ) : (
          Dash
        ),
      },
      {
        label: 'Status',
        value: (
          <div className={`status-secondary-${data?.seafarer_account_status?.toLowerCase()}`}>
            {journeyStatusJson[`${data?.seafarer_journey_status}`]?.name ?? Dash}
          </div>
        ),
      },
      {
        label: 'Rank',
        value: seafarer?.seafarer_rank?.value ?? Dash,
      },
      {
        label: 'Wages',
        value: getWagesText(wagesData),
      },
    ],
    UPDATED_WAGES: [
      {
        label: 'New Rank',
        value: (
          <span data-testid="fml-update-wages-and-promotion-new-rank">
            {' '}
            {_.get(wagesData, 'seafarer_promotion[0].is_promotion', false)
              ? _.get(wagesData, 'seafarer_promotion[0].new_rank.value', null) ?? Dash
              : Dash}{' '}
          </span>
        ),
        hide: !_.get(wagesData, 'seafarer_promotion[0].is_promotion', false),
      },
      {
        label: 'Effective Date',
        value: wagesData?.effective_date ? dateAsString(wagesData?.effective_date) : Dash,
      },
      {
        label: 'New Contract End Date',
        value:
          _.get(wagesData, 'seafarer_promotion[0].is_promotion', false) &&
            _.get(wagesData, 'seafarer_promotion[0].new_contract_end_date', null)
            ? dateAsString(_.get(wagesData, 'seafarer_promotion[0].new_contract_end_date', null))
            : Dash,
        hide: !_.get(wagesData, 'seafarer_promotion[0].is_promotion', false),
      },
      {
        label: 'New Wages',
        value: (
          <span data-testid="fml-update-wages-and-promotion-new-wages">
            {getWagesText(wagesData)}
          </span>
        ),
      },
      {
        label: 'Wages and Promotion Status',
        value: wagesData?.status ? (
          <div
            className={`font-${wageStatus?.color}`}
            data-testid="fml-update-wages-and-promotion-status"
          >
            {wageStatus?.name}
          </div>
        ) : (
          Dash
        ),
      },
      {
        label: 'Action',
        value: (
          <div className="row justify-content-start px-1">
            <div className="col-auto px-2">
              <UpdateButton />
            </div>
            <div className="col-auto px-2">
              <CancelButton seafarerWagesId={wagesData?.id ?? 0} />
            </div>
          </div>
        ),
        hide: wagesData?.status !== 'pending',
      },
      {
        label: 'Last Created Date',
        value: wagesData?.updated_at ? dateAsString(wagesData.updated_at) : Dash,
      },
      {
        label: 'Last Edited By',
        value: wagesData?.last_updated_by ?? Dash,
      },
      {
        single: true,
        label: 'Remark',
        value: (
          <div className="text-justify">
            {capitalizeFirstLetter(wagesData?.remarks)
              ? capitalizeFirstLetter(wagesData?.remarks)
              : Dash}
          </div>
        ),
      },
    ],
  };

  if (seafarer.seafarer_rank?.unit === RANK_VALUES.SUPY) {
    const wagesIndex = crewAssignmentModel.NOT_SIGNED_ON.findIndex(
      (param) => param.label === 'Wages',
    );
    if (wagesIndex > -1) {
      crewAssignmentModel.NOT_SIGNED_ON.splice(wagesIndex, 1);
    }
  }
  if (data?.seafarer_journey_status === seafarerStatus.CREW_ASSIGNMENT_APPROVED) {
    if (data?.no_recommendation_permission && !isSetToTravelVisible) {
      return crewAssignmentModel;
    }
    crewAssignmentModel.NOT_SIGNED_ON.splice(4, 0, {
      label: 'Action',
      value: (
        <div className="row justify-content-start px-1">
          {roleConfig?.seafarer?.edit?.contractEndDate ||
          roleConfig?.seafarer?.edit?.seafarerSuperUser ? (
            <div className="col-auto px-2">
              <SetToTravelButton disabled={!isSetToTravelVisible} />
            </div>
          ) : (
            ''
          )}

          {!data?.no_recommendation_permission ? (
            <div className="col-auto px-2">
              <RejectButton />
            </div>
          ) : (
            ''
          )}
        </div>
      ),
    });
    return crewAssignmentModel;
  }

  if (data?.seafarer_journey_status === seafarerStatus.TRAVELLING) {
    crewAssignmentModel.NOT_SIGNED_ON.splice(3, 0, endDateColumn);
    crewAssignmentModel.NOT_SIGNED_ON.splice(4, 0, {
      label: 'Action',
      value: (
        <div className="row justify-content-start px-1">
          {canChangeContractDate && (
            <div className="col-auto px-2">
              <CancelTravelButton />
            </div>
          )}
        </div>
      ),
    });
    return crewAssignmentModel;
  }

  if (
    [seafarerStatus.RECOMMENDED, seafarerStatus.RECOMMENDED_WITH_DEVIATION].includes(
      data?.seafarer_journey_status,
    ) &&
    !data?.no_recommendation_permission
  ) {
    canApproveRejectBasedOnTechGroup(data?.seafarer_journey_status) &&
      crewAssignmentModel.NOT_SIGNED_ON.splice(4, 0, {
        label: 'Action',
        value: (
          <div className="row justify-content-start px-1">
            <div className="col-auto px-2">
              <RejectButton />
            </div>
          </div>
        ),
      });
    if (currentPlanRank) {
      if (!hasRecommendationPermission(data?.seafarer_journey_status)) {
        return crewAssignmentModel;
      }
    } else if (
      !hasRecommendationPermission(data?.seafarer_journey_status, true) &&
      !hasRecommendationPermission(data?.seafarer_journey_status)
    ) {
      return crewAssignmentModel;
    }

    if (canApproveRejectBasedOnTechGroup(data?.seafarer_journey_status)) {
      crewAssignmentModel.NOT_SIGNED_ON[4] = {
        label: 'Action',
        value: (
          <div className="row justify-content-start px-1">
            <div className="col-auto px-2">
              {seafarer.seafarer_rank.unit !== RANK_VALUES.SUPY && (
                <ApproveButton seafarerStatus={data?.seafarer_journey_status} />
              )}
            </div>
            <div className="col-auto px-2">
              <RejectButton />
            </div>
          </div>
        ),
      };
    }
  }
  return crewAssignmentModel;
};

export default CrewAssignmentModel;
