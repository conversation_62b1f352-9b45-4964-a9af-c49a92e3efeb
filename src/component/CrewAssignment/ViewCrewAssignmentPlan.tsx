import _, { orderBy } from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import { useHistory } from 'react-router-dom';
import moment from 'moment-timezone';
import { SUPY } from '@src/constants/recommendation';
import { Seafarer, SeafarerStatusHistory } from '../../types/seafarerInterfaces';
import * as seafarerService from '../../service/seafarer-service';
import { TableBody, MainTitle, SubTitle } from '../common/TableUtils';
import CrewAssignmentModel from './CrewAssignmentModel';
import { SeafarerWages } from '../../types/wagesInterfaces';
import {
  WAGES_STATUS_PENDING,
  WAGES_STATUS_APPLIED,
  WAGES_STATUS_CANCELLED,
  WAGES_STATUS_OBSOLETE,
} from '../../constants/seafarer-wages';
import ConfirmActionModalView from '../AddSeafarer/ConfirmActionModalView';
import Spinner from '../common/Spinner';

import { seafarerStatus } from '../../model/constants';
import { VesselOwnershipDetails } from '../../types/vesselService';
import { getStatusHistoryWithLatestVesselOwnershipData } from '../../util/view-utils';
import { getFullName } from '../CrewPlanner/utils';
import RecommendationWithDeviationModal from '../Details/recommendation-with-deviation-modal/recommendation-with-deviation-modal';
import { ConfirmationModal } from '../confirmation-modal/confirmation-modal';
import RecommendationApprovalModal from '../Details/recommendation-approval-modal/recommendation-approval-modal';
import RecommendationWithDeviationApprovalModal from '../Details/recommendation-with-deviation-approval-modal/recommendation-with-deviation-approval-modal';
import RecommendationRejectionModal from '../Details/recommendation-rejection-modal/recommendation-rejection-modal';

import '../scss/view-crew-assignment-plan.scss';
import SetSeafarerToTravelModal from '../Details/set-seafarer-to-travel-modal/set-seafarer-to-travel-modal';
import CancelTravelModal from '../Details/cancel-travel-modal/cancel-travel-modal';

const getWagesToShowInCrewAssignmentPlan = (
  isSignedOn: boolean,
  seafarerWages: SeafarerWages[],
) => {
  if (isSignedOn) {
    // for crew assignment plan after signed on, wages showing here must be with status = 'applied'
    return seafarerWages.find((i) => i.is_history === false && i.status === WAGES_STATUS_APPLIED);
  }
  // for crew assignment plan before signed on, wages status must be = 'pending'
  return seafarerWages.find((i) => i.is_history === false && i.status === WAGES_STATUS_PENDING);
};

const getWagesToShowInUpdateWagesAndPromotionSection = (seafarerWages: SeafarerWages[]) => {
  // for wages to be shown in update section, status may be either 'pending'/'applied'
  const latestPendingWages = seafarerWages.find(
    (i) => i?.is_history === false && i.status === WAGES_STATUS_PENDING,
  );

  const sortedAppliedOrCancelledWages = seafarerWages
    .filter((i) =>
      [WAGES_STATUS_APPLIED, WAGES_STATUS_CANCELLED, WAGES_STATUS_OBSOLETE].includes(i.status),
    )
    .sort((a, b) => (moment(b.updated_at) > moment(a.updated_at) ? 1 : -1));

  // only show if there is not wages update request before
  const latestSortedAppliedOrCancelledWages =
    sortedAppliedOrCancelledWages.length > 1 ||
    sortedAppliedOrCancelledWages
      .find((s) => !s.is_history)
      ?.seafarer_promotion?.some((p) => p.is_promotion)
      ? _.get(sortedAppliedOrCancelledWages, 0)
      : undefined;

  return latestPendingWages ?? latestSortedAppliedOrCancelledWages;
};

const ViewCrewAssignmentPlan = ({
  seafarer,
  roleConfig,
  eventTracker,
  activeVesselData,
  setError,
  dropdownData,
  setHasStatusChanged,
  plansList,
  setHasHistoryChanged,
}: {
  seafarer: Seafarer;
  roleConfig: any;
  eventTracker: (type: string, value: string | number) => void;
  activeVesselData: VesselOwnershipDetails[];
  setError: any;
  dropdownData: Object;
  setHasStatusChanged: any;
  plansList: SeafarerStatusHistory[];
  setHasHistoryChanged: (value: number) => void;
}) => {
  const history = useHistory();
  const [seafarerHistory, setSeafarerHistory] = useState<SeafarerStatusHistory[]>([]);
  const [seafarerWages, setSeafarerWages] = useState<SeafarerWages[]>([]);
  // const [activeVesselData, setActiveVesselData] = useState<VesselOwnershipDetails[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isModalLoading, setIsModalLoading] = useState(false);
  const [isShowConfirmationModal, setIsShowConfirmationModal] = useState(false);
  const [seafarerWagesId, setSeafarerWagesId] = useState(0);
  const [isUpdateEnabled, setIsUpdateEnabled] = useState(false);
  const [showRecommendationWithDeviationModal, setShowRecommendationWithDeviationModal] =
    useState(false);
  const currentRecommendationModalData = useRef(null);
  const [showApproveRecommendationModal, setShowApproveRecommendationModal] = useState(false);
  const [
    showApproveRecommendationWithDeviationModal,
    setShowApproveRecommendationWithDeviationModal,
  ] = useState(false);
  const [unableToApproveMessage, setUnableToApproveMessage] = useState('');
  const [isDisableApproveBtn, setIsDisableApproveBtn] = useState(false);
  const [showRejectRecommendationModal, setShowRejectRecommendationModal] = useState(false);
  const [recommendationStatusChanged, setRecommendationStatusChanged] = useState(false);
  const [showCancelTravelModal, setShowCancelTravelModal] = useState(false);
  const [showSetSeafarerToTravelModal, setShowSetSeafarerToTravelModal] = useState<boolean>(false);
  const recommendationPermissions = [
    roleConfig?.seafarer?.edit?.recommendation,
    roleConfig?.seafarer?.edit?.recommendationApproval,
    roleConfig?.seafarer?.edit?.recommendationApprovalExceptTopRanks,
    roleConfig?.seafarer?.edit?.recommendationDeviationApproval,
    roleConfig?.seafarer?.edit?.recommendationDeviationApprovalExceptTopRanks,
  ];

  const noRecommendationPermission = recommendationPermissions.every((role) => role === false);

  useEffect(() => {
    (async () => {
      !isShowConfirmationModal && (await fetchCrewAssignmentData());
      if (roleConfig?.seafarer?.edit?.wages) {
        setIsUpdateEnabled(true);
      }
    })();
  }, [isShowConfirmationModal, recommendationStatusChanged]);

  useEffect(() => {
    (async () => {
      const filteredData = (plansList || []).sort(
        (a: SeafarerStatusHistory, b: SeafarerStatusHistory) =>
          moment(a.created_at) < moment(b.created_at) ? 1 : -1,
      );
      const approvedPlanIndex = filteredData.findIndex(
        (plan) => plan.seafarer_journey_status === seafarerStatus.CREW_ASSIGNMENT_APPROVED,
      );
      if (approvedPlanIndex > -1) {
        if (noRecommendationPermission) {
          filteredData[approvedPlanIndex].no_recommendation_permission = true;
        } else if (filteredData[approvedPlanIndex].has_travel_with_fleet) {
          const { data } = await seafarerService.getRecommendedCheckListById(
            filteredData[approvedPlanIndex].id,
          );
          if (data.response) {
            const falseAnswer = data.response.find((row) => row.checklist_answer !== 'YES');
            filteredData[approvedPlanIndex].recommended_with_deviation = !!falseAnswer;
          }
        } else {
          filteredData[approvedPlanIndex].recommended_with_deviation = true;
        }
      }
      const recommendationPlan = filteredData.findIndex((plan) =>
        [seafarerStatus.RECOMMENDED_WITH_DEVIATION, seafarerStatus.RECOMMENDED].includes(
          plan.seafarer_journey_status,
        ),
      );
      if (recommendationPlan > -1 && noRecommendationPermission) {
        filteredData[recommendationPlan].no_recommendation_permission = true;
      }
      setSeafarerHistory(filteredData);
    })();
  }, [plansList]);


  const fetchCrewAssignmentData = async () => {
    try {
      setIsLoading(true);
      const { data } = await seafarerService.getSeafarerWages(seafarer.id, '?with_history=true');
      if (data) {
        setSeafarerWages(data);
      }
    } catch (err) {
      console.log(err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleActionCancel = (seafarerWagesId: number) => {
    eventTracker('cancelWagesFromGeneralTab', '');
    setIsShowConfirmationModal(true);
    setSeafarerWagesId(seafarerWagesId);
  };

  const handleConfirmCancelWages = async (seafarerWagesId: number) => {
    try {
      setIsModalLoading(true);
      await seafarerService.deleteSeafarerWages(seafarer.id, seafarerWagesId);
      setIsModalLoading(false);
      setIsShowConfirmationModal(false);
    } catch (error) {
      setIsModalLoading(false);
      console.log('Failed to cancel wages', error);
    }
  };
  const handleSetToTravel = () => {
    setShowSetSeafarerToTravelModal(true);
  };
  const statusHistoryForSetToTravel = orderBy(seafarerHistory, 'id', 'desc')?.find(
    (s) =>
      s.is_p1_history === false &&
      s.seafarer_journey_status === seafarerStatus.CREW_ASSIGNMENT_APPROVED,
  );
  const getSeafarerSetToTravelVisibility = () => {
    if (seafarer?.seafarer_rank?.value === SUPY && statusHistoryForSetToTravel) return true;
    return !!(statusHistoryForSetToTravel?.contract_length && seafarerWages?.[0]?.id);
  };
  return (
    <>
      {showSetSeafarerToTravelModal ? (
        <SetSeafarerToTravelModal
          show
          roleConfig={roleConfig}
          seafarerDetails={seafarer}
          statusHistory={statusHistoryForSetToTravel}
          onConfirm={() => {
            eventTracker('setToTravelModalSaveButton', getFullName(seafarer));
            setHasStatusChanged(Math.random());
          }}
          onClose={() => setShowSetSeafarerToTravelModal(false)}
        />
      ) : (
        ''
      )}
      {unableToApproveMessage && (
        <ConfirmationModal
          show
          confirmButtonLabel="OK"
          showCancelButton={false}
          title=""
          message={unableToApproveMessage}
          confirmCustomStyle={{ padding: '4px 25px' }}
          dialogClassName="unable-to-approve"
          onConfirm={() => setUnableToApproveMessage('')}
          onClose={() => setUnableToApproveMessage('')}
        />
      )}
      {isShowConfirmationModal && (
        <ConfirmActionModalView
          onClose={() => {
            setIsShowConfirmationModal(false);
          }}
          message=""
          title="Confirm Cancelling update wages and promotion?"
          onConfirm={() => {
            eventTracker('confirmCancelWagesFromGeneralTab', '');
            handleConfirmCancelWages(seafarerWagesId);
          }}
          isDisableConfirmDeleteBtn={false}
          show
          isModalLoading={isModalLoading}
        />
      )}
      {showRecommendationWithDeviationModal && (
        <RecommendationWithDeviationModal
          show={showRecommendationWithDeviationModal}
          onClose={() => {
            currentRecommendationModalData.current = null;
            setShowRecommendationWithDeviationModal(false);
          }}
          currentRecommendationDeviationModalData={currentRecommendationModalData.current}
        />
      )}
      {showApproveRecommendationModal && (
        <RecommendationApprovalModal
          show={showApproveRecommendationModal}
          onClose={() => {
            setShowApproveRecommendationModal(false);
          }}
          onConfirm={() => {
            currentRecommendationModalData.current = null;
            setHasStatusChanged(Math.random());
            setRecommendationStatusChanged(!recommendationStatusChanged);
            setShowApproveRecommendationModal(false);
          }}
          seafarerDetails={seafarer}
          currentRecommendationModalData={currentRecommendationModalData.current}
          ranks={dropdownData.ranks}
        />
      )}
      {showApproveRecommendationWithDeviationModal && (
        <RecommendationWithDeviationApprovalModal
          show={showApproveRecommendationWithDeviationModal}
          onClose={() => {
            setShowApproveRecommendationWithDeviationModal(false);
          }}
          onConfirm={() => {
            currentRecommendationModalData.current = null;
            setHasStatusChanged(Math.random());
            setRecommendationStatusChanged(!recommendationStatusChanged);
            setShowApproveRecommendationWithDeviationModal(false);
          }}
          currentRecommendationModalData={currentRecommendationModalData.current}
          seafarerDetails={seafarer}
        />
      )}
      {showRejectRecommendationModal && (
        <RecommendationRejectionModal
          show={showRejectRecommendationModal}
          onClose={() => {
            setShowRejectRecommendationModal(false);
          }}
          onConfirm={() => {
            currentRecommendationModalData.current = null;
            setHasStatusChanged(Math.random());
            setRecommendationStatusChanged(!recommendationStatusChanged);
            setShowRejectRecommendationModal(false);
          }}
          currentRecommendationModalData={currentRecommendationModalData.current}
          seafarerDetails={seafarer}
        />
      )}
      {showCancelTravelModal && (
        <CancelTravelModal
          show={showCancelTravelModal}
          onClose={() => {
            setShowCancelTravelModal(false);
          }}
          onConfirm={() => {
            currentRecommendationModalData.current = null;
            eventTracker('cancelTravelModalSaveButton', '');
            setHasStatusChanged(Math.random());
            setRecommendationStatusChanged(!recommendationStatusChanged);
            setShowCancelTravelModal(false);
          }}
          seafarerDetails={seafarer}
        />
      )}

      {!isLoading ? (
        seafarerHistory.map((statusHistory: SeafarerStatusHistory, index: number) => {
          const seafarerWagesLinkedToStatusHistory = seafarerWages.filter(
            (i) => i?.seafarer_status_history?.id === statusHistory.id,
          );
          const isSignedOn = statusHistory?.seafarer_journey_status === seafarerStatus.SIGNED_ON;

          const wagesToShowInCrewAssignmentPlan = getWagesToShowInCrewAssignmentPlan(
            isSignedOn,
            seafarerWagesLinkedToStatusHistory,
          );

          const wagesToShowInUpdateWagesAndPromotionSection =
            getWagesToShowInUpdateWagesAndPromotionSection(seafarerWagesLinkedToStatusHistory);

          const isWagesUpdated =
            seafarerWagesLinkedToStatusHistory.length > 1 ||
            seafarerWagesLinkedToStatusHistory
              .find((s) => !s.is_history)
              ?.seafarer_promotion?.some((p) => p.is_promotion);

          const statusHistoryWithLatestOwnershipData =
            getStatusHistoryWithLatestVesselOwnershipData(activeVesselData, statusHistory);

          const handleDeviationStatusClick = () => {
            if (!noRecommendationPermission) {
              setShowRecommendationWithDeviationModal(true);
              currentRecommendationModalData.current = statusHistory;
            }
          };

          const getScreeningStatus = async () => {
            setIsDisableApproveBtn(true);
            const { data } = await seafarerService.getScreeningStatus(seafarer.seafarer_person_id);
            setIsDisableApproveBtn(false);
            if (data.error) {
              setUnableToApproveMessage(data.message);
              return true;
            }
            return false;
          };

          const handleActionApprove = (status) => {
            if (status === seafarerStatus.RECOMMENDED) {
              handleApproveRecommend();
            } else if (status === seafarerStatus.RECOMMENDED_WITH_DEVIATION) {
              handleApproveRecommendWithDeviation();
            }
          };

          const handleApproveRecommend = async () => {
            try {
              const isUnderScreening = await getScreeningStatus();
              if (isUnderScreening) {
                return;
              }
              currentRecommendationModalData.current = statusHistory;
              setShowApproveRecommendationModal(true);
            } catch (error) {
              setIsDisableApproveBtn(false);
              setError(error);
            }
          };

          const handleApproveRecommendWithDeviation = async () => {
            try {
              const isUnderScreening = await getScreeningStatus();
              if (isUnderScreening) {
                return;
              }
              currentRecommendationModalData.current = statusHistory;
              setShowApproveRecommendationWithDeviationModal(true);
            } catch (error) {
              setIsDisableApproveBtn(false);
              setError(error);
            }
          };

          const handleActionReject = () => {
            currentRecommendationModalData.current = statusHistory;
            setShowRejectRecommendationModal(true);
          };

          const handleCancelTravel = () => {
            currentRecommendationModalData.current = statusHistory;
            setShowCancelTravelModal(true);
          };

          const handleChangeContractDate = (data) => {
            if (!data?.expected_contract_end_date) return;
            eventTracker('changePeriodModalSaveButton', '');
            const updatedSeafarerHistory = seafarerHistory.map((statusHistory) => {
              const validStatus = [seafarerStatus.SIGNED_ON, seafarerStatus.TRAVELLING];
              return validStatus.includes(statusHistory?.seafarer_journey_status)
                ? { ...statusHistory, expected_contract_end_date: data.expected_contract_end_date }
                : statusHistory;
            });
            setSeafarerHistory(updatedSeafarerHistory);
            setHasHistoryChanged(Math.random());
          };
          return (
            <div key={statusHistory.id} className="crew-assignment-plans">
              {isSignedOn ? (
                <>
                  <MainTitle title={`Crew Assignment Plan ${seafarerHistory.length - index}`} />
                  <TableBody
                    data={
                      CrewAssignmentModel({
                        data: statusHistoryWithLatestOwnershipData,
                        wagesData: wagesToShowInCrewAssignmentPlan,
                        seafarer,
                        history,
                        eventTracker,
                        isUpdateEnabled,
                        handleChangeContractDate,
                        roleConfig,
                      }).SIGNED_ON
                    }
                  />
                  {isWagesUpdated && (
                    <>
                      <SubTitle title="Update Wages and Promotion" />
                      <TableBody
                        data={
                          CrewAssignmentModel({
                            data: statusHistoryWithLatestOwnershipData,
                            wagesData: wagesToShowInUpdateWagesAndPromotionSection,
                            seafarer,
                            history,
                            eventTracker,
                            isUpdateEnabled,
                            handleActionCancel,
                          }).UPDATED_WAGES
                        }
                      />
                    </>
                  )}
                </>
              ) : (
                <>
                  <MainTitle title={`Crew Assignment Plan ${seafarerHistory.length - index}`} />
                  <TableBody
                    data={
                      CrewAssignmentModel({
                        data: statusHistoryWithLatestOwnershipData,
                        wagesData: wagesToShowInCrewAssignmentPlan,
                        seafarer,
                        history,
                        eventTracker,
                        isUpdateEnabled,
                        handleActionApprove,
                        handleActionReject,
                        handleDeviationStatusClick,
                        isDisableApproveBtn,
                        dropdownData,
                        roleConfig,
                        handleSetToTravel,
                        handleChangeContractDate,
                        isSetToTravelVisible: getSeafarerSetToTravelVisibility(),
                        handleCancelTravel,
                      }).NOT_SIGNED_ON
                    }
                  />
                </>
              )}
            </div>
          );
        })
      ) : (
        <div className="m-5">
          <Spinner />
        </div>
      )}
    </>
  );
};

export default ViewCrewAssignmentPlan;
