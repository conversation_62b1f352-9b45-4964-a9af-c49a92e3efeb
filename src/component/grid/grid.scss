.grid {
    .th {
      background-color: #ffffff;
      margin: 0;
      padding: 0.75em 0.7em;
      border-bottom: 2px solid #1f4a70;
      font-weight: bold;
      user-select: none;
    }
  
    .tr {
      border-bottom: 1px solid #cccccc;
    }
  
    .td {
      background-color: #ffffff;
      margin: 0;
      padding: 0.7em;
    }
  
    &.sticky {
      min-width: 100% !important;
      overflow: auto;
      .header {
        top: 0;
        position: sticky;
        z-index: 1;
      }
  
      .footer {
        bottom: 0;
        position: sticky;
        z-index: 1;
      }
      .body {
        position: relative;
        z-index: 0;
      }
  
      [data-sticky-td] {
        // intentionally kept empty block
      }
  
      [data-sticky-last-left-td] {
        border-right: 1px solid #cccccc;
      }
  
      [data-sticky-first-right-td] {
        // intentionally kept empty block
      }
    }
  }