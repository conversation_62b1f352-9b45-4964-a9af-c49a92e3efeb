import React, { useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';
import { useFlexLayout, usePagination, useSortBy, useTable } from 'react-table';
import { useSticky } from 'react-table-sticky';
import styleGuide from '../../styleGuide';
import Spinner from '../common/Spinner';
import Pagination from '../pagination/pagination';
import './grid.scss';
import { MODULES } from '@src/util';

const { Icon } = styleGuide;

export interface GridColumns {
  Header: string | (() => JSX.Element);
  Footer: () => JSX.Element | null;
  disableSortBy: boolean;
  accessor: string | (() => JSX.Element);
  width: number;
  maxWidth: number;
  minWidth: number;
  sticky: string;
  type: string;
  name: string;
  Cell: () => JSX.Element;
}

const smallerOf = (num1: number, num2: number) => {
  if (num1 > num2) return num2;
  return num1;
};

const getPaginationText = (pageNo: number, pageSize, totalCount) => {
  let lowerBound: number = pageNo * pageSize + 1;
  const upperBound = smallerOf(totalCount, (pageNo + 1) * pageSize);
  if (upperBound === 0) lowerBound = 0;
  return `Showing ${lowerBound} - ${upperBound} of ${totalCount}`;
};

const filterPages = (visiblePagesArr, totalPages) =>
  visiblePagesArr.filter((pageNum) => pageNum <= totalPages);

const getVisiblePages = (pageNo: number, total: number) => {
  if (total < 7) {
    return filterPages([1, 2, 3, 4, 5, 6], total);
  }
  if (pageNo % 5 >= 0 && pageNo > 4 && pageNo + 2 < total) {
    return [1, pageNo - 1, pageNo, pageNo + 1, total];
  }
  if (pageNo % 5 >= 0 && pageNo > 4 && pageNo + 2 >= total) {
    return [1, total - 3, total - 2, total - 1, total];
  }
  return [1, 2, 3, 4, 5, total];
};

const canClickIcon = (column) => {
  return (
    <span>
      {column.canSort &&
        (() => {
          if (column.isSorted) {
            if (column.isSortedDesc) {
              return <Icon icon="sort-ascending" size={20} className="default" />;
            } else {
              return <Icon icon="sort-descending" size={20} className="default" />;
            }
          } else {
            return <Icon icon="sort-off" size={20} className="default" />;
          }
        })()}
    </span>
  );
};

const renderLoader = (isLoading) => {
  return isLoading && <Spinner alignClass={'spinner-grid'} data-testid="loader" />;
};

export const Grid = ({
  columns,
  data,
  isLoading = false,
  fetchData = async () => {},
  initSort = [],
  totalCount = 0,
  defaultPageSize = 10,
  pageSizeOptions = [10, 20, 50, 100],
  externalPageIndex = 0,
  showTopPagination = false,
  showBottomPagination = true,
  footerRowStyle = {},
  gridStyle = {},
  isManualSort = true,
  hasStickyColumn = false,
  resultsCount = 0,
  showResultsCount = false,
  showPaginationLabel = true, //Showing 1 - 10 of 98
  onRowClick = (state: any, rowInfo: any, column: any, instance: any) => {},
  quickSearchParams = '',
  advancedSearchParams = '',
  ga4EventTrigger = (action, category, label) => {},
  calledFromModule = ''
}) => {
  const memoData = useMemo(() => data, [data]);
  const defaultColumn = useMemo(
    () => ({
      minWidth: 120,
      width: 120,
    }),
    [],
  );
  const [pageSize, setPageSize] = useState(defaultPageSize);
  const totalRecordCount = totalCount || 0;
  const pageCount = Math.ceil(totalRecordCount / pageSize);

  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    footerGroups,
    prepareRow,
    page,
    canPreviousPage,
    canNextPage,
    gotoPage,
    getTrProps = onRowClick,
    state: { pageIndex, sortBy },
  } = useTable(
    {
      columns,
      data: memoData,
      defaultColumn,
      initialState: { pageIndex: 0, sortBy: initSort },
      manualPagination: true,
      manualSortBy: isManualSort,
      autoResetPage: false,
      autoResetSortBy: false,
      pageCount,
      pageSize,
    },
    useSortBy,
    usePagination,
    useSticky,
    useFlexLayout,
  );

  const firstSortByHookCall = useRef(true);
  useLayoutEffect(() => {
    if (firstSortByHookCall.current) {
      firstSortByHookCall.current = false;
      return;
    }
    (async function () {
      await resetPage(pageIndex);
    })();
  }, [sortBy]);

  const firstRender = useRef(true);
  useEffect(() => {
    if (firstRender.current) {
      firstRender.current = false;
      return;
    }
    (async function () {
      await resetPage();
    })();
  }, [quickSearchParams, advancedSearchParams]);

  //this is done intentionally so that the page index from external/parent component syncs with grid(useTable) component page index
  useEffect(() => {
    gotoPage(externalPageIndex);
  }, [externalPageIndex]);

  const resetPage = async (pageNo = 0) => {
    await fetchData({ pageSize: pageSize, sortBy, pageIndex: pageNo });
    setPageSize(pageSize); //NOSONAR
    gotoPage(pageNo);
  };

  const pageSwitch = async (pageNo: number) => {
    await fetchData({ pageSize, sortBy, pageIndex: pageNo });
    gotoPage(pageNo);
  };

  const pageSizeSwitch = async (newPageSize: number) => {
    const newPageIndex = 0;
    gotoPage(newPageIndex);
    if(calledFromModule === MODULES.DAILY_NEWS) {
      ga4EventTrigger('Change Row Per Page', 'Seafarer - Daily News - List', 'Show ' + newPageSize);
    }
    await fetchData({ pageIndex: newPageIndex, sortBy, pageSize: newPageSize });
    setPageSize(newPageSize);
  };

  const visiblePages = getVisiblePages(pageIndex, pageCount);

  const spanPagination =
    (showTopPagination || showBottomPagination) &&
    getPaginationText(pageIndex, pageSize, totalCount);

  return (
    <>
      {showTopPagination && (
        <>
          <br />
          <Pagination
            spanPagination={spanPagination}
            canPreviousPage={canPreviousPage}
            pageIndex={pageIndex}
            visiblePages={visiblePages}
            canNextPage={canNextPage}
            pageSize={pageSize}
            pageSizeOptions={pageSizeOptions}
            pageSwitch={pageSwitch}
            pageSizeSwitch={pageSizeSwitch}
            showResultsCount={showResultsCount}
            resultsCount={resultsCount}
            showPaginationLabel={showPaginationLabel}
          />
        </>
      )}
      <div
        {...getTableProps()}
        className={hasStickyColumn ? 'grid sticky' : 'grid'}
        style={{ ...getTableProps(), ...gridStyle }}
      >
        <div className="header" data-testid="header">
          {headerGroups.map((headerGroup, headerGroupIndex) => (
            <div {...headerGroup.getHeaderGroupProps()} className="tr" key={headerGroupIndex}>
              {headerGroup.headers.map((column, columnIndex) => {
                const thProps = column.getHeaderProps(column.getSortByToggleProps());
                return (
                  <div
                    {...thProps}
                    className={`th`}
                    key={columnIndex}
                    style={{
                      ...thProps.style,
                      ...column.customHeaderCellStyle,
                    }}
                  >
                    {column.render('Header')}
                    {canClickIcon(column)}
                  </div>
                );
              })}
            </div>
          ))}
        </div>
        {renderLoader(isLoading)}
        <div {...getTableBodyProps()} className="body">
          {page.map((row, rIdx) => {
            prepareRow(row);
            return (
              <div {...row.getRowProps()} className="tr" key={rIdx} {...getTrProps(row)}>
                {row.cells.map((cell, cIdx) => {
                  const classes = `td ${cell.column.id}`;
                  const tdProps = cell.getCellProps();
                  return (
                    <div
                      {...tdProps}
                      className={classes}
                      style={{
                        ...tdProps.style,
                        ...cell.column.customDataCellStyle,
                      }}
                      key={cIdx}
                    >
                      {cell.render('Cell')}
                    </div>
                  );
                })}
              </div>
            );
          })}
        </div>

        <div className="footer">
          {footerGroups.map((footerGroup, footerGroupIndex) => {
            const footerGroupProps = footerGroup.getHeaderGroupProps();
            const createFooter = footerGroup.headers.some((column) => {
              return column.Footer;
            });
            return (
              createFooter && (
                <div
                  {...footerGroupProps}
                  className="tr"
                  key={footerGroupIndex}
                  style={{ ...footerGroupProps.style, ...footerRowStyle }}
                >
                  {footerGroup.headers.map((column, footerColumnIndex) => (
                    <div
                      {...column.getHeaderProps()}
                      className="td"
                      key={footerColumnIndex}
                      style={{
                        ...column.getHeaderProps().style,
                        ...column.customFooterCellStyle,
                      }}
                    >
                      {column.Footer && column.render('Footer')}
                    </div>
                  ))}
                </div>
              )
            );
          })}
        </div>
      </div>

      {showBottomPagination && (
        <>
          <br />
          <Pagination
            spanPagination={spanPagination}
            canPreviousPage={canPreviousPage}
            pageIndex={pageIndex}
            visiblePages={visiblePages}
            canNextPage={canNextPage}
            pageSize={pageSize}
            pageSizeOptions={pageSizeOptions}
            pageSwitch={pageSwitch}
            pageSizeSwitch={pageSizeSwitch}
            showPaginationLabel={showPaginationLabel}
          />
        </>
      )}
    </>
  );
};
