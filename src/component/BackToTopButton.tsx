import React, { useState, useEffect } from 'react';

import styleGuide from '../styleGuide';
import { Button } from 'react-bootstrap';
const { Icon } = styleGuide;

const ScrollArrow = ({ eventTracker }) => {
  const [showScroll, setShowScroll] = useState(false);

  const checkScrollTop = () => {
    if (!showScroll && window.scrollY > 400) {
      setShowScroll(true);
    } else if (showScroll && window.scrollY <= 400) {
      setShowScroll(false);
    }
  };
  const scrollTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
    eventTracker('scroll', 'Scroll to top');
  };

  useEffect(() => {
    window.addEventListener('scroll', checkScrollTop);
    return () => {
      window.removeEventListener('scroll', checkScrollTop);
    }
  });

  return (
    <div className="back-to-top">
      {showScroll && (
        <>
          <Icon icon="up-filled" size={40} onClick={scrollTop} />
          <Button variant="link" onClick={scrollTop}>
            <span>Back to top</span>
          </Button>
        </>
      )}
    </div>
  );
};

export default ScrollArrow;
