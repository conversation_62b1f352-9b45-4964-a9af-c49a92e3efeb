import React, { useState, useEffect } from 'react';
import { Col, Button, Modal, Form } from 'react-bootstrap';
import { Formik } from 'formik';
import * as yup from 'yup';
import DropDownSearchControl from '../AddSeafarer/DropDownSearchControl';
import FleetDatePicker from '../AddSeafarer/FleetDatePicker';
import { createContactLog } from '../../service/seafarer-service';

const contactLogSchema = yup.object({
  availability_date: yup
    .date()
    .nullable()
    .min(yup.ref('contact_date'), 'Availability date should be later than Contact date'),
  availability_remarks: yup.string().nullable(),
  contact_date: yup.date().nullable().required('Contact Date is required'),
  contact_mode: yup.string().nullable(),
  docs_in_hand: yup.boolean().nullable(),
  general_remarks: yup.string().nullable(),
  next_contact_date: yup.date().nullable(),
});

const dropDownData = [
  {
    id: 1,
    value: 'telephone',
  },
  {
    id: 2,
    value: 'online',
  },
  {
    id: 3,
    value: 'ship',
  },
  {
    id: 4,
    value: 'office',
  },
];

const UpdateContactLog = ({
  seafarerId,
  cancelButtonHandler,
  contactsLog,
  eventTracker,
  setIsCallContactLogData,
}) => {
  const [triedValidate, setTriedValidate] = useState(false);
  const [isDisabled, setIsDisabled] = useState(false);
  const [selectedContactMode, setSelectedContactMode] = useState({});
  const [latestContactLogData, setLatestContactLogData] = useState(null);
  useEffect(() => {
    if (contactsLog?.length > 0) {
      const latestContactData = contactsLog.find((e) => e.is_latest === true) ?? null;
      if (latestContactData?.contact_mode) {
        const contactMode = dropDownData.find((e) => e.value === latestContactData.contact_mode);
        setSelectedContactMode(contactMode);
        setLatestContactLogData(
          formatContactLog({ ...latestContactData, contact_mode_id: contactMode.id }),
        );
      } else {
        setLatestContactLogData(formatContactLog({ ...latestContactData, contact_mode_id: null }));
      }
    }
  }, []);

  const formatContactLog = (contactLog) => {
    const newContactLog = {
      availability_date: null,
      availability_remarks: '',
      contact_date: null,
      contact_mode_id: null,
      contact_mode: null,
      docs_in_hand: undefined,
      general_remarks: '',
      next_contact_date: null,
    };
    Object.keys(newContactLog).forEach((key) => {
      if (contactLog[key] === null) {
        newContactLog[key] = emptyContactLogData[key];
      } else {
        newContactLog[key] = contactLog[key];
      }
    });
    return newContactLog;
  };

  const onSubmit = (values) => {
    onSubmitContactLog(values);
  };

  const onSubmitContactLog = async (values) => {
    const payload = {
      availability_date: values.availability_date !== '' ? values.availability_date : null,
      availability_remarks: values.availability_remarks !== '' ? values.availability_remarks : null,
      contact_date: values.contact_date,
      contact_mode: selectedContactMode?.value,
      docs_in_hand: values.docs_in_hand ?? null,
      general_remarks: values.general_remarks !== '' ? values.general_remarks : null,
      next_contact_date: values.next_contact_date !== '' ? values.next_contact_date : null,
      is_latest: true,
    };

    try {
      const response = await createContactLog(seafarerId, payload);
      if (response.status == 200) {
        eventTracker('editAvailability', 'Submit Seafarer Availability Contact Log');
        cancelButtonHandler();
      }
      setIsCallContactLogData(false);
    } catch (error) {
      setIsCallContactLogData(false);
      setIsDisabled(false);
      console.log(error);
    }
  };

  const emptyContactLogData = {
    availability_date: null,
    availability_remarks: '',
    contact_date: null,
    contact_mode_id: null,
    contact_mode: null,
    docs_in_hand: false,
    general_remarks: '',
    next_contact_date: null,
  };

  return (
    <Modal
      id="contact-log-popup"
      show
      aria-labelledby="update-contact-log-modal"
      centered
      size="lg"
      backdrop="static"
    >
      <Modal.Header>
        <Modal.Title style={{ borderBottom: '0' }}>Update Contact Log</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <div className="update-contact-log-required-text">
          <div className="font-weight-bold p-2">* Required fields</div>
        </div>
        <Formik
          validationSchema={contactLogSchema}
          enableReinitialize
          initialValues={latestContactLogData ?? emptyContactLogData}
          validateOnChange={triedValidate}
          validateOnBlur={triedValidate}
        >
          {({ handleSubmit, handleBlur, values, errors, setFieldValue, dirty, validateForm }) => {
            const onInputChange = (event) => {
              const targetName = event?.target?.name;
              const targetValue = event?.target?.value;

              if (targetName === 'docs_in_hand') {
                const checkedValue = event?.target?.checked;
                setFieldValue(targetName, checkedValue || undefined);
              } else {
                setFieldValue(targetName, targetValue || undefined); // to make sure undefined is set if value is empty string
              }
              if (targetName === 'contact_mode_id') {
                const contactMode = dropDownData.find((e) => e.id === targetValue)?.value;
                if (contactMode) {
                  setSelectedContactMode({ id: targetValue, value: contactMode });
                } else {
                  setSelectedContactMode(null);
                }
              }
            };

            const genOnDateChange = (fieldName) => (value) => setFieldValue(fieldName, value);

            const onValidateForm = async () => {
              const errors = await validateForm();
              return {
                isFormValid: !Object.keys(errors).length,
              };
            };

            const onSubmitValues = () => {
              onValidateForm().then(({ isFormValid }) => {
                setTriedValidate(true);
                if (isFormValid) {
                  setIsCallContactLogData(true);
                  setIsDisabled(true);
                  onSubmit(values);
                }
              });
            };

            return (
              <Form noValidate onSubmit={handleSubmit}>
                <Form.Row>
                  <Form.Group as={Col} md="5">
                    <Form.Label>Availability Date</Form.Label>
                    <FleetDatePicker
                      name="availability_date"
                      value={values.availability_date}
                      onChange={genOnDateChange('availability_date')}
                      isInvalid={errors?.availability_date}
                    />
                    {errors?.availability_date && (
                      <div className="invalid-feedback d-block">{errors.availability_date}</div>
                    )}
                  </Form.Group>
                  <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
                    <Form.Label>Documents in hand</Form.Label>
                    <Form.Check
                      type="checkbox"
                      label="Yes, documents in hand"
                      checked={values?.docs_in_hand}
                      value={values?.docs_in_hand}
                      onChange={onInputChange}
                      name="docs_in_hand"
                      id="docs_in_hand"
                    />
                  </Form.Group>
                </Form.Row>
                <Form.Row>
                  <Form.Group as={Col} md="5">
                    <Form.Label>Availability Remark</Form.Label>
                    <Form.Control
                      as="textarea"
                      rows="3"
                      maxLength={1000}
                      name="availability_remarks"
                      value={values.availability_remarks}
                      isInvalid={!!errors.availability_remarks}
                      onChange={onInputChange}
                    />
                    <Form.Control.Feedback type="invalid">
                      {errors.availability_remarks}
                    </Form.Control.Feedback>
                  </Form.Group>
                  <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
                    <Form.Label>General Remark</Form.Label>
                    <Form.Control
                      as="textarea"
                      rows="3"
                      maxLength={1000}
                      name="general_remarks"
                      value={values.general_remarks}
                      isInvalid={!!errors.general_remarks}
                      onChange={onInputChange}
                    />
                    <Form.Control.Feedback type="invalid">
                      {errors.general_remarks}
                    </Form.Control.Feedback>
                  </Form.Group>
                </Form.Row>
                <Form.Row>
                  <Form.Group as={Col} md={5}>
                    <Form.Label>Contact Date*</Form.Label>
                    <FleetDatePicker
                      name="contact_date"
                      value={values.contact_date}
                      onChange={genOnDateChange('contact_date')}
                      isInvalid={errors?.contact_date}
                    />
                    {errors?.contact_date && (
                      <div className="invalid-feedback d-block">{errors.contact_date}</div>
                    )}
                  </Form.Group>
                  <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
                    <Form.Label>Contact Mode</Form.Label>
                    <DropDownSearchControl
                      name="contact_mode_id"
                      selectedValue={selectedContactMode?.id}
                      dropDownValues={dropDownData}
                      onInputChange={onInputChange}
                      onBlur={handleBlur}
                      isInvalid={!!errors.contact_mode_id}
                      testID="contact-mode"
                    />
                    {errors?.contact_mode && (
                      <div className="invalid-feedback d-block">{errors.contact_mode}</div>
                    )}
                  </Form.Group>
                </Form.Row>
                <Form.Row>
                  <Form.Group as={Col} md="5">
                    <Form.Label>Next Contact Date</Form.Label>
                    <FleetDatePicker
                      name="next_contact_date"
                      value={values.next_contact_date}
                      onChange={genOnDateChange('next_contact_date')}
                      isInvalid={errors?.next_contact_date}
                    />
                    {errors?.next_contact_date && (
                      <div className="invalid-feedback d-block">{errors.next_contact_date}</div>
                    )}
                  </Form.Group>
                </Form.Row>
                <hr className="grey_line" />
                <Form.Row>
                  <Modal.Footer style={{ borderTop: '0', width: '100%' }}>
                    <div className="ml-auto">
                      <Button variant="primary" className="m-2" onClick={cancelButtonHandler}>
                        Cancel
                      </Button>
                      <Button
                        variant="secondary"
                        disabled={isDisabled || !dirty}
                        className="confirm-btn"
                        onClick={onSubmitValues}
                      >
                        Save
                      </Button>
                    </div>
                  </Modal.Footer>
                </Form.Row>
              </Form>
            );
          }}
        </Formik>
      </Modal.Body>
    </Modal>
  );
};

export default UpdateContactLog;
