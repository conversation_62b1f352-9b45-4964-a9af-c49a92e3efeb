import React from 'react';
import { Col, Row } from 'react-bootstrap';
import { dateAsDayAndTime, dateAsString, stringAsDate } from '../../model/utils';
import NoRecord from '../common/NoRecords';
import Spinner from '../common/Spinner';

const ContactSummary = ({ contactsLog = [], loading }) => {
  const latestData = contactsLog?.find((contactlog) => contactlog?.is_latest);

  if (loading) {
    return <Spinner alignClass={`load-spinner`} />;
  }
  return (
    <>
      {contactsLog.length && latestData ? (
        <Row>
          <Col>
            <table className="table table-hover">
              <tbody>
                <tr>
                  <td className="details_page__row-name">Last Added by</td>
                  <td className="details_page__row-value">{latestData?.last_updated_by}</td>
                </tr>
                <tr>
                  <td className="details_page__row-name">Latest Contact Date</td>
                  <td className="details_page__row-value">
                    {dateAsDayAndTime(latestData?.contact_date)}
                  </td>
                </tr>
              </tbody>
            </table>
          </Col>
          <Col>
            <table className="table table-hover">
              <tbody>
                <tr>
                  <td className="details_page__row-name">Availability Date</td>
                  <td className="details_page__row-value">
                    {dateAsString(stringAsDate(latestData?.availability_date))}
                  </td>
                </tr>
                <tr>
                  <td className="details_page__row-name">General Remark</td>
                  <td className="details_page__row-value">{latestData?.general_remarks}</td>
                </tr>
              </tbody>
            </table>
          </Col>
        </Row>
      ) : (
        <div className="no-records-summary">
          <NoRecord />
        </div>
      )}
    </>
  );
};

export default ContactSummary;
