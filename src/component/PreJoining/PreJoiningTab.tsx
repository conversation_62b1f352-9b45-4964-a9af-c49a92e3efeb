import React, { useEffect, useState } from 'react';
import { <PERSON>, Col, Container, But<PERSON> } from 'react-bootstrap';
import _ from 'lodash';
import { useHistory, useParams } from 'react-router-dom';
import styleGuide from '../../styleGuide';
import Spinner from '../common/Spinner';
import PreJoiningTabModel from './PreJoiningModel';
import LastEditedByLine from '../document/LastEditedByLine';
import seafarerService from '../../service/seafarer-service';
import { seafarerStatus } from '../../model/constants';
// eslint-disable-next-line no-unused-vars
import { SeafarerBankAccount } from '../../types/seafarerInterfaces';
import { MainTitle, SubTitle, TableBody } from '../common/TableUtils';
import { getStatusHistoryWithLatestVesselOwnershipData } from '../../util/view-utils';
import SetContractDetailsModal from '../Details/set-contract-details-modal'
// eslint-disable-next-line no-unused-vars
import { PrejoiningTabProps } from '../../types/preJoiningTab';
import { SUPY } from '../../constants/recommendation';
const { PARIS_ONE_HOST } = process.env;
const { Icon } = styleGuide;

interface ContractDetailsData {
  seafarer_id: string | number;
  expected_contract_start_date: string | null;
  contract_length: number | null;
  repatriation_country_name: string | null;
  repatriation_port_id: number | null;
  embarkation_country_name: string | null;
  embarkation_port_id: number | null;
  nominee: number | null;
}

const PreJoiningTab = ({
  seafarer,
  setIsEditPreJoiningEnabled,
  roleConfig,
  eventTracker,
  activeVesselData,
  setHasHistoryChanged,
}: PrejoiningTabProps) => {
  const [preJoiningData, setPreJoiningData] = useState<any>({});
  const [bankDetails, setBankDetails] = useState<any>({});
  const [isLoading, setIsLoading] = useState(false);
  const [contractDetailsData, setContractDetailsData] = useState<ContractDetailsData | null>(null);
  const history = useHistory();
  const { seafarerId } = useParams<{ seafarerId: string }>();
  const [isDisabledSetWagesBtn, setIsDisabledSetWagesBtn] = useState(true);
  const [isShowContractDetails, setIsShowContractDetails] = useState(false);
  const [canEditContractDetails, setCanEditContractDetails] = useState(roleConfig?.seafarer?.edit?.contractEndDate || roleConfig?.seafarer?.edit?.seafarerSuperUser);
  const canViewContractDetails = roleConfig?.seafarer?.edit?.contractEndDate || roleConfig?.seafarer?.edit?.seafarerSuperUser || roleConfig?.seafarer?.view?.viewContractDetails;

  useEffect(() => {
    (async () => {
      fetchPreJoiningData();
    })();
  }, []);

  useEffect(() => {
    const prevPathName = history?.location?.state?.from;
    const refreshFlag = prevPathName === `/seafarer/details/${seafarerId}/pre-joining/wages/add`;
    if (refreshFlag) {
      fetchPreJoiningData();
    }
  }, [history.action]);

  const handleSetContractDetailsData = () => {
    setContractDetailsData({
      seafarer_id: seafarerId,
      ...preJoiningData?.seafarer_status_history,
    });
  }

  const handleSubmitContractDetails =  async (data) => {
    setContractDetailsData(null);
    if (!data?.contract_length) return;
    eventTracker('setContractDetailsModalSaveButton', '');
    fetchPreJoiningData();
    setHasHistoryChanged(Math.random());
  };

  const fetchPreJoiningData = async () => {
    try {
      setIsLoading(true);
      const response = await Promise.allSettled([
        seafarerService.getSeafarerPreJoiningDetails(seafarerId),
      ]);
      if (response[0].status === 'fulfilled') {
        const _preJoiningData = response[0]?.value?.data;
        const _preJoiningDataStatusHistory = _preJoiningData?.seafarer_status_history;

        if (_preJoiningDataStatusHistory) {
          const _isDisabledSetWagesBtn =
            !(
              _preJoiningDataStatusHistory.seafarer_journey_status === seafarerStatus.TRAVELLING ||
              _preJoiningDataStatusHistory.seafarer_journey_status ===
                seafarerStatus.CREW_ASSIGNMENT_APPROVED
            ) || !roleConfig?.seafarer?.edit?.wages;

          setIsDisabledSetWagesBtn(_isDisabledSetWagesBtn);
          const isShowAllotment = seafarer?.seafarer_person?.nationality?.alpha3_code === 'PHL';
          const isEnabled =
            ((_preJoiningDataStatusHistory?.seafarer_journey_status &&
              _preJoiningDataStatusHistory.seafarer_journey_status ===
                seafarerStatus.CREW_ASSIGNMENT_APPROVED) ||
              (isShowAllotment &&
                (_preJoiningDataStatusHistory.seafarer_journey_status ===
                  seafarerStatus.SIGNED_ON ||
                  _preJoiningDataStatusHistory.seafarer_journey_status ===
                    seafarerStatus.TRAVELLING))) &&
            roleConfig?.seafarer?.edit?.wages;
          setIsEditPreJoiningEnabled(isEnabled);
        }
        _preJoiningData.seafarer_id = seafarerId;

        const isShowContractDetails = seafarer?.seafarer_rank.value !== SUPY && _preJoiningDataStatusHistory.seafarer_journey_status ===
          seafarerStatus.CREW_ASSIGNMENT_APPROVED && !_preJoiningDataStatusHistory.is_p1_history;
        setIsShowContractDetails(isShowContractDetails);

        setPreJoiningData({
          ..._preJoiningData,
          seafarer_status_history: getStatusHistoryWithLatestVesselOwnershipData(
            activeVesselData,
            _preJoiningData?.seafarer_status_history,
          ),
        });
        setCanEditContractDetails(!_.isEmpty(_preJoiningData?.seafarer_wages));
      }
      if (seafarer) {
        const primaryBankAccount = seafarer.seafarer_person?.bank_accounts.find(
          (i: SeafarerBankAccount) => i.is_primary_payroll_account,
        );
        setBankDetails(primaryBankAccount);
      }
    } catch (err) {
      console.log(err);
    } finally {
      setIsLoading(false);
    }
  };

  const loadingComponent = () =>
    isLoading ? (
      <div className="mt-5">
        <Spinner />
      </div>
    ) : null;

  return (
    <Container>
      { contractDetailsData && canEditContractDetails && (
        <SetContractDetailsModal
          show={!!contractDetailsData}
          onClose={() => setContractDetailsData(null)}
          onSubmitCompleted={handleSubmitContractDetails}
          data={contractDetailsData}
          eventTracker={eventTracker}
        />
      )}
      {loadingComponent() ?? !_.isEmpty(preJoiningData) ? (
        <Row className="justify-content-center pre-joing-tab">
            <Col md={6} lg={6} xl={6}>
              {isShowContractDetails && canViewContractDetails && (
                <>
                  <MainTitle
                    title="Contract Details"
                    button={(
                      <Button
                        variant="outline-primary"
                        size="sm"
                        onClick={handleSetContractDetailsData}
                        disabled={!canEditContractDetails}
                      >
                        {preJoiningData?.seafarer_status_history?.contract_length ? 'Edit Contract Details' : 'Update Contract Details'}
                      </Button>
                    )}
                  />
                  <TableBody
                    data={
                      PreJoiningTabModel(preJoiningData, isDisabledSetWagesBtn, history, eventTracker)
                        .contract_details
                    }
                  />
                </>
              )}
              <MainTitle title={`Joining Expenses`} />
              <TableBody
                data={
                  PreJoiningTabModel(preJoiningData, isDisabledSetWagesBtn, history, eventTracker)
                    .joining_expenses
                }
              />
              <MainTitle title={`Joining Deduction`} />
              <TableBody
                data={
                  PreJoiningTabModel(preJoiningData, isDisabledSetWagesBtn, history, eventTracker)
                    .joining_deduction
                }
              />
              <div className="px-3 pb-3 mb-3 last-edited-by-line">
                {(preJoiningData.created_by || preJoiningData.created_at) && (
                  <LastEditedByLine
                    updated_after_created={false}
                    created_by={preJoiningData.created_by}
                    updated_by={preJoiningData.updated_by}
                    created_at={preJoiningData.created_at}
                    updated_at={preJoiningData.updated_at}
                  />
                )}

                {(preJoiningData.updated_by || preJoiningData.updated_at) && (
                  <LastEditedByLine
                    updated_after_created={true}
                    created_by={preJoiningData.created_by}
                    updated_by={preJoiningData.updated_by}
                    created_at={preJoiningData.created_at}
                    updated_at={preJoiningData.updated_at}
                  />
                )}
              </div>
            </Col>
            <Col md={6} lg={6} xl={6}>
              <MainTitle
                title={`Wage Details`}
                button={(
                  <Button
                    variant="btn btn-outline-primary btn-sm"
                    onClick={() => {
                      eventTracker('setWagesButton', '');
                      history.push(`/seafarer/details/${seafarerId}/pre-joining/wages/add`, {
                        from: history.location.pathname,
                      });
                    }}
                    disabled={isDisabledSetWagesBtn}
                  >
                    Set Wages
                  </Button>
                )}
              />
              <TableBody
                data={
                  PreJoiningTabModel(preJoiningData, isDisabledSetWagesBtn, history, eventTracker)
                    .wages_details
                }
              />
              <SubTitle title="Allotment Details" />
              <TableBody
                data={
                  PreJoiningTabModel(preJoiningData, isDisabledSetWagesBtn, history, eventTracker)
                    .allotment_details
                }
              />
              <SubTitle title="Beneficiary Details" />
              <TableBody
                data={
                  PreJoiningTabModel(bankDetails, isDisabledSetWagesBtn, history, eventTracker)
                    .beneficiary_details
                }
              />
            </Col>
          </Row>
        ) : (
          <div className="no-result-found">
            <Icon icon="alert" className="alert-icon-no-search" style={{ float: 'none' }} />
            <div>
              <b>No Approved Crew Assignment</b>
            </div>
          </div>
        )}
      </Container>
  );
};

export default PreJoiningTab;
