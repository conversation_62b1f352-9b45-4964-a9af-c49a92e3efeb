import React from 'react';
import { Button } from 'react-bootstrap';
import _ from 'lodash';
import {
  capitalizeArgs,
  capitalizeFirstLetter,
  decimalString,
  valueOrDash,
  Dash,
  dateAsString,
} from '../../model/utils';
import AccountDetails from '../../model/AccountDetails';
import { JOINING_ALLOWANCES, JOINING_DEDUCTIONS, NOMINEE_MAPPING } from '../../constants/pre-joining-details';
import { RouteComponentProps } from 'react-router-dom';
import { DEFAULT_CURRENCY_UNIT } from '@src/constants/seafarer-wages';

const ShowRecommendedWages = ({ recommended, total, unit }: { recommended: number; total: number, unit: string }) => {
  let classname = '';
  if (total !== recommended) {
    classname = 'font-weight-bold font-red';
  }
  const amount = decimalString(recommended);
  return <div className={classname}>{amount ? `${unit} ${amount}` : Dash}</div>;
};
const getCurrencyText = (data: any,amountKey: string,unitKey: string) => {
  const amount = decimalString(_.get(data, amountKey, null));
  const unit = _.get(data, unitKey, null) || DEFAULT_CURRENCY_UNIT;
  return amount ? `${unit?.toUpperCase()} ${amount}` : Dash;
}
const PreJoiningTabModel = (
  data: any,
  isDisabledSetWagesBtn: boolean,
  history: RouteComponentProps['history'],
  eventTracker: (type: string, value: string) => {},
) => {
  const getFullName = (data: any) => {
    const firstName = valueOrDash(data, ['account_holder_first_name'], true);
    const middleName = valueOrDash(data, ['account_holder_middle_name'], true);
    const lastName = valueOrDash(data, ['account_holder_last_name'], true);
    const fullName = capitalizeArgs(firstName, middleName, lastName) || Dash;
    return fullName;
  };
  const currencyUnit = _.get(data, 'seafarer_status_history.recommended_wages_unit', null)?.toUpperCase() || DEFAULT_CURRENCY_UNIT;
  return {
    contract_details: [
      {
        label: 'Vessel',
        value: (
          <Button
            variant="link"
            onClick={() => {
              if (_.get(data, 'seafarer_status_history.vessel_ownership_id', undefined)) {
                history.push(
                  `/vessel/ownership/details/${_.get(
                    data,
                    'seafarer_status_history.vessel_ownership_id',
                    undefined,
                  )}`,
                );
              }
            }}
          >
            {_.get(data, 'seafarer_status_history.vessel_name')}
          </Button>
        ),
      },
      {
        label: 'Expected Sign On Date',
        value: data?.seafarer_status_history?.expected_contract_start_date ? dateAsString(data?.seafarer_status_history?.expected_contract_start_date) : Dash,
      },
      {
        label: 'Length of the Contract (In Months)',
        value: data?.seafarer_status_history?.contract_length ?? Dash,
      },
      {
        label: 'Repatriation Port',
        value: data?.seafarer_status_history?.repatriation_port ?? Dash,
      },
      {
        label: 'Embarkation Port',
        value: data?.seafarer_status_history?.embarkation_port ?? Dash,
      },
      {
        label: 'Nominee',
        value: NOMINEE_MAPPING[data?.seafarer_status_history?.nominee] ?? Dash,
      },
    ],
    joining_expenses: _.get(data, 'seafarer_joining_spendings', [])
      .filter((item) => item.payhead.category === JOINING_ALLOWANCES)
      .map((i) => {
        return {
          label: i.payhead.head_name,
          value: (
            <div className="row">
              <div className="col-sm-3 px-3">{i?.amount ? `${currencyUnit} ${decimalString(i?.amount)}`: ''}</div>
              <div className="col px-3">{capitalizeFirstLetter(i?.remarks) ?? ''}</div>
            </div>
          ),
        };
      }),
    joining_deduction: _.get(data, 'seafarer_joining_spendings', [])
      .filter((item) => item.payhead.category === JOINING_DEDUCTIONS)
      .map((i) => {
        return {
          label: i.payhead.head_name,
          value: (
            <div className="row">
              <div className="col-sm-3 px-3">{i?.amount ? `${currencyUnit} ${decimalString(i?.amount)}`: ''}</div>
              <div className="col px-3">{capitalizeFirstLetter(i?.remarks) ?? ''}</div>
            </div>
          ),
        };
      }),
    wages_details: [
      {
        label: 'Basic Salary',
        value: getCurrencyText(data, 'seafarer_wages.amount_basic','seafarer_wages.amount_unit'),
      },
      {
        label: 'Total Salary',
        value: getCurrencyText(data, 'seafarer_wages.amount_total','seafarer_wages.amount_unit'),
      },
      {
        label: 'Recommended Wages',
        value: (
          <ShowRecommendedWages
            unit={currencyUnit}
            recommended={_.get(data, 'seafarer_status_history.recommended_wages', null)}
            total={_.get(data, 'seafarer_wages.amount_total', null)}
          />
        ),
      },
    ],
    allotment_details: [
      {
        label: 'Monthly Allotment',
        value: decimalString(_.get(data, 'seafarer_allotment.monthly_allotment', null)) ?? Dash,
      },
      {
        label: 'First Month Allotment',
        value: decimalString(_.get(data, 'seafarer_allotment.first_allotment', null)) ?? Dash,
      },
    ],
    beneficiary_details: [
      {
        label: 'Bank Name',
        value: <div className="font-weight-bold">{data?.bank_name ?? Dash}</div>,
      },
      ..._.slice(AccountDetails(data), 1, 3),
      {
        label: 'Account Type',
        value: data?.account_type ?? Dash,
      },
      {
        label: 'FCNR Deposit Period',
        value: data?.fcnr_months ? `${data?.fcnr_months} months` : Dash,
      },
      ..._.slice(AccountDetails(data), 3, 5),
      {
        label: 'CNAPS Number',
        value: data?.cnaps ?? Dash,
      },
      ..._.slice(AccountDetails(data), 5, 6),
      ..._.slice(AccountDetails(data), 7, 8),
      ..._.slice(AccountDetails(data), 23, 24),
      {
        single: true,
        label: 'Special Remittance Instructions',
        value: <div className="text-justify">{data?.special_remittence_instrcution ?? Dash}</div>,
      },
      {
        single: true,
        label: 'Remark',
        value: <div className="text-justify">{data?.remarks ?? Dash}</div>,
      },
      {
        label: 'Account Holder Name',
        value: <div className="font-weight-bold">{getFullName(data)}</div>,
      },
      ..._.slice(AccountDetails(data), 9, 12),
      ..._.slice(AccountDetails(data), 20, 23),
      ..._.slice(AccountDetails(data), 12, 14),
    ],
  };
};

export default PreJoiningTabModel;
