/* eslint-disable react/display-name */
/* eslint-disable react/prop-types */
import React from 'react';
import { useTable, useSortBy, useFlexLayout } from 'react-table';
import { useSticky } from 'react-table-sticky';
import { v4 as uuid } from 'uuid';
import NoRecord from '../common/NoRecords';
import Spinner from '../common/Spinner';

const DocumentTable = ({ columns, data, loading, dataTestId = '' }) => {
  const { getTableProps, getTableBodyProps, headerGroups, prepareRow, rows } = useTable(
    {
      columns,
      data,
    },
    useSortBy,
    useFlexLayout,
    useSticky,
  );

  return (
    <div className="seafarer-table" data-testid={dataTestId}>
      <div {...getTableProps()} className="table sticky">
        <div className="header">
          {headerGroups.map((headerGroup) => (
            <div key={uuid()} {...headerGroup.getHeaderGroupProps()} className="tr">
              {headerGroup.headers.map((column) => {
                const thProps = column.getHeaderProps(column.getSortByToggleProps());
                return (
                  <div key={column.id} {...thProps} className="th">
                    {column.render('Header')}
                  </div>
                );
              })}
            </div>
          ))}
        </div>
        {loading ? (
          <Spinner alignClass={`load-spinner`} />
        ) : (
          <div {...getTableBodyProps()} className="body">
            {rows.length > 0
              ? rows.map((row, index) => {
                  prepareRow(row);

                  const isOriginal = row?.original?.isOriginal;
                  const rowClassNames = isOriginal ? 'tr document-table-is-national-row' : 'tr';

                  return (
                    <div key={row.id} {...row.getRowProps()} className={rowClassNames}>
                      {row.cells.map((cell) => {
                        const tdProps = cell.getCellProps();
                        return (
                          <div
                            key={tdProps.key}
                            data-testid={`${dataTestId}-${cell.column.id}-${index}`}
                            {...tdProps}
                            className="td"
                          >
                            {cell.render('Cell')}
                          </div>
                        );
                      })}
                    </div>
                  );
                })
              : !loading && <NoRecord />}
          </div>
        )}
      </div>
    </div>
  );
};

export default DocumentTable;
