import React, { useState, useEffect } from 'react';
import { Container, Col, Row } from 'react-bootstrap';
import moment from 'moment-timezone';
import seafarerService from '../service/seafarer-service';

const Location = () => {
  return (
    <div className="ship-report-wrapper__location">
      <div className="ship-report-wrapper__bold">→ South Korea-Pusan</div>
      <div>ETA: 06 May 2020 00:00</div>
      <div>ETB: 06 May 2020 02:00</div>
      <div>ETD: 07 May 2020 14:00</div>
    </div>
  );
};
const ShipReport = ({ imo }) => {
  const [lastPosition, setLastPosition] = useState({});

  useEffect(() => {
    (async () => {
      if (imo) {
        try {
          let response = await seafarerService.getLastPosition(imo);
          setLastPosition(response.data);
        } catch (error) {}
      }
    })();
  }, []);

  return (
    <Container className="ship-report-wrapper">
      <Row className="ship-report-wrapper__first-row">
        <Col md={6}>
          <div className="ship-report-wrapper__bold">Master</div>
          <div className="ship-report-wrapper__underline">Mankkaleswaran Markandu</div>
        </Col>
        <Col md={6}>
          <div className="ship-report-wrapper__bold">Chief Engineer</div>
          <div className="ship-report-wrapper__underline">Oleksandr Kozareznyuk</div>
        </Col>
      </Row>

      <Row>
        <Col md={6}>
          <Row className="ship-report-wrapper__second-row">
            <Col>
              <div className="ship-report-wrapper__bold">Last Position</div>
              <div className="ship-report-wrapper__underline">
                <a
                  target="_blank"
                  href={`https://maps.google.com/maps?q=${lastPosition.lat}+${lastPosition.lon}`}
                >
                  {lastPosition.position}
                </a>
              </div>
              <div>
                {lastPosition.time &&
                  moment
                    .utc(lastPosition.time)
                    .tz('Asia/Hong_Kong')
                    .format('DD MMM YYYY HH:mm')}{' '}
                (HKT)
              </div>
              <div>(StratumFive)</div>
            </Col>
          </Row>
          <Row className="ship-report-wrapper__third-row">
            <Col>
              <div className="ship-report-wrapper__bold">Sea Report</div>
              <div>08 Apr 2020</div>
              <div>16:30 (SMT) / 14:30 (GMT)</div>
              <div>PORT: Suez Canal</div>
              <div>
                POSITION:{' '}
                <a target="_blank" href="https://maps.google.com/maps?q=-8.792057+115.123434">
                  26157N35826E
                </a>
              </div>
              <div>NEXT PORT: Jebel Ali</div>
              <div>ETA: 16 Apr 2020 11:00</div>
            </Col>
          </Row>
        </Col>
        <Col md={6}>
          <Row className="ship-report-wrapper__fourth-row">
            <div className="ship-report-wrapper__bold">Itinerary</div>
            <Location />
            <Location />
            <Location />
            <div>View full itinerary</div>
          </Row>
        </Col>
      </Row>
    </Container>
  );
};

export default ShipReport;
