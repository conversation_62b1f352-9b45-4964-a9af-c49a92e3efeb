/* eslint-disable react/prop-types */
import React, { useState, useEffect } from 'react';
import { Row, Col, Button, Modal, Form, InputGroup } from 'react-bootstrap';
import { Formik } from 'formik';
import * as yup from 'yup';
import { orderBy } from 'lodash';
import DropDownSearchControl from '../AddSeafarer/DropDownSearchControl';
import FleetDatePicker from '../AddSeafarer/FleetDatePicker';
import {
  createSeafarerExp,
  getSeafarerDropDownData,
  patchSeafarerExp,
  getDropDownDataFromVessel,
} from '../../service/seafarer-service';
import { dateAsDayAndTime } from '../../model/utils';
import { getVesselOwnershipList, getVesselOwnershipById } from '../../service/vessel-service';
import Spinner from '../../component/common/Spinner';
import { DEFAULT_CURRENCY_UNIT } from '@src/constants/seafarer-wages';

const getEmptySeafarerExp = () => {
  const item = {
    vessel_name: '',
    vessel_type: '',
    rank_id: '',
    start_date: '',
    end_date: '',
    owner_name: '',
    engine_type: '',
    engine_sub_type: '',
    salary: '',
    brake_horse_power: '',
    deadweight_tonnage: '',
    deadweight_gross_registered_tonnage: '',
    signoff_reason_id: '',
    is_company_vessel: '',
  };
  return { ...item };
};

const seafarerExpSchema = yup.object({
  vessel_name: yup.string().nullable().required('Vessel Name is required'),
  vessel_type: yup.string().nullable().required('Vessel Type is required'),
  rank_id: yup.number().nullable().required('Rank is required'),
  start_date: yup.date().nullable().required('Start Date is required'),
  end_date: yup
    .date()
    .nullable()
    .required('End Date is required')
    .min(yup.ref('start_date'), 'End Date should be greater than Start Date'),
  owner_name: yup.string().nullable(),
  engine_type: yup.string().nullable(),
  engine_sub_type: yup.string().nullable(),
  salary: yup
    .number()
    .nullable()
    .positive('Salary must be positive integer')
    .test('maxDecimalPlaces', 'Salary input must be upto 2 decimal places', (number) => {
      if (number) {
        const num = number.toString().split('.');
        if (num.length === 2) {
          return num[1].length <= 2;
        }
      }
      return true;
    }),
  brake_horse_power: yup.number().nullable(),
  deadweight_tonnage: yup.number().nullable(),
  deadweight_gross_registered_tonnage: yup.string().nullable(),
  signoff_reason_id: yup.number().nullable(),
});

const defaultDropDownValue = {
  vessels: [],
  signoffReason: [],
  ranks: [],
  vesselTypes: [],
  miscEngines: [],
};

const AddExperience = ({
  seafarer_id,
  cancelButtonHandler,
  expToEdit,
  eventTracker,
  canEditVesselDetails,
  setLoadExperiencePage,
  loadExperiencePage,
}) => {
  const [seafarerExp, setSeafarerExp] = useState(getEmptySeafarerExp());
  const [triedValidate, setTriedValidate] = useState(false);
  const [dropDownData, setDropDownData] = useState(defaultDropDownValue);
  const [loading, setLoading] = useState(false);
  const currencyUnit = seafarerExp?.salary_unit?.toUpperCase() || DEFAULT_CURRENCY_UNIT;
  const formatVesselDropdownValues = (ownerships) => {
    const modifiedData = [];
    ownerships?.forEach((ownership) => {
      modifiedData.push({
        id: {
          ref_id: ownership.vessel_p1_ref_id,
          name: ownership.ownership_name ? ownership.ownership_name : '---',
          id: ownership.ownership_id,
          currency_unit: ownership?.misc_currency?.value || DEFAULT_CURRENCY_UNIT
        },
        value: ownership.ownership_name ? ownership.ownership_name : '---',
      });
    });
    const sortedModifiedData = orderBy(modifiedData, ['value'], ['asc']);
    return { vessels: sortedModifiedData };
  };

  const formatMiscEnginesValues = (miscEngines) => {
    const modifiedData = [];
    miscEngines?.map((engine) => {
      modifiedData.push({
        id: engine.id,
        value: engine.value,
      });
    });
    const sortedModifiedData = orderBy(modifiedData, ['value'], ['asc']);
    return { miscEngines: sortedModifiedData };
  };

  const formatVesselTypesValues = (vesselTypes) => {
    const sortedModifiedData = orderBy(vesselTypes, ['value'], ['asc']);
    return { vesselTypes: sortedModifiedData };
  };

  useEffect(() => {
    (async () => {
      setLoading(true);
      try {
        const query = '?values=ranks&values=signoffReason';

        let [outcome1, outcome2, outcome3] = [];
        try {
          [outcome1, outcome2, outcome3] = await Promise.allSettled([
            getSeafarerDropDownData(query),
            getDropDownDataFromVessel(),
            getVesselOwnershipList(),
          ]);
        } catch (err) {
          console.log('Error fetching dropdown data', err);
          //do not further throw error to allow values that are successfully fetched to be populated to the UI
        }

        let newValues = defaultDropDownValue;

        if (outcome1 && outcome1?.status === 'fulfilled') {
          newValues = {
            ...newValues,
            ...outcome1.value,
          };
        }

        if (outcome2 && outcome2?.status === 'fulfilled') {
          newValues = {
            ...newValues,
            ...formatVesselTypesValues(outcome2.value?.data?.vesselTypes),
            ...formatMiscEnginesValues(outcome2.value?.data?.miscEngines),
          };
        }

        if (outcome3 && outcome3?.status === 'fulfilled') {
          newValues = {
            ...newValues,
            ...formatVesselDropdownValues(outcome3.value),
          };
        }

        if (expToEdit) {
          const isOldVesselNamePresent =
            newValues.vessels.find((ele) => ele?.value === expToEdit?.vessel_name) !== undefined;

          if (!isOldVesselNamePresent) {
            // overwrite then old vessel name corresponding to ownership_id
            newValues = {
              ...newValues,
              vessels: newValues.vessels.map((ele) => {
                if (ele.id.id === expToEdit.vessel_ownership_id) {
                  return {
                    id: { ...ele.id, name: expToEdit.vessel_name },
                    value: expToEdit.vessel_name,
                  };
                }
                return ele;
              }),
            };
          }
        }

        setDropDownData(newValues);
        if (expToEdit) {
          setSeafarerExp({
            ...expToEdit,
            is_company_vessel: expToEdit?.vessel_ref_id ? 'true' : 'false',
            vessel_name: expToEdit?.vessel_ref_id
              ? newValues.vessels.find((ele) => ele.value === expToEdit.vessel_name)?.id
              : expToEdit.vessel_name,
            vessel_type: newValues.vesselTypes.find((ele) => ele.value === expToEdit.vessel_type)
              ?.id,
            engine_type: newValues.miscEngines.find((ele) => ele.value === expToEdit.engine_type)
              ?.id,
          });
        }
      } catch (error) {
        console.trace(error);
      }
      setLoading(false);
    })();
  }, [expToEdit]);

  const onSubmitSeafarer = async () => {
    try {
      setLoading(true);
      const payload = {
        deadweight_gross_registered_tonnage:
          seafarerExp.deadweight_gross_registered_tonnage || null,
        end_date: seafarerExp.end_date,
        engine_sub_type: seafarerExp.engine_sub_type || null,
        engine_type:
          dropDownData.miscEngines.find((ele) => ele.id === seafarerExp.engine_type)?.value || null,
        owner_name: seafarerExp.owner_name || null,
        rank_id: seafarerExp.rank_id,
        signoff_reason_id: seafarerExp.signoff_reason_id || null,
        start_date: seafarerExp.start_date,
        vessel_name: seafarerExp?.vessel_name?.name || seafarerExp?.vessel_name,
        vessel_type: dropDownData.vesselTypes.find((ele) => ele.id === seafarerExp.vessel_type)
          ?.value,
        seafarer_id,
        salary: (seafarerExp?.salary && Number(seafarerExp.salary)) || null,
        salary_unit: seafarerExp?.salary_unit || DEFAULT_CURRENCY_UNIT.toLowerCase(),
        brake_horse_power: Number(seafarerExp.brake_horse_power),
        deadweight_tonnage: Number(seafarerExp.deadweight_tonnage),
        vessel_ref_id: seafarerExp?.vessel_name?.ref_id || null,
        vessel_ownership_id: seafarerExp?.vessel_ownership_id || null,
        vessel_tech_group: seafarerExp?.vessel_tech_group || null,
      };
      if (expToEdit) {
        const unchangedFields = Object.keys(payload).filter(
          (i) => _.get(payload, i) === _.get(expToEdit, i),
        );
        await patchSeafarerExp(expToEdit.id, _.omit(payload, unchangedFields));
      } else {
        await createSeafarerExp(payload);
      }
      cancelButtonHandler();
      setLoadExperiencePage(!loadExperiencePage);
    } catch (error) {
      console.log(`Error: ${error}`);
    }
    if (expToEdit) {
      eventTracker('editExperience', 'Submit Edit Experience');
    }
    setLoading(false);
  };
  return (
      <Modal
        id="seafarer-experience-popup"
        show={true}
        aria-labelledby="add-seafarer-experience-modal"
        centered
        size="lg"
        backdrop="static"
      >
        <Modal.Header>
          <Modal.Title style={{ borderBottom: '0' }}>
            {'Add/Edit Vessel Experience'}
            <div className="required-field-text">* Required fields</div>
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Formik
            validationSchema={seafarerExpSchema}
            enableReinitialize={true}
            initialValues={seafarerExp}
            validateOnChange={triedValidate}
            validateOnBlur={triedValidate}
            validateOnMount={!!seafarerExp.id}
          >
            {({ handleSubmit, handleBlur, values, errors, setFieldValue, validateForm }) => {
              const onValidateForm = async () => {
                const errors = await validateForm();
                return {
                  isFormValid: !Object.keys(errors).length,
                };
              };

              const onSubmitValues = () => {
                onValidateForm().then(({ isFormValid }) => {
                  setTriedValidate(true);
                  if (isFormValid) {
                    onSubmitSeafarer();
                  }
                });
              };

              const onSeafarerChange = (changedSeafarerExp, field, value) => {
                if (field) {
                  setFieldValue(field, value);
                }

                setSeafarerExp(changedSeafarerExp);
              };

              const onInputChange = async (event) => {
                const { name, value } = event.target;
                const changedSeafarerExp =
                  name === 'is_company_vessel' ? getEmptySeafarerExp() : { ...seafarerExp };
                if (name === 'vessel_name' && value?.id && values.is_company_vessel === 'true') {
                  setLoading(true);
                  const { data } = await getVesselOwnershipById(value?.id);
                  changedSeafarerExp.vessel_type = data?.vessel_type?.id ?? '';
                  changedSeafarerExp.owner_name = data?.owner.value ?? '';
                  changedSeafarerExp.brake_horse_power = data?.vessel?.bhp ?? '';
                  changedSeafarerExp.deadweight_tonnage = data?.vessel?.dwt ?? '';
                  changedSeafarerExp.deadweight_gross_registered_tonnage = 'D';
                  changedSeafarerExp.engine_type = data?.vessel?.misc_engine?.id ?? '';
                  changedSeafarerExp.vessel_ownership_id = data?.id ?? '';
                  changedSeafarerExp.vessel_tech_group = data?.fleet_staff?.tech_group ?? '';
                  setLoading(false);
                }
                if (name == 'vessel_name' && value?.currency_unit) {
                  eventTracker('SeafarerExperienceCurrencyUnit', value.currency_unit)
                  changedSeafarerExp['salary_unit'] = value.currency_unit;
                }
                changedSeafarerExp[name] = value;
                onSeafarerChange(changedSeafarerExp, name, value);
              };
              let isDisabled;
              if (values?.is_company_vessel) {
                if (expToEdit && canEditVesselDetails) {
                  isDisabled = false;
                } else if (values.is_company_vessel !== 'false') {
                  isDisabled = true;
                }
              }
              const lastUpdatedBy = seafarerExp?.last_updated_by_hash
                ? `by ${seafarerExp?.last_updated_by_hash}`
                : '';
              const isDisabledOnEdit = canEditVesselDetails ? false : !!expToEdit;
              return (
                <>
                  {!loading ? (
                    <Form noValidate onSubmit={handleSubmit}>
                      <Row>
                        <Col>
                          <div className="font-weight-bold vessel-form-label">VESSEL</div>
                        </Col>
                      </Row>
                      <Form.Row>
                        <Form.Group as={Col} md="5">
                          <Form.Label>Company Vessel</Form.Label>
                          <div>
                            <Form.Check
                              inline
                              type="radio"
                              name="is_company_vessel"
                              onChange={onInputChange}
                              isInvalid={!!errors.is_company_vessel}
                              label={'Yes'}
                              value="true"
                              defaultChecked={values.is_company_vessel === 'true'}
                              disabled={isDisabledOnEdit}
                              data-testid="fml-seafarer-experience-add-experience-company-vessel-yes"
                            ></Form.Check>
                            <Form.Check
                              inline
                              type="radio"
                              name="is_company_vessel"
                              onChange={onInputChange}
                              isInvalid={!!errors.is_company_vessel}
                              label={'No'}
                              value="false"
                              defaultChecked={values.is_company_vessel === 'false'}
                              disabled={isDisabledOnEdit}
                              data-testid="fml-seafarer-experience-add-experience-company-vessel-false"
                            ></Form.Check>
                            <Form.Control.Feedback
                              type="invalid"
                              className={errors.is_company_vessel ? 'set-display-block' : ''}
                            >
                              {errors.is_company_vessel}
                            </Form.Control.Feedback>
                          </div>
                        </Form.Group>
                        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
                          <Form.Label>Vessel Name*</Form.Label>
                          {values.is_company_vessel === 'false' ? (
                            <Form.Control
                              type="text"
                              name="vessel_name"
                              value={values.vessel_name ?? ''}
                              onChange={onInputChange}
                              isInvalid={!!errors.vessel_name}
                            />
                          ) : (
                            <DropDownSearchControl
                              name={'vessel_name'}
                              selectedValue={values.vessel_name ?? ''}
                              dropDownValues={dropDownData.vessels}
                              onInputChange={onInputChange}
                              onBlur={handleBlur}
                              isInvalid={!!errors.vessel_name}
                              testID={'vessel-name'}
                              disabled={isDisabledOnEdit}
                            />
                          )}
                          <Form.Control.Feedback type="invalid">
                            {errors.vessel_name}
                          </Form.Control.Feedback>
                        </Form.Group>
                      </Form.Row>
                      <Form.Row>
                        <Form.Group as={Col} md="5">
                          <Form.Label>Vessel Type*</Form.Label>
                          <DropDownSearchControl
                            name={'vessel_type'}
                            selectedValue={values.vessel_type ?? ''}
                            dropDownValues={dropDownData.vesselTypes}
                            onBlur={handleBlur}
                            isInvalid={!!errors.vessel_type}
                            onInputChange={onInputChange}
                            disabled={isDisabled}
                            testID={'vessel-type'}
                          />
                          <Form.Control.Feedback type="invalid">
                            {errors.vessel_type}
                          </Form.Control.Feedback>
                        </Form.Group>
                        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
                          <Form.Label>Owner</Form.Label>
                          <Form.Control
                            type="text"
                            name="owner_name"
                            value={values.owner_name ?? ''}
                            onChange={onInputChange}
                            isInvalid={!!errors.owner_name}
                            disabled={isDisabled}
                            data-testid="fml-seafarer-experience-add-experience-owner"
                          />
                          <Form.Control.Feedback type="invalid">
                            {errors.owner_name}
                          </Form.Control.Feedback>
                        </Form.Group>
                      </Form.Row>
                      <Form.Row>
                        <Form.Group as={Col} md={5}>
                          <Form.Label>Engine Type</Form.Label>
                          <DropDownSearchControl
                            name={'engine_type'}
                            selectedValue={values.engine_type ?? ''}
                            dropDownValues={dropDownData.miscEngines}
                            onBlur={handleBlur}
                            isInvalid={!!errors.engine_type}
                            onInputChange={onInputChange}
                            disabled={isDisabled}
                            testID={'engine-type'}
                          />
                          <Form.Control.Feedback type="invalid">
                            {errors.engine_type}
                          </Form.Control.Feedback>
                        </Form.Group>
                        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
                          <Form.Label>Engine Model</Form.Label>
                          <Form.Control
                            type="text"
                            name="engine_sub_type"
                            value={values.engine_sub_type ?? ''}
                            onChange={onInputChange}
                            isInvalid={!!errors.engine_sub_type}
                            disabled={isDisabled}
                            data-testid="fml-seafarer-experience-add-experience-engine-model"
                          />
                          <Form.Control.Feedback type="invalid">
                            {errors.engine_sub_type}
                          </Form.Control.Feedback>
                        </Form.Group>
                      </Form.Row>
                      <Form.Row>
                        <Form.Group as={Col} md="5">
                          <Form.Label>Brake Horsepower (BHP)</Form.Label>
                          <Form.Control
                            type="number"
                            name="brake_horse_power"
                            value={values.brake_horse_power ?? ''}
                            onChange={onInputChange}
                            isInvalid={!!errors.brake_horse_power}
                            disabled={isDisabled}
                            data-testid="fml-seafarer-experience-add-experience-brake-horsepower"
                          />
                          <Form.Control.Feedback type="invalid">
                            {errors.brake_horse_power}
                          </Form.Control.Feedback>
                        </Form.Group>
                        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
                          <Form.Label>DWT/GRT</Form.Label>
                          <Form.Control
                            type="number"
                            name="deadweight_tonnage"
                            value={values.deadweight_tonnage}
                            onChange={onInputChange}
                            isInvalid={!!errors.deadweight_tonnage}
                            disabled={isDisabled}
                            data-testid="fml-seafarer-experience-add-experience-brake-dwt-grt"
                          />
                          <Form.Control.Feedback type="invalid">
                            {errors.deadweight_tonnage}
                          </Form.Control.Feedback>
                          <div>
                            <Form.Check
                              inline
                              label="DWT"
                              name="deadweight_gross_registered_tonnage"
                              type="radio"
                              value="D"
                              onChange={onInputChange}
                              defaultChecked={values.deadweight_gross_registered_tonnage === 'D'}
                              id="inline-radio-dwt"
                              disabled={isDisabled}
                            />
                            <Form.Check
                              inline
                              label="GRT"
                              name="deadweight_gross_registered_tonnage"
                              type="radio"
                              value="G"
                              onChange={onInputChange}
                              defaultChecked={values.deadweight_gross_registered_tonnage === 'G'}
                              id="inline-radio-grt"
                              disabled={isDisabled}
                            />
                          </div>
                        </Form.Group>
                      </Form.Row>
                      <Row>
                        <Col>
                          <div className="font-weight-bold vessel-form-label">
                            CONTRACT INFORMATION
                          </div>
                        </Col>
                      </Row>
                      <Form.Row>
                        <Form.Group as={Col} md="5">
                          <Form.Label>Rank*</Form.Label>
                          <DropDownSearchControl
                            name={'rank_id'}
                            selectedValue={seafarerExp.rank_id ?? ''}
                            dropDownValues={dropDownData.ranks}
                            onBlur={handleBlur}
                            isInvalid={!!errors.rank_id}
                            onInputChange={onInputChange}
                            testID={'rank-id'}
                          />
                          <Form.Control.Feedback type="invalid">
                            {errors.rank_id}
                          </Form.Control.Feedback>
                        </Form.Group>
                        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
                          <Form.Label>Salary</Form.Label>
                          <InputGroup>
                            <InputGroup.Prepend>
                              <InputGroup.Text>{currencyUnit}</InputGroup.Text>
                            </InputGroup.Prepend>
                            <Form.Control
                              type="number"
                              name="salary"
                              className="wages-input"
                              value={seafarerExp?.salary ?? ''}
                              onChange={onInputChange}
                              isInvalid={!!errors.salary}
                              data-testid="fml-seafarer-experience-add-experience-salary"
                            />
                          </InputGroup>
                          <Form.Control.Feedback type="invalid">
                            {errors.salary}
                          </Form.Control.Feedback>
                        </Form.Group>
                      </Form.Row>
                      <Form.Row>
                        <Form.Group as={Col} md="5">
                          <Form.Label>Start Date*</Form.Label>
                          <FleetDatePicker
                            name="start_date"
                            value={seafarerExp.start_date}
                            onChange={(value) =>
                              onInputChange({ target: { name: 'start_date', value } })
                            }
                            isInvalid={!!errors?.start_date}
                            testID="fml-seafarer-experience-add-experience-start-date"
                          />
                          {errors?.start_date && (
                            <div className="invalid-feedback d-block">{errors.start_date}</div>
                          )}
                        </Form.Group>

                        <Form.Group as={Col} md={{ span: 5, offset: 1 }}>
                          <Form.Label>End Date*</Form.Label>
                          <FleetDatePicker
                            name="end_date"
                            value={seafarerExp.end_date ?? ''}
                            onChange={(value) =>
                              onInputChange({ target: { name: 'end_date', value } })
                            }
                            isInvalid={!!errors?.end_date}
                            testID="fml-seafarer-experience-add-experience-end-date"
                          />
                          {errors?.end_date && (
                            <div className="invalid-feedback d-block">{errors.end_date}</div>
                          )}
                        </Form.Group>
                      </Form.Row>
                      <Form.Row>
                        <Form.Group as={Col} md="5">
                          <Form.Label>Sign Off Reason</Form.Label>
                          <DropDownSearchControl
                            name={'signoff_reason_id'}
                            selectedValue={seafarerExp.signoff_reason_id ?? ''}
                            dropDownValues={dropDownData.signoffReason}
                            onBlur={handleBlur}
                            isInvalid={!!errors.signoff_reason_id}
                            onInputChange={onInputChange}
                            testID={'signoff-reason-id'}
                          />
                          <Form.Control.Feedback type="invalid">
                            {errors.signoff_reason_id}
                          </Form.Control.Feedback>
                        </Form.Group>
                      </Form.Row>
                      {expToEdit && (
                        <Row className="vessel-form-footer">
                          <Col>{`Last Edited  ${lastUpdatedBy} on ${dateAsDayAndTime(
                            seafarerExp?.updated_at,
                          )}`}</Col>
                        </Row>
                      )}
                      <Form.Row>
                        <div className="ml-auto">
                          <Button
                            variant="primary"
                            className="m-2"
                            onClick={cancelButtonHandler}
                            data-testid="fml-seafarer-experience-add-experience-cancel"
                          >
                            Cancel
                          </Button>
                          <Button
                            variant="secondary"
                            onClick={() => {
                              onSubmitValues();
                            }}
                            disabled={loading}
                            data-testid="fml-seafarer-experience-add-experience-add"
                          >
                            {expToEdit ? 'Save' : 'Add'}
                          </Button>
                        </div>
                        <div className="col-1"></div>
                      </Form.Row>
                    </Form>
                  ) : (
                    <div className="mt-5">
                      {' '}
                      <Spinner />{' '}
                    </div>
                  )}
                </>
              );
            }}
          </Formik>
        </Modal.Body>
      </Modal>
  );
};

export default AddExperience;
