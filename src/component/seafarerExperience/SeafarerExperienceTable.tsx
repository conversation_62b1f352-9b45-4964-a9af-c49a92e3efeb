/* eslint-disable react/display-name */
/* eslint-disable react/prop-types */
import React from 'react';
import { useTable, useSortBy, useFlexLayout } from 'react-table';
import { useSticky } from 'react-table-sticky';
import { v4 as uuid } from 'uuid';
import Spinner from '../common/Spinner';
import styleGuide from '../../styleGuide';
import NoRecord from '../common/NoRecords';
import { ShowSalary } from './SeafarerTableMenuList';
import { Button } from 'react-bootstrap';
import { columnSortIconName } from '../../util/view-utils';
const { Icon } = styleGuide;

const generateColumns = (selectedColumns, roleConfig, seafarerExpEditHandler, seafarerExpDeleteHandler) => {
  return [
    ...selectedColumns,
    {
      Header: function () {
        return <div className="text-center">Actions</div>;
      },
      id: 'actions',
      accessor: (row, index) =>
        roleConfig.seafarer.edit.seafarerExperience ? (
          <div style={{ display: 'flex' }}>
            <Button
              variant="link"
              onClick={() => {
                seafarerExpEditHandler(row);
              }}
              data-testid={`edit-${index}-doc-exp-btn`}
            >
              <Icon icon="pencil" size={20} className="mr-3" />
            </Button>
            <Button
              variant="link"
              onClick={() => {
                seafarerExpDeleteHandler(row, index);
              }}
              data-testid={`delete-${index}-doc-exp-btn`}
            >
              <Icon icon="bin" size={20} />
            </Button>
          </div>
        ) : null,
      disableSortBy: true,
      maxWidth: 100,
      sticky: 'right',
    },
  ];
};

const SeafarerExperienceTable = ({
  vesselExperience,
  selectedColumns,
  loading,
  tableRef,
  seafarerExpEditHandler,
  seafarerExpDeleteHandler,
  roleConfig,
  eventTracker,
}) => {
  selectedColumns.forEach(e => {
    if (e.id === 'salary') {
      e.accessor = (row) => <ShowSalary row={row} eventTracker={eventTracker} />;
    } else {
      return e;
    }
  });
  let columns = generateColumns(selectedColumns, roleConfig, seafarerExpEditHandler, seafarerExpDeleteHandler);

  return (
    <div className="seafarer-table">
      <Table columns={columns} data={vesselExperience} tableRef={tableRef} loading={loading} />
    </div>
  );
};

const Table = ({ columns, data, loading, tableRef }) => {
  const { getTableProps, getTableBodyProps, headerGroups, prepareRow, rows } = useTable(
    {
      columns,
      data,
    },
    useSortBy,
    useFlexLayout,
    useSticky,
  );

  return (
    <div {...getTableProps()} className="table sticky" ref={tableRef}>
      <div className="header">
        {headerGroups.map((headerGroup) => (
          <div key={uuid()} {...headerGroup.getHeaderGroupProps()} className="tr">
            {headerGroup.headers.map((column) => {
              const thProps = column.getHeaderProps(column.getSortByToggleProps());
              return (
                <div key={column.id} {...thProps} className="th">
                  {column.render('Header')}
                  <span>
                    {column.canSort && (
                      <Icon
                        icon={columnSortIconName(column)}
                        size={20}
                        className="default float-none"
                      />
                    )}
                  </span>
                </div>
              );
            })}
          </div>
        ))}
      </div>
      {loading && <Spinner alignClass={`load-spinner`} />}
      <div {...getTableBodyProps()} className="body">
        {rows.length > 0
          ? rows.map((row, index) => {
              prepareRow(row);
              return (
                <div key={row.id} {...row.getRowProps()} className="tr">
                  {row.cells.map((cell) => {
                    const tdProps = cell.getCellProps();
                    return (
                      <div
                        key={tdProps.key}
                        {...tdProps}
                        data-testid={`${cell.column.Header}-${index}-exp`}
                        className={`td ${
                          row?.original?.vessel_ref_id ? 'vessel-company-type' : ''
                        }`}
                      >
                        {cell.render('Cell')}
                      </div>
                    );
                  })}
                </div>
              );
            })
          : !loading && <NoRecord />}
      </div>
    </div>
  );
};

export default SeafarerExperienceTable;
