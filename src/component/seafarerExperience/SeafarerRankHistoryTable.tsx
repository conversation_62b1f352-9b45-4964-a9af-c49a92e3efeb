/* eslint-disable react/display-name */
/* eslint-disable react/prop-types */
import React from 'react';
import { useTable, useSortBy, useFlexLayout } from 'react-table';
import { useSticky } from 'react-table-sticky';
import { v4 as uuid } from 'uuid';
import Spinner from '../common/Spinner';
import { getShortDate } from '../../util/view-utils';
import './scss/SeafarerExperience.scss';
import NoRecord from '../common/NoRecords';

const SeafarerRankHistoryTable = ({ rankHistoryData, loading, tableRef }) => {
  let columns = [
    {
      Header: 'Date of Promotion',
      id: 'date_of_promotion',
      maxWidth: 30,
      accessor: (row) => getShortDate(row.start_date),
    },
    {
      Header: 'Rank',
      id: 'rank',
      maxWidth: 40,
      accessor: (row) => row.value,
    },
    {
      Header: 'By',
      id: 'by',
      maxWidth: 100,
      accessor: (row) => row.created_by_hash,
    },
  ];

  return (
    <div className="seafarer-table">
      <Table columns={columns} data={rankHistoryData} tableRef={tableRef} loading={loading} />
    </div>
  );
};

const Table = ({ columns, data, loading, tableRef }) => {
  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    prepareRow,
    rows,
    // state: { sortBy },
  } = useTable(
    {
      columns,
      data,
    },
    useSortBy,
    useFlexLayout,
    useSticky,
  );

  return (
    <div {...getTableProps()} className="table rankHistory sticky" ref={tableRef}>
      <div className="header">
        {headerGroups.map((headerGroup) => (
          <div key={uuid()} {...headerGroup.getHeaderGroupProps()} className="tr">
            {headerGroup.headers.map((column) => {
              const thProps = column.getHeaderProps(column.getSortByToggleProps());
              return (
                <div key={column.id} {...thProps} className="th">
                  {column.render('Header')}
                </div>
              );
            })}
          </div>
        ))}
      </div>
      {loading && <Spinner alignClass={`load-spinner`} />}
      <div {...getTableBodyProps()} className="body">
        {rows.length > 0
          ? rows.map((row) => {
              prepareRow(row);
              return (
                <div key={row.id} {...row.getRowProps()} className="tr">
                  {row.cells.map((cell) => {
                    const tdProps = cell.getCellProps();
                    return (
                      <div key={tdProps.key} {...tdProps} className="td">
                        {cell.render('Cell')}
                      </div>
                    );
                  })}
                </div>
              );
            })
          : !loading && <NoRecord />}
      </div>
    </div>
  );
};

export default SeafarerRankHistoryTable;
