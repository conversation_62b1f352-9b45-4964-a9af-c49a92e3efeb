import React, { useState, useEffect } from 'react';
import { Row, Button, Modal, Form, Table, InputGroup } from 'react-bootstrap';
import { Dash, capitalizeArgs, dateAsString } from '../../model/utils';
import _ from 'lodash';
import 'react-datepicker/dist/react-datepicker.css';
import Spinner from '../common/Spinner';
import { useParams, useLocation } from 'react-router-dom';
import WagesDropDown from './WagesDropDown';
import { ViewWagesHistoryModalProps, WagesDropDownValue } from '../../types/viewWagesHistory';
import { getSefarerWagesHistory } from '../../service/seafarer-service';
import styleGuide from '../../styleGuide';
import { SeafarerWages } from '../../types/wagesInterfaces';
import moment from 'moment-timezone';
import { DEFAULT_CURRENCY_UNIT } from '@src/constants/seafarer-wages';
import { formatAmount } from '@src/util/view-utils';
const { Icon } = styleGuide;

const WagesHistoryModal = ({ seafarer, history, eventTracker }: ViewWagesHistoryModalProps) => {
  const [dropDownData, setDropDownData] = useState<WagesDropDownValue[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [response, setResponse] = useState<any>([]);
  const [payHeadData, setPayHeadData] = useState<any[]>([]);
  const [basicSalary, setBasicSalary] = useState('0.00');
  const [totalSalary, setTotalSalary] = useState('0.00');
  const [currencyUnit, setCurrencyUnit] = useState<string>(DEFAULT_CURRENCY_UNIT);

  const { seafarerId } = useParams();
  const query = useLocation().search;
  const statusHistoryId = new URLSearchParams(query).get('seafarer_status_history_id');
  const [data, setData] = useState<WagesDropDownValue>({
    id: 0,
    effectiveDate: '',
    totalSalary: 0,
  });
  const fullName =
    capitalizeArgs(
      _.get(seafarer, 'seafarer_person.first_name', ''),
      _.get(seafarer, 'seafarer_person.middle_name', ''),
      _.get(seafarer, 'seafarer_person.last_name', ''),
    ) || Dash;

  useEffect(() => {
    (async () => {
      loadInitialData();
    })();
  }, []);

  useEffect(() => {
    response.length && refreshData();
  }, [data.id]);

  const loadInitialData = async () => {
    setIsLoading(true);
    try {
      const response = await getSefarerWagesHistory(seafarerId, statusHistoryId);
      if (_.has(response.data, '[0].seafarer_wages_details_payheads')) {
        throw new Error('No existing applied wages details');
      }
      const sortedData = [...response.data].sort((a, b) => {
        return moment(b.effective_date) > moment(a.effective_date) ? 1 : -1;
      });
      setResponse(sortedData);
      extractPayHeadData(sortedData, sortedData[0].id);
      extractDropDownData(sortedData);
      setIsLoading(false);
    } catch (error) {
      console.log('Failed to fetch wages data ', error);
      setIsLoading(false);
    }
  };

  const extractPayHeadData = (response: SeafarerWages[], id: number) => {
    const wagesData = response?.find((i) => i?.id == id);
    const BasicSalary = _.get(wagesData, 'amount_basic', 0);
    const TotalSalary = _.get(wagesData, 'amount_total', 0);
    const payHeadData = _.get(wagesData, 'seafarer_wages_details', []);
    setPayHeadData(
      [...payHeadData].sort((a, b) => a.payhead.display_order - b.payhead.display_order),
    );
    setBasicSalary(_.toNumber(BasicSalary).toFixed(2));
    setTotalSalary(_.toNumber(TotalSalary).toFixed(2));
    setCurrencyUnit(_.get(wagesData,'amount_unit',DEFAULT_CURRENCY_UNIT)?.toUpperCase());
  };

  const extractDropDownData = (response: SeafarerWages[]) => {
    const data = response.map((i) => {
      return {
        id: _.toNumber(i?.id),
        effectiveDate: dateAsString(i?.effective_date),
        totalSalary: i?.amount_total,
      };
    });
    if (data.length > 0) setData(data[0]);
    setDropDownData(data);
  };

  const refreshData = () => {
    extractPayHeadData(response, data.id);
  };

  const handleInputChange = (value: any) => {
    setData(value);
  };

  const handleCancelButton = () => {
    history.push(`/seafarer/details/${seafarerId}/experience`);
  };

  const noDataComponent = () => {
    if (data.id === 0) {
      return (
        <>
          <div className="no-result-found mb-3 pb-3">
            <Icon icon="alert" className="alert-icon-no-search" style={{ float: 'none' }} />
            <div>
              <b>Wages details not found</b>
            </div>
          </div>
          <Modal.Footer>
            <div className="d-flex justify-content-end pr-3">
              <Button
                variant="secondary"
                className="m-2"
                onClick={handleCancelButton}
                data-testid="wages-history-modal-cancel-btn"
              >
                Cancel
              </Button>
            </div>
          </Modal.Footer>
        </>
      );
    }
    return null;
  };

  return (
    <Modal
      id="update-wages-and-promotions"
      show={true}
      aria-labelledby="update-wages-and-promotions-modal"
      centered
      size="xl"
      backdrop="static"
    >
      <Modal.Header>
        <Modal.Title className="mb-0 pl-2">
          {`Salary History of ${fullName} (${_.get(
            seafarer,
            'seafarer_rank.unit',
            Dash,
          )}) (${_.get(seafarer, 'hkid', Dash)})`}
        </Modal.Title>
      </Modal.Header>
      {isLoading ? (
        <div className="pb-5">
          <Spinner />
        </div>
      ) : (
        noDataComponent() ?? (
          <>
            <Modal.Body className="update-wages-modal-body px-3 py-2">
              <Form>
                <Row className="m-auto">
                  <Form.Group className="col-lg-5 mb-3">
                    <WagesDropDown
                      onInputChange={handleInputChange}
                      dropDownValues={dropDownData}
                      selectedValue={data}
                      eventTracker={eventTracker}
                      wagesUnit={currencyUnit}
                    />
                  </Form.Group>
                </Row>
              </Form>
              <Row>
                <div className="col-lg-6">
                  <div className="update-wages-table pt-3">
                    <Table>
                      <thead>
                        <tr className="row px-3">
                          <th className="col-7">Monthly Allowance</th>
                          <th className="col-5">Amount</th>
                        </tr>
                      </thead>
                      <tbody>
                        {payHeadData
                          .filter(
                            (item) =>
                              item?.payhead?.type === 'Allowance' && !item?.is_display_input_sheet,
                          )
                          .map((i, index) => {
                            return (
                              <tr key={i.id} className="row px-3">
                                <td className="col-7">{i?.payhead?.head_name}</td>
                                <td className="col-5">
                                <InputGroup>
                                  <InputGroup.Prepend>
                                    <InputGroup.Text>{currencyUnit}</InputGroup.Text>
                                  </InputGroup.Prepend>
                                  <Form.Control
                                    id={i?.payhead_id}
                                    className="update-wages-table-input-field wages-input"
                                    value={i?.amount}
                                    type="number"
                                    placeholder=""
                                    disabled={true}
                                    data-testid={`wages-history-modal-payhead-${i?.payhead_id}-amount`}
                                  />
                                  </InputGroup>
                                </td>
                              </tr>
                            );
                          })}
                      </tbody>
                    </Table>
                  </div>
                </div>
                <div className="col-lg-6">
                  <div className="update-wages-table pt-3">
                    <Table>
                      <thead>
                        <tr className="row px-3">
                          <th className="col-7">Monthly Deduction</th>
                          <th className="col-5">Amount</th>
                        </tr>
                      </thead>
                      <tbody>
                        {payHeadData
                          .filter(
                            (item) =>
                              item?.payhead?.type === 'Deduction' && !item?.is_display_input_sheet,
                          )
                          .map((i, index) => {
                            return (
                              <tr key={i.id} className="row px-3">
                                <td className="col-7">{i?.payhead?.head_name}</td>
                                <td className="col-5">
                                <InputGroup>
                                  <InputGroup.Prepend>
                                    <InputGroup.Text>{currencyUnit}</InputGroup.Text>
                                  </InputGroup.Prepend>
                                  <Form.Control
                                    id={i?.payhead_id}
                                    className="update-wages-table-input-field wages-input"
                                    value={i?.amount}
                                    type="number"
                                    placeholder=""
                                    disabled={true}
                                    data-testid={`wages-history-modal-payhead-${i?.payhead_id}-amount`}
                                  />
                                  </InputGroup>
                                </td>
                              </tr>
                            );
                          })}
                      </tbody>
                    </Table>
                  </div>
                </div>
              </Row>
            </Modal.Body>
            <Form>
              <hr className="dark_grey_line mb-0" />
              <Form.Row className="py-3">
                <div className="col-sm-7 ml-3 pl-3">
                  <Form.Row>
                    <div className="col-6 col-lg-3 form-heading">Basic Salary</div>
                    <div
                      className="col-5 col-lg-3 mx-2 px-2 form-heading text-right"
                      data-testid="wages-history-modal-basic-salary"
                    >
                      <span>{formatAmount(basicSalary,currencyUnit)}</span>
                    </div>
                  </Form.Row>
                  <Form.Row>
                    <div className="col-6 col-lg-3 form-heading">Total Salary</div>
                    <div
                      className="col-5 col-lg-3 mx-2 px-2 form-heading text-right"
                      data-testid="wages-history-modal-total-salary"
                    >
                      <span>{formatAmount(totalSalary,currencyUnit)}</span>
                    </div>
                  </Form.Row>
                </div>
                <div className="col-sm-4 ml-auto">
                  <div className="d-flex justify-content-end pr-3">
                    <Button
                      variant="secondary"
                      className="m-2"
                      onClick={handleCancelButton}
                      data-testid="wages-history-modal-cancel-btn"
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              </Form.Row>
            </Form>
          </>
        )
      )}
    </Modal>
  );
};

export default WagesHistoryModal;
