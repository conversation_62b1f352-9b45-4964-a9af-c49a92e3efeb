import { WagesDropDownProps } from '../../types/viewWagesHistory';
import _ from 'lodash';
import React from 'react';
import { Row } from 'react-bootstrap';
import Dropdown from 'react-bootstrap/Dropdown';
import '../../styleGuide';
import './scss/WagesDropDown.scss';
import { formatAmount } from '@src/util/view-utils';
import { DEFAULT_CURRENCY_UNIT } from '@src/constants/seafarer-wages';

const WagesDropDown = ({
  dropDownValues = [],
  onInputChange,
  selectedValue,
  eventTracker,
  wagesUnit
}: WagesDropDownProps) => {
  const handleSelect = (eventKey: string | null) => {
    const selectedElement = dropDownValues.find((i) => i.id === _.toNumber(eventKey));
    selectedElement && onInputChange(selectedElement);
  };
  const currencyUnit = wagesUnit ?? DEFAULT_CURRENCY_UNIT;
  return (
    <Dropdown
      className="view-wages-dropdown"
      onSelect={handleSelect}
      data-testid={`wages-history-modal-effective-date-dropdown`}
      onClick={() => {
        eventTracker?.('wagesHistoryModalDropdown', '');
      }}
    >
      <Dropdown.Toggle variant="outline-primary" id="dropdown-basic" style={{ width: '100%' }}>
        <Row>
          <div className="col-6 d-flex justify-content-start text-wrap">
            <span className="text-left">{selectedValue?.effectiveDate ?? ''}</span>
          </div>
          <div className="col-6 d-flex justify-content-end text-wrap">
            <span>{formatAmount(selectedValue?.totalSalary ?? '',currencyUnit)}</span>
          </div>
        </Row>
      </Dropdown.Toggle>

      <Dropdown.Menu>
        <Row className="mx-1 p-1 my-0 py-0">
          <div className="col-6  form-heading my-0 py-0">Effective Date</div>
          <div className="col-6 d-flex justify-content-end text-wrap form-heading my-0 py-0">
            Total Wage &#40;{currencyUnit}&#41;
          </div>
        </Row>
        <hr className="grey_line my-0" />
        {dropDownValues.map((i) => {
          return (
            <Dropdown.Item key={i?.id} eventKey={_.toString(i?.id)} data-testid={`dropdown-item-${i?.id}`}>
              <Row>
                <div className="col-6  text-wrap document-table-title font-weight-bold">
                  {i?.effectiveDate}
                </div>
                <div className="col-6 d-flex justify-content-end text-wrap">{i?.totalSalary}</div>
              </Row>
            </Dropdown.Item>
          );
        })}
      </Dropdown.Menu>
    </Dropdown>
  );
};

export default WagesDropDown;
