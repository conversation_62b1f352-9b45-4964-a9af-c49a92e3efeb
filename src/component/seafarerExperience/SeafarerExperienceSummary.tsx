/* eslint-disable react/prop-types */
import React, { useState, useRef, useEffect } from 'react';
import { Col, Row, Button, ButtonToolbar } from 'react-bootstrap';
import TableColumnsButton from '../seafarerList/TableColumnsButton';
import items from './SeafarerTableMenuList';
import SeafarerExperienceTable from './SeafarerExperienceTable';
import SeafarerRankHistoryTable from './SeafarerRankHistoryTable';
import seafarerService from '../../service/seafarer-service';
import {
  SeafarerExperienceDetails,
  SeafarerExperienceByVesselType,
  SeafarerExperienceByRank,
  SeafarerExperienceByVesselTypeAndRank,
} from '../../model/SeafarerExperienceDetails';
import ConfirmActionModalView from '../../component/AddSeafarer/ConfirmActionModalView';
import _ from 'lodash';
import AddExperience from './AddExperience';
import ErrorDisplayModal from '../common/ErrorDisplayModal';

const ButtonsBar = ({
  selectedColumns,
  roleConfig,
  onSelectColumn,
  addExperienceBtnHandler,
  eventTracker,
}) => {
  return (
    <ButtonToolbar className="float-right pt-2">
      <TableColumnsButton
        size="sm"
        items={items}
        selectedColumns={selectedColumns}
        onSelectColumn={onSelectColumn}
      />
      {roleConfig.seafarer.edit.seafarerExperience && (
        <Button
          variant="outline-primary"
          size="sm"
          onClick={() => {
            eventTracker('seafarerExperience', 'Add Vessel Experience');
            addExperienceBtnHandler();
          }}
          data-testid="fml-seafarer-experience-add-experience"
        >
          Add Experience
        </Button>
      )}
    </ButtonToolbar>
  );
};

const SeafarerExperienceSummary = ({ seafarer, roleConfig, TableSection, eventTracker }) => {
  const [selectedColumns, setSelectedColumns] = useState(items.slice(1, 9));
  const [vesselExperience, setVesselExperience] = useState([]);
  const [rankHistoryData, setRankHistoryData] = useState([]);
  const [loadingExp, setLoadingExp] = useState(false);
  const tableRef = useRef(null);
  const statusTableRef = useRef(null);
  const [showExpModal, setShowExpModal] = useState(false);
  const [expToEdit, setExpToEdit] = useState(null);
  const [isRemoveDocumentConfirmationModalShow, setIsRemoveDocumentConfirmationModalShow] =
    useState(false);
  const [isDisableConfirmDeleteBtn, setIsDisableConfirmDeleteBtn] = useState(false);
  const [deleteVesselExperience, setDeleteVesselExperience] = useState(null);
  const [isModalLoading, setIsModalLoading] = useState(false);
  const [modalMessage, setModalMessage] = useState(null);
  const [loadExperiencePage, setLoadExperiencePage] = useState(false);

  useEffect(() => {
    (async () => {
      try {
        setVesselExperience([]);
        setRankHistoryData([]);
        setLoadingExp(true);
        const { data } = await seafarerService.getSeafarerExperience(seafarer.id);
        const rankHistrory = await seafarerService.getExperienceRankHistory(seafarer.id);
        const rankHistoryData = _.orderBy(
          rankHistrory.data,
          [(rankObj) => new Date(rankObj.start_date)],
          ['desc'],
        );
        setRankHistoryData(rankHistoryData);
        const vesselExperienceData = _.orderBy(
          data,
          [(vesselObj) => new Date(vesselObj.start_date)],
          ['desc'],
        );
        setVesselExperience(vesselExperienceData);
      } catch (error) {
        console.error(
          `Get seafarer experience by Seafarer ID: ${seafarer.id} failed. Error: ${error}`,
        );
      }
      setLoadingExp(false);
    })();
  }, [seafarer.id, loadExperiencePage]);

  const onSelectColumn = (item) => {
    const newSelection = selectedColumns.slice();
    const idx = newSelection.indexOf(item);
    if (idx !== -1) {
      newSelection.splice(idx, 1);
    } else {
      newSelection.push(item);
      newSelection.sort((a, b) => a.order - b.order);
    }
    setSelectedColumns(newSelection);
  };

  const onHideModalMessage = () => setModalMessage(null);

  const addExperienceBtnHandler = () => {
    setShowExpModal(true);
  };

  const cancelButtonHandler = () => {
    setExpToEdit(null);
    setShowExpModal(false);
  };

  const seafarerExpEditHandler = async (vesselExp) => {
    eventTracker('seafarerExperience', 'Edit Vessel Experience');
    setExpToEdit(vesselExp);
    setShowExpModal(true);
  };

  const seafarerExpDeleteHandler = async (vesselExp) => {
    eventTracker('seafarerExperience', 'Delete Vessel Experience');
    setDeleteVesselExperience(vesselExp);
    setIsRemoveDocumentConfirmationModalShow(true);
  };

  const handleDeleteSeafarerVesselExperience = async () => {
    if (deleteVesselExperience) {
      const { id } = deleteVesselExperience;
      try {
        setIsDisableConfirmDeleteBtn(true);
        setIsModalLoading(true);
        await seafarerService.deleteSeafarerExp(id);
        const newVessel = vesselExperience.filter((ele) => ele.id !== id);
        setVesselExperience(newVessel);
        setIsModalLoading(false);
        setIsRemoveDocumentConfirmationModalShow(false);
        setIsDisableConfirmDeleteBtn(false);
        setLoadExperiencePage(!loadExperiencePage);
      } catch (error) {
        setIsDisableConfirmDeleteBtn(false);
        setIsModalLoading(false);
        setIsRemoveDocumentConfirmationModalShow(false);
        setModalMessage('Oops something went wrong while deleting vessel exprience document.');
        console.error(`Edit seafarer experience by ID: ${id} failed. Error: ${error}`);
      }
    }
  };

  return (
    <>
      {showExpModal && (
        <AddExperience
          seafarer_id={seafarer.id}
          expToEdit={expToEdit}
          canEditVesselDetails={roleConfig.seafarer.edit.seafarerExperienceVessel}
          cancelButtonHandler={cancelButtonHandler}
          eventTracker={eventTracker}
          setLoadExperiencePage={setLoadExperiencePage}
          loadExperiencePage={loadExperiencePage}
        />
      )}

      <Row>
        <Col>
          <TableSection
            id="experience"
            title="Experience Overview"
            data={SeafarerExperienceDetails(seafarer, vesselExperience)}
          />
        </Col>
        <Col>
          <TableSection
            id="experience_by_vessel_type"
            title="Experience Summary by Vessel Type"
            data={SeafarerExperienceByVesselType(vesselExperience)}
          />
          <TableSection
            id="experience_by_rank"
            title="Experience Summary by Rank"
            data={SeafarerExperienceByRank(vesselExperience)}
          />
        </Col>
        <Col>
          <TableSection
            id="experience_by_vessel_type_and_rank"
            title="Experience Summary by Vessel Type / Rank"
            data={SeafarerExperienceByVesselTypeAndRank(vesselExperience)}
          />
        </Col>
      </Row>
      <div className="details_page__table_head">
        <Row>
          <Col>
            <div className="font-weight-bold p-2">Rank History</div>
          </Col>
        </Row>
        <Row>
          <Col>
            <SeafarerRankHistoryTable
              rankHistoryData={rankHistoryData}
              loading={loadingExp}
              roleConfig={roleConfig}
              tableRef={statusTableRef}
            />
          </Col>
        </Row>
      </div>
      <div className="details_page__table_head">
        <Row>
          <Col>
            <div className="font-weight-bold p-2">Vessel Experience</div>
          </Col>
          <Col>
            <ButtonsBar
              selectedColumns={selectedColumns}
              roleConfig={roleConfig}
              onSelectColumn={onSelectColumn}
              addExperienceBtnHandler={addExperienceBtnHandler}
              eventTracker={eventTracker}
            />
          </Col>
        </Row>
      </div>

      <Row>
        <Col>
          <SeafarerExperienceTable
            vesselExperience={vesselExperience}
            selectedColumns={selectedColumns}
            loading={loadingExp}
            roleConfig={roleConfig}
            tableRef={tableRef}
            eventTracker={eventTracker}
            seafarerExpEditHandler={(row) => {
              seafarerExpEditHandler(row);
            }}
            seafarerExpDeleteHandler={(row) => {
              seafarerExpDeleteHandler(row);
            }}
            data-testid="experience-table"
          />
        </Col>
      </Row>
      <ConfirmActionModalView
        show={isRemoveDocumentConfirmationModalShow}
        onClose={() => setIsRemoveDocumentConfirmationModalShow(false)}
        onConfirm={handleDeleteSeafarerVesselExperience}
        title={'Confirm Deleting the document?'}
        message={'Are you sure deleting the document?'}
        isDisableConfirmDeleteBtn={isDisableConfirmDeleteBtn}
        isModalLoading={isModalLoading}
      />
      <ErrorDisplayModal onHideModalMessage={onHideModalMessage} modalMessage={modalMessage} />
    </>
  );
};

export default SeafarerExperienceSummary;
