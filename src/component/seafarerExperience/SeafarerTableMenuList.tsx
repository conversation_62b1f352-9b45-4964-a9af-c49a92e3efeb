/* eslint-disable react/display-name */
/* eslint-disable react/prop-types */
import React from 'react';
import moment from 'moment';
import { Button } from 'react-bootstrap';
import { useHistory } from 'react-router-dom';
import { getDurationStrByDateRanges } from '../../model/utils';
import fmlLogo from '../../../public/icons/fml-vessel.svg';
import seafarerService from '../../service/seafarer-service';
import { formatAmount } from '../../util/view-utils';

const ShowVesselName = ({ row }) => {
  const history = useHistory();
  const visitVesselDetailPage = async (vesselRefId) => {
    if (vesselRefId) {
      const result = await seafarerService.getVesselOwnershipId(vesselRefId);
      history.push(`/vessel/ownership/details/${result.data.ownership_id}`);
    }
  };
  return (
    <>
      <img src={fmlLogo} />
      <Button variant="link" onClick={() => visitVesselDetailPage(row.vessel_ref_id)}>
        {row.vessel_name}
      </Button>
    </>
  );
};
export const ShowSalary = ({ row, eventTracker }) => {
  const history = useHistory();
  const viewWagesHistoryModal = (row) => {
    eventTracker?.('experienceTableSalaryLink', '');
    history.push(
      `experience/${row?.id}/wages-history?seafarer_status_history_id=${row?.status_history_id}`,
    );
  };

  return (
    <Button variant="link" onClick={() => viewWagesHistoryModal(row)}>
      <span>{formatAmount(row?.salary,row?.salary_unit)}</span>
    </Button>
  );
};

const items = [
  {
    type: 'header',
    Header: 'BASIC',
  },
  {
    type: 'item',
    Header: 'Vessel Name',
    id: 'vessel_name',
    accessor: (row) => (row.vessel_ref_id ? <ShowVesselName row={row} /> : row.vessel_name),
    width: 250,
    order: 0,
    sticky: 'left',
  },
  {
    type: 'item',
    Header: 'Type',
    id: 'vessel_type',
    accessor: (row) => row.vessel_type,
    width: 250,
    order: 1,
  },
  {
    type: 'item',
    Header: 'Rank',
    id: 'rank',
    accessor: (row) => row.rank?.value,
    width: 100,
    order: 2,
  },
  {
    type: 'item',
    Header: 'Start Date',
    id: 'start_date',
    accessor: (row) => (row.start_date ? moment(row.start_date).format('DD MMM YYYY') : '---'),
    sortType: (a, b) => {
      if (a.start_date && b.start_date) {
        const aDate = a.start_date ? moment(a.start_date).valueOf() : '';
        const bDate = b.start_date ? moment(b.start_date).valueOf() : '';
        if (aDate < bDate) return -1;
        if (aDate > bDate) return 1;
      }
      return 0;
    },
    width: 150,
    order: 3,
  },
  {
    type: 'item',
    Header: 'End Date',
    id: 'end_date',
    accessor: (row) => (row.end_date ? moment(row.end_date).format('DD MMM YYYY') : '---'),
    sortType: (a, b) => {
      if (a.end_date && b.end_date) {
        const aDate = a.end_date ? moment(a.end_date).valueOf() : '';
        const bDate = b.end_date ? moment(b.end_date).valueOf() : '';
        if (aDate < bDate) return -1;
        if (aDate > bDate) return 1;
      }
      return 0;
    },
    width: 150,
    order: 4,
  },
  {
    type: 'item',
    Header: 'Period',
    id: 'period',
    accessor: (row) =>
      getDurationStrByDateRanges([
        {
          fromDateISOString: moment(row.start_date).format('YYYY-MM-DD'),
          toDateISOString: moment(row.end_date).format('YYYY-MM-DD'),
        },
      ]),
    sortType: (a, b) => {
      if (
        a.original.start_date &&
        a.original.end_date &&
        b.original.start_date &&
        b.original.end_date
      ) {
        const a_duration =
          moment(a.original.end_date).valueOf() - moment(a.original.start_date).valueOf();
        const b_duration =
          moment(b.original.end_date).valueOf() - moment(b.original.start_date).valueOf();
        return a_duration - b_duration;
      }
      return 0;
    },
    width: 250,
    order: 5,
  },
  {
    type: 'item',
    Header: 'DWT/GRT',
    id: 'dwt_grt',
    accessor: (row) => {
      let res = `${row.deadweight_tonnage || ''}`;
      if (row.deadweight_gross_registered_tonnage) {
        res += `{${row.deadweight_gross_registered_tonnage}}`;
      }
      return res;
    },
    width: 250,
    order: 6,
  },
  {
    type: 'item',
    Header: 'Sign Off Reason',
    id: 'signoff_reason',
    accessor: (row) => (row.signoff_reason ? row.signoff_reason.value : 'N/A'),
    width: 250,
    order: 7,
    disableSortBy: true,
  },
  {
    type: 'item',
    Header: 'Engine',
    id: 'engine',
    accessor: (row) =>
      row.engine_type ? `${row.engine_type || ''} ${row.engine_sub_type || ''}` : 'N/A',
    width: 250,
    order: 8,
  },
  {
    type: 'item',
    Header: 'Salary',
    id: 'salary',
    accessor: (row) => <ShowSalary row={row} />,
    width: 250,
    order: 9,
  },
  {
    type: 'item',
    Header: 'BHP',
    id: 'bhp',
    accessor: (row) => row.brake_horse_power,
    width: 250,
    order: 10,
  },
  {
    type: 'item',
    Header: 'Owner',
    id: 'owner',
    accessor: (row) => row.owner_name,
    width: 250,
    order: 11,
  },
  {
    type: 'item',
    Header: 'Last Edited By',
    id: 'last_edited_by',
    accessor: (row) => row?.last_updated_by_hash ?? 'N/A',
    width: 250,
    order: 12,
  },
  {
    type: 'item',
    Header: 'Last Edited Date',
    id: 'last_edited_date',
    accessor: (row) => (row.updated_at ? moment(row.updated_at).format('DD MMM YYYY') : '---'),
    width: 250,
    order: 13,
  },
];

export default items;
