import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Modal } from 'react-bootstrap';

interface PlanningActionModalProps {
  show: boolean;
  handleClosePlanningAction: Function;
  handleConfirmPlanningAction: Function;
  isLoading: boolean;
  actionType: string;
}

const PlanningActionModal = ({
  show,
  handleClosePlanningAction,
  handleConfirmPlanningAction,
  isLoading,
  actionType,
}: PlanningActionModalProps) => {
  const planningPopupContent: { [key: string]: { header: string; body: JSX.Element } } = {
    rejected: {
      header: 'Reject Planning',
      body: (
        <div>
          <b>WARNING:</b> The seafarer will go back to unplanned
          <br />
          <b>ACTION:</b> Please also reject the Recommendation
        </div>
      ),
    },
    unclubbed: {
      header: 'Unclub planned Seafarers',
      body: 'The seafarers will be moved back to Unclubbed Section',
    },
    relieve_without_replacement: {
      header: 'Relieving without Replacement',
      body: (
        <div>
          Seafarer Onboard will be Relieved without a Replacement. The Rank will be{' '}
          <b>appearing as a Missing Rank</b> on this Vessel
        </div>
      ),
    },
    cancel_relieve_without_replacement: {
      header: 'Cancelling Relieve without Replacement',
      body: 'The Seafarer will be moved to Unplanned status',
    },
    cancel_additional_crew: {
      header: 'Deleting Additional Crew Position',
      body: (
        <div>
          <b>WARNING:</b> The Additional Crew Requirement will be deleted permanently.
        </div>
      ),
    },
  };
  return (
    <Modal
      size="lg"
      show={show}
      onHide={() => {
        handleClosePlanningAction();
      }}
    >
      <Modal.Header className="modal-header">
        {planningPopupContent[actionType].header}
      </Modal.Header>
      <Modal.Body>
        <Alert variant="danger">
          <div>{planningPopupContent[actionType].body}</div>
        </Alert>
      </Modal.Body>
      <Modal.Footer>
        <Button
          variant="primary"
          onClick={() => {
            handleClosePlanningAction();
          }}
        >
          Cancel
        </Button>
        <Button
          variant="secondary"
          onClick={() => {
            handleConfirmPlanningAction();
          }}
          disabled={isLoading}
        >
          Confirm
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default PlanningActionModal;
