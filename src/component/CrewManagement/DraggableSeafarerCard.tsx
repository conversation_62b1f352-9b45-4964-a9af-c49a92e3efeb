import React, { useState } from 'react';
import { useDrag } from 'react-dnd';
import { Itinerary } from '@src/types/itinerary';
import SeafarerCard from '../CrewPlanner/SeafarerCard';
import { CREW_PLANNING_STATUS } from '../../constants/crewPlanner';
import { seafarerStatus } from '../../model/constants';
import '../scss/clubbing-timeline.scss';
import CrewPlannerDetailDrawer from './CrewPlannerDetail';
import { checkAdditionalRequest, checkMissingPersonnel } from '@src/pages/CrewPlanner/utils';

interface DraggableSeafarerCardProps {
  crew: {
    id: number;
    crew_planning?: {
      reliever?: any;
    };
  };
  relieverStatus: any;
  itinerary: Itinerary;
  isFromTimeline?: boolean | null;
  onConfirmClub: Function;
  eventTracker: Function;
  hasEditAccess: boolean;
}

const DraggableSeafarerCard = ({
  crew,
  itinerary,
  relieverStatus,
  isFromTimeline,
  onConfirmClub,
  eventTracker,
  hasEditAccess
}: DraggableSeafarerCardProps) => {
  const reliever = crew?.crew_planning?.reliever;
  const isRelieverTravelling =
    reliever?.seafarer_person?.current_journey_status === seafarerStatus.TRAVELLING;
  const isRelieverSignedOn =
    reliever?.seafarer_person?.current_journey_status === seafarerStatus.SIGNED_ON;
  const isCrewMissingPersonnel = checkMissingPersonnel(crew);
  const isAdditionalCrewRequest = checkAdditionalRequest(crew);
  const shouldDragCard = () => {
    if (!hasEditAccess) return false;
    if (isCrewMissingPersonnel && !reliever) return false;
    if (isAdditionalCrewRequest && !reliever) return false;
    return !(
      isRelieverTravelling ||
      isRelieverSignedOn ||
      crew?.crew_planning?.planning_status === CREW_PLANNING_STATUS.club_confirmed ||
      crew?.crew_planning?.planning_status === CREW_PLANNING_STATUS.completed ||
      crew?.crew_planning?.planning_status === CREW_PLANNING_STATUS.expired
    );
  };
  const [{ isDragging }, drag] = useDrag({
    item: { type: 'seafarerCard', id: crew?.id, crew },
    end: (item, monitor) => {
      if (monitor.didDrop() && item) {
        // No event is needed here
      }
    },
    collect: (monitor) => ({
      isDragging: !!monitor.isDragging(),
    }),
    canDrag: shouldDragCard(), // disable drag
  });
  const [showDetails, setShowDetails] = useState(false);
  const handleShowDetails = () => {
    eventTracker('viewDetails');
    setShowDetails(true);
  };
  return (
    <div ref={drag} style={{ opacity: isDragging ? 0.5 : 1 }}>
      <SeafarerCard
        toBeReplacedSeafarer={crew}
        reliever={reliever}
        packedStyle
        isEditable
        visitSeafarerDetail={handleShowDetails}
        relieverStatus={relieverStatus}
        isRelieverTravelling={isRelieverTravelling}
        isRelieverSignedOn={isRelieverSignedOn}
        isFromTimeline={isFromTimeline}
        onConfirmClub={onConfirmClub}
        eventTracker={eventTracker}
        hasEditAccess={hasEditAccess}
      />
      {showDetails && (
        <CrewPlannerDetailDrawer
          visible={showDetails}
          reliever={reliever}
          itinerary={itinerary}
          toBeReplacedSeafarer={crew}
          relieverStatus={relieverStatus}
          isRelieverTravelling={isRelieverTravelling}
          isRelieverSignedOn={isRelieverSignedOn}
          onClose={() => setShowDetails(false)}
          eventTracker={eventTracker}
        />
      )}
    </div>
  );
};

export default DraggableSeafarerCard;
