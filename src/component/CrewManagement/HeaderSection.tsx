import React from 'react';
import { formatPort } from '../ManagedVessels/TableHeader';

const noValue = '---';

const CrewManagementHeaderSection = ({ vesselData, itinerary }) => {
  return (
    <div className="crew-management-header-section"   >
      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
        <div>
          <div className="header">Vessel Category</div>
          <div>{vesselData?.vessel_type?.value ?? noValue}</div>
        </div>
        <div>
          <div className="header">Flag Office</div>
          <div>{vesselData?.flags?.[0]?.office?.value ?? noValue}</div>
        </div>
        <div>
          <div className="header">Vessel Owner</div>
          <div>{vesselData?.owner?.value ?? noValue}</div>
        </div>
        <div>
          <div className="header">Tech Group</div>
          <div>{vesselData?.fleet_staff?.tech_group ?? noValue}</div>
        </div>
        <div>
          <div className="header">Group Head</div>
          <div>{vesselData?.fleet_staff?.tech_group_group_head?.full_name ?? noValue}</div>
        </div>
        <div>
          <div className="header">Estimated Port</div>
          <div>{formatPort(itinerary?.port, itinerary?.country)}</div>
        </div>
      </div>
    </div>
  );
};

export default CrewManagementHeaderSection;
