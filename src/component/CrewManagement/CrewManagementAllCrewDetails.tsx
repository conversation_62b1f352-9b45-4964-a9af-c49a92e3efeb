import React, { useState, useEffect } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { ArrowClockwise } from 'react-bootstrap-icons';
import { useParams } from 'react-router-dom';
import { Button } from 'react-bootstrap';
import { useCrewMangementContext } from '../../pages/CrewManagementPage';
import seafarerService from '../../service/seafarer-service';
import DraggableSeafarerCard from './DraggableSeafarerCard';
import { CREW_PLANNING_LEFT_SIDE } from '../../constants/crewPlanner';
import Spinner from '../common/Spinner';
import AddAdditionalCrewModal from './AddAdditionalCrewModal';
import { addAdditionalCrew } from '../../service/crew-planner';

interface CrewManagementAllCrewDetailsProps {
  relieverStatus: any[] | null;
  eventTracker: Function;
  hasEditAccess: boolean;
}

export const renderLoader = () => {
  return (
    <div className="crew-management-crew-details-loader-wrapper">
      <Spinner alignClass="spinner-grid" data-testid="loader" />
    </div>
  );
};

const CrewManagementAllCrewDetails = ({
  relieverStatus,
  eventTracker,
  hasEditAccess
}: CrewManagementAllCrewDetailsProps) => {
  const params = useParams();
  const { vesselOwnershipId } = params;
  const { crewList, isCrewListLoading, refresh, setAlertMessage, setCrewListData } = useCrewMangementContext();

  const [isOpenAdditionalCrewModal, setIsOpenAdditionalCrewModal] = useState(false);
  const [isLoadingAddCrew, setIsLoadingAddCrew] = useState(false);
  const [rankList, setRankList] = useState([]);

  useEffect(() => {
    const getRankList = async () => {
      try {
        const { ranks } = await seafarerService.getSeafarerDropDownData('?values=ranks');
        setRankList(ranks);
      } catch (error) {
        console.error('Error while fetching ranks data', error);
      }
    };
    getRankList();
  }, []);

  const handleAddAdditionalCrew = async (rank) => {
    setIsLoadingAddCrew(true);
    try {
      const response = await addAdditionalCrew({
        rank_id: rank?.id,
        ownership_id: parseInt(vesselOwnershipId, 10),
      });
      const updatedCreatedRequest = {
        ...response.data,
        rank,
        crew_planning: null,
        isAdditionalRequest: true,
      };
      setCrewListData([updatedCreatedRequest, ...crewList]);
      setIsLoadingAddCrew(false);
      setIsOpenAdditionalCrewModal(false);
      setAlertMessage({
        message: 'Additional Crew requirement added successfully!',
        variant: 'success',
      });
      eventTracker('addAdditionalCrew');
    } catch {
      setIsLoadingAddCrew(false);
      setIsOpenAdditionalCrewModal(false);
      setAlertMessage({
        message: 'Failed to add additional crew.',
        variant: 'danger',
      });
    }
  };

  return (
    <div className="crew-management-crew-details-section">
      <div className="header d-flex align-items-center justify-content-between border-bottom crew-management-crew-details-title">
        <div className="title text-primary font-weight-bold">All Crew Details</div>
        <div className="d-flex">
          {!isCrewListLoading && (
            <button
              type="button"
              className="refresh-button d-flex align-items-center bg-transparent border-0 cursor-pointer mr-2"
              onClick={() => {
                refresh();
                eventTracker('refreshDetails');
              }}
            >
              <ArrowClockwise size={14} />
              <span className="ml-2 text-primary">
                <u>Refresh</u>
              </span>
            </button>
          )}
          {hasEditAccess && <Button variant="primary" size="sm" onClick={() => setIsOpenAdditionalCrewModal(true)}>
            Additional Crew
          </Button>}
        </div>
      </div>
      <div className="crew-management-crew-details-list overflow-auto p-2">
        {isCrewListLoading ? (
          renderLoader()
        ) : (
          <DndProvider backend={HTML5Backend}>
            {crewList
              ?.filter(
                (crew) =>
                  crew?.crew_planning === null ||
                  CREW_PLANNING_LEFT_SIDE.includes(crew?.crew_planning?.planning_status),
              )
              .map((crew) => {
                const thisRelieverInfo =
                  relieverStatus?.find((i) => i.id === crew?.crew_planning?.reliever_id) ?? null;
                return (
                  <DraggableSeafarerCard
                    key={crew.id}
                    crew={crew}
                    relieverStatus={thisRelieverInfo}
                    eventTracker={eventTracker}
                    hasEditAccess={hasEditAccess}
                  />
                );
              })}
          </DndProvider>
        )}
      </div>
      {isOpenAdditionalCrewModal && (
        <AddAdditionalCrewModal
          show={isOpenAdditionalCrewModal}
          handleCloseModal={() => setIsOpenAdditionalCrewModal(false)}
          handleAddCrew={handleAddAdditionalCrew}
          isLoading={isLoadingAddCrew}
          rankList={rankList}
        />
      )}
    </div>
  );
};

export default CrewManagementAllCrewDetails;
