import React, { useState, useEffect } from 'react';
import { Col, Button, Modal, Form } from 'react-bootstrap';
import { Itinerary } from '@src/types/itinerary';
import { getPortById, getCountryByCode } from '../../../service/reference-service';
import AutoDismissibleAlert from '../../common/AutoDismissibleAlert';
import { patchSimulateItinerary } from '@src/service/crew-planner';
import SubmitButton from '@src/component/common/SubmitButton';
import { useCrewMangementContext } from '@src/pages/CrewManagementPage';
import { ExclamationTriangle } from 'react-bootstrap-icons';

interface DeleteSimulatedPortModal {
  itinerary: Itinerary;
  onClose: Function;
  show: boolean;
  eventTracker: Function;
}
// eslint-disable-next-line react/prop-types
const DeleteSimulatedPortModal = ({ itinerary, onClose, show, eventTracker }: DeleteSimulatedPortModal) => {
  const itineraryId = itinerary?.id;
  const canDelete = itinerary?.crewPlanning?.length === 0;

  const headerText = `Delete Crew Change Destination`;
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [country, setCountry] = useState<string | undefined>(undefined);
  const { refetchSimulateItinerary, setAlertMessage } = useCrewMangementContext();

  const handleClose = () => {
    onClose?.();
  };

  useEffect(() => {
    (async () => {
      if (canDelete && itinerary?.port_id) {
        const port = await getPortById(itinerary.port_id);
        const countryRes = await getCountryByCode(port.country_code);
        setCountry(countryRes.country.value);
      }
    })();
  }, [itinerary, canDelete]);

  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);
      eventTracker('deletePlaceHolder', '');
      // mark as inactive
      await patchSimulateItinerary({
        id: itineraryId,
        is_active: false,
      });
      await refetchSimulateItinerary();
      setAlertMessage({
        className: 'alert-simulate-itinerary-success',
        message: `Deleted Port and Seafarers Unclubbed successfully`,
        variant: 'success',
      });
      handleClose();
    } catch (e) {
      setError('Something went wrong, please try again later');
      console.error(e);
    } finally {
      setIsSubmitting(false);
    }
  };
  const renderWarningModal = () => {
    return (
      <Modal className="modal-crew" show={show}>
        <Modal.Header className="modal-header modal-header-crew justify-content-start align-items-center">
          {!canDelete && (
            <ExclamationTriangle
              className="text-warning mr-2"
              width={36}
              style={{ color: '#FFC107', cursor: 'pointer' }}
              size={36}
            />
          )}
          {headerText}
        </Modal.Header>
        <Modal.Body>
          <p>
            You cannot remove the itinerary because a plan is clubbed, please move the plan to
            another itinerary
          </p>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="primary" onClick={handleClose}>
            Ok
          </Button>
        </Modal.Footer>
      </Modal>
    );
  };
  if (!canDelete) {
    return renderWarningModal();
  }
  return (
    <Modal className="modal-crew" size="lg" show={show}>
      <Modal.Header className="modal-header modal-header-crew">{headerText}</Modal.Header>
      <Modal.Body>
        {error && <AutoDismissibleAlert message={error} variant="danger" />}
        <Form>
          <Form.Row>
            <Form.Group as={Col} md="6">
              <Form.Label className="form-heading">Country</Form.Label>
              <Form.Control disabled placeholder={country} />
            </Form.Group>
            <Form.Group as={Col} md="6">
              <Form.Label className="form-heading">Port</Form.Label>
              <Form.Control disabled placeholder={itinerary?.port} />
            </Form.Group>
            <Form.Group as={Col} md="6">
              <Form.Label className="form-heading">Estimated Date of Arrival</Form.Label>
              <Form.Control disabled placeholder={itinerary?.estimated_arrival_formatted} />
            </Form.Group>
          </Form.Row>
        </Form>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="primary" onClick={handleClose} disabled={isSubmitting}>
          Cancel
        </Button>
        <SubmitButton variant="secondary" onClick={handleSubmit} isLoading={isSubmitting}>
          Delete
        </SubmitButton>
      </Modal.Footer>
    </Modal>
  );
};

export default DeleteSimulatedPortModal;
