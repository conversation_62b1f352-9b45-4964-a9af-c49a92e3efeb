import React, { useState, useEffect } from 'react';
import { FormikConfig, useFormik } from 'formik';
import { Col, Button, Modal, Form, Alert } from 'react-bootstrap';
import { orderBy } from 'lodash';
import { Itinerary } from '@src/types/itinerary';
import { addSimulateItinerary, patchSimulateItinerary } from '@src/service/crew-planner';
import moment from 'moment-timezone';
import SubmitButton from '@src/component/common/SubmitButton';
import { useCrewMangementContext } from '@src/pages/CrewManagementPage';
import FleetDatePicker from '../../AddSeafarer/FleetDatePicker';
import DropDownSearchControl from '../../AddSeafarer/DropDownSearchControl';
import * as schema from '../../../model/SeafarerSchemaValidation';
import { getCountries, getPortById, getPorts } from '../../../service/reference-service';
import AutoDismissibleAlert from '../../common/AutoDismissibleAlert';

const defaultDropDownValue = {
  countries: [],
  ports: [],
};
interface FormValues {
  country_name?: string;
  port_id?: number;
  date_of_arrival?: string;
  can_be_changed?: boolean;
}

interface PortFormProps {
  isEditing?: boolean;
  startDate?: string;
  endDate?: string;
  formik: FormikConfig<FormValues>;
  getPortList: (ports: any[]) => void;
  onError?: (e: string) => void;
}

const PortForm = (props: PortFormProps) => {
  const { formik, startDate, endDate, isEditing, onError } = props;
  const { values, errors, setFieldValue } = formik;

  const today = new Date();
  const [dropDownData, setDropDownData] = useState(defaultDropDownValue);

  const formatCountryDropDownValue = (countries) => {
    const modifiedData = countries.map((country) => ({
      id: country.alpha2_code,
      value: country.value,
    }));
    const sortedData = orderBy(modifiedData, ['value'], ['asc']);
    return { countries: sortedData };
  };

  useEffect(() => {
    const loadDropDownData = async () => {
      try {
        const dropDownDataResponse = await getCountries();
        const updatedDropDownData = {
          ...formatCountryDropDownValue(dropDownDataResponse.countries),
          ports: defaultDropDownValue.ports,
        };
        setDropDownData(updatedDropDownData);
      } catch (error) {
        onError?.('Something went wrong, please try again later');
      }
    };

    loadDropDownData();
  }, []);

  useEffect(() => {
    const loadPorts = async () => {
      if (values.country_name) {
        try {
          const portsResponse = await getPorts(values.country_name);
          const modifiedData = portsResponse.ports.map((port) => ({
            id: port.id,
            value: port.name,
          }));
          const sortedData = orderBy(modifiedData, ['value'], ['asc']);
          props.getPortList(sortedData);
          setDropDownData((prevData) => ({
            ...prevData,
            ports: sortedData,
          }));
        } catch (error) {
          onError?.('Something went wrong, please try again later');
        }
      }
    };
    loadPorts();
  }, [values.country_name]);

  useEffect(() => {
    (async () => {
      // load the  country name if editing
      if (isEditing && values?.port_id) {
        const port = await getPortById(values.port_id);
        port && setFieldValue('country_name', port.country_code);
      }
    })();
  }, [isEditing]);

  const onInputChange = (event) => {
    const targetName = event?.target?.name;
    const targetValue = event?.target?.value;
    setFieldValue(targetName, targetValue);
  };
  const genOnDateChange = (fieldName: string) => (value: any) => setFieldValue(fieldName, value);
  return (
    <Form>
      <Form.Row>
        <Form.Group as={Col} md="6">
          <Form.Label className="form-heading">Country*</Form.Label>
          <DropDownSearchControl
            name="country_name"
            selectedValue={dropDownData.countries.length ? values.country_name : undefined}
            dropDownValues={dropDownData.countries}
            onInputChange={onInputChange}
            isInvalid={!!errors.country_name}
            testID="country_name"
          />
          <Form.Control.Feedback
            type="invalid"
            className={errors.country_name ? 'set-display-block' : ''}
          >
            {errors.country_name}
          </Form.Control.Feedback>
        </Form.Group>
        <Form.Group as={Col} md="6">
          <Form.Label className="form-heading">Port*</Form.Label>
          <DropDownSearchControl
            name="port_id"
            selectedValue={dropDownData.ports.length ? values.port_id : undefined}
            dropDownValues={dropDownData.ports}
            onInputChange={onInputChange}
            isInvalid={!!errors.port_id}
            testID="port_id"
          />
          <Form.Control.Feedback
            type="invalid"
            className={errors.port_id ? 'set-display-block' : ''}
          >
            {errors.port_id}
          </Form.Control.Feedback>
        </Form.Group>
        <Form.Group as={Col} md="6">
          <Form.Label className="form-heading">Estimated Date of Arrival*</Form.Label>
          <FleetDatePicker
            name="date_of_arrival"
            onChange={genOnDateChange('date_of_arrival')}
            value={values.date_of_arrival}
            isInvalid={!!errors.date_of_arrival}
            minDate={startDate && new Date(startDate) > today ? new Date(startDate) : today}
            maxDate={
              endDate && endDate !== 'N/A' ? new Date(endDate) : new Date(new Date().setMonth(today.getMonth() + 2))
            }
          />
          <Form.Control.Feedback
            type="invalid"
            className={errors.date_of_arrival ? 'set-display-block' : ''} // force the Feedback to be appear based on errors.date_of_expiry
          >
            {errors.date_of_arrival}
          </Form.Control.Feedback>
        </Form.Group>
        {isEditing && (
          <Form.Group as={Col} md="12">
            <Form.Check
              type="checkbox"
              className="custom-checkbox"
              label="The port is a placeholder and can be changed"
              checked={values?.can_be_changed}
              value={values?.can_be_changed}
              onChange={onInputChange}
              name="can_be_changed"
              id="can_be_changed"
            />
            <Form.Control.Feedback
              type="invalid"
              className={errors.deadline ? 'set-display-block' : ''} // force the Feedback to be appear based on errors.date_of_expiry
            >
              {errors.deadline}
            </Form.Control.Feedback>
          </Form.Group>
        )}
      </Form.Row>
    </Form>
  );
};
interface SimulatedPortModal {
  itinerary?: Itinerary;
  onClose?: Function;
  show: boolean;
  isEditing?: boolean;
  crewChangeDestinationData?: { startDate?: string | null; endDate?: string | null };
  eventTracker?: Function;
}

const SimulatedPortModal = ({
  itinerary,
  onClose,
  crewChangeDestinationData,
  show,
  isEditing,
  eventTracker,
}: SimulatedPortModal) => {
  const itineraryId = itinerary?.id;
  const headerText = `${isEditing ? 'Edit' : 'Add a'} Crew Change Destination`;
  const submitText = isEditing ? 'Save' : 'Add Port';
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [portList, setPortList] = useState([] as any[]);
  const { vesselData } = useCrewMangementContext();
  const vesselId = vesselData?.vessel?.id;
  const vesselOwnershipId = vesselData?.id;
  const { refetchSimulateItinerary } = useCrewMangementContext();
  const handleClose = () => {
    onClose?.();
  };
  const handleSubmit = async (values: FormValues) => {
    try {
      setIsSubmitting(true);
      if (!isEditing) {
        const selectedPort = portList.find((port) => port.id === values.port_id);
        eventTracker?.('addPort', selectedPort?.value);
        await addSimulateItinerary({
          ownership_id: vesselOwnershipId,
          vessel_id: vesselId,
          port_id: values.port_id as number,
          date: moment(values.date_of_arrival).format('YYYY-MM-DD'),
          is_active: true,
        });
      } else {
        // update itinerary
        await patchSimulateItinerary({
          id: itineraryId as number,
          port_id: values.port_id,
          date: moment(values.date_of_arrival).format('YYYY-MM-DD'),
          is_active: true,
        });
      }
      await refetchSimulateItinerary();
      handleClose();
    } catch (e) {
      setError('Something went wrong, please try again later');
      console.error(e);
    } finally {
      setIsSubmitting(false);
    }
  };

  const getPortList = (ports) => {
    setPortList(ports);
  };

  const formik = useFormik<FormValues>({
    initialValues: isEditing
      ? {
          port_id: itinerary.port_id,
          date_of_arrival: itinerary?.estimated_arrival,
          can_be_changed: true,
        }
      : ({} as FormValues),
    validationSchema: schema.addSimulatedPortSchema,
    onSubmit: (values: FormValues) => {
      // handle submit
      handleSubmit(values);
    },
  });
  const isSubmitValid = formik.isValid && formik.dirty && !isSubmitting;
  return (
    <Modal className="modal-crew" size="lg" show={show}>
      <Modal.Header className="modal-header modal-header-crew">{headerText}</Modal.Header>
      <Modal.Body>
        {error && <AutoDismissibleAlert message={error} variant="danger" />}
        <Alert variant="secondary">For better planning: add ports for crew changes.</Alert>
        <PortForm
          formik={formik}
          {...crewChangeDestinationData}
          isEditing={isEditing}
          onError={setError}
          getPortList={getPortList}
        />
      </Modal.Body>
      <Modal.Footer>
        <Button variant="primary" onClick={handleClose} disabled={isSubmitting}>
          Cancel
        </Button>
        <SubmitButton
          disabled={!isSubmitValid}
          variant="secondary"
          onClick={() => formik.handleSubmit()}
          isLoading={isSubmitting}
        >
          {submitText}
        </SubmitButton>
      </Modal.Footer>
    </Modal>
  );
};

export default SimulatedPortModal;
