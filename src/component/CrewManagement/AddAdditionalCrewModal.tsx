import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Button } from 'react-bootstrap';
import DropDownSearchControl from '../AddSeafarer/DropDownSearchControl';

interface RankListProps {
  id: number;
  unit: string;
  value: string;
}
interface AddAdditionalCrewProps {
  show: boolean;
  handleCloseModal: Function;
  handleAddCrew: Function;
  isLoading: boolean;
  rankList: RankListProps[];
}

const AddAdditionalCrewModal = ({
  show,
  handleCloseModal,
  handleAddCrew,
  isLoading,
  rankList,
}: AddAdditionalCrewProps) => {
  const [selectedRank, setSelectedRank] = useState<RankListProps | null>(null);

  return (
    <Modal
      size="lg"
      show={show}
      onHide={() => {
        handleCloseModal();
      }}
    >
      <Modal.Header className="modal-header">Add Additional Crew</Modal.Header>
      <Modal.Body>
        <Alert variant="info">
          <div>This is an additional crew added to the Vessel for a selected time period</div>
        </Alert>
        <p>
          <b>Select Crew Rank to be added</b>
        </p>
        <DropDownSearchControl
          name="rank"
          selectedValue={selectedRank?.id}
          dropDownValues={rankList}
          onInputChange={(e) => {
            setSelectedRank(rankList.find((rank) => rank.id === e.target.value) ?? null);
          }}
          placeholder="Please select"
          isInvalid={false}
        />
      </Modal.Body>
      <Modal.Footer>
        <Button variant="primary" onClick={() => handleCloseModal()}>
          Cancel
        </Button>
        <Button
          variant="secondary"
          onClick={() => handleAddCrew(selectedRank)}
          disabled={isLoading || !selectedRank}
        >
          Add Crew
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default AddAdditionalCrewModal;
