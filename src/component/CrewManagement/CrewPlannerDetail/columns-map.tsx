import React from 'react';
import { get } from 'lodash';
import classNames from 'classnames';

import { Dash, dateAsString } from '@src/model/utils';
import { Button } from 'react-bootstrap';
import moment from 'moment-timezone';
import { type DetailMeta } from '.';
import ShowYearsMonthsFromDays, { ShowYearsMonthsFromYears } from '../../CrewPlanner/ShowYears';
import { getSeamanBook } from '@src/component/CrewPlanner/CrewRecommendModalSeafarerDetails';
import { getAvailbilityDate } from '../../CrewPlanner/utils';

const renderValidity = (date: string) => {
  if (!date) {
    return Dash;
  }
  const dateStr = dateAsString(date);
  const isExpired = moment(date).isBefore(moment());
  return (
    <div className="status-indicator">
      <span
        className={classNames('dot ml-0', {
          'dot-green': !isExpired,
          'dot-red': isExpired,
        })}
      />
      <span>
        Till {dateStr}
      </span>
    </div>
  );
};

export const displayExp = (ocimf: string, expYears: string, exp: string) => {
  if (ocimf) {
    return <ShowYearsMonthsFromYears value={parseFloat(ocimf)} />;
  }
  if (expYears) {
    return <ShowYearsMonthsFromYears value={parseFloat(expYears)} />;
  }
  return <ShowYearsMonthsFromDays value={parseInt(exp, 10)} />;
};

export const getDetailColumns = (
  crew: any,
  isSignedOnCrew?: boolean,
  vesselType?: string,
  currentRank?: string,
): DetailMeta[] => {
  const previouslVeselOwnershipId = get(crew, 'seafarer_experience[0].vessel_ownership_id');
  const rankOcimf = crew?.ocimf?.find((o) => o?.group_by_name === 'rank' && o?.group_value === currentRank)?.years;
  const typeOcimf = crew?.ocimf?.find(
    (o) => o?.group_by_name === 'vessel_type' && o?.group_value === vesselType,
  )?.years;
  const companyOcimf = crew?.ocimf?.find((o) => o?.group_by_name === 'company')?.years;

  const columns = [
    {
      title: 'Rank Experience',
      order: 1,
      value: displayExp(
        rankOcimf,
        crew?.experience_summary?.duration_in_target_rank_years,
        crew?.experience_summary?.duration_in_target_rank,
      ),
    },
    {
      title: 'Exp. in Vessel Category',
      order: 2,
      value: displayExp(
        typeOcimf,
        crew?.experience_summary?.duration_on_target_vessel_type_years,
        crew?.experience_summary?.duration_on_target_vessel_type,
      ),
    },
    {
      title: 'Years in Company',
      order: 3,
      value: displayExp(
        companyOcimf,
        crew?.experience_summary?.duration_with_company_years,
        crew?.experience_summary?.duration_with_company,
      ),
    },
    // {
    //   title: 'Training Requirement',
    //   order: 6,
    //   value: 'Comming soon',
    // },
    {
      title: 'Nationality',
      order: 7,
      value: get(crew, 'seafarer_person.nationality.value', Dash),
    },
    {
      title: 'Passport Validity',
      order: 8,
      value: renderValidity(crew?.seafarer_person?.passports?.[0]?.date_of_expiry),
    },
    {
      title: "Seaman's Book Validity",
      order: 9,
      value: renderValidity(getSeamanBook(crew?.seafarer_person?.seaman_books)?.[0]?.date_of_expiry),
    },
    {
      title: 'Reporting Office',
      order: 10,
      value: get(crew, 'seafarer_reporting_office.value', Dash),
    },
  ];
  if (!isSignedOnCrew) {
    columns.push(
      ...[
        {
          title: 'Due off Date',
          order: 4,
          value:
            dateAsString(
              get(crew, 'seafarer_person.seafarer_status_history[0].expected_contract_end_date'),
            ) ?? Dash,
        },
        {
          title: 'Joining Date',
          order: 5,
          value:
            dateAsString(get(crew, 'seafarer_person.seafarer_status_history[0].status_date')) ??
            Dash,
        },
      ],
    );
  } else {
    columns.push(
      ...[
        {
          title: 'Sign off Date',
          order: 4,
          value: dateAsString(get(crew, 'prevSignOff.status_date')) ?? Dash,
        },
        {
          title: 'Availablity Date',
          order: 5,
          value:
            dateAsString(getAvailbilityDate(crew, crew?.crew_planning?.crew_planning_audit_log)) ??
            Dash,
        },
        {
          title: 'Previous Vessel',
          order: 11,
          value: previouslVeselOwnershipId ? (
            <Button
              onClick={(e) => {
                e.stopPropagation();
                window.open(`/vessel/ownership/details/${previouslVeselOwnershipId}`);
              }}
              variant="link"
              className="p-0"
            >
              {get(crew, 'seafarer_experience[0].vessel_name', Dash)}
            </Button>
          ) : (
            get(crew, 'seafarer_experience[0].vessel_name', Dash)
          ),
        },
        {
          title: 'Previous Owner',
          order: 12,
          value: get(crew, 'seafarer_experience[0].owner_name'),
        },
        {
          title: 'Appraisal',
          order: 13,
          value: (
            <Button
              onClick={(e) => {
                e.stopPropagation();
                window.open(`/seafarer/details/${crew.id}/appraisals`, '_blank');
              }}
              variant="link"
              className="p-0"
            >
              Appraisal
            </Button>
          ),
        },
      ],
    );
  }

  return columns.sort((a, b) => a.order - b.order);
};
