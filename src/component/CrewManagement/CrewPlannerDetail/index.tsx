import React, { ReactNode, useCallback, useEffect, useMemo, useState } from 'react';
import { <PERSON><PERSON>, Button, Col, OverlayTrigger, Row, Tooltip } from 'react-bootstrap';
import Avatar from '@src/component/common/Avatar';
import { Drawer } from '@src/component/common/Drawer';
import { ExclamationCircle } from 'react-bootstrap-icons';
import { Itinerary } from '@src/types/itinerary';
import './style.scss';
import { CREW_PLANNING_STATUS, RANK_HIERARCHY } from '@src/constants/crewPlanner';
import { getCrewList, getCrewPlanningScore, getOcimfData } from '@src/service/seafarer-service';
import moment from 'moment';
import {
  checkAdditionalRequest,
  checkMissingPersonnel,
  getScoreDifferenceColor,
  getVesselIdFromSeafarer,
  getVesselType,
} from '@src/pages/CrewPlanner/utils';
import { getRank, getUIPlanningStatusForPODManager } from '@src/component/CrewPlanner/utils';
import { set } from 'lodash';
import { UIPlanningStatus } from '@src/constants/status';
import { renderLoader } from '../CrewManagementAllCrewDetails';
import { getDetailColumns } from './columns-map';

interface SeafarerProfileCardProps {
  firstName: string;
  lastName: string;
  metadata: string;
  remark?: string;
}

interface CrewDetailProps {
  crew: any;
  experienceScore: string;
  isSignedOnCrew?: boolean;
  eventTracker: Function;
  isOnBoardingWith?: boolean;
  vesselType?: string;
}

export interface DetailMeta {
  title: string;
  order?: number;
  value: string | ReactNode;
}

interface CrewPlannerDetailsProps {
  visible: boolean;
  toBeReplacedSeafarer: any;
  reliever: any;
  onClose?: () => void;
  itinerary?: Itinerary;
  relieverStatus: any;
  isRelieverTravelling: boolean;
  isRelieverSignedOn: boolean;
}

export const checkOcimfExist = (crew) => {
  return crew?.ocimf;
};

const SeafarerProfileCard = ({
  firstName,
  lastName,
  metadata,
  remark,
}: SeafarerProfileCardProps) => {
  return (
    <div className="d-flex align-items-center">
      <Avatar firstName={firstName} lastName={lastName} />
      <div className="d-flex flex-column ml-2">
        <div className="text-primary">
          {firstName}
          {' '}
          {lastName}
          <OverlayTrigger
            placement="right"
            overlay={(
              <Tooltip id="profile-tooltip">
                <p>{remark ?? '---'}</p>
              </Tooltip>
            )}
          >
            <ExclamationCircle className="ml-2 text-black-50" size={15} />
          </OverlayTrigger>
        </div>
        <div>
          <b>{metadata ?? '---'}</b>
        </div>
      </div>
    </div>
  );
};

const CrewDetail = ({
  crew,
  isSignedOnCrew,
  eventTracker,
  isOnBoardingWith,
  experienceScore,
  vesselType,
}: CrewDetailProps) => {
  const { id: seafarerId, seafarer_person, crew_planning_remarks } = crew;
  const handleViewProfile = useCallback(() => {
    eventTracker(
      'crewViewProfile',
      `${seafarer_person.first_name ?? ''} ${seafarer_person.middle_name ?? ''} ${
        seafarer_person.last_name ?? ''
      }`,
    );
    window.open(`/seafarer/details/${seafarerId}/general`, '_blank');
  }, [seafarerId]);

  const seafarerDetailList = useMemo(() => {
    return getDetailColumns(crew, isSignedOnCrew, vesselType, getRank(crew));
  }, [crew, isSignedOnCrew]);

  const remark = crew_planning_remarks?.[0]?.remarks;
  return (
    <>
      <div className="d-flex justify-content-between align-items-center mb-3">
        <SeafarerProfileCard
          firstName={seafarer_person?.first_name}
          lastName={seafarer_person?.last_name}
          metadata={getRank(crew)}
          remark={remark}
        />
        <div className="d-flex">
          {isOnBoardingWith && (
            <div
              className={`planning-relieve-date-bg mt-1 mr-2 ${getScoreDifferenceColor(
                experienceScore,
              )}`}
            >
              {`Joint Experience Score: ${experienceScore}`}
            </div>
          )}
          <Button variant="outline-primary" onClick={handleViewProfile} size="sm">
            Visit Profile
          </Button>
        </div>
      </div>
      <Row className="mx-0" md={3}>
        {seafarerDetailList.map((item) => {
          return (
            <Col key={item.title} className="mb-3 p-0">
              <div className="font-weight-bold">{item.title}</div>
              <div>{item.value}</div>
            </Col>
          );
        })}
      </Row>
    </>
  );
};

const CrewPlannerDetailDrawer = ({
  visible,
  toBeReplacedSeafarer,
  reliever,
  onClose,
  itinerary,
  relieverStatus,
  isRelieverTravelling,
  isRelieverSignedOn,
  eventTracker,
}: CrewPlannerDetailsProps) => {
  const renderAlert = () => {
    if (!itinerary?.isSimulatedItinerary) return;
    const { port, estimated_arrival_formatted } = itinerary;

    return (
      <Alert variant="info">
        Placeholder Port:
        {' '}
        {port} • Suggestive Date:
        {' '}
        {estimated_arrival_formatted}
      </Alert>
    );
  };
  const [onBoardingWithSeafarers, setOnBoardingWithSeafarers] = useState([]);
  const [isLoadingOnboardSeafarers, setIsLoadingOnboardSeafarers] = useState(false);
  const [currentSeafarer, setCurrentSeafarer] = useState(null);
  const [currentReliever, setCurrentReliever] = useState(null);
  const [jointExperienceScore, setJointExperienceScore] = useState('N/A');

  const vesselType = getVesselType(toBeReplacedSeafarer);
  const vesselId =
    getVesselIdFromSeafarer(toBeReplacedSeafarer) || getVesselIdFromSeafarer(reliever);
  const currentRank = getRank(toBeReplacedSeafarer);

  const crewPlanStatus = toBeReplacedSeafarer?.crew_planning?.planning_status;

  useEffect(() => {
    setIsLoadingOnboardSeafarers(true);
    setCurrentSeafarer(null);
    setCurrentReliever(null);
    (async () => {
      if (vesselId && toBeReplacedSeafarer && visible) {
        const seafarerPersonIds = [
          !checkOcimfExist(toBeReplacedSeafarer) ? toBeReplacedSeafarer?.seafarer_person_id : null,
          !checkOcimfExist(reliever) ? reliever?.seafarer_person_id : null,
        ].filter(Boolean);
        const aggregatedResponses: any[] = [];
        const ocimfPromises = seafarerPersonIds?.map((id) => {
          return getOcimfData(
            {
              rank_id: toBeReplacedSeafarer?.rank_id ?? reliever?.rank_id,
              rank: currentRank,
              vessel_id: vesselId,
              vessel_type: vesselType,
            },
            id,
          );
        });
        const ocimfResponses = await Promise.all(ocimfPromises);
        ocimfResponses?.forEach((res) => {
          aggregatedResponses.push(...(res?.data?.response ?? []));
        });
        [toBeReplacedSeafarer, reliever]
          .filter((crew) => crew)
          .forEach((crew) => {
            if (crew?.ocimf) {
              set(
                crew,
                'experience_summary.duration_in_target_rank_years',
                crew?.ocimf?.filter(
                  (o) => o?.group_by_name === 'rank' && o?.group_value === currentRank,
                )?.[0]?.years ?? 0,
              );
              set(
                crew,
                'experience_summary.duration_on_target_vessel_type_years',
                crew?.ocimf?.filter((o) => o?.group_value === vesselType)?.[0]?.years ?? 0,
              );
              set(
                crew,
                'experience_summary.duration_with_company_years',
                crew?.ocimf?.filter((o) => o?.group_by_name === 'company')?.[0]?.years ?? 0,
              );
            } else if (!crew?.ocimf) {
              const ocimfResponse = aggregatedResponses.find((r) => r.seafarerId === crew.id);
              if (ocimfResponse) {
                set(
                  crew,
                  'experience_summary.duration_in_target_rank_years',
                  ocimfResponse?.yearsInCurrentRank,
                );
                set(
                  crew,
                  'experience_summary.duration_on_target_vessel_type_years',
                  ocimfResponse?.yearsOnCurrentTypeOfVessel,
                );
                set(
                  crew,
                  'experience_summary.duration_with_company_years',
                  ocimfResponse?.yearsWithCompany,
                );
              }
            }
          });
        const isMissingPersonnal = checkMissingPersonnel(toBeReplacedSeafarer);
        if (!isMissingPersonnal) {
          setCurrentSeafarer(toBeReplacedSeafarer);
        }
        setCurrentReliever(reliever);
        if (
          Object.keys(RANK_HIERARCHY).includes(currentRank) &&
          ![CREW_PLANNING_STATUS.completed, CREW_PLANNING_STATUS.expired].includes(crewPlanStatus)
        ) {
          const crewList = await getCrewList(
            `seafarer_rank.value=${
              RANK_HIERARCHY[currentRank]
            }&crew_list_status_date=${moment().format('yyyy-MM-DD')}&vessel_id=${vesselId}`,
          );
          crewList?.data?.results?.forEach((crew) => {
            const ocimf = aggregatedResponses?.filter((aggRes) => aggRes?.seafarerId === crew?.id);
            if (ocimf?.length) {
              set(
                crew,
                'experience_summary.duration_in_target_rank',
                ocimf?.[0]?.yearsInCurrentRank * 365,
              );
              set(
                crew,
                'experience_summary.duration_on_target_vessel_type',
                ocimf?.[0]?.yearsOnCurrentTypeOfVessel * 365,
              );
              set(
                crew,
                'experience_summary.duration_with_company',
                ocimf?.[0]?.yearsWithCompany * 365,
              );
            }
          });
          setOnBoardingWithSeafarers(crewList?.data?.results);
        }
        setIsLoadingOnboardSeafarers(false);
      }
    })();
  }, [visible]);
  const isAdditionalCrewRequest = checkAdditionalRequest(toBeReplacedSeafarer);
  const renderStatus = () => {
    let status;

    const planStatus = getUIPlanningStatusForPODManager({
      reliever,
      toBeReplacedSeafarer,
      relieverStatus,
      planningStatus: toBeReplacedSeafarer?.crew_planning?.planning_status,
      isHistoryModal: false,
    });
    if (isRelieverSignedOn || isRelieverTravelling) return null;
    if (planStatus === UIPlanningStatus.Completed) {
      status = <span className="status-badge status-badge-success">Completed</span>;
    } else if (planStatus === UIPlanningStatus.Expired) {
      status = <span className="status-badge status-badge-danger">Expired</span>;
    } else if (
      !relieverStatus?.isCrewAssignmentApproved ||
      !relieverStatus?.isDocumentAndTrainingComplete
    ) {
      status = (
        <span className="status-badge status-badge-warning">Technical Approval Pending</span>
      );
    }
    if (isAdditionalCrewRequest) {
      return (
        <>
          <span className="seafarer-card-additional-crew">Additional crew</span>
          {status}
        </>
      );
    }
    return status;
  };

  useEffect(() => {
    const loadCrewPlanningScore = async () => {
      const payload = {
        vessel_id: vesselId,
        vessel_type: vesselType,
        seafarer_ids: [currentReliever?.id],
        rank: currentRank,
      };
      const response = await getCrewPlanningScore(payload);
      if (response) {
        const jointExp = response.data?.[0]?.experience_grade;
        setJointExperienceScore(jointExp ?? 'N/A');
      }
    };

    if (currentReliever) {
      loadCrewPlanningScore();
    }
  }, [currentReliever]);

  return (
    <Drawer show={visible}>
      <Drawer.Header title="Crew Change Details" onHide={onClose} extra={renderStatus()} />
      <Drawer.Body className="crew-planner-detail">
        {itinerary && renderAlert()}
        {!!currentSeafarer && !currentSeafarer?.isAdditionalRequest && (
          <CrewDetail
            crew={currentSeafarer}
            isSignedOnCrew={false}
            eventTracker={eventTracker}
            vesselType={vesselType}
            experienceScore={jointExperienceScore}
          />
        )}
        {currentReliever && currentSeafarer && <hr />}
        {!!currentReliever && (
          <CrewDetail
            crew={currentReliever}
            isSignedOnCrew
            eventTracker={eventTracker}
            vesselType={vesselType}
            experienceScore={jointExperienceScore}
          />
        )}
        {currentReliever && !!onBoardingWithSeafarers?.length && <hr />}
        {isLoadingOnboardSeafarers && !onBoardingWithSeafarers.length ? renderLoader() : ''}
        {!!onBoardingWithSeafarers?.length && (
          <div>
            <div className="planning-relieve-modal-headings">
              Seafarer will be joining Onboard with:
            </div>
            <div className="mb-2">
              The planner needs to confirm the Experience Details with the combined Crew Onboard for
              the Vessel Category.
            </div>
            {onBoardingWithSeafarers?.map((onBoardingSeafarer) => (
              <CrewDetail
                key={onBoardingSeafarer?.id}
                crew={onBoardingSeafarer}
                isOnBoardingWith
                eventTracker={eventTracker}
                vesselType={vesselType}
                experienceScore={jointExperienceScore}
              />
            ))}
          </div>
        )}
      </Drawer.Body>
    </Drawer>
  );
};

export default CrewPlannerDetailDrawer;
