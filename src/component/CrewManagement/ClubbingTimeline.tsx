import React, { useState } from 'react';
import { DndProvider, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { ChevronDown, ChevronUp, Pen, Trash3 } from 'react-bootstrap-icons';
import { <PERSON><PERSON>, <PERSON> } from 'react-bootstrap';
import { Itinerary } from '@src/types/itinerary';
import { useCrewMangementContext } from '@src/pages/CrewManagementPage';
import { UIPlanningStatus } from '@src/constants/status';
import InfiniteScroll from 'react-infinite-scroll-component';
import moment from 'moment-timezone';
import DraggableSeafarerCard from './DraggableSeafarerCard';
import { CREW_PLANNING_RIGHT_SIDE } from '../../constants/crewPlanner';
import '../scss/clubbing-timeline.scss';
import AddPlus from '../../../public/icons/add-plus.svg';
import History from '../../../public/icons/history.svg';
import Anchor from '../../../public/icons/anchor.svg';
import Spinner from '../common/Spinner';
import ConfirmClubModal from '../CrewPlanner/ConfirmClubModal';
import SimulatedPortModal from './SimulatePort/SimulatedPortModal';
import DeleteSimulatedPortModal from './SimulatePort/DeleteSimulatedPortModal';
import '../scss/simulate-itinerary.scss';
import { getUIPlanningStatusForPODManager } from '../CrewPlanner/utils';
import ClubbingHistory from '../CrewPlanner/ClubbingHistory';

const { PARIS_ONE_HOST } = process.env;

interface ClubbingTimelineProps {
  relieverStatus: any[] | null;
  handleConfirmPlanningAction: Function;
  eventTracker: Function;
  hasEditAccess: boolean;
}
interface TimelineEventProps {
  itinerary: Itinerary;
  endDate: string | null;
  relieverStatus: any[] | null;
  onConfirmClub: Function;
  eventTracker: Function;
  hasEditAccess: boolean;
  isAllowAddPort: boolean;
}

const TimelineEvent = ({
  itinerary,
  endDate,
  relieverStatus,
  onConfirmClub,
  eventTracker,
  hasEditAccess,
  isAllowAddPort,
}: TimelineEventProps) => {
  // Define state here
  const [showSimulatedPortModal, setShowSimulatedPortModal] = useState(false);
  const [selectEditItinerary, setSelectEditItinerary] = useState<number | null>(null);
  const [expanded, setExpanded] = useState(false);
  const [showDeleteSimulatedItineraryModal, setShowDeleteSimulatedItineraryModal] =
    useState<boolean>(false);
  const { upsertClubbedCrew } = useCrewMangementContext();
  const {
    port,
    estimated_arrival_formatted,
    crewPlanning = [],
    crewChanges,
    isDeletedItineraryByMaster,
  } = itinerary;
  const [, drop] = useDrop({
    accept: 'seafarerCard',
    drop: (item) => {
      if (isDeletedItineraryByMaster || moment().isAfter(moment(itinerary.estimated_arrival), 'D'))
        return; // do not allow drop on deleted itinerary or past dates
      upsertClubbedCrew(item.crew.id, itinerary, item?.crew?.crew_planning?.id);
    },
  });
  const isSimulated = itinerary.isSimulatedItinerary;
  const validCrewPlaning = crewPlanning.filter((crew) =>
    CREW_PLANNING_RIGHT_SIDE.includes(crew?.crew_planning?.planning_status),
  );

  const handleEditSimulatePort = (id: number) => {
    setShowSimulatedPortModal(true);
    setSelectEditItinerary(id);
  };
  const handleDeleteSimulatePort = () => {
    setShowDeleteSimulatedItineraryModal(true);
  };
  const handleCloseSimulatePortModal = () => {
    setShowSimulatedPortModal(false);
    setSelectEditItinerary(null);
    setShowDeleteSimulatedItineraryModal(false);
  };

  const handleAddSimulatePort = () => {
    setShowSimulatedPortModal(true);
  };
  const renderClubbingStatus = () => {
    if (moment().isAfter(moment(itinerary?.estimated_arrival)) && crewPlanning?.length) {
      return (
        <div className="clubbing-status">
          <div
            className="d-flex align-items-center"
            onClick={() => setExpanded(!expanded)}
            style={{ cursor: 'pointer' }}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' '){
                e.preventDefault();
                setExpanded(!expanded);
              } 
            }}
            tabIndex={0}
          >
            <span className="underline-text"> {itinerary?.crewPlanning?.length} Crew Planned</span>
            {expanded ? (
              <ChevronDown color="#1F4A70" className="ml-2" size={14} />
            ) : (
              <ChevronUp color="#1F4A70" className="ml-2" size={14} />
            )}
          </div>
        </div>
      );
    }
    if (!validCrewPlaning.length) return null;

    const pendingCount = validCrewPlaning.reduce((count, crew) => {
      const relieverId = crew?.crew_planning?.reliever_id;
      const currentRelieverStatus = relieverStatus?.find((i) => i.id === relieverId);
      const status = getUIPlanningStatusForPODManager({
        relieverStatus: currentRelieverStatus,
        reliever: crew?.crew_planning?.reliever,
        toBeReplacedSeafarer: crew,
        planningStatus: crew?.crew_planning?.planning_status,
        isHistoryModal: false,
      });
      return status === UIPlanningStatus.Pending ? count + 1 : count;
    }, 0);

    const plannedCount = validCrewPlaning?.length - pendingCount;

    const clubbingStatus = [
      plannedCount && `${plannedCount} Crew Planned`,
      pendingCount && `${pendingCount} Pending`,
    ]
      .filter(Boolean)
      .join(' • ');

    return <div className="clubbing-status">{clubbingStatus}</div>;
  };
  return (
    <>
      {showSimulatedPortModal && (
        <SimulatedPortModal
          itinerary={itinerary}
          isEditing={!!selectEditItinerary}
          crewChangeDestinationData={{
            startDate: estimated_arrival_formatted,
            endDate,
          }}
          show={showSimulatedPortModal}
          onClose={handleCloseSimulatePortModal}
          eventTracker={eventTracker}
        />
      )}
      {showDeleteSimulatedItineraryModal && (
        <DeleteSimulatedPortModal
          itinerary={itinerary}
          show={showDeleteSimulatedItineraryModal}
          onClose={handleCloseSimulatePortModal}
          eventTracker={eventTracker}
        />
      )}
      <div ref={drop} className="timeline-event">
        <div
          className={
            isDeletedItineraryByMaster ? 'timeline-label date-not-available' : 'timeline-label'
          }
        >
          {estimated_arrival_formatted}
          {isSimulated && <div className="timeline-label-meta">Placeholder Port</div>}
        </div>

        <div className="timeline-seperator" style={isAllowAddPort ? { cursor: 'pointer' } : { cursor: 'auto' }}>
          {isAllowAddPort && hasEditAccess && (<img
              src={AddPlus}
              id="add-port"
              className="add-port"
              onKeyDown={() => ({})}
              onClick={handleAddSimulatePort}
            />)}
        </div>
        <div className="timeline-port">
          <div className={isDeletedItineraryByMaster ? 'port-name port-deleted' : 'port-name'}>
            <span>{port}</span>
            {isSimulated && hasEditAccess && (
              <>
                <Pen
                  className="paris2-icon btn-icon ml-2"
                  size={14}
                  onClick={() => handleEditSimulatePort(itinerary?.id)}
                />
                <Trash3
                  className="paris2-icon btn-icon ml-2"
                  size={14}
                  onClick={handleDeleteSimulatePort}
                />
              </>
            )}
          </div>
          {!!crewChanges && (
<div className="crew-changes">{crewChanges} Crew Changes</div>
)}
          {renderClubbingStatus()}
          {expanded ||
            (moment().isBefore(moment(itinerary?.estimated_arrival)) && crewPlanning?.length) ||
            isDeletedItineraryByMaster
            ? crewPlanning
              .filter((crew) =>
                CREW_PLANNING_RIGHT_SIDE.includes(crew?.crew_planning?.planning_status),
              )
              .map((crew) => {
                const thisRelieverStatus =
                  relieverStatus?.find((i) => i.id === crew?.crew_planning?.reliever_id) ?? null;
                return (
                  <DraggableSeafarerCard
                    isFromTimeline
                    key={crew.id}
                    crew={crew}
                    relieverStatus={thisRelieverStatus}
                    itinerary={itinerary}
                    onConfirmClub={onConfirmClub}
                    eventTracker={eventTracker}
                    hasEditAccess={hasEditAccess}
                />
                );
              })
            : ''}
        </div>
      </div>
    </>
  );
};

const ClubbingTimeline = ({
  relieverStatus,
  handleConfirmPlanningAction,
  eventTracker,
  hasEditAccess,
}: ClubbingTimelineProps) => {
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
  const [isViewClubbingModalOpen, setIsViewClubbingModalOpen] = useState(false);

  const [showSimulatedPortModal, setShowSimulatedPortModal] = useState(false);
  const {
    setSelectedActionSeafarer,
    isCrewListLoading,
    itineraryList,
    fetchMoreData,
    hasMoreData,
    visitSeafarerDetail,
  } = useCrewMangementContext();
  const [seafarerId, setSeafarerId] = useState(0);
  const handleCloseConfirm = () => {
    setIsConfirmModalOpen(false);
    setSeafarerId(0);
  };
  const handleConfirmClub = async () => {
    await handleConfirmPlanningAction();
    handleCloseConfirm();
    visitSeafarerDetail(seafarerId);
    setSeafarerId(0);
  };
  const handleConfirmClick = (payload: any) => {
    setSelectedActionSeafarer(payload);
    setSeafarerId(payload?.seafarer?.crew_planning?.reliever?.id);
    setIsConfirmModalOpen(true);
  };
  const renderLoader = () => {
    return (
      <div className="crew-management-crew-details-loader-wrapper">
        <Spinner alignClass="spinner-grid" data-testid="loader" />
      </div>
    );
  };
  const renderEmptyTimeline = () => {
    return (
      <div className="timeline-empty">
        <div className="timeline-empty-body">
          <img className="mb-3" src={Anchor} />
          <div className="text-muted mb-3">
            No Ports Available. Please add a Placeholder port to start scheduling Seafarers
          </div>
          {hasEditAccess && (
            <Button variant="primary" size="sm" onClick={() => setShowSimulatedPortModal(true)}>
              Add Placeholder Port
            </Button>
          )}
        </div>
      </div>
    );
  };
  const renderContent = () => {
    if (isCrewListLoading) {
      return renderLoader();
    }

    if (!itineraryList?.length) {
      return renderEmptyTimeline();
    }

    return (
      <div
        id="scrollableDiv"
        style={{
          height: '100%',
          overflowY: 'scroll',
          display: 'flex',
          flexDirection: 'column-reverse', // To make the scroll start from the bottom
        }}
        className="timeline"
      >
        <InfiniteScroll
          dataLength={itineraryList?.length}
          next={fetchMoreData}
          style={{ display: 'flex', flexDirection: 'column-reverse' }} // To put endMessage and loader to the top.
          inverse //
          hasMore={hasMoreData}
          loader={(
            <div
              className="timeline-seperator-loader d-flex"
              style={{
                marginLeft: '110px',
                width: '100%',
                padding: '10px',
              }}
            >
              <div className="spinner-timeline-p">
                <Spinner customClass="spinner-timeline" />
                <span className="mx-2">Loading Past Ports</span>
              </div>
            </div>
          )}
          scrollableTarget="scrollableDiv"
        >
          <DndProvider backend={HTML5Backend}>
            {itineraryList?.map((itinerary, index) => {
              // allow add port when:
              // - port is in the future
              // - 1 port before today's date
              // - it is the last port in the list (but this port need to be a non-deleted port)
              const lastPortThatIsNotDeletedPort = itineraryList.findIndex((i) => !i.isDeletedItineraryByMaster)
              const isAllowAddPort = new Date(itineraryList?.[index - 1]?.estimated_arrival) > new Date() || index === lastPortThatIsNotDeletedPort;
              return (
                <TimelineEvent
                  endDate={itineraryList?.[index - 1]?.estimated_arrival_formatted}
                  key={itinerary.id}
                  itinerary={itinerary}
                  relieverStatus={relieverStatus}
                  onConfirmClub={handleConfirmClick}
                  eventTracker={eventTracker}
                  isAllowAddPort={isAllowAddPort}
                  hasEditAccess={hasEditAccess}
            />
              );
            })}
          </DndProvider>
        </InfiniteScroll>
      </div>
    );
  };
  const handleViewClubbingHistory = () => {
    setIsViewClubbingModalOpen(true);
  };
  return (
    <>
      {isConfirmModalOpen && (
        <ConfirmClubModal
          show={isConfirmModalOpen}
          handleCloseConfirm={handleCloseConfirm}
          handleConfirmClub={handleConfirmClub}
        />
      )}
      {isViewClubbingModalOpen ? (
        <ClubbingHistory
          show={isViewClubbingModalOpen}
          handleClose={() => setIsViewClubbingModalOpen(false)}
        />
      ) : (
        ''
      )}
      {showSimulatedPortModal && (
        <SimulatedPortModal
          show={showSimulatedPortModal}
          onClose={() => setShowSimulatedPortModal(false)}
          eventTracker={eventTracker}
        />
      )}
      <Card>
        <Card.Header className="d-flex justify-content-between">
          <span className="text-primary font-weight-bold">Clubbing Timeline</span>
          <span 
            onClick={handleViewClubbingHistory}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                handleViewClubbingHistory();
              }
            }}
            tabIndex={0}
            style={{ cursor: 'pointer' }}
          >
            <img src={History} style={{ marginRight: '1px' }} />
            <span className="view-clubbing-history"> View Clubbing History</span>
          </span>
        </Card.Header>

        <Card.Body className="overflow-auto timeline-card-body crew-management-crew-details-list">
          {renderContent()}
        </Card.Body>
      </Card>
    </>
  );
};
export default ClubbingTimeline;
