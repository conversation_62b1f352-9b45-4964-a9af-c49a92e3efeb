import React from 'react';
import { Col, Row } from 'react-bootstrap';
import CrewManagementAllCrewDetails from './CrewManagementAllCrewDetails';
import ClubbingTimeline from './ClubbingTimeline';

interface CrewManagementContentSectionProps {
  relieverStatus: any[] | null;
  handleConfirmPlanningAction: Function;
  eventTracker: Function;
  hasEditAccess: boolean;
}

const CrewManagementContentSection = ({
  relieverStatus,
  handleConfirmPlanningAction,
  eventTracker,
  hasEditAccess
}: CrewManagementContentSectionProps) => {
  return (
    <Row className="seafarer-header-row">
      <Col md="4">
        <CrewManagementAllCrewDetails
          relieverStatus={relieverStatus}
          eventTracker={eventTracker}
          hasEditAccess={hasEditAccess}
        />
      </Col>
      <Col md="8">
        <ClubbingTimeline
          relieverStatus={relieverStatus}
          handleConfirmPlanningAction={handleConfirmPlanningAction}
          eventTracker={eventTracker}
          hasEditAccess={hasEditAccess}
        />
      </Col>
    </Row>
  );
};

export default CrewManagementContentSection;
