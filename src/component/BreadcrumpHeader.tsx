import React from 'react';
import { Breadcrumb } from 'react-bootstrap';

const BreadcrumbHeader = ({ items = [], activeItem, onClick }) => {
  return (
    <Breadcrumb className="bread-crump-wrapper">
      {items.map((item) => {
        const isItemActive = item?.title === activeItem;
        const className = isItemActive ? 'breadcrumb-text' : 'inactive-breadcrumb-text';
        return (
          item && (
            <Breadcrumb.Item
              key={item.title}
              href={item.link}
              className={className}
              active={isItemActive}
              onClick={() => onClick(item.label)}
            >
              {item.title}
            </Breadcrumb.Item>
          )
        );
      })}
    </Breadcrumb>
  );
};

export { BreadcrumbHeader };
