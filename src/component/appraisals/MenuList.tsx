import React from 'react';
import { getCurrentUserInfo } from '../../service/user-service';
import { Dash, dateAsString, stringAsDate } from '../../model/utils';
import VesselNameLink from '../CrewList/VesselNameLink';
import styleGuide from '../../styleGuide';
import { OverlayTrigger, Tooltip, Button } from 'react-bootstrap';
import {
  showColorClass,
  getMasterAppaisalGradeColorClass,
  getAppaisalToolTipText,
  isSubmittedColor,
} from '../../util/appraisal';
// eslint-disable-next-line no-unused-vars
import { SuptAppraisalResult } from '../../types/suptAppraisal';
// eslint-disable-next-line no-unused-vars
import { MasterAppraisalResult } from '../../types/masterAppraisal';
// eslint-disable-next-line no-unused-vars
import { DebriefingResult } from '../../types/debreifing';
// eslint-disable-next-line no-unused-vars
import { TrainingRequirementResult } from '../../types/trainingRequirement';
import { Link } from 'react-router-dom';
import moment from 'moment';
import _ from 'lodash';
import GA4React from 'ga-4-react';
import {
  SeafarerDocument,
  SeafarerTrainingRequirementDocument,
} from '../../types/seafarerInterfaces';
import { ALL_DOC_TYPES } from '../../constants/documentTypes';
import { trainingReqCreatedByLabels } from '../../constants/appraisal';

const { Icon } = styleGuide;

let roleConfig: any;

export const setRoleConfig = (_roleConfig: any) => {
  roleConfig = _roleConfig;
};

const ga4EventTrigger = (action: string, category: string, label: string, ga4react: GA4React) => {
  try {
    ga4react?.event(action, _.toString(label), category, false);
  } catch (error) {
    console.log(error);
  }
};

const eventTracker = (type: string, value: string, ga4react: GA4React) => {
  switch (type) {
    case 'viewMasterAppraisalVessel':
      ga4EventTrigger('View Vessel Details', 'Seafarer Master Appraisals - link ', value, ga4react);
      break;
    case 'viewMaster':
      ga4EventTrigger(
        'View Mater Appraisal Document',
        'Seafarer Master Appraisals - link ',
        value,
        ga4react,
      );
      break;
    case 'viewSuptAppraisalVessel':
      ga4EventTrigger(
        'View Vessel Details',
        'Seafarer Superintendent Appraisals - link',
        value,
        ga4react,
      );
      break;
    case 'viewSuptAppraisal':
      ga4EventTrigger(
        'View Superintendent Appraisal',
        'Seafarer Superintendent Appraisals - link',
        value,
        ga4react,
      );
      break;
    case 'editSuptAppraisal':
      ga4EventTrigger(
        'Edit Superintendent Appraisal',
        'Seafarer Superintendent Appraisals - menu',
        value,
        ga4react,
      );
      break;
    case 'viewTrainingRequirementVessel':
      ga4EventTrigger(
        'View Vessel Details',
        'Seafarer Training Requirements - link',
        value,
        ga4react,
      );
      break;
    case 'viewTrainingReqSuptAppraisal':
      ga4EventTrigger(
        'View Superintendent Appraisal',
        'Seafarer Training Requirements - link',
        value,
        ga4react,
      );
      break;
    case 'viewTrainingRequirementSupportingDocument':
    case 'viewTrainingRequirementSuptReport':
      ga4EventTrigger(
        'View Training Requirement Document',
        'Seafarer Training Requirements - link',
        value,
        ga4react,
      );
      break;
    case 'editTrainingRequirement':
      ga4EventTrigger(
        'Edit Training Requirement',
        'Seafarer Training Requirement - menu',
        value,
        ga4react,
      );
      break;
    default:
      ga4EventTrigger('Click', 'Crew List Page', value, ga4react);
      break;
  }
};

const showViewLink = (suptAppraisalId?: number | null, row?: SuptAppraisalResult) => {
  const handleEvent = () => {
    const seafarerName = `${row?.survey_participation_superintendent_appraisal?.first_name} ${row?.survey_participation_superintendent_appraisal?.last_name}`;
    const rank = row?.survey_participation_superintendent_appraisal?.rank_value;

    row?.ga4react && eventTracker('viewSuptAppraisal', `${seafarerName} ${rank}`, row?.ga4react);
  };
  return (
    <Link to={`/survey/supt-appraisal/view/${suptAppraisalId}`} onClick={() => handleEvent()}>
      <u>View</u>
    </Link>
  );
};

const routeToMasterAppraisalDetailsPage = (
  seafarerId: number,
  masterAppraisalId: number,
  row: MasterAppraisalResult,
) => {
  return (
    <Link
      to={`/seafarer/details/${seafarerId}/appraisals/mstr-appraisal/view/${masterAppraisalId}`}
      onClick={() => {
        row?.ga4react && eventTracker('viewMaster', row?.rank?.value, row?.ga4react);
      }}
    >
      <u>View </u>
    </Link>
  );
};

const routeToSupportingDocumentPage = (row: TrainingRequirementResult) => {
  const trainingReqDocs: SeafarerTrainingRequirementDocument[] =
    row?.seafarer_training_requirement_document;
  const supportingDocument = trainingReqDocs?.find(
    (doc: SeafarerTrainingRequirementDocument) =>
      doc.seafarer_document?.type === ALL_DOC_TYPES.SUPPORTING_DOCUMENT &&
      !doc.seafarer_document?.is_deleted,
  );
  if (supportingDocument) {
    const doc: SeafarerDocument = supportingDocument.seafarer_document;
    return (
      <Button
        variant="link"
        className="link-underline"
        onClick={() => {
          row?.ga4react &&
            eventTracker(
              'viewTrainingRequirementSupportingDocument',
              row?.vessel_name,
              row?.ga4react,
            );
          window.open(
            `https://${window.location.hostname}/seafarer/document/${doc.id}/${doc.type}`,
          );
        }}
      >
        <u>View</u>
      </Button>
    );
  } else {
    return Dash;
  }
};

const routeToSuptReportDocumentPage = (row: TrainingRequirementResult) => {
  const trainingReqDocs: SeafarerTrainingRequirementDocument[] =
    row?.seafarer_training_requirement_document;
  const suptReport = trainingReqDocs?.find(
    (doc: SeafarerTrainingRequirementDocument) =>
      doc.seafarer_document?.type === ALL_DOC_TYPES.SUPERINTENDENT_REPORT &&
      !doc.seafarer_document?.is_deleted,
  );
  if (suptReport) {
    const doc: SeafarerDocument = suptReport.seafarer_document;
    return (
      <Button
        variant="link"
        className="link-underline"
        onClick={() => {
          row?.ga4react &&
            eventTracker('viewTrainingRequirementSuptReport', row?.vessel_name, row?.ga4react);
          window.open(
            `https://${window.location.hostname}/seafarer/document/${doc.id}/${doc.type}`,
          );
        }}
      >
        <u>View</u>
      </Button>
    );
  } else {
    return Dash;
  }
};

const getFullName = (data: any) => {
  const fullName = `${data?.first_name ?? ''} ${data?.middle_name ?? ''} ${data?.last_name ?? ''}`;
  return fullName.length !== 0 ? fullName : null;
};

const renderTooltip = (props: any) => (
  <Tooltip id="appraisal-tooltip" className="supt-appraisal-tooltip-text" {...props}>
    {getAppaisalToolTipText()}
  </Tooltip>
);

const showMasterGrade = (grade: number) => {
  return (
    <OverlayTrigger placement="bottom" delay={{ show: 250, hide: 400 }} overlay={renderTooltip}>
      <div className={`supt-appraisal-score ${getMasterAppaisalGradeColorClass(Number(grade))}`}>
        {' '}
        {`${grade}`}{' '}
      </div>
    </OverlayTrigger>
  );
};

const showAppraisal = (averageScore: string | number) => {
  return (
    <OverlayTrigger placement="bottom" delay={{ show: 250, hide: 400 }} overlay={renderTooltip}>
      <div className={`supt-appraisal-score ${showColorClass(Number(averageScore))} text-center`}>
        {' '}
        {`${averageScore}%`}{' '}
      </div>
    </OverlayTrigger>
  );
};

const viewSuptAppraisalEditAccessor = (data: SuptAppraisalResult) => {
  const handleEvent = () => {
    const seafarerName = `${data?.survey_participation_superintendent_appraisal?.first_name} ${data?.survey_participation_superintendent_appraisal?.last_name}`;
    const rank = data?.survey_participation_superintendent_appraisal?.rank_value;
    data?.ga4react && eventTracker('editSuptAppraisal', `${seafarerName} ${rank}`, data?.ga4react);
  };
  const user = getCurrentUserInfo();
  // Filter department based on Tech Group
  const techGroup = _.filter(user?.group, (value) => _.includes(value, 'Tech Group'));
  // Split based on actual tech group name
  const techGroupNames = _.map(techGroup, (group) => _.nth(_.split(group, '/'), 2));

  return (
    <div>
      {data?.access_control?.is_editable &&
        data?.survey_participation_superintendent_appraisal?.id > 0 &&
        (techGroupNames.includes(
          data?.survey_participation_superintendent_appraisal?.vessel_ownership_access?.tech_group,
        ) ||
          data.created_by_hash === user?.user_name_hash) && (
          <OverlayTrigger
            key="bottom"
            placement="bottom"
            overlay={
              <Tooltip id={`tooltip-${'bottom'}`}>
                Editable until {moment(data.created_at).add(7, 'days').format('DD MMM')}
                <br />
                (7 days since created day)
              </Tooltip>
            }
          >
            <Link
              to={`/survey/supt-appraisal/edit/${
                data?.survey_participation_superintendent_appraisal?.id ?? '0'
              }`}
              className="action-column"
              onClick={() => handleEvent()}
            >
              <Icon icon="pencil" size={20} />
            </Link>
          </OverlayTrigger>
        )}
    </div>
  );
};

export const superintendentApppraisalsMenuItems = [
  {
    Header: 'Created on',
    id: 'created-on',
    accessor: function createdOnAccessor(row: SuptAppraisalResult) {
      return (
        <div>
          {row?.survey_participation_superintendent_appraisal?.created_at
            ? dateAsString(
                stringAsDate(row?.survey_participation_superintendent_appraisal?.created_at),
              )
            : Dash}
        </div>
      );
    },
    order: 1,
    disableSortBy: true,
    minWidth: 80,
  },
  {
    Header: 'Vessel',
    id: 'vessel_name',
    accessor: function nameOfVesselAccessor(row: SuptAppraisalResult) {
      return (
        <VesselNameLink
          ownershipId={row?.survey_participation_superintendent_appraisal?.vessel_ownership_id}
          vesselName={
            row?.survey_participation_superintendent_appraisal?.vessel_ownership_access?.vessel_name
          }
          eventTracker={() => {
            row?.ga4react &&
              eventTracker(
                'viewSuptAppraisalVessel',
                row?.survey_participation_superintendent_appraisal?.vessel_ownership_access
                  ?.vessel_name,
                row?.ga4react,
              );
          }}
        />
      );
    },
    order: 2,
    disableSortBy: true,
    minWidth: 80,
  },
  {
    Header: 'Rank',
    id: 'rank',
    accessor: function rankAccessor(row: SuptAppraisalResult) {
      return <div>{row?.survey_participation_superintendent_appraisal?.rank_value ?? Dash}</div>;
    },
    order: 3,
    disableSortBy: true,
    minWidth: 80,
  },
  {
    Header: 'Appraiser',
    id: 'appraiser',
    order: 4,
    accessor: function appraiserAccessor(row: SuptAppraisalResult) {
      return <div>{row?.survey_participant ? getFullName(row?.survey_participant) : Dash}</div>;
    },
    disableSortBy: true,
    minWidth: 80,
  },
  {
    Header: 'Submitted on',
    id: 'submitted-on',
    accessor: function createdOnAccessor(row: SuptAppraisalResult) {
      return (
        <div>
          {row?.survey_participation_superintendent_appraisal?.updated_at
            ? dateAsString(
                stringAsDate(row?.survey_participation_superintendent_appraisal?.updated_at),
              )
            : Dash}
        </div>
      );
    },
    order: 5,
    disableSortBy: true,
    minWidth: 80,
  },
  {
    Header: 'Total Weighted Average Score',
    id: 'total-weighted-average-score',
    order: 6,
    accessor: function totalAverageScoreAccessor(row: SuptAppraisalResult) {
      return (
        <div>
          {row?.survey_participation_superintendent_appraisal.average_score
            ? showAppraisal(row.survey_participation_superintendent_appraisal.average_score)
            : Dash}
        </div>
      );
    },
    disableSortBy: true,
    minWidth: 80,
  },
  {
    Header: 'Comment',
    id: 'comment',
    accessor: function appraiserAccessor(row: SuptAppraisalResult) {
      return <div>{row?.comment ?? Dash}</div>;
    },
    order: 7,
    disableSortBy: true,
    minWidth: 80,
  },
  {
    Header: 'Appraisal',
    id: 'appraisal',
    accessor: function appraiserAccessor(row: SuptAppraisalResult) {
      return (
        <div>
          {row.survey_participation_superintendent_appraisal.id
            ? showViewLink(row.survey_participation_superintendent_appraisal.id, row)
            : Dash}
        </div>
      );
    },
    order: 8,
    disableSortBy: true,
    minWidth: 80,
  },
  {
    Header: 'Action',
    id: 'action',
    accessor: (row: SuptAppraisalResult) => viewSuptAppraisalEditAccessor(row),
    disableSortBy: true,
    maxWidth: 50,
    sticky: 'right',
  },
];

export const masterApppraisalsMenuItems = [
  {
    Header: 'Date',
    id: 'created_at',
    accessor: function createdAtAccessor(row: MasterAppraisalResult) {
      return <div>{row?.created_at ? dateAsString(stringAsDate(row?.created_at)) : Dash}</div>;
    },
    order: 1,
    disableSortBy: true,
    minWidth: 80,
  },
  {
    Header: 'Vessel',
    id: 'vessel_name',
    accessor: function nameOfVesselAccessor(row: MasterAppraisalResult) {
      return (
        <VesselNameLink
          ownershipId={row?.vessel_ownership_id}
          vesselName={row?.vessel_name}
          eventTracker={() => {
            row?.ga4react &&
              eventTracker('viewMasterAppraisalVessel', row?.vessel_name, row?.ga4react);
          }}
        />
      );
    },
    order: 2,
    disableSortBy: true,
    minWidth: 80,
  },
  {
    Header: 'Rank',
    id: 'rank_id',
    accessor: function rankAccessor(row: MasterAppraisalResult) {
      return <div>{row?.rank?.value ?? Dash}</div>;
    },
    order: 3,
    disableSortBy: true,
    minWidth: 80,
  },
  {
    Header: 'From',
    id: 'start_date',
    order: 4,
    accessor: function starDateAccessor(row: MasterAppraisalResult) {
      return <div>{row?.start_date ? dateAsString(stringAsDate(row?.start_date)) : Dash}</div>;
    },
    disableSortBy: true,
    minWidth: 80,
  },
  {
    Header: 'To',
    id: 'end_date',
    accessor: function endDateAccessor(row: MasterAppraisalResult) {
      return <div>{row?.end_date ? dateAsString(stringAsDate(row?.end_date)) : Dash}</div>;
    },
    order: 5,
    disableSortBy: true,
    minWidth: 80,
  },
  {
    Header: 'Grade',
    id: 'overall_grade',
    order: 6,
    accessor: function gradeAccessor(row: MasterAppraisalResult) {
      return (
        <div className="d-flex">
          {row?.overall_grade ? showMasterGrade(row.overall_grade) : Dash}
        </div>
      );
    },
    disableSortBy: true,
    minWidth: 80,
  },
  {
    Header: "Master's Comment",
    id: 'comment',
    accessor: function masterCommentAccessor(row: MasterAppraisalResult) {
      return <div>{row?.master_comment ?? Dash}</div>;
    },
    order: 7,
    disableSortBy: true,
    minWidth: 80,
  },
  {
    Header: 'Head Comment',
    id: 'head_comment',
    accessor: function headCommentAccessor(row: MasterAppraisalResult) {
      return <div>{row?.head_comment ?? Dash}</div>;
    },
    order: 8,
    disableSortBy: true,
    minWidth: 80,
  },
  {
    Header: 'Crew Comment',
    id: 'crew_comment',
    accessor: function crewCommentAccessor(row: MasterAppraisalResult) {
      return <div>{row?.crew_comment ?? Dash}</div>;
    },
    order: 9,
    disableSortBy: true,
    minWidth: 80,
  },
  {
    Header: 'Appraisal',
    id: 'appraisal',
    accessor: function appraiserAccessor(row: MasterAppraisalResult) {
      return <div>{routeToMasterAppraisalDetailsPage(row.seafarer_id, row.id, row)}</div>;
    },
    order: 8,
    disableSortBy: true,
    minWidth: 80,
  },
];

export const DebriefingMenuItems = [
  {
    Header: 'Created On',
    id: 'created_at',
    accessor: function debriefingCreatedAccessor(row: DebriefingResult) {
      return <>{row?.created_at ? dateAsString(stringAsDate(row?.created_at)) : Dash}</>;
    },
    order: 1,
    disableSortBy: true,
    minWidth: 80,
  },
  {
    Header: 'Vessel',
    id: 'vessel_name',
    accessor: function debriefingVesselAccessor(row: DebriefingResult) {
      return (
        <>
          {row.survey_participation_debriefing?.vessel_ownership_access?.vessel_name ? (
            <VesselNameLink
              vesselName={row.survey_participation_debriefing?.vessel_ownership_access?.vessel_name}
              ownershipId={row.survey_participation_debriefing?.vessel_ownership_id}
              eventTracker={() => {
                row?.ga4react &&
                  eventTracker(
                    'viewDebriefingVessel',
                    row.survey_participation_debriefing?.vessel_ownership_access?.vessel_name,
                    row?.ga4react,
                  );
              }}
            />
          ) : (
            Dash
          )}
        </>
      );
    },
    order: 2,
    disableSortBy: true,
    minWidth: 180,
  },
  {
    Header: 'Rank',
    id: 'rank_value',
    accessor: function debriefingRankAccessor(row: DebriefingResult) {
      return <>{row?.survey_participation_debriefing['seafarer_rank.value'] ?? Dash}</>;
    },
    order: 3,
    disableSortBy: true,
    width: 100,
  },
  {
    Header: 'Submitted On',
    id: 'completed_at',
    accessor: function debriefingSubmittedAccessor(row: DebriefingResult) {
      return (
        <>
          {row?.completed_at
            ? dateAsString(stringAsDate(row?.completed_at))
            : dateAsString(stringAsDate(row?.updated_at))}
        </>
      );
    },
    order: 4,
    disableSortBy: true,
    minWidth: 80,
  },
  {
    Header: 'Status',
    id: 'status',
    order: 5,
    accessor: function statusAccessor(row: DebriefingResult) {
      return (
        <div className={`supt-appraisal-score font-weight-bold ${isSubmittedColor(row?.status)}`}>
          {row?.status === 'done' || row?.status === 'pending_office_comment'
            ? 'Submitted'
            : 'Not Submitted'}
        </div>
      );
    },
    disableSortBy: true,
    width: 120,
  },
  {
    Header: 'Report',
    id: 'debriefing-report',
    accessor: function reportAccessor(row: DebriefingResult, index: number) {
      return (
        <Link
          to={`/survey/debriefing/${row?.survey_participation_debriefing?.id ?? '0'}`}
          data-testid={`view-${index}-btn`}
        >
          <u>View</u>
        </Link>
      );
    },
    disableSortBy: true,
    width: 80,
  },
];

export const trainingRequirementsMenuItems = [
  {
    Header: 'Date Recommended',
    id: 'recommended_date',
    accessor: function createdAtAccessor(row: TrainingRequirementResult) {
      return (
        <div>
          {row?.recommended_date ? dateAsString(stringAsDate(row?.recommended_date)) : Dash}
        </div>
      );
    },
    order: 1,
    disableSortBy: true,
    minWidth: 80,
  },
  {
    Header: 'Vessel',
    id: 'vessel_name',
    accessor: function nameOfVesselAccessor(row: TrainingRequirementResult) {
      return row?.vessel_ownership_id === null ? (
        <div>{'N/A'}</div>
      ) : (
        <VesselNameLink
          ownershipId={row?.vessel_ownership_id}
          vesselName={row?.vessel_name}
          eventTracker={() => {
            row?.ga4react &&
              eventTracker('viewTrainingRequirementVessel', row?.vessel_name, row?.ga4react);
          }}
          className={'pl-0'}
        />
      );
    },
    order: 2,
    disableSortBy: true,
    minWidth: 80,
  },
  {
    Header: 'Deadline',
    id: 'deadline_date',
    order: 3,
    accessor: function deadlineDateAccessor(row: TrainingRequirementResult) {
      return (
        <div>{row?.deadline_date ? dateAsString(stringAsDate(row?.deadline_date)) : Dash}</div>
      );
    },
    disableSortBy: true,
    minWidth: 80,
  },
  {
    Header: 'Date Completed',
    id: 'completed_date',
    accessor: function completedDateAccessor(row: TrainingRequirementResult) {
      return (
        <div>{row?.completed_date ? dateAsString(stringAsDate(row?.completed_date)) : Dash}</div>
      );
    },
    order: 4,
    disableSortBy: true,
    minWidth: 80,
  },
  {
    Header: 'Created by',
    id: 'created_by_hash',
    accessor: function createdByAccessor(row: TrainingRequirementResult) {
      let label;
      if (row?.master_appraisal_id) {
        label = trainingReqCreatedByLabels.MASTER_APPRAISAL;
      } else if (row?.supt_appraisal_id) {
        label = trainingReqCreatedByLabels.SUPERINTENDENT;
      } else {
        label = trainingReqCreatedByLabels.OFFICE;
      }
      return <div>{label}</div>;
    },
    order: 5,
    disableSortBy: true,
    minWidth: 80,
  },
  {
    Header: 'Training Needs',
    id: 'training_needs',
    accessor: function trainingNeedsAccessor(row: TrainingRequirementResult) {
      return <div>{row?.training_needs ?? Dash}</div>;
    },
    order: 6,
    disableSortBy: true,
    minWidth: 80,
  },
  {
    Header: 'Training Imparted',
    id: 'training_imparted',
    accessor: function trainingImpartedAccessor(row: TrainingRequirementResult) {
      return <div>{row?.training_imparted ?? Dash}</div>;
    },
    order: 7,
    disableSortBy: true,
    minWidth: 80,
  },
  {
    Header: 'Supporting Document',
    id: 'supporting_doc',
    accessor: function supportDocAccessor(row: TrainingRequirementResult) {
      return <div>{routeToSupportingDocumentPage(row)}</div>;
    },
    order: 8,
    disableSortBy: true,
    minWidth: 80,
  },
  {
    Header: 'Supt. Report',
    id: 'supt_report',
    accessor: function suptReportAccessor(row: TrainingRequirementResult) {
      return <div>{routeToSuptReportDocumentPage(row)}</div>;
    },
    order: 9,
    disableSortBy: true,
    minWidth: 80,
  },
];
