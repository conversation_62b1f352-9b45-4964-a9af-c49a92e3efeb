import { getMasterAppaisalGradeColorClass } from '../../util/appraisal';
import React from 'react';
import { OverlayTrigger, Table, Tooltip } from 'react-bootstrap';
import { MASTER_APPRAISAL_GRADE_SCALE } from '../../constants/appraisal';
// eslint-disable-next-line no-unused-vars
import { MasterAppraisalGetResponse } from '../../types/masterAppraisal';

const MasterAppraisalIndividualGradeTable = ({
  data,
  tableClass,
  tableHeadClass,
  tableDataClass,
}: {
  data: MasterAppraisalGetResponse | null;
  tableClass?: string | null;
  tableHeadClass?: string | null;
  tableDataClass?: string | null;
}) => {
  const showMasterGrade = (grade: number, precision = 0) => {
    const renderTooltip = (props: any) => (
      <Tooltip id="appraisal-tooltip" {...props}>
        <table>
          <tbody>
            {MASTER_APPRAISAL_GRADE_SCALE.map((i) => {
              return (
                <tr key={i.id}>
                  <td className="text-left">{i.label}</td>
                  <td className="text-left pl-3">{i.range}</td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </Tooltip>
    );

    return (
      <div className="d-flex">
        <OverlayTrigger placement="bottom" delay={{ show: 250, hide: 400 }} overlay={renderTooltip}>
          <div
            className={`supt-appraisal-score ${getMasterAppaisalGradeColorClass(Number(grade))}`}
          >
            {grade.toFixed(precision)}
          </div>
        </OverlayTrigger>
      </div>
    );
  };
  const sumOfMasterAppraisalResult =
    data?.master_appraisal_result?.reduce((acc, value) => acc + value.result, 0) ?? 0;
  const averageGrade =
    sumOfMasterAppraisalResult / (data?.master_appraisal_result.length ?? 0) || 0;
  return (
    <div className="master-appraisal-table">
      <Table className={`${tableClass ?? ''}`}>
        <thead>
          <tr className="row px-3">
            <th className={`${tableHeadClass ?? ''} col`} colSpan={3}>
              Individual Grades
            </th>
          </tr>
        </thead>
        <tbody>
          <tr className="row px-3">
            <td className={`${tableDataClass ?? ''}`}>&nbsp;</td>
            <td className={`${tableDataClass ?? ''} col-7`}>
              <b>Item</b>
            </td>
            <td className={`${tableDataClass ?? ''} col`}>
              <b>Grade</b>
            </td>
          </tr>
          {data?.master_appraisal_result?.map((i, index) => {
            return (
              <tr key={i.id} className="row px-3">
                <td className={`${tableDataClass ?? ''}`}>
                  <b>{index + 1}</b>
                </td>
                <td className={`${tableDataClass ?? ''} col-7`}>
                  {i?.master_appraisal_question.value}
                </td>
                <td className={`${tableDataClass ?? ''} col`}>{showMasterGrade(i.result)}</td>
              </tr>
            );
          })}
          <tr className="row px-3">
            <td className={`${tableDataClass ?? ''}`}>&nbsp;</td>
            <td className={`${tableDataClass ?? ''} col-7`}>
              <b>Average</b>
            </td>
            <td className={`${tableDataClass ?? ''} col`}>
              {showMasterGrade(Math.floor(averageGrade))}
            </td>
          </tr>
        </tbody>
      </Table>
    </div>
  );
};

export default MasterAppraisalIndividualGradeTable;
