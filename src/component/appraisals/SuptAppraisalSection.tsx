import React from 'react';
import { Col, Row } from 'react-bootstrap';
import { showColorClass, renderToolTipText } from '../../util/appraisal';
import _ from 'lodash';
import { SuptAppraisalResult } from '../../types/suptAppraisal';

interface Props {
  suptAppraisalData: SuptAppraisalResult[];
}
const SuptAppraisalSection = ({ suptAppraisalData = [] }: Props) => {
  const averageScores = suptAppraisalData.map((item) => {
    return item.survey_participation_superintendent_appraisal.average_score;
  });
  const averageGrade = _.sum(averageScores) / averageScores.length;
  const bestGrade = Math.max(...averageScores);
  const worstGrade = Math.min(...averageScores);

  return (
    <Row>
      <Col>
        <table className="table table-hover">
          <thead className="details_page__table_head">
            <tr>
              <th>Superintendent Appraisals</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="details_page__row-name">No. of Appraisals</td>
              <td className="details_page__row-value">{suptAppraisalData.length}</td>
            </tr>
            <tr>
              <td className="details_page__row-name">Average Grade</td>
              <td
                className={`details_page__row-value font-weight-bold supt-appraisal-score ${showColorClass(
                  averageGrade,
                )}`}
              >
                {`${averageGrade} (${renderToolTipText(averageGrade)})`}{' '}
              </td>
            </tr>
            <tr>
              <td className="details_page__row-name">Best Grade</td>
              <td
                className={`details_page__row-value font-weight-bold supt-appraisal-score ${showColorClass(
                  bestGrade,
                )}`}
              >
                {`${bestGrade} (${renderToolTipText(bestGrade)})`}{' '}
              </td>
            </tr>
            <tr>
              <td className="details_page__row-name">Worst Grade</td>
              <td
                className={`details_page__row-value font-weight-bold supt-appraisal-score ${showColorClass(
                  worstGrade,
                )}`}
              >
                {`${worstGrade} (${renderToolTipText(worstGrade)})`}{' '}
              </td>
            </tr>
          </tbody>
        </table>
      </Col>
    </Row>
  );
};

export default SuptAppraisalSection;
