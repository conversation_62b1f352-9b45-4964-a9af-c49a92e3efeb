import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Modal } from 'react-bootstrap';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { TouchBackend } from 'react-dnd-touch-backend';
import update from 'immutability-helper';
import ImageList from './ImageList';

const isTouchDevice = () => {
  if ('ontouchstart' in window) {
    return true;
  }
  return false;
};
const backendForDND = isTouchDevice() ? TouchBackend : HTML5Backend;

export default function Lowerzone({ files, setFiles, photos, photoUrlMap, updateVessel }) {
  const [show, setShow] = useState(false);
  const [deleteIdx, setDeleteIdx] = useState(-1);
  const handleShow = (idx) => {
    setDeleteIdx(idx);
    setShow(true);
  };
  const handleClose = () => {
    setShow(false);
  };
  const handleConfirm = () => {
    let newList = [];
    let newFiles = [];
    if (deleteIdx >= 0) {
      newFiles = update(files, {
        $splice: [[deleteIdx, 1]],
      });
      newList = newFiles.map((f, idx) => {
        return {
          image_id: f.image_id,
          extension: f.extension,
          order: idx,
          caption: f.caption,
        };
      });
    }

    updateVessel(newList);
    setFiles(newFiles);
    setShow(false);
  };

  const moveImage = (dragIndex, hoverIndex) => {
    const draggedImage = files[dragIndex];
    setFiles(
      update(files, {
        $splice: [
          [dragIndex, 1],
          [hoverIndex, 0, draggedImage],
        ],
      }),
    );
  };

  const deleteImage = (e, index) => {
    e.stopPropagation();
    setFiles(
      update(files, {
        $splice: [[index, 1]],
      }),
    );
  };

  const onTextChange = (index, value) => {
    const arrayData = [...files];
    arrayData[index].caption = value;
    setFiles(arrayData);
  };

  useEffect(() => {
    setFiles(
      photos
        .sort((a, b) => a.order - b.order)
        .map((p) => ({
          id: p.image_id,
          image_id: p.image_id,
          extension: p.extension,
          preview: photoUrlMap[`${p.image_id}.${p.extension}`],
          caption: p.caption,
        })),
    );
  }, []);

  useEffect(
    () => () => {
      // trigger when close modal

      // Make sure to revoke the data uris to avoid memory leaks
      files.forEach((file) => URL.revokeObjectURL(file.preview));
    },
    [files],
  );

  const baseStyle = {
    backgroundColor: 'rgba(0, 145, 184, 0.05)',
    color: '#1f4a70',
    borderRadius: 4,
    padding: '20px 60px',
    outline: 'none',
  };

  return (
    <div style={baseStyle}>
      <Button
        onClick={() => {
          handleShow(-1);
        }}
        variant="outline-primary"
      >
        Delete all
      </Button>
      <div className="mt-2">To reorder photos, click and drag photos</div>
      <DndProvider backend={backendForDND}>
        <ImageList
          images={files}
          moveImage={moveImage}
          deleteImage={deleteImage}
          handleShow={handleShow}
          onTextChange={onTextChange}
        />
      </DndProvider>
      <Modal
        show={show}
        onHide={handleClose}
        aria-labelledby="contained-modal-title-vcenter"
        centered
      >
        <Modal.Header>
          <Modal.Title>Confirm Deleting Photos?</Modal.Title>
        </Modal.Header>
        <Modal.Body>Are you confirm to delete photos?</Modal.Body>
        <Modal.Footer>
          <Button variant="primary" onClick={handleClose}>
            Cancel
          </Button>
          <Button variant="secondary" onClick={handleConfirm}>
            Confirm
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
}
