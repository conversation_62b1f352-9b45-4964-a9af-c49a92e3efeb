import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON> } from 'react-bootstrap';
import axios from 'axios';
import 'react-18-image-lightbox/style.css';
import seafarerService from '../../service/seafarer-service';
import Dropzone from './Dropzone';
import Lowerzone from './Lowerzone';

function PhotoUploadModal({
  label,
  photos,
  photoUrlMap,
  onCustomInputChange,
  setPhotoUrlMap,
  onSubmitVesselOnly,
}) {
  const [dropzoneFiles, setDropzoneFiles] = useState([]);
  const [lowerzoneFiles, setLowerzoneFiles] = useState([]);
  const [show, setShow] = useState(false);
  const handleShow = () => setShow(true);
  const handleClose = () => {
    // clear dropzone
    setDropzoneFiles([]);

    setShow(false);
  };
  const handleConfirm = async () => {
    const newList = [...lowerzoneFiles, ...dropzoneFiles].map((f, idx) => {
      return {
        image_id: f.image_id,
        extension: f.extension,
        order: idx,
        caption: f.caption,
      };
    });

    updateVessel(newList);

    // upload to s3
    const requestUploadItems = dropzoneFiles.map((f) => ({
      id: f.id,
      key: f.key,
      contentType: f.type,
      file: f,
    }));
    try {
      const response = await seafarerService.requestUploadUrl(requestUploadItems);
      if (response.data.result) {
        await Promise.all(
          Object.entries(response.data.result).map(async (entry, index) => {
            const [key, s3uploadUrl] = entry;
            try {
              const formData = new FormData();
              const fileData = requestUploadItems[index].file;
              formData.append('file', fileData, fileData.fileName);

              const options = {
                headers: {
                  'Content-Type': requestUploadItems[index].contentType,
                },
              };

              await axios.put(s3uploadUrl, fileData, options);
              return {
                key,
                result: 'success',
                imageID: requestUploadItems[index].id,
              };
            } catch (error) {
              console.log(error);
              return {
                key,
                result: 'failed',
                error: err,
              };
            }
          }),
        );
      }
    } catch (error) {}

    // refresh photoUrlMap
    try {
      const response = await seafarerService.requestDownloadUrl(
        [...lowerzoneFiles, ...dropzoneFiles].map((f) => `${f.image_id}.${f.extension}`),
      );
      if (response.data.result) {
        setPhotoUrlMap(response.data.result);
      }
    } catch (error) {}

    // clear dropzone
    setDropzoneFiles([]);

    setShow(false);
  };

  const updateVessel = async (newList) => {
    // set vessel state
    onCustomInputChange('photos', newList);
    // save to db
    onSubmitVesselOnly();
  };

  return (
    <>
      <Button onClick={handleShow} variant="outline-primary">
        {label}
      </Button>
      <Modal
        show={show}
        onHide={handleClose}
        size="xl"
        aria-labelledby="contained-modal-title-vcenter"
        centered
      >
        <Modal.Header>
          <Modal.Title>Upload Photos</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Dropzone files={dropzoneFiles} setFiles={setDropzoneFiles} />
          {photos.length > 0 && (
            <>
              <Modal.Title className="mt-4 mb-2">Edit Uploaded Photos</Modal.Title>
              <Lowerzone
                files={lowerzoneFiles}
                setFiles={setLowerzoneFiles}
                photos={photos}
                photoUrlMap={photoUrlMap}
                updateVessel={updateVessel}
              />
            </>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="primary" onClick={handleClose}>
            Cancel
          </Button>
          <Button
            variant="secondary"
            onClick={handleConfirm}
            disabled={dropzoneFiles.length === 0 && lowerzoneFiles.length === 0}
          >
            Confirm
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
}

export default PhotoUploadModal;
