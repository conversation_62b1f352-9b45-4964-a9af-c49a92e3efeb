import React from 'react';
import { Link } from 'react-router-dom';
import './scss/error-list.scss';

const Result = ({ link, title, isActive, seafarerId, onValidateLink, error, testID }) => {
  const classStyle = isActive ? 'active' : '';
  const hasErrorClass = error && 'nav-bar-error-message';
  const onClick = (event) => {
    if (!onValidateLink(link)) {
      event.preventDefault();
    }
  };

  return (
    <Link
      to={seafarerId ? `/seafarer/${seafarerId}/add/${link}` : `/seafarer/add/${link}`}
      onClick={onClick}
      data-testid={testID}
      className={testID}
    >
      <div className={`tab_navigation__title ${classStyle} ${hasErrorClass}`}>{title}</div>
      <div className={`tab_navigation__progress_line ${classStyle}`}></div>
      <div className={`tab_navigation__progress_dot ${classStyle}`}></div>
    </Link>
  );
};

export default Result;
