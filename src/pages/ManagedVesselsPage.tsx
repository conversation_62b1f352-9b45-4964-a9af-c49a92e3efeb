/* eslint-disable react/prop-types */
import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useHistory } from 'react-router-dom';
import { useDebouncedCallback } from 'use-debounce';
import moment from 'moment';
import { Col, Row, Container, Alert } from 'react-bootstrap';
import { AxiosResponse } from 'axios';
import { toString, isEmpty, isInteger, keyBy } from 'lodash';
import qs from 'qs';
import SearchInput from '@src/component/common/Form/SearchInput';
import {
  cleanUpFilter,
  generateApiQueryFromFilterObject,
  generateFilterObjectFromApiQuery,
  processApiQuery,
} from '@src/component/CrewPlanner/DynamicFilter/helper';
import useMultiApiLoader from '@src/hooks/useMultiApiLoader';
import AccessHandlerWrapper from '../component/common/AccessHandlerWrapper';
import seafarerService from '../service/seafarer-service';
import vesselService, {
  getManagedVesselDropDownDataFromVessel,
  getVesselItinerary,
  queryVesselOwnership,
} from '../service/vessel-service';
import './scss/crew-list-page.scss';
import ManagedVesselsTable from '../component/ManagedVessels/ManagedVesselsTable';
import ManagedSeafarers, { SeafarerFilter } from '../component/ManagedVessels/ManagedSeafarers';
import { managedVesselsHeader } from '../component/ManagedVessels/TableHeader';
import { getTechGroupList } from '../service/keycloak-service';
import {
  ACTION_NOT_REQUIRED,
  ACTION_REQUIRED,
  MANAGED_VESSELS_ACTION_REQUIRED_STATUS,
  ListTabData,
  MANAGED_SEAFARERS,
  MANAGED_VESSELS,
} from '../constants/managedVessels';
import { CREW_PLANNING_STATUS_MAPPING, STATUS } from '../constants/crewPlanner';
import TabWrapper from '../component/common/TabWrapper';
import { getPageSort, storePageNumber } from '../util/local-storage-helper';
import httpService from '../service/http-service';
import { shipPartyType } from '../model/constants';
import { QueryParams, SortBy } from './CrewPlanner/interface';
import { useStatusState } from '@src/hooks/useStatusState';
import PlannerKPI from '@src/component/CrewPlanner/PlannerKPI';
import {
  compareKPIPayload,
  getSelelectRanks,
  getVesselIdsForQuery,
  initialPagination,
} from './CrewPlanner/utils';
import { qsDefaultOptions } from '@src/util';
import { prepareContractExpirySeafarers } from './CrewPlanner/model';
import TypeAbleDropDownSearchControl from '@src/component/advanced_search/TypeAbleDropDownSearchControl';
import { dateAsDash } from '@src/model/utils';
import { useAccess } from '@src/component/common/Access';

const dateFormat = 'yyyy-MM-DD';

const getDefaultSort = () => {
  return {
    [MANAGED_SEAFARERS]: [
      {
        id: 'seafarer_person:seafarer_status_history.expected_contract_end_date',
        desc: false,
      },
    ],
  };
};

const filterDataAPI = {
  seafarer: { call: seafarerService.getSeafarerDropDownData, args: ['?values=ranks'] },
  tech_group: {
    call: seafarerService.getTechGroupDropDown,
    responseHandler: (res) => {
      const techGroup = res.response?.tech_group.map((v) => ({ id: v, value: v }));
      return { tech_group: techGroup };
    },
  },
  vessel: {
    call: seafarerService.getDropDownDataFromVessel,
    args: ['owners  { id, value }'],
    responseHandler: (res) => res.data,
  },
};

const actionRequiredDropdown = () => {
  return MANAGED_VESSELS_ACTION_REQUIRED_STATUS.map((item, i) => ({
    id: i,
    value: item,
  }));
};

type Item = {
  id: number;
  value: string;
};

type DropdownData = {
  owners: Item[];
  vesselTypes: Item[];
  techgroups: Item[];
};
const shouldIncludeMissingRanks = (apiQueryObj: any, activeKey: string) => {
  const { keyword } = apiQueryObj;
  return !keyword & (activeKey === MANAGED_SEAFARERS);
};
export const getValue = (a, b, sortField) => {
  const fieldParts = sortField.split('.');
  let aValue = a;
  let bValue = b;
  for (const part of fieldParts) {
    aValue = aValue?.[part] ?? null;
    bValue = bValue?.[part] ?? null;
  }
  return [aValue, bValue];
};

export const handleSorting = (initSort, processedVesselList) => {
  const sortField = initSort[0].id;

  const isDescending = initSort[0].desc;

  return [...processedVesselList].sort((a, b) => {
    // get value for sorting in the object
    const [aValue, bValue] = getValue(a, b, sortField);

    // nulls sort after anything else
    if (aValue === null) {
      return 1;
    }
    if (bValue === null) {
      return -1;
    }

    // sort value
    if (aValue < bValue) {
      return isDescending ? 1 : -1;
    }
    if (aValue > bValue) {
      return isDescending ? -1 : 1;
    }

    return 0;
  });
};

export const updateVesselListWithItinerary = (vesselItinerary, vesselList) => {
  return vesselList.map((v) => {
    const itinerary = vesselItinerary.find((i) => v.id === i.vessel_ownership_id);
    if (itinerary) {
      return { ...v, itinerary };
    }
    return v;
  });
};

export const updateVesselListWithActionRequired = (
  planningActionResult: AxiosResponse<{ vesselId: number; actionRequired: boolean }[]>,
  vesselList: any[],
  searchVesselStatus: any,
) => {
  let processedVesseList = vesselList.map((v) => {
    const actionRequired = planningActionResult?.find?.((p) => p.vesselId === v?.vessel?.id);
    return { ...v, actionRequired: actionRequired?.actionRequired ?? false };
  });

  if (searchVesselStatus) {
    processedVesseList = processedVesseList?.filter((p) => {
      if (p.actionRequired && searchVesselStatus === ACTION_REQUIRED) {
        return true;
      }
      return !!(!p.actionRequired && searchVesselStatus === ACTION_NOT_REQUIRED);
    });
  }
  return processedVesseList;
};
const REMOVE_KEYS_FROM_COMPARE_FOR_KPI = ['limit', 'offset', 'orderBy'];
const ManagedVesselsPage = ({ ga4react }) => {
  const [loading, setLoading] = useState(true);
  const { roleConfig } = useAccess();
  const [loadingDropdown, setLoadingDropdown] = useState(false);
  const [loadingItinerary, setLoadingItinerary] = useState(false);
  const [loadingPlanningAction, setLoadingPlanningAction] = useState(false);
  const tableRef = useRef(null);
  const [activeKey, setActiveKey] = useState(MANAGED_VESSELS);
  const history = useHistory();
  const [seafarersTotalCount, setSeafarersTotalCount] = useState(0);

  const [columns, setColumns] = useState([]);
  const [initSort, setInitSort] = useState([]);
  const [errorStatusCode, setErrorStatusCode] = useState(null);
  const [errorMessage, setErrorMessage] = useState('');

  const [vesselList, setVesselList] = useState([]);
  const [processedVesselList, setProcessedVesselList] = useState([]);

  const [dropdownFilterData, setDropdownFilterData] = useState<DropdownData | null>(null);
  const [dropDownData, setDropDownData] = useState({});

  const [searchName, setSearchName] = useState<string | null>('');
  const [searchType, setSearchType] = useState<string | null>(null);
  const [searchOwner, setSearchOwner] = useState<string | null>(null);
  const [searchTechGroup, setSearchTechGroup] = useState<string | null>(null);
  const [searchVesselStatus, setSearchVesselStatus] = useState(null);

  const [planningAction, setPlanningAction] = useState(null);
  const [intineraryData, setIntineraryData] = useState(null);
  const hasRoleAccess = roleConfig?.seafarer?.view?.crewPlannerSeafarer;
  const hasEditAccess = roleConfig?.seafarer?.edit?.crewPlannerVessel;
  const [selectedFilters, setSelectedFilters] = useState({});
  const [filteredSeafarers, setFilteredSeafarers] = useState([]);
  const [apiQuery, setApiQuery] = useState('');
  const [vesselOwnershipData, setVesselOwnershipData] = React.useState([]);
  const [searchedKeyword, setSearchedKeyword] = useState('');
  const [sortBy, setSortBy] = useState(getDefaultSort());
  const [isPageViewInvoked, setIsPageViewInvoked] = useState(false);
  const [dueIn30Days, setDueIn30Days] = useStatusState(0, 'Due in 30 Days (Not Planned)', true);
  const [overdueSeafarers, setOverdueSeafarers] = useStatusState(0, 'Seafarers Overdue', true);
  const [missingRanks, setMissingRanks] = useStatusState(0, 'Missing Ranks', true);
  const [isPodManager, setIsPodManager] = useState(false);
  const [podManagerVesselList, setPodManagerVesselList] = useState([]);
  const [isInitialRender, setIsInitialRender] = useState(true);
  const [searchNotClicked, setSearchNotClicked] = useState(false);
  const [kpiQueryHistory, setKPIQueryHistory] = useState({
    due_in_30_days: {},
    overdue_seafarer: {},
    missing_ranks: {},
  });
  const kpiQueryHistoryHandler = (key, val) => {
    setKPIQueryHistory((prev) => ({ ...prev, [key]: val }));
  };

  const setSortByWrapper = (key, value) => {
    setSortBy((prev) => ({ ...prev, [key]: value }));
  };
  const cleanupSeafarerTable = useCallback(() => {
    setFilteredSeafarers([]);
    setSeafarersTotalCount(0);

    setPagination(initialPagination);
  }, [activeKey, pagination]);

  const [pagination, setPagination] = React.useState<{
    pageSize?: number;
    pageIndex?: number;
  } | null>(initialPagination);
  const memoizedPagination = React.useMemo(
    () => ({ ...(pagination ?? {}), sortBy: sortBy[activeKey] }),
    [JSON.stringify({ pagination, sortBy }), activeKey],
  );

  const onFilterChange = (name, value) => {
    setSelectedFilters((prev) => ({ ...prev, [name]: value }));
  };

  const apiQueryHandler = (query) => {
    storePageNumber(activeKey, 0);
    setApiQuery(query);
  };

  const clearSearchedKeyword = () => {
    setSearchedKeyword('');
    setSearchName('');
  };
  const handlePagination = useCallback(
    ({ fetchMore, sortBy }: { fetchMore: boolean; sortBy: SortBy }) => {
      if (!fetchMore) cleanupSeafarerTable();
      setPagination((prev) => {
        return {
          ...prev,
          ...initialPagination,
          pageIndex: fetchMore ? prev?.pageIndex + 1 : 0,
        };
      });
      setSortByWrapper(activeKey, sortBy);
    },
    [activeKey, cleanupSeafarerTable],
  );

  const ga4EventTrigger = (action: string, category: string, label: string) => {
    try {
      ga4react?.event(action, toString(label), category, false);
    } catch (error) {
      console.log(error);
    }
  };

  const eventTracker = (type: string, value?: string | number) => {
    switch (type) {
      case 'searchVessel':
        ga4EventTrigger('Search Vessel', 'POD Manager - Search ', toString(value));
        break;
      case 'filterVessel':
        ga4EventTrigger('Filter Vessel', 'POD Manager - Filter', toString(value));
        break;
      case 'viewDetails':
        ga4EventTrigger('Clicks View Detail', 'POD Manager - Vessel List', toString(value));
        break;
      case 'clickingVesselName':
        ga4EventTrigger('Clicks Vessel Name', 'POD Manager - Vessel List', toString(value));
        break;
      case 'switchSeafarerTab':
        ga4EventTrigger('Switch to the Managed Seafarer Tab', 'POD Manager - Vessel List', 'Click');
        break;
      case 'switchVesselTab':
        ga4EventTrigger('Switches the tab', 'POD Manager - Vessel List', 'Click');
        break;
      case 'vesselSearch':
        ga4EventTrigger('Vessel Search', 'POD Manager - Managed Seafarers', '');
        break;
      default:
        break;
    }
  };

  const loadDropDownData = async () => {
    const [vesselDropdown, techgroupList] = await Promise.all([
      getManagedVesselDropDownDataFromVessel(),
      getTechGroupList(),
    ]);

    setDropdownFilterData({
      owners: vesselDropdown.data.owners,
      vesselTypes: vesselDropdown.data.vesselTypes,
      techgroups: techgroupList,
    });
  };

  useEffect(() => {
    setLoadingDropdown(true);
    loadDropDownData();
    setLoadingDropdown(false);
  }, []);

  const getValueById = (searchVal, array) => {
    return array?.filter((arr) => arr?.id === searchVal?.target?.value)?.[0]?.value;
  };

  const queryVesselListData = useDebouncedCallback(async (shouldResetKpis = false) => {
    try {
      setLoading(true);
      setErrorStatusCode(null);
      setErrorMessage('');

      const columnList = managedVesselsHeader(eventTracker);
      setColumns(columnList);
      const searchText = searchName?.trim()?.toLowerCase();
      const searchNameString = searchText ? `&name=${searchText}` : '';
      const searchTypeString = searchType?.target?.value
        ? `&vessel_type=${getValueById(searchType, dropdownFilterData?.vesselTypes ?? [])}`
        : '';
      const searchOwnerString = searchOwner?.target?.value
        ? `&owner=${getValueById(searchOwner, dropdownFilterData?.owners ?? [])}`
        : '';
      const searchTechGroupString = searchTechGroup?.target?.value
        ? `&fleet_staff_tech_group=${getValueById(
            searchTechGroup,
            dropdownFilterData?.techgroups ?? [],
          )}`
        : '';

      setKpisLoading();
      if (isInitialRender || shouldResetKpis) {
        setIsInitialRender(false);
        const paramV1 =
          'order=created_at+desc&status=active&f=name&f=id&f=vessel.id&flatten=true&f=owner.value&f=owner.id';
        const paramV2 = '&status=active&status=pending_handover&flatten=true&fmlFilter=podManager';
        const [vesselV2Response, vesselV1Response] = await Promise.all([
          vesselService.getVesselOwnershipV2(paramV2),
          queryVesselOwnership(paramV1),
        ]);
        setVesselOwnershipData(vesselV1Response);

        if (vesselV2Response.data.results.length > 0) {
          setProcessedVesselList(vesselV2Response.data.results);
          setIsPodManager(true);
          setPodManagerVesselList(vesselV2Response.data.results);
          loadActionRequired(vesselV2Response.data.results);
        } else {
          loadActionRequired(vesselV1Response);
        }
        setVesselList(vesselV2Response.data.results);
        return;
      }
      if (!searchNameString && !searchTypeString && !searchOwnerString && !searchTechGroupString) {
        // no params selected. do not call vessel API
        setVesselList([]);
        setProcessedVesselList([]);
        loadActionRequired([]);
        setLoading(false);
        return;
      }

      // reset sorting when re-querying
      setInitSort([]);

      let param = `&status=active&status=pending_handover${searchNameString}${searchTypeString}${searchOwnerString}${searchTechGroupString}&flatten=true`;
      if (isPodManager) param += '&fmlFilter=podManager';
      const [vesselListResponse] = await Promise.all([vesselService.getVesselOwnershipV2(param)]);
      if (vesselListResponse) {
        setVesselList(vesselListResponse.data.results);
        setProcessedVesselList(vesselListResponse.data.results);
        setPodManagerVesselList(vesselListResponse.data.results);
        setLoading(false);
        loadActionRequired(vesselListResponse?.data?.results);
      }
    } catch (error) {
      setErrorStatusCode(error?.response?.status);
      setErrorMessage(`Error when loading vessel list, error: ${error?.message}`);
      setLoading(false);
      console.error(`Error when loading vessel list, error: ${error}`);
    }
  }, 500);
  useEffect(() => {
    if (
      !searchName &&
      !searchType?.target?.value &&
      !searchOwner?.target?.value &&
      !searchTechGroup?.target?.value
    ) {
      queryVesselListData(true);
    } else {
      queryVesselListData();
    }
  }, [searchName, searchType, searchOwner, searchTechGroup]);

  const getDateStringForItinerary = () => {
    const oneYearLater = new Date();
    oneYearLater.setFullYear(oneYearLater.getFullYear() + 1);
    const today = new Date();

    return `${moment(today).format(dateFormat)}, ${moment(oneYearLater).format(dateFormat)}`;
  };

  const setKpisLoading = (
    handler = [setDueIn30Days, setOverdueSeafarers, setMissingRanks],
    value = true,
  ) => {
    handler.forEach((fn) => fn((prevState) => ({ ...prevState, isLoading: value })));
  };

  const loadActionRequired = async (overridingVesselsList = []) => {
    try {
      let areFiltersInUse = false;
      if (
        searchName ||
        searchType?.target?.value ||
        searchOwner?.target?.value ||
        searchTechGroup?.target?.value ||
        searchVesselStatus?.target?.value !== null
      ) {
        areFiltersInUse = true;
      }
      if (areFiltersInUse && !overridingVesselsList?.length) {
        setDueIn30Days((prevState) => ({ ...prevState, number: 0, isLoading: false }));
        setOverdueSeafarers((prevState) => ({ ...prevState, number: 0, isLoading: false }));
        setMissingRanks((prevState) => ({ ...prevState, number: 0, isLoading: false }));
        setLoadingPlanningAction(false);
        return;
      }
      if (!overridingVesselsList?.length && !vesselList?.length) {
        setLoadingPlanningAction(false);
        return;
      }
      setErrorStatusCode(null);
      setErrorMessage('');
      setPlanningAction(null);
      if (!dueIn30Days?.number) {
        setKpisLoading();
      }
      let vessels;

      if (overridingVesselsList?.length) {
        vessels = overridingVesselsList;
      } else if (isPodManager) {
        vessels = podManagerVesselList;
      } else {
        vessels = vesselList;
      }
      const missingRanksPayload = {
        vessel_ownership_id: vessels?.map((vessel) => vessel?.id).join(),
      };
      seafarerService.getMissingRanksCount(missingRanksPayload, false).then((totalCount) => {
        setMissingRanks((prevState) => ({
          ...prevState,
          number: totalCount ?? 0,
          isLoading: false,
        }));
      });
      const getPlanningActionPayload = {
        vessel_ids: vessels.map((i) => i?.vessel?.id || i?.vessel_id),
      };
      const getPlanningActionResult = await seafarerService.getPlanningAction(
        getPlanningActionPayload,
      );

      if (!getPlanningActionResult) {
        setPlanningAction(null);
        setLoadingPlanningAction(false);
        return;
      }

      setProcessedVesselList((prevState) =>
        updateVesselListWithActionRequired(
          getPlanningActionResult?.data?.list,
          prevState,
          getValueById(searchVesselStatus, actionRequiredDropdown()),
        ),
      );
      setPlanningAction(getPlanningActionResult?.data?.list);
      setLoadingPlanningAction(false);
      setDueIn30Days((prevState) => ({
        ...prevState,
        number: getPlanningActionResult?.data?.due_count,
        isLoading: false,
      }));
      setOverdueSeafarers((prevState) => ({
        ...prevState,
        number: getPlanningActionResult?.data?.overdue_count,
        isLoading: false,
      }));
    } catch (ex) {
      setErrorStatusCode(ex?.response?.status);
      setErrorMessage(`Error when loading contract expiry, error: ${ex?.message}`);
      console.log('error when loading contract expiry, ', ex.message);
      setLoadingPlanningAction(false);
      setPlanningAction(null);
      setDueIn30Days((prevState) => ({ ...prevState, number: null }));
      setOverdueSeafarers((prevState) => ({ ...prevState, number: null }));
      setMissingRanks((prevState) => ({ ...prevState, number: null }));
    }
  };

  useEffect(() => {
    setLoadingItinerary(true);
    setLoadingPlanningAction(true);

    const loadItinerary = async () => {
      try {
        if (!vesselList?.length) {
          setLoadingItinerary(false);
          return;
        }
        setErrorStatusCode(null);
        setErrorMessage('');
        setIntineraryData(null);

        const queryString = getDateStringForItinerary();

        const itinerary = await Promise.all(
          vesselList.map((v) => getVesselItinerary(v.id, queryString, 1)),
        );

        const itineraryList: any[] = [];
        itinerary.forEach((i) => {
          if (i.data?.results?.[0]) {
            itineraryList.push(i.data?.results?.[0]);
          }
        });

        setProcessedVesselList((prevState) =>
          updateVesselListWithItinerary(itineraryList, prevState),
        );
        setIntineraryData(itineraryList);
        setLoadingItinerary(false);
      } catch (ex) {
        setErrorStatusCode(ex?.response?.status);
        setErrorMessage(`Error when loading itinerary, error: ${ex?.message}`);
        console.log('error when loading itinerary, ', ex.message);
        setLoadingItinerary(false);
        setIntineraryData(null);
      }
    };

    loadItinerary();
    if (isPodManager) {
      loadActionRequired();
    } else {
      setLoadingPlanningAction(false);
      setProcessedVesselList((prevState) =>
        updateVesselListWithActionRequired(
          planningAction,
          prevState,
          getValueById(searchVesselStatus, actionRequiredDropdown()),
        ),
      );
    }
  }, [vesselList]);

  useEffect(() => {
    setLoadingPlanningAction(true);

    const injectActionRequired = async () => {
      try {
        if (!vesselList?.length) {
          setLoadingPlanningAction(false);
          return;
        }

        let updatedVesselList = updateVesselListWithItinerary(intineraryData, vesselList);
        updatedVesselList = updateVesselListWithActionRequired(
          planningAction,
          updatedVesselList,
          getValueById(searchVesselStatus, actionRequiredDropdown()),
        );
        setProcessedVesselList(updatedVesselList);
        loadActionRequired(updatedVesselList);
        setLoadingPlanningAction(false);
      } catch (ex) {
        setErrorStatusCode(ex?.response?.status);
        setErrorMessage(`Error when loading contract expiry, error: ${ex?.message}`);
        console.log('error when loading contract expiry, ', ex.message);
        setLoadingPlanningAction(false);
      }
    };

    injectActionRequired();
  }, [searchVesselStatus]);

  useEffect(() => {
    if (!initSort?.[0]) {
      return;
    }
    const sortedArray = handleSorting(initSort, processedVesselList);
    setProcessedVesselList(sortedArray);
  }, [initSort]);

  const handleTypeDropdownChange = (selectedValue: string) => {
    if (searchType?.target?.value !== selectedValue?.target?.value) {
      eventTracker('filterVessel', selectedValue);
      setSearchType(selectedValue ?? null);
    }
  };

  const handleOwnerDropdownChange = (selectedValue: string) => {
    if (searchOwner?.target?.value !== selectedValue?.target?.value) {
      eventTracker('filterVessel', selectedValue);
      setSearchOwner(selectedValue);
    }
  };

  const handleTechGroupDropdownChange = (selectedValue: string) => {
    if (searchTechGroup?.target?.value !== selectedValue?.target?.value) {
      eventTracker('filterVessel', selectedValue);
      setSearchTechGroup(selectedValue);
    }
  };

  const handleStatusDropdownChange = (selectedValue: string) => {
    if (searchVesselStatus?.target?.value !== selectedValue?.target?.value) {
      eventTracker('filterVessel', selectedValue);
      setSearchVesselStatus(selectedValue ?? null);
    }
  };

  const handleKeywordChange = (value) => {
    eventTracker('searchVessel', value);
    setSearchName(value);
  };

  const handleTabSelect = (key) => {
    if (key === MANAGED_SEAFARERS) {
      eventTracker('switchSeafarerTab');
    }
    if (key === activeKey) {
      if (apiQuery) {
        // clear the current query and refresh page
        cleanupSeafarerTable();
        handleTabSwitch(key);
      }
      return;
    }
    cleanupSeafarerTable();
    handleTabSwitch(key);
    setIsPageViewInvoked(false);
  };

  const queryListData = useDebouncedCallback(async (sortPaginateData, activeKey) => {
    try {
      setLoading(true);
      setSearchNotClicked(false);
      const requestPromises = [];
      const allowedVesselOwnershipIds = isPodManager ? podManagerVesselList.map((i) => i.id) : null;
      const ownershipDataById = keyBy(vesselOwnershipData, 'id');
      const apiQueryObj = qs.parse(apiQuery);
      const currentFilters = selectedFilters[activeKey] ?? {};
      const isManningAgent = roleConfig.shipPartyType === shipPartyType.MANNING_AGENT;
      const { pageSize, pageIndex, sortBy = [], offset } = sortPaginateData;
      const ownerList = dataStates?.vessel?.owners ?? [];
      const ownersMap = new Map(ownerList?.map((owner) => [owner.id, owner?.value]) ?? []);
      const selectedRanks = getSelelectRanks(apiQueryObj);
      const includeMissingRanks =
        pageIndex === 0 && shouldIncludeMissingRanks(apiQueryObj, activeKey);
      const queryObj: QueryParams = {
        keyword: searchedKeyword || null,
        'seafarer_person:seafarer_status_history.vessel_id': isPodManager
          ? podManagerVesselList.map((i) => i.vessel.id).join()
          : null,
        ...apiQueryObj,
        'seafarer_reporting_office.ship_party_id': isManningAgent ? roleConfig.shipPartyId : null,
        limit: pageSize,
        offset: pageIndex ?? offset ?? 0,
        orderBy: sortBy.map(({ id, desc }: SortBy) => `${id} ${desc ? 'desc' : 'asc'}`),
      };
      requestPromises.push(
        seafarerService.getContractExpirySeafarers(qs.stringify(queryObj, qsDefaultOptions)),
      );
      if (activeKey === MANAGED_SEAFARERS) {
        const today = dateAsDash(new Date());
        const monthAfterToday = dateAsDash(moment().add(1, 'month'));
        const due30DaysKpiQuery = {
          ...queryObj,
          'seafarer_person:seafarer_status_history.expected_contract_end_date': `${today},${monthAfterToday}`,
          crew_planning_status: 'unplanned',
        };
        delete due30DaysKpiQuery.keyword;
        if (
          !compareKPIPayload(
            due30DaysKpiQuery,
            kpiQueryHistory.due_in_30_days,
            REMOVE_KEYS_FROM_COMPARE_FOR_KPI,
          )
        ) {
          kpiQueryHistoryHandler('due_in_30_days', due30DaysKpiQuery);
          setKpisLoading([setDueIn30Days], true);
          seafarerService
            .getContractExpirySeafarers(qs.stringify(due30DaysKpiQuery, qsDefaultOptions), false)
            .then((res) => {
              setDueIn30Days((prevState) => ({
                ...prevState,
                number: res?.data?.pagination?.totalCount,
                isLoading: false,
              }));
            })
            .catch(() => {});
        }
        const overdueKpiQuery = {
          ...queryObj,
          'seafarer_person:seafarer_status_history.expected_contract_end_date': `,${today}`,
          crew_planning_status: 'unplanned',
        };
        delete overdueKpiQuery?.keyword;
        if (
          !compareKPIPayload(
            overdueKpiQuery,
            kpiQueryHistory.overdue_seafarer,
            REMOVE_KEYS_FROM_COMPARE_FOR_KPI,
          )
        ) {
          setKpisLoading([setOverdueSeafarers], true);
          kpiQueryHistoryHandler('overdue_seafarer', overdueKpiQuery);
          seafarerService
            .getContractExpirySeafarers(qs.stringify(overdueKpiQuery, qsDefaultOptions), false)
            .then((res) => {
              setOverdueSeafarers((prevState) => ({
                ...prevState,
                number: res?.data?.pagination?.totalCount,
                isLoading: false,
              }));
            })
            .catch(() => {});
        }
      }
      if (includeMissingRanks) {
        const vesselOwnershipIdFromFilter = getVesselIdsForQuery(apiQueryObj, dropDownData.vessels);
        const ownerNames = currentFilters?.owner?.map((id) => ownersMap.get(id))?.filter(Boolean);
        const missingRanksParams = {
          owner_name: ownerNames,
          vessel_ownership_id: vesselOwnershipIdFromFilter ?? allowedVesselOwnershipIds?.join(),
          crew_planning_status: apiQueryObj.crew_planning_status?.split(','),
          rank: selectedRanks?.join('|'),
          tech_group: queryObj['seafarer_person:seafarer_status_history.vessel_tech_group'],
          vessel_type: queryObj.vessel_type,
        };
        const missingRankKPIQuery = {
          ...missingRanksParams,
        };
        if (
          activeKey === MANAGED_SEAFARERS &&
          !compareKPIPayload(
            missingRankKPIQuery,
            kpiQueryHistory.missing_ranks,
            REMOVE_KEYS_FROM_COMPARE_FOR_KPI,
          )
        ) {
          kpiQueryHistoryHandler('missing_ranks', missingRankKPIQuery);
          setKpisLoading([setMissingRanks], true);
        }
        requestPromises.push(seafarerService.getMissingRanks(missingRanksParams));
        requestPromises.push(
          seafarerService.getMissingRanksCount({
            ...missingRanksParams,
            crew_planning_status: ['unplanned'],
          }),
        );
        const ranks = selectedFilters[activeKey]?.rank;
        const additionalRequestParams = {
          rank_id: ranks?.join(','),
          crew_planning_status: apiQueryObj.crew_planning_status,
          vessel_ownership_id: vesselOwnershipIdFromFilter ?? allowedVesselOwnershipIds?.join(),
          tech_group: queryObj['seafarer_person:seafarer_status_history.vessel_tech_group'],
          vessel_type: queryObj.vessel_type,
        };
        requestPromises.push(seafarerService.getAdditionalCrewRequest(additionalRequestParams));
      } else if (pageIndex === 0) {
        setMissingRanks((prevState) => ({ ...prevState, number: 0 }));
      }
      const [
        contractExpiryResp,
        missingPersonnelsResp,
        missingPersonnelsCountResp,
        additionalRequestResp,
      ] = await Promise.all(requestPromises);
      const missingPersonnelsData =
        missingPersonnelsResp?.data?.results?.map((s) => ({ ...s, isMissingPersonnel: true })) ??
        [];
      const additionalRequestData =
        additionalRequestResp?.data?.results?.map((s) => ({ ...s, isAdditionalRequest: true })) ??
        [];
      const contractExpiryData = contractExpiryResp?.data?.results ?? [];
      const results = prepareContractExpirySeafarers(
        [...missingPersonnelsData, ...additionalRequestData, ...contractExpiryData],
        ownershipDataById,
      );
      if (includeMissingRanks) {
        setMissingRanks((prevState) => ({
          ...prevState,
          number: missingPersonnelsCountResp,
          isLoading: false,
        }));
      }
      const { totalCount } = contractExpiryResp.data?.pagination ?? {};
      setFilteredSeafarers((prev) => [...prev, ...results]);
      if (includeMissingRanks) {
        setSeafarersTotalCount(
          totalCount + missingPersonnelsData.length + additionalRequestData.length,
        );
      }
      setLoading(false);
    } catch (error) {
      if (httpService.axios.isCancel(error)) {
        return;
      }
      console.log('error:', error);
      setLoading(false);
    }
  }, 100);

  const handleSearch = () => {
    if (
      !vesselOwnershipData?.length ||
      !memoizedPagination?.pageSize ||
      !isInteger(memoizedPagination?.pageIndex) ||
      Object.values(loadingStates).some((v) => v)
    )
      return;

    cleanupSeafarerTable();
    queryListData({ ...pagination, sortBy: sortBy?.[activeKey] ?? [] }, activeKey);
  };

  const onCrewPlanningFilterChange = (selectedStatus: string) => {
    const isClubbedOrUnclubbed = [STATUS.club_confirmed, STATUS.clubbed, STATUS.unclubbed].includes(
      CREW_PLANNING_STATUS_MAPPING[selectedStatus][0],
    );
    const urlFilter = window.location.search;
    const urlFilterValues = generateFilterObjectFromApiQuery(
      urlFilter,
      SeafarerFilter({ isClubbedOrUnclubbed }),
    );
    const existingFilters = cleanUpFilter(urlFilterValues);
    const newFilters = {
      ...existingFilters,
      crew_planning_status: CREW_PLANNING_STATUS_MAPPING[selectedStatus],
    };
    onFilterChange(activeKey, newFilters);
    setApiQuery(
      processApiQuery({
        lookupData: dropDownData,
        values: newFilters,
        filters: SeafarerFilter({ isClubbedOrUnclubbed }),
      }),
    );
    window.history.replaceState(
      {},
      null,
      `${history.location.pathname}?${generateApiQueryFromFilterObject(
        newFilters,
        SeafarerFilter({ isClubbedOrUnclubbed }),
      )}`,
    );
    handleSearch();
  };

  const { dataStates, loadingStates } = useMultiApiLoader(filterDataAPI);

  useEffect(() => {
    if (!Object.values(loadingStates).some((v) => v) && !isEmpty(vesselOwnershipData)) {
      const tempDropDownData = { ...dataStates };
      if (dataStates?.vessel?.owners) {
        tempDropDownData.vessel.owners = dataStates.vessel.owners.map((v) => {
          return {
            ...v,
            ownership_ids: vesselOwnershipData
              .filter((item) => item.owner?.id === v.id)
              .map((item) => item?.id),
          };
        });
      }
      const vesselSearchDropDownData = vesselOwnershipData.map((v) => ({
        id: v.vessel_id,
        value: v.name,
        ownership_id: v.id,
      }));
      tempDropDownData.vessels = vesselSearchDropDownData;
      setDropDownData(tempDropDownData);
    }
  }, [loadingStates, dataStates, vesselOwnershipData]);

  useEffect(() => {
    if (!isPageViewInvoked) {
      try {
        ga4react?.pageview(history?.location.pathname, '', 'Seafarer List');
        setIsPageViewInvoked(true);
      } catch (e) {
        console.log(e);
      }
    }
  }, [isPageViewInvoked, history, ga4react]);

  useEffect(() => {
    clearSearchedKeyword();
    cleanupSeafarerTable();
    let defaultSort = getPageSort(activeKey);
    if (!defaultSort?.length) {
      defaultSort = getDefaultSort()[activeKey];
    }
    setSortByWrapper(activeKey, defaultSort);
    if (activeKey === MANAGED_VESSELS) {
      queryVesselListData(true);
    }
  }, [activeKey]);

  const navigateTab = () => {
    history.push('/seafarer/crew-planner/managed-vessel');
  };

  const handleTabSwitch = (tab) => {
    setPagination(initialPagination);
    tab === MANAGED_VESSELS && navigateTab();
    setActiveKey(tab);
  };
  useEffect(() => {
    if (!vesselOwnershipData?.length) return;
    cleanupSeafarerTable();
  }, [vesselOwnershipData, activeKey]);

  useEffect(() => {
    // Fetch data for listing and searching
    if (
      !vesselOwnershipData?.length ||
      !memoizedPagination?.pageSize ||
      !isInteger(memoizedPagination?.pageIndex)
    )
      return;
    queryListData({ ...pagination, sortBy: sortBy?.[activeKey] ?? [] }, activeKey);
  }, [searchedKeyword, vesselOwnershipData, memoizedPagination, activeKey]);

  return (
    <AccessHandlerWrapper
      hasRoleAccess={errorStatusCode === 403 ? false : hasRoleAccess || hasEditAccess}
    >
      {errorMessage && <Alert variant="danger">{errorMessage}</Alert>}
      <div className="crew-planner">
        <Container data-testid="managed-vessels-table">
          <Row className="no-print">
            <Col className="crew-planner-tab">
              <TabWrapper
                isFixedWidth
                activeKey={activeKey}
                handleTabSelect={handleTabSelect}
                data={ListTabData}
              />
            </Col>
          </Row>
          {activeKey === MANAGED_VESSELS && (
            <>
              <Row className="managed-vessels-vessel-row" style={{ gap: 20 }}>
                <Col className="p-0" md={2}>
                  <SearchInput
                    id="managed-vessels-search-name"
                    data-testid="managed-vessels-search-name"
                    name="keyword"
                    value={searchName}
                    placeholder="Search by Vessel Name"
                    onChange={(e) => handleKeywordChange(e.target.value)}
                  />
                </Col>
                <TypeAbleDropDownSearchControl
                  selectedValue={searchType?.target?.value}
                  onInputChange={handleTypeDropdownChange}
                  dropDownValues={dropdownFilterData?.vesselTypes ?? []}
                  name="Select Vessel Category"
                  labelKey="value"
                  loading={loadingDropdown}
                  testID="dropdown-select-vessel-category"
                  showAllOptionsAfterSelection
                />
                <TypeAbleDropDownSearchControl
                  selectedValue={searchOwner?.target?.value}
                  onInputChange={handleOwnerDropdownChange}
                  dropDownValues={dropdownFilterData?.owners ?? []}
                  placeholder="Select Owner"
                  labelKey="value"
                  loading={loadingDropdown}
                  testID="dropdown-select-owner"
                  showAllOptionsAfterSelection
                />
                <TypeAbleDropDownSearchControl
                  selectedValue={searchTechGroup?.target?.value}
                  onInputChange={handleTechGroupDropdownChange}
                  dropDownValues={dropdownFilterData?.techgroups ?? []}
                  placeholder="Select Tech Group"
                  labelKey="value"
                  loading={loadingDropdown}
                  testID="dropdown-select-tech-group"
                  showAllOptionsAfterSelection
                />
                <TypeAbleDropDownSearchControl
                  selectedValue={searchVesselStatus?.target?.value}
                  onInputChange={handleStatusDropdownChange}
                  dropDownValues={actionRequiredDropdown() ?? []}
                  placeholder="Select Vessel Status"
                  labelKey="value"
                  loading={loadingDropdown}
                  testID="dropdown-select-vessel-status"
                  showAllOptionsAfterSelection
                />
              </Row>
              <Container>
                <PlannerKPI kpiData={[dueIn30Days, missingRanks, overdueSeafarers]} />
              </Container>
              <ManagedVesselsTable
                columns={columns}
                loading={loading || loadingItinerary || loadingPlanningAction}
                tableRef={tableRef}
                data={processedVesselList}
                init_sort={initSort}
                setInitSort={setInitSort}
                eventTracker={eventTracker}
              />
            </>
          )}
          {activeKey === MANAGED_SEAFARERS && (
            <ManagedSeafarers
              KPI={<PlannerKPI kpiData={[dueIn30Days, missingRanks, overdueSeafarers]} />}
              selectedFilters={selectedFilters[activeKey]}
              onFilterChange={(v) => onFilterChange(activeKey, v)}
              setApiQuery={apiQueryHandler}
              hasMoreData={filteredSeafarers.length < seafarersTotalCount}
              fetchData={handlePagination}
              data={filteredSeafarers}
              eventTracker={eventTracker}
              loading={loading}
              filterData={dropDownData}
              onCrewPlanningFilterChange={onCrewPlanningFilterChange}
              init_sort={memoizedPagination.sortBy}
              handleSearch={handleSearch}
              searchNotClicked={searchNotClicked}
            />
          )}
        </Container>
      </div>
    </AccessHandlerWrapper>
  );
};

export default ManagedVesselsPage;
