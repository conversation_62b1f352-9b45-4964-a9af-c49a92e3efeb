import React, { useState, useEffect, useRef } from 'react';
import { Container } from 'react-bootstrap';
import AutoDismissibleAlert from '@src/component/common/AutoDismissibleAlert';
import Spinner from '@src/component/common/Spinner';
import AccessHandlerWrapper from '../component/common/AccessHandlerWrapper';
import seafarerService from '../service/seafarer-service';
import vesselService from '../service/vessel-service';
import CrewPlanner from './CrewPlanner';
import { useAccess } from '@src/component/common/Access';

function CrewPlannerPage({ ga4react, keycloak }) {
  const { roleConfig } = useAccess();
  const [alertMessage, setAlertMessage] = useState<{
    message: string;
    variant?: string;
  }>({
    message: '',
  });
  const [timeoutError, setTimeoutError] = useState<{
    message: string;
    variant?: string;
  }>({
    message: '',
  });
  const alertRef = useRef<AlertRef>(null);
  const timeoutRef = useRef<AlertRef>(null);
  const [isLoadingPlanner, setIsLoadingPlanner] = useState(true);
  const [plannerRanks, setPlannerRanks] = useState(null);
  const [vesselTypes, setVesselTypes] = useState(null);
  const hasRoleAccess =
    roleConfig?.seafarer?.view?.crewPlannerSeafarer ||
    roleConfig?.seafarer?.edit?.crewPlannerSeafarer;

  useEffect(() => {
    const fetchPlannerRanks = async () => {
      setIsLoadingPlanner(true);
      try {
        const [plannerRankList, vesselTypesList] = await Promise.all([
          seafarerService.getPlannerRanks(),
          vesselService.getVesselType(),
        ]);
        setPlannerRanks(plannerRankList?.data?.response?.ranks);
        let filteredVesselList;
        if (roleConfig.vessel.view.dryVessel) {
          filteredVesselList = vesselTypesList?.data?.filter((vessel) => vessel.type === 'dry');
        } else if (roleConfig.vessel.view.tankerVessel) {
          filteredVesselList = vesselTypesList?.data?.filter((vessel) => vessel.type === 'tanker');
        } else {
          filteredVesselList = vesselTypesList?.data;
        }
        setVesselTypes(filteredVesselList);
      } catch (error) {
        setAlertMessage({ message: 'failed to fetch planner ranks', variant: 'danger' });
      } finally {
        setIsLoadingPlanner(false);
      }
    };
    fetchPlannerRanks();
  }, []);

  return (
    <AccessHandlerWrapper hasRoleAccess={hasRoleAccess}>
      <Container className="crew-planner">
        {isLoadingPlanner ? (
          <Spinner />
        ) : (
          <>
            {!!alertMessage?.message && (
              <AutoDismissibleAlert
                setAlertMessage={setAlertMessage}
                noAutoDismissOnDanger
                variant={alertMessage.variant}
                message={alertMessage.message}
                ref={alertRef}
              />
            )}
            {!!timeoutError?.message && (
              <AutoDismissibleAlert
                setAlertMessage={setTimeoutError}
                noAutoDismissOnDanger
                variant={timeoutError.variant}
                message={timeoutError.message}
                ref={timeoutRef}
                className="font-size-14 font-weight-bold"
              />
            )}
            <CrewPlanner
              alertRef={alertRef}
              keycloak={keycloak}
              setAlertMessage={setAlertMessage}
              isVisible
              plannerRanks={plannerRanks}
              vesselTypesList={vesselTypes}
              ga4react={ga4react}
              roleConfig={roleConfig}
              setTimeoutError={setTimeoutError}
            />
          </>
        )}
      </Container>
    </AccessHandlerWrapper>
  );
}

export default CrewPlannerPage;
