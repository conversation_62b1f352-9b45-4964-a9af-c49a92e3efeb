import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { Col, Row } from 'react-bootstrap';
import { CCircle } from 'react-bootstrap-icons';
import { Grid } from '../../component/grid/grid';
import SeafarerBioDataGridColumns, { setScreenData } from './seafarer-bio-data-grid-columns';
import '../scss/seafarer-bio-data.scss';
import seafarerService from '../../service/seafarer-service';
import Spinner from '../../component/common/Spinner';
import { formatDate } from '../../util/view-utils';
import ImageController from '../../controller/image-upload-controller';
import ErrorAlert from '../../component/common/ErrorAlert';
import AccessHandlerWrapper from '../../component/common/AccessHandlerWrapper';
import styleGuide from '../../styleGuide';

const logo = require('../../../public/images/fleet-management-logo.svg');

const { Icon } = styleGuide;
const imageController = new ImageController();

function SeafarerBioData() {
  const { seafarerPersonId } = useParams();
  const [pageSections, setPageSections] = useState<Array<Object>>([]);
  const [gridData, setGridData] = useState([]);
  const [seafarerDetails, setSeafarerDetails] = useState({});
  const [loading, setLoading] = useState(false);
  const [photo, setPhoto] = useState('');
  const [error, setError] = useState('');

  useEffect(() => {
    (async () => {
      try {
        setLoading(true);
        const { data: biodata } = await seafarerService.getSeafarerBioDataById(seafarerPersonId);
        if (biodata.response) {
          const img = await imageController.downloadSeafarerImage({
            id: biodata.response.seafarerPhotoId,
          });
          const base64 = imageController.arrayBufferToBase64(img);
          setPhoto(base64);
          setScreenData(
            biodata.response,
            setGridData,
            setPageSections,
            setSeafarerDetails,
            setLoading,
          );
        } else if (biodata.error) {
          setError(`Oops, something went wrong. Please try again. Error: ${biodata.message}`);
          setLoading(false);
        } else {
          //do nothing
        }
      } catch (error) {
        console.log(error);
        setError(`Oops, something went wrong. Please try again. Error: ${error}`);
        setLoading(false);
      }
    })();
  }, [seafarerPersonId]);
  const navbar = document.getElementsByClassName('topnav').navbar;
  if (navbar) {
    navbar.hidden = true;
  }

  const renderLoader = () => {
    return <Spinner alignClass="spinner-grid" data-testid="loader" />;
  };
  return (
    <AccessHandlerWrapper hasRoleAccess>
      {error ? <ErrorAlert message={error} /> : ''}
      <div className="seafarer-bio">
        <Row className="page-heading">
          <h4>BIO-DATA</h4>
          <img src={logo} className="logo" alt="fmlLogo" />
        </Row>
        {loading ? (
          renderLoader()
        ) : (
          <Row>
            <Col xs={9} className="padding-right-zero">
              <div className="bio-data">
                {pageSections.map((pageSection, pageSectionIndex) => {
                  const sectionKey = `section_${pageSectionIndex}`;
                  return (
                    <div className="section" key={sectionKey}>
                      {Object.keys(pageSection).map((pageSectionKey) => {
                        return (
                          <Row key={pageSectionKey}>
                            <Col>{pageSection[pageSectionKey].label}</Col>
                            <Col>
                              {pageSection[pageSectionKey].children
                                ? Object.keys(pageSection[pageSectionKey].children).map(
                                    (key: string) => {
                                      const child = pageSection[pageSectionKey].children[key];
                                      return (
                                        <span
                                          className={`child ${child.class}`}
                                          key={key}
                                        >
                                          {child.value}
                                        </span>
                                      );
                                    },
                                  )
                                : pageSection[pageSectionKey].value}
                            </Col>
                          </Row>
                        );
                      })}
                    </div>
                  );
                })}
              </div>
            </Col>
            <Col xs={3} className="image-container">
              {photo && (
                <img
                  className="seafarer-print-bio-photo"
                  src={`data:image/png;base64, ${photo}`}
                  alt="seafarerPhoto"
                />
              )}
            </Col>
          </Row>
        )}
        <hr />
        <div className="seafarer-exp-heading">SEA EXPERIENCE</div>
        {!loading && (
          <>
            {gridData.length ? (
              <Grid
                columns={SeafarerBioDataGridColumns}
                data={gridData}
                showBottomPagination={false}
                gridStyle={{
                  marginTop: '.5rem',
                  fontSize: '15px',
                }}
                isManualSort={false}
                isLoading={loading}
              />
            ) : (
              <Row className="no-data-alert-icon">
                <div>
                  <Icon icon="alert" size={30} />
                  <p>No experience found for the seafarer.</p>
                </div>
              </Row>
            )}
          </>
        )}
        {!loading && (
          <div className="copyright">
            <div className="icon-container">
              <CCircle size={16} />
              <span>Fleet Management Ltd. All rights reserved.</span>
            </div>
            <div>
              BIO-DATA of
              {` ${seafarerDetails.firstName || ''} ${seafarerDetails.middleName || ''} ${
                seafarerDetails.lastName || ''
              } `}
              , page generated on {formatDate(new Date())}
            </div>
          </div>
        )}
      </div>
    </AccessHandlerWrapper>
  );
}

export default SeafarerBioData;
