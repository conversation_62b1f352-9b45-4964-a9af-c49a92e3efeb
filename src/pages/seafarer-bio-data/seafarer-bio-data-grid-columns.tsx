import moment from 'moment';
import React from 'react';
import { useHistory } from 'react-router';
import { Button } from 'react-bootstrap';
import { getDurationByDateRanges } from '../../model/utils';
import { formatDate } from '../../util/view-utils';
import seafarerService from '../../service/seafarer-service';
import { SeafarerExperienceByRank } from '../../model/SeafarerExperienceDetails';
import '../scss/seafarer-bio-data.scss';

const fmlLogo = require('../../../public/icons/fml-vessel.svg');

const renderTimeRangeInYMD = ({ years, months, days }) => {
  let yearStr = '';
  let monthStr = '';
  let dayStr = '';
  if (years) {
    yearStr = `${years}Y `;
  }
  if (months) {
    monthStr = `${months}M `;
  }
  if (days) {
    dayStr = `${days}D`;
  }
  return yearStr + monthStr + dayStr;
};

const SeafarerCurrentRankExperience = (rankId: number, vesselExperience) => {
  const currentRankExperince = vesselExperience.filter((exp) => exp.rank_id === rankId);
  return SeafarerExperienceByRank(currentRankExperince)[0]?.value;
};

const getValue = (text = '', defaultValue = '---') => {
  return text || defaultValue;
};

const getHeightWeight = (seafarerPerson) => {
  let heightWeight = '';
  if (seafarerPerson.height) {
    heightWeight += `${seafarerPerson.height}cm`;
  } else {
    heightWeight += '---';
  }
  heightWeight += ' / ';
  if (seafarerPerson.weight) {
    heightWeight += `${seafarerPerson.weight}kg`;
  } else {
    heightWeight += '---';
  }
  return heightWeight;
};

function ShowVesselName({ row }) {
  const history = useHistory();
  const visitVesselDetailPage = async (vesselRefId) => {
    if (vesselRefId) {
      const result = await seafarerService.getVesselOwnershipId(vesselRefId);
      history.push(`/vessel/ownership/details/${result.data.ownership_id}`);
    }
  };
  return (
    <>
      <img src={fmlLogo} alt="isFMLVesselLogo" />
      <Button
        variant="link"
        className="dynamic-text-size"
        onClick={() => visitVesselDetailPage(row.original.vessel_ref_id)}
      >
        {row.original.vessel_name}
      </Button>
    </>
  );
}

export const setScreenData = (
  response,
  setGridData,
  setPageSections,
  setSeafarerDetails,
  setLoading,
) => {
  const {
    seafarerBioDetail,
    stcwCourses,
    certificateOfCompetency,
  } = response;
  const pageSectionsCopy: Array<Object> = [
    {
      fullName: {
        label: 'Full Name (Surname Underlined)',
        children: {
          firstName: { value: getValue(seafarerBioDetail.seafarer_person.first_name, '') },
          middleName: { value: getValue(seafarerBioDetail.seafarer_person.middle_name, '') },
          lastName: {
            value: getValue(seafarerBioDetail.seafarer_person.last_name, ''),
            class: 'underline',
          },
        },
      },
      rank: {
        label: 'Rank',
        value: getValue(seafarerBioDetail.rank),
      },
      nationality: {
        label: 'Nationality',
        value: getValue(seafarerBioDetail.seafarer_person.nationality.value),
      },
      dob: {
        label: 'Date of Birth',
        value: formatDate(seafarerBioDetail.seafarer_person.date_of_birth),
      },
      pob: {
        label: 'Place of Birth',
        value: `${getValue(seafarerBioDetail.seafarer_person.place_of_birth)}, ${getValue(seafarerBioDetail?.seafarer_person?.country_of_birth?.value)}`,
      },
      height: {
        label: 'Height / Weight',
        value: getHeightWeight(seafarerBioDetail.seafarer_person),
      },
    },
  ];
  if (seafarerBioDetail.seafarer_person.passports.length) {
    const passports = JSON.parse(JSON.stringify(seafarerBioDetail.seafarer_person.passports));
    passports.sort((passport1, passport2) => {
      return (
        new Date(passport2.date_of_expiry).getTime() - new Date(passport1.date_of_expiry).getTime()
      );
    });
    pageSectionsCopy.push({
      passportNumber: {
        label: 'Passport Number',
        value: getValue(passports[0].number),
      },
      doi: {
        label: 'Date of Issue / Valid Until / Place of Issue',
        value: `${formatDate(passports[0].date_of_issue)} / ${formatDate(
          passports[0].date_of_expiry,
        )} / ${getValue(passports[0].place_of_issue)}, ${getValue(passports[0].country.value)}`,
      },
    });
  } else {
    pageSectionsCopy.push({
      passportNumber: {
        label: 'Passport Number',
        value: '---',
      },
      doi: {
        label: 'Date of Issue / Valid Until / Place of Issue',
        value: `--- / ---`,
      },
    });
  }
  if (seafarerBioDetail.seafarer_person.seaman_books.length) {
    const seamanBooks = JSON.parse(JSON.stringify(seafarerBioDetail.seafarer_person.seaman_books));
    seamanBooks.sort((book1, book2) => {
      return new Date(book2.date_of_issue).getTime() - new Date(book1.date_of_issue).getTime();
    });
    pageSectionsCopy.push({
      seamanBookNumber: {
        label: `Seaman's Book Number`,
        value: getValue(seamanBooks[0].number),
      },
      doi: {
        label: 'Date of Issue / Place of Issue ',
        value: `${formatDate(seamanBooks[0].date_of_issue)} / ${getValue(
          seamanBooks[0].place_of_issue,
        )}, ${getValue(seamanBooks[0].country.value)}`,
      },
    });
  } else {
    pageSectionsCopy.push({
      seamanBookNumber: {
        label: `Seaman's Book Number`,
        value: '---',
      },
      doi: {
        label: 'Date of Issue / Place of Issue ',
        value: `--- / ---`,
      },
    });
  }
  if (certificateOfCompetency.length) {
    certificateOfCompetency.sort((certificate1, certificate2) => {
      return (
        new Date(certificate2.date_of_expiry).getTime() -
        new Date(certificate1.date_of_expiry).getTime()
      );
    });
    pageSectionsCopy.push({
      certificateOfCompetency: {
        label: 'Certificate Of Competency',
        value: getValue(certificateOfCompetency[0].certificate_name),
      },
      validUntil: {
        label: 'Number / Valid Until',
        value: `${getValue(certificateOfCompetency[0].certificate_no)} / ${formatDate(
          certificateOfCompetency[0].date_of_expiry,
        )}`,
      },
    });
  } else {
    pageSectionsCopy.push({
      certificateOfCompetency: {
        label: 'Certificate Of Competency',
        value: '---',
      },
      validUntil: {
        label: 'Number / Valid Until',
        value: `--- / ---`,
      },
    });
  }

  pageSectionsCopy.push({
    stcwCourses: {
      label: 'STCW Courses',
      value: stcwCourses.length ? stcwCourses.join(' / ') : '---',
    },
  });
  const SeafarerCurrentRankExp = SeafarerCurrentRankExperience(
    seafarerBioDetail.rank_id,
    seafarerBioDetail.seafarer_experience,
  );
  pageSectionsCopy.push({
    yearsInCurrentRank: {
      label: 'Years in Current Rank',
      value: getValue(SeafarerCurrentRankExp !== '0' ? SeafarerCurrentRankExp : ''),
    },
  });

  setPageSections(pageSectionsCopy);
  setGridData(seafarerBioDetail.seafarer_experience);
  setSeafarerDetails({
    firstName: seafarerBioDetail.seafarer_person.first_name || '',
    middleName: seafarerBioDetail.seafarer_person.middle_name || '',
    lastName: seafarerBioDetail.seafarer_person.last_name || '',
  });
  setLoading(false);
};

const SeafarerBioDataGridColumns = [
  {
    Header: 'Vessel Name',
    id: 'vessel_name',
    accessor: 'vessel_name',
    Cell: ({ row }) =>
      row.original.vessel_ref_id ? <ShowVesselName row={row} /> : row.original.vessel_name,
    Footer: null,
    disableSortBy: true,
  },
  {
    Header: 'Type',
    id: 'vessel_type',
    accessor: 'vessel_type',
    Footer: null,
    disableSortBy: true,
  },
  {
    Header: 'Engine Type',
    id: 'engine_type',
    accessor: 'engine_type',
    Footer: null,
    disableSortBy: true,
    minWidth: 50,
    width: 85,
  },
  {
    Header: 'BHP',
    id: 'brake_horse_power',
    accessor: 'brake_horse_power',
    Footer: null,
    disableSortBy: true,
    minWidth: 50,
    width: 85,
    customHeaderCellStyle: {
      textAlign: 'right',
      paddingRight: '2em',
    },
    customDataCellStyle: {
      textAlign: 'right',
      paddingRight: '2rem',
    },
  },
  {
    Header: 'DWT/GRT',
    id: 'deadweight_tonnage',
    accessor: (row) =>
      `${row.deadweight_tonnage} (` + (row.deadweight_gross_registered_tonnage || '') + ')',
    Footer: null,
    disableSortBy: true,
    minWidth: 80,
    width: 95,
  },
  {
    Header: 'Rank',
    id: 'rank',
    accessor: 'rank',
    Footer: null,
    disableSortBy: true,
    width: 140,
  },
  {
    Header: 'Owner',
    id: 'owner_name',
    accessor: 'owner_name',
    Footer: null,
    disableSortBy: true,
  },
  {
    Header: 'Start Date',
    id: 'start_date',
    accessor: (row) => formatDate(row.start_date),
    minWidth: 100,
    Footer: null,
    disableSortBy: true,
    customHeaderCellStyle: {
      textAlign: 'right',
      paddingRight: 15,
    },
    customDataCellStyle: {
      textAlign: 'right',
      paddingRight: 15,
    },
  },
  {
    Header: 'End Date',
    id: 'end_date',
    accessor: (row) => formatDate(row.end_date),
    minWidth: 100,
    Footer: null,
    disableSortBy: true,
    customHeaderCellStyle: {
      textAlign: 'right',
      paddingRight: 15,
    },
    customDataCellStyle: {
      textAlign: 'right',
      paddingRight: 15,
    },
  },
  {
    Header: 'Period',
    id: 'period',
    accessor: (row) =>
      renderTimeRangeInYMD(
        getDurationByDateRanges([
          {
            fromDateISOString: moment(row.start_date).format('YYYY-MM-DD'),
            toDateISOString: moment(row.end_date).format('YYYY-MM-DD'),
          },
        ]),
      ),
    Footer: null,
    disableSortBy: true,
  },
];

export default SeafarerBioDataGridColumns;
