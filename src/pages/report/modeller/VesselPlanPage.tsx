import React, { useEffect, useRef, useState } from 'react';
import { useHistory, useParams } from 'react-router-dom';
import { Container, Row, Col, Button, ButtonToolbar, ButtonGroup } from 'react-bootstrap';
import styleGuide, { ErrorPage } from '../../../styleGuide';
import NavigationBar from '../../../component/SeafarerReport/NavigationBar/NavigationBar';
const { Icon } = styleGuide;
import VesselPlanTable from '../../../component/SeafarerReport/Modeller/VesselPlanTable';
import { vesselPlanItems } from '../../../component/SeafarerReport/Modeller/MenuList';
import _ from 'lodash';
import { GetVesselPlanApiRes } from '../../../types/vesselPlan';
import Spinner from '../../../component/common/Spinner';
import VesselPlanFooter from '../../../component/SeafarerReport/Modeller/Footer';
import { vesselPlanFooterItems } from '../../../component/SeafarerReport/Modeller/FooterItem';
import { exportTableToExcel, extractValidColumns } from '../../../util/excel-export';
import seafarerReportService from '../../../service/seafarer-report-service';
import CopyVesselPlan from './copyVesselPlan/CopyVesselPlan';
import { formatValue } from '../../../util/view-utils';
import AccessHandlerWrapper from '../../../component/common/AccessHandlerWrapper';
import GA4React from 'ga-4-react';
import { useAccess } from '@src/component/common/Access';

// eslint-disable-next-line no-undef
interface Props {
  // eslint-disable-next-line no-undef
  ga4react: GA4React;
}

const VesselPlan = ({ ga4react }: Props) => {
  const history = useHistory();
  const { roleConfig } = useAccess();
  let { vesselOwnerShipId, action }: { vesselOwnerShipId: string; action: string } = useParams();
  const [vesselPlanData, setVesselPlanData] = useState<GetVesselPlanApiRes | undefined>();
  const [vesselDropdownData, setVesselDropdownData] = useState<any>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState(false);
  const tableRef = useRef(null);
  const [isPageViewInvoked, setIsPageViewInvoked] = useState(false);

  useEffect(() => {
    if (!isPageViewInvoked) {
      try {
        ga4react?.pageview(history.location.pathname, '', 'Seafarer List');
        setIsPageViewInvoked(true);
      } catch (e) {
        console.log(e);
      }
    }
  }, [isPageViewInvoked]);

  const handleClose = () => {
    history.push('/seafarer-reports/modeller');
  };
  useEffect(() => {
    (async () => {
      fetchTableData();
    })();
  }, []);

  const ga4EventTrigger = (action: string, category: string, label: string) => {
    try {
      ga4react?.event(action, _.toString(label), category, false);
    } catch (error) {
      console.log(error);
    }
  };

  const eventTracker = (type: string, value: string) => {
    switch (type) {
      case 'breadCrumb':
        ga4EventTrigger('Breadcrumb', 'Modeller Report - menu', value ?? '');
        break;
      case 'vesselPlanEditButton':
        ga4EventTrigger('Edit Modeller from Details', 'Modeller Report - menu', value ?? '');
        break;
      case 'vesselPlanCopyButton':
        ga4EventTrigger('Copy Modeller Plan', 'Modeller Report - menu', value ?? '');
        break;
      case 'vesselPlanConfirmCopyButton':
        ga4EventTrigger(
          'Confirm Copy Modeller Plan',
          'Modeller Report - menu',
          vesselPlanData?.vessel_name ?? '',
        );
        break;
      case 'exportModellerReport':
        ga4EventTrigger('Export Modeller Report', 'Modeller Report - menu', value ?? '');
        break;
      default:
        ga4EventTrigger('Click', 'Seafarer Modeller Report', value);
        break;
    }
  };

  const filterVesselPlanData = (vesselPlanData: GetVesselPlanApiRes) => {
    return {
      ...vesselPlanData,
      seafarer_report_modeller_details: vesselPlanData.seafarer_report_modeller_details.filter(
        (ele: any) =>
          ele.planned_nationality_id ||
          ele.planned_number ||
          ele.planned_wages ||
          ele.actual_nationality_id ||
          ele.actual_number ||
          ele.actual_wages,
      ),
    };
  };

  const fetchTableData = async () => {
    try {
      setIsLoading(true);
      const [vesselPlan, vesselDropdown] = await Promise.all([
        seafarerReportService.getVesselPlanByOwnershipId(vesselOwnerShipId),
        seafarerReportService.getModellerReports('orderBy=vessel_name'),
      ]);
      const filteredVesselPlanData = filterVesselPlanData(vesselPlan?.data);

      setVesselPlanData(filteredVesselPlanData);
      setVesselDropdownData(
        vesselDropdown.data.results.map(
          (i: { id: number; vessel_name: string; vessel_ownership_id: number }) => ({
            id: i.vessel_ownership_id,
            value: formatValue(i?.vessel_name ? i.vessel_name + ' ' + i.id : null),
          }),
        ),
      );
    } catch (error) {
      console.log(error);
      setIsError(true);
    }
    setIsLoading(false);
  };

  const refreshTableData = async () => {
    try {
      setIsLoading(true);
      const vesselPlan = await seafarerReportService.getVesselPlanByOwnershipId(vesselOwnerShipId);
      const filteredVesselPlanData = filterVesselPlanData(vesselPlan?.data);

      setVesselPlanData(filteredVesselPlanData);
    } catch (error) {
      console.log(error);
      setIsError(true);
    }
    setIsLoading(false);
  };

  const constructTableData = (data: GetVesselPlanApiRes) => {
    let rankIds: number[] = [];
    if (data) {
      // this is needed
      const sortedReportDetails = data?.seafarer_report_modeller_details?.toSorted(
        (a, b) => a.rank.sortpriority - b.rank.sortpriority,
      );

      const modifiedData = sortedReportDetails
        ?.filter(
          (item) =>
            item?.planned_number !== 0 ||
            item?.actual_wages !== null ||
            item?.planned_nationality !== null ||
            item?.actual_nationality !== null,
        )
        .map((i, index) => {
          if (!rankIds.includes(i.rank_id)) {
            rankIds.push(i.rank_id);
            return { ...i, id: index + 1 };
          }
          return {
            ...i,
            id: index + 1,
            rank: {
              ...i.rank,
              value: ' ',
            },
            planned_number: ' ',
            actual_number: ' ',
          };
        }); // NOSONAR
      return modifiedData;
    }
    return [];
  };

  const handleExportToExcel = () => {
    eventTracker('exportModellerReport', vesselPlanData?.vessel_name ?? '');
    if (!vesselPlanData) return;
    const footerColumns = [
      { Header: ' ', value: 'Total', type: 'text' },
      ...vesselPlanFooterItems(vesselPlanData, vesselPlanData?.planned_wages_unit).slice(0, 3),
      ..._.fill(Array(2), { Header: ' ', value: ' ', type: 'text' }),
      ...vesselPlanFooterItems(vesselPlanData, vesselPlanData?.planned_wages_unit).slice(3),
    ];
    const excelData = [
      {
        jsonData: [vesselPlanData],
        columns: extractValidColumns(footerColumns),
        colStartIndex: 1,
      },
      {
        jsonData: constructTableData(vesselPlanData),
        columns: extractValidColumns(
          vesselPlanItems({ plannedWagesUnit: vesselPlanData.planned_wages_unit }),
        ),
      },
    ];
    exportTableToExcel(
      excelData,
      `Vessel Plan of ${vesselPlanData?.vessel_name}`,
      'Vessel Plan Summary',
    );
  };

  const goToVesselPlanEditPage = () => {
    eventTracker('vesselPlanEditButton', vesselPlanData?.vessel_name ?? '');
    history.push(`/seafarer-reports/modeller/${vesselOwnerShipId}/edit`);
  };

  const goToCopyVesselPlanModal = () => {
    eventTracker('vesselPlanCopyButton', vesselPlanData?.vessel_name ?? '');
    history.push(`/seafarer-reports/modeller/${vesselOwnerShipId}/copy`);
  };

  const loadingComponent = () => {
    return isLoading ? (
      <div className="pb-5">
        <Spinner />
      </div>
    ) : null;
  };

  const errroComponent = () => {
    return isError ? <ErrorPage errorCode={404} /> : null;
  };

  return (
    <AccessHandlerWrapper
      hasRoleAccess={
        action === 'copy'
          ? roleConfig?.seafarer?.edit?.reportModeller
          : roleConfig?.seafarer?.view?.reportModeller
      }
    >
      <Container>
        {loadingComponent() ?? errroComponent() ?? (
          <>
            {action && (
              <CopyVesselPlan
                history={history}
                vesselOwnerShipId={vesselOwnerShipId}
                vesselDropdownData={vesselDropdownData}
                refreshTableData={refreshTableData}
                roleConfig={roleConfig}
                eventTracker={eventTracker}
              />
            )}
            <Row className="px-3">
              <div className="col-md-7 mb-1 justify-content-start">
                <NavigationBar
                  vesselName={vesselPlanData?.vessel_name}
                  isEdit={false}
                  vesselOwnerShipId={vesselOwnerShipId}
                  eventTracker={eventTracker}
                />
              </div>
              <Col>
                <ButtonToolbar className="no-print btn-toolbar justify-content-end">
                  <ButtonGroup className="mr-2">
                    <Button
                      size="sm"
                      variant="outline-primary"
                      data-testid="edit-plan-btn"
                      onClick={goToVesselPlanEditPage}
                      disabled={!roleConfig?.seafarer?.edit?.reportModeller}
                    >
                      Edit Plan
                    </Button>
                  </ButtonGroup>
                  <ButtonGroup className="mr-2">
                    <Button
                      size="sm"
                      variant="outline-primary"
                      data-testid="edit-plan-btn"
                      onClick={goToCopyVesselPlanModal}
                      disabled={!roleConfig?.seafarer?.edit?.reportModeller}
                    >
                      Copy Plan
                    </Button>
                  </ButtonGroup>
                  <ButtonGroup className="mr-2">
                    <Button
                      size="sm"
                      variant="outline-primary"
                      onClick={handleExportToExcel}
                      data-testid="export-to-excel-btn"
                    >
                      Export to Excel
                    </Button>
                  </ButtonGroup>
                  <div className="vessel-plan-page">
                    <Icon className="ml-2 mt-1" icon="close" size={30} onClick={handleClose} />
                  </div>
                </ButtonToolbar>
              </Col>
            </Row>
            <Row className="px-3">
              <Col>
                {vesselPlanData && (
                  <VesselPlanTable
                    columns={vesselPlanItems({
                      plannedWagesUnit: vesselPlanData.planned_wages_unit,
                    })}
                    data={constructTableData(vesselPlanData)}
                    loading={false}
                    dataTestId="vessel-plan-table"
                    tableRef={tableRef}
                  />
                )}
              </Col>
            </Row>
            <Row>
              <VesselPlanFooter
                columns={vesselPlanFooterItems(vesselPlanData, vesselPlanData?.planned_wages_unit)}
                tableRef={tableRef}
              />
            </Row>
          </>
        )}
      </Container>
    </AccessHandlerWrapper>
  );
};

export default VesselPlan;
