// eslint-disable-next-line no-unused-vars
import { CopyVesselPlanProps } from '../../../../types/copyVesselPlan';
import React, { useState } from 'react';
import { But<PERSON>, Col, Form, Modal } from 'react-bootstrap';
import VesselDropDown from './VesselDropDown';
import { ConfirmationModal } from '../../../../component/common/ConfirmationModal';
import ErrorDisplayModal from '../../../../component/common/ErrorDisplayModal';
import { Dash } from '../../../../model/utils';
import seafarerReportService from '../../../../service/seafarer-report-service';
import AccessHandlerWrapper from '../../../../component/common/AccessHandlerWrapper';
import _ from 'lodash';

const CopyVesselPlan = ({
  history,
  vesselOwnerShipId,
  vesselDropdownData,
  refreshTableData,
  eventTracker,
  roleConfig,
}: CopyVesselPlanProps) => {
  const [selectedVessel, setSelectedVessel] = useState({ id: null, value: null });
  const [isError, setIsError] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isShowConfirmModal, setIsShowConfirmModal] = useState(false);

  const onClose = () => {
    history.push(`/seafarer-reports/modeller/${vesselOwnerShipId}`);
  };

  const onCopy = () => {
    setIsShowConfirmModal(true);
  };

  const handleSubmit = async () => {
    eventTracker('vesselPlanConfirmCopyButton', '');
    try {
      setIsSubmitting(true);
      await seafarerReportService.copyVesselPlanByOwnershipId(vesselOwnerShipId, selectedVessel.id);
      setIsShowConfirmModal(false);
      refreshTableData();
      history.push(`/seafarer-reports/modeller/${vesselOwnerShipId}`);
    } catch (error) {
      setErrorMessage(
        _.get(error, 'response.data', 'If the problem persists, contact your technical support'),
      );
      setIsShowConfirmModal(false);
      setIsError(true);
    }
    setIsSubmitting(false);
  };

  return (
    <AccessHandlerWrapper hasRoleAccess={roleConfig?.seafarer?.edit?.reportModeller}>
      <ConfirmationModal
        isVisible={isShowConfirmModal}
        isSubmitting={isSubmitting}
        title={
          <div className="font-size-20">
            Copy from <strong>{selectedVessel?.value ?? Dash}</strong>
          </div>
        }
        onClose={() => {
          setIsShowConfirmModal(false);
        }}
        onConfirm={handleSubmit}
      />
      <ErrorDisplayModal
        onHideModalMessage={() => {
          setIsError(false);
          setErrorMessage(null);
        }}
        modalMessage={isError ? errorMessage : false}
        title="Unable to process your request"
      />
      <Modal
        show={!(isError || isShowConfirmModal)}
        dialogClassName="confirmation-modal"
        centered
        scrollable={false}
      >
        <Modal.Header>
          <Modal.Title className="ml-3">Copy Plan from Vessel</Modal.Title>
        </Modal.Header>
        <Modal.Body className="pt-0">
          <Form>
            <Form.Group>
              <Form.Label className="ml-3">
                <strong>Vessel</strong>{' '}
              </Form.Label>
              <Col className="pr-3">
                <VesselDropDown
                  dropDownValues={vesselDropdownData}
                  onChange={(e) => {
                    setSelectedVessel(e);
                  }}
                />
              </Col>
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="primary" onClick={onClose}>
            Cancel
          </Button>
          {onCopy && (
            <Button variant="secondary" onClick={onCopy} disabled={!selectedVessel?.id}>
              Copy
            </Button>
          )}
        </Modal.Footer>
      </Modal>
    </AccessHandlerWrapper>
  );
};

export default CopyVesselPlan;
