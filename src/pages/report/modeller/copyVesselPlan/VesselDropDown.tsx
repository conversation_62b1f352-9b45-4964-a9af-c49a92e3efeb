/* eslint-disable react/display-name */
// eslint-disable-next-line no-unused-vars
import { VesselDropDownProps } from '../../../../types/copyVesselPlan';
import React, { useRef, Fragment } from 'react';
import { Highlighter, Menu, MenuItem, Typeahead } from 'react-bootstrap-typeahead';
import { Icon } from '../../../../styleGuide';
import { Button } from 'react-bootstrap';

const VesselDropDown = ({ dropDownValues = [], onChange }: VesselDropDownProps) => {
  const typeaheadRef = useRef(null);
  const props: any = {};
  props.renderMenu = (
    results: any[],
    menuProps: any,
    state: { text: string; selected: string[] },
  ) => {
    const items = results.map((i, index) => {
      const menuItemProps = {
        option: i,
      };
      if (i?.paginationOption) {
        return (
          <Fragment key="pagination-item">
            <Menu.Divider />
            <MenuItem
              {...menuItemProps}
              className="rbt-menu-pagination-option"
              label={'Display additional results...'}
            >
              {'Display additional results...'}
            </MenuItem>
          </Fragment>
        );
      }
      return (
        <MenuItem
          key={i.id}
          option={i}
          position={index}
          className="font-deep-blue font-weight-bold"
        >
          <Highlighter search={state.text}>{i.value.toString()}</Highlighter>
        </MenuItem>
      );
    });

    return <Menu {...menuProps}>{items}</Menu>;
  };

  const handleChange = (selected: any[]) => {
    if (selected.length) onChange(selected[0]);
    else onChange({ id: null, value: null });
  };

  return (
    <>
      <Typeahead
        {...props}
        labelKey="value"
        id="search-type-menu"
        ref={typeaheadRef}
        onChange={handleChange}
        options={dropDownValues}
        placeholder="Please select"
        multiple={false}
      />
      <Button
        variant="link"
        className="typeable-dropdown-icon-style mr-3"
        onClick={() => {
          // @ts-ignore
          if (typeaheadRef) typeaheadRef?.current?.focus();
        }}
      >
        {Icon && <Icon icon="dropdown" size={20} />}
      </Button>
    </>
  );
};

export default VesselDropDown;
