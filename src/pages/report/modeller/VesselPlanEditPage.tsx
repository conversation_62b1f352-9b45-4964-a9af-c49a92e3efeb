import React, { useEffect, useState } from 'react';
import _ from 'lodash';
import { useHistory, useParams } from 'react-router-dom';
import styleGuide from '../../../styleGuide';
import { Container, Row, Col } from 'react-bootstrap';
import NavigationBar from '../../../component/SeafarerReport/NavigationBar/NavigationBar';
import { EditPlanForm } from '../../../component/SeafarerReport/Modeller/EditPlanForm';
import seafarerReportService from '../../../../src/service/seafarer-report-service';
import Spinner from '../../../component/common/Spinner';
import { SeafarerReportModeller } from '@src/types/seafarerInterfaces';
// eslint-disable-next-line no-unused-vars
import GA4React from 'ga-4-react';
import AccessHandlerWrapper from '../../../component/common/AccessHandlerWrapper';
import { useAccess } from '@src/component/common/Access';
const { Icon } = styleGuide;

// eslint-disable-next-line no-undef
interface Props {
  // eslint-disable-next-line no-undef
  ga4react: GA4React;
}

const VesselPlanEdit = ({ ga4react }: Props) => {
  let { vesselOwnerShipId } = useParams<{ vesselOwnerShipId: string }>();
  const history = useHistory();
  const { roleConfig } = useAccess();
  const [vesselPlanData, setVesselPlanData] = useState<SeafarerReportModeller>();
  const [isLoading, setIsLoading] = useState(true);
  const [isPageViewInvoked, setIsPageViewInvoked] = useState(false);

  useEffect(() => {
    if (!isPageViewInvoked) {
      try {
        ga4react?.pageview(history.location.pathname, '', 'Seafarer List');
        setIsPageViewInvoked(true);
      } catch (e) {
        console.log(e);
      }
    }
  }, [isPageViewInvoked]);

  useEffect(() => {
    (async () => {
      const response = await seafarerReportService.getVesselPlanByOwnershipId(vesselOwnerShipId);
      const responseVesselPlanData = response.data;
      setVesselPlanData(responseVesselPlanData);
      setIsLoading(false);
    })();
  }, []);

  const ga4EventTrigger = (action: string, category: string, label: string) => {
    try {
      ga4react?.event(action, _.toString(label), category, false);
    } catch (error) {
      console.log(error);
    }
  };

  const eventTracker = (type: string, value: string) => {
    switch (type) {
      case 'breadCrumb':
        ga4EventTrigger('Breadcrumb', 'Modeller Report - menu', value ?? '');
        break;
      case 'editVesselPlanSaveButton':
        ga4EventTrigger('Save Edit Modeller Plan', 'Modeller Report - menu', value ?? '');
        break;
      default:
        ga4EventTrigger('Click', 'Seafarer Modeller Report', value);
        break;
    }
  };

  const handleClose = () => {
    history.push(`/seafarer-reports/modeller/${vesselOwnerShipId}`);
  };
  return (
    <AccessHandlerWrapper hasRoleAccess={roleConfig?.seafarer?.edit?.reportModeller}>
      <Container>
        {!isLoading && vesselPlanData ? (
          <>
            <Row>
              <div className="col-auto justify-content-start">
                <NavigationBar
                  vesselName={vesselPlanData.vessel_name}
                  isEdit={true}
                  vesselOwnerShipId={vesselOwnerShipId}
                  eventTracker={eventTracker}
                />
              </div>
              <Col className="float-right">
                <div className="vessel-plan-page">
                  <Icon icon="close" size={30} onClick={handleClose} />
                </div>
              </Col>
            </Row>
            <Row>
              <EditPlanForm
                vesselOwnershipId={_.toNumber(vesselOwnerShipId)}
                vesselPlanDetails={vesselPlanData.seafarer_report_modeller_details}
                vesselName={vesselPlanData.vessel_name}
                eventTracker={eventTracker}
                plannedWagesUnit={vesselPlanData.planned_wages_unit}
              />
            </Row>
          </>
        ) : (
          <div className="mt-50">
            {' '}
            <Spinner />{' '}
          </div>
        )}
      </Container>
    </AccessHandlerWrapper>
  );
};

export default VesselPlanEdit;
