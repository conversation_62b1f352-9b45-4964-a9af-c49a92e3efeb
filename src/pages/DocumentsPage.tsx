/* eslint-disable react/display-name */
/* eslint-disable react/prop-types */
import React, { useState, useEffect, useRef } from 'react';
import { Container, Row, Col, Button } from 'react-bootstrap';
import { useParams, useHistory, Switch, Route } from 'react-router-dom';
import { orderBy } from 'lodash';
import seafarerService from '../service/seafarer-service';
import styleGuide from '../styleGuide';
import { dateAsString } from '../model/utils';
import AddSeafarerController from '../controller/add-seafarer-controller';
import AccessHandlerWrapper from '../component/common/AccessHandlerWrapper';
import ConfirmActionModalView from '../component/AddSeafarer/ConfirmActionModalView';
import { ExpiryDateStyling } from '../component/common/ExpiryDateStyling';
import DocumentTable from '../component/OtherDocument/DocumentTable';
import AddDocumentModal from '../component/document/AddDocumentModal';
import * as documentTypes from '../constants/documentTypes';
import DocumentDownloadFailedModal from '../component/document/DocumentDownloadFailedModal';
import { scrollToSection } from '../util/view-utils';
import { removeSpaces } from '../util/string-utils';
import ErrorDisplayModal from '../component/common/ErrorDisplayModal';
import { useAccess } from '@src/component/common/Access';

const { Icon } = styleGuide;

const { DOC_DOWNLOAD_FAIL_MESSAGE } = process.env;

const addSeafarerController = new AddSeafarerController();

const generateDocumentColumn = (downloadFile) => {
  return [
    {
      Header: 'Documents',
      id: 'documents',
      accessor: function documentAccessor(row) {
        return (
          <Button
            variant="link"
            className="selected-file-link"
            onClick={() => downloadFile(row?.documentType, { id: row?.document?.id }, row.docPath)}
          >
            {row?.docPath ? 'View' : ''}
          </Button>
        );
      },
      disableSortBy: true,
      maxWidth: 100,
    },
  ];
};

const generateEditDeleteActionColumn = (showDeleteAndEdit, editHandler, deleteHandler) => {
  return [
    {
      Header: 'Action',
      id: 'action',
      accessor: function actionAccessor(row) {
        return (
          <div className="action-column">
            {showDeleteAndEdit && (
              <div style={{ display: 'flex' }}>
                <Button
                  variant="link"
                  onClick={() => {
                    editHandler(row?.documentType, row?.document?.id);
                  }}
                  data-testid="edit-doc-btn"
                >
                  <Icon icon="pencil" size={20} className="mr-3" />
                </Button>
                <Button
                  variant="link"
                  onClick={() => {
                    deleteHandler(row?.document?.id, row?.documentType);
                  }}
                  data-testid="delete-doc-btn"
                >
                  <Icon icon="bin" size={20} />
                </Button>
              </div>
            )}
          </div>
        );
      },
      disableSortBy: true,
      maxWidth: 50,
      sticky: 'right',
    },
  ];
};

const generateEditActionColumn = (showDeleteAndEdit, editHandler) => {
  return [
    {
      Header: function () {
        return <div className="text-center">Actions</div>;
      },
      id: 'action',
      accessor: function actionAccessor(row) {
        return (
          <div className="action-column">
            {showDeleteAndEdit && (
              <span>
                <Button
                  variant="link"
                  onClick={() => {
                    editHandler(row?.documentType, row?.document?.id);
                  }}
                  data-testid="edit-doc-btn"
                >
                  <Icon icon="pencil" size={20} className="mr-3" />
                </Button>
              </span>
            )}
          </div>
        );
      },
      disableSortBy: true,
      maxWidth: 50,
      sticky: 'right',
    },
  ];
};

const generatePassportColumns = (documentColumn, editActionColumn) => {
  return [
    {
      Header: 'Passport No.',
      id: 'passport-number',
      accessor: function passportNumberAccessor(row) {
        return <div>{row?.passportNumber ?? '---'}</div>;
      },
      disableSortBy: true,
      maxWidth: 110,
    },
    {
      Header: 'Place of Issue',
      id: 'passport-place-of-issue',
      accessor: function placeOfIssueAccessor(row) {
        return <div>{row?.placeOfIssue ?? '---'}</div>;
      },
      disableSortBy: true,
      maxWidth: 110,
    },
    {
      Header: 'Country',
      id: 'passport-country',
      accessor: function countryAccessor(row) {
        return <div>{row?.country ?? '---'}</div>;
      },
      disableSortBy: true,
      maxWidth: 110,
    },
    {
      Header: 'Date of Issue',
      id: 'visa-date-of-issue',
      accessor: function dateOfIssueAccessor(row) {
        return <div>{row?.dateOfIssue ? dateAsString(row?.dateOfIssue) : '---'}</div>;
      },
      disableSortBy: true,
      maxWidth: 100,
    },
    {
      Header: 'Date of Expiry',
      id: 'visa-date-of-expiry',
      accessor: function dateOfExpiryAccessor(row) {
        return (
          <div>{row?.dateOfExpiry ? <ExpiryDateStyling value={row?.dateOfExpiry} /> : '---'}</div>
        );
      },
      disableSortBy: true,
      maxWidth: 100,
    },
    ...documentColumn,
    ...editActionColumn,
  ];
};

const generateVisaColumns = (documentColumn, editDeleteActionColumn) => {
  return [
    {
      Header: 'Type',
      id: 'type',
      accessor: function typeAccessor(row) {
        return <div>{row?.typeOfVisa ?? '---'}</div>;
      },
      disableSortBy: true,
      maxWidth: 110,
    },
    {
      Header: 'Country',
      id: 'visa-region',
      accessor: function visaRegionAccessor(row) {
        return <div>{row?.visaRegion ?? '---'}</div>;
      },
      disableSortBy: true,
      maxWidth: 110,
    },
    {
      Header: 'Number',
      id: 'visa-number',
      accessor: function numberAccessor(row) {
        return <div>{row?.number ?? '---'}</div>;
      },
      disableSortBy: true,
      maxWidth: 110,
    },
    {
      Header: 'Date of Issue',
      id: 'visa-date-of-issue',
      accessor: function dateOfIssueAccessor(row) {
        return <div>{row?.dateOfIssue ? dateAsString(row?.dateOfIssue) : '---'}</div>;
      },
      disableSortBy: true,
      maxWidth: 100,
    },
    {
      Header: 'Date of Expiry',
      id: 'visa-date-of-expiry',
      accessor: function dateOfExpiryAccessor(row) {
        return (
          <div>{row?.dateOfExpiry ? <ExpiryDateStyling value={row?.dateOfExpiry} /> : '---'}</div>
        );
      },
      disableSortBy: true,
      maxWidth: 100,
    },
    {
      Header: 'Issuing Authority',
      id: 'visa-issuing-authority',
      accessor: function issuingAuthorityAccessor(row) {
        return <div>{row?.issuingAuthority ?? '---'}</div>;
      },
      disableSortBy: true,
      maxWidth: 100,
    },
    {
      Header: 'Rejected',
      id: 'visa-rejected',
      accessor: function rejectedAccessor(row) {
        return <div>{row?.rejected ?? '---'}</div>;
      },
      disableSortBy: true,
      maxWidth: 100,
    },
    ...documentColumn,
    ...editDeleteActionColumn,
  ];
};

const generateSeamanBookColumns = (documentColumn, editActionColumn) => {
  return [
    {
      Header: 'Country',
      id: 'seaman-book-country',
      accessor: function countryAccessor(row) {
        if (row?.isOriginal) {
          return <div className="is-original">{row?.country ? `${row?.country}*` : '---'}</div>;
        } else {
          return <div>{row?.country ? row?.country : '---'}</div>;
        }
      },
      disableSortBy: true,
      maxWidth: 110,
    },
    {
      Header: 'Document No.',
      id: 'seaman-book-document-number',
      accessor: function documentNumberAccessor(row) {
        return <div>{row?.number ?? '---'}</div>;
      },
      disableSortBy: true,
      maxWidth: 110,
    },
    {
      Header: 'Port of Issue',
      id: 'port-of-issue',
      accessor: function portOfIssueAccessor(row) {
        return <div>{row?.portOfIssue ?? '---'}</div>;
      },
      disableSortBy: true,
      maxWidth: 110,
    },
    {
      Header: 'Date of Issue',
      id: 'visa-date-of-issue',
      accessor: function dateOfIssueAccessor(row) {
        return <div>{row?.dateOfIssue ? dateAsString(row?.dateOfIssue) : '---'}</div>;
      },
      disableSortBy: true,
      maxWidth: 100,
    },
    {
      Header: 'Date of Expiry',
      id: 'visa-date-of-expiry',
      accessor: function dateOfExpiryAccessor(row) {
        return (
          <div>{row?.dateOfExpiry ? <ExpiryDateStyling value={row?.dateOfExpiry} /> : '---'}</div>
        );
      },
      disableSortBy: true,
      maxWidth: 100,
    },
    ...documentColumn,
    ...editActionColumn,
  ];
};

const generateIndosColumns = (documentColumn, editDeleteActionColumn) => {
  return [
    {
      Header: 'INDoS No.',
      id: 'indos_number',
      accessor: function indosNumberAccessor(row) {
        return <div>{row?.certificateNumber ?? '---'}</div>;
      },
      disableSortBy: true,
      maxWidth: 110,
    },
    {
      Header: 'Issued By',
      id: 'indos-issued-by',
      accessor: function issuedByAccessor(row) {
        return <div>{row?.issuedBy ?? '---'}</div>;
      },
      disableSortBy: true,
      maxWidth: 110,
    },
    {
      Header: 'Date of Issue',
      id: 'visa-date-of-issue',
      accessor: function dateOfIssueAccessor(row) {
        return <div>{row?.dateOfIssue ? dateAsString(row?.dateOfIssue) : '---'}</div>;
      },
      disableSortBy: true,
      maxWidth: 100,
    },
    {
      Header: 'Date of Expiry',
      id: 'visa-date-of-expiry',
      accessor: function dateOfExpiryAccessor(row) {
        return (
          <div>{row?.dateOfExpiry ? <ExpiryDateStyling value={row?.dateOfExpiry} /> : '---'}</div>
        );
      },
      disableSortBy: true,
      maxWidth: 100,
    },
    ...documentColumn,
    ...editDeleteActionColumn,
  ];
};

const prepareVisaData = (visaArr, visaRegions) => {
  const preparedVisaData = visaArr?.map((ele) => {
    const { seafarer_doc_visa } = ele;
    const docName = ele.doc_path ? ele.doc_path.split('/').pop().split('_').slice(1).join('_') : '';
    const fileName = `Visa_${docName}`;
    let rejected = '';
    if (seafarer_doc_visa?.rejected !== null) {
      rejected = seafarer_doc_visa.rejected ? 'Yes' : 'No';
    }
    return {
      typeOfVisa: seafarer_doc_visa.type_of_visa,
      visaRegion: visaRegions?.find((ele) => ele === seafarer_doc_visa.visa_region),
      number: seafarer_doc_visa.number,
      dateOfIssue: seafarer_doc_visa.date_of_issue,
      dateOfExpiry: seafarer_doc_visa.date_of_expiry,
      issuingAuthority: seafarer_doc_visa.issuing_authority,
      rejected,
      fileName: fileName.replace(' ', '_'),
      document: {
        id: seafarer_doc_visa.seafarer_document_id,
      },
      documentType: ele.type,
      docPath: ele.doc_path,
    };
  });
  return orderBy(
    preparedVisaData,
    [(item) => item?.typeOfVisa?.toLowerCase(), 'dateOfIssue'],
    ['asc', 'desc'],
  );
};

const prepareIndosData = (indosArr) => {
  const prepareIndosData = indosArr?.map((ele) => {
    const { seafarer_doc_indos } = ele;
    const docName = ele.doc_path ? ele.doc_path.split('/').pop().split('_').slice(1).join('_') : '';
    const fileName = `Indos_.${docName}`;
    return {
      certificateNumber: seafarer_doc_indos.certificate_no,
      issuedBy: seafarer_doc_indos.issued_by,
      dateOfIssue: seafarer_doc_indos.date_of_issue,
      dateOfExpiry: seafarer_doc_indos.date_of_expiry,
      fileName: fileName.replace(' ', '_'),
      document: {
        id: seafarer_doc_indos.seafarer_document_id,
      },
      documentType: ele.type,
      docPath: ele.doc_path,
    };
  });
  return orderBy(prepareIndosData, ['dateOfIssue'], ['desc']);
};

const preparePassportsData = (seafarer) => {
  const person = seafarer?.seafarer_person ?? {};
  const items = person.passports ?? [];

  const result = items?.map((ele) => {
    const item = {};

    const fullName = getFullName(seafarer);
    const country = ele.country ? ele.country.value : '';
    const document = ele.document && ele.document.length > 0 ? ele.document[0] : undefined;

    const extension = ele.doc_path ? ele.doc_path.split('.').pop() : '';
    const fileName = `Passport_${fullName}_${country}.${extension}`;

    item.passportNumber = ele.number ?? '';
    item.placeOfIssue = ele.place_of_issue ?? '';
    item.country = ele.country ? ele.country.value : '';
    item.dateOfIssue = ele.date_of_issue;
    item.dateOfExpiry = ele.date_of_expiry;
    item.document = document;
    item.fileName = removeSpaces(fileName);
    item.documentType = 'passport';
    item.docPath = ele.doc_path;
    return item;
  });

  const sortedPassportData = orderBy(result, ['dateOfIssue'], ['desc']);
  return sortedPassportData;
};

const prepareSeamanBooksData = (seafarer) => {
  const person = seafarer?.seafarer_person ?? {};
  const items = person.seaman_books ?? [];

  const result = items?.map((ele) => {
    const item = {};

    const fullName = getFullName(seafarer);
    const country = ele.country ? ele.country.value : '';
    const document = ele.document && ele.document.length > 0 ? ele.document[0] : undefined;
    const extension = ele.doc_path ? ele.doc_path.split('.').pop() : '';
    const fileName = `SeamanBook_${fullName}_${country}.${extension}`;

    item.country = ele.country ? ele.country.value : '';
    item.number = ele.number ?? '';
    item.portOfIssue = ele.port_of_issue ?? '';
    item.dateOfIssue = ele.date_of_issue;
    item.dateOfExpiry = ele.date_of_expiry;
    item.isOriginal = ele.is_original;

    item.document = document;
    item.fileName = fileName.replace(' ', '_');
    item.documentType = 'seamans_book';
    item.docPath = ele.doc_path;
    return item;
  });
  const sortedSeamanBookData = orderBy(result, ['date_of_issue'], ['desc']);

  const isNational = sortedSeamanBookData.some((item) => item?.isOriginal === true);
  isNational &&
    sortedSeamanBookData.forEach(function (item, i) {
      if (item?.isOriginal) {
        sortedSeamanBookData.splice(i, 1);
        sortedSeamanBookData.unshift(item);
      }
    });

  return sortedSeamanBookData;
};

const getFullName = (seafarer) => {
  const person = seafarer?.seafarer_person ?? {};
  const firstName = person.first_name ?? '';
  const lastName = person.last_name ?? '';
  const result = `${firstName} ${lastName}`;
  return result;
};

const DocumentsPage = ({ seafarer, dropdownData, eventTracker }) => {
  const history = useHistory();
  const { roleConfig } = useAccess();
  let { seafarerId, documentId } = useParams();
  const seafarerPersonId = seafarer?.seafarer_person_id;

  const [showDownloadFailModal, setShowDownloadFailModal] = useState(false);
  const [passportData, setPassportData] = useState([]);
  const [seamanBookData, setSeamanBookData] = useState([]);
  const [visaData, setVisaData] = useState([]);
  const [indosData, setIndosData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectDocumentID, setSelectDocumentID] = useState(undefined);
  const [selectDocumentType, setSelectDocumentType] = useState(undefined);
  const [isRemoveDocumentConfirmationModalShow, setIsRemoveDocumentConfirmationModalShow] =
    useState(false);
  const [hash, setHash] = useState(undefined);
  const [isDisableConfirmDeleteBtn, setIsDisableConfirmDeleteBtn] = useState(false);
  const [modalMessage, setModalMessage] = useState(null);
  const [isModalLoading, setIsModalLoading] = useState(false);

  const passportSectionRef = useRef(null);
  const visaSectionRef = useRef(null);
  const seamanBookSectionRef = useRef(null);
  const indosSectionRef = useRef(null);

  const showDeleteAndEdit = roleConfig.seafarer.edit.seafarerDocument;

  const hashToReloadAndScrollHandler = {
    '#visa': async () => {
      const queryParam = '?type=visa';
      const [seafarerDocData, dropDownDataResponse] = await Promise.all([
        loadDocsData(seafarerPersonId, queryParam),
        addSeafarerController.loadDropDownData(),
      ]);
      const visaRegions = dropDownDataResponse.visaRegions;
      setVisaData(prepareVisaData(seafarerDocData.visa, visaRegions));
      setTimeout(() => scrollToSection(visaSectionRef, 0));
    },
    '#indos': async () => {
      const queryParam = '?type=indos';
      const seafarerDocData = await loadDocsData(seafarerPersonId, queryParam);
      setIndosData(prepareIndosData(seafarerDocData.indos));
      setTimeout(() => scrollToSection(indosSectionRef, 0));
    },
  };

  useEffect(() => {
    if (history.location.hash !== undefined && history.location.hash !== '') {
      setHash(history.location.hash);
    } else {
      setHash(undefined);
    }
  });

  useEffect(() => {
    (async () => {
      if (hash === undefined) {
        return;
      }

      //scroll to section based on hash
      const reloadAndScrollHandler = hashToReloadAndScrollHandler[history.location.hash];
      if (reloadAndScrollHandler) {
        setIsLoading(true);
        await reloadAndScrollHandler();
        setIsLoading(false);
      }
    })();
  }, [hash]);

  const onHideModalMessage = () => setModalMessage(null);

  const loadDocsData = async (seafarerPersonId, queryParam) => {
    const { data } = await seafarerService.getSeafarerDocumentsList(seafarerPersonId, queryParam);
    let responseObj = {};
    for (const dataObj of data) {
      if (responseObj[dataObj.type]) {
        responseObj[dataObj.type].push(dataObj);
      } else {
        responseObj[dataObj.type] = [dataObj];
      }
    }

    return responseObj;
  };

  useEffect(() => {
    (async () => {
      try {
        setIsLoading(true);
        if (seafarer?.seafarer_person_id) {
          const query = '?type=visa&type=indos';
          const [seafarerDocData, dropDownDataResponse] = await Promise.all([
            loadDocsData(seafarerPersonId, query),
            addSeafarerController.loadDropDownData(),
          ]);

          setPassportData(preparePassportsData(seafarer));
          setSeamanBookData(prepareSeamanBooksData(seafarer));

          const visaRegions = dropDownDataResponse.visaRegions;
          setVisaData(prepareVisaData(seafarerDocData.visa, visaRegions));
          setIndosData(prepareIndosData(seafarerDocData.indos));
        }
      } catch (error) {
        console.log('## Error', error);
        console.error(`Get seafarer documents list failed. Error: ${error}`);
      }
      setIsLoading(false);
    })();
  }, [seafarer]);

  const editHandler = async (documentType, seafarerDocumentId) => {
    eventTracker('editDocument', 'Document');
    if (documentType === 'passport') {
      history.push(`/seafarer/${seafarerId}/add/basic#passports`);
    } else if (documentType === 'seamans_book') {
      history.push(`/seafarer/${seafarerId}/add/basic#seaman-books`);
    } else {
      history.push(
        `/seafarer/details/${seafarerId}/id-documents/${documentType}/edit/${seafarerDocumentId}`,
      );
    }
  };

  const deleteHandler = async (id, type) => {
    eventTracker('deleteDocument', 'Document');
    setSelectDocumentID(id);
    setSelectDocumentType(type);
    setIsRemoveDocumentConfirmationModalShow(true);
  };

  const handleDeleteSeafarerDocument = async () => {
    try {
      setIsDisableConfirmDeleteBtn(true);
      setIsModalLoading(true);
      await seafarerService.deleteSeafarerDocument(selectDocumentID);
      const newData =
        selectDocumentType == 'visa'
          ? visaData?.filter((ele) => ele.document.id !== selectDocumentID)
          : indosData?.filter((ele) => ele.document.id !== selectDocumentID);
      selectDocumentType == 'visa' ? setVisaData(newData) : setIndosData(newData);
      setIsDisableConfirmDeleteBtn(false);
      setIsModalLoading(false);
      setIsRemoveDocumentConfirmationModalShow(false);
    } catch (error) {
      setIsModalLoading(false);
      setIsRemoveDocumentConfirmationModalShow(false);
      setModalMessage('Oops something went wrong while deleting seafarer document.');
      console.error(`Delete seafarer Document by ID: ${selectDocumentID} failed. Error: ${error}`);
      setIsDisableConfirmDeleteBtn(false);
    }
  };

  const downloadFile = async (documentType, document, fileName) => {
    if (isLoading) {
      return;
    }
    if (documentType && document && fileName) {
      window.open(
        `https://${window.location.hostname}/seafarer/document/${document.id}/${documentType}`,
      );
    }
    eventTracker('viewDocument', 'ID Document');
  };

  const documentColumn = generateDocumentColumn(downloadFile);

  const editDeleteActionColumn = generateEditDeleteActionColumn(
    showDeleteAndEdit,
    editHandler,
    deleteHandler,
  );

  const editActionColumn = generateEditActionColumn(showDeleteAndEdit, editHandler);

  const passportColumns = generatePassportColumns(documentColumn, editActionColumn);

  const visaColumns = generateVisaColumns(documentColumn, editDeleteActionColumn);

  const seamanBookColumns = generateSeamanBookColumns(documentColumn, editActionColumn);

  const indosColumns = generateIndosColumns(documentColumn, editDeleteActionColumn);

  return (
    <AccessHandlerWrapper hasRoleAccess={roleConfig.seafarer.view.general}>
      <Container>
        <div>
          <Row>
            <Col>
              <div className="font-weight-bold p-2 document-table-title" ref={passportSectionRef}>
                PASSPORT
              </div>
            </Col>
          </Row>
          <Row>
            <Col>
              <DocumentTable
                data={passportData}
                loading={isLoading}
                columns={passportColumns}
                dataTestId="passport-table"
              />
            </Col>
          </Row>
          <div className="details_page__table_head" ref={visaSectionRef}>
            <Row>
              <Col>
                <div className="font-weight-bold p-2">VISA</div>
              </Col>
            </Row>
          </div>
          <Row>
            <Col>
              <DocumentTable
                data={visaData}
                loading={isLoading}
                columns={visaColumns}
                dataTestId="visa-table"
              />
            </Col>
          </Row>

          <div className="details_page__table_head" ref={seamanBookSectionRef}>
            <Row>
              <Col>
                <div className="font-weight-bold p-2">
                  SEAMAN&apos;S BOOK
                  <div className="is-national">* Is National</div>
                </div>
              </Col>
            </Row>
          </div>
          <Row>
            <Col>
              <DocumentTable
                data={seamanBookData}
                loading={isLoading}
                columns={seamanBookColumns}
                dataTestId="seaman-book-table"
              />
            </Col>
          </Row>

          <div className="details_page__table_head" ref={indosSectionRef}>
            <Row>
              <Col>
                <div className="font-weight-bold p-2">INDIAN DATABASE OF SEAFARERS NUMBER</div>
              </Col>
            </Row>
          </div>
          <Row>
            <Col>
              <DocumentTable
                data={indosData}
                loading={isLoading}
                columns={indosColumns}
                dataTestId="indos-table"
              />
            </Col>
          </Row>
          <DocumentDownloadFailedModal
            show={showDownloadFailModal}
            onClose={() => setShowDownloadFailModal(false)}
            title={'Download Failed'}
          >
            <p>{DOC_DOWNLOAD_FAIL_MESSAGE}</p>
          </DocumentDownloadFailedModal>
          {/* route to popup modal for editing documents*/}
          <Switch>
            {documentTypes.ID_DOCUMENTS_TAB_NEW_DOC_TYPE_KEYS.map((doc_type_key) => (
              <Route
                key={doc_type_key}
                exact
                path={`/seafarer/details/:seafarerId/id-documents/${documentTypes.ALL_DOC_TYPES[doc_type_key]}/edit/:documentId`}
              >
                <AddDocumentModal
                  targetId={documentTypes.DOC_FORM_IDS[doc_type_key]}
                  seafarerPersonId={seafarer.seafarer_person_id}
                  history={history}
                  docId={documentId}
                  dropdownData={dropdownData}
                  eventTracker={eventTracker}
                ></AddDocumentModal>
              </Route>
            ))}
          </Switch>
        </div>
      </Container>

      <ConfirmActionModalView
        show={isRemoveDocumentConfirmationModalShow}
        onClose={() => setIsRemoveDocumentConfirmationModalShow(false)}
        onConfirm={handleDeleteSeafarerDocument}
        title={'Confirm Deleting the document?'}
        message={'Are you sure deleting the document?'}
        isDisableConfirmDeleteBtn={isDisableConfirmDeleteBtn}
        isModalLoading={isModalLoading}
      />
      <ErrorDisplayModal onHideModalMessage={onHideModalMessage} modalMessage={modalMessage} />
    </AccessHandlerWrapper>
  );
};

export default DocumentsPage;
