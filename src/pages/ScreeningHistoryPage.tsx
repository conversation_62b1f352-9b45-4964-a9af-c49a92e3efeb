import React, { useState, useEffect, useMemo } from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import { useParams, useHistory } from 'react-router-dom';
import styleGuide from '../styleGuide';
const { Icon } = styleGuide;
import screeningService from '../service/screening-service';
import seafarerService from '../service/seafarer-service';
import Remarks from '../component/common/Remarks';
import { approvalStatuses } from '../model/constants';
import { getShortDate, getSortOrder } from '../util/view-utils';
import Spinner from '../component/common/Spinner';
import AccessHandlerWrapper from '../component/common/AccessHandlerWrapper';
import FileUploadComponents from '../component/common/FileUploadModalView';
import { BreadcrumbHeader } from '../component/BreadcrumpHeader';
import { useAccess } from '@src/component/common/Access';

const { UploadedFilesModalView } = FileUploadComponents;

const PageHeader = ({ breadCrumbsItems, visitDetailsPage }) => {
  return (
    <Row>
      <Col>
        <div className="flex-between">
          <BreadcrumbHeader
            items={breadCrumbsItems}
            activeItem="Screening History"
            onClick={() => {}}
          />
          <Icon icon="close" size={30} onClick={visitDetailsPage} />
        </div>
      </Col>
    </Row>
  );
};

const TableHeaderRow = () => {
  return (
    <tr key="header">
      <th scope="col">Date</th>
      <th scope="col">Approval Group</th>
      <th scope="col">Approver Name</th>
      <th scope="col">Status</th>
      <th scope="col">Remarks</th>
      <th scope="col">Documents</th>
    </tr>
  );
};

const TableDataRows = ({ approvalData, showUploadedFilesModal }) => {
  const nonPendingApprovals = approvalData.filter(
    (approval) => approval.approval_status !== approvalStatuses.PENDING,
  );
  return nonPendingApprovals.map((item) => {
    return (
      <tr key={item.id} className={item.approval_status}>
        <td>{getShortDate(item.updated_at)}</td>
        <td>{item.approval_group}</td>
        <td>{item.approver_name}</td>
        <td>{item.approval_status}</td>
        <td>{item.remarks ? <Remarks remarksData={item.remarks} /> : ''}</td>
        <td>
          {item.document.length > 0 ? (
            <button
              type="button"
              className="btn btn-link p-0 text-left"
              onClick={showUploadedFilesModal.bind(this, item.id)}
            >
              <u>View</u>
            </button>
          ) : null}
        </td>
      </tr>
    );
  });
};

const Table = ({
  approvalData,
  showUploadedFiles,
  uploadedDocuments,
  setShowUploadedFiles,
  showUploadedFilesModal,
}) => {
  return (
    <Row>
      <Col>
        <table className="table">
          <thead>
            <TableHeaderRow />
          </thead>
          <tbody>
            {approvalData ? (
              <TableDataRows
                approvalData={approvalData}
                showUploadedFilesModal={showUploadedFilesModal}
              />
            ) : (
              <Spinner />
            )}

            <tr></tr>
          </tbody>
        </table>
        <UploadedFilesModalView
          show={showUploadedFiles}
          uploadedFiles={uploadedDocuments}
          onClose={() => setShowUploadedFiles(false)}
        />
      </Col>
    </Row>
  );
};

const ScreeningHistoryPage = () => {
  const history = useHistory();
  let { seafarerId } = useParams();
  const { roleConfig } = useAccess();
  const [seafarerData, setSeafarerData] = useState(null);
  const [approvalData, setApprovalData] = useState(null);
  const [showUploadedFiles, setShowUploadedFiles] = useState(false);
  const [uploadedDocuments, setUploadedDocuments] = useState({});

  useEffect(() => {
    (async () => {
      try {
        const seafarerResponse = await seafarerService.getSeafarerFieldsData(seafarerId, [
          'first_name',
          'last_name',
          'seafarer_person.id',
        ]);
        setSeafarerData(seafarerResponse.data.results[0]);
        let approvalResponse = await screeningService.getScreeningData(
          seafarerResponse.data.results[0]['seafarer_person.id'],
        );
        setApprovalData(approvalResponse?.data?.sort(getSortOrder('updated_at')));
      } catch (error) {
        console.error(`Get seafarer by ID: ${seafarerId} failed. Error: ${error}`);
      }
    })();
  }, []);

  const visitDetailsPage = () => {
    history.push(`/seafarer/details/${seafarerId}/general`);
  };

  const breadCrumbsItems = useMemo(
    () => [
      { title: 'Seafarer', label: 'To List Page', link: '/seafarer/passed' },
      {
        title: seafarerData ? `${seafarerData.first_name} ${seafarerData.last_name}` : '- - -',
        label: 'Details',
        link: `/seafarer/details/${seafarerId}/general`,
      },
      { title: 'Screening History', label: 'Screening History', link: '#' },
    ],
    [seafarerData],
  );

  const showUploadedFilesModal = (approvalId) => {
    prepareDocumentsData(approvalId);
    setShowUploadedFiles(true);
  };

  const prepareDocumentsData = (approvalId) => {
    (approvalData ?? []).forEach((item) => {
      if (item.id === approvalId) {
        setUploadedDocuments(item.document);
      }
    });
  };

  return (
    <AccessHandlerWrapper hasRoleAccess={roleConfig.seafarer.screening.view}>
      <Container className="screening_history">
        <PageHeader breadCrumbsItems={breadCrumbsItems} visitDetailsPage={visitDetailsPage} />
        <Table
          approvalData={approvalData}
          showUploadedFiles={showUploadedFiles}
          uploadedDocuments={uploadedDocuments}
          setShowUploadedFiles={setShowUploadedFiles}
          showUploadedFilesModal={showUploadedFilesModal}
        />
      </Container>
    </AccessHandlerWrapper>
  );
};

export default ScreeningHistoryPage;
