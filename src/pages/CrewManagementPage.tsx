/* eslint-disable react/prop-types */
import React, { useState, useEffect, useCallback, useContext, useMemo } from 'react';
import { useHistory, useParams } from 'react-router-dom';
import moment from 'moment';
import { Col, Row, Form, Container, Button } from 'react-bootstrap';
import _, { omit, uniqBy } from 'lodash';
import crewPlannerService from '@src/service/crew-planner';
import { Itinerary } from '@src/types/itinerary';
import { SendEmailModal } from '@src/styleGuide';
import PlannerKPI from '@src/component/CrewPlanner/PlannerKPI';
import { useVesselKpiStatusState } from '@src/hooks/useStatusState';
import AccessHandlerWrapper from '../component/common/AccessHandlerWrapper';
import vesselService from '../service/vessel-service';
import './scss/crew-list-page.scss';
import Select from '../component/ManagedVessels/Select';
import { formatValue } from '../util/view-utils';
import CrewManagementHeaderSection from '../component/CrewManagement/HeaderSection';
import { CREW_MANAGEMEMT_TAB_LIST } from '../model/TabData';
import CrewManagementContentSection from '../component/CrewManagement/CrewManagementContentSection';
import Spinner from '../component/common/Spinner';
import { TabWrapper } from '../component/common/TabWrapper';
import AutoDismissibleAlert from '../component/common/AutoDismissibleAlert';
import seafarerService, { getVesselBudgetKpi } from '../service/seafarer-service';
import { dateAsDash, dateAsString, stringAsDate } from '../model/utils';
import PlanningActionModal from '../component/CrewManagement/PlanningActionModal';
import {
  CREW_PLANNING_STATUS,
  CREW_PLANNING_TYPE,
  STATUS,
  CREW_PLANNING_RIGHT_SIDE,
} from '../constants/crewPlanner';
import { seafarerStatus } from '../model/constants';
import { RECOMMENDED_ERROR_KEYS } from './Details';
import getSymbolFromCurrency from 'currency-symbol-map';
import { UserInfo } from '@src/types/keycloakUser';
import { generateReplacementEmailContent } from '@src/component/CrewPlanner/utils';
import useRequest from '@src/hooks/useRequest';
import keycloakService from '@src/service/keycloak-service';
import { User } from './AdminAccess/types';
import { sortAndFilterList } from './AdminAccess/utils';
import { SendEmailPayload } from '@src/types/notification-service';
import {
  NotificationMessagePayload,
  parseMessageToHTML,
  sendEmailNotificationMessage,
} from '@src/service/notification-service';
import { useAccess } from '@src/component/common/Access';

const dateFormat = 'yyyy-MM-DD';
const PAGE_SIZE = 20;
interface AlertMessage {
  message: string;
  variant?: string;
  className?: string;
}
interface CrewMangementContextValue {
  itineraryList: Itinerary[];
  refetchSimulateItinerary: () => Promise<void>;
  crewList: any[];
  visitSeafarerDetail: Function;
  setSelectedActionSeafarer: Function;
  refresh: Function;
  setActiveOverlaySeafarerId: Function;
  activeOverlaySeafarerId: number | null;
  isCrewListLoading: boolean | null;
  setAlertMessage: (value: AlertMessage) => void;
  vesselData: any;
  upsertClubbedCrew: (crewId: number, itinerary: Itinerary) => Promise<void>;
  setCrewListData: Function;
  userProfile: UserInfo;
  onRequestReplacement: Function;
  fetchMoreData: Function;
  hasMoreData: boolean;
}

const CrewMangementContext = React.createContext<CrewMangementContextValue>(
  {} as CrewMangementContextValue,
);

export const useCrewMangementContext = () => useContext(CrewMangementContext);

const mergeSimulateItineraryList = (itineraryList: any, simulatedItineraryList: Itinerary[]) => {
  const originalItineraryList =
    itineraryList.filter((itinerary) => !itinerary?.isSimulatedItinerary) ?? [];
  const modifiedSimiluatedItineraryList = simulatedItineraryList
    .map((item) => ({
      ...item,
      vessel_ownership_id: item.ownership_id,
      estimated_arrival: item.date,
      isSimulatedItinerary: true,
    }))
    .filter((item) => item.port);
  const finalItineraryList = [...originalItineraryList, ...modifiedSimiluatedItineraryList];
  // sort by date
  return finalItineraryList.sort((a, b) => {
    return new Date(b.estimated_arrival)?.valueOf() - new Date(a.estimated_arrival)?.valueOf();
  });
};

const CrewManagementPage = ({ ga4react }) => {
  const history = useHistory();

  const [loading, setLoading] = useState(false);
  const { roleConfig, userProfile } = useAccess();
  const params = useParams();
  const hasRoleAccess = roleConfig?.seafarer?.view?.crewPlannerSeafarer;
  const hasEditAccess = roleConfig?.seafarer?.edit?.crewPlannerVessel;
  const { vesselOwnershipId } = params;
  const [vesselOwnershipIdParam, setVesselOwnershipIdParam] = useState(vesselOwnershipId);

  const [vesselOwnershipListData, setVesselOwnershipListData] = useState<any>(null);

  const [vesselData, setVesselData] = useState<any>(null);
  const [vesselItineraryData, setVesselItineraryData] = useState<any[]>([]);
  const [simulatedItineraryData, setSimulatedItineraryData] = useState<any[]>([]);
  const [crewListData, setCrewListData] = useState<any[]>([]);
  const [isCrewListLoading, setIsCrewListLoading] = useState<boolean>(false);
  const [relieverStatusData, setRelieverStatusData] = useState<any>(null);
  const [errorStatusCode, setErrorStatusCode] = useState(null);
  const [alertMessage, setAlertMessage] = useState<AlertMessage | null>({
    message: '',
  });
  const [totalFpdBudget] = useVesselKpiStatusState('-', 'Total FPD Budget');
  const [totalExpenses] = useVesselKpiStatusState('-', 'Total Expenses');
  const [crewWagesYearly, setCrewWagesYearly] = useVesselKpiStatusState('-', 'Crew Wages (Yearly)');
  const [crewTravelYearly] = useVesselKpiStatusState('-', 'Crew Travel (Yearly)');
  const [avgCrewWages, setAvgCrewWages] = useVesselKpiStatusState('-', 'Avg. Crew Wages');
  const [avgCrewExpenses] = useVesselKpiStatusState('-', 'Avg. Crew Expenses');

  const [vesselsDropdownData, setVesselsDropdownData] = useState([]);
  const [selectedRequestedReplacement, setSelectedRequestedReplacement] = useState<any>(null);
  const { data } = useRequest<User[], any>(keycloakService.getUsers, {
    initialArgs: [{ role: 'uma_authorization' }],
  });
  const userList = useMemo(() => {
    return sortAndFilterList(data ?? []).filter((item) => !!item.email);
  }, [data]);

  const [isLoadingReject, setIsLoadingReject] = useState(false);
  const [selectedActionSeafarer, setSelectedActionSeafarer] = useState<{
    seafarer: any;
    action:
    | 'rejected'
    | 'unclubbed'
    | 'club_confirmed'
    | 'relieve_without_replacement'
    | 'cancel_relieve_without_replacement'
    | 'cancel_additional_crew'
    | null;
  }>({
    seafarer: null,
    action: null,
  });
  const [activeOverlaySeafarerId, setActiveOverlaySeafarerId] = useState(null);

  const ga4EventTrigger = (action: string, category: string, label: string) => {
    try {
      ga4react?.event(action, _.toString(label), category, false);
    } catch (error) {
      console.log(error);
    }
  };

  const eventTracker = (type: string, value?: string | number) => {
    const confirmButtonAction = 'Confirm button';
    switch (type) {
      case 'switchVessel':
        ga4EventTrigger('Switch Vessel Dropdown', 'POD Manager - Crew Details', _.toString(value));
        break;
      case 'viewVesselDetails':
        ga4EventTrigger(
          'View Vessel Details Button',
          'POD Manager - Crew Details',
          _.toString(vesselData?.name),
        );
        break;
      case 'seafarerNameLink':
        ga4EventTrigger('Seafarer Name Link', 'POD Manager - Crew Details', _.toString(value));
        break;
      case 'refreshDetails':
        ga4EventTrigger('Refresh Details', 'POD Manager - Crew Details', 'Click');
        break;
      case 'clickEllipsis':
        ga4EventTrigger('Actions', 'POD Manager - Crew Details Card', 'Click Ellipsis');
        break;
      case 'requestReplacement':
        ga4EventTrigger(
          'Request Replacement',
          'POD Manager - Crew Details Card Actions',
          _.toString(value),
        );
        break;
      case 'viewDetails':
        ga4EventTrigger(
          'View Details',
          'POD Manager - Crew Details Card Actions',
          _.toString(value),
        );
        break;
      case 'seafarerReject':
        ga4EventTrigger(confirmButtonAction, 'POD Manager - Reject Planning Modal', 'Click');
        break;
      case 'addPort':
        ga4EventTrigger('Add Port button', 'POD Manager - Adding Port Modal', _.toString(value));
        break;
      case 'confirmClubbing':
        ga4EventTrigger(confirmButtonAction, 'POD Manager - Confirm Clubbing Modal', 'Click');
        break;
      case 'relieveWithoutReplacement':
        ga4EventTrigger(
          confirmButtonAction,
          'POD Manager - Relieve without Replacement Modal',
          'Click',
        );
        break;
      case 'deletePlaceHolder':
        ga4EventTrigger('Delete button', 'POD Manager - Delete Placeholder Modal', 'Click');
        break;
      case 'crewViewProfile':
        ga4EventTrigger(
          'View Profile button',
          'POD Manager - Crew Change Details Overlay',
          _.toString(value),
        );
        break;
      case 'requestStatusForMissingRank':
        ga4EventTrigger('Clicking Request Status', 'POD Manager - Mssing Rank Status', 'Click');
        break;
      case 'clickUnclub':
        ga4EventTrigger('Clicks Confirm', 'POD Manager - Unclub', 'Click');
        break;
      case 'addAdditionalCrew':
        ga4EventTrigger('Clicks Add Crew', 'POD Manager - Additional Crew', 'Click');
        break;
      case 'requestReplacementTimeline':
        ga4EventTrigger('Request Replacement', 'POD Manager - Clubbing Card', 'Click');
        break;
      default:
        break;
    }
  };

  const getDateStringForItinerary = () => {
    const oneYearLater = new Date();
    oneYearLater.setFullYear(oneYearLater.getFullYear() + 1);
    const today = new Date();

    return `${moment(today).subtract(1, 'year').format(dateFormat)},${moment(oneYearLater).format(
      dateFormat,
    )}`;
  };

  const handleDeletedItinerary = (crewList, itineraryList) => {
    const itineraryIds = new Set(
      itineraryList
        .filter((itinerary) => !itinerary.isSimulatedItinerary) // get real itinerary only
        .map((itinerary) => itinerary.id),
    );
    const crewItineraryIds = new Set(
      crewList
        .filter((crew) => crew.crew_planning?.itinerary_id)
        .map((crew) => crew.crew_planning.itinerary_id),
    );
    const deletedItinerayIds = Array.from(crewItineraryIds).filter((id) => !itineraryIds.has(id));
    if (deletedItinerayIds.length > 0) {
      const deletedItineraries = deletedItinerayIds.map((id) => ({
        id,
        port: 'Deleted Port (by Master)',
        estimated_arrival: null,
        isDeletedItineraryByMaster: true,
      }));
      return [...deletedItineraries, ...itineraryList];
    }
    return itineraryList;
  };

  const mapItineraryData = (itineraryList: any, crewList: any) => {
    const itineraryListHandled = handleDeletedItinerary(crewList, itineraryList);

    if (!itineraryListHandled?.length) {
      return [];
    }

    return itineraryListHandled.map((itinerary) => ({
      id: itinerary.id,
      port: itinerary.port,
      port_id: itinerary.port_id,
      estimated_arrival: itinerary.estimated_arrival,
      estimated_arrival_formatted: itinerary.estimated_arrival
        ? dateAsString(stringAsDate(itinerary.estimated_arrival))
        : 'N/A',
      isSimulatedItinerary: itinerary.isSimulatedItinerary,
      isDeletedItineraryByMaster: itinerary.isDeletedItineraryByMaster,
      crewPlanning: crewList?.filter(
        (crew) =>
          (crew.crew_planning?.simulate_itinerary_id === itinerary.id ||
            crew.crew_planning?.itinerary_id === itinerary.id) &&
          CREW_PLANNING_RIGHT_SIDE.includes(crew.crew_planning?.planning_status),
      ),
    }));
  };

  const loadRelieverStatus = async (crewList) => {
    try {
      if (crewList?.length > 0) {
        const relievers = crewList?.map((c) => c?.crew_planning?.reliever_id).filter((r) => !!r);
        const relieverQueries = relievers?.map((c) => {
          return seafarerService.getSeafarer(c, false, '?values=status_history');
        });
        const recommendationCheckQueries = relievers?.map((c) => {
          return seafarerService.getRecommendedChecks(c);
        });

        const { relieverList, recommendationCheckList } = await Promise.all([
          Promise.all(relieverQueries),
          Promise.all(recommendationCheckQueries),
        ]).then(([first, second]) => ({
          relieverList: first,
          recommendationCheckList: second,
        }));

        const isCrewAssignmentApproved = relieverList?.map((l) => {
          const recommendation = l.data?.seafarer_person?.seafarer_status_history?.find(
            (ssh) =>
              (ssh.is_p1_history === false &&
                ssh.seafarer_journey_status === seafarerStatus.CREW_ASSIGNMENT_APPROVED) ||
              (ssh.is_current_status === true &&
                ssh.seafarer_journey_status === seafarerStatus.TRAVELLING),
          );
          return {
            id: l.data?.id,
            isCrewAssignmentApproved: !!recommendation,
          };
        });

        const relieverStatus = recommendationCheckList?.map((l, index) => {
          let isDocumentAndTrainingComplete = true;
          if (l?.data?.error) {
            const messages = l?.data?.message.split('||');
            messages.forEach((message) => {
              if (message !== RECOMMENDED_ERROR_KEYS.error_on_leave) {
                isDocumentAndTrainingComplete = false;
              }
            });
          }

          const id = relievers[index];
          return {
            id,
            isDocumentAndTrainingComplete,
            isCrewAssignmentApproved: isCrewAssignmentApproved.find((r) => r.id === id)
              ?.isCrewAssignmentApproved,
          };
        });

        if (relieverStatus) {
          setRelieverStatusData(relieverStatus);
        }
      }
    } catch (error) {
      console.error(`Error when loadRelieverData, error: ${error}`);
      setErrorStatusCode(error?.response?.status);
      setAlertMessage({
        message: `Error when loading reliever data, error: ${error?.message}`,
        variant: 'danger',
      });
    }
  };

  const refreshCrewList = async () => {
    try {
      setIsCrewListLoading(true);
      const vesselId = vesselData?.vessel?.id as number;
      const currentDate = dateAsDash(new Date());
      const crewListQueryParams = `vessel_id=${vesselId}&crew_list_status_date=${currentDate}&orderBy=seafarer_person:seafarer_status_history.expected_contract_end_date`;
      const intineraryDateQueryString = getDateStringForItinerary();
      const [
        itinerary,
        crewList,
        missingPersonnelResponse,
        additionalRequestResponse,
      ] = await Promise.all([
        vesselService.getVesselItinerary(vesselOwnershipId, intineraryDateQueryString, PAGE_SIZE),
        seafarerService.getCrewList(crewListQueryParams),
        seafarerService.getMissingRanks({
          vessel_ownership_id: vesselData.id,
        }),
        seafarerService.getAdditionalCrewRequest({
          vessel_ownership_id: vesselData.id,
          crew_planning_status: 'unplanned,unclubbed,clubbed,club_confirmed,completed,expired',
        }),
      ]);
      const vesselItineraryList = itinerary?.data?.results ?? [];

      const fromDate = vesselItineraryList?.[vesselItineraryList?.length - 1]?.estimated_arrival ? moment(vesselItineraryList?.[vesselItineraryList?.length - 1]?.estimated_arrival).format('YYYY-MM-DD') : '';
      const toDate = moment().add(1, 'year').format('YYYY-MM-DD');
      const simulatedItinerary = await crewPlannerService.getSimulateItinerary(vesselId, `${fromDate},${toDate}`);

      if (vesselItineraryList?.length < PAGE_SIZE) {
        setHasMoreData(false);
      }
      const simulatedItineraryList = simulatedItinerary?.data ?? [];
      const missingPersonnelList = missingPersonnelResponse?.data?.results ?? [];
      const additionalRequestList =
        additionalRequestResponse?.data?.results?.map((s) => ({
          ...s,
          isAdditionalRequest: true,
        })) ?? [];

      const signedOffSeafarersCrewPlanning = await crewPlannerService.queryCrewPlan(
        vesselItineraryList.map((r) => r.id),
        simulatedItineraryList.map((r) => r.id),
        [vesselData?.id],
        null,
        null,
        [
          CREW_PLANNING_STATUS.unclubbed,
          CREW_PLANNING_STATUS.clubbed,
          CREW_PLANNING_STATUS.club_confirmed,
          CREW_PLANNING_STATUS.completed,
          CREW_PLANNING_STATUS.expired,
        ],
      );
      const formattedSignedOffCrewListData = signedOffSeafarersCrewPlanning.map((c) => ({
        ...c.seafarer,
        crew_planning: { ...omit(c, ['seafarer', 'reliever']), reliever: c.reliever },
      }));

      const updatedCrewList = constructUniquCrewList([
        ...missingPersonnelList,
        ...additionalRequestList,
        ...(crewList?.data?.results ?? []),
        ...formattedSignedOffCrewListData,
      ]);

      setVesselItineraryData(vesselItineraryList);
      setSimulatedItineraryData(simulatedItineraryList);

      setIsCrewListLoading(false);
      if (updatedCrewList?.length > 0) {
        setCrewListData(updatedCrewList);
        loadRelieverStatus(updatedCrewList);
      }
    } catch (error) {
      setErrorStatusCode(error?.response?.status);
      setAlertMessage({
        message: `Error when loading Crew list, error: ${error?.message}`,
        variant: 'danger',
      });
    } finally {
      setIsCrewListLoading(false);
    }
  };
  const refreshSimulateItinerary = async () => {
    const { data } = await crewPlannerService.getSimulateItinerary(
      vesselData?.vessel?.id as number,
    );
    if (data) {
      setSimulatedItineraryData(data);
    }
  };

  useEffect(() => {
    setLoading(true);
    const loadVesselData = async () => {
      try {
        setErrorStatusCode(null);
        setAlertMessage({ message: '' });

        let vesselList = vesselOwnershipListData;
        if (!vesselList) {
          const paramV2 = '&status=active&status=pending_handover&flatten=true&fmlFilter=podManager&orderBy=name asc';
          const vesselV2Response = await vesselService.getVesselOwnershipV2(paramV2);
          if (vesselV2Response?.data?.results?.length) {
            vesselList = vesselV2Response?.data?.results;
            setVesselOwnershipListData(vesselList);
          } else {
            const ownershipParam = '&status=active&status=pending_handover&flatten=true&orderBy=name asc';
            const result = await vesselService.getVesselOwnershipV2(ownershipParam);
            vesselList = result?.data?.results;
            setVesselOwnershipListData(vesselList);
          }
        }

        if (vesselList) {
          const selectedVesselResponse = vesselList.find((e) => e?.id == vesselOwnershipId);

          const vesselDropdownData = vesselList.map((vessel) => {
            return { id: vessel?.id, value: formatValue(`${vessel.name}(${vessel.id})`) };
          });

          setLoading(false);
          setVesselData(selectedVesselResponse);
          setVesselsDropdownData(vesselDropdownData);
        }
      } catch (error) {
        console.error(`Error when loading vessel list, error: ${error.message}`);
        setErrorStatusCode(error?.response?.status);
        setAlertMessage({
          message: `Error when loading vessel list, error: ${error?.message}`,
          variant: 'danger',
        });
        setLoading(false);
      }
    };

    loadVesselData();
  }, [vesselOwnershipIdParam]);

  useEffect(() => {
    const vesselId = vesselData?.vessel?.id;
    if (vesselId) {
      refreshCrewList();
      fetchVesselKpi(vesselId);
    }
  }, [vesselData]);

  const fetchVesselKpi = async (vesselId: number) => {
    if (!vesselId) return;
    setCrewWagesYearly((prevState) => ({ ...prevState, isLoading: true }));
    setAvgCrewWages((prevState) => ({ ...prevState, isLoading: true }));
    const result = await getVesselBudgetKpi(vesselId);
    const currencySymbol = getSymbolFromCurrency(
      vesselData?.vessel?.misc_currency?.value?.toUpperCase(),
    );
    setCrewWagesYearly((prevState) => ({
      ...prevState,
      value: `${currencySymbol}${parseFloat(result?.data?.amount_total)?.toFixed(2)} /-`,
      isLoading: false,
    }));
    const monthsFromStartOfYear = new Date().getMonth();
    setAvgCrewWages((prevState) => ({
      ...prevState,
      value: `${currencySymbol}${(result?.data?.amount_total / monthsFromStartOfYear).toFixed(
        2,
      )} /-`,
      isLoading: false,
    }));
  };

  const upsertClubbedCrew = async (crewId: number, itinerary: Itinerary, planId: number) => {
    try {
      setErrorStatusCode(null);
      setAlertMessage({ message: '' });
      const crewPlan = crewListData?.find((c) => c.id === crewId && c?.crew_planning?.id === planId)?.crew_planning;
      const itineraryId = itinerary?.id;
      const data = {
        itinerary_id: !itinerary?.isSimulatedItinerary ? itineraryId : null,
        simulate_itinerary_id: itinerary?.isSimulatedItinerary ? itineraryId : null,
        planning_status: CREW_PLANNING_STATUS.clubbed,
      };
      const itineraryDate = itinerary?.estimated_arrival
        ? moment(itinerary?.estimated_arrival).format(dateFormat)
        : null;
      if (crewPlan) {
        const payload = {
          id: crewPlan?.id,
          itinerary_date: itineraryDate,
          ...data,
        };
        await crewPlannerService.updateCrewPlan(payload);
        const updatedCrewList = crewListData?.map((crew) => {
          if (crew.id === crewId && crew?.crew_planning?.id === planId) {
            return {
              ...crew,
              crew_planning: {
                ...crew.crew_planning,
                ...data,
                planning_type: CREW_PLANNING_TYPE.relieve,
              },
            };
          }
          return crew;
        });
        setCrewListData(updatedCrewList);
        setAlertMessage({
          message: 'Seafarers clubbed and moved to the Clubbing section.',
          variant: 'success',
        });
      } else {
        const payload = {
          seafarer_id: crewId,
          vessel_id: vesselData?.vessel?.id,
          ownership_id: Number(vesselOwnershipIdParam),
          planning_type: CREW_PLANNING_TYPE.relieve,
          itinerary_date: itineraryDate,
          ...data,
        };
        const insertedCrewPlan = await crewPlannerService.createCrewPlan(payload);
        const updatedCrewList = crewListData?.map((crew) => {
          if (crew.id === crewId && crew?.crew_planning?.id === planId) {
            return {
              ...crew,
              crew_planning: {
                ...crew.crew_planning,
                ...insertedCrewPlan.data,
              },
            };
          }
          return crew;
        });
        setCrewListData(updatedCrewList);

        setAlertMessage({
          message: 'Seafarers clubbed and moved to the Clubbing section.',
          variant: 'success',
        });
      }
    } catch (error) {
      console.error(`Error when updating crew plan, error: ${error}`);
      setErrorStatusCode(error?.response?.status);
      setAlertMessage({
        message: `Error when updating crew plan, error: ${error?.message}`,
        variant: 'danger',
      });
    }
  };

  const routeToVesselDetailPage = () => {
    if (vesselOwnershipId) {
      eventTracker('viewVesselDetails', '');
      window.open(`/vessel/ownership/details/${vesselOwnershipId}`, '_blank');
    }
  };

  const handleDropdownChange = (e, selectedItem: any) => {
    if (selectedItem) {
      eventTracker('switchVessel', selectedItem.value);
      setVesselOwnershipIdParam(selectedItem.id);
      const selectedVessel = vesselOwnershipListData?.find((o) => o?.name === selectedItem);
      if (selectedVessel?.length) {
        setVesselData(selectedVessel);
      }
      history.push(`/seafarer/crew-planner/vessel/${selectedItem.id}`);
    }
  };

  const handleTabSelect = () => {
    // will be added later
  };

  const handleConfirmPlanningAction = async (e) => {
    try {
      setIsLoadingReject(true);
      setErrorStatusCode(null);
      setAlertMessage({ message: '' });
      const { seafarer, action } = selectedActionSeafarer;
      const planId = seafarer?.crew_planning?.id;
      const payload = {
        id: planId,
        planning_status: CREW_PLANNING_STATUS[action],
        ...(seafarer?.isAdditionalRequest && { additional_request_id: seafarer?.id }),
      };
      if (action === STATUS.unclubbed && !seafarer?.crew_planning?.reliever?.id) {
        payload.planning_status = CREW_PLANNING_STATUS.canceled;
      }
      if (action === 'cancel_relieve_without_replacement') {
        payload.planning_status = CREW_PLANNING_STATUS.canceled;
      }
      if (action === 'cancel_additional_crew') {
        await seafarerService.cancelAdditionalRequest(seafarer?.id);
      } else if (action === 'relieve_without_replacement') {
        const relievePayload = {
          id: planId,
          planning_type: CREW_PLANNING_TYPE.offsigner,
        };
        await crewPlannerService.updateCrewPlan(relievePayload);
      } else {
        await crewPlannerService.updateCrewPlan(payload);
      }
      let updatedCrewList = [];
      let successMsg = '';
      if (action === STATUS.rejected) {
        eventTracker('seafarerReject', '');
        updatedCrewList = crewListData.map((c) => {
          if (c.id === seafarer?.id && c?.crew_planning?.id === planId) {
            return {
              ...c,
              crew_planning: null,
            };
          }
          return c;
        });
        successMsg = 'Crew Plan Rejected!';
      } else if (action === STATUS.unclubbed) {
        if (!seafarer?.crew_planning?.reliever?.id) {
          updatedCrewList = crewListData.map((c) => {
            if (c.id === seafarer?.id && c?.crew_planning?.id === planId) {
              return {
                ...c,
                crew_planning: null,
              };
            }
            return c;
          });
        } else {
          updatedCrewList = crewListData.map((c) => {
            if (c.id === seafarer?.id && c?.crew_planning?.id === planId) {
              return {
                ...c,
                crew_planning: {
                  ...c.crew_planning,
                  planning_status: CREW_PLANNING_STATUS.unclubbed,
                  itinerary_id: null,
                  simulated_itinerary_id: null,
                },
              };
            }
            return c;
          });
        }
        successMsg = 'Seafarers Unclubbed and moved to the Unclubbed section.';
      } else if (action === STATUS.club_confirmed) {
        eventTracker('confirmClubbing', '');
        updatedCrewList = crewListData.map((c) => {
          if (c.id === seafarer?.id && c?.crew_planning?.id === planId) {
            return {
              ...c,
              crew_planning: {
                ...c.crew_planning,
                planning_status: CREW_PLANNING_STATUS.club_confirmed,
              },
            };
          }
          return c;
        });
        successMsg = 'Clubbing confirmed.';
      } else if (action === 'relieve_without_replacement') {
        eventTracker('relieveWithoutReplacement', '');
        updatedCrewList = crewListData.map((c) => {
          if (c.id === seafarer?.id && c?.crew_planning?.id === planId) {
            return {
              ...c,
              crew_planning: {
                ...c.crew_planning,
                planning_type: CREW_PLANNING_TYPE.offsigner,
              },
            };
          }
          return c;
        });
        successMsg = 'Relieved seafarer without Replacement';
      } else if (action === 'cancel_relieve_without_replacement') {
        updatedCrewList = crewListData.map((c) => {
          if (c.id === seafarer?.id && c?.crew_planning?.id === planId) {
            return {
              ...c,
              crew_planning: {
                ...c.crew_planning,
                planning_status: CREW_PLANNING_STATUS.canceled,
              },
            };
          }
          return c;
        });
        successMsg = 'Cancelled Relieve seafarer without Replacement';
      } else if (action === 'cancel_additional_crew') {
        updatedCrewList = crewListData.filter((c) => c.id !== seafarer?.id);
        successMsg = 'Additional Crew requirement deleted successfully!';
      }

      setCrewListData(updatedCrewList);

      setSelectedActionSeafarer({ seafarer: null, action: null });
      setIsLoadingReject(false);
      setAlertMessage({ message: successMsg, variant: 'success' });
    } catch (error) {
      console.error(`Error when rejecting plan, error: ${error}`);
      setErrorStatusCode(error?.response?.status);
      setAlertMessage({
        message: `Error when rejecting plan, error: ${error?.message}`,
        variant: 'danger',
      });
    }
  };

  const visitSeafarerDetail = useCallback((seafarerId) => {
    window.open(`/seafarer/details/${seafarerId}/general`, '_blank');
  }, []);
  const [pageNo, setPageNo] = useState(0);
  const [hasMoreData, setHasMoreData] = useState(true);
  const fetchMoreData = async () => {
    if (!hasMoreData) return;
    const newPageNo = pageNo + 1;
    const intineraryDateQueryString = getDateStringForItinerary();
    const response = await vesselService.getVesselItinerary(
      vesselOwnershipId,
      intineraryDateQueryString,
      PAGE_SIZE,
      newPageNo,
    );
    const vesselItineraryList = response.data.results;
    if (vesselItineraryList?.length) {
      const fromDate = vesselItineraryList[vesselItineraryList.length - 1].estimated_arrival;
      const toDate =
        vesselItineraryData[vesselItineraryData.findLastIndex((i) => i?.estimated_arrival)]
          .estimated_arrival;
      const vesselId = vesselData?.vessel?.id as number;

      const simulateItineraryResp = await crewPlannerService.getSimulateItinerary(
        vesselId,
        `${fromDate},${toDate}`,
      );
      const simulateItinerary = simulateItineraryResp.data;

      const crewPlanning = await crewPlannerService.queryCrewPlan(
        vesselItineraryList.map((r) => r.id),
        simulateItinerary.map((r) => r.id),
        [vesselData?.id],
        null,
        null,
        [
          CREW_PLANNING_STATUS.unclubbed,
          CREW_PLANNING_STATUS.clubbed,
          CREW_PLANNING_STATUS.club_confirmed,
          CREW_PLANNING_STATUS.completed,
          CREW_PLANNING_STATUS.expired,
        ],
      );

      setVesselItineraryData([...vesselItineraryData, ...vesselItineraryList]);
      setSimulatedItineraryData([...simulatedItineraryData, ...simulateItinerary]);
      const formattedCrewListData = crewPlanning.map((c) => ({
        ...c.seafarer,
        crew_planning: { ...omit(c, ['seafarer', 'reliever']), reliever: c.reliever },
      }));

      setCrewListData(constructUniquCrewList([...formattedCrewListData, ...crewListData]));
    }

    if (vesselItineraryList.length < 20) {
      setHasMoreData(false);
    } else {
      setPageNo(newPageNo);
    }
  };
  const constructUniquCrewList = (_crewListData) => {
    const crewListWithoutPlan = _crewListData.filter((c) => !c?.crew_planning?.id);
    const crewListWithPlan = _crewListData.filter((c) => c?.crew_planning?.id);
    return [...uniqBy(crewListWithPlan, 'crew_planning.id'), ...crewListWithoutPlan];
  };
  const itineraryData = useMemo(() => {
    const mergedItineraryList = mergeSimulateItineraryList(
      vesselItineraryData,
      simulatedItineraryData,
    );
    return mapItineraryData(mergedItineraryList, crewListData);
  }, [crewListData, simulatedItineraryData, vesselItineraryData]);
  const onRequestReplacement = (payload: {
    seafarerRank: string;
    isSeafarerMissingPersonnel: boolean;
    isAdditionalCrewRequest: boolean;
  }) => {
    setSelectedRequestedReplacement(payload);
  };
  const handleEmailSubmit = useCallback(
    async (emailValues: SendEmailPayload) => {
      try {
        const { selectedRecipients, subject, message, cc } = emailValues;
        const recipients = selectedRecipients.map((user) => ({
          username: user,
          email: user,
        }));
        const payload: NotificationMessagePayload = {
          message: subject,
          users: recipients,
          icon: 'vessel-invert',
          emailContent: { subject, message: parseMessageToHTML(message) },
          ccAddress: cc as string[],
        };
        await sendEmailNotificationMessage(payload);
        setSelectedRequestedReplacement(null);
        setAlertMessage({
          message: 'The replacement request email was successfully sent.',
          variant: 'success',
          className: 'alert-message-success',
        });
        eventTracker('sendEmail', 'Successfully send email for replacement request');
      } catch (error) {
        console.log('Something went wrong on sending email :', error);
        setAlertMessage({
          message: 'An error occurred. The replacement request email could not be sent.',
          variant: 'danger',
        });
      }
    },
    [eventTracker],
  );

  const renderEmailModal = () => {
    const { seafarerRank, isSeafarerMissingPersonnel, isAdditionalCrewRequest } =
      selectedRequestedReplacement;
    const emailContent = generateReplacementEmailContent({
      rank: seafarerRank,
      vesselName: vesselData.name,
      isMissingRank: isSeafarerMissingPersonnel,
      isAdditionalCrew: isAdditionalCrewRequest,
      user: userProfile,
    });
    return (
      <SendEmailModal
        show={!!selectedRequestedReplacement}
        recipientsList={userList.map((item) => ({ id: item.email, value: item.full_name }))}
        initialValues={{
          message: emailContent,
          subject: `Missing ${seafarerRank} at ${vesselData.name}`,
        }}
        generateEmailContent={(recipients: string[]) => {
          const recipientFirstNames = userList
            .filter((user) => recipients.includes(user.email))
            .map((user) => user.first_name);
          return generateReplacementEmailContent({
            rank: seafarerRank,
            vesselName: vesselData.name,
            isMissingRank: isSeafarerMissingPersonnel,
            isAdditionalCrew: isAdditionalCrewRequest,
            user: userProfile,
            recipients: recipientFirstNames,
          });
        }}
        onClose={() => setSelectedRequestedReplacement(null)}
        onSubmit={handleEmailSubmit}
      />
    );
  };
  const contextValue = {
    fetchMoreData,
    hasMoreData,
    itineraryList: itineraryData,
    refetchSimulateItinerary: refreshSimulateItinerary,
    crewList: crewListData,
    visitSeafarerDetail,
    setSelectedActionSeafarer,
    refresh: refreshCrewList,
    isCrewListLoading,
    setActiveOverlaySeafarerId,
    activeOverlaySeafarerId,
    setAlertMessage,
    vesselData,
    upsertClubbedCrew,
    setCrewListData,
    userProfile,
    onRequestReplacement,
  };
  return (
    <AccessHandlerWrapper
      hasRoleAccess={errorStatusCode === 403 ? false : hasRoleAccess || hasEditAccess}
    >
      {loading && <Spinner alignClass="load-spinner" />}
      <Container>
        {!!alertMessage?.message && (
          <AutoDismissibleAlert
            noAutoDismissOnDanger
            className={alertMessage.className}
            variant={alertMessage.variant}
            message={alertMessage.message}
            onClose={() => setAlertMessage(null)}
          />
        )}
        {!loading && vesselData && (
          <div>
            <Row className="crew-management-heading">
              <Col md="8" className="header-col">
                <span>
                  <Button
                    className="vessel-name-crew-planning-heading"
                    variant="link"
                    onClick={() => {
                      history.push('/seafarer/crew-planner/managed-vessel');
                    }}
                  >
                    Crew Planner /
                  </Button>
                </span>
                <span>
                  <Button
                    className="vessel-name-crew-planning-heading"
                    variant="link"
                    onClick={routeToVesselDetailPage}
                  >
                    {vesselData?.name}
                  </Button>
                </span>
                <span> / Crew Details</span>
              </Col>
              <Col md="4" className="header-col">
                <div className="d-flex justify-content-end">
                  <Form.Group className="crew-management-switch-vessel">
                    <Select
                      value={formatValue(`${vesselData.name}(${vesselData.id})`)}
                      onChange={handleDropdownChange}
                      options={vesselsDropdownData}
                      labelKey="value"
                      placeholder="Switch Vessel"
                      loading={loading}
                      isFromCrewManagementPage
                    />
                  </Form.Group>
                  <Button
                    variant="outline-primary"
                    onClick={() => {
                      routeToVesselDetailPage();
                    }}
                  >
                    Vessel Details
                  </Button>
                </div>
              </Col>
            </Row>
            <CrewManagementHeaderSection vesselData={vesselData} itinerary={itineraryData?.[0]} />

            <PlannerKPI
              kpiData={[
                totalFpdBudget,
                totalExpenses,
                crewWagesYearly,
                crewTravelYearly,
                avgCrewWages,
                avgCrewExpenses,
              ]}
            />
            <Row className="crew-management-tab">
              <TabWrapper
                handleTabSelect={handleTabSelect}
                data={CREW_MANAGEMEMT_TAB_LIST}
                isFixedWidth
              />
            </Row>
            <div>
              <CrewMangementContext.Provider value={contextValue}>
                <CrewManagementContentSection
                  relieverStatus={relieverStatusData}
                  handleConfirmPlanningAction={handleConfirmPlanningAction}
                  eventTracker={eventTracker}
                  hasEditAccess={hasEditAccess}
                />
              </CrewMangementContext.Provider>
            </div>
          </div>
        )}
      </Container>
      {selectedActionSeafarer?.action && selectedActionSeafarer?.action !== 'club_confirmed' && (
        <PlanningActionModal
          show={!!selectedActionSeafarer.seafarer}
          handleClosePlanningAction={() => {
            setSelectedActionSeafarer({ seafarer: null, action: null });
          }}
          handleConfirmPlanningAction={handleConfirmPlanningAction}
          isLoading={isLoadingReject}
          actionType={selectedActionSeafarer?.action}
        />
      )}
      {!!selectedRequestedReplacement && renderEmailModal()}
    </AccessHandlerWrapper>
  );
};

export default CrewManagementPage;
