import React, { useState, useEffect } from 'react';
import { Container, Row, Button } from 'react-bootstrap';
import { useHistory } from 'react-router-dom';
import seafarerService from '../service/seafarer-service';
import { getShortDate } from '../util/view-utils';
import Spinner from '../component/common/Spinner';
import AccessHandlerWrapper from '../component/common/AccessHandlerWrapper';
import { seafarerStatusService } from 'paris2-seafarer-status';
import { PaginationBar } from './PaginationBar';
import { useTable, useSortBy, usePagination, useFlexLayout } from 'react-table';
import { useSticky } from 'react-table-sticky';
import styleGuide from '../styleGuide';
import { useAccess } from '@src/component/common/Access';
const { Icon } = styleGuide;

const generateSelectedColumns = ({ visitVesselDetailPage, displayJourneyStatus, displayExaminationStatus, displayRank}) => {
  return [
    {
      Header: 'Date',
      id: 'updated_at',
      type: 'item',
      accessor: ({ status_date }) => {
        return getShortDate(status_date) ?? '';
      },
      minWidth: 180,
      width: 180,
      maxWidth: 200,
    },
    {
      Header: 'Vessel Name',
      id: 'vessel_name',
      type: 'item',
      accessor: ({ vessel_name, vessel_ref_id }) => {
        return vessel_name ? (
          <Button variant="link" onClick={() => visitVesselDetailPage(vessel_ref_id)}>
            <b>{vessel_name}</b>
          </Button>
        ) : (
          '---'
        );
      },
      minWidth: 180,
      width: 150,
      maxWidth: 200,
    },
    {
      Header: 'Rank',
      id: 'rank_id',
      type: 'item',
      accessor: ({ rank_id }) => {
        return displayRank(rank_id);
      },
      minWidth: 180,
      width: 150,
      maxWidth: 200,
    },
    {
      Header: 'Journey Status',
      id: 'seafarer_journey_status',
      type: 'item',
      accessor: ({ seafarer_journey_status }) => {
        return displayJourneyStatus(seafarer_journey_status);
      },
      minWidth: 250,
      width: 250,
      maxWidth: 500,
    },
    {
      Header: 'Examination Status',
      id: 'seafarer_exam_status',
      type: 'item',
      accessor: ({ seafarer_exam_status }) => {
        if (seafarer_exam_status) {
          return displayExaminationStatus(seafarer_exam_status);
        } else {
          return '---';
        }
      },
      minWidth: 250,
      width: 250,
      maxWidth: 500,
    },
    {
      Header: 'By',
      id: 'created_by',
      type: 'item',
      accessor: ({ created_by }) => {
        return created_by;
      },
      minWidth: 180,
      width: 150,
      maxWidth: 200,
    },
    {
      Header: 'Remark',
      id: 'remarks',
      type: 'item',
      accessor: ({ seafarer_journey_remarks, seafarer_exam_remarks }) => {
        return seafarer_exam_remarks && seafarer_exam_remarks !== ''
          ? seafarer_exam_remarks
          : seafarer_journey_remarks;
      },
      minWidth: 180,
      width: 150,
      maxWidth: 200,
    },
  ];
};

const Table = ({ columns, data, defaultColumn }) => {
  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    prepareRow,
    page,
    canPreviousPage,
    canNextPage,
    pageCount,
    gotoPage,
    nextPage,
    previousPage,
    setPageSize,
    state: { pageIndex, pageSize },
  } = useTable(
    {
      data,
      columns,
      defaultColumn,
      initialState: {
        pageIndex: 0,
        pageSize: 10,
        sortBy: [],
      },
      manualPagination: false,
      manualSortBy: true,
      autoResetPage: false,
      autoResetSortBy: false,
    },
    useSortBy,
    usePagination,
    useSticky,
    useFlexLayout,
  );

  const PaginationBarProps = {
    canPreviousPage,
    canNextPage,
    pageCount,
    gotoPage,
    nextPage,
    previousPage,
    setPageSize,
    pageIndex,
    pageSize,
  };

  return (
    <>
      <Row>
        <PaginationBar className="top-pagination-bar" {...PaginationBarProps} />
        <div className="seafarer-list-count pt-2">
          <b>{data?.length ?? 0}</b> Results
        </div>
      </Row>
      <div {...getTableProps()} className="table sticky status_history min-width-auto">
        <div className="header w-100">
          {headerGroups.map((headerGroup) => (
            <div {...headerGroup.getHeaderGroupProps()} className="tr">
              {headerGroup.headers.map((column) => {
                // @ts-ignore
                const thProps = column.getHeaderProps(column.getSortByToggleProps());
                return (
                  <div
                    {...thProps}
                    className="th"
                    data-testid={`${column.id}-table-header-column`}
                  >
                    {column.render('Header')}
                  </div>
                );
              })}
            </div>
          ))}
        </div>
        <div {...getTableBodyProps()} className="body">
          {page.map((row) => {
            prepareRow(row);
            return (
              <div {...row.getRowProps()} className="tr">
                {row.cells.map((cell) => {
                  return (
                    <div {...cell.getCellProps()} className="td">
                      {cell.render('Cell')}
                    </div>
                  );
                })}
              </div>
            );
          })}
        </div>
      </div>
      <br />
      <Row>
        <PaginationBar {...PaginationBarProps} />
      </Row>
    </>
  );
};

const StatusHistoryPage = ({ seafarerPersonId }) => {
  const history = useHistory();
  const defaultDropDownValue = {
    ranks: [],
  };
  const { roleConfig } = useAccess();
  const [rankData, setRankData] = useState(defaultDropDownValue);
  const [journeyStatusJsonData, setJourneyStatusJsonData] = useState({});
  const [examStatusJsonData, setExamStatusJsonData] = useState({});
  const [statusHistoryData, setStatusHistoryData] = useState(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    (async () => {
      try {
        setLoading(true);
        const seafarerStatusResponse = await seafarerService.getSeafarerStatus(seafarerPersonId);
        const seafarerStatusData = seafarerStatusResponse?.data;

        const dropDownData = await Promise.all([seafarerService.getSeafarerDropDownData()]);
        const journeyStatusJson = seafarerStatusService.getJourneyStatus();
        const examStatusJson = seafarerStatusService.getExamStatus();
        setJourneyStatusJsonData(journeyStatusJson);
        setExamStatusJsonData(examStatusJson);
        setRankData(dropDownData[0].ranks);
        setStatusHistoryData(seafarerStatusData);
        setLoading(false);
      } catch (error) {
        setLoading(false);
        console.error(
          `Get seafarer by seafarerPersonId: ${seafarerPersonId} failed. Error: ${error}`,
        );
      }
    })();
  }, []);

  const visitVesselDetailPage = async (vesselRefId) => {
    if (vesselRefId) {
      const result = await seafarerService.getVesselOwnershipId(vesselRefId);
      history.push(`/vessel/ownership/details/${result.data.ownership_id}`);
    }
  };

  const displayRank = (rank_id) => {
    if (rank_id) {
      let rank = rankData.find((rankElement) => {
        return rankElement.id == rank_id;
      });
      return rank.value ?? '';
    }
    return '';
  };

  const displayJourneyStatus = (status) => {
    if (journeyStatusJsonData[status]) return journeyStatusJsonData[status].name;
  };

  const displayExaminationStatus = (status) => {
    if (examStatusJsonData[status]) return examStatusJsonData[status].name;
  };

  const selectedColumns = generateSelectedColumns({
    visitVesselDetailPage,
    displayJourneyStatus,
    displayExaminationStatus,
    displayRank,
  });

  const defaultColumn = {
    minWidth: 180,
    width: 350,
    maxWidth: 500,
  };

  return (
    <AccessHandlerWrapper hasRoleAccess={roleConfig.seafarer.view.general}>
      <Container className="seafarer-table" data-testid="seafarer-status-history">
        {!loading &&
          (statusHistoryData?.length > 0 ? (
            <Table columns={selectedColumns} data={statusHistoryData} defaultColumn={defaultColumn} />
          ) : (
            <div className="no-result-found">
              <Icon icon="alert" className="alert-icon-no-search" style={{ float: 'none' }} />
              <div>
                <b>No Results Found</b>
              </div>
            </div>
          ))}
        {loading && (
          <div className="mt-5">
            {' '}
            <Spinner />
          </div>
        )}
      </Container>
    </AccessHandlerWrapper>
  );
};

export default StatusHistoryPage;
