/* eslint-disable react/prop-types */
import React, { useState, useRef, useCallback, useEffect } from 'react';
import { useHistory, useParams } from 'react-router-dom';
import {
  Col,
  Row,
  ButtonToolbar,
  Button,
  Form,
  OverlayTrigger,
  Tooltip,
  Container,
  Tab,
} from 'react-bootstrap';
import { toast } from 'react-toastify';
import moment from 'moment-timezone';
import { useAccess } from '@src/component/common/Access';
import CrewListTable from '../component/CrewList/CrewListTable';
import {
  retrieveColumns,
  storeColumns,
  isKeyStored,
  getPageSort,
} from '../util/local-storage-helper';
import { LOCAL_STORAGE_FIELDS, seafarerStatus } from '../model/constants';
import FleetDatePicker from '../component/AddSeafarer/FleetDatePicker';
import TableColumnsButton from '../component/seafarerList/TableColumnsButton';
import AccessHandlerWrapper from '../component/common/AccessHandlerWrapper';
import { getCrewListPageColumns } from '../component/CrewList/MenuList';
import seafarerService from '../service/seafarer-service';
import vesselService from '../service/vessel-service';
import './scss/crew-list-page.scss';
import { dateAsDash } from '../model/utils';
import ReplaceButton from '../component/CrewList/ReplacementButton';
import { formatValue } from '../util/view-utils';
import DropdownSearchControl from '../component/CrewList/DropDownSearchControl';
import parentHKIDLink from '../component/common/HKIDLink';
import ReplaceWith from '../component/CrewList/ReplaceWith';
import TravelModal from '../component/travel-modal/travel-modal';
import { ConfirmationModal } from '../component/confirmation-modal/confirmation-modal';
import TabWrapper from '../component/common/TabWrapper';
import { CrewListTabData } from '../model/TabData';
import { MODULES } from '../util';

const changeCrewListColumnsAccessor = ({ columns, roleConfig, eventTracker }) => {
  columns.forEach((e) => {
    if (e.id === 'hkid') {
      e.accessor = (row) => parentHKIDLink(row, eventTracker, roleConfig);
    }
    if (e.id === 'replace_with') {
      e.accessor = function ReplaceWithAccessor(row) {
        return (
          <div>
            <ReplaceWith
              replaced_by={row?.seafarer_person?.seafarer_status_history[0]?.replaced_by}
              type="name"
              tableName="crew-list"
              roleConfig={roleConfig}
            />
          </div>
        );
      };
    }
  });
  return columns;
};

const CrewListPage = ({ ga4react }) => {
  const [loading, setLoading] = useState(false);
  const tableRef = useRef(null);
  const { roleConfig, userProfile } = useAccess();
  const history = useHistory();
  const params = useParams();
  const { vesselIdParam, date } = params;
  const [vesselId, setVesselId] = useState(vesselIdParam);
  const [ownershipId, setOwnershipId] = useState(null);
  const [vesselType, setVesselType] = useState('');
  const [selectedDate, setSelectedDate] = useState(dateAsDash(date));

  const [crewListData, setCrewListData] = useState([]);
  const [selectedColumns, setSelectedColumns] = useState([]);
  const [vesselName, setVesselName] = useState(null);
  const [selectedVessel, setSelectedVessel] = useState(null);
  const [vesselsDropdownData, setVesselsDropdownData] = useState([]);
  const [activeKey, setActiveKey] = useState(seafarerStatus.SIGNED_ON);
  const [initSort, setInitSort] = useState(getPageSort(activeKey));
  const [isPageViewInvoked, setIsPageViewInvoked] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [showTravelModal, setShowTravelModal] = useState(false);
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const [seafarersWhoCanTravel, setSeafarersWhoCanTravel] = useState([]);
  const [enableOcimf, setEnableOcimf] = useState(() => {
    const queryParams = new URLSearchParams(location.search);
    return queryParams.get('is_ocimf_enabled') === 'true';
  });
  const [ocimfViewTimer, setOcimfViewTimer] = useState(0);

  const onTravelClick = () => {
    let signedOnOrRecommendedSeafarers = [];
    if (activeKey === seafarerStatus.RECOMMENDED) {
      signedOnOrRecommendedSeafarers = selectedRows;
    } else {
      signedOnOrRecommendedSeafarers = selectedRows?.filter(
        (row) =>
          seafarerStatus.CREW_ASSIGNMENT_APPROVED ===
            row.seafarer_person?.seafarer_status_history?.sort((a, b) => b.id - a.id)?.[0]
              ?.seafarer_journey_status ||
          seafarerStatus.SIGNED_ON === row.seafarer_person?.current_journey_status,
      );
    }
    setSeafarersWhoCanTravel(signedOnOrRecommendedSeafarers);
    if (selectedRows.length !== signedOnOrRecommendedSeafarers.length) {
      setShowConfirmationModal(true);
      return;
    }
    setShowTravelModal(true);
  };
  const [errorStatusCode, setErrorStatusCode] = useState(null);

  useEffect(() => {
    if (!isPageViewInvoked) {
      try {
        ga4react?.pageview('/seafarer/crew-list/vessel/', '', 'Crew List');
        setIsPageViewInvoked(true);
      } catch (e) {
        console.log(e);
      }
    }
  }, [isPageViewInvoked]);

  useEffect(() => {
    eventTracker('tabs', 'Crew List');
    changeCrewListColumnsAccessor({
      columns: getCrewListPageColumns(enableOcimf, activeKey),
      roleConfig,
      eventTracker,
    });
  }, []);

  const generateParams = (sortPaginateData) => {
    if (sortPaginateData.length > 0 && sortPaginateData[0].id) {
      return `&orderBy=${sortPaginateData[0].id} ${sortPaginateData[0].desc ? 'desc' : 'asc'}`;
    }
    return '';
  };

  const queryListData = async (sortPaginateData) => {
    getCrewListPageColumns(enableOcimf, activeKey).forEach((e) => {
      if (e.Header === 'Replacement') {
        e.accessor = function replacementAccessor(row) {
          return (
            <div>
              {row?.id ? <ReplaceButton seafarerId={row.id} eventTracker={eventTracker} /> : '---'}
            </div>
          );
        };
      }
    });
    try {
      setErrorStatusCode(null);
      if (
        !isKeyStored(
          'crew-list-table-details',
          activeKey,
          LOCAL_STORAGE_FIELDS.crewListTableSelectedColumns,
        )
      ) {
        if (roleConfig?.seafarer && !roleConfig.seafarer.replaceCrewList) {
          const columns = getCrewListPageColumns(enableOcimf, activeKey) ?? [];
          setSelectedColumns(
            [...columns.filter((c) => c.showToOwner)].filter((col) => col.showByDefault),
          );
        } else {
          const columns = getCrewListPageColumns(enableOcimf, activeKey) ?? [];
          setSelectedColumns(columns.filter((col) => col.showByDefault));
        }
      } else {
        let columns = retrieveColumns(activeKey, enableOcimf);
        // Remove replacement column, If user doesn't have replace|crew-list role
        if (!roleConfig?.seafarer.replaceCrewList && columns) {
          columns = columns?.filter((c) => c.showToOwner);
        }
        setSelectedColumns(columns);
      }
      if (selectedDate !== '') {
        const selectedDateQuery =
          activeKey === seafarerStatus.SIGNED_ON
            ? `&crew_list_status_date=${dateAsDash(selectedDate)}`
            : '';
        const queryParams = `vessel_id=${vesselId}${selectedDateQuery}${generateParams(
          sortPaginateData,
        )}`;
        const apiFunction =
          activeKey === seafarerStatus.SIGNED_ON
            ? seafarerService.getCrewList
            : seafarerService.getRecommendedCrewList;
        const promises = [apiFunction(queryParams), vesselService.getAllVessels()];
        if (enableOcimf) {
          promises.push(seafarerService.getOcimfMatrix(vesselId));
        }
        const [crewList, vesselDropdown, ocimfMatrix] = await Promise.all(promises);
        const selectedVessel = vesselDropdown.data.results.find(
          (e) => e.vessel?.id == vesselIdParam,
        );
        setVesselName(selectedVessel?.name);
        setOwnershipId(selectedVessel?.id);
        setVesselType(selectedVessel?.vessel_type?.type);
        const vesselDropdownData = vesselDropdown.data.results.map((vessel) => {
          return { id: vessel.vessel?.id, value: formatValue(`${vessel.name}(${vessel.id})`) };
        });
        const vesselDropdownSort = () => {
          return vesselDropdownData.length
            ? vesselDropdownData.sort((a, b) =>
                a?.value?.toLowerCase() > b?.value?.toLowerCase() ? 1 : -1,
              )
            : [];
        };
        setVesselsDropdownData(vesselDropdownSort);
        if (enableOcimf) {
          const ocimfMatrixInjectedCrewList = [];
          crewList.data?.results?.forEach((crew) => {
            const ocimfMat = ocimfMatrix?.data?.crew_ocimf_matrix?.filter(
              (mat) => parseInt(mat?.id?.trim()) === crew?.id,
            );
            if (ocimfMat?.length && Object.keys(crew?.experience_summary).length) {
              crew.experience_summary.duration_on_target_vessel = ocimfMat?.[0]?.monthsTour;
              crew.experience_summary.duration_in_target_rank = ocimfMat?.[0]?.yearsrank * 365;
              crew.experience_summary.duration_in_watch_years =
                ocimfMat?.[0]?.yearsWatch && ocimfMat?.[0]?.yearsWatch > 0
                  ? ocimfMat?.[0]?.yearsWatch * 365
                  : 'N/A';
              crew.experience_summary.duration_with_company = ocimfMat?.[0]?.yearsoperator * 365;
              crew.experience_summary.duration_on_target_vessel_type =
                ocimfMat?.[0]?.yearstankertype * 365;
              crew.experience_summary.duration_on_all_vessel_type =
                ocimfMat?.[0]?.yearsalltankertypes * 365;
              crew.ocimf_matrix = ocimfMat?.[0];
              ocimfMatrixInjectedCrewList.push(crew);
            }
          });
          setCrewListData(ocimfMatrixInjectedCrewList);
        } else {
          setCrewListData(crewList.data.results);
        }
        handleShowHideColumns(activeKey);
      }
    } catch (error) {
      setErrorStatusCode(error?.response?.status);
      setLoading(false);
      console.error(`Get seafarer by vesselId: ${vesselId} failed. Error: ${error}`);
    }
  };

  useEffect(() => {
    (async () => {
      if (vesselType === 'tanker') {
        const queryParams = new URLSearchParams(window.location.search);
        queryParams.set('is_ocimf_enabled', enableOcimf ? 'true' : 'false');
        const newUrl = `${window.location.pathname}?${queryParams.toString()}`;
        history.replace(newUrl);
      }
      setLoading(true);
      if (!date) {
        const currentDate = dateAsDash(new Date());
        setSelectedDate(currentDate);
        return history.push(`/seafarer/crew-list/vessel/${vesselId}/${currentDate}`);
      }

      await queryListData(initSort);
      setLoading(false);
    })();
  }, [selectedDate, vesselId, initSort, enableOcimf]);

  useEffect(() => {
    if (!vesselType) return;
    if (vesselType !== 'tanker') {
      const queryParams = new URLSearchParams(window.location.search);
      queryParams.delete('is_ocimf_enabled');
      const newUrl = `${window.location.pathname}?${queryParams.toString()}`;
      history.replace(newUrl);
    }
  }, [vesselType]);

  useEffect(() => {
    if (enableOcimf) {
      setOcimfViewTimer(new Date().getTime());
    } else if (ocimfViewTimer) {
      const currentTime = new Date().getTime();
      const elapsedTime = currentTime - ocimfViewTimer;
      ga4EventTrigger(
        'Time Spent',
        'Crew List',
        moment.utc(elapsedTime).format('mm:ss').toString(),
      );
      setOcimfViewTimer(0);
    }
  }, [enableOcimf]);

  const visitUpdateSeafarer = useCallback((seafarerId) => {
    history.push(`/seafarer/${seafarerId}/add/basic`);
  }, []);

  const handleDropdownChange = (event) => {
    if (event) {
      setSelectedRows([]);
      eventTracker('switchVessel', 'Menu');
      setSelectedVessel(event);
      setVesselId(event.id);
      setEnableOcimf(false);
      history.push(`/seafarer/crew-list/vessel/${event.id}/${selectedDate}`);
    }
  };

  const onSelectColumn = (item) => {
    const newSelection = selectedColumns.slice();
    const idx = newSelection?.map((col) => col?.id).indexOf(item?.id);
    if (idx !== -1) {
      newSelection.splice(idx, 1);
      eventTracker('columnDisplay', `${item.Header} remove`);
    } else {
      newSelection.push(item);
      newSelection.sort((a, b) => a.order - b.order);
      eventTracker('columnDisplay', `${item.Header} add`);
    }
    setSelectedColumns(newSelection);
    storeColumns(activeKey, newSelection);
  };

  const routeToVesselDetailPage = () => {
    if (ownershipId) {
      history.push(`/vessel/ownership/details/${ownershipId}`);
    }
  };

  const onDateOfChange = (value) => {
    eventTracker('expiryDateChange', 'Crew List Page');
    if (value) {
      setSelectedDate(dateAsDash(value));
      return history.push(`/seafarer/crew-list/vessel/${vesselId}/${selectedDate}`);
    }
  };

  const ga4EventTrigger = (action, category, label) => {
    try {
      ga4react?.event(action, label.toString(), category, false);
    } catch (error) {
      console.log(error);
    }
  };

  const eventTracker = (type, value) => {
    switch (type) {
      case 'columnDisplay':
        ga4EventTrigger('Column', 'Vessel Crew List', `Crew List Page - ${value}`);
        break;
      case 'sortBy':
        ga4EventTrigger('Sorting', 'Vessel Crew List', `Crew List Page: Sort By - ${value}`);
        break;
      case 'replaceSeafarer':
        ga4EventTrigger('Replace', 'Vessel Crew List Menu', 'Crew List Page');
        break;
      case 'expiryDateChange':
        ga4EventTrigger('Change Expiry Date', 'Vessel Crew List', 'Crew List Page');
        break;
      case 'switchVessel':
        ga4EventTrigger('Switch Vessel', 'Vessel Crew List Menu', 'Crew List Page');
        break;
      case 'tabs':
        ga4EventTrigger('Tab', 'Nav', `Seafarer List Page - ${value}`);
        break;
      case 'ocimf':
        ga4EventTrigger('Click Crew / OCIMF matrix', 'Vessel - Crew List', 'Crew / OCIMF matrix');
        break;
      default:
        ga4EventTrigger('Click', value, 'Crew List Page');
        break;
    }
  };

  const handleShowHideColumns = (key: string) => {
    if (
      key === seafarerStatus.SIGNED_ON &&
      !isKeyStored(`${key}-table-details`, key, LOCAL_STORAGE_FIELDS.signedOnTableSelectedColumns)
    ) {
      const columns = getCrewListPageColumns(enableOcimf, activeKey)?.filter(
        (col) => col.showByDefault,
      );
      setSelectedColumns(columns);
    } else if (
      key === seafarerStatus.RECOMMENDED &&
      !isKeyStored(
        `${key}-table-details`,
        key,
        LOCAL_STORAGE_FIELDS.recommendedTableSelectedColumns,
      )
    ) {
      const columns = getCrewListPageColumns(enableOcimf, activeKey)
        ?.filter((col) => !['Repatriation Port', 'Embarkation Port'].includes(col.Header))
        ?.filter((col) => col.showByDefault);
      setSelectedColumns(columns);
    } else {
      setSelectedColumns(retrieveColumns(key, enableOcimf));
    }
  };

  const handleTabSelect = async (key: string) => {
    if (loading) return;
    setEnableOcimf(false);
    setActiveKey(key);
    setSelectedRows([]);
    setInitSort(getPageSort(key));
  };

  const getTravelButtonOverlayText = () => {
    if (!selectedRows.length) {
      return (
        <Tooltip id="tooltip-disabled">Select seafarers with checkboxes to apply travel</Tooltip>
      );
    }
    if (selectedRows.length > 21) {
      return (
        <Tooltip id="tooltip-disabled">Travel team can choose up to 21 seafarers at a time</Tooltip>
      );
    }
    return <span />;
  };

  const getTableColumns = (tab) => {
    if (!roleConfig?.seafarer?.replaceCrewList) {
      return getCrewListPageColumns(enableOcimf, activeKey)?.filter((c) => c.showToOwner);
    }
    if (tab === seafarerStatus.RECOMMENDED) {
      return [...getCrewListPageColumns(false, activeKey)]?.filter(
        (col) => !['Repatriation Port', 'Embarkation Port'].includes(col.Header),
      );
    }
    return getCrewListPageColumns(enableOcimf, activeKey);
  };

  return (
    <AccessHandlerWrapper
      hasRoleAccess={errorStatusCode === 403 ? false : roleConfig.seafarer.view.crewList}
    >
      <Container className="crew-list">
        <Row className="crew-list-heading">
          {vesselName && (
            <div>
              <span>
                <Button
                  className="vessel-name-crew-list-heading"
                  variant="link"
                  onClick={routeToVesselDetailPage}
                >
                  {vesselName}
                </Button>
              </span>
              <span> / Crew List</span>
            </div>
          )}
          <div className="heading-wrapper-crew-list">
            {vesselName && activeKey === seafarerStatus.SIGNED_ON && (
              <Col className="date-search-crew-list date-input-field">
                <FleetDatePicker
                  name="date-of-search"
                  value={selectedDate}
                  onChange={onDateOfChange}
                  isClearable={false}
                />
              </Col>
            )}
            {vesselName && (
              <Form.Group className="vessel_dropdown ml-2">
                <DropdownSearchControl
                  selectedVessel={
                    selectedVessel ?? {
                      id: vesselId,
                      value: formatValue(`${vesselName}(${vesselId})`),
                    }
                  }
                  onChange={handleDropdownChange}
                  vesselsDropdownData={vesselsDropdownData}
                  labelKey="value"
                  dropdownLoading={loading}
                />
              </Form.Group>
            )}
            {vesselName && roleConfig.seafarer.edit?.travel && (
              <OverlayTrigger overlay={getTravelButtonOverlayText()}>
                <span className="d-inline-block">
                  <Button
                    data-testid="travel-button"
                    variant="outline-primary"
                    onClick={onTravelClick}
                    className="travel-btn ml-2"
                    disabled={selectedRows.length ? selectedRows.length > 21 : true}
                    style={{
                      pointerEvents:
                        selectedRows.length && selectedRows.length <= 21 ? 'auto' : 'none',
                    }}
                  >
                    Email Profile Data
                  </Button>
                </span>
              </OverlayTrigger>
            )}
          </div>
        </Row>
        <Tab.Container activeKey={activeKey}>
          <div>
            <Row className="no-print">
              <Col className="nav-buttons">
                <TabWrapper handleTabSelect={handleTabSelect} data={CrewListTabData} isFixedWidth />
              </Col>
            </Row>
          </div>
        </Tab.Container>

        <Row className="crew-list-table-column-wrapper">
          <div className="seafarer-list-count-crew-list">
            <b>{crewListData?.length || 0}</b> Seafarers Onboard
          </div>
          <Col md={4} className="btn-container">
            {vesselType === 'tanker' && activeKey === seafarerStatus.SIGNED_ON && (
              <div className="ocimf-checkbox-wrapper">
                <input
                  data-testid="ocimf-checkbox"
                  type="checkbox"
                  className="checkbox ocimf-checkbox"
                  checked={enableOcimf}
                  onClick={() => eventTracker('ocimf', !enableOcimf)}
                  onChange={() => {
                    setEnableOcimf(!enableOcimf);
                  }}
                />
                <label>Enable OCIMF Matrix</label>
              </div>
            )}
            <ButtonToolbar>
              <TableColumnsButton
                size="md"
                items={[
                  {
                    type: 'header',
                    Header: '',
                  },
                  ...getTableColumns(activeKey),
                ]}
                selectedColumns={selectedColumns}
                onSelectColumn={onSelectColumn}
              />
            </ButtonToolbar>
          </Col>
        </Row>

        <CrewListTable
          selectedColumns={selectedColumns}
          loading={loading}
          tableRef={tableRef}
          data={crewListData}
          roleConfig={roleConfig}
          visitUpdateSeafarer={visitUpdateSeafarer}
          eventTracker={eventTracker}
          init_sort={initSort}
          seafarersTotalCount={crewListData.length}
          setInitSort={setInitSort}
          selectedRows={selectedRows}
          onSelectRow={(rows: []) => setSelectedRows(rows)}
          tab={activeKey}
          enableOcimf={enableOcimf}
        />
        {showTravelModal && (
          <TravelModal
            data={{
              seafarers: selectedRows.map((row) => ({
                id: row.id,
                hkid: row.hkid,
                firstName: row.seafarer_person.first_name,
                middleName: row.seafarer_person.middle_name,
                lastName: row.seafarer_person.last_name,
              })),
              vesselName,
            }}
            show={showTravelModal}
            onClose={() => setShowTravelModal(false)}
            onSave={() => {
              setSelectedRows([]);
              setShowTravelModal(false);
            }}
            user={userProfile}
            ga4EventTrigger={ga4EventTrigger}
            calledFromModule={MODULES.CREW_LIST_PAGE}
          />
        )}
        {showConfirmationModal && (
          <ConfirmationModal
            show={showConfirmationModal}
            onClose={() => {
              setShowConfirmationModal(false);
            }}
            title={
              seafarersWhoCanTravel.length
                ? 'Selected crew members are not fully available'
                : 'Selected crew members are not available'
            }
            message={
              seafarersWhoCanTravel.length
                ? 'Some of the selected members are not available to travel. Please check your selections again or you can proceed with only qualified crew members within your selection.'
                : 'The selected members are not available to travel. Please check your selections again.'
            }
            closeButtonLabel="Cancel"
            confirmButtonLabel="Proceed with qualified members only"
            onConfirm={() => {
              if (seafarersWhoCanTravel.length) {
                setSelectedRows([...seafarersWhoCanTravel]);
                setShowConfirmationModal(false);
                setShowTravelModal(true);
              } else {
                toast.error('No qualified member');
              }
            }}
            confirmCustomStyle={{ paddingLeft: '1.25rem', paddingRight: '1.25rem' }}
            cancelCustomStyle={{ paddingLeft: '1.25rem', paddingRight: '1.25rem' }}
            className="confirmation-modal"
            bodyCustomStyle={{
              marginLeft: '0.25rem',
              marginRight: '0.25rem',
              padding: '0px 22px 18px 22px',
            }}
            headerCustomStyle={{
              fontSize: '20px',
              paddingLeft: '0.5rem',
              paddingRight: '0.5rem',
            }}
            disableConfirm={!seafarersWhoCanTravel.length}
          />
        )}
      </Container>
    </AccessHandlerWrapper>
  );
};

export default CrewListPage;
