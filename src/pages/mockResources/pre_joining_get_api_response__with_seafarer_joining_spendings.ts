const response = {
  seafarer_id: 108701,
  seafarer_person_id: 108701,
  seafarer_status_history: {
    id: 1479023,
    seafarer_person_id: 108701,
    seafarer_account_status: 'active',
    seafarer_journey_status: 'crew_assignment_approved',
    seafarer_exam_status: null,
    rank_id: 26,
    vessel_name: 'Bochem Mumbai',
    vessel_ref_id: 4589,
    created_by_hash: '<EMAIL>',
    created_by: '<PERSON><PERSON>',
    seafarer_journey_remarks: '',
    seafarer_exam_remarks: null,
    status_date: '2021-09-08T00:00:00.000Z',
    vessel_ownership_id: 520,
    sign_off_date: null,
    expected_contract_end_date: null,
    embarkation_port: null,
    repatriation_port: null,
    vessel_tech_group: 'Tech T10',
    vessel_type: 'Chemical Tanker',
    replaced_by_id: null,
    is_p1_history: null,
    created_at: '2022-04-02T08:04:06.930Z',
    updated_at: '2022-04-02T08:04:06.930Z',
    created_at_p1: '2021-09-08T10:37:09.000Z',
    updated_at_p1: '2021-09-08T10:37:09.000Z',
    paris1_ref_id: 1452589,
    is_current_status: false,
  },
  seafarer_wages: {
    amount_basic: 9999.05,
    amount_total: 12000.05,
    unit: 'usd',
    recommended_wages: 10000.0,
  },
  seafarer_allotment: {
    monthly_allotment: 100.0,
    first_allotment: 100.0,
    unit: 'usd',
    created_at: '2022-06-29T12:13:33.521Z',
    updated_at: '2022-06-29T12:13:33.521Z',
    created_by: 'Brian Lai',
    updated_by: 'Brian Lai',
  },
  seafarer_joining_spendings: [
    {
      id: 1,
      payhead: {
        id: 65,
        head_name: 'US Visa',
        type: 'Allowance',
        nature: 'Input',
        category: 'Joining Allowances',
        is_display_input_sheet: true,
        default_value: 0,
        place_entered: 'Office',
        is_add_to_total_wages: false,
        is_add_to_basic_wages: false,
        display_order: 1,
        status: 1,
        created_by: '2022-06-30 19:11:17.445367+08',
        updated_by: null,
      },
      amount: 9123.0,
      unit: 'usd',
      remarks: 'this is remarks testing',
      ref_id: 1,
      created_at: '2022-06-29T12:13:33.521Z',
      updated_at: '2022-06-29T12:13:33.521Z',
      created_by: 'Brian Lai',
      updated_by: 'Brian Lai',
    },
    {
      id: 2,
      payhead: {
        id: 64,
        head_name: 'Travelling Expenses',
        type: 'Allowance',
        nature: 'Input',
        category: 'Joining Allowances',
        is_display_input_sheet: true,
        default_value: 0,
        place_entered: 'Offiice',
        is_add_to_total_wages: false,
        is_add_to_basic_wages: false,
        display_order: 1,
        status: 1,
        created_by: '2022-06-30 19:11:17.445367+08',
        updated_by: null,
      },
      amount: 2340.0,
      unit: 'usd',
      remarks: 'this is remarks',
      ref_id: 1,
      created_at: '2022-06-29T12:13:33.521Z',
      updated_at: '2022-06-29T12:13:33.521Z',
      created_by: 'Brian Lai',
      updated_by: 'Brian Lai',
    },
    {
      id: 3,
      payhead: {
        id: 55,
        head_name: 'Rejoining Bonus',
        type: 'Allowance',
        nature: 'Input',
        category: 'Joining Allowances',
        is_display_input_sheet: true,
        default_value: 0,
        place_entered: 'Office',
        is_add_to_total_wages: false,
        is_add_to_basic_wages: false,
        display_order: 1,
        status: 1,
        created_by: '2022-06-30 19:11:17.445367+08',
        updated_by: null,
      },
      amount: 10.01,
      unit: 'usd',
      remarks: 'this is remarks',
      ref_id: 1,
      created_at: '2022-06-29T12:13:33.521Z',
      updated_at: '2022-06-29T12:13:33.521Z',
      created_by: 'Brian Lai',
      updated_by: 'Brian Lai',
    },
    {
      id: 4,
      payhead: {
        id: 53,
        head_name: 'Other Expenses',
        type: 'Allowance',
        nature: 'Input',
        category: 'Joining Allowances',
        is_display_input_sheet: true,
        default_value: 0,
        place_entered: 'Office',
        is_add_to_total_wages: false,
        is_add_to_basic_wages: false,
        display_order: 1,
        status: 1,
        created_by: '2022-06-30 19:11:17.445367+08',
        updated_by: null,
      },
      amount: 100.12,
      unit: 'usd',
      remarks: 'this is remarks',
      ref_id: 1,
      created_at: '2022-06-29T12:13:33.521Z',
      updated_at: '2022-06-29T12:13:33.521Z',
      created_by: 'Brian Lai',
      updated_by: 'Brian Lai',
    },
    {
      id: 5,
      payhead: {
        id: 49,
        head_name: 'JNG/Repat Expenses',
        type: 'Allowance',
        nature: 'Input',
        category: 'Joining Allowances',
        is_display_input_sheet: true,
        default_value: 0,
        place_entered: 'Office',
        is_add_to_total_wages: false,
        is_add_to_basic_wages: false,
        display_order: 1,
        status: 1,
        created_by: '2022-06-30 19:11:17.445367+08',
        updated_by: null,
      },
      amount: 100.0,
      unit: 'usd',
      remarks: 'this is remarks',
      ref_id: 1,
      created_at: '2022-06-29T12:13:33.521Z',
      updated_at: '2022-06-29T12:13:33.521Z',
      created_by: 'Brian Lai',
      updated_by: 'Brian Lai',
    },

    {
      id: 6,
      payhead: {
        id: 68,
        head_name: 'Airfare/Rep.',
        type: 'Deduction',
        nature: 'Input',
        category: 'Joining Deductions',
        is_display_input_sheet: true,
        default_value: 0,
        place_entered: 'Office',
        is_add_to_total_wages: false,
        is_add_to_basic_wages: false,
        display_order: 1,
        status: 1,
        created_by: '2022-06-30 19:11:17.445367+08',
        updated_by: null,
      },
      amount: 100.0,
      unit: 'usd',
      remarks: 'this is remarks',
      ref_id: 1,
      created_at: '2022-06-29T12:13:33.521Z',
      updated_at: '2022-06-29T12:13:33.521Z',
      created_by: 'Brian Lai',
      updated_by: 'Brian Lai',
    },
    {
      id: 7,
      payhead: {
        id: 72,
        head_name: 'Cash from office',
        type: 'Deduction',
        nature: 'Input',
        category: 'Joining Deductions',
        is_display_input_sheet: true,
        default_value: 0,
        place_entered: 'Office',
        is_add_to_total_wages: false,
        is_add_to_basic_wages: false,
        display_order: 1,
        status: 1,
        created_by: '2022-06-30 19:11:17.445367+08',
        updated_by: null,
      },
      amount: 100.0,
      unit: 'usd',
      remarks: 'this is remarks',
      ref_id: 1,
      created_at: '2022-06-29T12:13:33.521Z',
      updated_at: '2022-06-29T12:13:33.521Z',
      created_by: 'Brian Lai',
      updated_by: 'Brian Lai',
    },
    {
      id: 8,
      payhead: {
        id: 86,
        head_name: 'Visa',
        type: 'Deduction',
        nature: 'Input',
        category: 'Joining Deductions',
        is_display_input_sheet: false,
        default_value: 0,
        place_entered: 'Office',
        is_add_to_total_wages: false,
        is_add_to_basic_wages: false,
        display_order: 1,
        status: 1,
        created_by: '2022-06-30 19:11:17.445367+08',
        updated_by: null,
      },
      amount: 150.0,
      unit: 'usd',
      remarks: 'this is remarks',
      ref_id: 1,
      created_at: '2022-06-29T12:13:33.521Z',
      updated_at: '2022-06-29T12:13:33.521Z',
      created_by: 'Brian Lai',
      updated_by: 'Brian Lai',
    },
  ],
  created_at: '2022-06-29T12:13:33.521Z',
  updated_at: '2022-06-29T12:13:33.521Z',
  created_by: 'Brian Lai',
  updated_by: 'Brian Lai',
};

export default response;
