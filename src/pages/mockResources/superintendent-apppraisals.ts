export const mockSuperintendentApppraisals = {
  pagination: {
    totalCount: 22,
    count: 1,
    offset: '0',
    limit: '1',
    orderBy: 'supt_appraisal.updated_at desc',
  },
  results: [
    {
      id: 1,
      survey: {
        id: 2,
        name: 'supt_appraisal',
        created_at: '2022-04-02T08:04:06.930Z',
        updated_at: '2022-04-02T08:04:06.930Z',
      },
      survey_id: 2,
      seafarer_id: 1,
      due_date: '2022-04-02',
      completed_at: '2022-04-02T08:04:06.930Z',
      status: '',
      created_at: '2022-04-02T08:04:06.930Z',
      updated_at: '2022-04-02T08:04:06.930Z',
      created_by: '2022-06-30 19:11:17.445367+08',
      updated_by: null,
      supt_appraisal: {
        id: 1,
        seafarer_status_history_id: 1,
        average_score: 77.5,
        hkid: 1808,
        first_name: '<PERSON>',
        middle_name: '<PERSON><PERSON>',
        last_name: '<PERSON><PERSON><PERSON>',
        rank_id: 1,
        vessel_ownership_id: 1,
        vessel_tech_group: 'Tech T10',
        vessel_type: 'Chemical Tanker',
        vessel_name: 'abc',
        rank_value: 'MASTER',
        vessel_ref_id: 1,
        ref_id: null,
        created_at: '2022-04-02T08:04:06.930Z',
        updated_at: '2022-04-02T08:04:06.930Z',
        created_by: '2022-06-30 19:11:17.445367+08',
        updated_by: null,
      },
    },
  ],
};
