import React, { useState, useEffect } from 'react';

import { Container, Row, Col, Button } from 'react-bootstrap';
import { useParams } from 'react-router-dom';

import * as wcoService from '../service/wco-service';
import seafarerService from '../service/seafarer-service';
import AccessHandlerWrapper from '../component/common/AccessHandlerWrapper';
import { useAccess } from '@src/component/common/Access';

function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

function WorldCheckOnePage() {
  const { seafarerId } = useParams();
  const { roleConfig } = useAccess();
  const [seafarer, setSeafarer] = useState('loading...');
  const [wcoCase, setWcoCase] = useState('loading...');
  const [wcoCaseResults, setWcoCaseResults] = useState('loading...');

  useEffect(async () => {
    // seafarer
    const { data: seafarerData } = await seafarerService.getSeafarer(seafarerId);
    setSeafarer(seafarerData);
    const {
      seafarer_person: { id: seafarerPersonId },
    } = seafarerData;
    // case
    const { data: wcoCaseData } = await wcoService.getWcoCase(seafarerPersonId);
    setWcoCase(wcoCaseData);
    // results
    const { data: wcoCaseResultsData } = await wcoService.getScreeningResults(seafarerPersonId);
    setWcoCaseResults(wcoCaseResultsData);
  }, []);

  const screenSeafarerPerson = async (seafarerPersonId) => {
    setWcoCase('requesting...');
    const { data: requestResult } = await wcoService.requestScreening(seafarerPersonId);
    setWcoCase(requestResult);
    let finished = false;
    while (!finished) {
      await sleep(3000);
      const { data: wcoCaseData } = await wcoService.getWcoCase(seafarerPersonId);
      setWcoCase(wcoCaseData);
      finished = wcoCaseData.status === 'success' || wcoCaseData.status === 'error';
    }
    // results
    const { data: wcoCaseResultsData } = await wcoService.getScreeningResults(seafarerPersonId);
    setWcoCaseResults(wcoCaseResultsData);
  };

  const onScreenClick = () => {
    const {
      seafarer_person: { id: seafarerPersonId },
    } = seafarer;
    screenSeafarerPerson(seafarerPersonId);
  };

  const hasAccess = () => {
    const person = roleConfig.seafarer;
    return person?.screening.view;
  };

  return (
    <AccessHandlerWrapper hasRoleAccess={hasAccess()}>
      <Container>
        <Row>
          <Col>
            <Button onClick={onScreenClick}>Screen the seafarer</Button>
          </Col>
        </Row>
        <Row>
          <Col>
            <h2>Person basic info</h2>
            <textarea
              id="seafarer"
              name="seafarer"
              rows="30"
              style={{ width: '100%' }}
              value={JSON.stringify(seafarer, undefined, 4)}
            ></textarea>
          </Col>
          <Col>
            <h2>World Check One Case</h2>
            <textarea
              id="wco_case"
              name="wco_case"
              rows="30"
              style={{ width: '100%' }}
              value={JSON.stringify(wcoCase, undefined, 4)}
            ></textarea>
          </Col>
        </Row>
        <Row>
          <Col>
            <h2>World Check One Case Results</h2>
            <textarea
              id="wco_case_results"
              name="wco_case_results"
              rows="30"
              style={{ width: '100%' }}
              value={JSON.stringify(wcoCaseResults, undefined, 4)}
            ></textarea>
          </Col>
        </Row>
      </Container>
    </AccessHandlerWrapper>
  );
}

export default WorldCheckOnePage;
