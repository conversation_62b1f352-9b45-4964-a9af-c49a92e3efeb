/* eslint-disable react/display-name */
/* eslint-disable react/prop-types */
import React, { useState, useEffect, useRef } from 'react';
import { useParams, useHistory, Switch, Route } from 'react-router-dom';
import { Container, Row, Col, Button } from 'react-bootstrap';
import DocumentTable from '../component/OtherDocument/DocumentTable';
import seafarerService from '../service/seafarer-service';
import { dateAsString, stringAsDate } from '../model/utils';
import { ExpiryDateStyling } from '../component/common/ExpiryDateStyling';
import { ALL_DOC_TYPES } from '../constants/documentTypes';
import * as documentTypes from '../constants/documentTypes';
import AddSeafarerController from '../controller/add-seafarer-controller';
import ConfirmActionModalView from '../component/AddSeafarer/ConfirmActionModalView';
import styleGuide from '../styleGuide';
import _ from 'lodash';
import AddDocumentModal from '../component/document/AddDocumentModal';
import ErrorDisplayModal from '../component/common/ErrorDisplayModal';
import DocumentDownloadFailedModal from '../component/document/DocumentDownloadFailedModal';
import { scrollToSection } from '../util/view-utils';
const { Icon } = styleGuide;

const addSeafarerController = new AddSeafarerController();

const { DOC_DOWNLOAD_FAIL_MESSAGE } = process.env;

const prepareEndorsementData = (ele, endorsementDropdownData) => {
  const { seafarer_doc_endorsement } = ele;
  if (!seafarer_doc_endorsement) return;

  const docName = ele.doc_path ? ele.doc_path.split('/').pop().split('_').slice(1).join('_') : '';
  const fileName = `Endorsement_${docName}`;
  return {
    title: endorsementDropdownData?.find(
      (ele) => ele.id === seafarer_doc_endorsement.endorsement_id,
    )?.value,
    issuedBy: seafarer_doc_endorsement.issued_by,
    certificateNumber: seafarer_doc_endorsement.certificate_no,
    dateOfIssue: dateAsString(stringAsDate(seafarer_doc_endorsement.date_of_issue)),
    dateOfExpiry: seafarer_doc_endorsement.date_of_expiry,
    docPath: ele.doc_path,
    fileName: fileName.replace(' ', '_'),
    document: {
      id: seafarer_doc_endorsement.seafarer_document_id,
    },
    documentType: ele.type,
  };
};

const prepareVerificationData = (ele) => {
  const { seafarer_doc_verification } = ele;
  if (!seafarer_doc_verification) return;

  const docName = ele.doc_path ? ele.doc_path.split('/').pop().split('_').slice(1).join('_') : '';
  const fileName = `Verification_${docName}`;
  return {
    title: seafarer_doc_verification.type_of_verification,
    issuedBy: null,
    certificateNumber: null,
    dateOfIssue: dateAsString(stringAsDate(seafarer_doc_verification.date_of_issue)),
    dateOfExpiry: seafarer_doc_verification.date_of_expiry,
    docPath: ele.doc_path,
    fileName: fileName.replace(' ', '_'),
    document: {
      id: seafarer_doc_verification.seafarer_document_id,
    },
    documentType: ele.type,
  };
};

const prepareDceVerificationData = (ele) => {
  const { seafarer_doc_dce_verification } = ele;
  if (!seafarer_doc_dce_verification) return;

  const docName = ele.doc_path ? ele.doc_path.split('/').pop().split('_').slice(1).join('_') : '';
  const fileName = `DCE_Verification_${docName}`;
  return {
    title: seafarer_doc_dce_verification.type_of_verification,
    issuedBy: null,
    certificateNumber: null,
    dateOfIssue: dateAsString(stringAsDate(seafarer_doc_dce_verification.date_of_issue)),
    dateOfExpiry: seafarer_doc_dce_verification.date_of_expiry,
    docPath: ele.doc_path,
    fileName: fileName.replace(' ', '_'),
    document: {
      id: seafarer_doc_dce_verification.seafarer_document_id,
    },
    documentType: ele.type,
  };
};

const generateDocumentColumns = (downloadFile) => {
  return [
    {
      Header: 'Documents',
      id: 'documents',
      accessor: function documentAccessor(row) {
        return (
          <Button
            variant="link"
            className="selected-file-link"
            onClick={() => downloadFile(row?.documentType, { id: row?.document?.id }, row.docPath)}
          >
            {row?.docPath ? 'View' : ''}
          </Button>
        );
      },
      disableSortBy: true,
      maxWidth: 100,
    },
  ];
};

const generateActionColumn = (showDeleteAndEdit, editHandler, deleteHandler) => {
  return [
    {
      Header: function () {
        return <div className="text-center">Actions</div>;
      },
      id: 'action',
      accessor: function actionAccessor(row) {
        return (
          <div className="action-column">
            {showDeleteAndEdit && (
              <div style={{ display: 'flex' }}>
                <Button
                  variant="link"
                  onClick={() => {
                    editHandler(row?.documentType, row?.document?.id);
                  }}
                  data-testid="edit-doc-btn"
                >
                  <Icon icon="pencil" size={20} className="mr-3" />
                </Button>
                <Button
                  variant="link"
                  onClick={() => {
                    deleteHandler(row?.document?.id, row?.documentType);
                  }}
                  data-testid="delete-doc-btn"
                >
                  <Icon icon="bin" size={20} />
                </Button>
              </div>
            )}
          </div>
        );
      },
      disableSortBy: true,
      maxWidth: 50,
      sticky: 'right',
    },
  ];
};

const generateEndorsementColumns = (documentColumn, actionColumn) => {
  return [
    {
      Header: 'Title',
      id: 'title',
      accessor: function issueByAccessor(row) {
        return <div>{row?.title ?? '---'}</div>;
      },
      disableSortBy: true,
      maxWidth: 110,
    },
    {
      Header: 'Issued by',
      id: 'issue_by',
      accessor: function issueByAccessor(row) {
        return <div>{row?.issuedBy ?? '---'}</div>;
      },
      disableSortBy: true,
      maxWidth: 110,
    },
    {
      Header: 'Certificate Number',
      id: 'certificate_number',
      accessor: function certificateNumberAccessor(row) {
        return <div>{row?.certificateNumber ?? '---'}</div>;
      },
      disableSortBy: true,
      maxWidth: 110,
    },
    {
      Header: 'Date of Issue',
      id: 'date-of-issue',
      accessor: function dateOfIssueAccessor(row) {
        return <div>{row?.dateOfIssue ? dateAsString(row?.dateOfIssue) : '---'}</div>;
      },
      disableSortBy: true,
      maxWidth: 100,
    },
    {
      Header: 'Date of Expiry',
      id: 'date-of-expiry',
      accessor: function dateOfExpiryAccessor(row) {
        return (
          <div>{row?.dateOfExpiry ? <ExpiryDateStyling value={row?.dateOfExpiry} /> : '---'}</div>
        );
      },
      disableSortBy: true,
      maxWidth: 100,
    },

    ...documentColumn,
    ...actionColumn,
  ];
};

const generateDceOrDocVerificationColumns = (documentColumn, actionColumn) => {
  return [
    {
      Header: 'Title',
      id: 'title',
      accessor: function issueByAccessor(row) {
        return <div>{row?.title ?? '---'}</div>;
      },
      disableSortBy: true,
      maxWidth: 110,
    },
    {
      Header: 'Date of Issue',
      id: 'date-of-issue',
      accessor: function dateOfIssueAccessor(row) {
        return <div>{row?.dateOfIssue ? dateAsString(row?.dateOfIssue) : '---'}</div>;
      },
      disableSortBy: true,
      maxWidth: 100,
    },
    {
      Header: 'Date of Expiry',
      id: 'date-of-expiry',
      accessor: function dateOfExpiryAccessor(row) {
        return (
          <div>{row?.dateOfExpiry ? <ExpiryDateStyling value={row?.dateOfExpiry} /> : '---'}</div>
        );
      },
      disableSortBy: true,
      maxWidth: 100,
    },

    ...documentColumn,
    ...actionColumn,
  ];
};

const EndorsementAndVerification = ({ seafarer, roleConfig, dropdownData, eventTracker }) => {
  let { seafarerId, documentId } = useParams();
  const seafarerPersonId = seafarer?.seafarer_person_id;
  const history = useHistory();
  const [showDownloadFailModal, setShowDownloadFailModal] = useState(false);
  const [endorsementData, setEndorsementData] = useState([]);
  const [dceVerificationData, setDceVerificationData] = useState([]);
  const [documentVerificationData, setDocumentVerificationData] = useState([]);
  const [selectDocumentID, setSelectDocumentID] = useState(undefined);
  const [selectDocumentType, setSelectDocumentType] = useState(undefined);
  const [isLoading, setIsLoading] = useState(false);

  const [isRemoveDocumentConfirmationModalShow, setIsRemoveDocumentConfirmationModalShow] =
    useState(false);
  const [hash, setHash] = useState(undefined);
  const [isDisableConfirmDeleteBtn, setIsDisableConfirmDeleteBtn] = useState(false);
  const [modalMessage, setModalMessage] = useState(null);
  const [isModalLoading, setIsModalLoading] = useState(false);

  const showDeleteAndEdit = roleConfig.seafarer.edit.seafarerDocument;

  const endorsementSectionRef = useRef(null);
  const dceVerificationSectionRef = useRef(null);
  const documentVerificationSectionRef = useRef(null);

  const loadDocsData = async (seafarerPersonId, queryParam) => {
    const { data } = await seafarerService.getSeafarerDocumentsList(seafarerPersonId, queryParam);
    let responseObj = {};
    for (const dataObj of data) {
      if (responseObj[dataObj.type]) {
        responseObj[dataObj.type].push(dataObj);
      } else {
        responseObj[dataObj.type] = [dataObj];
      }
    }

    return responseObj;
  };

  const hashToReloadAndScrollHandler = {
    '#endorsement': async () => {
      const queryParam = '?type=endorsement';
      const responseObj = await loadDocsData(seafarerPersonId, queryParam);
      _setEndorsementData(responseObj, dropdownData?.endorsements);
      setTimeout(() => scrollToSection(endorsementSectionRef, 0));
    },
    '#dce-verification': async () => {
      const queryParam = '?type=dce_verification';
      const responseObj = await loadDocsData(seafarerPersonId, queryParam);
      _setDceVerificationData(responseObj);
      setTimeout(() => scrollToSection(dceVerificationSectionRef, 0));
    },
    '#document-verification': async () => {
      const queryParam = '?type=verification';
      const responseObj = await loadDocsData(seafarerPersonId, queryParam);
      _setDocumentVerificationData(responseObj);
      setTimeout(() => scrollToSection(documentVerificationSectionRef, 0));
    },
  };

  const _setEndorsementData = (responseObj, endorsementDropDowndData) => {
    const endorsementData = responseObj?.endorsement
      ? responseObj.endorsement
          .map((ele) => prepareEndorsementData(ele, endorsementDropDowndData))
          .filter((ele) => ele)
      : [];

    const sortedResult = _.orderBy(endorsementData, ['title', 'dateOfIssue'], ['asc', 'desc']);
    setEndorsementData(sortedResult);
  };

  const _setDceVerificationData = (responseObj) => {
    const dceVerificationData = responseObj?.dce_verification
      ? responseObj.dce_verification
          .map((ele) => prepareDceVerificationData(ele))
          .filter((ele) => ele)
      : [];

    const sortedResult = _.orderBy(dceVerificationData, ['title', 'dateOfIssue'], ['asc', 'desc']);
    setDceVerificationData(sortedResult);
  };

  const _setDocumentVerificationData = (responseObj) => {
    const verificationData = responseObj?.verification
      ? responseObj.verification.map((ele) => prepareVerificationData(ele)).filter((ele) => ele)
      : [];

    const sortedResult = _.orderBy(verificationData, ['title', 'dateOfIssue'], ['asc', 'desc']);
    setDocumentVerificationData(sortedResult);
  };

  useEffect(() => {
    (async () => {
      try {
        setIsLoading(true);
        if (seafarer?.seafarer_person_id) {
          const query = '?type=endorsement&type=verification&type=dce_verification';
          const responseObj = await loadDocsData(seafarerPersonId, query);
          const dropDownDataResponse = await addSeafarerController.loadDropDownData();
          _setEndorsementData(responseObj, dropDownDataResponse?.endorsements);
          _setDceVerificationData(responseObj);
          _setDocumentVerificationData(responseObj);
        }
      } catch (error) {
        console.log('## Error', error);
      }
      setIsLoading(false);
    })();
  }, [seafarer]);

  useEffect(() => {
    if (history.location.hash !== undefined && history.location.hash !== '') {
      setHash(history.location.hash);
    } else {
      setHash(undefined);
    }
  });

  useEffect(() => {
    (async () => {
      //scroll to section based on hash
      const reloadAndScrollHandler = hashToReloadAndScrollHandler[history.location.hash];

      if (reloadAndScrollHandler) {
        setIsLoading(true);
        await reloadAndScrollHandler();
        setIsLoading(false);
      }
    })();
  }, [hash]);

  const onHideModalMessage = () => setModalMessage(null);

  const downloadFile = async (documentType, document, fileName) => {
    if (isLoading) {
      return;
    }
    if (documentType && document && fileName) {
      window.open(
        `https://${window.location.hostname}/seafarer/document/${document.id}/${documentType}`,
      );
    }
    eventTracker('viewDocument', 'Endorsement Verification');
  };

  const editHandler = async (documentType, documentId) => {
    eventTracker('editDocument', 'Document');
    history.push(`/seafarer/details/${seafarerId}/endorsement/${documentType}/edit/${documentId}`);
  };

  const deleteHandler = async (id, type) => {
    eventTracker('deleteDocument', 'Document');
    setSelectDocumentID(id);
    setSelectDocumentType(type);
    setIsRemoveDocumentConfirmationModalShow(true);
  };

  const handleDeleteSeafarerDocument = async () => {
    try {
      setIsDisableConfirmDeleteBtn(true);
      setIsModalLoading(true);
      await seafarerService.deleteSeafarerDocument(selectDocumentID);
      if (selectDocumentType === ALL_DOC_TYPES.ENDORSEMENT) {
        const newData = endorsementData?.filter((ele) => ele.document.id !== selectDocumentID);
        setEndorsementData(newData);
      } else if (selectDocumentType === ALL_DOC_TYPES.DCE_VERIFICAITON) {
        const newData = dceVerificationData?.filter((ele) => ele.document.id !== selectDocumentID);
        setDceVerificationData(newData);
      } else if (selectDocumentType === ALL_DOC_TYPES.VERIFICATION) {
        const newData = documentVerificationData?.filter(
          (ele) => ele.document.id !== selectDocumentID,
        );
        setDocumentVerificationData(newData);
      }

      setIsModalLoading(false);
      setIsRemoveDocumentConfirmationModalShow(false);
      setIsDisableConfirmDeleteBtn(false);
    } catch (error) {
      setIsModalLoading(false);
      setIsRemoveDocumentConfirmationModalShow(false);
      setModalMessage('Oops something went wrong while deleting seafarer document.');
      console.error(`Delete seafarer Document by ID: ${selectDocumentID} failed. Error: ${error}`);
      setIsDisableConfirmDeleteBtn(false);
    }
  };

  const documentColumn = generateDocumentColumns(downloadFile);

  const actionColumn = generateActionColumn(showDeleteAndEdit, editHandler, deleteHandler);

  const endorsementColumns = generateEndorsementColumns(documentColumn, actionColumn);

  const dceVerificationColumns = generateDceOrDocVerificationColumns(documentColumn, actionColumn);

  const documentVerificationColumns = generateDceOrDocVerificationColumns(
    documentColumn,
    actionColumn,
  );

  return (
    <Container>
      <div>
        <Row>
          <Col>
            <div className="font-weight-bold p-2 document-table-title" ref={endorsementSectionRef}>
              ENDORSEMENT
            </div>
          </Col>
        </Row>
        <Row>
          <Col>
            <DocumentTable
              data={endorsementData}
              loading={isLoading}
              columns={endorsementColumns}
              dataTestId="endorsement-table"
            />
          </Col>
        </Row>
        <Row>
          <Col>
            <div
              className="font-weight-bold p-2 document-table-title"
              ref={dceVerificationSectionRef}
            >
              DCE VERIFICATION
            </div>
          </Col>
        </Row>
        <Row>
          <Col>
            <DocumentTable
              data={dceVerificationData}
              loading={isLoading}
              columns={dceVerificationColumns}
              dataTestId="dce-verification-table"
            />
          </Col>
        </Row>
        <Row>
          <Col>
            <div
              className="font-weight-bold p-2 document-table-title"
              ref={documentVerificationSectionRef}
            >
              DOCUMENT VERIFICATION
            </div>
          </Col>
        </Row>
        <Row>
          <Col>
            <DocumentTable
              data={documentVerificationData}
              loading={isLoading}
              columns={documentVerificationColumns}
              dataTestId="document-verification-table"
            />
          </Col>
        </Row>

        <ConfirmActionModalView
          show={isRemoveDocumentConfirmationModalShow}
          onClose={() => setIsRemoveDocumentConfirmationModalShow(false)}
          onConfirm={handleDeleteSeafarerDocument}
          title={'Confirm Deleting the document?'}
          message={'Are you sure deleting the document?'}
          isDisableConfirmDeleteBtn={isDisableConfirmDeleteBtn}
          isModalLoading={isModalLoading}
        />
        <ErrorDisplayModal onHideModalMessage={onHideModalMessage} modalMessage={modalMessage} />
        <DocumentDownloadFailedModal
          show={showDownloadFailModal}
          onClose={() => setShowDownloadFailModal(false)}
          title={'Download Failed'}
        >
          <p>{DOC_DOWNLOAD_FAIL_MESSAGE}</p>
        </DocumentDownloadFailedModal>
        {/* route to popup modal for editing documents*/}
        <Switch>
          {documentTypes.ENDORSEMENT_VERIFICATION_TAB_DOC_TYPE_KEYS.map((doc_type_key) => (
            <Route
              key={doc_type_key}
              exact
              path={`/seafarer/details/:seafarerId/endorsement/${documentTypes.ALL_DOC_TYPES[doc_type_key]}/edit/:documentId`}
            >
              <AddDocumentModal
                targetId={documentTypes.DOC_FORM_IDS[doc_type_key]}
                seafarerPersonId={seafarer.seafarer_person_id}
                history={history}
                docId={documentId}
                dropdownData={dropdownData}
                eventTracker={eventTracker}
              ></AddDocumentModal>
            </Route>
          ))}
        </Switch>
      </div>
    </Container>
  );
};

export default EndorsementAndVerification;
