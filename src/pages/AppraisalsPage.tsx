import React, { useEffect, useState } from 'react';
import { But<PERSON>, Container, Col, Row, OverlayTrigger, Tooltip } from 'react-bootstrap';
import DocumentTable from '../component/OtherDocument/DocumentTable';
import {
  superintendentApppraisalsMenuItems,
  masterApppraisalsMenuItems,
  DebriefingMenuItems,
  trainingRequirementsMenuItems,
  setRoleConfig,
} from '../component/appraisals/MenuList';
import { getSuptAppraisalList, getDebriefingList } from '../service/seafarer-survery-service';
import { useParams, useHistory, Switch, Route } from 'react-router-dom';
import AddTrainingRequirementModal from '../component/document/AddTrainingRequirementModal';
import AddSeafarerController from '../controller/add-seafarer-controller';
import { SuptAppraisalResult } from '../types/suptAppraisal';
import GA4React from 'ga-4-react';
import {
  getMasterAppraisalList,
  getTrainingReqListBySeafarerId,
} from '../service/seafarer-service';
import { MasterAppraisalResult } from '../types/masterAppraisal';
import AccessHandlerWrapper from '../component/common/AccessHandlerWrapper';
import { DebriefingResult } from '@src/types/debreifing';
import { TrainingRequirementResult } from '../types/trainingRequirement';
import { Seafarer } from '../types/seafarerInterfaces';
import { ACCOUNT_STATUSES } from '../constants/seafarer-status';
import EditTrainingRequirementModal from '../component/document/EditTrainingRequirementModal';
import styleGuide from '../styleGuide';
import moment from 'moment';
import { useAccess } from '@src/component/common/Access';

/*global Props, SeafarerRank, ga4react, id, value, unit*/
/*eslint no-undef: "error"*/
interface SeafarerRank {
  id: number;
  value: string;
  unit: string;
}

interface Props {
  ga4react: GA4React;
  // eslint-disable-next-line no-undef
  seafarer: Seafarer;
  // eslint-disable-next-line no-undef
  refreshDetailsPageData: any;
}

const { Icon } = styleGuide;

const addSeafarerController = new AddSeafarerController();

const AppraisalsPage = ({ ga4react, seafarer, refreshDetailsPageData }: Props) => {
  const [suptAppraisalData, setSuptAppraisalData] = useState<SuptAppraisalResult[]>([]);
  const [masterAppraisalData, setMasterAppraisalData] = useState<MasterAppraisalResult[]>([]);
  const [debriefingData, setDebriefingData] = useState<DebriefingResult[]>([]);
  const [trainingReqData, setTrainingReqData] = useState<TrainingRequirementResult[]>([]);
  const [invTrainingReqData, setInvTrainingReqData] = useState<TrainingRequirementResult[]>([]);
  const [isLoadingSuptAppraisal, setIsLoadingSuptAppraisal] = useState(false);
  const [isLoadingMasterAppraisal, setIsLoadingMasterAppraisal] = useState(false);
  const [isLoadingDebriefing, setIsLoadingDebriefing] = useState(false);
  const [isLoadingTrainingRequirement, setIsLoadingTrainingRequirement] = useState(false);
  const [showAddTrainingRequirementModal, setShowAddTrainingRequirementModal] = useState(false);
  const [isCallTrainingRequirement, setIsCallTrainingRequirement] = useState(false);
  const [isInvestigationTrainingReq, setIsInvestigationTrainingReq] = useState(false);
  const history = useHistory();
  const { roleConfig } = useAccess();
  const { seafarerId } = useParams();

  const hasRoleAccess =
    roleConfig.seafarer.view.viewSuptAppraisal ||
    roleConfig.seafarer.view.viewMasterAppraisal ||
    roleConfig.seafarer.view.viewDebriefing ||
    roleConfig.seafarer.view.viewTraining;

  const editHandler = async (seafarerId: number, trainingReqId: number, type: number) => {
    if (type === 1) {
      history.push(
        `/seafarer/details/${seafarerId}/appraisals/investigation-training-req/${trainingReqId}/edit`,
      );
    } else {
      history.push(`/seafarer/details/${seafarerId}/appraisals/training-req/${trainingReqId}/edit`);
    }
  };

  // Doesnt make sense to move these props to parent
  // NOSONAR
  const action = [
    {
      // eslint-disable-next-line react/display-name
      Header: function () {
        return <div className="text-center">Actions</div>;
      },
      id: 'action',
      accessor: function actionAccessor(row: TrainingRequirementResult) {
        const canEditTrainingReq: boolean =
          roleConfig?.seafarer?.edit?.training || roleConfig?.seafarer?.edit?.suptTraining;
        let isEditable = true;
        if (row.completed_date_created_at) {
          const currentDate = moment();
          const disabledDate = moment(row.completed_date_created_at).add(15, 'days');
          isEditable = !disabledDate.isBefore(currentDate);
        }
        if (!(canEditTrainingReq && isEditable)) return null;
        const isEditableWithDate = row?.completed_date_created_at;

        return (
          <div className="action-column">
            {isEditableWithDate ? (
              <OverlayTrigger
                key="bottom"
                placement="bottom"
                overlay={
                  <Tooltip id={`tooltip-bottom`}>
                    Editable until{' '}
                    {moment(row.completed_date_created_at).add(15, 'days').format('DD MMM')}
                    <br />
                    (15 days since completed day)
                  </Tooltip>
                }
              >
                <span>
                  <Button
                    variant="link"
                    onClick={() => editHandler(row?.seafarer_id, row.id, row.type)}
                    data-testid="edit-doc-btn"
                  >
                    <Icon icon="pencil" size={20} className="mr-3" />
                  </Button>
                </span>
              </OverlayTrigger>
            ) :
              (
                <span>
                  <Button
                    variant="link"
                    onClick={() => editHandler(row?.seafarer_id, row.id, row.type)}
                    data-testid="edit-doc-btn"
                  >
                    <Icon icon="pencil" size={20} className="mr-3" />
                  </Button>
                </span>
              )}
          </div>
        );
      },
      disableSortBy: true,
      maxWidth: 50,
      sticky: 'right',
    },
  ];

  const trainingRequirementsColumns = [...trainingRequirementsMenuItems, ...action];

  const getSuptAppraisal = async () => {
    setIsLoadingSuptAppraisal(true);
    try {
      const params = `?seafarer_id=${seafarerId}&limit=100&orderBy=survey_participation_superintendent_appraisal.created_at desc`;
      const suptAppraisalResponse = await getSuptAppraisalList(params);
      const seafarerRecords: SuptAppraisalResult[] = suptAppraisalResponse?.data?.results;
      const dropDownDataResponse = await addSeafarerController.loadDropDownData();
      const seafarerRecordsWithRankDetails = seafarerRecords.map((r) => {
        const matchedRank = dropDownDataResponse.ranks.find(
          (i: SeafarerRank) => i.id === r?.survey_participation_superintendent_appraisal?.rank_id,
        );
        if (matchedRank) {
          Object.assign(r.survey_participation_superintendent_appraisal, {
            rank_value: matchedRank.value,
            rank_unit: matchedRank.unit,
          });
        }
        return {
          ...r,
          ga4react: ga4react,
        };
      });
      setSuptAppraisalData(seafarerRecordsWithRankDetails);
    } catch (err) {
      console.log('error occured while fetching supt appraisal data', err);
    }
    setIsLoadingSuptAppraisal(false);
  };

  const getMasterAppraisal = async () => {
    setIsLoadingMasterAppraisal(true);
    try {
      const masterAppraisalParams = `seafarer_id=${seafarerId}&orderBy=created_at%20desc`;
      const masterAppraisalResponse = await getMasterAppraisalList(masterAppraisalParams);
      const masterAppraisalResult: MasterAppraisalResult[] = masterAppraisalResponse?.data?.results;

      const masterAppraisalRecords = masterAppraisalResult.map((res) => {
        return {
          ...res,
          ga4react: ga4react,
        };
      });

      setMasterAppraisalData(masterAppraisalRecords);
    } catch (err) {
      console.log('error occured while fetching master appraisal data', err);
    }
    setIsLoadingMasterAppraisal(false);
  };

  const getDebriefing = async () => {
    setIsLoadingDebriefing(true);
    try {
      const debriefingParams = `?seafarer_id=${seafarerId}&status=done&orderBy=created_at%20desc`;
      const debriefingResponse = await getDebriefingList(debriefingParams);
      const debriefingResult: DebriefingResult[] = debriefingResponse?.data?.results;

      const debriefingRecords = debriefingResult.map((res) => {
        return {
          ...res,
          ga4react: ga4react,
        };
      });

      setDebriefingData(debriefingRecords);
    } catch (err) {
      console.log('error occured while fetching debriefing data', err);
    }
    setIsLoadingDebriefing(false);
  };

  const getTrainingRequirement = async () => {
    setIsLoadingTrainingRequirement(true);
    try {
      const { data } = await getTrainingReqListBySeafarerId(seafarerId);
      let trainingReqRecords = [];
      let investigationTrainingReqRecords = [];
      data.forEach((res: TrainingRequirementResult) => {
        if (res?.type === 1) {
          investigationTrainingReqRecords.push({
            ...res,
            ga4react: ga4react,
          });
        } else {
          trainingReqRecords.push({
            ...res,
            ga4react: ga4react,
          });
        }
      });

      setTrainingReqData(trainingReqRecords);
      setInvTrainingReqData(investigationTrainingReqRecords);
      setIsLoadingTrainingRequirement(false);
    } catch (err) {
      console.error(
        `Get seafarer training requirements by Seafarer ID: ${seafarerId} failed. Error: ${err}`,
      );
      setIsLoadingTrainingRequirement(false);
    }
  };

  useEffect(() => {
    setRoleConfig(roleConfig);
    roleConfig.seafarer.view.viewSuptAppraisal && getSuptAppraisal();
    roleConfig.seafarer.view.viewMasterAppraisal && getMasterAppraisal();
    roleConfig.seafarer.view.viewDebriefing && getDebriefing();
  }, []);

  const isCurrentAccountStatusActive =
    seafarer?.seafarer_person?.current_account_status === ACCOUNT_STATUSES.ACTIVE;

  const callTrainingRequirement = () => {
    setIsCallTrainingRequirement(true);
  };

  useEffect(() => {
    roleConfig.seafarer.view.viewTraining && getTrainingRequirement();
    isCallTrainingRequirement && refreshDetailsPageData();
  }, [isCallTrainingRequirement]);

  return (
    <AccessHandlerWrapper hasRoleAccess={hasRoleAccess}>
      <Container>
        {showAddTrainingRequirementModal && (
          <AddTrainingRequirementModal
            isFromMasterAppraisal={false}
            setShowAddTrainingRequirementModal={setShowAddTrainingRequirementModal}
            callTrainingRequirement={callTrainingRequirement}
            isInvestigationTrainingReq={isInvestigationTrainingReq}
          />
        )}
        {roleConfig.seafarer.view.viewSuptAppraisal && (
          <>
            <Row>
              <Col>
                <div className="font-weight-bold p-2 document-table-title">
                  SUPERINTENDENT APPRAISALS
                </div>
              </Col>
            </Row>
            <Row>
              <Col className="mb-3">
                <div className="appraisal-table">
                  <DocumentTable
                    data={suptAppraisalData}
                    loading={isLoadingSuptAppraisal}
                    columns={superintendentApppraisalsMenuItems}
                    dataTestId="superintendent-appraisals-table"
                  />
                </div>
              </Col>
            </Row>
            <hr className="section_line" />
          </>
        )}
        {roleConfig.seafarer.view.viewMasterAppraisal && (
          <>
            <Row>
              <Col>
                <div className="font-weight-bold p-2 document-table-title">MASTER APPRAISALS</div>
              </Col>
            </Row>
            <Row>
              <Col className="mb-3">
                <div className="appraisal-table">
                  <DocumentTable
                    data={masterAppraisalData}
                    loading={isLoadingMasterAppraisal}
                    columns={masterApppraisalsMenuItems}
                    dataTestId="master-appraisals-table"
                  />
                </div>
              </Col>
            </Row>
            <hr className="section_line" />
          </>
        )}
        {roleConfig.seafarer.view.viewDebriefing && (
          <>
            <Row>
              <Col>
                <div className="font-weight-bold p-2 document-table-title">DEBRIEFING</div>
              </Col>
            </Row>
            <Row>
              <Col className="mb-3">
                <div className="appraisal-table">
                  <DocumentTable
                    data={debriefingData}
                    loading={isLoadingDebriefing}
                    columns={DebriefingMenuItems}
                    dataTestId="debriefing-appraisals-table"
                  />
                </div>
              </Col>
            </Row>
            <hr className="section_line" />
          </>
        )}
        {roleConfig.seafarer.view.viewTraining && (
          <>
            <Row>
              <Col>
                <div className="font-weight-bold p-2 document-table-title">
                  TRAINING REQUIREMENTS
                </div>
              </Col>
              <Col className="d-flex justify-content-end">
                {roleConfig.seafarer.add?.training && isCurrentAccountStatusActive && (
                  <Button
                    variant="outline-primary"
                    onClick={() => {
                      setShowAddTrainingRequirementModal(true);
                      setIsInvestigationTrainingReq(false);
                    }}
                    data-testid="fml-add-training"
                  >
                    Add
                  </Button>
                )}
              </Col>
            </Row>
            <Row>
              <Col className="mb-3">
                <div className="appraisal-table">
                  <DocumentTable
                    data={trainingReqData}
                    loading={isLoadingTrainingRequirement}
                    columns={trainingRequirementsColumns}
                    dataTestId="training-requirements-table"
                  />
                </div>
              </Col>
            </Row>
          </>
        )}
        {roleConfig.seafarer.view.viewTraining && (
          <>
            <Row>
              <Col>
                <div className="font-weight-bold p-2 document-table-title">
                  INVESTIGATION OF TRAINING REQUIREMENTS
                </div>
              </Col>
              <Col className="d-flex justify-content-end">
                {roleConfig.seafarer.add?.training && isCurrentAccountStatusActive && (
                  <Button
                    variant="outline-primary"
                    onClick={() => {
                      setShowAddTrainingRequirementModal(true);
                      setIsInvestigationTrainingReq(true);
                    }}
                    data-testid="fml-add-investigation-training"
                  >
                    Add
                  </Button>
                )}
              </Col>
            </Row>
            <Row>
              <Col className="mb-3">
                <div className="appraisal-table">
                  <DocumentTable
                    data={invTrainingReqData}
                    loading={isLoadingTrainingRequirement}
                    columns={trainingRequirementsColumns}
                    dataTestId="investigation-training-requirements-table"
                  />
                </div>
              </Col>
            </Row>
          </>
        )}
        <Switch>
          <Route
            exact
            path={`/seafarer/details/:seafarerId/appraisals/training-req/:trainingReqId/edit`}
          >
            <EditTrainingRequirementModal
              history={history}
              callTrainingRequirement={callTrainingRequirement}
              seafarerPersonId={seafarer?.seafarer_person_id}
              roleConfig={roleConfig}
              isInvestigationTrainingReq={false}
            />
          </Route>
          <Route
            exact
            path={`/seafarer/details/:seafarerId/appraisals/investigation-training-req/:trainingReqId/edit`}
          >
            <EditTrainingRequirementModal
              history={history}
              callTrainingRequirement={callTrainingRequirement}
              seafarerPersonId={seafarer?.seafarer_person_id}
              roleConfig={roleConfig}
              isInvestigationTrainingReq={true}
            />
          </Route>
        </Switch>
      </Container>
    </AccessHandlerWrapper>
  );
};

export default AppraisalsPage;
