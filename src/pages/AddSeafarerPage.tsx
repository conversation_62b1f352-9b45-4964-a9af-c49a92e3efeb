import React, { useState, useEffect, useRef, useLayoutEffect, useMemo } from 'react';
import moment from 'moment';
import { Route, Switch, useParams, useHistory, withRouter } from 'react-router-dom';
import { Formik, validateYupSchema, yupToFormErrors } from 'formik';
import { Container, Form, Col, Button, Row, Modal } from 'react-bootstrap';
import { Persist } from 'enhanced-formik-persist';
import _, { cloneDeep, toString } from 'lodash';
import NavigationBarItem from '../component/NavigationBarItem';
import DuplicateSeafarersComponent from '../component/AddSeafarer/DuplicateSeafarersComponent';
import AddSeafarerBasicForm from '../component/AddSeafarer/AddSeafarerBasicForm';
import AddSeafarerPersonalParticularsForm from '../component/AddSeafarer/AddSeafarerPersonalParticularsForm';
import {
  getBankAccounts,
  applyPersonAccountInfo,
} from '../component/AddSeafarer/AddSeafarerBankAccounts';
import AddSeafarerExperienceForm from '../component/AddSeafarer/AddSeafarerExperienceForm';
import AddSeafarerContactDetails from '../component/AddSeafarer/AddSeafarerContactDetails';
import ErrorsListComponent from '../component/ErrorsListComponent';
import InvalidDataComponent from '../component/AddSeafarer/InvalidDataComponent';
import AddSeafarerController from '../controller/add-seafarer-controller';
import styleGuide, { ErrorPage } from '../styleGuide';
import BottomButton from '../component/advanced_search/BottomButton';
import { screeningStatus, searchType } from '../model/constants';
import Spinner from '../component/common/Spinner';
import AccessHandlerWrapper from '../component/common/AccessHandlerWrapper';
import ConfirmActionModalView from '../component/AddSeafarer/ConfirmActionModalView';
import './scss/modal.scss';
import {
  getBasicFormErrors,
  hasContactErrors,
  hasBankErrors,
  hasPersonalParticularError,
  hasBasicFormErrors,
  doesNotContainDocNumber,
  getDuplicateNumbers,
} from '../util/form-utils';
import { scrollToSection, checkUserBelongToSameShipParty } from '../util/view-utils';
import {
  RANK_OFFICE_SECTION,
  PERSONAL_DETAILS_SECTION,
  PASSPORT_SECTION,
  SEAMAN_BOOK_SECTION,
  CONTACT_SECTION,
  FAMILY_MEMBER_SECTION,
} from '../constants/formSections';
import {
  BASIC_TAB,
  CONTACT_DETAILS_TAB,
  PERSONAL_PARTICULARS_TAB,
  EXPERIENCE_TAB,
} from '../constants/test-id/seafarer-form-test-id';
import FIELDS_TO_IGNORE from '../constants/form-persist-ignore-fields';
import seafarerSchema, { getSeamanBooksSchema } from '../model/SeafarerSchemaValidation';
import { BreadcrumbHeader } from '../component/BreadcrumpHeader';
import * as documentTypes from '../constants/documentTypes';
import { createSeafarerDocument } from '../service/seafarer-service';
import { useAccess } from '@src/component/common/Access';

const { Icon } = styleGuide;

const PROCEED_OTHER_TAB_FAILED_MESSAGE = {
  header: 'Fill in required fields before proceed',
  message: 'Please fill in all the required fields',
};

const ERROR_FAILED_TO_CREATE_MESSAGE = {
  header: 'Create seafarer failed',
  message: 'Create seafarer failed',
};

const ERROR_FAILED_TO_SAVE_MESSAGE = {
  header: 'Save seafarer failed',
  message: 'Save seafarer failed',
};

const basicToAccountHolderMap = {
  first_name: 'first_name',
  middle_name: 'middle_name',
  last_name: 'last_name',
  nationality_id: 'nationality_id',
  date_of_birth: 'date_of_birth',
  gender: 'gender',
};

const getDuplicateErrorModal = (title) => ({
  header: `Seafarer(s) with the same ${title} already exists`,
  message: 'Please correct it in order to save.',
});

const handleDocumentDuplicate = (documents, setCallback, duplicateData) => {
  if (doesNotContainDocNumber(documents, duplicateData.number)) {
    setCallback([...documents, { ...duplicateData }]);
  }
};

const NavBarItem = ({ data, seafarerId, step, validate }) => {
  return (
    <Col className="p-0 m-0">
      <NavigationBarItem
        error={data.error}
        link={data.link}
        seafarerId={seafarerId}
        title={data.title}
        sm={2}
        isActive={step === data.link}
        onValidateLink={validate}
        testID={data.testID}
      />
    </Col>
  );
};

const AddSeafarerPage = withRouter(({ keycloak, location, ga4react }) => {
  const defaultDropDownValue = {
    countries: [],
    nationalities: [],
    offices: [],
  };
  const { roleConfig } = useAccess();
  const { current: controller } = useRef(new AddSeafarerController());
  const params = useParams();
  const { step = 'basic', seafarerId } = params;

  const history = useHistory();
  const [seafarer, setSeafarer] = useState(controller.getEmptySeafarer());
  const [loading, setLoading] = useState(true);
  const [dropDownData, setDropDownData] = useState(defaultDropDownValue);
  const selectedBank = useRef([]);
  const [portsMap, setPortsMap] = useState({});
  const [error, setError] = useState(null);
  const [modalMessage, setModalMessage] = useState(null);
  const [modalErrorData, setModalErrorData] = useState(null);
  const [triedValidate, setTriedValidate] = useState(false);
  const [showBankAccountNotCompleted, setShowBankAccountNotCompleted] = useState(false);
  const [cachedDuplicates, setCachedDuplicates] = useState({
    personalDetails: {},
  });

  const [duplicatePassports, setDuplicatePassports] = useState([]);
  const [duplicateSeamansBook, setDuplicateSeamansBook] = useState([]);
  const [isPageViewInvoked, setIsPageViewInvoked] = useState(false);
  const [title, setTitle] = useState('Add Seafarer');
  const [originalSeafarer, setOriginalSeafarer] = useState(null);
  const [showDataLossForCreateAlert, setShowDataLossForCreateAlert] = useState(false);
  const [showDataLossForEditAlert, setShowDataLossForEditAlert] = useState(false);
  const [currentPageCloseTriggerFromSource, setCurrentPageCloseTriggerFromSource] = useState();
  const [isSeamanBooksUpdated, setIsSeamanBooksUpdated] = useState(false);
  const [startTime, setStartTime] = useState(null);

  const basicRef = useRef(null);
  const personalDetailsRef = useRef(null);
  const passportRef = useRef(null);
  const seamanBookRef = useRef(null);
  const contactRef = useRef(null);
  const familyMemberRef = useRef(null);

  if (!isPageViewInvoked) {
    let pageTitle = 'Add Seafarer';

    if (seafarerId) {
      setTitle('Edit Seafarer');
      pageTitle = 'Edit Seafarer';
    }
    setStartTime(moment().valueOf());
    try {
      ga4react?.pageview(history.location.pathname, '', pageTitle);
      setIsPageViewInvoked(true);
    } catch (e) {
      console.log(e);
      setIsPageViewInvoked(true);
    }
  }
  const onHideModalMessage = () => setModalMessage(null);

  const createIceExperienceDocument = async (values, seafarerPersonId) => {
    const documentData = cloneDeep(values);
    Object.keys(documentData).forEach(function (key) {
      if (/file/.exec(key)) delete documentData[key];
    });
    const formData = new FormData();
    const docType =
      documentTypes.DOC_FORM_ID_TO_DB_DOC_TYPE[documentTypes.DOC_FORM_IDS.USER_DEFINED_DOCUMENT];

    documentData.user_defined_document_type_id = documentTypes.user_defined_document_type_id;

    const payload = {
      seafarer_person_id: seafarerPersonId,
      document_type: docType,
      document_data: documentData,
    };

    formData.append('body', JSON.stringify(payload));
    formData.append('document', values.file);
    try {
      const response = await createSeafarerDocument(formData);

      if (response.status === 201) {
        // create success
        console.log('Create ice experience document succeed');
      }
    } catch (error) {
      console.error(error);
    }
  };

  const onSubmitSeafarer = async () => {
    setLoading(true);
    try {
      const { id, seafarerPersonId } = await controller.onSubmitSeafarer();

      try {
        // create ice experience document here
        const iceExperienceDoc = controller?.seafarerChanges?.ice_experience_doc;
        if (iceExperienceDoc) {
          await createIceExperienceDocument(iceExperienceDoc, seafarerPersonId);
        }
      } catch (error) {
        throw new Error('Create ice experience fail while creating seafarer ...', { cause: error });
      }

      if (id) {
        const endTime = moment().valueOf();
        const spentTime = moment.utc(endTime - startTime).format('HH:mm:ss');
        ga4EventTrigger('Time Spent', 'Seafarer Edit', `Seafarer Edit - ${spentTime}`);
        history.push(`/seafarer/details/${id}/general`);
        localStorage.removeItem('seafarer-form');
      }
    } catch (error) {
      console.log('## error', error);
      setModalErrorData(error?.response?.data);
      if (seafarerId !== undefined && seafarerId !== null) {
        setModalMessage(ERROR_FAILED_TO_SAVE_MESSAGE);
      } else {
        setModalMessage(ERROR_FAILED_TO_CREATE_MESSAGE);
      }
    } finally {
      setLoading(false);
    }
  };

  const cacheDuplicates = (duplicates, type, document) => {
    const cache = { ...cachedDuplicates };

    if (type && type === searchType.PASSPORT) {
      if (duplicates && document) {
        handleDocumentDuplicate(duplicatePassports, setDuplicatePassports, duplicates[0]);
        return;
      }
    }

    if (type && type === searchType.SEAMANS_BOOK) {
      if (duplicates && document) {
        handleDocumentDuplicate(duplicateSeamansBook, setDuplicateSeamansBook, duplicates[0]);
        return;
      }
    }

    if (type && type === searchType.PERSONAL_DETAILS) {
      cache.personalDetails = {
        document,
        duplicates,
      };

      setCachedDuplicates(cache);
    }
  };

  const ga4EventTrigger = (action, category, label) => {
    try {
      ga4react?.event(action, toString(label), category, false);
    } catch (error) {
      console.log(error);
    }
  };

  const eventTracker = (type, value) => {
    switch (type) {
      case 'tabNavigation':
        ga4EventTrigger('Tab', 'Nav', `${title} - ${value}`);
        break;
      case 'submit':
        ga4EventTrigger('Save', title, title);
        break;
      case 'routeToDetailsPage':
      case 'routeToListPage':
        ga4EventTrigger('Close', title, title);
        break;
      case 'anchorLink':
        ga4EventTrigger('Alert Anchor Link', 'Nav', `${title} :${value}`);
        break;
      case 'routeTo':
        ga4EventTrigger('Breadcrumb', 'Nav', `Vessel List Page - ${value}`);
        break;
      default:
        ga4EventTrigger('Click', title, value);
        break;
    }
  };

  const NavBar = ({ validate, navBarErrorProps, canViewContacts }) => {
    const isContactDetailsHidden = roleConfig.seafarer.hidden.contactDetails;
    const { hasExistingContactErrors, hasExistingFamilyError, hasExistingBasicError } =
      navBarErrorProps;

    const getSteps = () => {
      const steps = [];
      steps.push({
        link: 'basic',
        title: 'Basic',
        error: hasExistingBasicError,
        testID: BASIC_TAB,
      });
      if (!isContactDetailsHidden && canViewContacts) {
        steps.push({
          link: 'contact-details',
          title: 'Contact Details',
          error: hasExistingContactErrors,
          testID: CONTACT_DETAILS_TAB,
        });
      }
      steps.push({
        link: 'personal-particulars',
        title: 'Personal Particulars',
        error: hasExistingFamilyError,
        testID: PERSONAL_PARTICULARS_TAB,
      });
      steps.push({ link: 'experience', title: 'Experience', testID: EXPERIENCE_TAB });
      return steps;
    };

    return (
      <Row className="nav-bar">
        {getSteps().map((item, index) => {
          return (
            <NavBarItem
              key={item.link}
              data={item}
              seafarerId={seafarerId}
              step={step}
              validate={validate}
            />
          );
        })}
      </Row>
    );
  };

  const disableFieldValidationBasedOnRole = () => {
    if (seafarerId) {
      if (!roleConfig.seafarer.edit.personalDetails) {
        delete cloneSeafarerSchema.fields.seafarer_person.fields.first_name;
        delete cloneSeafarerSchema.fields.seafarer_person.fields.last_name;
        delete cloneSeafarerSchema.fields.seafarer_person.fields.date_of_birth;
        delete cloneSeafarerSchema.fields.seafarer_person.fields.place_of_birth;
        delete cloneSeafarerSchema.fields.seafarer_person.fields.gender;
        delete cloneSeafarerSchema.fields.seafarer_person.fields.country_of_birth_id;
        delete cloneSeafarerSchema.fields.seafarer_person.fields.nationality_id;
      }
      if (!roleConfig.seafarer.edit.seamansBook) {
        delete cloneSeafarerSchema.fields.seafarer_person.fields.seaman_books;
      }
      if (!roleConfig.seafarer.edit.passport) {
        delete cloneSeafarerSchema.fields.seafarer_person.fields.passports;
      }
      if (!roleConfig.seafarer.edit.reportingOffice) {
        delete cloneSeafarerSchema.fields.office_id;
      }
      if (!roleConfig.seafarer.edit.rank) {
        delete cloneSeafarerSchema.fields.rank_id;
      }
      if (!roleConfig.seafarer.edit.personalParticulars) {
        delete cloneSeafarerSchema.fields.seafarer_person.fields.family_members;
      }
      if (!roleConfig.seafarer.edit.contactDetails) {
        delete cloneSeafarerSchema.fields.seafarer_person.fields.seafarer_contacts;
        delete cloneSeafarerSchema.fields.seafarer_person.fields.mobile_numbers;
        delete cloneSeafarerSchema.fields.seafarer_person.fields.telephone_numbers;
        delete cloneSeafarerSchema.fields.seafarer_person.fields.mobile_numbers;
      }
    }
    // Bank Account Form is not part of Add Seafarer Anymore
    delete cloneSeafarerSchema.fields.seafarer_person.fields.bank_accounts;

    return cloneSeafarerSchema;
  };

  const getCurrentPath = () => {
    const { pathname } = location;
    const currentSectionPath = pathname.split('/').filter(Boolean).pop();
    controller.step = currentSectionPath;
  };

  useEffect(() => {
    loadSeafarer(seafarerId);
    getCurrentPath();
  }, [seafarerId]);

  useEffect(() => {
    // scroll to passport/seaman's book section based on hash
    if (!loading) {
      if (history.location.hash === '#passports') {
        setTimeout(() => scrollToSection(passportRef), 0);
      } else if (history.location.hash === '#seaman-books') {
        setTimeout(() => scrollToSection(seamanBookRef), 0);
      }
    }
  }, [loading]);

  async function loadPortsOfIssueForSeamanBook(countries, seafarerData) {
    if (seafarerData?.seafarer_person?.seaman_books) {
      const countryCodes = seafarerData.seafarer_person.seaman_books
        .map(({ country_id }) => {
          const country = countries?.find(({ id }) => id === country_id);
          if (country) {
            return country.alpha2_code;
          }
          return null;
        })
        .filter((code) => code);
      const portsMapData = await controller.loadPortsOfIssueForSeamanBookMap(countryCodes);
      setPortsMap(portsMapData);
    }
  }

  function loadSeafarer(id) {
    (async () => {
      setLoading(true);
      try {
        const [
          seafarerData,
          dropDownDataResponse,
          seafarerReportingOfficeDropDownData,
          manningAgentDropDownData,
          bankNameResponse,
        ] = await Promise.all([
          controller.loadSeafarer(id),
          controller.loadDropDownData(),
          controller.loadSeafarerReportingOfficeDropDownData(),
          controller.loadManningAgentDropDownData(),
          controller.loadBankNames(),
        ]);

        if (id && Object.keys(seafarerData).length === 0) {
          setError('404 page error');
          return;
        }

        seafarerData?.seafarer_person?.bank_accounts?.forEach((account) => {
          applyPersonAccountInfo(account, seafarerData.seafarer_person);
        });

        setSeafarer(seafarerData);
        const cloneSeafarer = cloneDeep(seafarerData);
        setOriginalSeafarer(cloneSeafarer);
        controller.seafarerChanges = seafarerData;
        controller.initialValues = cloneDeep(seafarerData);
        dropDownDataResponse.offices = seafarerReportingOfficeDropDownData;
        dropDownDataResponse.manningAgents = manningAgentDropDownData;
        dropDownDataResponse.bankNames = bankNameResponse.data;
        setDropDownData(dropDownDataResponse);

        await loadPortsOfIssueForSeamanBook(dropDownDataResponse.countries, seafarerData);
      } catch (error) {
        setError(error.message);
        console.log('## error', error);
      } finally {
        setLoading(false);
      }
    })();
  }

  const handleBankAccountHolderChange = () => {
    const changedSeafarer = seafarer;
    const person = changedSeafarer.seafarer_person || {};
    const bankAccounts = getBankAccounts(person);
    person.bank_accounts = bankAccounts;
    setSeafarer({ ...changedSeafarer });
  };

  useEffect(() => {
    handleBankAccountHolderChange();
  }, []);

  useEffect(() => {
    if (seafarerId) setTriedValidate(true);
  }, [seafarerId]);

  const onSubmit = (values) => {
    eventTracker('submit', 'Submit Seafarer');
    setShowBankAccountNotCompleted(false);
    setTriedValidate(true);

    // Set additioanl fields
    if (!seafarerId) values.seafarer_person.screening_status = screeningStatus.UNDER_SCREENING;
    onSubmitSeafarer();
  };

  const routeToDetailsPage = (data) => {
    eventTracker(
      'routeToDetailsPage',
      `To Details Page - ${toString(data?.seafarer_person?.first_name)} ${toString(
        data?.seafarer_person?.last_name,
      )}`,
    );
    history.push(`/seafarer/details/${data.id}/general`);
  };

  const routeToListPage = () => {
    eventTracker('routeToListPage', 'To List Page');
    history.push('/seafarer/passed');
  };

  const routeToUserDefinedDocumentsList = () => {
    if (seafarerId) {
      history.push(`/seafarer/details/${seafarerId}/other-documents#user-defined-document`);
    } else {
      throw Error('Fail to route to user defined document list due to missing seafarerPersonId');
    }
  };

  const handleDataLostForCreateAlertClose = () => setShowDataLossForCreateAlert(false);

  function isFunction(functionToCheck) {
    return functionToCheck && {}.toString.call(functionToCheck) === '[object Function]';
  }

  const difference = (changed, initial, parentKey) => {
    if (!initial) {
      return _.cloneDeep(changed);
    }
    if (_.isArray(changed)) {
      return changed
        .map((ele) => {
          const initialArr = initial.find((obj) => {
            return obj.id === ele.id;
          });
          return difference(ele, initialArr, parentKey);
        })
        .filter((diffValue) => !_.isEmpty(diffValue));
    }
    const diff = {};
    for (const [key, val] of Object.entries(changed)) {
      if (_.isObject(val)) {
        parentKey = key;
        const objDiff = difference(val, initial[key], parentKey);
        if (Object.keys(objDiff).length > 0) {
          parentKey = key;
          diff[key] = difference(val, initial[key], parentKey);
        }
      } else if (key !== 'localId' && val !== initial[key]) {
        diff[key] = val;
      }
    }
    return diff;
  };

  const PAGE_CLOSE_TRIGGER_FROM_EXPERIENCE_FORM = 'PAGE_CLOSE_TRIGGER_FROM_EXPERIENCE_FORM';
  const handleClose = (pageCloseTriggerFromSource) => {
    setCurrentPageCloseTriggerFromSource(pageCloseTriggerFromSource);

    const isAnyInputEntered = () => {
      localStorage.removeItem('seafarer-form');
      return (
        Object.values(seafarer).filter((value) => {
          if (value && isFunction(value.trim)) {
            return value.trim() != '';
          }
          return false;
        }).length > 0
      );
    };
    const diff = difference(controller.seafarerChanges, controller.initialValues);
    if (Object.keys(diff).length > 0) {
      if (seafarer.id) {
        return setShowDataLossForEditAlert(true);
      }
      return isAnyInputEntered()
        ? setShowDataLossForCreateAlert(true)
        : setShowDataLossForEditAlert(true);
    }
    if (pageCloseTriggerFromSource === PAGE_CLOSE_TRIGGER_FROM_EXPERIENCE_FORM) {
      routeToUserDefinedDocumentsList();
    } else {
      return seafarer.id ? routeToDetailsPage(seafarer) : routeToListPage();
    }
  };

  const doesUserHaveAccess = () => {
    return seafarerId ? roleConfig.seafarer.editSeafarer : roleConfig.seafarer.addSeafarer;
  };

  const personalDetails = cachedDuplicates.personalDetails || {};
  const personalDetailsDuplicates = personalDetails.duplicates || [];

  const person = seafarer.seafarer_person || {};
  const passports = person.passports || [];
  const seamansBooks = person.seaman_books || [];

  // Backend Duplicates
  const existingPassportDuplicates = getDuplicateNumbers(duplicatePassports, passports);
  const existingSeamanBooksDuplicates = getDuplicateNumbers(duplicateSeamansBook, seamansBooks);

  const cloneSeafarerSchema = cloneDeep(seafarerSchema);
  delete cloneSeafarerSchema.fields.seafarer_person.fields.seaman_books.innerType.fields.doc_path;
  delete cloneSeafarerSchema.fields.seafarer_person.fields.passports.innerType.fields.doc_path;
  delete cloneSeafarerSchema.fields.seafarer_person.fields.seafarer_contacts;

  const breadCrumbsItems = useMemo(
    () =>
      seafarerId
        ? [
            { title: 'Seafarer', label: 'To List Page', link: '/seafarer/passed' },
            {
              title: person ? `${person?.first_name} ${person?.last_name}` : '- - -',
              label: 'Details',
              link: `/seafarer/details/${seafarer.id}/general`,
            },
            {
              title: 'Edit a Seafarer',
              label: 'Edit a Seafarer',
              link: '#',
            },
          ]
        : [
            { title: 'Seafarer', label: 'To List Page', link: '/seafarer/passed' },
            {
              title: 'Add a Seafarer',
              label: 'Add a Seafarer',
              link: '#',
            },
          ],
    [person],
  );

  const onBreadcrumbClick = (label) => {
    eventTracker('routeTo', toString(label));
  };
  const schema = disableFieldValidationBasedOnRole();
  schema.fields.seafarer_person.fields.seaman_books = getSeamanBooksSchema(
    isSeamanBooksUpdated,
    false,
  );

  const errorComponent = () => {
    if (!error) {
      if (error === '404 page error') {
        return <ErrorPage errorCode={404} />;
      }
      return error;
    }
    return null;
  };

  return (
    <AccessHandlerWrapper
      hasRoleAccess={
        checkUserBelongToSameShipParty(dropDownData.offices, seafarer, roleConfig) &&
        doesUserHaveAccess()
      }
    >
      {loading ? (
        <div className="spinner-container">
          <Spinner />
        </div>
      ) : (
        <Container>
          {errorComponent() ?? (
            <Formik
              validate={async (value) => {
                try {
                  await validateYupSchema(value, schema, true, {
                    dropDownData,
                    value,
                    selectedBank: selectedBank.current,
                  });
                } catch (err) {
                  return yupToFormErrors(err); // for rendering validation errors
                }
                return {};
              }}
              enableReinitialize
              initialValues={seafarer}
              validateOnChange={triedValidate}
              validateOnBlur={triedValidate}
              validateOnMount={!!seafarerId}
            >
              {({
                handleSubmit,
                handleChange,
                handleBlur,
                values,
                touched,
                isValid,
                errors,
                setFieldValue,
                validateForm,
              }) => {
                const [subSection, setSubSection] = useState(null);

                const handleScroll = () => {
                  const ErrorScrollMapping = {
                    [RANK_OFFICE_SECTION]: basicRef,
                    [PERSONAL_DETAILS_SECTION]: personalDetailsRef,
                    [PASSPORT_SECTION]: passportRef,
                    [SEAMAN_BOOK_SECTION]: seamanBookRef,
                    [CONTACT_SECTION]: contactRef,
                    [FAMILY_MEMBER_SECTION]: familyMemberRef,
                  };
                  scrollToSection(ErrorScrollMapping[subSection]);
                  setSubSection(null);
                };

                useLayoutEffect(() => {
                  if (!subSection) return;
                  handleScroll();
                }, [subSection]);

                const isBasicFormValid = async () => {
                  const errors = await validateForm();

                  const basicFormErrors = getBasicFormErrors(errors);
                  const isValid = Object.keys(basicFormErrors).length === 0;

                  if (!isValid) {
                    setTriedValidate(true);
                    setModalMessage(PROCEED_OTHER_TAB_FAILED_MESSAGE);
                  }

                  return isValid;
                };

                const onValidateForm = async () => {
                  const errors = await validateForm();

                  const isFormCompleted = Object.keys(errors).length === 0;
                  const isBankAccountFormCompleted =
                    !errors?.seafarer_person?.bank_accounts ||
                    Object.keys(errors?.seafarer_person?.bank_accounts).length === 0;

                  const validatingErrors = { ...errors };
                  setTriedValidate(true);

                  let isFormValid = true;
                  if (Object.keys(validatingErrors).length > 0) {
                    isFormValid = false;
                    setModalMessage(PROCEED_OTHER_TAB_FAILED_MESSAGE);
                  }

                  return {
                    isFormValid,
                    isFormCompleted,
                    isBankAccountFormCompleted,
                  };
                };

                // custom submit logic to not block saving when there's only bank account errors
                const onSubmitValues = () => {
                  onValidateForm().then(({ isFormValid }) => {
                    if (isFormValid) {
                      if (hasDuplicates()) return;
                      onSubmit(values);
                    }
                  });
                };

                const onValidateLink = (link, subSection) => {
                  eventTracker('anchorLink', `${link} - ${subSection}`);
                  if (controller.step === link) {
                    if (subSection) setSubSection(subSection);
                    return false;
                  }
                  if (controller.step !== 'basic') {
                    navigateToLink(link);
                    if (subSection) setSubSection(subSection);
                    return true;
                  }
                  isBasicFormValid()
                    .then((isValid) => {
                      if (isValid) {
                        if (hasDuplicates()) return;
                        controller.step = link;
                        navigateToLink(link);
                      }
                    })
                    .then(() => {
                      if (subSection) setSubSection(subSection);
                    });
                  return false;
                };

                const hasDuplicates = () => {
                  if (personalDetailsDuplicates.length > 0) {
                    const modalContent = getDuplicateErrorModal('Name and Date of Birth');
                    setModalMessage(modalContent);
                    return personalDetailsDuplicates.length > 0;
                  }

                  if (existingPassportDuplicates.length > 0) {
                    const modalContent = getDuplicateErrorModal('Passport Number');
                    setModalMessage(modalContent);
                    return existingPassportDuplicates.length > 0;
                  }

                  if (existingSeamanBooksDuplicates.length > 0) {
                    const modalContent = getDuplicateErrorModal("Seaman's Book Number");
                    setModalMessage(modalContent);
                    return existingSeamanBooksDuplicates.length > 0;
                  }
                  return false;
                };

                const navigateToLink = (link) => {
                  controller.step = link;
                  eventTracker('tabNavigation', link);
                  if (seafarerId) {
                    return history.push(`/seafarer/${seafarerId}/add/${link}`);
                  }
                  return history.push(`/seafarer/add/${link}`);
                };

                const onSeafarerChange = (changedSeafarer, field, value) => {
                  setFieldValue('seafarer_person', changedSeafarer.seafarer_person);
                  if (field) {
                    if (field === 'seaman_books') {
                      setIsSeamanBooksUpdated(true);
                    } else {
                      if (basicToAccountHolderMap[field]) {
                        handleBankAccountHolderChange();
                      }
                      if (value !== null && value !== undefined) {
                        setFieldValue(field, value);
                      }
                    }
                  }
                  controller.seafarerChanges = changedSeafarer;
                  setSeafarer(changedSeafarer);
                  loadPortsOfIssueForSeamanBook(dropDownData.countries, changedSeafarer);
                };

                const seafarerValues = seafarerId ? seafarer : { ...seafarer, ...values };

                const formPageProps = {
                  seafarer: seafarerValues,
                  dropDownData,
                  selectedBank,
                  setDropDownData,
                  errors,
                  handleBlur,
                  onSeafarerChange,
                  roleConfig,
                  seafarerId: Number(seafarerId),
                  portsMap,
                };

                const hasExistingContactErrors = hasContactErrors(errors);
                const hasExistingBankErrors = hasBankErrors(errors);
                const hasExistingFamilyError = hasPersonalParticularError(errors);
                const hasExistingBasicError = hasBasicFormErrors(errors);

                const navBarErrorProps = {
                  hasExistingContactErrors,
                  hasExistingFamilyError,
                  hasExistingBasicError,
                  hasExistingBankErrors,
                };

                return (
                  <Container>
                    <Row className="closeIcon">
                      <BreadcrumbHeader
                        items={breadCrumbsItems}
                        activeItem={seafarerId ? 'Edit a Seafarer' : 'Add a Seafarer'}
                        onClick={onBreadcrumbClick}
                      />
                      <Icon icon="close" size={25} onClick={handleClose} />
                    </Row>

                    {seafarer?.name && (
                      <Row>
                        <h6>{seafarer.name}</h6>
                      </Row>
                    )}

                    <NavBar
                      validate={onValidateLink}
                      navBarErrorProps={navBarErrorProps}
                      canViewContacts={!seafarerId || seafarer.can_view_contacts}
                    />

                    <ErrorsListComponent errors={errors} onValidateLink={onValidateLink} />
                    {(hasExistingContactErrors || hasExistingFamilyError) && (
                      <InvalidDataComponent
                        displayContactError={hasExistingContactErrors}
                        displayFamilyError={hasExistingFamilyError}
                        seafarer={seafarer}
                      />
                    )}

                    {personalDetailsDuplicates.length > 0 && (
                      <DuplicateSeafarersComponent
                        currentSearchType={searchType.PERSONAL_DETAILS}
                        seafarers={personalDetailsDuplicates}
                        title="Seafarer"
                      />
                    )}

                    {existingPassportDuplicates.length > 0 && (
                      <DuplicateSeafarersComponent
                        currentSearchType={searchType.PASSPORT}
                        seafarers={existingPassportDuplicates}
                        title="Passports"
                      />
                    )}

                    {existingSeamanBooksDuplicates.length > 0 && (
                      <DuplicateSeafarersComponent
                        currentSearchType={searchType.SEAMANS_BOOK}
                        seafarers={existingSeamanBooksDuplicates}
                        title="Seaman's Books"
                      />
                    )}

                    <Form noValidate onSubmit={handleSubmit}>
                      <Switch>
                        <Route
                          exact
                          path={['/seafarer/:seafarerId?/add/basic', '/seafarer/:seafarerId?/add']}
                        >
                          <AddSeafarerBasicForm
                            {...formPageProps}
                            ref={{ basicRef, personalDetailsRef, passportRef, seamanBookRef }}
                            cacheDuplicates={cacheDuplicates}
                            cachedDuplicates={cachedDuplicates}
                            keycloak={keycloak}
                            originalSeafarer={originalSeafarer}
                          />
                        </Route>

                        <Route exact path="/seafarer/:seafarerId?/add/contact-details">
                          <AddSeafarerContactDetails {...formPageProps} ref={contactRef} />
                        </Route>

                        <Route exact path="/seafarer/:seafarerId?/add/personal-particulars">
                          <AddSeafarerPersonalParticularsForm
                            {...formPageProps}
                            ref={familyMemberRef}
                          />
                        </Route>

                        <Route exact path="/seafarer/:seafarerId?/add/experience">
                          <AddSeafarerExperienceForm
                            {...formPageProps}
                            setModalMessage={setModalMessage}
                            handleClose={handleClose}
                            setFieldValue={setFieldValue}
                          />
                        </Route>
                      </Switch>
                      {!seafarerId && (
                        <Persist name="seafarer-form" ignoreFields={FIELDS_TO_IGNORE} />
                      )}
                      <BottomButton
                        title="Save"
                        testID="form-seafarer-save-button"
                        onClick={onSubmitValues}
                      />
                    </Form>
                    <ConfirmActionModalView
                      show={showBankAccountNotCompleted}
                      onClose={() => setShowBankAccountNotCompleted(false)}
                      onConfirm={() => onSubmit(values)}
                      confirmBtnText="Save"
                      title={
                        'Screening cannot start because required information on Bank Accounts page ' +
                        'is missing. You can still save the page.'
                      }
                      message="Save now and complete Bank Accounts page later"
                    />
                    <ConfirmActionModalView
                      show={showDataLossForEditAlert}
                      onClose={() => setShowDataLossForEditAlert(false)}
                      onConfirm={() => {
                        setShowDataLossForEditAlert(false);
                        if (
                          currentPageCloseTriggerFromSource ===
                          PAGE_CLOSE_TRIGGER_FROM_EXPERIENCE_FORM
                        ) {
                          routeToUserDefinedDocumentsList();
                        } else {
                          seafarer.id ? routeToDetailsPage(seafarer) : routeToListPage();
                        }
                      }}
                      title="Are you sure?"
                      message="Your changes will be lost. Are you sure you want to cancel?"
                    />
                  </Container>
                );
              }}
            </Formik>
          )}
          <Modal show={modalMessage !== null} onHide={onHideModalMessage} centered>
            <Modal.Header className="modal-header">{modalMessage?.header}</Modal.Header>
            <Modal.Body>
              {modalMessage?.message}
              {modalErrorData && <div>
Error Message :{modalErrorData}</div>}
            </Modal.Body>
            <Modal.Footer>
              <Button variant="secondary" onClick={onHideModalMessage}>
                Close
              </Button>
            </Modal.Footer>
          </Modal>
          <Modal show={showDataLossForCreateAlert} centered>
            <Modal.Header>
              <Modal.Title className="h5">You Have Unsaved Input Data</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              Are you sure to quit this page? You will lose your unsaved input data.
            </Modal.Body>
            <Modal.Footer>
              <Button variant="primary" onClick={handleDataLostForCreateAlertClose}>
                Cancel
              </Button>
              <Button
                variant="secondary"
                onClick={() => {
                  routeToListPage();
                }}
              >
                Confirm
              </Button>
            </Modal.Footer>
          </Modal>
        </Container>
      )}
    </AccessHandlerWrapper>
  );
});

export default AddSeafarerPage;
