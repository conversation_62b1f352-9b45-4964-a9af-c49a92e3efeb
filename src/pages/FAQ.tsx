/* eslint-disable react/prop-types */
/* eslint-disable no-restricted-globals */
import React from 'react';
import { Button, Container } from 'react-bootstrap';
import './scss/faq.scss';
import { Divider } from '../component/common/Divider';
import styleGuide from '../styleGuide';
import { useHistory, useParams } from 'react-router-dom';
import { useSharepointCMS } from '../hooks/useSharepointCMS';
const { Icon } = styleGuide;

const TITLE_MAP = {
  general: 'Seafarer - Frequently Asked Questions',
  'seafarer-status-instructions': 'Seafarer - User Guide',
};

const FAQ = () => {
  const { cmsSharepointSiteID } = useParams();

  const history = useHistory();
  const faqContent = useSharepointCMS(cmsSharepointSiteID);

  const handleGoBack = () => {
    history.goBack();
  };

  return (
    <Container fluid style={{ paddingTop: '20px' }} className="faq-wrapper">
      <div className="faq-heading">
        <span className="faq-title">{TITLE_MAP[cmsSharepointSiteID] ?? TITLE_MAP.general}</span>
        <Button variant="link" aria-hidden="true" onClick={handleGoBack}>
          <Icon style={{ cursor: 'pointer' }} icon="close" size={20} />
        </Button>
      </div>
      <Divider height="0.25" />
      <hr />
      {faqContent}
    </Container>
  );
};

export { FAQ };
