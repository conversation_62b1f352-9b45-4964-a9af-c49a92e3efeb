.carousel-control-prev, .carousel-control-next {
  opacity: 0 !important;
  filter: alpha(opacity=0) !important; /* IE support */
  cursor: default !important;
}

.carousel-wrapper {
  background: #485059 0% 0% no-repeat padding-box !important;
  opacity: 1 !important;
}

.change-status-btn {
  &:disabled {
    color: #aaaaaa;
    background: #efefef 0% 0% no-repeat padding-box;
    border-radius: 4px;
    border-color: #cccccc;
  }
}

.modal:first-child {
  z-index: 1 !important;
}
.add-document-btn {
  &:disabled {
    color: #aaaaaa;
    background: #efefef 0% 0% no-repeat padding-box;
    border-radius: 4px;
    border-color: #cccccc;
  }
}

.paris2-icon.float-left {
  float: left;
}

.screening-status {
  margin: 0px 0px 16px 5px;
  padding: 2px 7px;
  span {
    font-weight: 600;
  }
}

.reject-status {
  background: #fdf3f6;
  span {
    color: #d82f65
  }
}

.under-screening-status {
  background: #e5f4f8;
  span {
    color: #0091B8;
  }
}

.screening-reject-remark {
  color: #9e9ca3 !important;
}