$blue-color: #1f4a70;

.seafarer-bio {
  padding: 0 60px;
  .page-heading {
    color: $blue-color;
    border-bottom: 1px solid $blue-color;
    margin-bottom: 0px;
    justify-content: space-between;
  }
  .logo-container {
    position: relative;
    right: 0;
    .logo {
      position: absolute;
      right: 0;
      top: -1rem;
    }
  }
  .padding-right-zero {
    padding-right: 0px;
  }
  .bio-data {
    .section {
      margin-top: 0.5rem;
      border-bottom: 1px solid $blue-color;
      .row {
        margin-bottom: 1rem;
      }
      .row:first-child {
        .col:first-child {
          font-weight: bold;
        }
      }
      .col:nth-child(odd) {
        color: $blue-color;
      }
      .col:nth-child(even) {
        font-weight: bold;
      }
    }
  }
  hr {
    margin-top: 2.5rem;
    border-top: 3px solid $blue-color;
  }
  .seafarer-exp-heading {
    font-weight: bold;
    color: $blue-color;
  }
  .grid {
    .th,
    .td {
      padding-left: 0px;
      word-break: break-word;
    }

    .th {
      border-bottom: 1px solid;
      padding-bottom: 0.25rem;
    }
  }
  .copyright {
    margin-top: 4rem;
    margin-bottom: 3rem;
    color: $blue-color;
    .icon-container {
      display: flex;
      align-items: center;
      span {
        margin-left: 0.25rem;
      }
    }
  }

  .image-container {
    text-align: right;
  }

  .seafarer-print-bio-photo {
    object-fit: contain;
    height: 260px;
    margin-top: 15px;
  }
  
  @media (max-width: 600px) {
    .seafarer-print-bio-photo {
      object-fit: scale-down;
      height: 120px;
    }
  }
  
  @media (min-width: 600px) and (max-width: 1200px) {
    .seafarer-print-bio-photo {
      object-fit: cover;
      height: 200px;
    }
  }
  
  @media (min-width: 1200px) {
    .seafarer-print-bio-photo {
      object-fit: contain;
      height: 260px;
    }
  }

  .child {
    margin-right: 0.25rem;
  }

  .underline {
    text-decoration: underline;
  }

  .d-flex {
    justify-content: center;
    padding-top: 3rem;
  }

  .dynamic-text-size {
    font-size: calc(1vw);
  }
}
