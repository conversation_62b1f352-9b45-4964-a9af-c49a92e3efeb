/* eslint-disable react/display-name */
/* eslint-disable react/prop-types */
import React, { useState, useRef, useEffect } from 'react';
import { Col, Row, Button } from 'react-bootstrap';
import _ from 'lodash';
import { useParams, useHistory, Switch, Route } from 'react-router-dom';
import DocumentTable from '../component/OtherDocument/DocumentTable';
import AddDocumentModal from '../component/document/AddDocumentModal';
import * as documentTypes from '../constants/documentTypes';
const { Icon } = styleGuide;
import styleGuide from '../styleGuide';
import {
  Medical,
  CertificatesOfCompentency,
  STCW,
  Education,
  Apprenticeship,
  Training,
  OtherCourses,
  OtherDocuments,
  CorrespondenceDetails,
  UserDefinedDocs,
} from '../model/OtherDocumentTableColumns';
import seafarerService from '../service/seafarer-service';
import ConfirmActionModalView from '../component/AddSeafarer/ConfirmActionModalView';
import { ALL_DOC_TYPES } from '../constants/documentTypes';
import AddSeafarerController from '../controller/add-seafarer-controller';
import DocumentDownloadFailedModal from '../component/document/DocumentDownloadFailedModal';
import ErrorDisplayModal from '../component/common/ErrorDisplayModal';

import { scrollToSection } from '../util/view-utils';
import { useAccess } from '@src/component/common/Access';

const { DOC_DOWNLOAD_FAIL_MESSAGE } = process.env;

const generateDocuments = (downloadFile) => {
  return [
    {
      Header: 'Documents',
      id: 'documents',
      accessor: function documentAccessor(row) {
        return (
          <Button
            variant="link"
            className="selected-file-link"
            onClick={() => {
              downloadFile(
                row?.type,
                { id: row?.[`seafarer_doc_${row.type}`]?.seafarer_document_id },
                row.doc_path,
              );
            }}
          >
            {row?.doc_path ? 'View' : ''}
          </Button>
        );
      },
      disableSortBy: true,
      maxWidth: 100,
    },
  ];
};

const generateCertificates = (downloadFile) => {
  return [
    {
      Header: 'Certificate',
      id: 'certificate',
      accessor: function certificateAccessor(row) {
        return (
          <Button
            variant="link"
            className="selected-file-link"
            onClick={() => {
              downloadFile(
                row?.type,
                { id: row?.[`seafarer_doc_${row.type}`]?.seafarer_document_id },
                row.doc_path,
              );
            }}
          >
            {row?.doc_path ? 'View' : ''}
          </Button>
        );
      },
      disableSortBy: true,
      maxWidth: 100,
    },
  ];
};

const generateAction = (showDeleteAndEdit, editHandler, deleteHandler) => {
  return [
    {
      Header: function () {
        return <div className="text-center">Actions</div>;
      },
      id: 'action',
      accessor: function actionAccessor(row) {
        return (
          <div className="action-column">
            {showDeleteAndEdit && (
              <div style={{ display: 'flex' }}>
                <Button
                  variant="link"
                  onClick={() => {
                    editHandler(row.type, row.id);
                  }}
                  data-testid="edit-doc-btn"
                >
                  <Icon icon="pencil" size={20} className="mr-3" />
                </Button>
                <Button
                  variant="link"
                  onClick={() => {
                    deleteHandler(row);
                  }}
                  data-testid="delete-doc-btn"
                >
                  <Icon icon="bin" size={20} />
                </Button>
              </div>
            )}
          </div>
        );
      },
      disableSortBy: true,
      maxWidth: 50,
      sticky: 'right',
    },
  ];
};

const generateMedicalColumn = ({displayData, dropdownData, visitVesselDetailPage, documents, action }) => {
  return [
    {
      Header: 'Medical Certificates/Tester',
      id: 'medical-certificate',
      accessor: (row) => {
        return row?.seafarer_doc_medical?.medical_certificate_id
          ? displayData(
              row?.seafarer_doc_medical?.medical_certificate_id,
              dropdownData.medicalCertificates,
            )
          : row?.seafarer_doc_drug_alcohol_test?.tester;
      },
      disableSortBy: true,
      maxWidth: 110,
    },
    ...Medical,
    {
      Header: 'Vessel',
      id: 'vessel',
      accessor: function vesselAccessor(row) {
        return (
          <div>
            {row?.seafarer_doc_drug_alcohol_test?.vessel_name ? (
              <Button
                variant="link"
                onClick={() =>
                  visitVesselDetailPage(row?.seafarer_doc_drug_alcohol_test?.vessel_ref_id)
                }
              >
                <b>{row.seafarer_doc_drug_alcohol_test.vessel_name}</b>
              </Button>
            ) : (
              ''
            )}
          </div>
        );
      },
      disableSortBy: true,
      maxWidth: 100,
    },
    ...documents,
    ...action,
  ];
};

const generateCertificatesOfCompentencyColumn = ({ displayData, dropdownData, documents, action  }) => {
  return [
    {
      Header: 'Flag',
      id: 'flag',
      disableSortBy: true,
      maxWidth: 100,
      accessor: function flagAccessor(row) {
        const country = displayData(
          row?.seafarer_doc_certificate_of_competency?.country_id,
          dropdownData.countries,
        );
        return (
          <div
            className={
              row?.seafarer_doc_certificate_of_competency?.is_original ? 'font-weight-bold' : ''
            }
          >
            {' '}
            {row?.seafarer_doc_certificate_of_competency?.is_original ? `${country}*` : country}
          </div>
        );
      },
      order: 1,
    },
    ...CertificatesOfCompentency,
    {
      Header: 'Certificate',
      id: 'certificate',
      disableSortBy: true,
      accessor: function certificateAccessor(row) {
        const certificate = displayData(
          row?.seafarer_doc_certificate_of_competency?.coc_certificate_id,
          dropdownData.cocCertificates,
        );
        return (
          <div
            className={
              row?.seafarer_doc_certificate_of_competency?.is_original ? 'font-weight-bold' : ''
            }
          >
            {' '}
            {certificate}{' '}
          </div>
        );
      },
      maxWidth: 100,
      order: 2,
    },
    ...documents,
    ...action,
  ];
};

const generateSTCWColumn = ({ displayData, dropdownData, documents, action }) => {
  return [
    {
      Header: 'Course',
      id: 'course',
      disableSortBy: true,
      accessor: (row) => {
        return displayData(row?.seafarer_doc_stcw?.stcw_licence_id, dropdownData.stcwLicences);
      },
      maxWidth: 100,
    },
    ...STCW,
    ...documents,
    ...action,
  ];
};

const generateTrainingColumn = ({ certificates, action }) => {
  return [
    {
      Header: 'Course',
      id: 'course',
      accessor: function courseAccessor(row) {
        return <div>{row?.seafarer_doc_training?.course}</div>;
      },
      disableSortBy: true,
      maxWidth: 100,
    },
    {
      Header: 'Institute',
      id: 'institute',
      accessor: function instituteAccessor(row) {
        return <div>{row?.seafarer_doc_training?.institute}</div>;
      },
      disableSortBy: true,
      maxWidth: 100,
    },
    ...Training,
    ...certificates,
    ...action,
  ];
};

const generatePreSeaTrainingColumn = ({ displayData,  dropdownData, certificates, action }) => {
  return [
    {
      Header: 'Course',
      id: 'course',
      accessor: function courseAccessor(row) {
        return (
          <div>
            {displayData(
              row?.seafarer_doc_pre_sea_training?.pre_sea_training_course_id,
              dropdownData.preSeaTrainingCourses,
            )}
          </div>
        );
      },
      disableSortBy: true,
      maxWidth: 120,
    },
    {
      Header: 'Institute',
      id: 'institute',
      accessor: function instituteAccessor(row) {
        return (
          <div>
            {displayData(row?.seafarer_doc_pre_sea_training?.institute_id, dropdownData.institutes)}
          </div>
        );
      },
      disableSortBy: true,
      maxWidth: 120,
    },
    ...Training,
    ...certificates,
    ...action,
  ];
};

const OtherDocumentsPage = ({
  seafarerId,
  seafarer,
  dropdownData,
  eventTracker,
  refreshDetailsPageData,
}) => {
  seafarerId = seafarerId === undefined ? seafarer?.id : seafarerId;
  const seafarerPersonId = seafarer?.seafarer_person_id;
  const { roleConfig } = useAccess();
  const history = useHistory();
  let { documentId } = useParams();
  const controller = new AddSeafarerController();
  const [showDownloadFailModal, setShowDownloadFailModal] = useState(false);
  const [loadingOtherDocument, setLoadingOtherDocument] = useState(false);
  const [medicalData, setMedicalData] = useState([]);
  const [certificateOfCompetencyData, setCertificateOfCompetencyData] = useState([]);
  const [stcwData, setStcwData] = useState([]);
  const [educationData, setEducationData] = useState([]);
  const [apprenticeshipData, setApprenticeshipData] = useState([]);
  const [preSeaTrainingData, setPreSeaTrainingData] = useState([]);
  const [trainingData, setTrainingData] = useState([]);
  const [otherDocumentData, setOtherDocumentData] = useState([]);
  const [otherCourseData, setOtherCourseData] = useState({});
  const [correspondenceDetailsData, setCorrespondenceDetailsData] = useState([]);
  const [userDefinedData, setUserDefinedData] = useState({});
  const [selectedDocument, setSelectedDocument] = useState(null);
  const [hash, setHash] = useState(undefined);
  const [isRemoveDocumentConfirmationModalShow, setIsRemoveDocumentConfirmationModalShow] =
    useState(false);
  const [isDisableConfirmDeleteBtn, setIsDisableConfirmDeleteBtn] = useState(false);
  const [modalMessage, setModalMessage] = useState(null);
  const [isModalLoading, setIsModalLoading] = useState(false);
  const hasDropDownData = Object.values(dropdownData)?.length > 0;
  const showDeleteAndEdit = roleConfig.seafarer.edit.seafarerDocument;

  const medicalSectionRef = useRef(null);
  const certificateOfCompetencySectionRef = useRef(null);
  const stcwSectionRef = useRef(null);
  const educationSectionRef = useRef(null);
  const apprenticeshipSectionRef = useRef(null);
  const trainingSectionRef = useRef(null);
  const preseaTraingSectionRef = useRef(null);
  const otherCoursesSectionRef = useRef(null);
  const otherDocumentsSectionRef = useRef(null);
  const correspondenceDetailsSectionRef = useRef(null);
  const userDefinedDocumentsSectionRef = useRef(null);

  useEffect(() => {
    if (history.location.hash !== undefined && history.location.hash !== '') {
      setHash(history.location.hash);
    } else {
      setHash(undefined);
    }
  });

  const onHideModalMessage = () => setModalMessage(null);

  const _setMedicalData = (responseObj, medicalCertificatesData) => {
    responseObj?.medical?.forEach((medicalItem) => {
      let dropDownItem =
        medicalItem?.seafarer_doc_medical?.medical_certificate_id &&
        medicalCertificatesData?.length &&
        medicalCertificatesData.find((item) => {
          return item?.id == medicalItem?.seafarer_doc_medical?.medical_certificate_id;
        });
      medicalItem.medical_certificate_name = dropDownItem?.value.toLowerCase();
    });

    let medicalTableData = [];
    if (responseObj?.medical || responseObj?.drug_alcohol_test) {
      if (responseObj?.medical && responseObj?.drug_alcohol_test) {
        medicalTableData = [...responseObj.medical, ...responseObj.drug_alcohol_test];
      } else {
        medicalTableData = responseObj?.medical ?? responseObj?.drug_alcohol_test;
      }
    }

    const sortedMedicalData = _.orderBy(
      medicalTableData,
      [
        'medical_certificate_name',
        (user) =>
          new Date(
            user?.seafarer_doc_medical?.date_of_issue ||
              user?.seafarer_doc_drug_alcohol_test?.date_of_test,
          ),
      ],
      ['asc', 'desc'],
    );
    setMedicalData(sortedMedicalData);
  };

  const _setCertificateOfCompetencyData = (responseObj, cocCertificatesData) => {
    responseObj?.certificate_of_competency?.forEach((cocItem) => {
      let dropDownItem =
        cocItem?.seafarer_doc_certificate_of_competency?.coc_certificate_id &&
        cocCertificatesData?.length &&
        cocCertificatesData.find((item) => {
          return item?.id == cocItem?.seafarer_doc_certificate_of_competency?.coc_certificate_id;
        });
      cocItem.coc_certificate_name = dropDownItem?.value;
    });
    const certificateOfCompetencyTable = responseObj?.certificate_of_competency
      ? responseObj.certificate_of_competency
      : [];
    const sortedCertificateOfCompetency = _.orderBy(
      certificateOfCompetencyTable,
      [
        (item) => item?.coc_certificate_name?.toLowerCase(),
        (item) => new Date(item?.certificate_of_competency?.date_of_issue),
      ],
      ['asc', 'desc'],
    );
    const isNational = sortedCertificateOfCompetency.some(
      (item) => item?.seafarer_doc_certificate_of_competency?.is_original === true,
    );
    isNational &&
      sortedCertificateOfCompetency.forEach(function (item, i) {
        if (item?.seafarer_doc_certificate_of_competency?.is_original) {
          sortedCertificateOfCompetency.splice(i, 1);
          sortedCertificateOfCompetency.unshift(item);
        }
      });

    setCertificateOfCompetencyData(sortedCertificateOfCompetency);
  };

  const _setStcwData = (responseObj, stcwDropdownData) => {
    responseObj?.stcw?.forEach((stcwItem) => {
      let dropDownItem =
        stcwItem?.seafarer_doc_stcw?.stcw_licence_id &&
        stcwDropdownData?.length &&
        stcwDropdownData.find((item) => {
          return item?.id == stcwItem?.seafarer_doc_stcw?.stcw_licence_id;
        });
      stcwItem.stcw_licence_name = dropDownItem?.value;
    });
    const stcwTable = responseObj?.stcw ? responseObj.stcw : [];
    const sortedStcw = _.orderBy(
      stcwTable,
      [
        (item) => item?.stcw_licence_name?.toLowerCase(),
        (item) => new Date(item?.seafarer_doc_stcw?.date_of_issue),
      ],
      ['asc', 'desc'],
    );
    setStcwData(sortedStcw);
  };

  const _setEducationData = (responseObj) => {
    setEducationData(responseObj?.education ? responseObj.education : []);
  };

  const _setApprenticeshipData = (responseObj) => {
    setApprenticeshipData(responseObj?.apprenticeship ? responseObj.apprenticeship : []);
  };

  const _setTrainingData = (responseObj) => {
    const traningTable = responseObj?.training ? responseObj.training : [];
    const sortTraning = _.orderBy(
      traningTable,
      [(item) => new Date(item?.seafarer_doc_training?.start_date)],
      ['desc'],
    );
    setTrainingData(sortTraning);
  };

  const _setPreSeaTrainingData = (responseObj) => {
    const preSeaTraningTable = responseObj?.pre_sea_training ? responseObj.pre_sea_training : [];
    const sortPreSeaTraning = _.orderBy(
      preSeaTraningTable,
      [(item) => new Date(item?.seafarer_doc_pre_sea_training?.start_date)],
      ['desc'],
    );
    setPreSeaTrainingData(sortPreSeaTraning);
  };

  const _setOtherCourseData = (responseObj, otherCourseTypesData) => {
    let otherCourseObj = {};
    if (responseObj?.other_course) {
      for (const item of responseObj?.other_course ?? []) {
        if (otherCourseObj[item.seafarer_doc_other_course.other_course_type_id]) {
          otherCourseObj[item.seafarer_doc_other_course.other_course_type_id].push(item);
        } else {
          otherCourseObj[item.seafarer_doc_other_course.other_course_type_id] = [item];
        }
      }
      const courseObjKeys = Object.keys(otherCourseObj).map((key) => {
        let dropDownItem =
          otherCourseTypesData?.length &&
          otherCourseTypesData.find((item) => {
            return item?.id == key;
          });
        return dropDownItem?.value;
      });

      for (const [index, [key, value]] of Object.entries(Object.entries(otherCourseObj))) {
        const sortValues = _.orderBy(
          value,
          [
            (item) => item?.seafarer_doc_other_course?.course_title,
            (item) => new Date(item?.seafarer_doc_other_course?.date_of_issue),
          ],
          ['asc', 'desc'],
        );
        otherCourseObj[courseObjKeys[index]] = sortValues;
        delete otherCourseObj[key];
      }
    }
    const sortOtherObj = Object.fromEntries(
      Object.entries(otherCourseObj).sort((a, b) => (a[0] > b[0] ? 1 : -1)),
    );
    setOtherCourseData(sortOtherObj);
  };

  const _setCorrespondenceDetailsData = (responseObj) => {
    setCorrespondenceDetailsData(
      responseObj?.correspondence_details ? responseObj.correspondence_details : [],
    );
  };

  const _setOtherDocumentData = (responseObj, otherDocumentsTypes) => {
    responseObj?.other_document?.forEach((otherDocumentItem) => {
      let dropDownItem =
        otherDocumentItem?.seafarer_doc_other_document?.other_document_type_id &&
        otherDocumentsTypes?.length &&
        otherDocumentsTypes.find((item) => {
          return item?.id == otherDocumentItem?.seafarer_doc_other_document?.other_document_type_id;
        });
      otherDocumentItem.other_document_type_name = dropDownItem?.value;
    });
    const otherDocumentTable = responseObj?.other_document ? responseObj.other_document : [];
    const sortedOtherDocument = _.orderBy(
      otherDocumentTable,
      [
        (item) => item?.other_document_type_name?.toLowerCase(),
        (item) => new Date(item?.seafarer_doc_other_document?.date_of_issue),
      ],
      ['asc', 'desc'],
    );

    setOtherDocumentData(sortedOtherDocument);
  };

  const _setUserDefinedData = (responseObj, userDefinedDocumentTypes) => {
    let userDefinedObj = {};
    if (responseObj.user_defined_document) {
      for (const item of responseObj?.user_defined_document ?? []) {
        if (userDefinedObj[item.seafarer_doc_user_defined_document.user_defined_document_type_id]) {
          userDefinedObj[
            item.seafarer_doc_user_defined_document.user_defined_document_type_id
          ].push(item);
        } else {
          userDefinedObj[item.seafarer_doc_user_defined_document.user_defined_document_type_id] = [
            item,
          ];
        }
      }

      const userDefinedObjKeys = Object.keys(userDefinedObj).map((key) => {
        let dropDownItem =
          userDefinedDocumentTypes?.length &&
          userDefinedDocumentTypes.find((item) => {
            return item?.id == key;
          });
        return dropDownItem?.value;
      });

      for (const [index, [key, value]] of Object.entries(Object.entries(userDefinedObj))) {
        const sortValues = _.orderBy(
          value,
          [(item) => new Date(item?.seafarer_doc_user_defined_document?.date_of_issue)],
          ['desc'],
        );
        userDefinedObj[userDefinedObjKeys[index]] = sortValues;
        delete userDefinedObj[key];
      }
    }
    const sortUserDefinedData = Object.fromEntries(
      Object.entries(userDefinedObj).sort((a, b) => (a[0] > b[0] ? 1 : -1)),
    );
    setUserDefinedData(sortUserDefinedData);
  };

  const hashToReloadAndScrollHandler = {
    '#medical': async () => {
      const queryParam = '?type=medical&type=drug_alcohol_test';
      const responseObj = await loadDocsData(seafarerPersonId, queryParam);
      _setMedicalData(responseObj, dropdownData?.medicalCertificates);
      setTimeout(() => scrollToSection(medicalSectionRef, 0));
    },
    '#coc': async () => {
      const queryParam = '?type=certificate_of_competency';
      const responseObj = await loadDocsData(seafarerPersonId, queryParam);
      _setCertificateOfCompetencyData(responseObj, dropdownData?.cocCertificates);
      setTimeout(() => scrollToSection(certificateOfCompetencySectionRef, 0));
    },
    '#stcw': async () => {
      const queryParam = '?type=stcw';
      const responseObj = await loadDocsData(seafarerPersonId, queryParam);
      _setStcwData(responseObj, dropdownData.stcw);
      setTimeout(() => scrollToSection(stcwSectionRef, 0));
    },
    '#education': async () => {
      const queryParam = '?type=education';
      const responseObj = await loadDocsData(seafarerPersonId, queryParam);
      _setEducationData(responseObj);
      setTimeout(() => scrollToSection(educationSectionRef, 0));
    },
    '#apprenticeship': async () => {
      const queryParam = '?type=apprenticeship';
      const responseObj = await loadDocsData(seafarerPersonId, queryParam);
      _setApprenticeshipData(responseObj);
      setTimeout(() => scrollToSection(apprenticeshipSectionRef, 0));
    },
    '#training': async () => {
      const queryParam = '?type=training';
      const responseObj = await loadDocsData(seafarerPersonId, queryParam);
      _setTrainingData(responseObj);
      setTimeout(() => scrollToSection(trainingSectionRef, 0));
    },
    '#pre-sea-training': async () => {
      const queryParam = '?type=pre_sea_training';
      const responseObj = await loadDocsData(seafarerPersonId, queryParam);
      _setPreSeaTrainingData(responseObj);
      setTimeout(() => scrollToSection(preseaTraingSectionRef, 0));
    },
    '#other-courses': async () => {
      const queryParam = '?type=other_course';
      const responseObj = await loadDocsData(seafarerPersonId, queryParam);
      _setOtherCourseData(responseObj, dropdownData.otherCourseTypes);
      setTimeout(() => scrollToSection(otherCoursesSectionRef, 0));
    },
    '#other-documents': async () => {
      const queryParam = '?type=other_document';
      const responseObj = await loadDocsData(seafarerPersonId, queryParam);
      _setOtherDocumentData(responseObj, dropdownData.otherDocumentsTypes);
      setTimeout(() => scrollToSection(otherDocumentsSectionRef, 0));
    },
    '#correspondence-details': async () => {
      const queryParam = '?type=correspondence_details';
      const responseObj = await loadDocsData(seafarerPersonId, queryParam);
      _setCorrespondenceDetailsData(responseObj);
      setTimeout(() => scrollToSection(correspondenceDetailsSectionRef, 0));
    },
    '#user-defined-document': async () => {
      const queryParam = '?type=user_defined_document';
      const responseObj = await loadDocsData(seafarerPersonId, queryParam);
      _setUserDefinedData(responseObj, dropdownData.userDefinedDocumentTypes);
      setTimeout(() => scrollToSection(userDefinedDocumentsSectionRef, 0));
    },
  };

  useEffect(() => {
    (async () => {
      if (hash === undefined || !hasDropDownData) {
        return;
      }

      //scroll to section based on hash
      const reloadAndScrollHandler = hashToReloadAndScrollHandler[history.location.hash];
      if (reloadAndScrollHandler) {
        setLoadingOtherDocument(true);
        await reloadAndScrollHandler();
        setLoadingOtherDocument(false);
      }
    })();
  }, [hash, hasDropDownData]);

  const loadDocsData = async (seafarerPersonId, queryParam) => {
    const { data } = await seafarerService.getSeafarerDocumentsList(seafarerPersonId, queryParam);
    let responseObj = {};
    for (const dataObj of data) {
      if (responseObj[dataObj.type]) {
        responseObj[dataObj.type].push(dataObj);
      } else {
        responseObj[dataObj.type] = [dataObj];
      }
    }

    return responseObj;
  };

  useEffect(() => {
    (async () => {
      if (hash !== undefined) {
        return;
      }

      try {
        setLoadingOtherDocument(true);
        const queryParam =
          '?type=medical&type=drug_alcohol_test&type=certificate_of_competency&type=stcw&type=training&type=education&type=other_course&type=other_document&type=apprenticeship&type=pre_sea_training&type=correspondence_details&type=user_defined_document';

        const responseObj = await loadDocsData(seafarerPersonId, queryParam);
        const dropDownDataResponse = await controller.loadDropDownData();
        _setMedicalData(responseObj, dropDownDataResponse?.medicalCertificates);
        _setCertificateOfCompetencyData(responseObj, dropDownDataResponse?.cocCertificates);
        _setStcwData(responseObj, dropDownDataResponse?.stcwLicences);
        _setEducationData(responseObj);
        _setApprenticeshipData(responseObj);
        _setTrainingData(responseObj);
        _setPreSeaTrainingData(responseObj);
        _setOtherDocumentData(responseObj, dropDownDataResponse?.otherDocumentsTypes);
        _setOtherCourseData(responseObj, dropDownDataResponse?.otherCourseTypes);
        _setCorrespondenceDetailsData(responseObj);
        _setUserDefinedData(responseObj, dropDownDataResponse?.userDefinedDocumentTypes);
      } catch (error) {
        console.error(
          `Get seafarer documents by Seafarer ID: ${seafarerId} failed. Error: ${error}`,
        );
      }
      setLoadingOtherDocument(false);
    })();
  }, [seafarerId]);

  const downloadFile = async (documentType, document, fileName) => {
    if (loadingOtherDocument) {
      return;
    }
    if (documentType && document && fileName) {
      window.open(`https://${window.location.hostname}/seafarer/document/${document.id}/${documentType}`);
    }
    eventTracker('viewDocument', 'Other Document');
  };

  const deleteHandler = async (row) => {
    eventTracker('deleteDocument', 'Document');
    setSelectedDocument(row);
    setIsRemoveDocumentConfirmationModalShow(true);
  };

  const handleDeleteSeafarerDocument = async () => {
    try {
      setIsDisableConfirmDeleteBtn(true);
      setIsModalLoading(true);
      await seafarerService.deleteSeafarerDocument(selectedDocument?.id, selectedDocument?.seafarer_doc_user_defined_document?.user_defined_document_type_id || null);
      if (
        selectedDocument?.type === ALL_DOC_TYPES.MEDICAL ||
        selectedDocument?.type === ALL_DOC_TYPES.DRUG_ALCOHOL_TEST
      ) {
        const newData = medicalData?.filter((ele) => ele.id !== selectedDocument?.id);
        setMedicalData(newData);
      }
      if (selectedDocument?.type === ALL_DOC_TYPES.CERTIFICATE_OF_COMPETENCY) {
        const newData = certificateOfCompetencyData?.filter(
          (ele) => ele.id !== selectedDocument?.id,
        );
        setCertificateOfCompetencyData(newData);
      }
      if (selectedDocument?.type === ALL_DOC_TYPES.STCW) {
        const newData = stcwData?.filter((ele) => ele.id !== selectedDocument?.id);
        setStcwData(newData);
      }
      if (selectedDocument?.type === ALL_DOC_TYPES.EDUCATION) {
        const newData = educationData?.filter((ele) => ele.id !== selectedDocument?.id);
        setEducationData(newData);
      }
      if (selectedDocument?.type === ALL_DOC_TYPES.APPRENTICESHIP) {
        const newData = apprenticeshipData?.filter((ele) => ele.id !== selectedDocument?.id);
        setApprenticeshipData(newData);
      }
      if (selectedDocument?.type === ALL_DOC_TYPES.TRAINING) {
        const newData = trainingData?.filter((ele) => ele.id !== selectedDocument?.id);
        setTrainingData(newData);
      }
      if (selectedDocument?.type === ALL_DOC_TYPES.PRE_SEA_TRAINING) {
        const newData = preSeaTrainingData?.filter((ele) => ele.id !== selectedDocument?.id);
        setPreSeaTrainingData(newData);
      }
      if (selectedDocument?.type === ALL_DOC_TYPES.OTHER_DOCUMENT) {
        const newData = otherDocumentData?.filter((ele) => ele.id !== selectedDocument?.id);
        setOtherDocumentData(newData);
      }
      if (selectedDocument?.type === ALL_DOC_TYPES.OTHER_COURSE) {
        let newOtherCourseData = { ...otherCourseData };

        Object.keys(newOtherCourseData).forEach((key) => {
          newOtherCourseData[key] = newOtherCourseData[key]?.filter(
            (ele) => ele.id !== selectedDocument?.id,
          );
        });
        setOtherCourseData(newOtherCourseData);
      }
      if (selectedDocument?.type === ALL_DOC_TYPES.CORRESPONDENCE_DETAILS) {
        const newData = correspondenceDetailsData?.filter((ele) => ele.id !== selectedDocument?.id);
        setCorrespondenceDetailsData(newData);
      }
      if (selectedDocument?.type === ALL_DOC_TYPES.USER_DEFINED_DOCUMENT) {
        let newUserDefinedData = { ...userDefinedData };

        Object.keys(newUserDefinedData).forEach((key) => {
          newUserDefinedData[key] = newUserDefinedData[key]?.filter(
            (ele) => ele.id !== selectedDocument?.id,
          );
        });

        setUserDefinedData(newUserDefinedData);
      }

      setIsRemoveDocumentConfirmationModalShow(false);
      setIsModalLoading(false);
      setIsDisableConfirmDeleteBtn(false);
    } catch (error) {
      setIsModalLoading(false);
      setIsRemoveDocumentConfirmationModalShow(false);
      setModalMessage('Oops something went wrong while deleting seafarer document.');
      console.error(
        `Delete seafarer Document by ID: ${selectedDocument?.id} failed. Error: ${error}`,
      );
      setIsDisableConfirmDeleteBtn(false);
    }
  };

  const documents = generateDocuments(downloadFile);

  const certificates = generateCertificates(downloadFile);

  const editHandler = async (documentType, documentId) => {
    eventTracker('editDocument', 'Document');
    history.push(
      `/seafarer/details/${seafarerId}/other-documents/${documentType}/edit/${documentId}`,
    );
  };

  const action = generateAction(showDeleteAndEdit, editHandler, deleteHandler);

  const visitVesselDetailPage = async (vesselRefId) => {
    if (vesselRefId) {
      const result = await seafarerService.getVesselOwnershipId(vesselRefId);
      history.push(`/vessel/ownership/details/${result.data.ownership_id}`);
    }
  };

  const displayData = (id, dropDownData) => {
    if (id && dropDownData?.length) {
      let dropDownItem = dropDownData.find((item) => {
        return item.id == id;
      });
      return dropDownItem.value ?? '';
    }
    return '';
  };

  const medicalColumn = generateMedicalColumn({
    displayData,
    dropdownData,
    visitVesselDetailPage,
    documents,
    action,
  });

  const certificatesOfCompentencyColumn = generateCertificatesOfCompentencyColumn({
    displayData,
    dropdownData,
    documents,
    action,
  });

  const STCWColumn = generateSTCWColumn({
    displayData,
    dropdownData,
    documents,
    action,
  });

  const EducationColumn = [...Education, ...certificates, ...action];

  const ApprenticeshipColumn = [...Apprenticeship, ...certificates, ...action];

  const TrainingColumn = generateTrainingColumn({
    certificates,
    action,
  });

  const onSubmitDocument = (props) => {
    refreshDetailsPageData();
  };

  const PreSeaTrainingColumn = generatePreSeaTrainingColumn({
    displayData,
    dropdownData,
    certificates,
    action,
  });

  const OtherCoursesColumn = [...OtherCourses, ...certificates, ...action];
  const CorrespondenceDetailsColumn = [...CorrespondenceDetails, ...certificates, ...action];
  const OtherDocumentsColumn = [
    {
      Header: 'Document',
      id: 'document',
      disableSortBy: true,
      accessor: (row) => {
        return displayData(
          row?.seafarer_doc_other_document?.other_document_type_id,
          dropdownData.otherDocumentsTypes,
        );
      },
      maxWidth: 100,
    },
    ...OtherDocuments,
    ...documents,
    ...action,
  ];

  const UserDefinedDocuments = [...UserDefinedDocs, ...documents, ...action];

  const isNationalCertificateOfCompetency = certificateOfCompetencyData.some(
    (item) => item?.seafarer_doc_certificate_of_competency?.is_original === true,
  );
  const cocSortedColumns = [...certificatesOfCompentencyColumn].sort((a, b) => a.order - b.order);

  // NOSONAR
  return (
    <>
      <Row>
        <Col>
          <div className="font-weight-bold p-2 document-table-title" ref={medicalSectionRef}>
            MEDICAL
          </div>
        </Col>
      </Row>
      <Row>
        <Col>
          <DocumentTable
            data={medicalData}
            loading={loadingOtherDocument}
            columns={medicalColumn}
            dataTestId="medical-table"
          />
        </Col>
      </Row>
      <div className="details_page__table_head" ref={certificateOfCompetencySectionRef}>
        <Row>
          <Col>
            <div className="font-weight-bold p-1">CERTIFICATE OF COMPETENCY</div>
            {isNationalCertificateOfCompetency && (
              <div className="font-weight-bold is-original">*Is National</div>
            )}
          </Col>
        </Row>
      </div>
      <Row>
        <Col>
          <DocumentTable
            data={certificateOfCompetencyData}
            loading={loadingOtherDocument}
            columns={cocSortedColumns}
            dataTestId="certificate-of-competency-table"
          />
        </Col>
      </Row>
      <div className="details_page__table_head" ref={stcwSectionRef}>
        <Row>
          <Col>
            <div className="font-weight-bold p-2">STCW</div>
          </Col>
        </Row>
      </div>
      <Row>
        <Col>
          <DocumentTable
            data={stcwData}
            loading={loadingOtherDocument}
            columns={STCWColumn}
            dataTestId="stcw-table"
          />
        </Col>
      </Row>
      <div className="details_page__table_head" ref={educationSectionRef}>
        <Row>
          <Col>
            <div className="font-weight-bold p-2">EDUCATION</div>
          </Col>
        </Row>
      </div>
      <Row>
        <Col>
          <DocumentTable
            data={educationData}
            loading={loadingOtherDocument}
            columns={EducationColumn}
            dataTestId="education-table"
          />
        </Col>
      </Row>
      <div className="details_page__table_head" ref={apprenticeshipSectionRef}>
        <Row>
          <Col>
            <div className="font-weight-bold p-2">APPRENTICESHIP</div>
          </Col>
        </Row>
      </div>
      <Row>
        <Col>
          <DocumentTable
            data={apprenticeshipData}
            loading={loadingOtherDocument}
            columns={ApprenticeshipColumn}
            dataTestId="apprenticeship-table"
          />
        </Col>
      </Row>
      <div className="details_page__table_head" ref={trainingSectionRef}>
        <Row>
          <Col>
            <div className="font-weight-bold p-2">TRAINING</div>
          </Col>
        </Row>
      </div>
      <Row>
        <Col>
          <DocumentTable
            data={trainingData}
            loading={loadingOtherDocument}
            columns={TrainingColumn}
            dataTestId="training-table"
          />
        </Col>
      </Row>
      <div className="details_page__table_head" ref={preseaTraingSectionRef}>
        <Row>
          <Col>
            <div className="font-weight-bold p-2">PRE SEA TRAINING</div>
          </Col>
        </Row>
      </div>
      <Row>
        <Col>
          <DocumentTable
            data={preSeaTrainingData}
            loading={loadingOtherDocument}
            columns={PreSeaTrainingColumn}
            dataTestId="pre-sea-training-table"
          />
        </Col>
      </Row>
      <div className="details_page__table_head" ref={otherCoursesSectionRef}>
        <Row>
          <Col>
            <div className="font-weight-bold p-2">OTHER COURSES</div>
          </Col>
        </Row>
      </div>
      {Object.keys(otherCourseData).length > 0 ? (
        Object.entries(otherCourseData).map(([key, value]) => {
          return (
            <div key={key}>
              <Row>
                <div className="table-sub-heading font-weight-bold">{key}</div>
              </Row>
              <Row>
                <Col>
                  <DocumentTable
                    data={value}
                    loading={loadingOtherDocument}
                    columns={OtherCoursesColumn}
                    dataTestId="other-courses-table"
                  />
                </Col>
              </Row>
            </div>
          );
        })
      ) : (
        <Row>
          <Col>
            <DocumentTable
              data={[]}
              loading={loadingOtherDocument}
              columns={OtherCoursesColumn}
              dataTestId="other-courses-table"
            />
          </Col>
        </Row>
      )}
      <div className="details_page__table_head" ref={correspondenceDetailsSectionRef}>
        <Row>
          <Col>
            <div className="font-weight-bold p-2">CORRESPONDENCE DETAILS</div>
          </Col>
        </Row>
      </div>
      <Row>
        <Col>
          <DocumentTable
            data={correspondenceDetailsData}
            loading={loadingOtherDocument}
            columns={CorrespondenceDetailsColumn}
            dataTestId="correspondence-details-table"
          />
        </Col>
      </Row>
      <div className="details_page__table_head" ref={otherDocumentsSectionRef}>
        <Row>
          <Col>
            <div className="font-weight-bold p-2">OTHER DOCUMENTS</div>
          </Col>
        </Row>
      </div>
      <Row>
        <Col>
          <DocumentTable
            data={otherDocumentData}
            loading={loadingOtherDocument}
            columns={OtherDocumentsColumn}
            dataTestId="other-documents-table"
          />
        </Col>
      </Row>
      <div className="details_page__table_head" ref={userDefinedDocumentsSectionRef}>
        <Row>
          <Col>
            <div className="font-weight-bold p-2">USER DEFINED DOCUMENTS</div>
          </Col>
        </Row>
      </div>
      {Object.keys(userDefinedData).length > 0 ? (
        Object.entries(userDefinedData).map(([key, value]) => {
          return (
            <div key={key}>
              <Row>
                <div className="table-sub-heading font-weight-bold">{key}</div>
              </Row>
              <Row>
                <Col>
                  <DocumentTable
                    data={value}
                    loading={loadingOtherDocument}
                    columns={UserDefinedDocuments}
                    dataTestId="user-defined-table"
                  />
                </Col>
              </Row>
            </div>
          );
        })
      ) : (
        <Row>
          <Col>
            <DocumentTable
              data={[]}
              loading={loadingOtherDocument}
              columns={UserDefinedDocuments}
              dataTestId="user-defined-table"
            />
          </Col>
        </Row>
      )}
      {/* route to popup modal for editing documents*/}
      <DocumentDownloadFailedModal
        show={showDownloadFailModal}
        onClose={() => setShowDownloadFailModal(false)}
        title={'Download Failed'}
      >
        <p>{DOC_DOWNLOAD_FAIL_MESSAGE}</p>
      </DocumentDownloadFailedModal>
      <Switch>
        {documentTypes.OTHER_DOCUMENTS_TAB_DOC_TYPE_KEYS.map((doc_type_key) => (
          <Route
            key={doc_type_key}
            exact
            path={`/seafarer/details/:seafarerId/other-documents/${documentTypes.ALL_DOC_TYPES[doc_type_key]}/edit/:documentId`}
          >
            <AddDocumentModal
              targetId={documentTypes.DOC_FORM_IDS[doc_type_key]}
              seafarerPersonId={seafarerPersonId}
              history={history}
              docId={documentId}
              dropdownData={dropdownData}
              eventTracker={eventTracker}
              onSubmitCallback={onSubmitDocument}
            ></AddDocumentModal>
          </Route>
        ))}
      </Switch>
      <ConfirmActionModalView
        show={isRemoveDocumentConfirmationModalShow}
        onClose={() => setIsRemoveDocumentConfirmationModalShow(false)}
        onConfirm={handleDeleteSeafarerDocument}
        title={'Confirm Deleting the document?'}
        message={'Are you sure deleting the document?'}
        isDisableConfirmDeleteBtn={isDisableConfirmDeleteBtn}
        isModalLoading={isModalLoading}
      />
      <ErrorDisplayModal onHideModalMessage={onHideModalMessage} modalMessage={modalMessage} />
    </>
  );
};

export default OtherDocumentsPage;
