import React, { useState, useEffect, forwardRef } from 'react';
import { Container, Col, Row, Button } from 'react-bootstrap';
import { useParams } from 'react-router-dom';
import AccessHandlerWrapper from '../component/common/AccessHandlerWrapper';
import seafarerService from '../service/seafarer-service';
import Spinner from '../component/common/Spinner';
import BackToTop from '../component/BackToTopButton';
import ErrorAlert from '../component/common/ErrorAlert';
import AccountDetails from '../model/AccountDetails';
import styleGuide from '../styleGuide';
const { Icon } = styleGuide;

import ImageController from '../controller/image-upload-controller';
const imageController = new ImageController();

const TableRow = ({ data, isSectionHeader, downloadFile }) => {
  return data?.map((item) => {
    const classLabel = isSectionHeader
      ? 'details_page__row-name__header'
      : 'details_page__row-name';
    const classValue = isSectionHeader
      ? 'details_page__row-value__header'
      : 'details_page__row-value';
    const isDocument = item.document !== undefined;

    return (
      <tr key={item.label}>
        <td className={classLabel}>{item.label}</td>
        <td className={classValue}>
          {isDocument ? (
            <Button
              variant="link"
              className="selected-file-link"
              onClick={downloadFile.bind(this, item.documentType, item.document, item.fileName)}
            >
              {item.value}
            </Button>
          ) : (
            item.value
          )}
        </td>
      </tr>
    );
  });
};

const TableSection = forwardRef((props, ref) => (
  <Col md={12} lg={6} xl={6} xxl={6}>
    <table style={{ backgroundColor: props.backgroundColor }} className="table table-hover">
      <thead className="details_page__table_head">
        <tr>
          <th id={props.id} colSpan="2">
            {props.title} {props.id + 1}
          </th>
        </tr>
      </thead>
      <tbody>
        <TableRow data={props.data} downloadFile={props.downloadFile} />
      </tbody>
    </table>
  </Col>
));

const AccountDetailsPage = ({ roleConfig }) => {
  let { seafarerId } = useParams();
  const [error, setError] = useState(null);
  const [seafarer, setSeafarer] = useState(null);

  useEffect(() => {
    (async () => {
      try {
        const response = await seafarerService.getSeafarer(seafarerId);
        setSeafarer(response.data);
      } catch (error) {
        setError(`Oops, something went wrong. Please try again. Error: ${error}`);
        console.log('## Error', error);
        console.error(`Get seafarer by ID: ${seafarerId} failed. Error: ${error}`);
      }
    })();
  }, []);

  const downloadFile = async (documentType, document, fileName) => {
    window.open(
      `https://${window.location.hostname}/seafarer/document/${document.id}/${documentType}`,
    );
  };

  return (
    <AccessHandlerWrapper hasRoleAccess={roleConfig.seafarer.view.bankAccount}>
      <Container className="accdetails_page">
        {error ? <ErrorAlert message={error} /> : ''}
        {seafarer ? (
          <Row>
            {seafarer.seafarer_person.bank_accounts?.map((item, index) => {
              return (
                <TableSection
                  key={item.id}
                  id={index}
                  title="Bank Account"
                  data={AccountDetails(item)}
                  downloadFile={downloadFile}
                />
              );
            })}
          </Row>
        ) : (
          <div className="spinner-container">
            <Spinner />
          </div>
        )}
        <BackToTop />
      </Container>
    </AccessHandlerWrapper>
  );
};

export default AccountDetailsPage;
