/* eslint-disable react/prop-types */
import _ from 'lodash';
import React, { useState, useEffect, Fragment } from 'react';
import { Container, Col, Row, Button } from 'react-bootstrap';
import { useParams } from 'react-router-dom';
import seafarerService from '../service/seafarer-service';
import screeningService from '../service/screening-service';
import ErrorAlert from '../component/common/ErrorAlert';
import { getShortDate } from '../util/view-utils';
import styleGuide from '../styleGuide';
const { Icon } = styleGuide;
import { ScreeningModalView } from '../component/screening/ScreeningModalView';
import Remarks from '../component/common/Remarks';
import Spinner from '../component/common/Spinner';
import AccessHandlerWrapper from '../component/common/AccessHandlerWrapper';
import { approvalStatuses } from '../model/constants';
import FileUploadComponents from '../component/common/FileUploadModalView';
import WcoScreeningDetails from '../component/screening/WcoScreeningDetails';
import { getScreenForColumnValue } from '../component/seafarerList/MenuList';
import { useAccess } from '@src/component/common/Access';

const { UploadedFilesModalView } = FileUploadComponents;
const TableHeaderRow = () => {
  return (
    <tr key="header">
      <th scope="col">Approval Group</th>
      <th scope="col">Date</th>
      <th scope="col">Approver Name</th>
      <th scope="col">Screening for</th>
      <th scope="col">Status</th>
      <th scope="col">Remarks</th>
      <th scope="col">Documents</th>
    </tr>
  );
};

const HKIdInfo = ({ seafarerData }) => {
  return seafarerData?.['hkid'] ? (
    <div style={{ marginTop: '60px' }}>
      <Icon icon="checked" size={20} className="float-left screening-page__moved-tick" />
      <span>The Seafarer ID (HKID) is </span>
      <span>
        <b>{seafarerData['hkid']}</b>.
      </span>
    </div>
  ) : null;
};

const ScreeningHistoryLink = ({ approvalData, eventTracker, seafarerId }) => {
  const haveScreeningHistory =
    approvalData.filter((approval) => approval.approval_status !== approvalStatuses.PENDING)
      .length > 0;
  return haveScreeningHistory ? (
    <a
      style={{ color: '#1F4A70' }}
      onClick={() => eventTracker('routeToScreening', 'Screening History')}
      href={`/seafarer/screening-history/${seafarerId}`}
      target={'_blank'}
      rel="noopener noreferrer"
    >
      <u>Screening History</u>
    </a>
  ) : null;
};

const LoggedInUserActionButtons = ({
  userApprovalData,
  latestApproval,
  userBelongsToSameGroup,
  roleConfig,
  handleScreeningButtonClick,
}) => {
  const userApprovalId = userApprovalData ? userApprovalData.id : latestApproval.id;
  const userApprovalStatus = userApprovalData
    ? userApprovalData.approval_status
    : latestApproval.approval_status;
  const isInSameGroup = userBelongsToSameGroup(latestApproval?.approval_group);
  switch (userApprovalStatus) {
    case approvalStatuses.PENDING: {
      return (
        <td colSpan="6" className="button-row">
          {roleConfig.seafarer.screening.approve && isInSameGroup && (
            <Button
              className="btn-sm mr-2"
              variant="outline-primary"
              onClick={() => handleScreeningButtonClick(userApprovalId, 'approve')}
              data-testid="approve-screening"
            >
              Approve
            </Button>
          )}
          {roleConfig.seafarer.screening.forward ? (
            <Button
              className="btn-sm mr-2"
              variant="outline-primary"
              onClick={() => handleScreeningButtonClick(userApprovalId, 'forward')}
              data-testid="forward-to-superior-screening"
            >
              Forward to Supervisor
            </Button>
          ) : null}
          {roleConfig.seafarer.screening.approve && isInSameGroup && (
            <Button
              className="btn-sm mr-2"
              variant="outline-danger"
              onClick={() => handleScreeningButtonClick(userApprovalId, 'reject')}
              data-testid="reject-screening"
            >
              Reject
            </Button>
          )}
          {roleConfig.seafarer.screening.reapply &&
            latestApproval.approval_status === approvalStatuses.REJECTED && (
              <Button
                className="btn-sm mr-2"
                variant="outline-primary"
                onClick={() => handleScreeningButtonClick(userApprovalId, 'reapply')}
                data-testid="reapply-screening"
              >
                Reapply
              </Button>
            )}
        </td>
      );
    }
    case approvalStatuses.APPROVED:
    case approvalStatuses.REJECTED:
      return null;
    default:
      return null;
  }
};

const TableDataRows = ({
  data,
  getLatestApprovalForUserView,
  roleConfig,
  actionLoadingFor,
  showUploadedFilesModal,
  handleScreeningButtonClick,
  userBelongsToSameGroup,
}) => {
  const userGroup = roleConfig.screeningGroup;
  if (data.length === 0) return null;

  // data = data.sort(getSortOrder('updated_at'));
  // const latestApproval = // data[data.length - 1];
  const latestApproval = data.length > 1 ? getLatestApprovalForUserView(data, userGroup) : data[0];
  const isRowStatusNotPending = latestApproval.approval_status !== approvalStatuses.PENDING;
  const latestUserGroupApproval = _.maxBy(
    data.filter((approval) => approval.approval_group === userGroup),
    'updated_at',
  );
  const isUserActionButtonVisible = latestApproval ?? roleConfig.seafarer.screening.approve;
  return (
    <Fragment key={'approval-table'}>
      <tr
        key={latestApproval.id}
        className={latestApproval.approval_status}
        style={{ borderBottom: 'none !important' }}
      >
        <td>{latestApproval.approval_group}</td>
        <td>
          {isRowStatusNotPending && latestApproval.updated_at
            ? getShortDate(latestApproval.updated_at)
            : ''}
        </td>
        <td>{isRowStatusNotPending ? latestApproval.approver_name : ''}</td>
        <td>{getScreenForColumnValue(latestApproval.screening_for)}</td>
        <td>
          {actionLoadingFor.rowId === latestApproval.id ? (
            <Spinner alignClass="align-left" />
          ) : (
            latestApproval.approval_status
          )}
        </td>
        <td>
          {isRowStatusNotPending && latestApproval.remarks ? (
            <Remarks remarksData={latestApproval.remarks} />
          ) : (
            ''
          )}
        </td>
        <td>
          {isRowStatusNotPending && latestApproval.document.length > 0 ? (
            <button
              type="button"
              className="btn btn-link p-0 text-left"
              onClick={() => showUploadedFilesModal(latestApproval.id)}
            >
              <u>View</u>
            </button>
          ) : null}
        </td>
      </tr>
      <tr key={`btn-${latestApproval.id}`}>
        {isUserActionButtonVisible ? (
          <LoggedInUserActionButtons
            userApprovalData={latestUserGroupApproval}
            latestApproval={latestApproval}
            userBelongsToSameGroup={userBelongsToSameGroup}
            roleConfig={roleConfig}
            handleScreeningButtonClick={handleScreeningButtonClick}
          />
        ) : null}
      </tr>
    </Fragment>
  );
};

const PageView = ({
  seafarerData,
  approvalData,
  seafarerId,
  roleConfig,
  setHasStatusChanged,
  eventTracker,
  setChangedStatus,
}) => {
  const [showScreeningModal, setShowScreeningModal] = useState(false);
  const [approvalId, setApprovalId] = useState(undefined);
  const [actionLoadingFor, setActionLoadingFor] = useState({ rowId: null });
  const [actionType, setActionType] = useState(undefined);
  const [showUploadedFiles, setShowUploadedFiles] = useState(false);
  const [uploadedDocuments, setUploadedDocuments] = useState({});

  const handleScreeningButtonClick = (approvalId, button) => {
    eventTracker('screeningDetails', button);
    setActionType(button);
    setApprovalId(approvalId);
    setShowScreeningModal(true);
  };

  const getLatestApprovalForUserView = (approvals, userGroup) => {
    const sortedApproval = _.orderBy(approvals, 'updated_at', 'desc');
    const isRecentApprovalBySameUser = sortedApproval[0].approval_group === userGroup;
    const isRecentApprovalPending = sortedApproval[0].approval_status === approvalStatuses.PENDING;
    const isRecentActionRework =
      isRecentApprovalPending &&
      isRecentApprovalBySameUser &&
      sortedApproval[0].approval_group === sortedApproval[1].approval_group;

    const isRecentActionApproved = sortedApproval[0].approval_status === approvalStatuses.APPROVED;
    const res =
      isRecentActionRework ||
      isRecentActionApproved ||
      userGroup == null ||
      sortedApproval[1]?.approval_status === 'reapplied' || //for showing "pending" record for everyone after seafarer has been reapplied
      sortedApproval[1]?.approval_status === 'forwarded' || //for showing "pending" record for everyone after seafarer has been forwarded
      sortedApproval[1]?.approval_status === 'approved' //for showing "pending" record for everyone for rescreening flow
        ? sortedApproval[0]
        : sortedApproval[1];
    return res;
  };

  const userBelongsToSameGroup = (dataApprovalGroup) => {
    return dataApprovalGroup === roleConfig?.screeningGroup;
  };

  const prepareDocumentsData = (approvalId) => {
    (approvalData ?? []).forEach((item) => {
      if (item.id === approvalId) {
        setUploadedDocuments(item.document);
      }
    });
  };

  const showUploadedFilesModal = (approvalId) => {
    prepareDocumentsData(approvalId);
    setShowUploadedFiles(true);
  };

  const renderTable = () => {
    setHasStatusChanged(Math.random());
  };

  return (
    <>
      <Row>
        <Col>
          <h6 className="screening-page__section-header">1. APPROVAL</h6>
          <table className="table" id="test__firstApproval">
            <thead>
              <TableHeaderRow />
            </thead>
            {approvalData ? (
              <tbody>
                <TableDataRows
                  data={approvalData}
                  getLatestApprovalForUserView={getLatestApprovalForUserView}
                  roleConfig={roleConfig}
                  actionLoadingFor={actionLoadingFor}
                  showUploadedFilesModal={showUploadedFilesModal}
                  handleScreeningButtonClick={handleScreeningButtonClick}
                  userBelongsToSameGroup={userBelongsToSameGroup}
                />
                <tr></tr>
              </tbody>
            ) : null}
          </table>
          {approvalData ? (
            <>
              <ScreeningModalView
                show={showScreeningModal}
                closeScreeningModal={() => setShowScreeningModal(false)}
                seafarerId={seafarerId}
                approvalId={approvalId}
                setLoadingFor={setActionLoadingFor}
                renderTable={renderTable}
                actionType={actionType}
                roleConfig={roleConfig}
                setChangedStatus={setChangedStatus}
              />
              <UploadedFilesModalView
                show={showUploadedFiles}
                uploadedFiles={uploadedDocuments}
                onClose={() => setShowUploadedFiles(false)}
              />
            </>
          ) : (
            <Spinner />
          )}
        </Col>
      </Row>
      <hr></hr>
      <Row>
        <Col>
          {approvalData ? (
            <>
              <ScreeningHistoryLink
                approvalData={approvalData}
                eventTracker={eventTracker}
                seafarerId={seafarerId}
              />
              <HKIdInfo seafarerData={seafarerData} />
              <hr></hr>
            </>
          ) : null}
        </Col>
      </Row>
      <Row>
        <Col>
          {seafarerData ? (
            <WcoScreeningDetails
              seafarerId={seafarerData.id}
              seafarerPersonId={seafarerData['seafarer_person.id']}
            />
          ) : null}
        </Col>
      </Row>
    </>
  );
};

const ScreeningPage = ({ eventTracker, setChangedStatus }) => {
  let { seafarerId } = useParams();
  const { roleConfig } = useAccess();
  const [seafarerData, setSeafarerData] = useState(null);
  const [approvalData, setApprovalData] = useState(null);
  const [error, setError] = useState(null);
  const [hasStatusChanged, setHasStatusChanged] = useState(0);

  useEffect(() => {
    (async () => {
      try {
        setApprovalData(null);
        const seafarerResponse = await seafarerService.getSeafarerFieldsData(seafarerId, [
          'id',
          'first_name',
          'last_name',
          'hkid',
          'seafarer_person.id',
        ]);

        const seafarer = seafarerResponse.data.results[0];
        setSeafarerData(seafarer);
        const personId = seafarer['seafarer_person.id'];
        let approvalResponse = await screeningService.getScreeningData(personId);
        setApprovalData(approvalResponse.data);
      } catch (error) {
        setError('Oops, something went wrong. Please try again.');
        console.error(`get screening data in service error for ${seafarerId}. Error: ${error}`);
      }
    })();
  }, [hasStatusChanged]);

  const hasAccess = () => {
    const person = roleConfig.seafarer;
    return person?.screening.view;
  };

  return (
    <AccessHandlerWrapper hasRoleAccess={hasAccess()}>
      <div className="screening-page">
        <Container>
          {error && <ErrorAlert message={error} />}
          <PageView
            seafarerData={seafarerData}
            approvalData={approvalData}
            seafarerId={seafarerId}
            roleConfig={roleConfig}
            setHasStatusChanged={setHasStatusChanged}
            setChangedStatus={setChangedStatus}
            eventTracker={eventTracker}
          />
        </Container>
      </div>
    </AccessHandlerWrapper>
  );
};

export default ScreeningPage;
