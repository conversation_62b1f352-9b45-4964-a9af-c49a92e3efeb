$blue-color: #1f4a70;
$gray-color: #cccccc;
$orange-color: #ffa221;
$red-color: #d41b56;

.seafarer-recommendation {
  a {
    text-decoration: underline !important;
    color: $blue-color;
    font-weight: 500;
  }
  .container {
    margin-bottom: 10rem;
    padding: 0px 30px;
  }
  .breadcrumb-container {
    min-width: 80%;
    .paris1-link-detail-page {
      color: $blue-color;
      text-decoration: underline !important;
      padding-left: 5px;
    }
  }
  .close-btn-container {
    display: flex;
    justify-content: end;
    align-items: center;
    .close {
      cursor: pointer;
      margin-right: 1rem;
    }
  }

  .errors-display {
    border-radius: 4px;
    margin-top: 2rem;
    padding: 0.75rem;
    .errors-heading {
      color: $red-color;
      font-size: 20px;
    }
    ul {
      padding-left: 1rem;
      margin-bottom: 0px;
      li {
        float: none;
        list-style: initial;
        text-decoration: underline;
        font-weight: normal;
      }
    }
  }

  .required-fields-label {
    margin: 1rem 0 1.5rem;
  }

  .form-fields-container {
    .row {
      margin-bottom: 1rem;
    }
    .form-label {
      margin-bottom: 0;
      font-weight: bold;
    }
    .react-datepicker-wrapper,
    .input-group,
    .form-control {
      width: 85%;
    }
  }

  .ocimf-label {
    color: $blue-color;
    margin-top: 3rem;
    font-weight: bold;
  }

  .grid {
    margin-bottom: 3rem;
    position: relative;
    .orange-text {
      color: $orange-color;
    }
  }

  .checklist-label {
    @extend .ocimf-label;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid $gray-color;
  }

  .checklist {
    .row {
      padding: 0.5rem;
      margin: 0 !important;
      padding-left: 0px;
      border-bottom: 1px solid #cccccc;
      .col {
        padding-left: 0px;
        padding-right: 0px;
      }
    }
  }

  .radio-container {
    display: flex;
    justify-content: end;

    .radio-item {
      margin-left: 2rem;
      display: flex;
      align-items: center;

      .radio-input {
        margin-right: 0.3rem;
        height: 18px;
        width: 18px;
      }
    }
  }

  .sticky-footer {
    text-align: center;
    padding: 0.6rem 0;
    position: fixed;
    bottom: 0;
    width: 100%;
    background-color: #f8f9fa;
    border-top: 1px solid darkgray;
    button {
      width: 380px;
    }
  }

  .checklist-answer {
    position: relative;
    text-align: center;
  }

  .checklist-answer-yes-span {
    position: absolute;
    right: 160px;
    color: #28A747;
  }

  .checklist-answer-no-span {
    position: absolute;
    right: 83px;
    color: #ff0000;
  }

  .radio-item .radio-input:checked {
    accent-color: $blue-color;
  }
 
  .submit-recommend .save-recommendation-spinner {
      justify-content: center;
      .text-secondary{
        color: #FFFFFF !important;
      }    
  }
  .submit-recommend:disabled {
      background-color: #DEDEDE;
      color: #666666;
      border-color: #DEDEDE;
  }
  .spinner-border {
    width: 21px;
    height: 21px;
  }

  .spinner-grid {
    justify-content: center;
    position: absolute;
    left: 50%;
    top: 90px;
  }
}