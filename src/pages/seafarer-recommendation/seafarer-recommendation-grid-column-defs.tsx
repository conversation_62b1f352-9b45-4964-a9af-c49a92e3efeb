import React from 'react';

const topRanks =  ['CHIEF OFFICER', 'MASTER', '2ND ENGINEER', 'CHIEF ENGINEER'];

const getCellClassName = (cell, row, limit) => {
  if (topRanks.includes(row.values.rank) && cell.value < limit) {
    return 'orange-text';
  } else {
    return '';
  }
};

export const seafarerRecommendationGridColumnDefs = [
  {
    Header: 'HKID',
    accessor: 'hkid',
    Footer: null,
    disableSortBy: true,
    customHeaderCellStyle: {
      paddingLeft: '0px',
    },
    customDataCellStyle: {
      paddingLeft: '0px',
    },
    maxWidth: 50,
    Cell: ({ row }) => {
      return (
        <a href={`/seafarer/details/${row.original.seafarerId}/general`} target="_blank">
          {row.original.hkid}
        </a>
      );
    },
  },
  {
    Header: 'Name',
    accessor: 'name',
    Footer: null,
    disableSortBy: true,
    maxWidth: 100,
  },
  {
    Header: 'Rank',
    accessor: 'rank',
    Footer: null,
    disableSortBy: true,
    maxWidth: 100,
  },
  {
    Header: 'Years with Company',
    accessor: 'yearsWithCompany',
    Footer: null,
    disableSortBy: true,
    maxWidth: 100,
  },
  {
    Header: 'Years in Rank',
    accessor: 'yearsInCurrentRank',
    Footer: null,
    disableSortBy: true,
    maxWidth: 100,
    Cell: ({ cell, row }) => {
      return <span className={getCellClassName(cell, row, 0.5)}>{cell.value}</span>;
    },
  },
  {
    Header: 'Years on this type of tanker',
    accessor: 'yearsOnCurrentTypeOfVessel',
    Footer: null,
    disableSortBy: true,
    maxWidth: 100,
    Cell: ({ cell, row }) => {
      return <span className={getCellClassName(cell, row, 1.0)}>{cell.value}</span>;
    },
  },
  {
    Header: 'Years on all types of tanker',
    accessor: 'yearsOnAllTypesOfVessel',
    Footer: null,
    disableSortBy: true,
    maxWidth: 100,
    Cell: ({ cell, row }) => {
      return <span className={getCellClassName(cell, row, 2.5)}>{cell.value}</span>;
    },
  },
];