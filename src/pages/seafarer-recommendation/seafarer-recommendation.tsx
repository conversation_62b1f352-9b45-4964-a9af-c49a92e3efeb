import { useFormik } from 'formik';
import React, { ChangeEvent, useEffect, useMemo, useRef, useState } from 'react';
import { Button, Col, Container, Form, InputGroup, Row } from 'react-bootstrap';
import DatePicker from 'react-datepicker';
import { useHistory, useParams } from 'react-router';
import { BreadcrumbHeader } from '../../component/BreadcrumpHeader';
import ErrorAlert from '../../component/common/ErrorAlert';
import { Grid } from '../../component/grid/grid';
import {
  seafarerValidationSchema,
  supyValidationSchema,
} from '../../model/seafarer-recommendation-validation';
import { capitalizeArgs, Dash } from '../../model/utils';
import styleGuide from '../../styleGuide';
import { seafarerRecommendationGridColumnDefs } from './seafarer-recommendation-grid-column-defs';
import './seafarer-recommendation.scss';

import Spinner from '../../component/common/Spinner';
import seafarerService from '../../service/seafarer-service';
import { formatDate } from '../../util/view-utils';
import { SUPY } from '../../constants/recommendation';
import { DEFAULT_CURRENCY_UNIT } from '@src/constants/seafarer-wages';
import { toString } from 'lodash';
import vesselService from '@src/service/vessel-service';

const { Icon } = styleGuide;
const { PARIS_ONE_HOST } = process.env;

enum CheckboxOptions {
  Yes = 'yes',
  No = 'no',
  NA = 'n/a',
}

enum ChecklistPayload {
  'yes' = 'YES',
  'no' = 'NO',
  'n/a' = 'NA',
}

const rankConfig = {
  supy: {
    initialValues: {
      expectedSignOnDate: undefined,
      vesselName: '',
      remark: '',
    },
    validationSchema: supyValidationSchema,
  },
  seafarer: {
    initialValues: {
      expectedSignOnDate: undefined,
      vesselName: '',
      wages: '',
      rank: '',
      email: '',
      remark: '',
    },
    validationSchema: seafarerValidationSchema,
  },
};

const errorKeyToDisplayMessageMapping = {
  vesselName: 'Please select a vessel',
  rank: 'Please select seafarer rank',
  expectedSignOnDate: 'Please select an expected sign on date',
  email: 'Please enter correct Email Address (e.g. <EMAIL>)',
  wages: 'Wages must be greater than or equal to 0',
};

function getSeafarerFullName(seafarerPerson: {
  first_name: string;
  last_naame: string;
  middle_name: string;
}) {
  const firstName = seafarerPerson.first_name ?? '';
  const lastName = seafarerPerson.last_naame ?? '';
  const middleName = seafarerPerson.middle_name ?? '';
  return capitalizeArgs(firstName, middleName, lastName) || Dash;
}

const renderErrorMsg = (errors: Object, key: string) => {
  return <div className="error-msg">{errors[key]}</div>;
};

const scrollToForm = (formRef) => {
  const measurement = formRef.current?.getBoundingClientRect();
  const heightOffset = -60; // height of navbar
  const y = measurement.top + window.scrollY + heightOffset;

  return window.scrollTo({ top: y, behavior: 'smooth' });
};

const SeafarerForm = ({
  formRef,
  formikObj,
  vesselList,
  eventTracker,
  rankList,
  checklist,
  setChecklist,
  setError,
  fetchedEmail,
  sailedWithinLastThreeYears,
  seafarerDetailsRef,
  getVesselById,
}) => {
  const { values, errors, setFieldValue, handleBlur, handleChange } = formikObj;
  const [ocimfData, setOcimfData] = useState([]);
  const [isShowGridLoader, setIsShowGridLoader] = useState(false);
  const [isShowZeroAlcoholPolicy, setIsShowZeroAlcoholPolicy] = useState(false);
  const [isShowOCIMFMatrix, setIsShowOCIMFMatrix] = useState(false);
  const [selectedCurrency, setSelectedCurrency] = useState(DEFAULT_CURRENCY_UNIT);
  useEffect(() => {
    if (values.vesselName) {
      const selectedVessel = vesselList.filter((vessel) => vessel.id == values.vesselName);
      if (selectedVessel?.length > 0) {
         const currentVesselOwnership = selectedVessel[0];
         const currencyUnit = (currentVesselOwnership?.vessel?.misc_currency?.value || DEFAULT_CURRENCY_UNIT)?.toUpperCase()
          setSelectedCurrency(currencyUnit);
          eventTracker('currencyUpdate',currencyUnit);
          setFieldValue('wages_unit', currencyUnit.toLowerCase());
      }

      (async function getZeroAlchoholPolicy() {
        const ship_party_id: number | undefined = selectedVessel[0]?.owner?.ship_party_id;
        let resp;
        if (ship_party_id) {
          resp = await seafarerService.getShipPartyById(ship_party_id);
        }
        const is_zero_alcohal_policy: boolean = resp?.ship_party_owner?.is_zero_alcohal_policy ?? false;
        setIsShowZeroAlcoholPolicy(is_zero_alcohal_policy);
      })();

      if (selectedVessel[0]?.vessel_type?.type?.toUpperCase() === 'TANKER') {
        (async () => {
          try {
            setIsShowOCIMFMatrix(true);
            setIsShowGridLoader(true);
            const payload = getOCIMFPayload();
            const { data } = await seafarerService.getOcimfData(
              payload,
              seafarerDetailsRef.seafarer_person.id,
            );

            if (data.response) {
              setError('');
              setOcimfData(data.response);
            } else if (data.error) {
              setOcimfData([]);
              throw new Error(data.message);
            } else {
              setOcimfData([]);
            }
            setIsShowGridLoader(false);
          } catch (error) {
            setIsShowGridLoader(false);
            setError(`Oops, something went wrong. Please try again. ${error}`);
            window.scrollTo({ top: 0, behavior: 'smooth' });
          }
        })();
      } else {
        setError('');
        setOcimfData([]);
        setIsShowOCIMFMatrix(false);
        getVesselById(values.vesselName);
      }
    }
  }, [values.vesselName, values.rank]);

  const getOCIMFPayload = () => {
    const vesselData = getVesselById(values.vesselName);
    const rank = rankList.find((item) => item.id === +values.rank)?.value;
    return {
      rank,
      rank_id: +values.rank,
      vessel_id: vesselData.vesselId,
      vessel_type: vesselData.vesselType,
    };
  };

  const onCheckboxChange = (event: ChangeEvent<HTMLInputElement>, index: number) => {
    const checklistCopy = [...checklist];
    checklistCopy[index].value = event.target.value as CheckboxOptions;
    setChecklist(checklistCopy);
  };
  return (
    <>
      <div className="form-fields-container" ref={formRef}>
        <Row>
          <Col>
            <Form.Label htmlFor="vesselName">Vessel Name*</Form.Label>
            <Form.Control
              data-testid="vesselName"
              name="vesselName"
              as="select"
              value={values.vesselName}
              onChange={handleChange}
              onBlur={handleBlur}
              isInvalid={!!errors.vesselName}
            >
              <option value="" hidden>
                {vesselList.length ? 'Please Select Vessel' : 'Loading ...'}
              </option>
              {vesselList.map((vessel: { id: number; name: string }) => (
                <option key={vessel['id']} value={vessel['id']}>
                  {vessel['name']}
                </option>
              ))}
            </Form.Control>
            {renderErrorMsg(errors, 'vesselName')}
          </Col>
          <Col>
          <Form.Group>
            <Form.Label htmlFor="wages">Wages</Form.Label>
            <Form.Control 
                type="text" 
                hidden 
                id="wages_unit"
                name="wages_unit"
                onChange={handleChange}
                data-testid="wages_unit"
                value={selectedCurrency?.toLowerCase()} />
            <InputGroup>
              <InputGroup.Prepend>
                <InputGroup.Text>{selectedCurrency}</InputGroup.Text>
              </InputGroup.Prepend>
              <Form.Control
                id="wages"
                name="wages"
                className="wages-input"
                data-testid="wages"
                type="number"
                value={values.wages}
                onChange={handleChange}
                onKeyDown={(e: KeyboardEvent) => {
                  if (e.key === 'e' || e.key === '-') {
                    e.preventDefault();
                  }
                }}
                onBlur={handleBlur}
                isInvalid={!!errors.wages}
              ></Form.Control>
             </InputGroup>
            {renderErrorMsg(errors, 'wages')}
            </Form.Group>
          </Col>
        </Row>
        <Row>
          <Col>
            <Form.Label htmlFor="rank">Rank*</Form.Label>
            <Form.Control
              id="rank"
              as="select"
              value={values.rank}
              onChange={handleChange}
              onBlur={handleBlur}
              isInvalid={!!errors.rank}
              disabled={!values.vesselName}
            >
              <option value="" hidden>
                Please Select
              </option>
              {rankList?.map((rank) => (
                <option key={rank.id} value={rank.id}>
                  {rank.value}
                </option>
              ))}
            </Form.Control>
            {renderErrorMsg(errors, 'rank')}
          </Col>
          <Col>
            <Form.Label htmlFor="rank">Expected Sign On Date*</Form.Label>
            <div className="calendar">
              <DatePicker
                selected={values.expectedSignOnDate}
                id="expectedSignOnDate"
                name="expectedSignOnDate"
                dateFormat="d MMM yyyy"
                placeholderText="Please select"
                onBlur={handleBlur}
                onChange={(selectedDate) => {
                  setFieldValue('expectedSignOnDate', selectedDate);
                }}
                minDate={new Date()}
                onKeyDown={(e: KeyboardEvent) => {
                  e.preventDefault();
                }}
                isInvalid={!!errors.rank}
              />
            </div>

            {renderErrorMsg(errors, 'expectedSignOnDate')}
          </Col>
        </Row>
        <Row>
          <Col>
            <Form.Label htmlFor="email">Email*</Form.Label>
            <Form.Control
              name="email"
              data-testid="email"
              value={values.email}
              onChange={handleChange}
              onBlur={handleBlur}
              isInvalid={!!errors.email}
              disabled={fetchedEmail.length}
            ></Form.Control>
            {renderErrorMsg(errors, 'email')}
          </Col>
          <Col>
            <Form.Label htmlFor="remark">Remark</Form.Label>
            <Form.Control
              id="remark"
              as="textarea"
              value={values.remark}
              onChange={handleChange}
              onBlur={handleBlur}
              isInvalid={!!errors.remark}
              maxLength={300}
            ></Form.Control>
            {renderErrorMsg(errors, 'remark')}
          </Col>
        </Row>
      </div>
      {values.vesselName && isShowOCIMFMatrix && (
        <>
          <div className="ocimf-label">OCIMF Matrix</div>
          {ocimfData.length || isShowGridLoader ? (
            <Grid
              columns={seafarerRecommendationGridColumnDefs}
              data={ocimfData}
              isLoading={isShowGridLoader}
              showBottomPagination={false}
              gridStyle={{
                marginTop: '1rem',
              }}
              isManualSort={false}
              showTopPagination={false}
            ></Grid>
          ) : (
            <Row className="no-data-alert-icon">
              <div>
                <Icon icon="alert" size={30} />
                <p>No experience found for the seafarer.</p>
              </div>
            </Row>
          )}
        </>
      )}
      <div className="checklist-label">Check List</div>
      <div className="checklist">
        {checklist.map((checklistItem, index) => {
          return (
            <Row key={checklistItem['checklist_name']}>
              <Col>
                {index + 1}. {checklistItem['checklist_name']}
              </Col>
              <Col>
                {checklist.length - 1 !== index ? (
                  <div className="radio-container">
                    <div className="radio-item">
                      <input
                        data-testid="yesCheckbox"
                        name={`radio_${index}`}
                        className="radio-input"
                        type="radio"
                        value={CheckboxOptions.Yes}
                        checked={CheckboxOptions.Yes === checklistItem['value']}
                        onChange={(e) => {
                          onCheckboxChange(e, index);
                        }}
                      />
                      <span>Yes</span>
                    </div>
                    <div className="radio-item">
                      <input
                        data-testid="noCheckbox"
                        name={`radio_${index}`}
                        className="radio-input"
                        type="radio"
                        value={CheckboxOptions.No}
                        checked={CheckboxOptions.No === checklistItem['value']}
                        onChange={(e) => {
                          onCheckboxChange(e, index);
                        }}
                      />
                      <span>No</span>
                    </div>
                    <div className="radio-item">
                      <input
                        data-testid="naCheckbox"
                        name={`radio_${index}`}
                        className="radio-input"
                        type="radio"
                        value={CheckboxOptions.NA}
                        checked={CheckboxOptions.NA === checklistItem['value']}
                        onChange={(e) => {
                          onCheckboxChange(e, index);
                        }}
                      />
                      <span>N/A</span>
                    </div>
                  </div>
                ) : (
                  <div className="checklist-answer">
                    {sailedWithinLastThreeYears ? (
                      <span className="checklist-answer-yes-span">Yes</span>
                    ) : (
                      <span className="checklist-answer-no-span">No</span>
                    )}
                  </div>
                )}
              </Col>
            </Row>
          );
        })}
        {/* The ZERO alcohol policy is for displaying only, not to block submitting, thus it should not be wrapped in errors */}
        {isShowZeroAlcoholPolicy && (
          <div className="error-msg" data-testid="zeroAlcohol">
            NOTE: Please note that Ship Owner has ZERO ALCOHOL POLICY
          </div>
        )}
      </div>
    </>
  );
};

const SupyForm = ({ formRef, formikObj, vesselList, getVesselById }) => {
  const { values, errors, setFieldValue, handleBlur, handleChange, setFieldTouched } = formikObj;

  useEffect(() => {
    setFieldTouched('remark');
  }, []);

  useEffect(() => {
    (() => {
      if (values.vesselName) {
        getVesselById(values.vesselName);
      }
    })();
  }, [values.vesselName]);

  return (
      <div className="form-fields-container" ref={formRef}>
        <Row>
          <Col>
            <Form.Label htmlFor="vesselName">Vessel Name*</Form.Label>
            <Form.Control
              data-testid="vesselName"
              name="vesselName"
              as="select"
              value={values.vesselName}
              onChange={handleChange}
              onBlur={handleBlur}
              isInvalid={!!errors.vesselName}
            >
              <option value="" hidden>
                {vesselList.length ? 'Please Select Vessel' : 'Loading ...'}
              </option>
              {vesselList.map((vessel) => (
                <option key={vessel['id']} value={vessel['id']}>
                  {vessel['name']}
                </option>
              ))}
            </Form.Control>
            {renderErrorMsg(errors, 'vesselName')}
          </Col>
          <Col>
            <Form.Label htmlFor="remark">Remark</Form.Label>
            <Form.Control
              id="remark"
              as="textarea"
              value={values.remark}
              onChange={handleChange}
              onBlur={handleBlur}
              isInvalid={!!errors.remark}
              maxLength={300}
            ></Form.Control>
            {renderErrorMsg(errors, 'remark')}
          </Col>
        </Row>
        <Row>
          <Col className="col-6">
            <Form.Label htmlFor="rank">Expected Sign On Date*</Form.Label>
            <div className="calendar">
              <DatePicker
                selected={values.expectedSignOnDate}
                id="expectedSignOnDate"
                name="expectedSignOnDate"
                dateFormat="d MMM yyyy"
                placeholderText="Please select"
                onBlur={handleBlur}
                onChange={(selectedDate) => {
                  setFieldValue('expectedSignOnDate', selectedDate);
                }}
                minDate={new Date()}
                onKeyDown={(e: KeyboardEvent) => {
                  e.preventDefault();
                }}
                isInvalid={!!errors.rank}
              />
            </div>

            {renderErrorMsg(errors, 'expectedSignOnDate')}
          </Col>
        </Row>
      </div>
  );
};

const SeafarerRecommendation = ({
  ga4react
}) => {
  const { seafarerId } = useParams();
  const [vesselList, setVesselList] = useState([]);
  const [headingTitle, setHeadingTitle] = useState('');
  const [checklist, setChecklist] = useState([]);
  const [rankList, setRankList] = useState([]);
  const [sailedWithinLastThreeYears, setSailedWithinLastThreeYears] = useState(false);
  const [fetchedEmail, setFetchedEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const seafarerDetailsRef = useRef(null);
  const isSupy = useRef(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const vesselInfoObjRef = useRef({
    vesselName: '',
    vesselType: '',
    vesselTechGroup: '',
    ownershipId: '',
    vesselId: '',
    refId: '',
  });

  const formRef = useRef(null);
  const history = useHistory();
  const ga4EventTrigger = (action, category, label) => {
    try {
      ga4react?.event(action, toString(label), category, false);
    } catch (error) {
      console.log(error);
    }
  };
  const eventTracker = (type: string, value: unknown) => {
    if (type === 'currencyUpdate') {
      ga4EventTrigger('currency-update', value, `Vessel Recommandation Page`);
    }
  };

  useEffect(() => {
    (async () => {
      try {
        setIsLoading(true);
        const { data } = await seafarerService.getRecommendedChecks(seafarerId);
        if (data?.error) {
          history.push(`/seafarer/details/${seafarerId}/general`);
          return;
        }

        const seafarerDetailsPromise = seafarerService.getSeafarer(seafarerId, true);
        const vesselsListPromise = vesselService.getVesselV2Ownerships();

        const [
          { data: seafarerDetails },
          {
            data: { results },
          },
        ] = await Promise.all([seafarerDetailsPromise, vesselsListPromise]);

        seafarerDetailsRef.current = seafarerDetails;
        isSupy.current = seafarerDetails.seafarer_rank.value === SUPY;
        setPageHeading(seafarerDetails);
        setVesselList(results);

        if (!isSupy.current) {
          const rankListPromise = seafarerService.getSeafarerDropDownData('?values=ranks');
          const checklistPromise = seafarerService.getRecommendedCheckList();
          const [
            { ranks },
            {
              data: { response: checklist },
            },
          ] = await Promise.all([rankListPromise, checklistPromise]);
          const ranksWithOutSupy = ranks.filter((rank) => rank.value !== SUPY);
          setRankList(ranksWithOutSupy);
          prepareAndSetChecklist(checklist);
          setSeafarerRelatedUI();
        }
        setIsLoading(false);
      } catch (error) {
        setIsLoading(false);
        history.push(`/seafarer/details/${seafarerId}/general`);
      }
    })();
  }, []);

  const prepareAndSetChecklist = (checklistResponse) => {
    if (!checklistResponse.length) {
      return;
    }
    const checklist = checklistResponse.map((obj) => {
      return { ...obj, value: CheckboxOptions.No };
    });
    checklist.push({
      id: -1,
      checklist_name: 'Sailed with Fleet in last 3 years',
      value: 'no',
    });
    setChecklist(checklist);
  };

  const setSeafarerRelatedUI = () => {
    const seafarerDetails = seafarerDetailsRef.current;
    if (!seafarerDetails) {
      return;
    }
    const seafarerPerson = seafarerDetails.seafarer_person;
    const seafarerRank = seafarerDetails.seafarer_rank;
    const seafarerExpWithFleet = seafarerDetails.seafarer_experience.filter(
      (exp) => exp.vessel_ref_id && !exp.deleted_at,
    );
    setFieldValue('rank', seafarerRank.id);
    const seafarerContact = seafarerPerson.seafarer_contacts.find(
      (contact: { contact_type: string }) => contact.contact_type === 'email',
    );
    if (seafarerContact) {
      setFieldValue('email', seafarerContact.contact);
      setFetchedEmail(seafarerContact.contact);
    }
    setTimeout(() => {
      validateField('email');
      validateField('rank');
    }, 0);
    if (seafarerExpWithFleet.length) {
      const experiences = JSON.parse(JSON.stringify(seafarerExpWithFleet));
      experiences.sort((experience1: { end_date: Date }, experience2: { end_date: Date }) => {
        return new Date(experience1.end_date) < new Date(experience2.end_date) ? 1 : -1;
      });
      const currentDate = new Date();
      const expiryDate = new Date(experiences[0].end_date);
      const diffTime = Math.abs(currentDate.getTime() - expiryDate.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      if (diffDays < 1095) {
        setSailedWithinLastThreeYears(true);
      } else {
        setSailedWithinLastThreeYears(false);
      }
    }
  };

  const breadCrumbsItems = useMemo(
    () => [
      { title: 'Seafarer', label: 'To List Page', link: '/seafarer/passed' },
      {
        title: headingTitle ?? '- - -',
        label: 'Details',
        link: '#',
      },
    ],
    [headingTitle],
  );

  const setPageHeading = (seafarerDetails: { seafarer_person; seafarer_rank; hkid }) => {
    const seafarerPerson = seafarerDetails.seafarer_person;
    const seafarerRank = seafarerDetails.seafarer_rank;
    const fullName = getSeafarerFullName(seafarerPerson);
    const rankTitle = seafarerRank.unit ? `(${seafarerRank.unit})` : Dash;
    setHeadingTitle(`${fullName} ${rankTitle} (${seafarerDetails.hkid}) / Vessel Recommendation`);
  };

  const getVesselById = (vesselName) => {
    const vesselData = vesselList.find((vessel) => vessel.id === +vesselName);
    if (vesselData) {
      vesselInfoObjRef.current = {
        vesselName: vesselData.name,
        vesselType: vesselData.vessel_type.value,
        vesselTechGroup: vesselData.fleet_staff.tech_group,
        refId: vesselData.vessel.ref_id,
        ownershipId: vesselData.id,
        vesselId: vesselData.vessel.id,
      };
    }
    return vesselInfoObjRef.current;
  };

  const createRecommendationPayload = async (formValuesObj) => {
    let payload = {
      vessel_id: vesselInfoObjRef.current.vesselId,
      seafarer_person_id: seafarerDetailsRef.current?.seafarer_person_id,
      expected_contract_start_date: formatDate(formValuesObj.expectedSignOnDate, 'YYYY-MM-DD'),
      seafarer_journey_remarks: formValuesObj.remark,
      rank_id: isSupy.current ? seafarerDetailsRef.current?.rank_id : parseInt(formValuesObj.rank),
    };

    const vesselInfo: any = {
      vessel_name: vesselInfoObjRef.current.vesselName,
      vessel_type: vesselInfoObjRef.current.vesselType,
      vessel_tech_group: vesselInfoObjRef.current.vesselTechGroup,
      vessel_ownership_id: vesselInfoObjRef.current.ownershipId,
      vessel_ref_id: vesselInfoObjRef.current.refId,
    };
    payload = { ...payload, ...vesselInfo };

    if (!isSupy.current) {
      let seafarereSpecificPayload: any = {
        recommended_wages: parseFloat(formValuesObj.wages).toFixed(2),
        recommended_wages_unit: formValuesObj.wages_unit,
        has_travel_with_fleet: sailedWithinLastThreeYears ? 'Yes' : 'No',
        update_email: !fetchedEmail.length,
        email: formValuesObj.email,
      };
      const filteredChecklist = checklist.filter((row) => row.id !== -1);
      seafarereSpecificPayload['checklist'] = filteredChecklist.map((row) => {
        return {
          recommendation_checklist_id: row.id,
          checklist_answer: ChecklistPayload[row.value],
        };
      });
      payload = { ...payload, ...seafarereSpecificPayload };
    }
    return payload;
  };

  const handleSaveClick = async (formValuesObj) => {
    try {
      setSubmitLoading(true);
      const payload = await createRecommendationPayload(formValuesObj);
      const { data } = await seafarerService.createRecommendation(payload);
      if (data.error) {
        setSubmitLoading(false);
        setError(data.message);
        window.scrollTo({ top: 0, behavior: 'smooth' });
      } else {
        history.push(`/seafarer/details/${seafarerId}/general`);
      }
    } catch (error) {
      setSubmitLoading(false);
      setError(`Oops, something went wrong. Please try again. Error: ${error}`);
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  const { initialValues, validationSchema } = isSupy.current
    ? rankConfig.supy
    : rankConfig.seafarer;
  const formikObj = useFormik({
    initialValues,
    validationSchema,
    validateOnBlur: true,
    onSubmit: (valueObj) => {
      handleSaveClick(valueObj);
    },
  });

  const { handleSubmit, setFieldValue, errors, validateField } = formikObj;

  return (
    <>
      {error ? <ErrorAlert message={error} /> : ''}
      {!isLoading ? (
        <form autoComplete="off" onSubmit={handleSubmit} className="seafarer-recommendation">
          <Container>
            <Row>
              <Col className="breadcrumb-container">
                <div>
                  <BreadcrumbHeader items={breadCrumbsItems} activeItem={headingTitle} />
                </div>
                <a
                  target="_blank"
                  href={`${PARIS_ONE_HOST}/fml/PARIS?display=appraisals&appraisaltype=1&crewid=${seafarerDetailsRef.current?.ref_id}`}
                  type="absolute"
                  className="paris1-link-detail-page"
                  rel="noreferrer"
                >
                  View PARIS 1.0 Training Requirements
                </a>
              </Col>
              <Col className="close-btn-container">
                <Icon
                  className="close"
                  icon="close"
                  size={30}
                  onClick={() => {
                    return history.push(`/seafarer/details/${seafarerId}/general`);
                  }}
                ></Icon>
              </Col>
            </Row>
            {Object.keys(errors).length ? (
              <div className="errors-display alert-danger">
                <div className="errors-heading">Click the following links to correct them:</div>
                <ul>
                  {Object.keys(errors).map((errorKey) => (
                    <li key={errorKey}>
                      <Button
                        variant="link"
                        key={errorKey}
                        data-testid="errorKey"
                        onClick={() => {
                          scrollToForm(formRef);
                        }}
                      >
                        {errorKeyToDisplayMessageMapping[errorKey]}
                      </Button>
                  </li>
                  ))}
                </ul>
              </div>
            ) : null}
            <div className="required-fields-label">* Required fields</div>
            {seafarerDetailsRef.current &&
              (isSupy.current ? (
                <SupyForm
                  formRef={formRef}
                  formikObj={formikObj}
                  vesselList={vesselList}
                  getVesselById={getVesselById}
                ></SupyForm>
              ) : (
                <SeafarerForm
                  formRef={formRef}
                  formikObj={formikObj}
                  vesselList={vesselList}
                  rankList={rankList}
                  checklist={checklist}
                  setChecklist={setChecklist}
                  setError={setError}
                  eventTracker={eventTracker}
                  fetchedEmail={fetchedEmail}
                  sailedWithinLastThreeYears={sailedWithinLastThreeYears}
                  seafarerDetailsRef={seafarerDetailsRef.current}
                  getVesselById={getVesselById}
                ></SeafarerForm>
              ))}
          </Container>
          <div className="sticky-footer">
            <Button
              data-testid="submit"
              variant="secondary"
              className="submit-recommend"
              size="sm"
              type="submit"
              disabled={Object.keys(errors).length > 0}
              style={submitLoading ? { pointerEvents: 'none' } : {}}
            >
              {submitLoading ? (
                <Spinner
                  animation="border"
                  size="sm"
                  alignClass="save-recommendation-spinner"
                  aria-live="polite"
                  aria-label="Loading"
                ></Spinner>
              ) : (
                'Submit Recommendation'
              )}
            </Button>
          </div>
        </form>
      ) : (
        <div className="spinner-container">
          <Spinner />
        </div>
      )}
      ;
    </>
  );
};

export default SeafarerRecommendation;
