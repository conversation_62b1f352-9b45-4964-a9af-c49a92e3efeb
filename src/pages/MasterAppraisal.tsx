import React, { useState, useEffect } from 'react';
import { useHistory, useParams } from 'react-router-dom';
import { BreadcrumbHeader } from '../component/BreadcrumpHeader';
import { Container, Row, Col, Spinner, Button, ButtonGroup } from 'react-bootstrap';
import { dateAsString, stringAsDate } from '../model/utils';
import styleGuide from '../styleGuide';
import { logo } from '../test/resources/logo';
const { Icon } = styleGuide;
import * as seafarerService from '../service/seafarer-service';
import { Seafarer } from '../types/seafarerInterfaces';
import { TableBody, PdfFormatTableBody } from '../component/common/TableUtils';
import MasterAppraisalModel from '../model/MasterAppraisalModel';
import { jsPDF } from 'jspdf';
import html2canvas from 'html2canvas';
import MasterAppraisalIndividualGradeTable from '../component/appraisals/MasterAppraisalIndividualGradeTable';
import { getMasterAppraisal } from '../service/seafarer-service';
import AddSeafarerController from '../controller/add-seafarer-controller';
import SpinnerComponent from '../component/common/Spinner';
import { MasterAppraisalGetResponse } from '@src/types/masterAppraisal';

const addSeafarerController = new AddSeafarerController();
const MasterAppraisal = ({ ga4react }: Props) => {
  const history = useHistory();
  const [seafarer, setSeafarer] = useState<Seafarer>();
  const person = seafarer?.seafarer_person;
  const [dropDownData, setDropDownData] = useState(null);
  const [masterDetail, setMasterDetail] = useState<MasterAppraisalGetResponse>();
  const [isLoading, setIsLoading] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const { seafarerId, mstrAppraisalId } = useParams<{
    seafarerId: string;
    mstrAppraisalId: string;
  }>();

  useEffect(() => {
    (async () => {
      setIsLoading(true);
      try {
        const { data } = await seafarerService.getSeafarer(parseInt(seafarerId));
        const dropdowndataResp = await addSeafarerController.loadDropDownData();
        const masterAppraisalResponse = await getMasterAppraisal(parseInt(mstrAppraisalId));
        setSeafarer(data);
        setDropDownData(dropdowndataResp);
        setMasterDetail(masterAppraisalResponse?.data);
      } catch (error) {
        console.log('## Error', error);
      }
      setIsLoading(false);
    })();
  }, [seafarerId, mstrAppraisalId]);

  const ga4EventTrigger = (action: string, category: string, label: string) => {
    try {
      ga4react?.event(action, label.toString(), category);
    } catch (error) {
      console.log(error);
    }
  };

  const eventTracker = (type: string, value: string) => {
    switch (type) {
      case 'exportPDF':
        ga4EventTrigger('Export to PDF', 'Seafarer Master Appraisals - Menu', value);
        break;
      case 'breadCrumb':
        ga4EventTrigger('Breadcrumb', 'Seafarer Master Appraisals - Menu', value);
        break;
      default:
        ga4EventTrigger('Click', 'Crew List Page', value);
        break;
    }
  };

  const handleEvent = (action: string) => {
    const seafarerName = `${person?.first_name} ${person?.last_name}`;
    const seafarerRank = seafarer?.seafarer_rank?.unit;
    eventTracker(action, `${seafarerName} ${seafarerRank}`);
  };

  const breadCrumbsItems = () => {
    return [
      { title: 'Seafarer', label: 'To List Page', link: '/seafarer/passed' },
      {
        title: person
          ? `${person?.first_name} ${person?.last_name} (${seafarer?.seafarer_rank?.unit}) (${seafarer?.hkid})`
          : '- - -',
        label: 'Details',
        link: `/seafarer/details/${seafarerId}/general`,
      },
      {
        title: 'Appraisals',
        label: 'Appraisals',
        link: `/seafarer/details/${seafarerId}/appraisals`,
      },
      {
        title: 'Master-Appraisals',
        label: 'Master-Appraisals',
        link: '#',
      },
    ];
  };

  const handleClose = () => {
    history.push(`/seafarer/details/${seafarerId}/appraisals`);
  };

  const pdfContent = () => {
    let today = new Date().toLocaleString();

    return (
      <>
        {masterDetail && (
          <div className="pdf-content-only">
            <div id="pdf-content-part-1">
              <Row className="closeIcon mt-2 mb-2">
                <div className="pdf-content-navTitle">
                  {`Master Appraisals of ${person?.first_name} ${person?.last_name} (${seafarer?.seafarer_rank?.unit}) (${seafarer?.hkid})`}
                </div>
                <div>
                  <Row className="d-flex justify-space-between">
                    <div className="col-1 mb-1 justify-content-end">
                      <img src={logo} alt="icon" />
                    </div>
                  </Row>
                </div>
              </Row>
              <hr className="section_line mb-0" />
              <Row className="justify-content-center">
                <Col>
                  <PdfFormatTableBody
                    data={MasterAppraisalModel(masterDetail, dropDownData).primaryLeftPdfVersion}
                  />
                </Col>
                <Col>
                  <PdfFormatTableBody
                    data={MasterAppraisalModel(masterDetail, dropDownData).primaryRightPdfVersion}
                  />
                </Col>
              </Row>
              <hr className="section_line mb-0" />
              <Row className="justify-content-center">
                <Col>
                  <PdfFormatTableBody
                    data={MasterAppraisalModel(masterDetail, dropDownData).secondaryLeft}
                  />
                </Col>
                <Col>
                  <PdfFormatTableBody
                    data={MasterAppraisalModel(masterDetail, dropDownData).secondaryRight}
                  />
                </Col>
              </Row>
              <Row className="d-flex justify-space-between">
                <div className="col-9">
                  <hr className="mb-0" />
                  <MasterAppraisalIndividualGradeTable
                    data={masterDetail}
                    tableClass="pdf-table-font-size"
                    tableDataClass="p-1"
                    tableHeadClass="p-1"
                  />
                </div>
                <div className="col-3">
                  <hr className="section_line mb-0" />
                  <PdfFormatTableBody
                    data={MasterAppraisalModel(masterDetail, dropDownData).gradingTable}
                  />
                  <hr className="section_line mt-0" />
                </div>
              </Row>
              <div className="master-appraisal-pdf-spacing" />
              <Row>
                <Col>
                  <hr className="section_line mt-0" />
                  <div className="pdf-crew-list-heading font-weight-bold">Seaman</div>
                  <div className="signature"></div>
                </Col>
                <Col>
                  <hr className="section_line mt-0" />
                  <div className="pdf-crew-list-heading font-weight-bold">Master</div>
                  <div className="signature"></div>
                </Col>
              </Row>
            </div>
            <div id="pdf-content-part-2">
              <div className="pdf-content-footer">© Fleet Management Ltd. All rights reserved.</div>
              <div className="pdf-content-footer">{`Master Appraisals of
          ${person?.first_name} ${person?.last_name}
          (${seafarer?.seafarer_rank?.unit}) (${seafarer?.hkid}), page generated on ${dateAsString(
                stringAsDate(today),
              )}`}</div>
            </div>
          </div>
        )}
      </>
    );
  };

  const exportPDF = () => {
    handleEvent('exportPDF');
    if (isLoading) {
      return;
    }

    if (isExporting) {
      return;
    }

    setIsExporting(true);
    let pdf = new jsPDF('p', 'mm', 'a4', true);
    const marginForPDF = 10;
    const width = pdf.internal.pageSize.getWidth() - 2 * marginForPDF;
    const pageCount = pdf.internal.getNumberOfPages();
    for (let i = 0; i < pageCount; i++) {
      pdf.setPage(i);
      pdf.setFontSize(9);
      pdf.setTextColor('#1f4a70');
      pdf.text(`${i + 1}` + '/' + pageCount, 195, pdf.internal.pageSize.height - 7);
    }

    // Top part
    const pdfElement = document.getElementById('pdf-content-part-1');
    if (pdfElement) {
      pdfElement.style.display = 'block';
      html2canvas(pdfElement, { scale: 3 }).then((canvas) => {
        const imgData = canvas.toDataURL('image/png', 1);
        let height = pdfElement?.clientHeight / 6;
        const pageCount = Math.ceil(height / pdf.internal.pageSize.getHeight());

        pdf.addImage(imgData, 'PNG', marginForPDF, marginForPDF, width, height);
        pdfElement.style.display = 'none';

        if (pageCount > 1) {
          for (let i = 2; i <= pageCount; i++) {
            pdf.addPage();
            pdf.addImage(
              imgData,
              'PNG',
              marginForPDF,
              -pdf.internal.pageSize.getHeight() * (i - 1) + marginForPDF,
              width,
              height,
            );
          }
        }

        // Footer part
        const pdfElementFooter = document.getElementById('pdf-content-part-2');
        if (pdfElementFooter) {
          pdfElementFooter.style.display = 'block';
          html2canvas(pdfElementFooter, { scale: 3 }).then((canvas) => {
            const imgDataFooter = canvas.toDataURL('image/png', 1);
            let height = pdfElementFooter?.clientHeight / 6;

            pdf.addImage(
              imgDataFooter,
              'PNG',
              marginForPDF,
              pdf.internal.pageSize.getHeight() - height - marginForPDF,
              width,
              height,
            );
            pdf.save('download.pdf');
            pdfElementFooter.style.display = 'none';
            setIsExporting(false);
          });
        }
      });
    }
  };

  const mobileNavigationBar = () => {
    return (
      <div className="mobile">
        <Row className="d-flex justify-space-between">
          <div className="col-10 justify-content-start">
            <BreadcrumbHeader
              items={breadCrumbsItems()}
              activeItem={'Master-Appraisals'}
              onClick={() => handleEvent('breadCrumb')}
            />
          </div>
          <div className="col-2 pl-0 text-right">
            <Icon icon="close" size={25} onClick={handleClose} />
          </div>
        </Row>
        <Row className="d-flex justify-content-end pr-3">
          <ButtonGroup>
            <Button variant="outline-primary" size="sm" onClick={exportPDF} disabled={isExporting}>
              {isExporting && <Spinner className="mr-2" animation="border" size="sm" />}
              Export To PDF
            </Button>
          </ButtonGroup>
        </Row>
      </div>
    );
  };

  const desktopNavigationBar = () => {
    return (
      <div className="desktop">
        <Row className="closeIcon">
          <Col className="pl-0">
            <BreadcrumbHeader
              items={breadCrumbsItems()}
              activeItem={'Master-Appraisals'}
              onClick={() => handleEvent('breadCrumb')}
            />
          </Col>

          <Col style={{ maxWidth: 'fit-content' }}>
            <Row className="d-flex justify-space-between">
              <div className="col-10 mb-1 justify-content-start">
                <ButtonGroup className="mr-2 desktop">
                  <Button
                    variant="outline-primary export-pdf-btn"
                    onClick={exportPDF}
                    disabled={isExporting}
                  >
                    {isExporting && <Spinner className="mr-2" animation="border" size="sm" />}
                    Export To PDF
                  </Button>
                </ButtonGroup>
              </div>
              <div className="col-1 mb-1 justify-content-end">
                <Icon icon="close" size={25} onClick={handleClose} />
              </div>
            </Row>
          </Col>
        </Row>
      </div>
    );
  };

  return (
    <>
      {isLoading || !dropDownData || !masterDetail ? (
        <div className="spinner-container">
          <SpinnerComponent />
        </div>
      ) : (
        <Container>
          {mobileNavigationBar()}
          {desktopNavigationBar()}
          <hr className="section_line mb-0" />
          <Row className="justify-content-center">
            <Col lg={6} md={6} sm={12} xs={12}>
              <TableBody
                data={MasterAppraisalModel(masterDetail, dropDownData).primaryLeft}
                labelClass="col-4"
                valueClass="col-8"
              />
            </Col>
            <Col lg={6} md={6} sm={12} xs={12}>
              <TableBody
                data={MasterAppraisalModel(masterDetail, dropDownData).primaryRight}
                labelClass="col-4"
                valueClass="col-8"
              />
            </Col>
          </Row>
          <hr className="section_line mb-0" />
          <Row className="justify-content-center">
            <Col lg={6} md={6} sm={12} xs={12}>
              <TableBody
                data={MasterAppraisalModel(masterDetail, dropDownData).secondaryLeft}
                labelClass="col-4"
                valueClass="col-8"
              />
            </Col>
            <Col lg={6} md={6} sm={12} xs={12}>
              <TableBody
                data={MasterAppraisalModel(masterDetail, dropDownData).secondaryRight}
                labelClass="col-4"
                valueClass="col-8"
              />
            </Col>
          </Row>
          <MasterAppraisalIndividualGradeTable data={masterDetail} />
          {pdfContent()}
        </Container>
      )}
    </>
  );
};

export default MasterAppraisal;
