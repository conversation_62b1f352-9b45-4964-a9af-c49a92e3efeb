import StandardModal from '@src/component/common/Modal';
import useRequest from '@src/hooks/useRequest';
import keycloakService from '@src/service/keycloak-service';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Alert, Form } from 'react-bootstrap';
import { Hint, Typeahead } from 'react-bootstrap-typeahead';
import { Option } from 'react-bootstrap-typeahead/types/types';
import './style.scss';
import { useFormik } from 'formik';
import useLazyRequest from '@src/hooks/useLazyRequest';
import {
  ManageRankGroupArgs,
  MangeRankGroupResponse,
  updateManageRankGroup,
} from '@src/service/crew-planner';
import { useNotification } from '@src/component/common/Notification/useNotification';
import SearchInput from '@src/component/common/Form/SearchInput';
import PlannerForm, { validationPlannerSchema } from './PlannerForm';
import { managedRanks } from '../../constant';
import UserCard from './UserCard';
import { type User } from '../../types';
import { sortAndFilterList } from '../../utils';

const PlannerModal = ({ user, children, onUpdate, visible: show, onVisible: onShow }) => {
  const isEdit = !!user;
  const [selected, setSelected] = useState<User[] | null>(null);
  const selectedUserId = user?.keycloak_user_id || selected?.[0]?.id;
  const selectedUser = user || selected?.[0];
  const [visible, setVisible] = useState(show || false);
  const { notify } = useNotification();
  const handleClick = useCallback(() => {
    setVisible(!visible);
  }, [visible, setVisible]);
  const ChilrenComponent = children && React.cloneElement(children, { onClick: handleClick });
  const { data, loading: dataLoading } = useRequest<User[], any>(keycloakService.getUsers, {
    initialArgs: [{ parentGroup: 'Fleet Personnel' }],
  });
  const userList = useMemo(() => {
    return sortAndFilterList(data ?? []).filter((item) => !!item.email);
  }, [data]);

  const [updateCrewPlannerUserGroup, { loading: isSubmitting }] = useLazyRequest<
    MangeRankGroupResponse,
    [ManageRankGroupArgs]
  >(updateManageRankGroup, {
    onComplete(response) {
      notify.success(response?.message as string);
      cleanUpStates();
      onUpdate?.();
    },
  });
  const cleanUpStates = () => {
    setVisible(false);
    setSelected(null);
    formik.resetForm({});
  };
  useEffect(() => {
    onShow?.(visible);
    if (!visible) {
      cleanUpStates();
    }
  }, [visible]);
  const renderMenuItem = (option: Option) => <UserCard user={option} />;
  const filterByCallback = (option: Option, props) => {
    const searchText = props?.text?.toLowerCase();
    return (
      option?.full_name?.toLowerCase()?.indexOf(searchText) !== -1 ||
      option?.email?.toLowerCase()?.indexOf(searchText) !== -1
    );
  };
  const getLabelKey = (option: Option) => `${option.full_name} (${option.email})`;
  const formik = useFormik({
    initialValues: {
      grant_access: true,
      vessel_type: user?.keycloak_vessel_role || null,
      managed_ranks:
        user?.group_name?.length > 0
          ? managedRanks
              .filter((item) => user?.group_name?.includes(item.label))
              .map((item) => item.value)
          : [],
    },
    validationSchema: validationPlannerSchema,
    validateOnChange: true,
    onSubmit: (values) => {
      updateCrewPlannerUserGroup({
        user_id: selectedUserId,
        user_name: selectedUser?.username,
        user_role: values.vessel_type,
        rankgroup_id: values.managed_ranks,
      });
    },
  });

  const canSubmit = selectedUserId && formik.isValid && formik.dirty;

  const renderWarning = () => {
    if (!selectedUserId || formik.values.grant_access) return null;
    return (
      <Alert variant="danger">
        <b>The user will not have any access to the plan a Seafarer.</b> Enable the access to the Crew Planner Module to Continue.
      </Alert>
    );
  };
  const renderForm = () => {
    return (
      <Form.Group>
        <Form.Label>
          <b>Select User</b>
        </Form.Label>
        <Typeahead
          options={userList ?? []}
          isLoading={dataLoading}
          onChange={setSelected}
          placeholder="Add Crew Planner by Name or Email"
          selected={selected ?? []}
          renderMenuItemChildren={renderMenuItem}
          labelKey={getLabelKey}
          filterBy={filterByCallback}
          clearButton
          renderInput={({ inputRef, referenceElementRef, ...inputProps }) => (
            <Hint>
              <SearchInput
                {...inputProps}
                ref={(node) => {
                  inputRef(node);
                  referenceElementRef(node);
                }}
              />
            </Hint>
          )}
        />
      </Form.Group>
    );
  };
  const renderUserCard = () => {
    if (!user) return null;
    return <UserCard user={user} />;
  };
  return (
    <>
      {ChilrenComponent}
      <StandardModal
        title={isEdit ? 'Manage Access' : 'Add Planner'}
        okText={isEdit ? 'Save' : 'Add'}
        visible={visible}
        closeButton
        onVisibleChange={handleClick}
        size="lg"
        okButtonProps={{
          disabled: !canSubmit,
        }}
        onOk={formik.handleSubmit}
        confirmLoading={isSubmitting}
      >
        {renderWarning()}
        {renderUserCard()}
        {!isEdit && renderForm()}
        {selectedUserId && <PlannerForm formik={formik} />}
      </StandardModal>
    </>
  );
};

export default PlannerModal;
