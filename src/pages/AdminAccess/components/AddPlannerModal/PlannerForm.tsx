import React, { ReactNode } from 'react';
import { Col, Form, Row } from 'react-bootstrap';
import * as Yup from 'yup';
import Switch from '@src/component/common/Form/Switch';
import Checkbox from '@src/component/common/Form/Checkbox';
import { managedRanks, vesselTypes } from '../../constant';

interface FormFieldProps {
  label: ReactNode;
  children: ReactNode;
  labelProps?: Object | null;
}

const FormField = ({ label, children, labelProps = {} }: FormFieldProps) => {
  const labelContent = typeof label === 'string' ? <b>{label}</b> : label;
  return (
    <Form.Group as={Row} className="align-items-center">
      <Form.Label column {...labelProps}>
        {labelContent}
      </Form.Label>
      <Col className="text-right">{children}</Col>
    </Form.Group>
  );
};

export const validationPlannerSchema = Yup.object().shape({
  grant_access: Yup.boolean()
    .oneOf([true], 'Access permission must be granted')
    .required('Access permission is required'),
  vessel_type: Yup.string().required('Vessel type is required'),
  managed_ranks: Yup.array()
    .of(Yup.string())
    .min(1, 'At least one managed rank is required')
    .required('Managed ranks are required'),
});

const PlannerForm = ({ formik }) => {
  const { handleChange, values } = formik;
  const canSubmit = formik.values?.grant_access;

  return (
    <>
      <hr className="section_line lg" />
      <FormField
        label={
          <>
            <h5 className="text-primary font-weight-bold form-label-large">
              Manage Crew Planner Access
            </h5>
            <div>Grant access to view and plan seafarer for Vessel Category and Ranks</div>
          </>
        }
        labelProps={{ sm: '10' }}
      >
        <Switch
          name="grant_access"
          onChange={handleChange}
          checked={values.grant_access}
          id="default-grant"
        />
      </FormField>
      <FormField label="Select Vessel Category">
        <Row className="text-left">
          {vesselTypes.map((item) => (
            <Col key={item.label} sm={4} className="p-0">
              <Checkbox
                inline
                label={item.label}
                name="vessel_type"
                disabled={!canSubmit}
                value={item.value}
                checked={formik.values.vessel_type === item.value}
                onChange={(e) => {
                  if (e.target.checked) {
                    formik.setFieldValue('vessel_type', item.value);
                  } else {
                    formik.setFieldValue('vessel_type', null);
                  }
                }}
              />
            </Col>
          ))}
        </Row>
      </FormField>
      <FormField label="Select Managed Ranks">
        <Row className="text-left">
          {managedRanks.map((item) => (
            <Col key={item.label} className="p-0" sm={4}>
              <Checkbox
                inline
                label={item?.label}
                disabled={!canSubmit}
                name="managed_ranks"
                value={item.value}
                checked={formik.values.managed_ranks.includes(item.value)}
                onChange={(e) => {
                  if (e.target.checked) {
                    formik.setFieldValue('managed_ranks', [
                      ...formik.values.managed_ranks,
                      item.value,
                    ]);
                  } else {
                    formik.setFieldValue(
                      'managed_ranks',
                      formik.values.managed_ranks.filter((v) => v !== item.value),
                    );
                  }
                }}
              />
            </Col>
          ))}
        </Row>
      </FormField>
    </>
  );
};

export default PlannerForm;
