import Avatar from '@src/component/common/Avatar';
import React from 'react';
import cx from 'classnames';

const UserCard = ({ clasName, user, size = 'sm' }) => {
  const { first_name, last_name, email } = user;
  const fullName = [first_name, last_name].filter(Boolean).join(' ')?.trim()?.toLowerCase();
  return (
    <div className={cx('d-flex align-items-center', clasName)}>
      <Avatar
        className={cx({ 'avatar-sm': size === 'sm' })}
        firstName={first_name}
        lastName={last_name}
      />
      <div className="ml-2">
        <div className="font-weight-bold text-capitalize">{fullName}</div>
        <div>{email}</div>
      </div>
    </div>
  );
};

export default UserCard;
