import StandardModal from '@src/component/common/Modal';
import useLazyRequest from '@src/hooks/useLazyRequest';
import React, { useCallback } from 'react';
import { Alert } from 'react-bootstrap';
import './style.scss';
import { useNotification } from '@src/component/common/Notification/useNotification';
import {
  ManageRankGroupArgs,
  MangeRankGroupResponse,
  updateManageRankGroup,
} from '@src/service/crew-planner';
import UserCard from '../AddPlannerModal/UserCard';

const DeletePlanner = ({ user, visible, onVisible, onDelete }) => {
  const { notify } = useNotification();
  const handleClick = useCallback(() => {
    onVisible(!visible);
  }, [visible, onVisible]);
  const [updateCrewPlannerUserGroup, { loading: isSubmitting }] = useLazyRequest<
    MangeRankGroupResponse,
    [ManageRankGroupArgs]
  >(updateManageRankGroup, {
    onComplete(response) {
      notify.success(response?.message as string);
      onVisible(false);
      onDelete?.();
    },
  });

  const handleDeletePlanner = () => {
    updateCrewPlannerUserGroup({
      user_id: user?.keycloak_user_id,
      user_name: user?.username,
      user_role: user.keycloak_vessel_role,
      is_delete_user: true,
      rankgroup_id: [],
    });
  };
  const renderMessage = () => {
    return (
      <Alert variant="danger">
        <b>
          The Planner will be deleted permanently, the user will not have any acess to the plan a Seafarer.
        </b>{' '}
        Are you sure you want to continue?
      </Alert>
    );
  };
  return (
    <StandardModal
      title="Deleting Planner"
      okText="Confirm"
      visible={visible}
      onVisibleChange={handleClick}
      size="lg"
      onOk={handleDeletePlanner}
      confirmLoading={isSubmitting}
    >
      {renderMessage()}
      <UserCard clasName="planner-user-card" size="lg" user={user} />
    </StandardModal>
  );
};

export default DeletePlanner;
