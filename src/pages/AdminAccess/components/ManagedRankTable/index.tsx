/* eslint-disable react/display-name */
/* eslint-disable react/prop-types */
import React, { useState } from 'react';
import Paris2Table from '@src/component/common/Paris2Table';
import { Icon } from '@src/styleGuide';
import { OverlayTrigger, Popover } from 'react-bootstrap';
import { getColumns } from './columns';
import DeletePlanner from '../DeletePlannerModal';
import PlannerModal from '../AddPlannerModal';
import './style.scss';

interface Props {
  row: any;
  onDelete: (id: number, name: string) => void;
  roles: string[];
}

const PopoverAction = ({ row, onEdit, onDelete, roles }: Props) => {
  const { id } = row;
  return (
    <div
      style={{
        textAlign: 'center',
      }}
      onClick={(e) => {
        e.stopPropagation();
      }}
      aria-hidden="true"
    >
      <OverlayTrigger
        rootClose
        trigger="click"
        key="bottom"
        placement="bottom-end"
        overlay={(
          <Popover id="action" className="table-popover-action">
            <Popover.Content className="text-primary">
              <ul className="List__PopoverMenu">
                <li
                  data-testid={`${id}-edit-button-link`}
                  onClick={() => onEdit?.(row)}
                  onKeyDown={() => onEdit?.(row)}
                  style={{
                    marginRight: 0,
                    marginTop: 0,
                    padding: 0,
                  }}
                >
                  Manage Access
                </li>
                <li onClick={() => onDelete?.(row)} onKeyDown={() => onDelete?.(row)}>
                  Delete
                </li>
              </ul>
            </Popover.Content>
          </Popover>
        )}
      >
        <div data-testid={`${id}-action-overlay`}>
          <Icon icon="more" size={20} className="default" style={{ cursor: 'pointer' }} />
        </div>
      </OverlayTrigger>
    </div>
  );
};

const getActionListColumn = ({ onDelete, onEdit }: { onDelete: (user: unknown) => void, onEdit: (user: unknown) => void }) => ({
  Header: 'Actions',
  align: 'center',
  id: 'actions',
  accessor: (row, index) => (
    <PopoverAction
      row={row}
      onDelete={onDelete}
      onEdit={onEdit}
    />
  ),
  disableSortBy: true,
  maxWidth: 10,
  minWidth: 80,
});

const ManageGroupTable = React.memo(({ loading, data, onUpdate }) => {
  const [deletedUser, setDeletedUser] = useState(null);
  const [selectedUser, setSelectedUser] = useState(null);
  const ListActionColumn = getActionListColumn({
    onDelete: setDeletedUser,
    onEdit: setSelectedUser,
  });
  const generatedColumns = [...getColumns(), ListActionColumn];
  return (
    <>
      <Paris2Table
        columns={generatedColumns}
        data={data}
        loading={loading}
        totalCount={data.length}
        pagination={false}
        autoSort
      />
      {deletedUser && (
        <DeletePlanner
          user={deletedUser}
          visible={!!deletedUser}
          onDelete={onUpdate}
          onVisible={() => setDeletedUser(null)}
        />
      )}
      {selectedUser && (
        <PlannerModal
          user={selectedUser}
          visible={!!selectedUser}
          onUpdate={onUpdate}
          onVisible={(visible) => {
            if (!visible) {
              setSelectedUser(null);
            }
          }}
        />
      )}
    </>
  );
});

export default ManageGroupTable;
