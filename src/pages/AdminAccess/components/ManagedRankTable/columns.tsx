/* eslint-disable import/prefer-default-export */
import React from 'react';
import { vesselTypes } from '../../constant';
import UserCard from '../AddPlannerModal/UserCard';
import { sortUser } from '../../utils';

export const getColumns = () => {
  return [
    {
      Header: 'User',
      id: 'id',
      type: 'item',
      accessor: (row) => {
        return <UserCard user={row} />;
      },
      sortType: (a, b) => sortUser(a, b),
      maxWidth: 360,
      minWidth: 260,
      disableSortBy: false,
    },
    {
      type: 'text',
      Header: 'Vessel Category',
      id: 'vessel_type',
      name: 'vessel_type',
      accessor: (row) => {
        const vesselType = vesselTypes.find((item) => item.value === row?.keycloak_vessel_role);
        return vesselType?.label ?? '---';
      },
      order: 2,
      disableSortBy: true,
      maxWidth: 100,
    },
    {
      type: 'text',
      Header: 'Managed Ranks',
      id: 'manage_ranks',
      name: 'manage_ranks',
      accessor: (row) => {
        return row.group_name?.join(', ');
      },
      order: 3,
      disableSortBy: true,
      maxWidth: 100,
    },
  ];
};
