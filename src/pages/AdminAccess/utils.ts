import { User } from './types';

export function sortUser(a: User, b: User) {
  const firstNameA = a.first_name?.toLowerCase();
  const firstNameB = b.first_name?.toLowerCase();
  const lastNameA = a.last_name?.toLowerCase();
  const lastNameB = b.last_name?.toLowerCase();

  if (firstNameA < firstNameB) return -1;
  if (firstNameA > firstNameB) return 1;
  if (lastNameA < lastNameB) return -1;
  if (lastNameA > lastNameB) return 1;
  return 0;
}

export const sortAndFilterList = (dataList: User[], keyword: string = '') => {
  let listToSort = [...dataList];

  if (keyword) {
    const lowerCaseKeyword = keyword?.toLowerCase();
    listToSort = listToSort.filter(
      (person) =>
        person.first_name?.toLowerCase().includes(lowerCaseKeyword) ||
        person.last_name?.toLowerCase().includes(lowerCaseKeyword),
    );
  }

  return listToSort.toSorted(sortUser);
};
