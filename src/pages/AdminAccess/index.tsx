import AccessHandlerWrapper from '@src/component/common/AccessHandlerWrapper';
import React, { useEffect, useMemo, useState } from 'react';
import { Button, Container } from 'react-bootstrap';
import ManageGroupTable from './components/ManagedRankTable';
import SearchInput from '@src/component/common/Form/SearchInput';
import PlannerModal from './components/AddPlannerModal';
import useRequest from '@src/hooks/useRequest';
import { getPlannerList } from '@src/service/crew-planner';
import { sortAndFilterList } from './utils';
import NotificationProvider from '@src/component/common/Notification';
import './style.scss';
import { useAccess } from '@src/component/common/Access';

const AdminAccess = () => {
  const { roleConfig } = useAccess();
  const hasRoleAccess = roleConfig?.seafarer?.manage?.crewPlanner;
  const { data, loading, refetch } = useRequest(getPlannerList);
  const [searchKeyword, setSearchKeyword] = useState('');
  const userList = useMemo(() => {
    return sortAndFilterList(data ?? [], searchKeyword);
  }, [data, searchKeyword]);

  const handleSearchChange = (e) => {
    setSearchKeyword(e.target.value);
  };
  useEffect(() => {
    document.documentElement.style.fontSize = '14px';

    return () => {
      document.documentElement.style.fontSize = '';
    };
  }, []);
  return (
    <AccessHandlerWrapper hasRoleAccess={hasRoleAccess}>
      <Container className="manage-crew-planner-container">
        <NotificationProvider>
          <h5 className="text-primary page-header">Manage Crew Planner</h5>
          <div className="d-flex justify-content-between my-4">
            <div style={{ width: 320 }}>
              <SearchInput
                name="keyword"
                placeholder="Search by User Name"
                onChange={handleSearchChange}
              />
            </div>
            <PlannerModal onUpdate={() => refetch()}>
              <Button>Add Planner</Button>
            </PlannerModal>
          </div>
          <ManageGroupTable data={userList} loading={loading} onUpdate={() => refetch()} />
        </NotificationProvider>
      </Container>
    </AccessHandlerWrapper>
  );
};

export default AdminAccess;
