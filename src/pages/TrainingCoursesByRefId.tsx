import React, { useEffect } from 'react';
import { useHistory, useParams } from 'react-router-dom';
import Spinner from '../component/common/Spinner';
import seafarerService from '../service/seafarer-service';
import { ErrorPage } from '../styleGuide';
const PAGE_STATUS = {
  LOADING: 'loading',
  ERROR: 'error',
  NOT_FOUND: 'not_found',
  NOT_LOADING: 'not_loading',
  // eslint-disable-next-line no-undef
} as const;
const TrainingCoursesByRefId = () => {
  const [status, setStatus] = React.useState<string>(PAGE_STATUS.NOT_LOADING);
  const { seafarerRefId } = useParams() as { seafarerRefId: number | undefined };
  const history = useHistory();
  useEffect(() => {
    (async () => {
      try {
        if (seafarerRefId) {
          setStatus(PAGE_STATUS.LOADING);
          const results = await seafarerService.getSeafarers(
            '',
            `ref_id=${seafarerRefId}&f=id`,
            false,
          );
          if (results?.data?.results?.length) {
            history.push(`/seafarer/details/${results.data.results[0].id}/training-courses`);
          } else {
            setStatus(PAGE_STATUS.NOT_FOUND);
          }
        }
      } catch (error) {
        setStatus(PAGE_STATUS.ERROR);
      }
    })();
  }, [seafarerRefId]);
  if (status === PAGE_STATUS.ERROR || status === PAGE_STATUS.NOT_FOUND) {
    return <ErrorPage errorCode={404} />;
  } else if (status === PAGE_STATUS.LOADING) {
    return (
      <div className="spinner-container">
        <Spinner />
      </div>
    );
  } else {
    return <></>;
  }
};
export default TrainingCoursesByRefId;
