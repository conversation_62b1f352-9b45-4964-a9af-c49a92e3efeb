/* eslint-disable react/function-component-definition */
import React, { useState, useEffect, useRef, useCallback, createContext, useMemo } from 'react';
import _, { toString } from 'lodash';
import {
  Container,
  Col,
  Button,
  Row,
  ButtonGroup,
  Dropdown,
  Tab,
  Form,
  Modal,
} from 'react-bootstrap';
import { useHistory, useParams, useLocation } from 'react-router-dom';
import { useDebounce, useDebouncedCallback } from 'use-debounce';
import moment from 'moment';
import { useAccess } from '@src/component/common/Access';
import { DEFAULT_PAGE_SIZE } from '@src/component/InfiniteScrollTable';
import {
  mapQueryStringToSearchCriteria,
  addSortOrPaginateParams,
  generateQuickSearch,
} from '../util/advance-search/search-query';
import seafarerService from '../service/seafarer-service';
import ScrollArrow from '../component/BackToTopButton';
import SearchParametersView from '../component/SearchParametersView';
import SeafarerTable from '../component/seafarerList/SeafarerTable';
import items, { getDefaultScreeningColumns } from '../component/seafarerList/MenuList';
import {
  approvalColumns,
  APPROVAL_GROUPS,
  SCREENING_COLUMNS,
  shipPartyType,
  LOCAL_STORAGE_FIELDS,
  SCREENING_STATUS,
} from '../model/constants';
import AddSeafarerController from '../controller/add-seafarer-controller';
import { goToParis1, goToParis1SeafarerRecommend } from '../util/paris-link';
import AccessHandlerWrapper from '../component/common/AccessHandlerWrapper';
import ContractExpiryTable from '../component/seafarerList/ContractExpiryTable';
import ToReplaceSeafarer from '../component/seafarerList/ToReplaceSeafarer';
import {
  retrieveColumns,
  storeColumns,
  isKeyStored,
  getPageSort,
  resetAllTabs,
  storePageNumber,
  getStoredColumnHeaders,
} from '../util/local-storage-helper';
import httpService from '../service/http-service';
import AdvancedSearch from '../component/advanced_search/AdvancedSearch';
import AdvancedSearchMobile from '../component/advanced_search/AdvancedSearchMobile';
import SearchController from '../controller/search-controller';
import ButtonsBar from '../component/seafarerList/ButtonsBar';
import TabWrapper from '../component/common/TabWrapper';
import { ListTabData } from '../model/TabData';
import { contractExpiryColumns, availableSeafarerColumn } from '../component/CrewList/MenuList';
import AvailableSeafarerTable from '../component/AvailableSeafarerList/AvailableSeafarerTable';
import defaultFilters, {
  INITIAL_LOAD_FILTERS,
} from '../util/advance-search/advance-search-default-filter';
import { DEFAULT_COLUMNS_CONTRACT_EXPIRY } from '../util/default-columns';
import { exportTableToExcel } from '../util/excel-export';
import {
  approvalGroupKey,
  commonApprovalGroupKey,
  pendingApprovalGroupKey,
} from '../util/advance-search/search-types';

export const TabContext = createContext({});
const DEFAULT_TOP_MARGIN = 150;
const EVERY_ITEM = 60;

const List = ({ ga4react }) => {
  const { current: controller } = useRef(new AddSeafarerController());
  const history = useHistory();
  const { roleConfig } = useAccess();
  const [loading, setLoading] = useState(false);

  const { tab, seafarerId } = useParams();
  const [signOffLoading, setSignOffLoading] = useState(!!seafarerId);
  const [activeKey, setActiveKey] = useState(tab);
  const [selectedColumns, setSelectedColumns] = useState([]);
  const [initSort, setInitSort] = useState(getPageSort(activeKey));
  const query = useLocation().search?.substring(1);
  const [apiQuery, setApiQuery] = useState('');
  const criteria = mapQueryStringToSearchCriteria(query);
  const [signOffRecords, setSignOffRecords] = useState([]);
  const [showAdvSearchMobile, setShowAdvSearchMobile] = useState(false);

  const [filteredSeafarers, setFilteredSeafarers] = useState([]);

  const hasNoSearchCriteria = criteria.length === 0;
  const [searchedKeyword, setSearchedKeyword] = useState('');
  const [filters, setFilters] = useState([]);
  const [debouncedFilters] = useDebounce(filters, 700);

  const [showModal, setShowModal] = useState(false);
  const [showAdvSearch, setShowAdvSearch] = useState(false);
  const selectedRefId = useRef(null);
  const tableRef = useRef(null);
  const signOffTableRef = useRef(null);
  const searchInputRef = useRef(null);
  const [isPageViewInvoked, setIsPageViewInvoked] = useState(false);
  const [isLongServiceExcelExporting, setIsLongServiceExcelExporting] = useState(false);
  const [isLongServiceFilterSelected, setIsLongServiceFilterSelected] = useState(true);
  const [selectedRows, setSelectedRows] = useState([]);
  const [hasMoreData, setHasMoreData] = useState(true);
  const [dropDownData, setDropDownData] = useState();

  useEffect(() => {
    if (!isPageViewInvoked) {
      try {
        ga4react?.pageview(history.location.pathname, '', 'Seafarer List');
        setIsPageViewInvoked(true);
      } catch (e) {
        console.log(e);
      }
    }
  }, [isPageViewInvoked]);

  useEffect(() => {
    (async () => {
      loadNationalities();
    })();
  }, []);

  useEffect(() => {
    setFilteredSeafarers([]);
    setShowAdvSearch(false);
    setActiveKey(tab);
    setDefaultFilters(tab);
    setInitSort(getPageSort(tab));
  }, [tab]);

  const ga4EventTrigger = (action, category, label) => {
    try {
      ga4react?.event(action, toString(label), category, false);
    } catch (error) {
      console.log(error);
    }
  };

  const handleLongServiceExportToExcel = async () => {
    eventTracker('exportLongServiceToExcel', activeKey);
    try {
      setIsLongServiceExcelExporting(true);
      const batchSize = 1500;
      const batchRunNum = Math.ceil(500000 / batchSize);
      const excelSeafarerList = [];
      for (let n = 0; n < batchRunNum; n += 1) {
        const fetchList = await fetchExcelData({
          pageSize: batchSize,
          sortBy: initSort,
          pageIndex: 0,
          offset: n,
        });
        if (!fetchList || fetchList.length === 0) {
          break;
        }

        excelSeafarerList.push(...fetchList);
      }
      if (excelSeafarerList.length === 0) {
        setIsLongServiceExcelExporting(false);
        return;
      }
      const columnsToExport = selectedColumns.filter((s) => s.isLongServiceExportField);
      const excelData = [
        {
          jsonData: excelSeafarerList,
          columns: columnsToExport,
        },
      ];
      exportTableToExcel(excelData, 'Seafarer list', 'Seafarer list');
      setIsLongServiceExcelExporting(false);
    } catch (error) {
      console.log('Failed to export data', error);
    }
    setIsLongServiceExcelExporting(false);
  };

  const eventTracker = (type, value) => {
    switch (type) {
      case 'tabs':
        ga4EventTrigger('Tab', 'Nav', `Seafarer List Page - ${getTabName(value)}`);
        break;
      case 'pageSwitch':
        ga4EventTrigger('Page Number', 'Pagination', `Seafarer List Page - ${value}`);
        break;
      case 'pageSizeSwitch':
        ga4EventTrigger('Number Of Rows', 'Pagination', `Seafarer List Page - ${value}`);
        break;
      case 'columnDisplay':
        ga4EventTrigger('Column', 'List', `Seafarer List Page - ${value}`);
        break;
      case 'KeywordSearch':
        ga4EventTrigger('Keyword', 'Keyword Search', `Seafarer List Page - ${value}`);
        break;
      case 'KeywordSearchClick':
        ga4EventTrigger('Click', 'Keyword Search', 'Seafarer List Page');
        break;
      case 'advancedSearch':
        ga4EventTrigger('Click', 'Advance Search', `Seafarer List Page - ${value}`);
        break;
      case 'filterTypeChange':
        ga4EventTrigger('Category', 'Advance Search', `Seafarer List Page - ${value}`);
        break;
      case 'filterSubTypeChange':
        ga4EventTrigger('Value', 'Advance Search', `Seafarer List Page - ${value}`);
        break;
      case 'sortBy':
        ga4EventTrigger('Sorting', 'List', `Seafarer List Page: Sort By - ${value}`);
        break;
      case 'scroll':
        ga4EventTrigger('Back to Top', 'Nav', 'Back To Top');
        break;
      case 'actions':
        ga4EventTrigger('List More', 'Menu', `Seafarer List Page - ${value}`);
        break;
      case 'moreDropdown':
        ga4EventTrigger('More', 'Menu', `Seafarer List Page - ${value}`);
        break;
      case 'exportToExcel':
        ga4EventTrigger(
          'Export Modeller Summary',
          'Seafarer list - menu',
          `Seafarer List Page - ${value}`,
        );
        break;
      case 'yearsOfLongService':
        ga4EventTrigger('Years of Long Service', 'Seafarer list - filter', `${value}`);
        break;
      default:
        ga4EventTrigger('Click', value, 'Seafarer List Page');
        break;
    }
  };

  const contractExpEventTracker = (type, value) => {
    switch (type) {
      case 'addFilter':
        ga4EventTrigger(
          'Add Filter',
          getTabName(activeKey),
          `Seafarer List Page - ${getTabName(activeKey)}`,
        );
        break;
      case 'columnDisplay':
        ga4EventTrigger('Column', 'Contract Expiry List', `Seafarer List Page - ${value}`);
        break;
      case 'pageSizeSwitch':
        ga4EventTrigger(
          'Number Of Rows',
          'Contract Expiry List Pagination',
          `Contract Expiry Page - ${value}`,
        );
        break;
      case 'scroll':
        ga4EventTrigger('Back to Top', 'Contract Expiry Nav', 'Back To Top');
        break;
      case 'sortBy':
        ga4EventTrigger(
          'Sorting',
          'Contract Expiry List',
          `Contract Expiry Page: Sort By - ${value}`,
        );
        break;
      case 'KeywordSearchClick':
        ga4EventTrigger(
          'Click',
          'Contract Expiry Keyword Search',
          `Contract Expiry Page - ${value}`,
        );
        break;
      case 'replaceSeafarer':
        ga4EventTrigger('Replace', 'Contract Expiry Menu', `Contract Expiry Page - ${value}`);
        break;
      case 'crewListLink':
        ga4EventTrigger(
          'Crew List from Contract Expiry',
          'Contract Expiry Link',
          `Contract Expiry Page - ${value}`,
        );
        break;
      case 'crewHKIDLink':
        ga4EventTrigger(
          'View Crew Details',
          'Contract Expiry Link',
          `Contract Expiry Page - ${value}`,
        );
        break;
      case 'vesselDetailsLink':
        ga4EventTrigger(
          'View Vessel Details',
          'Contract Expiry Link',
          `Contract Expiry Page - ${value}`,
        );
        break;
      default:
        eventTracker(type, value);
        break;
    }
  };

  const avlSeafarerEventTracker = (type, value) => {
    switch (type) {
      case 'addFilter':
        ga4EventTrigger(
          'Add Filter',
          getTabName(activeKey),
          `Seafarer List Page - ${getTabName(activeKey)}`,
        );
        break;
      case 'columnDisplay':
        ga4EventTrigger('Column', 'Available Seafarer', `Seafarer List Page - ${value}`);
        break;
      case 'pageSizeSwitch':
        ga4EventTrigger(
          'Number Of Rows',
          'Available Seafarer Pagination',
          `Available Seafarer Page - ${value}`,
        );
        break;
      case 'scroll':
        ga4EventTrigger('Back to Top', 'Available Seafarer Nav', 'Back To Top');
        break;
      case 'sortBy':
        ga4EventTrigger(
          'Sorting',
          'Available Seafarer',
          `Available Seafarer Page: Sort By - ${value}`,
        );
        break;
      case 'crewHKIDLink':
        ga4EventTrigger(
          'View Crew Details',
          'Available Seafarer Link',
          `Available Seafarer Page - ${value}`,
        );
        break;
      case 'vesselDetailsLink':
        ga4EventTrigger(
          'View Vessel Details',
          'Available Seafarer Link',
          `Available Seafarer Page - ${value}`,
        );
        break;
      default:
        eventTracker(type, value);
        break;
    }
  };

  const getTabSpecificEventTracker = (activeKey) => {
    switch (activeKey) {
      case 'contract-expiry':
        return contractExpEventTracker;
      case 'available-seafarers':
        return avlSeafarerEventTracker;
      default:
        return eventTracker;
    }
  };

  useEffect(() => {
    if (debouncedFilters.length) {
      resetAllTabs();
      const filteredList = removeEmptyFilters(debouncedFilters);
      if (filteredList.some((f) => f.type.type.includes('years_of_long_service'))) {
        setIsLongServiceFilterSelected(true);
      } else {
        setIsLongServiceFilterSelected(false);
      }
      if (filteredList.length === 0) {
        window.history.replaceState({}, null, `${history.location.pathname}`);
        setApiQuery('');
        return;
      }
      storePageNumber(tab, 0);
      const controller = new SearchController();
      const apiFilterQuery = controller.getQuery(filteredList);
      setApiQuery(apiFilterQuery);
      const filterQuery = controller.getQuery(
        filteredList.filter((i) => i.defaultTab === undefined),
      );
      window.history.replaceState({}, null, `${history.location.pathname}?${filterQuery}`);
    } else {
      window.history.replaceState({}, null, `${history.location.pathname}`);
      setApiQuery('');
      setIsLongServiceFilterSelected(false);
    }
  }, [debouncedFilters]);

  const getTabName = (key) => {
    return ListTabData.find((e) => e.eventKey === key).tabName;
  };

  const getData = useCallback(
    async ({ pageSize, pageIndex, sortBy }) => {
      // Use default columns only if no
      // selection has been made previously
      if (
        activeKey === 'contract-expiry' &&
        !isKeyStored(
          'contract-expiry-table-details',
          activeKey,
          LOCAL_STORAGE_FIELDS.contractTableSelectedColumns,
        )
      ) {
        setSelectedColumns(
          [...contractExpiryColumns]
            .filter((i) => DEFAULT_COLUMNS_CONTRACT_EXPIRY.includes(i.Header))
            .sort((a, b) => a.order - b.order),
        );
      } else if (
        activeKey === 'available-seafarers' &&
        !isKeyStored(
          'available-seafarers-table-details',
          activeKey,
          LOCAL_STORAGE_FIELDS.availableTableSelectedColumns,
        )
      ) {
        availableSeafarerColumn.forEach((e) => {
          if (e.id === 'recommend') {
            e.accessor = function recommendAccessor(row) {
              return (
                <div>
                  <Button
                    onClick={(e) => {
                      e.stopPropagation();
                      goToParis1SeafarerRecommend(row.ref_id, roleConfig.shipPartyId);
                    }}
                    variant="outline-primary"
                  >
                    Recommend
                  </Button>
                </div>
              );
            };
          }
        });
        setSelectedColumns(availableSeafarerColumn.filter(col => col.showByDefault).sort((a, b) => a.order - b.order));
      } else if (
        activeKey !== 'available-seafarers' &&
        activeKey !== 'contract-expiry' &&
        !SCREENING_STATUS.includes(activeKey) &&
        !isKeyStored('seafarer-table-details', activeKey, LOCAL_STORAGE_FIELDS.tableSelectedColumns)
      ) {
        setSelectedColumns([...items.slice(1, 11), items[12]]);
      } else if (
        SCREENING_STATUS.includes(activeKey) &&
        !isKeyStored('seafarer-table-details', activeKey, LOCAL_STORAGE_FIELDS.tableSelectedColumns)
      ) {
        setSelectedColumns([
          ...items.slice(1, 7),
          ...getDefaultScreeningColumns(activeKey),
          ...items.slice(7, 11),
          items[12],
        ]);
      } else {
        setSelectedColumns(retrieveColumns(activeKey));
      }

      // Fetch data for listing and searching
      // fetchData({ pageSize, pageIndex, sortBy });

      // Quick search active
      if (searchedKeyword.length && hasNoSearchCriteria) {
        handleQuickSearch({ pageSize, pageIndex, sortBy });
      } else {
        await fetchData({ pageSize, pageIndex, sortBy });
      }
    },
    [searchedKeyword, activeKey, query, apiQuery],
  );

  const clearFilters = () => {
    const filteredList = removeEmptyFilters(filters);
    if (filteredList.length === 0) {
      setFilters([]);
    }
    if (searchedKeyword.length || filteredList.length) {
      document.getElementById('search-bar').value = '';
      setSearchedKeyword('');
      setFilters([]);
      setFilteredSeafarers([]);
      setLoading(false);
      history.push(`/seafarer/${activeKey}`);
    }
    eventTracker('clearAll', 'Clear All');
  };

  // handle both listing and searching
  const queryListData = async (sortPaginateData) => {
    try {
      setLoading(true);
      if (sortPaginateData.pageIndex === 0) {
        setFilteredSeafarers([]);
        setHasMoreData(true);
      }
      const tempSortPaginationData = { ...sortPaginateData };
      let queryParams = generateQuickSearch({ keyword: searchedKeyword });
      if (sortPaginateData.sortBy.length) {
        let validColumns = [...items, ...getDefaultScreeningColumns(activeKey)];
        if (activeKey === 'available-seafarers') {
          validColumns = availableSeafarerColumn;
        }
        const columnExists = validColumns
          .map((column) => column.id)
          .find((columnId) => columnId === sortPaginateData.sortBy[0].id);
        if (!columnExists) {
          tempSortPaginationData.sortBy = [];
        }
      }
      queryParams = addSortOrPaginateParams(tempSortPaginationData, queryParams);
      if (filters.length) {
        let filteredList = _.cloneDeep(removeEmptyFilters(filters));
        if (filteredList.length) {
          filteredList.forEach((filter) => {
            if ([approvalGroupKey, pendingApprovalGroupKey].includes(filter.type.queryKey)) {
              filter.type.queryKey = commonApprovalGroupKey;
            }
          });
          filteredList = filteredList.filter((filter) => {
            if (filter.type.type === 'data_of_contract_expry' && !filter.subtype?.startDate) {
              return false;
            }
            return true;
          });
          const controller = new SearchController();
          const filterQuery = controller.getQuery(filteredList);
          queryParams = `${queryParams}&${filterQuery}`;
        }
      } else {
        queryParams += `&${apiQuery}`;
      }
      if (roleConfig.shipPartyType === shipPartyType.MANNING_AGENT) {
        queryParams += `&seafarer_reporting_office.ship_party_id=${roleConfig.shipPartyId}`;
      }
      const queryParamsObject = new URLSearchParams(queryParams);
      queryParamsObject.set('excludeCount', 'true');
      let response;
      if (activeKey === 'contract-expiry') {
        response = await seafarerService.getContractExpirySeafarers(queryParamsObject.toString());
      } else if (activeKey === 'available-seafarers') {
        response = await seafarerService.getAvailableSeafarers(queryParamsObject.toString());
      } else {
        response = await seafarerService.getSeafarers(activeKey, queryParamsObject.toString());
      }
      delete response.data.pagination.totalCount;
      const seafarerRecords = response.data.results;
      const filteredRecords = seafarerRecords.map((item) => {
        return { ...item, keyword: searchedKeyword };
      });
      if (sortPaginateData.pageIndex === 0) {
        setFilteredSeafarers(filteredRecords);
      } else {
        setFilteredSeafarers((prev) => [...prev, ...filteredRecords]);
      }
      if (filteredRecords.length < DEFAULT_PAGE_SIZE) {
        setHasMoreData(false);
      }
      setLoading(false);
    } catch (error) {
      if (httpService.axios.isCancel(error)) {
        return;
      }
      console.log('error:', error);
      setLoading(false);
    }
  };

  const fetchExcelData = async (sortPaginateData) => {
    try {
      let queryParams = generateQuickSearch({ keyword: searchedKeyword });
      queryParams = addSortOrPaginateParams(sortPaginateData, queryParams);
      if (filters.length) {
        const filteredList = removeEmptyFilters(filters);
        if (filteredList.length !== 0) {
          const controller = new SearchController();
          const filterQuery = controller.getQuery(filteredList);
          queryParams = `${queryParams}&${filterQuery}`;
        }
      } else {
        queryParams += `&${apiQuery}`;
      }
      if (roleConfig.shipPartyType === shipPartyType.MANNING_AGENT) {
        queryParams += `&seafarer_reporting_office.ship_party_id=${roleConfig.shipPartyId}`;
      }
      let response;
      if (activeKey === 'contract-expiry') {
        response = await seafarerService.getContractExpirySeafarers(queryParams);
      } else if (activeKey === 'available-seafarers') {
        response = await seafarerService.getAvailableSeafarers(queryParams);
      } else {
        response = await seafarerService.getSeafarers(activeKey, queryParams);
      }
      return response.data.results;
    } catch (err) {
      console.log(err, 'error while fetching excel records');
    }
  };

  // Fetch data for searching
  const handleQuickSearch = useDebouncedCallback(
    async (sortPaginateData) => {
      await queryListData(sortPaginateData);
    },
    700,
    [query, searchedKeyword, activeKey, apiQuery],
  );

  const removeEmptyFilters = (filter) => {
    return filter.filter(
      (item) => item.subtype !== null && item.subtype !== undefined && item.subtype !== '',
    );
  };

  // Fetch data for non quick search case
  const fetchData = useCallback(
    async (sortPaginateData) => {
      await queryListData(sortPaginateData);
    },
    [query, searchedKeyword, activeKey, apiQuery],
  );

  const loadNationalities = async () => {
    try {
      const response = await controller.loadDropDownData();
      const searchController = new SearchController();
      const result = await searchController.onLoadPage();
      const existingDropdowns = Object.keys(response);
      Object.keys(result.dropDownData).forEach((i) => {
        if (!existingDropdowns?.includes(i)) {
          response[i] = result.dropDownData[i];
        }
      });

      /* using window location instead of active key because when user quickly switches
          tab during initial load activekey new value is not reflected yet during this function execution */
      const tab = window.location.pathname
        .split('/')
        .find((i) => ListTabData.map((i) => i.eventKey).includes(i));
      tab === 'available-seafarers' && seafarerId
        ? await handleAvailableSeafarerFilters(
          processOnLoadFilters(mapQueryStringToSearchCriteria(query, response, tab)),
        )
        : setDefaultFilters(
          tab,
          processOnLoadFilters(mapQueryStringToSearchCriteria(query, response, tab)),
        );
      setDropDownData(response);
      setShowAdvSearch(true);
      return response;
    } catch (error) {
      console.log('## error', error);
    }
  };

  const processOnLoadFilters = (filters = []) => {
    let onLoadFilters = INITIAL_LOAD_FILTERS;

    if (filters.length) {
      let additionalFilters = [...filters];
      onLoadFilters = onLoadFilters.map(
        (i) => (i = additionalFilters.find((item) => item?.type?.type === i?.type?.type) ?? i),
      );
      const existingFilter = onLoadFilters.map((i) => i?.type?.type);
      additionalFilters = additionalFilters.filter((i) => !existingFilter.includes(i?.type?.type));
      onLoadFilters = [...onLoadFilters, ...additionalFilters];
    }
    return onLoadFilters;
  };

  const getHeaderName = (header) => {
    return header === approvalColumns.APPROVAL_GROUP
      ? approvalColumns.PENDING_APPROVAL_GROUP
      : approvalColumns.APPROVAL_GROUP;
  };

  const onSelectColumn = (item) => {
    const newSelection = selectedColumns.slice();
    let columnsStoredInLocalStorage = [];
    const idx = newSelection.indexOf(item);
    const tracker = getTabSpecificEventTracker(activeKey);
    if (idx !== -1) {
      newSelection.splice(idx, 1);
      tracker('columnDisplay', `${item.Header} remove`);
    } else {
      newSelection.push(item);
      newSelection.sort((a, b) => a.order - b.order);
      tracker('columnDisplay', `${item.Header} add`);
      // storing approval group and pending approval group in localstorage if any one is selected from tablecolumn
      if (APPROVAL_GROUPS.includes(item.Header)) {
        columnsStoredInLocalStorage.push({ ...item, Header: getHeaderName(item.Header) });
      }
    }

    // getting stored column headers from localstorage
    const storedColumnHeaders = getStoredColumnHeaders(activeKey);
    // getting stored screening column headers from localstorage
    const storedScreeningHeaders = storedColumnHeaders?.filter((selectedHead) =>
      SCREENING_COLUMNS.includes(selectedHead),
    );

    if (
      storedColumnHeaders &&
      !APPROVAL_GROUPS.includes(item.Header) &&
      storedScreeningHeaders.length
    ) {
      const index = storedScreeningHeaders.findIndex((header) => header === item.Header);
      if (index > -1) storedScreeningHeaders.splice(index, 1); // remove column from local storage if column deselected
      // create array of headers object needs to stored in localstorage
      const cols = storedScreeningHeaders.map((head) => {
        return {
          Header: head,
        };
      });
      columnsStoredInLocalStorage = cols;
    }

    columnsStoredInLocalStorage = [...newSelection, ...columnsStoredInLocalStorage];
    columnsStoredInLocalStorage = columnsStoredInLocalStorage.filter(
      ({ Header }, index) =>
        !columnsStoredInLocalStorage.map((column) => column.Header).includes(Header, index + 1),
    ); // removed duplicates column
    setSelectedColumns(newSelection);
    // Update selected columns in local store
    storeColumns(
      activeKey,
      columnsStoredInLocalStorage.length ? columnsStoredInLocalStorage : newSelection,
    );
  };

  const handleAddSeafarer = () => {
    eventTracker('addSeafarer', 'Add Seafarer');
    history.push('/seafarer/add/basic/');
  };

  const visitUpdateSeafarer = useCallback((seafarerId, ref_id, seafarer) => {
    eventTracker('edit', `Edit Seafarer - ${seafarer.first_name} ${seafarer.last_name}`);
    history.push(`/seafarer/${seafarerId}/add/basic`);
  }, []);

  const visitSeafarer = useCallback((seafarerId, activeKey = '') => {
    if (activeKey === 'contract-expiry' || activeKey === 'available-seafarers') {
      activeKey === 'contract-expiry'
        ? contractExpEventTracker('crewHKIDLink', seafarerId)
        : avlSeafarerEventTracker('crewHKIDLink', seafarerId);
      window.open(`/seafarer/details/${seafarerId}/general`, '_blank');
    } else {
      history.push(`/seafarer/details/${seafarerId}/general`);
    }
  }, []);

  const visitSeafarerContactUpdate = (e, seafarerId) => {
    e.stopPropagation();
    avlSeafarerEventTracker('editAvailability', seafarerId);
    history.push(`/seafarer/details/${seafarerId}/availability`);
  };

  const setDefaultFilters = (key, additionalQueryFilters, defaultFilterData = null) => {
    const controller = new SearchController();
    let additionalFilters = additionalQueryFilters
      ? additionalQueryFilters.filter((i) => {
        return i.type.validTabs === undefined || i.type.validTabs.includes(key);
      })
      : undefined;
    let filterArray = [...filters].filter((i) => {
      return (
        (i.defaultTab === undefined || i.defaultTab === key) &&
        (i.type.validTabs === undefined || i.type.validTabs.includes(key))
      );
    });

    if (!['contract-expiry', 'available-seafarers'].includes(key)) {
      additionalFilters
        ? setFilters([...filterArray, ...additionalFilters])
        : setFilters([...filterArray]);
      additionalFilters
        ? setApiQuery(controller.getQuery([...filterArray, ...additionalFilters]).trim())
        : setApiQuery(controller.getQuery([...filterArray]));
      return;
    }
    let newFilter = [];
    if (defaultFilterData) {
      newFilter = defaultFilters(key, defaultFilterData);
    } else {
      newFilter = defaultFilters(key);
    }
    if (additionalFilters) {
      newFilter = newFilter.map(
        (i) => (i = additionalFilters.find((item) => item?.type?.type === i?.type?.type) ?? i),
      );
      const existingFilter = newFilter.map((i) => i?.type?.type);
      additionalFilters = additionalFilters.filter((i) => !existingFilter.includes(i?.type?.type));
      setFilters([...newFilter, ...filterArray, ...additionalFilters]);
      setApiQuery(controller.getQuery([...newFilter, ...filterArray, ...additionalFilters]));
    } else {
      newFilter = newFilter.map(
        (i) => (i = filterArray.find((item) => item?.type?.type === i?.type?.type) ?? i),
      );
      const existingFilter = newFilter.map((i) => i?.type?.type);
      filterArray = filterArray.filter((i) => !existingFilter.includes(i?.type?.type));
      setFilters([...newFilter, ...filterArray]);
      setApiQuery(controller.getQuery([...newFilter, ...filterArray]));
    }
  };

  const handleAvailableSeafarerFilters = async (urlQuerys = null) => {
    try {
      setSignOffRecords([]);
      setSignOffLoading(true);
      const result = await Promise.all([
        seafarerService.getSignOffSeafarers(`id=${seafarerId}`),
        seafarerService.getDropDownDataFromVessel(),
      ]);
      let vessel_type =
        result[0]?.data?.results[0]?.seafarer_person?.seafarer_status_history?.[0]?.vessel_type ??
        '';
      vessel_type = result[1]?.data?.vesselTypes.filter((i) => i.value === vessel_type);
      vessel_type = vessel_type.length
        ? vessel_type
        : [
          {
            id: 0,
            value: 'All Vessel Types',
          },
        ];
      const filterdata = {
        rank: [
          {
            id:
              result[0]?.data?.results[0]?.seafarer_person?.seafarer_status_history?.[0]
                ?.seafarer_rank.id ?? '',
            value:
              result[0]?.data?.results[0]?.seafarer_person?.seafarer_status_history?.[0]
                ?.seafarer_rank.value ?? '',
          },
        ],
        vesselType: vessel_type,
        contractEndDate: moment(
          result[0]?.data?.results[0]?.seafarer_person?.seafarer_status_history[0]
            ?.expected_contract_end_date,
        ).toDate(),
      };
      setDefaultFilters(activeKey, urlQuerys, filterdata);

      const seafarerRecords = result[0].data.results;
      const filteredRecords = seafarerRecords.map((item) => {
        return { ...item, keyword: searchedKeyword };
      });
      if (filteredRecords) {
        setSignOffLoading(false);
        setSignOffRecords(filteredRecords);
      }
    } catch (error) {
      console.log(`failed to fetch seafarer data with id:${seafarerId} for default filters`, error);
      setDefaultFilters(activeKey, urlQuerys);
    }
  };

  const handleTabSelect = (key) => {
    setInitSort(getPageSort(key));
    if (key === activeKey) {
      if (query || apiQuery) {
        // clear the current query and refresh page
        setFilters([]);
        resetAllTabs();

        setFilteredSeafarers([]);
        setActiveKey(key);
        history.push(`/seafarer/${key}`);
      }
      return;
    }
    getTabSpecificEventTracker(key)('tabs', key);

    setFilteredSeafarers([]);
    setShowAdvSearch(false);
    setActiveKey(key);
    history.push(`/seafarer/${key}?${query}`);
    setDefaultFilters(key);
    setIsPageViewInvoked(false);
  };

  useEffect(() => {
    (async () => {
      if (activeKey === 'available-seafarers' && seafarerId) await handleAvailableSeafarerFilters();
    })();
  }, [seafarerId]);

  useEffect(() => {
    if (
      (['contract-expiry', 'available-seafarers'].includes(activeKey) ||
        filters.map((i) => i?.type?.type).includes('hkid')) &&
      !showAdvSearch &&
      dropDownData
    ) {
      setShowAdvSearch(true);
    }
  }, [activeKey, dropDownData]);

  const canCreateSeafarer = () => {
    return roleConfig.seafarer.addSeafarer === true;
  };

  const handleKeywordChange = useDebouncedCallback((value) => {
    eventTracker('KeywordSearch', value);
    storePageNumber(tab, 0);
    setSearchedKeyword(value.trim().toLowerCase());
  }, 500);

  const ontoggle = () => {
    eventTracker('advancedSearch', `${showAdvSearch ? 'close' : 'open'}`);
    setShowAdvSearch(!showAdvSearch);
  };

  const advSearchStyle = `${
    DEFAULT_TOP_MARGIN + (filters.length > 0 ? Math.ceil(filters.length / 2) : 1) * EVERY_ITEM
  }px`;
  const contractExpiryContextValue = useMemo(
    () => ({
      activeKey,
      setActiveKey,
      setShowAdvSearch,
      setSignOffLoading,
      setInitSort,
    }),
    [activeKey],
  );

  const availableSeafarerContextValue = useMemo(
    () => ({ activeKey, apiQuery }),
    [activeKey, apiQuery],
  );
  // The rerenders based on activeKey are needed
  // NOSONAR
  return (
    <AccessHandlerWrapper hasRoleAccess={roleConfig.seafarer.view.general}>
      <Container>
        <Modal show={showModal} centered>
          <Modal.Header>
            <Modal.Title>The feature is under construction.</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            This page is for view only at the moment. To edit or perform other actions, please go to
            <Button
              variant="link"
              onClick={() => goToParis1(selectedRefId.current, roleConfig.shipPartyId)}
            >
              PARIS 1.0
            </Button>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={() => setShowModal(false)}>
              Close
            </Button>
          </Modal.Footer>
        </Modal>
        {showAdvSearchMobile ? (
          <div className="mobileAdvSearch">
            <AdvancedSearchMobile
              setShowAdvSearchMobile={setShowAdvSearchMobile}
              roleConfig={roleConfig}
              filters={filters}
              setFilters={setFilters}
              tab={activeKey}
            />
          </div>
        ) : (
          <>
            <Tab.Container activeKey={activeKey} defaultActiveKey="all">
              <div>
                <Row className="no-print">
                  <Col>
                    <TabWrapper handleTabSelect={handleTabSelect} data={ListTabData} />
                  </Col>
                </Row>
                <Row className="no-print">
                  <Col xs={6} md={3}>
                    <div className="mt-4 quick-filters-button">
                      <Form.Control
                        id="search-bar"
                        data-testid="search-bar"
                        type="text"
                        name="keyword"
                        placeholder="Search by Name or HKID 2"
                        ref={searchInputRef}
                        onClick={() =>
                          getTabSpecificEventTracker(activeKey)(
                            'KeywordSearchClick',
                            'Keywork Search Click',
                          )
                        }
                        onChange={(e) => handleKeywordChange(e.target.value)}
                      />
                    </div>
                  </Col>
                  <Col xs={6} md="auto">
                    <div className="mobileAdvSearch">
                      <ButtonGroup className="advanced-search-seafarer mt-4 w-100">
                        <Button
                          variant="outline-primary"
                          onClick={() => {
                            setShowAdvSearchMobile(true);
                          }}
                        >
                          Advanced Search
                        </Button>
                      </ButtonGroup>
                    </div>
                    <div className="desktopAdvSearch">
                      <ButtonGroup className="advanced-search-seafarer mt-4">
                        <Dropdown
                          alignRight={false}
                          onToggle={() => ontoggle()}
                          show={showAdvSearch}
                        >
                          <Dropdown.Toggle
                            onClick={ontoggle}
                            variant="outline-primary"
                            id="dropdown-advanced-search"
                            data-testid="dropdown-advanced-search"
                          >
                            Advanced Search{' '}
                            {removeEmptyFilters(debouncedFilters).length
                              ? `(${removeEmptyFilters(debouncedFilters).length})`
                              : ''}
                          </Dropdown.Toggle>
                          <Dropdown.Menu>
                            <div className="advanced-search-seafarer-menu">
                              <AdvancedSearch
                                roleConfig={roleConfig}
                                filters={filters}
                                setFilters={setFilters}
                                eventTracker={eventTracker}
                                tab={activeKey}
                                dropDownDataProp={dropDownData}
                              />
                            </div>
                          </Dropdown.Menu>
                        </Dropdown>
                      </ButtonGroup>
                    </div>
                  </Col>
                  <Col xs="auto">
                    <Button
                      variant="outline-primary"
                      className="mt-4"
                      onClick={clearFilters}
                      data-testid="fml-seafarer-list-advanced-search-clear-all"
                    >
                      Clear All
                    </Button>
                  </Col>
                  <Col className="seafarer-list-btn-container">
                    <ButtonsBar
                      canCreateSeafarer={canCreateSeafarer}
                      handleAddSeafarer={handleAddSeafarer}
                      onSelectColumn={onSelectColumn}
                      selectedColumns={selectedColumns}
                      eventTracker={eventTracker}
                      activeKey={activeKey}
                      handleLongServiceExportToExcel={handleLongServiceExportToExcel}
                      isLongServiceExcelExporting={isLongServiceExcelExporting}
                      isLongServiceFilterSelected={isLongServiceFilterSelected}
                    />
                  </Col>
                </Row>
              </div>
              {filters.length > 0 && (
                <Row className="mobileAdvSearch">
                  <SearchParametersView
                    criteria={filters}
                    setShowAdvSearchMobile={setShowAdvSearchMobile}
                  />
                </Row>
              )}
              <Row
                className="desktopAdvSearch"
                style={{
                  marginTop: showAdvSearch ? advSearchStyle : '0px',
                }}
              />
              {activeKey === 'available-seafarers' && seafarerId && (
                <Row>
                  <Col>
                    <ToReplaceSeafarer
                      seafarers={signOffRecords}
                      visitSeafarer={visitSeafarer}
                      eventTracker={eventTracker}
                      visitUpdateSeafarer={visitUpdateSeafarer}
                      selectedColumns={[
                        ...contractExpiryColumns.slice(0, 5),
                        ...contractExpiryColumns.slice(6, 7),
                        ...contractExpiryColumns.slice(9, 10),
                        ...contractExpiryColumns.slice(8, 9),
                        ...contractExpiryColumns.slice(11, 12),
                      ].map((i) => {
                        i = { ...i };
                        i.sticky = null;
                        return i;
                      })}
                      loading={signOffLoading}
                      roleConfig={roleConfig}
                      init_sort={initSort}
                      seafarersTotalCount={1}
                      tableRef={signOffTableRef}
                    />
                  </Col>
                </Row>
              )}
              <Row>
                <Col>
                  <Tab.Content>
                    <Tab.Pane eventKey={activeKey}>
                      {activeKey === 'contract-expiry' && (
                        <TabContext.Provider value={contractExpiryContextValue}>
                          <ContractExpiryTable
                            tabName={activeKey}
                            fetchData={getData}
                            seafarers={filteredSeafarers}
                            visitSeafarer={visitSeafarer}
                            eventTracker={contractExpEventTracker}
                            visitUpdateSeafarer={visitUpdateSeafarer}
                            selectedColumns={selectedColumns}
                            loading={loading}
                            quickSearchParams={searchedKeyword}
                            advancedSearchParams={apiQuery}
                            init_sort={initSort}
                            tableRef={tableRef}
                            hasMoreData={hasMoreData}
                          />
                        </TabContext.Provider>
                      )}
                      {activeKey === 'available-seafarers' && !signOffLoading && (
                        <TabContext.Provider value={availableSeafarerContextValue}>
                          <AvailableSeafarerTable
                            tabName={activeKey}
                            fetchData={getData}
                            seafarers={filteredSeafarers}
                            visitSeafarer={visitSeafarer}
                            eventTracker={avlSeafarerEventTracker}
                            visitSeafarerContactUpdate={visitSeafarerContactUpdate}
                            selectedColumns={selectedColumns}
                            loading={loading}
                            roleConfig={roleConfig}
                            quickSearchParams={searchedKeyword}
                            advancedSearchParams={apiQuery}
                            init_sort={initSort}
                            tableRef={tableRef}
                            hasMoreData={hasMoreData}
                          />
                        </TabContext.Provider>
                      )}

                      {activeKey !== 'contract-expiry' && activeKey !== 'available-seafarers' && (
                        <SeafarerTable
                          tabName={activeKey}
                          seafarers={filteredSeafarers}
                          fetchData={getData}
                          visitSeafarer={visitSeafarer}
                          eventTracker={eventTracker}
                          visitUpdateSeafarer={visitUpdateSeafarer}
                          selectedColumns={selectedColumns}
                          loading={loading}
                          roleConfig={roleConfig}
                          quickSearchParams={searchedKeyword}
                          advancedSearchParams={apiQuery}
                          init_sort={initSort}
                          tableRef={tableRef}
                          selectedRows={selectedRows}
                          onSelectRow={(rows) => setSelectedRows(rows)}
                          hasMoreData={hasMoreData}
                        />
                      )}
                    </Tab.Pane>
                  </Tab.Content>
                </Col>
              </Row>
            </Tab.Container>
            <ScrollArrow eventTracker={getTabSpecificEventTracker(activeKey)} />
          </>
        )}
      </Container>
    </AccessHandlerWrapper>
  );
};

export default List;
