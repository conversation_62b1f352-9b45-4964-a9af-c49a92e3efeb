/* eslint-disable no-unused-vars */
import React, { useEffect, useState } from 'react';
import download from 'downloadjs';

import { Document, Page, pdfjs } from 'react-pdf'; // NOSONAR
// NOSONAR
import pdfjsWorker from 'pdfjs-dist/build/pdf.worker.entry'; // NOSONAR
import { useParams } from 'react-router-dom';
import { Button, Col } from 'react-bootstrap';
import { useAccess } from '@src/component/common/Access';
import seafarerService from '../service/seafarer-service';
import Spinner from '../component/common/Spinner';
import DocumentDownloadFailedModal from '../component/document/DocumentDownloadFailedModal';
import styleGuide from '../styleGuide';
import AccessHandlerWrapper from '../component/common/AccessHandlerWrapper';

const { Icon } = styleGuide;
//NOSONAR
// pdfjs.GlobalWorkerOptions.workerSrc = pdfjsWorker; //NOSONAR

const { DOC_DOWNLOAD_FAIL_MESSAGE } = process.env;

const PdfDocViewer = () => {
  const [loading, setLoading] = useState(true);
  const { roleConfig } = useAccess();
  const { docId, docType } = useParams();
  const [type, setType] = useState();
  const [fileUrl, setFileUrl] = useState();
  const [file, setFile] = useState();
  const [fileName, setFileName] = useState('Document');
  const [showDownloadFailModal, setShowDownloadFailModal] = useState(false);
  const [downloadFailStatus, setDownloadFailStatus] = useState(undefined);
  const [rotation, setRotation] = useState(0);
  const [marginRotate, setMarginRotate] = useState(5);
  const [url, setUrl] = useState('');
  const downloadDoc = () => {
    let name;
    if (url.includes('.rar')) {
      name = `${fileName}.rar`;
    } else if (url.includes('.msg')) {
      name = `${fileName}.msg`;
    } else {
      name = fileName;
    }
    download(file, name, type);
  };

  const rotateDoc = () => {
    let newRotation = rotation + 90;
    setMarginRotate(15);
    if (newRotation >= 360) {
      newRotation -= 360;
      setMarginRotate(5);
    }
    setRotation(newRotation);
  };
  const hasAccess = roleConfig.seafarer.view.general;

  const getFileName = (disposition) => {
    let name = fileName;
    if (disposition && disposition.indexOf('attachment') !== -1) {
      const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
      const matches = filenameRegex.exec(disposition);
      if (matches?.[1]) {
        name = matches[1].replace(/['"]/g, '');
      }
    }
    setFileName(name);
  };
  useEffect(() => {
    document.getElementById('navbar').style.display = 'none';
    const bodyTag = document.getElementsByTagName('body')[0];
    bodyTag.style.marginTop = '0';
    bodyTag.style.background = '#000';
  }, []);
  useEffect(async () => {
    try {
      let response;
      if (docType === 'passport') {
        response = await seafarerService.downloadPassportImage(docId);
      } else if (docType === 'seamans_book') {
        response = await seafarerService.downloadSeamanBookImage(docId);
      } else if (docType === 'bank_account') {
        response = await seafarerService.downloadBankAccountImage(docId);
      } else if (docType === 'seafarer_image') {
        response = await seafarerService.downloadSeafarerImage(docId);
      } else {
        response = await seafarerService.downloadSeafarerDocument(docId);
      }
      const file = response.data;
      const type = response.headers['content-type'];
      const name = response.headers['content-disposition'];
      if (file) {
        getFileName(name);
        const updatedFile = new Blob([file], { type });
        const fileURL = window.URL.createObjectURL(updatedFile);
        setFileUrl(fileURL);
        setLoading(false);
        setType(type);
        setFile(file);
        setUrl(response.request?.responseURL);
      }
    } catch (error) {
      setLoading(false);
      if (error?.response?.status) {
        setDownloadFailStatus(error?.response?.status);
      }
      setShowDownloadFailModal(true);
    }
  }, []);

  const [numPages, setNumPages] = useState(null);
  const [pageNumber, setPageNumber] = useState(1);
  const onDocumentLoadSuccess = ({ numPages }) => {
    if (numPages === 1) {
      setNextPageDisabled(true);
      setPrevPageDisabled(true);
    }
    setNumPages(numPages);
  };
  const [nextPageDisabled, setNextPageDisabled] = useState(false);
  const [prevPageDisabled, setPrevPageDisabled] = useState(true);
  const nextPage = () => {
    if (pageNumber + 1 == numPages) {
      setNextPageDisabled(true);
    }
    setPrevPageDisabled(false);
    setPageNumber(pageNumber + 1);
  };
  const prevPage = () => {
    if (pageNumber - 1 === 1) {
      setPrevPageDisabled(true);
    }
    setNextPageDisabled(false);
    setPageNumber(pageNumber - 1);
  };

  const closeWindow = () => {
    window.close();
  };
  return (
    <AccessHandlerWrapper hasRoleAccess={hasAccess}>
      <div className="document-continer">
        <DocumentDownloadFailedModal
          show={showDownloadFailModal}
          onClose={() => {
            setShowDownloadFailModal(false);
            window.close();
          }}
          title="Download Failed"
        >
          {downloadFailStatus == 413 ? (
            <>
              <p>
                We're aware that there are some file download issues. This has to do with the file
                size that has been uploaded in PARIS 1.0. A very small % of all data has been
                uploaded above our new size limit.
              </p>
              <p>
                Going forward, we will not accept files bigger than 5 MB. For the time being, we ask
                you to download this file from{' '}
                <Button
                  variant="link"
                  onClick={() =>
                    goToParis1SeafarerDocument(seafarer.ref_id, roleConfig.shipPartyId)
                  }
                >
                  PARIS 1.0
                </Button>{' '}
                while we work on a permanent solution.
              </p>
            </>
          ) : (
            <p>{DOC_DOWNLOAD_FAIL_MESSAGE}</p>
          )}
        </DocumentDownloadFailedModal>
        {loading ? (
          <div className="spinner-document">
            <Spinner alignClass="" />
          </div>
        ) : (
          <div>
            <div className="header-container">
              <Col>
                <span>{fileName}</span>{' '}
              </Col>
              {numPages && (
                <Col className="page-number-container">
                  {!prevPageDisabled && <Icon icon="arrow-left" onClick={prevPage} size={21} />}
                  <span>
                    {pageNumber} of
                    {numPages}
                  </span>
                  {!nextPageDisabled && <Icon icon="arrow-right" onClick={nextPage} size={21} />}
                </Col>
              )}
              <Col>
                <div className="icon-container">
                  <Icon icon="download" onClick={downloadDoc} size={21} />
                  <Icon icon="close" size={21} onClick={closeWindow} />
                  <Icon icon="rotate" size={21} onClick={rotateDoc} />
                </div>
              </Col>
            </div>
            <div
              className="pdf-style"
              style={{ transform: `rotate(${rotation}deg)`, marginTop: `${marginRotate}%` }}
            >
              {type == 'application/pdf' ? (
                <Document file={file} onLoadSuccess={onDocumentLoadSuccess}>
                  <Page key={`page_${pageNumber}`} pageNumber={pageNumber} />
                </Document>
              ) : (
                <img src={fileUrl} />
              )}
            </div>
          </div>
        )}
      </div>
    </AccessHandlerWrapper>
  );
};

export default PdfDocViewer;
