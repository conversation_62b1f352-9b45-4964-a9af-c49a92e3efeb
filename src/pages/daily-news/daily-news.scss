$blue-color: #1f4a70;

.daily-news {
  padding-left: 36px;
  padding-right: 36px;
  .heading-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .heading {
      font-size: 24px;
      color: $blue-color;
      font-weight: 600;
      margin-right: 1rem;
    }
  }
  .edit-icon {
    color: $blue-color;
  }
  .spinner-grid{
    padding-top: 30px;
    justify-content: center;
  }
  .text-secondary {
    color: $blue-color !important;
  }
  .add-news-btn {
    font-weight: 500;
  }
  .action {
    text-align: end;
    .edit-icon{
      margin-left: 0px;
      margin-right: 2px;
    }

  }
  .page-num {
    margin-left: 0px;
  }
  .page-number-border {
    padding-left: 0px;
  }
  .results-count {
    margin-right: 0px !important;
    font-weight: 100;
  }

  .grid{
    .body .td {
      padding-left: 0px;
    } 
    .th {
      padding-left: 0px;
    }
  }
  .pagination .page-number-border {
    border: none;
  } 

  
}