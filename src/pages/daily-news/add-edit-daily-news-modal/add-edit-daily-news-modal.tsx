import React, { useState } from 'react';
import { useFormik } from 'formik';
import { Button, Col, Modal, Row, Spinner, Form } from 'react-bootstrap';
import {
  initialValues,
  validationSchema,
} from '../../../model/AddEditDailyNewsModalFormValidation';
import './add-edit-daily-news-modal.scss';
import { convertTZ, getDateFromTimestamp } from '../../../util/date-utils';
import { addNews, editNews } from '../../../service/news-service';
import { toast } from 'react-toastify';
import { onHandleError } from '../../../model/utils';
import { AxiosError } from 'axios';

const renderLoaderOnButton = (loading: boolean) => {
  return loading ? <Spinner animation="border" /> : 'Save';
};

const AddEditDailyNewsModal = ({
  show,
  onClose,
  onSave,
  newsToBeEdited,
  categories,
  isEdit = false,
  editable = true,
  ga4EventTrigger = (action, category, label) => {},
}: {
  newsToBeEdited: Object;
  show: boolean;
  onClose: Function;
  onSave: Function;
  categories: Array<Object>;
  isEdit: boolean;
  editable: boolean;
}) => {
  const [loading, setLoading] = useState(false);

  const handleAddEditNews = async (payload) => {
    try {
      setLoading(true);
      if (isEdit) {
        const { data } = await editNews(newsToBeEdited.id, payload);
        if (data.error) {
          onHandleError(data.message, setLoading);
          return;
        }
        toast.success('News has been updated successfully');
      } else {
        const { data } = await addNews(payload);
        if (data.error) {
          onHandleError(data.message, setLoading);
          return;
        }
        toast.success('News has been created successfully');
      }
      onSave();
      setLoading(false);
    } catch (error) {
      const { message } = error as AxiosError;
      toast.error(`Error: ${message}`);
      setLoading(false);
    }
  };

  const { handleSubmit, values, errors, handleBlur, handleChange, dirty } = useFormik({
    initialValues: newsToBeEdited || initialValues,
    validationSchema,
    onSubmit: (valueObj) => {
      const payload = {
        newsCategoryId: parseInt(valueObj.categoryId),
        headline: valueObj.headline,
        story: valueObj.story,
      };
      handleAddEditNews(payload);
    },
  });

  const renderModalHeading = () => {
    let heading = `${isEdit ? 'Edit ' : 'Add '}Daily News`;
    if (!editable) {
      heading = 'Daily News';
    }
    return heading;
  };

  const getLastEdited = () => {
    if (isEdit && editable) {
      const lastEditedHkTime = new Date(convertTZ(newsToBeEdited.updatedAt, 'HongKong'));
      const lastEditedText = `Last edited by ${
        newsToBeEdited.updatedBy ?? newsToBeEdited.createdBy
      } on ${getDateFromTimestamp(lastEditedHkTime)} ${lastEditedHkTime.getHours()}:${
        lastEditedHkTime.getMinutes() < 10 ? '0' : ''
      }${lastEditedHkTime.getMinutes()}`;
      return (
        <Row className="margin-top-1rem">
          <Col>
            <p style={{ color: '#6C757D', fontSize: '13px' }} data-testid="edit-news-text">
              {lastEditedText}
            </p>
          </Col>
        </Row>
      );
    }
  };
  return (
    <Modal
      className="add-edit-daily-news-modal"
      show={show}
      onHide={onClose}
      centered
      size="lg"
      data-testid="add-edit-daily-news-modal"
    >
      <form
        onSubmit={(e) => {
          handleSubmit(e);
        }}
        autoComplete="off"
      >
        <Modal.Header closeButton>
          <Modal.Title>
            <div>{renderModalHeading()}</div>
            <div>* Required Fields</div>
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Row>
            <Col sm={6}>
              <Row>
                <Col>
                  <Form.Label htmlFor="date">Date*</Form.Label>
                  <Form.Control
                    value={getDateFromTimestamp(values.date)}
                    type="text"
                    id="date"
                    name="date"
                    placeholder="Please select"
                    disabled
                  />
                </Col>
              </Row>
              <Row className="margin-top-1rem">
                <Col>
                  <Form.Label htmlFor="categoryId">Category*</Form.Label>
                  <Form.Control
                    name="categoryId"
                    as="select"
                    onChange={(e) => {
                      handleChange(e);
                      ga4EventTrigger(
                        'Select Category',
                        'Seafarer - Daily News - Modal',
                        categories?.filter((c) => c?.id == e?.target?.value)?.[0]?.category,
                      );
                    }}
                    value={values.categoryId}
                    onBlur={handleBlur}
                    disabled={!editable}
                    data-testid="edit-category"
                  >
                    <option value="">Please select</option>
                    {categories?.map((category) => (
                      <option value={category.id} key={category.id}>
                        {category.category}
                      </option>
                    ))}
                  </Form.Control>
                </Col>
              </Row>
              <Row className="margin-top-1rem">
                <Col>
                  <Form.Label htmlFor="headline">Headline*</Form.Label>
                  <Form.Control
                    name="headline"
                    as="textarea"
                    type="text"
                    value={values.headline}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    disabled={!editable}
                    data-testid="edit-headline"
                    maxLength={200}
                    isInvalid={!!errors.headline}
                  />
                  <Form.Control.Feedback type="invalid">{errors.headline}</Form.Control.Feedback>
                </Col>
              </Row>
              {getLastEdited()}
              {}
            </Col>
            <Col sm={6}>
              <Form.Label htmlFor="story">Story*</Form.Label>
              <Form.Control
                id="story"
                name="story"
                as="textarea"
                value={values.story}
                onChange={handleChange}
                onBlur={handleBlur}
                disabled={!editable}
                data-testid="edit-story"
                isInvalid={!!errors.story}
              />
              <Form.Control.Feedback type="invalid">{errors.story}</Form.Control.Feedback>
            </Col>
          </Row>
        </Modal.Body>
        <Modal.Footer>
          <Button
            variant="primary"
            onClick={() => onClose()}
            className="width-140"
            data-testid="add-news-cancel"
          >
            Cancel
          </Button>

          <Button
            variant="secondary"
            type="submit"
            className={
              Object.keys(errors).length || !dirty ? 'width-140 disabled-gray' : 'width-140'
            }
            disabled={Object.keys(errors).length > 0 || !dirty}
            data-testid="add-news-save"
            hidden={!editable}
          >
            {renderLoaderOnButton(loading)}
          </Button>
        </Modal.Footer>
      </form>
    </Modal>
  );
};

export default AddEditDailyNewsModal;
