import React from 'react';
import { render, cleanup, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { MemoryRouter, Route } from 'react-router-dom';
import DailyNews from './daily-news';
import AddEditDailyNewsModal from './add-edit-daily-news-modal/add-edit-daily-news-modal';
import { AccessProvider } from '@src/component/common/Access';

jest.mock('../../styleGuide');
jest.mock('../../service/user-service');
jest.mock('../../service/news-service.ts');
jest.mock('../../service/travel-service.ts');

afterEach(cleanup);

const renderDailyNewsPage = async () => {
  const { container, getByTestId, findByTestId } = render(
    <MemoryRouter initialEntries={['/seafarer/daily-news']}>
      <Route path="/seafarer/daily-news">
        <AccessProvider config={{
          seafarer: {
            edit: {
              dailyNews: true,
            },
          },
        }}>
          <DailyNews />
        </AccessProvider>
      </Route>
    </MemoryRouter>,
  );
  return { container, getByTestId, findByTestId };
};

const renderEditNewsPage = async () => {
  const { container, getByTestId, findByTestId } = render(
    <AddEditDailyNewsModal
      show={true}
      onClose={() => { }}
      onSave={() => { }}
      newsToBeEdited={{
        createdOn: '2022-12-16T05:31:31.482Z',
        modifiedOn: '2022-12-16T05:31:31.482Z',
        id: 16,
        date: '2022-12-16T00:00:00.000Z',
        categoryId: 1,
        category: 'sample1',
        status: 1,
        headline: 'vijay travels',
        story: 'uttrakhand',
        createdBy: 'Ashish Kaushik',
        modifiedBy: 'Ashish Kaushik',
      }}
      categories={[
        {
          deleted: false,
          deletedOn: null,
          deletedBy: null,
          createdOn: '2022-12-13T07:46:43.053Z',
          modifiedOn: null,
          id: 1,
          category: 'sample1',
          status: 1,
          createdBy: 'SYSTEM',
          modifiedBy: null,
        },
      ]}
      isEdit={true}
      editable={true}
    />,
  );
  return { container, getByTestId, findByTestId };
};

const openAddNewsModal = async (dailyNewsPage) => {
  const addNewsButton = dailyNewsPage.getByTestId('add-daily-news');
  fireEvent.click(addNewsButton);
  await waitFor(() => dailyNewsPage.getByTestId('add-edit-daily-news-modal'));
};

describe('Testing daily news component', () => {
  it('should component render', async () => {
    const dailyNewsPage = await renderDailyNewsPage();
    expect(dailyNewsPage.getByTestId('daily-news')).toBeInTheDocument();
  });

  it('should click event invoke on click add daily news button', async () => {
    const dailyNewsPage = await renderDailyNewsPage();
    await openAddNewsModal(dailyNewsPage);
    expect(dailyNewsPage.getByTestId('add-edit-daily-news-modal')).toBeInTheDocument();
  });
});

describe('Testing add daily news modal', () => {
  it('should click event invoke on click cancel button', async () => {
    const dailyNewsPage = await renderDailyNewsPage();
    await openAddNewsModal(dailyNewsPage);
    const cancelButton = dailyNewsPage.getByTestId('add-news-cancel');
    fireEvent.click(cancelButton);
    // Optionally you can add assertions if the modal should be closed or some behavior after cancel.
  });

  it('should click event invoke on click save button', async () => {
    const dailyNewsPage = await renderDailyNewsPage();
    await openAddNewsModal(dailyNewsPage);

    fireEvent.change(dailyNewsPage.getByTestId('edit-category'), {
      target: { value: 1, name: 'categoryId' },
    });

    fireEvent.change(dailyNewsPage.getByTestId('edit-headline'), {
      target: { value: 'headline text', name: 'headline' },
    });

    fireEvent.change(dailyNewsPage.getByTestId('edit-story'), {
      target: { value: 'story text', name: 'story' },
    });

    fireEvent.submit(dailyNewsPage.getByTestId('add-news-save'));
    // Optionally wait for any async actions after submit
    await waitFor(() => expect(dailyNewsPage.getByTestId('add-news-save')).toBeInTheDocument());
  });

  it('should edit modal component render', async () => {
    const editNewsModal = await renderEditNewsPage();
    expect(editNewsModal.getByTestId('edit-news-text')).toBeInTheDocument();
  });

  it('should edit modal save changes', async () => {
    const editNewsModal = await renderEditNewsPage();

    fireEvent.change(editNewsModal.getByTestId('edit-story'), {
      target: { value: 'story text', name: 'story' },
    });

    fireEvent.submit(editNewsModal.getByTestId('add-news-save'));
    // Optionally wait for any async actions after submit
    await waitFor(() => expect(editNewsModal.getByTestId('add-news-save')).toBeInTheDocument());
  });
  it('should throw error for non-supported xml1.0 chars', async () => {
    const editNewsModal = await renderEditNewsPage();

    fireEvent.change(editNewsModal.getByTestId('edit-story'), {
      target: { value: 'feeling awesome?. ', name: 'story' },
    });

    fireEvent.submit(editNewsModal.getByTestId('add-news-save'));
    // Optionally wait for any async actions after submit
    await waitFor(() => expect(editNewsModal.getByTestId('add-news-save')).toBeDisabled());
  });
});
