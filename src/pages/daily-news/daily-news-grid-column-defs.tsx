import React from 'react';
import { PencilFill } from 'react-bootstrap-icons';

function generateActionsCell(rowDetails) {
  const ButtonLink = () => {
    const rowDate = new Date(rowDetails.original.date);
    const currentDate = new Date();

    return (
      <div className='action'>
        {rowDate.setHours(0, 0, 0, 0) === currentDate.setHours(0, 0, 0, 0) ? (
          <PencilFill className="edit-icon" data-testid="edit-icon" />
        ) : null}
      </div>
    );
  };

  return <ButtonLink row={rowDetails} />;
}

export const dailyNewsGridColumnDefs = (categories) => [
  {
    Header: 'Date',
    customHeaderCellStyle: {
      paddingLeft: '0px'
    },
    Cell: ({ cell }) => {
      const newsDate = new Date(cell.value);
      return (
        <>
          {newsDate.toLocaleDateString('en-GB', {
            day: 'numeric',
            month: 'short',
            year: 'numeric',
          })}
        </>
      );
    },
    Footer: null,
    disableSortBy: true,
    accessor: 'date',
    width: 70,
    maxWidth: 70,
  },
  {
    Header: 'Category',
    Footer: null,
    disableSortBy: true,
    accessor: 'category',
    width: 240,
    maxWidth: 240,
  },
  {
    Header: 'Headline',
    Footer: null,
    disableSortBy: true,
    accessor: 'headline',
    width: 650,
    maxWidth: 650,
  },
  {
    Header: 'Action',
    customHeaderCellStyle: {
      textAlign: 'end',
      paddingRight: '0px'
    },
    id: 'action',
    Footer: null,
    disableSortBy: true,
    Cell: (tableInstance) => generateActionsCell(tableInstance.row, categories),
    maxWidth: 40,
  },
];
