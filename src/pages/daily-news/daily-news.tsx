import { Grid } from '../../component/grid/grid';
import React, { useEffect, useState } from 'react';
import { Button } from 'react-bootstrap';
import AddEditDailyNewsModal from './add-edit-daily-news-modal/add-edit-daily-news-modal';
import './daily-news.scss';
import { dailyNewsGridColumnDefs } from './daily-news-grid-column-defs';
import { getCategoryList, getNews } from '../../service/news-service';
import { isEmpty } from 'lodash';
import { toast } from 'react-toastify';
import { AxiosError } from 'axios';
import AccessHandlerWrapper from '../../component/common/AccessHandlerWrapper';
import { onHandleError } from '../../model/utils';
import { MODULES } from '@src/util';
import moment from 'moment-timezone';
import { useAccess } from '@src/component/common/Access';

const DailyNews = ({ ga4react }) => {
  const { roleConfig } = useAccess();
  const [showAddEditNewsModal, setShowAddEditNewsModal] = useState(false);
  const [newsList, setNewsList] = useState([]);
  const [newsResponse, setNewsResponse] = useState({});
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [addEditSaveTriggered, setAddEditSaveTriggered] = useState(false);
  const [pageSize, setPageSize] = useState(10);
  const [pageIndex, setPageIndex] = useState(0);
  const [sortBy, setSortBy] = useState([]);
  const [isModalEditable, setIsModalEditable] = useState(true);
  const [newsToBeEdited, setNewsToBeEdited] = useState({ date: new Date() });
  const [isEditModal, setIsEditModal] = useState(false);
  const startTimer = (new Date()).getTime();

  const ga4EventTrigger = (action, category, label) => {
    try {
      ga4react?.event(action, label, category, false);
    } catch (error) {
      console.log(error);
    }
  };

  const prepareAndSetData = (data) => {
    setNewsResponse(data);
    const newsResponseList = data.response.map((news) => {
      const currentNews = {
        id: news.id,
        headline: news.headline,
        story: news.story,
        date: news.newsDate,
        category: news.category,
        categoryId: news.categoryId,
        createdBy: news.createdBy,
        createdOn: news.createdAt,
        updatedBy: news.updatedBy,
        updatedAt: news.updatedAt,
      };
      return currentNews;
    });
    setNewsList(newsResponseList);
  };

  const onRowClick = (row) => {
    return {
      onClick: (e) => {
        ga4EventTrigger('Edit Daily News', 'Seafarer - Daily News - Modal', 'Edit');
        const rowDate = new Date(row.original.date);
        const currentDate = new Date();
        if (rowDate.setHours(0, 0, 0, 0) === currentDate.setHours(0, 0, 0, 0)) {
          setIsModalEditable(true);
          setIsEditModal(true);
        } else {
          setIsModalEditable(false);
        }
        setNewsToBeEdited({ ...row.original });
        setShowAddEditNewsModal(true);
      },
    };
  };

  useEffect(() => {
    (async () => {
      try {
        setLoading(true);
        const { data } = await getCategoryList();
        if (data.error) {
          onHandleError(data.message, setLoading);
          return;
        }
        setCategories(data.response);
      } catch (error) {
        const { message } = error as AxiosError;
        toast.error(`Error: ${message}`);
        setLoading(false);
      }
    })();
  }, []);

  useEffect(() => {
    if (categories?.length) {
      (async () => {
        await fetchData({
          pageIndex: pageIndex,
          pageSize: pageSize,
          sortBy: sortBy,
        });
      })();
    }
  }, [categories, addEditSaveTriggered]);

  const fetchData = async (queryParams) => {
    try {
      setLoading(true);
      // reset state
      setPageIndex(queryParams.pageIndex);
      setPageSize(queryParams.pageSize);
      setSortBy(queryParams.sortBy);
      const { data } = await getNews({ ...queryParams });
      if (data.error) {
        onHandleError(data.message, setLoading);
        return;
      }
      prepareAndSetData(data);
      setLoading(false);
    } catch (error) {
      const { message } = error as AxiosError;
      toast.error(`Error: ${message}`);
      setLoading(false);
    }
  };

  useEffect(() => {
    return () => {
      const currentTime = (new Date()).getTime();
      const elapsedTime = currentTime - startTimer;
      ga4EventTrigger('Time Spent', 'Seafarer - Daily News', moment.utc(elapsedTime).format("mm:ss").toString());
    };
  }, []);

  return (
    <AccessHandlerWrapper hasRoleAccess={roleConfig?.seafarer.edit.dailyNews}>
      <div className="daily-news container" data-testid="daily-news">
        <div className="heading-container">
          <span className="heading">Daily News</span>
          <Button
            variant="outline-primary"
            data-testid="add-daily-news"
            onClick={() => {
              setShowAddEditNewsModal(true);
              ga4EventTrigger('Add Daily News', 'Seafarer - Daily News - Modal', 'Add');
            }}
            className="add-news-btn"
          >
            Add Daily News
          </Button>
        </div>
        {showAddEditNewsModal && (
          <AddEditDailyNewsModal
            isEdit={isEditModal}
            editable={isModalEditable}
            show={showAddEditNewsModal}
            onClose={() => {
              setShowAddEditNewsModal(false);
              setIsModalEditable(true);
              setIsEditModal(false);
              setNewsToBeEdited({ date: new Date() });
            }}
            onSave={() => {
              setShowAddEditNewsModal(false);
              setAddEditSaveTriggered(!addEditSaveTriggered);
              setIsModalEditable(true);
              setIsEditModal(false);
              setNewsToBeEdited({ date: new Date() });
              ga4EventTrigger(`Save ${isEditModal ? 'Edited' : 'Added'} Daily News`, 'Seafarer - Daily News - Modal', 'Save');
            }}
            categories={categories}
            newsToBeEdited={newsToBeEdited}
            ga4EventTrigger={ga4EventTrigger}
          ></AddEditDailyNewsModal>
        )}
        <Grid
          isLoading={loading}
          columns={dailyNewsGridColumnDefs(categories)}
          data={newsList}
          isManualSort={false}
          showTopPagination={true}
          totalCount={!isEmpty(newsResponse) ? newsResponse['count'] : 0}
          showResultsCount={true}
          resultsCount={!isEmpty(newsResponse) ? newsResponse['count'] : 0}
          showPaginationLabel={false}
          fetchData={fetchData}
          defaultPageSize={10}
          onRowClick={onRowClick}
          ga4EventTrigger={ga4EventTrigger}
          calledFromModule={MODULES.DAILY_NEWS}
        ></Grid>
      </div>
    </AccessHandlerWrapper>
  );
};

export default DailyNews;
