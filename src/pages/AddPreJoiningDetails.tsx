import React, { useState, useMemo, useEffect } from 'react';
import { useHistory, useParams } from 'react-router-dom';
import { Container, Form, Row, Col, InputGroup } from 'react-bootstrap';
import { BreadcrumbHeader } from '../component/BreadcrumpHeader';
import * as seafarerService from '../service/seafarer-service';
import { Formik, FormikValues } from 'formik';
import styleGuide from '../styleGuide';
const { Icon } = styleGuide;
import './scss/add-pre-joining-details.scss';
import { JOINING_ALLOWANCES, JOINING_DEDUCTIONS } from '../constants/pre-joining-details';
import BottomButton from '../component/advanced_search/BottomButton';
import Spinner from '../component/common/Spinner';
import { ALLOWED_ALLOTMENT_STATUSES, seafarerStatus } from '../model/constants';
import {
  // eslint-disable-next-line no-unused-vars
  SeafarerJoiningSpendingsPayload,
  // eslint-disable-next-line no-unused-vars
  SeafarerPreJoiningPayload,
} from '../types/wagesInterfaces';
import { Seafarer, SeafarerStatusHistory } from '../types/seafarerInterfaces';
import { seafarerPreJoiningEditSchema } from '../model/SeafarerSchemaValidation';
import { objectDifference } from '../util/form-utils';
// eslint-disable-next-line no-unused-vars
import GA4React from 'ga-4-react';
import _ from 'lodash';
import AccessHandlerWrapper from '../component/common/AccessHandlerWrapper';
import ErrorDisplayModal from '../component/common/ErrorDisplayModal';
import { DEFAULT_CURRENCY_UNIT } from '@src/constants/seafarer-wages';
import { useAccess } from '@src/component/common/Access';

const emptyAllotmentDetail = {
  monthly_allotment: '0.00',
  first_allotment: '0.00',
};

// eslint-disable-next-line no-undef
interface TitleCompProps {
  // eslint-disable-next-line no-undef
  title: string;
}

// eslint-disable-next-line no-undef
interface Props {
  // eslint-disable-next-line no-undef
  ga4react: GA4React;
}

const SectionTitleComponent = ({ title }: TitleCompProps) => {
  return (
    <div>
      <hr className="section_line mt-10" />
      <h5 className="add_pre_joining_details__section-title mb-3"> {title ?? ''}</h5>
    </div>
  );
};

const AddPreJoiningDetails = ({ ga4react }: Props) => {
  const history = useHistory();
  const { roleConfig } = useAccess();
  const { seafarerId } = useParams<{ seafarerId: string }>();
  const [seafarer, setSeafarer] = useState<Seafarer>();
  const [loading, setLoading] = useState(true);
  const [joiningExpensesDeductions, setJoiningExpensesDeductions] = useState<any[]>([]);
  const [allotmentDetails, setAllotmentDetails] = useState<any>(emptyAllotmentDetail);
  const [initialData, setInitialData] = useState<FormikValues>({});
  const [isJoiningDataExist, setIsJoiningDataExist] = useState(false);
  const person = seafarer?.seafarer_person;
  const [statusHistory, setStatusHistory] = useState<SeafarerStatusHistory>();
  const [formSchema, setFormSchema] = useState(null);
  const [isDisabled, setIsDisabled] = useState(false);
  const [isPageViewInvoked, setIsPageViewInvoked] = useState(false);
  const [modalMessage, setModalMessage] = useState(null);

  const isShowAllotment = person?.nationality?.alpha3_code === 'PHL';

  const joiningExpensesDeductionsEditDisabled =
    statusHistory?.seafarer_journey_status === seafarerStatus.SIGNED_ON ||
    statusHistory?.seafarer_journey_status === seafarerStatus.TRAVELLING;

  const allotmentDetailsEditDisabled = statusHistory
    ? !ALLOWED_ALLOTMENT_STATUSES.includes(statusHistory?.seafarer_journey_status)
    : true;

  useEffect(() => {
    if (!isPageViewInvoked) {
      try {
        ga4react?.pageview(history.location.pathname, '', 'Seafarer List');
        setIsPageViewInvoked(true);
      } catch (e) {
        console.log(e);
      }
    }
  }, [isPageViewInvoked]);

  useEffect(() => {
    loadSeafarer(parseInt(seafarerId));
  }, [seafarerId]);

  useEffect(() => {
    (async () => {
      setLoading(true);
      const preJoiningData = await fetchPreJoiningData();
      if (preJoiningData?.seafarer_status_history) {
        setStatusHistory(preJoiningData.seafarer_status_history);
      }
      let expenseDeductionResponseFiltered = [];
      let fetchedJoiningDeduction = [];
      let formData = {};
      const expenseDeductionResponse = preJoiningData;
      if (expenseDeductionResponse?.seafarer_joining_spendings) {
        expenseDeductionResponseFiltered = expenseDeductionResponse.seafarer_joining_spendings;
        fetchedJoiningDeduction = expenseDeductionResponseFiltered.map((p: any) => {
          return {
            id: p.payhead.id,
            joining_spendings_id: p.id,
            head_name: p.payhead.head_name ?? null,
            amount: p.amount ?? '0.00',
            category: p.payhead.category,
            remark: p.remarks,
          };
        });
        setIsJoiningDataExist(true);
      } else if (expenseDeductionResponse?.seafarer_joining_spendings_payhead) {
        expenseDeductionResponseFiltered =
          expenseDeductionResponse.seafarer_joining_spendings_payhead;
        fetchedJoiningDeduction = expenseDeductionResponseFiltered.map((p: any) => {
          return {
            id: p.id,
            head_name: p.head_name ?? null,
            amount: p?.default_value ?? '0.00',
            category: p.category,
            remark: '',
          };
        });
      }
      if (expenseDeductionResponse?.seafarer_allotment) {
        const seafarerAllotment = expenseDeductionResponse.seafarer_allotment;
        const allotmentDetailsResponse = {
          id: seafarerAllotment.id,
          monthly_allotment:
            seafarerAllotment.monthly_allotment ?? emptyAllotmentDetail.monthly_allotment,
          first_allotment:
            seafarerAllotment.first_allotment ?? emptyAllotmentDetail.first_allotment,
        };
        setAllotmentDetails(allotmentDetailsResponse);
        formData = {
          monthly_allotment: allotmentDetailsResponse.monthly_allotment,
          first_allotment: allotmentDetailsResponse.first_allotment,
        };
      } else {
        formData = {
          monthly_allotment: emptyAllotmentDetail.monthly_allotment,
          first_allotment: emptyAllotmentDetail.first_allotment,
        };
      }

      if (fetchedJoiningDeduction) {
        setJoiningExpensesDeductions(fetchedJoiningDeduction);
        const resAmounts = fetchedJoiningDeduction.map((e: any) => {
          let key = `amount_${e.id}`;
          return [key, e.amount];
        });
        const amounts = Object.fromEntries(resAmounts);
        const resRemarks = fetchedJoiningDeduction.map((e: any) => {
          let key = `remark_${e.id}`;
          return [key, e.remark];
        });
        const remarks = Object.fromEntries(resRemarks);
        formData = {
          ...formData,
          ...amounts,
          ...remarks,
        };
        setInitialData(formData);
        const schema = seafarerPreJoiningEditSchema(fetchedJoiningDeduction);
        setFormSchema(schema);
      }
      setLoading(false);
    })();
  }, []);

  const fetchPreJoiningData = async () => {
    try {
      const response = await seafarerService.getSeafarerPreJoiningDetails(seafarerId);
      if (response.status === 200) {
        return response.data;
      }
    } catch (err) {
      console.log(err);
      return;
    }
  };

  const loadSeafarer = (id: number) => {
    (async () => {
      setLoading(true);
      try {
        const { data } = await seafarerService.getSeafarer(id);
        setSeafarer(data);
      } catch (error) {
        console.log('## error', error);
      } finally {
        setLoading(false);
      }
    })();
  };

  const ga4EventTrigger = (action: string, category: string, label: string) => {
    try {
      ga4react?.event(action, _.toString(label), category, false);
    } catch (error) {
      console.log(error);
    }
  };

  const eventTracker = (type: string, value: string | number) => {
    switch (type) {
      case 'saveButton':
        ga4EventTrigger(
          'Save Edit Pre Joining',
          'Seafarer Pre Joining',
          _.toString(seafarer?.hkid),
        );
        break;
      case 'closeButton':
        ga4EventTrigger(
          'Close Edit Pre Joining',
          'Seafarer Pre Joining',
          _.toString(seafarer?.hkid),
        );
        break;
      default:
        ga4EventTrigger('Click', 'Seafarer Pre Joining', value);
        break;
    }
  };

  const breadCrumbsItems = useMemo(
    () => [
      { title: 'Seafarer', label: 'To List Page', link: '/seafarer/passed' },
      {
        title: person
          ? `${person?.first_name} ${person?.last_name} (${seafarer?.seafarer_rank?.unit}) (${seafarer?.hkid})`
          : '- - -',
        label: 'Details',
        link: '#',
      },
      {
        title: 'Pre-Joining',
        label: 'Pre-Joining',
        link: '#',
      },
    ],
    [person],
  );

  const handleClose = () => {
    eventTracker('closeButton', '');
    history.push(`/seafarer/details/${seafarerId}/pre-joining`);
  };

  const truncateDecimals = (num: string | number, digits: number) => {
    const numS = num.toString(),
      decPos = numS.indexOf('.'),
      substrLength = decPos === -1 ? numS.length : 1 + decPos + digits,
      trimmedResult = numS.substring(0, substrLength),
      finalResult = isNaN(parseFloat(trimmedResult)) ? '0' : trimmedResult;
    return parseFloat(finalResult);
  };

  const convertFormValuesToJoiningSpendings = (values: Object, updatedValues?: Object) => {
    return Object.keys(updatedValues ?? values)
      .filter((e) => {
        return e.split('_')[0] === 'amount' || e.split('_')[0] === 'remark';
      })
      .map((e) => {
        const formValues = updatedValues ?? values;
        const id = parseInt(e.split('_')[1]);
        const nullAmount = 0;
        const amt = formValues[`amount_${id}` as keyof typeof formValues] ?? nullAmount.toFixed(2);
        const remark =
          formValues[`remark_${id}` as keyof typeof formValues] ??
          values[`remark_${id}` as keyof typeof values];
        let res: SeafarerJoiningSpendingsPayload = {
          payhead_id: id,
          amount: truncateDecimals(amt?.toString(), 2),
          remarks: remark?.toString(),
        };
        if (isJoiningDataExist) {
          const joiningDetailsData = joiningExpensesDeductions.find((e) => e.id === id);
          const joiningDetailsId = joiningDetailsData?.joining_spendings_id;
          res = {
            ...res,
            id: joiningDetailsId,
          };
        }
        return res;
      });
  };

  const onSubmitValues = async (values: FormikValues) => {
    setLoading(true);
    setIsDisabled(true);
    if (!isJoiningDataExist) {
      let payload: SeafarerPreJoiningPayload = {
        seafarer_joining_spendings: [],
      };

      if (isShowAllotment && !allotmentDetailsEditDisabled) {
        const seafarer_allotment = {
          monthly_allotment: truncateDecimals(values.monthly_allotment.toString(), 2),
          first_allotment: truncateDecimals(values.first_allotment.toString(), 2),
        };
        payload = {
          ...payload,
          seafarer_allotment,
        };
      }

      if (!joiningExpensesDeductionsEditDisabled) {
        const seafarer_joining_spendings = convertFormValuesToJoiningSpendings(values);
        payload.seafarer_joining_spendings = seafarer_joining_spendings;
      }

      try {
        const res = await seafarerService.createSeafarerPreJoiningDetails(
          seafarerId,
          statusHistory?.id,
          payload,
        );
        if (res?.status === 201) {
          handleClose();
        }
      } catch (err) {
        console.error(err);
        setModalMessage(err?.response?.data ?? 'Something went wrong, Please try again later');
        setIsDisabled(false);
      } finally {
        setLoading(false);
      }
    } else {
      let payload: SeafarerPreJoiningPayload = {
        seafarer_joining_spendings: [],
      };
      if (isShowAllotment && !allotmentDetailsEditDisabled) {
        const seafarer_allotment = {
          id: allotmentDetails?.id,
          monthly_allotment: truncateDecimals(values.monthly_allotment.toString(), 2),
          first_allotment: truncateDecimals(values.first_allotment.toString(), 2),
        };
        payload = {
          ...payload,
          seafarer_allotment,
        };
      }

      const patchValues = objectDifference(initialData, values);
      if (!joiningExpensesDeductionsEditDisabled && Object.keys(patchValues).length !== 0) {
        const seafarer_joining_spendings = convertFormValuesToJoiningSpendings(values, patchValues);
        payload.seafarer_joining_spendings = seafarer_joining_spendings;
      }

      try {
        const res = await seafarerService.patchSeafarerPreJoiningDetails(
          seafarerId,
          statusHistory?.id,
          payload,
        );
        if (res?.status === 201) {
          handleClose();
        }
      } catch (err) {
        console.error(err);
        setModalMessage(err?.response?.data ?? 'Something went wrong, Please try again later');
        setIsDisabled(false);
      } finally {
        setLoading(false);
      }
    }
  };
  const onHideModalMessage = () => setModalMessage(null);
  const currencyUnit =
    statusHistory?.recommended_wages_unit?.toUpperCase() ?? DEFAULT_CURRENCY_UNIT;
  return (
    <AccessHandlerWrapper hasRoleAccess={roleConfig?.seafarer?.edit?.wages}>
      <Container className="add-pre-joining-details-container">
        <Row className="closeIcon">
          <BreadcrumbHeader items={breadCrumbsItems} activeItem={'Pre-Joining'} onClick={null} />
          <Icon icon="close" size={25} onClick={handleClose} />
        </Row>

        <ErrorDisplayModal onHideModalMessage={onHideModalMessage} modalMessage={modalMessage} />
        {!loading && Object.keys(initialData).length > 0 && statusHistory ? (
          <Formik
            validationSchema={formSchema}
            initialValues={initialData}
            onSubmit={onSubmitValues}
          >
            {({ handleSubmit, handleChange, handleBlur, values, errors, setFieldValue, dirty }) => {
              const onInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
                const targetName = event?.target?.name;
                const targetValue = event?.target?.value;

                if (targetName.startsWith('amount')) {
                  const nullAmount = 0;
                  setFieldValue(targetName, targetValue || nullAmount);
                } else {
                  setFieldValue(targetName, targetValue || undefined); //to make sure undefined is set if value is empty string
                }
              };

              const handleFocus = (event: React.FocusEvent<HTMLInputElement>) =>
                event?.target?.select();

              const blockInvalidChar = (e: React.KeyboardEvent<HTMLInputElement>) => {
                if (['e', 'E', '+', '-'].includes(e.key)) {
                  e.preventDefault();
                }
                const targetValue = e?.target?.value;
                if (targetValue === '0' && ['0'].includes(e.key)) {
                  e.preventDefault();
                }
              };

              return (
                <Form
                  onSubmit={(e) => {
                    e.preventDefault();
                    eventTracker('saveButton', '');
                    handleSubmit();
                  }}
                >
                  {isShowAllotment && (
                    <>
                      <SectionTitleComponent title={'Allotment Details'.toUpperCase()} />

                      <Row className="ml-1">
                        {allotmentDetails && (
                          <>
                            <Form.Group className="col-md-6 col-sm-6 mt-2">
                              <Form.Label className="font-weight-bold row mb-0">
                                Monthly Allotment
                              </Form.Label>
                              <Row>
                                <Col className="p-0 col-md-9">
                                  <InputGroup>
                                    <InputGroup.Prepend>
                                      <InputGroup.Text>{currencyUnit}</InputGroup.Text>
                                    </InputGroup.Prepend>
                                    <Form.Control
                                      className="wages-input"
                                      type="number"
                                      name="monthly_allotment"
                                      value={values.monthly_allotment}
                                      min={0}
                                      step={0.01}
                                      onChange={onInputChange}
                                      onBlur={handleBlur}
                                      onFocus={handleFocus}
                                      onKeyDown={blockInvalidChar}
                                      disabled={allotmentDetailsEditDisabled}
                                    />
                                  </InputGroup>
                                  {errors.monthly_allotment && (
                                    <div className="invalid-feedback d-block">
                                      {typeof errors.monthly_allotment === 'string'
                                        ? errors.monthly_allotment
                                        : JSON.stringify(errors.monthly_allotment)}
                                    </div>
                                  )}
                                </Col>
                              </Row>
                            </Form.Group>
                            <Form.Group className="col-md-6 col-sm-6 mt-2">
                              <Form.Label className="font-weight-bold row mb-0">
                                First Month Allotment
                              </Form.Label>
                              <Row>
                                <Col className="p-0 col-md-9">
                                  <InputGroup>
                                    <InputGroup.Prepend>
                                      <InputGroup.Text>{currencyUnit}</InputGroup.Text>
                                    </InputGroup.Prepend>
                                    <Form.Control
                                      className="wages-input "
                                      type="number"
                                      name="first_allotment"
                                      value={values.first_allotment}
                                      min={0}
                                      step={0.01}
                                      onChange={onInputChange}
                                      onBlur={handleBlur}
                                      onFocus={handleFocus}
                                      onKeyDown={blockInvalidChar}
                                      disabled={allotmentDetailsEditDisabled}
                                    />
                                  </InputGroup>
                                  {errors.first_allotment && (
                                    <div className="invalid-feedback d-block">
                                      {typeof errors.first_allotment === 'string'
                                        ? errors.first_allotment
                                        : JSON.stringify(errors.first_allotment)}
                                    </div>
                                  )}
                                </Col>
                              </Row>
                            </Form.Group>
                          </>
                        )}
                      </Row>
                    </>
                  )}

                  <SectionTitleComponent title={'Joining Expenses'.toUpperCase()} />

                  <Row className="ml-1">
                    {joiningExpensesDeductions
                      .filter((item) => item.category === JOINING_ALLOWANCES)
                      .map((data) => {
                        const amtField = `amount_${data.id}`;
                        const remarkField = `remark_${data.id}`;
                        return (
                          <Form.Group key={data.id} className="col-md-6 col-sm-6 mt-2">
                            <Form.Label className="font-weight-bold row mb-0">
                              {data.head_name}
                            </Form.Label>
                            <Row>
                              <Col className="col-md-3 p-0">
                                <InputGroup>
                                  <InputGroup.Prepend>
                                    <InputGroup.Text>{currencyUnit}</InputGroup.Text>
                                  </InputGroup.Prepend>
                                  <Form.Control
                                    className="wages-input mr-3"
                                    type="number"
                                    name={amtField}
                                    value={values[amtField]}
                                    min={0}
                                    step={0.01}
                                    onChange={onInputChange}
                                    onBlur={handleBlur}
                                    onFocus={handleFocus}
                                    onKeyDown={blockInvalidChar}
                                    disabled={joiningExpensesDeductionsEditDisabled}
                                  />
                                </InputGroup>
                                {errors[amtField] && (
                                  <div className="invalid-feedback d-block">
                                    {errors[amtField]?.toString()}
                                  </div>
                                )}
                              </Col>
                              <Form.Control
                                as="textarea"
                                className="col-md-6"
                                type="text"
                                name={remarkField}
                                placeholder="Enter your remarks here"
                                value={values[remarkField]}
                                onChange={handleChange}
                                rows={1}
                                disabled={joiningExpensesDeductionsEditDisabled}
                              />
                            </Row>
                          </Form.Group>
                        );
                      })}
                  </Row>

                  <SectionTitleComponent title={'Joining Deduction'.toUpperCase()} />

                  <Row className="ml-1 bottom-margin">
                    {joiningExpensesDeductions
                      .filter((item) => item.category === JOINING_DEDUCTIONS)
                      .map((data) => {
                        const amtField = `amount_${data.id}`;
                        const remarkField = `remark_${data.id}`;
                        return (
                          <Form.Group key={data.id} className="col-md-6 col-sm-6 mt-2">
                            <Form.Label className="font-weight-bold row mb-0">
                              {data.head_name}
                            </Form.Label>
                            <Row>
                              <Col className="col-md-3 p-0">
                                <InputGroup>
                                  <InputGroup.Prepend>
                                    <InputGroup.Text>{currencyUnit}</InputGroup.Text>
                                  </InputGroup.Prepend>
                                  <Form.Control
                                    className="wages-input mr-3"
                                    type="number"
                                    name={amtField}
                                    value={values[amtField]}
                                    min={0}
                                    step={0.01}
                                    onChange={onInputChange}
                                    onBlur={handleBlur}
                                    onFocus={handleFocus}
                                    onKeyDown={blockInvalidChar}
                                    disabled={joiningExpensesDeductionsEditDisabled}
                                  />
                                </InputGroup>
                                {errors[amtField] && (
                                  <div className="invalid-feedback d-block">
                                    {errors[amtField]?.toString()}
                                  </div>
                                )}
                              </Col>
                              <Form.Control
                                as="textarea"
                                className="col-md-6"
                                type="text"
                                name={remarkField}
                                placeholder="Enter your remarks here"
                                value={values[remarkField]}
                                onChange={handleChange}
                                rows={1}
                                disabled={joiningExpensesDeductionsEditDisabled}
                              />
                            </Row>
                          </Form.Group>
                        );
                      })}
                  </Row>

                  <BottomButton
                    title={'Save'}
                    testID="form-seafarer-save-button"
                    type="submit"
                    disabled={isDisabled || !dirty}
                  />
                </Form>
              );
            }}
          </Formik>
        ) : (
          <div className="mt-5">
            {' '}
            <Spinner />{' '}
          </div>
        )}
      </Container>
    </AccessHandlerWrapper>
  );
};

export default AddPreJoiningDetails;
