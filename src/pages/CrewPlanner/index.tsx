/* eslint-disable react/function-component-definition */
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { isEmpty, isInteger, keyBy } from 'lodash';
import { <PERSON><PERSON>, Spinner } from 'react-bootstrap';
import { useHistory, useParams } from 'react-router-dom';
import { useDebouncedCallback } from 'use-debounce';
import moment from 'moment';
import qs from 'qs';
import { getStatus } from '@src/component/CrewPlanner/Column';
import useMultiApiLoader from '@src/hooks/useMultiApiLoader';
import {
  cleanUpFilter,
  generateApiQueryFromFilterObject,
  generateFilterObjectFromApiQuery,
  processApiQuery,
} from '@src/component/CrewPlanner/DynamicFilter/helper';
import { vesselStatuses } from '@src/constants/status';
import { qsDefaultOptions } from '@src/util';
import AvailableSeafarer, {
  getAvailableSeafarerFilter,
} from '../../component/CrewPlanner/AvailableSeafarer';
import UnplanRelief from '../../component/CrewPlanner/UnplanRelief';
import ConfirmRecommendModal from '../../component/CrewPlanner/ConfirmRecommendModal';
import { updateCrewPlan, createCrewPlan, plannerRemark } from '../../service/crew-planner';
import { queryVesselOwnership } from '../../service/vessel-service';
import { addSortOrPaginateParams } from '../../util/advance-search/search-query';
import seafarerService from '../../service/seafarer-service';
import ScrollArrow from '../../component/BackToTopButton';
import { seafarerStatus, shipPartyType } from '../../model/constants';
import AccessHandlerWrapper from '../../component/common/AccessHandlerWrapper';
import SeafarerToRelieve, {
  getSeafarerToRelieveFilter,
} from '../../component/CrewPlanner/SeafarerToRelieve';
import { getPageSort } from '../../util/local-storage-helper';
import httpService from '../../service/http-service';

import TabWrapper from '../../component/common/TabWrapper';
import {
  ListTabData,
  ON_LEAVE_SEAFARER,
  SEAFARERS_TO_RELIEVE,
  STATUS,
  CREW_PLANNING_STATUS,
  CREW_PLANNING_TYPE,
  CREW_PLANNING_STATUS_MAPPING,
  CREW_PLANNING_STATUSES,
  OPERATION_CANCELLED_ERROR_TEXT,
  SECOND_TABLE_FILTERS,
} from '../../constants/crewPlanner';

import getEventTracker from './eventTracker';
import PlannerRemarkModal from '../../component/CrewPlanner/PlannerRemarkModal';
import * as utils from './utils';
import { LoaderState, Pagination, type QueryParams, type SortBy } from './interface';
import {
  filterDataAPI,
  getRemarks,
  shouldIncludeMissingRanks,
  getTabName,
  updateNestedKey,
  prepareContractExpirySeafarers,
} from './model';
import './style.scss';
import Paris2Modal from '@src/component/common/Paris2Modal';
import { getVesselType } from './utils';
import PlannerKPI from '@src/component/CrewPlanner/PlannerKPI';
import { dateAsDash } from '@src/model/utils';
import { useStatusState } from '@src/hooks/useStatusState';

const {
  checkMissingPersonnel,
  checkAdditionalRequest,
  getSeafarerRank,
  getSeafarerVesselId,
  getSeafarerVesselOwnershipId,
  initialPagination,
} = utils;
const REMOVE_KEYS_FROM_COMPARE_FOR_KPI = [
  'limit',
  'offset',
  'orderBy',
  'seafarer_person.current_journey_status',
];
const CrewPlanner = ({
  keycloak,
  roleConfig,
  ga4react,
  isVisible,
  plannerRanks,
  vesselTypesList,
  setAlertMessage,
  alertRef,
  setTimeoutError,
}) => {
  const plannerRankIds = plannerRanks?.map((rank) => rank?.rank_id) ?? [];
  const isPlanner = plannerRanks?.length > 0;
  const hasEditAccess = roleConfig?.seafarer?.edit?.crewPlannerSeafarer;
  /* Ref */
  const selectedReliefSeafarer = useRef();
  const selectedSeafarerForRemarks = useRef();

  /* States */
  const history = useHistory();
  const [loading, setLoading] = useState(false);
  const [seafarersTotalCount, setSeafarersTotalCount] = useState(0);
  const [extraSeafarerCount, setExtraSeafarerCount] = useState(0);
  const { tab } = useParams();
  const [activeKey, setActiveKey] = useState(tab || SEAFARERS_TO_RELIEVE);
  const [sortBy, setSortBy] = useState(utils.getDefaultSort());
  const [apiQuery, setApiQuery] = useState('');
  const [selectedSeafarer, setSelectedSeafarer] = useState(null);
  const [selectedSeafarerToBeReliever, setSelectedSeafarerToBeReliever] = useState(null);
  const [filteredSeafarers, setFilteredSeafarers] = useState([]);
  const [vesselOwnershipData, setVesselOwnershipData] = React.useState([]);
  const [pagination, setPagination] = React.useState<Pagination>(utils.initialPagination);
  const [isPageViewInvoked, setIsPageViewInvoked] = useState(false);
  const [isOpenUnplanReliefModal, setIsOpenUnplanReliefModal] = useState(false);
  const [isOpenConfirmRecommendModal, setIsOpenConfirmRecommendModal] = useState(false);
  const [isOpenRemarkModal, setIsOpenRemarkModal] = useState(false);
  const [startTime, setStartTime] = useState(0);
  const { dataStates, loadingStates } = useMultiApiLoader(filterDataAPI);
  const [dropDownData, setDropDownData] = useState({});
  const [secondTableAPIQuery, setSecondTableAPIQuery] = useState('');
  const [relieverSectionData, setRelieverSectionData] = useState(utils.initialSectionData);
  const [secondTableLoadType, setSecondTableLoadType] = useState(
    Object.values(SECOND_TABLE_FILTERS)[0],
  );
  const [isSecondTableLoadTypeChanged, setIsSecondTableLoadTypeChanged] = useState(false);
  const [loaders, setLoaders] = React.useState<LoaderState>({});
  const [suggestedSeafarersLoading, setSuggestedSeafarersLoading] = useState(false);
  const [searchNotClicked, setSearchNotClicked] = useState(false);
  const {
    avlSeafarerEventTracker,
    contractExpEventTracker,
    eventTracker,
    crewPlannerEventTracker,
  } = getEventTracker({
    ga4react,
    getTabName,
    activeKey,
  });
  const [reliverSectionPagination, setReliverSectionPagination] = useState(
    utils.DEFAULT_RELIVER_SECTION_PAGINATION,
  );
  const [selectedFilters, setSelectedFilters] = useState({});
  const [ringfencingOwners, setRingfencingOwners] = useState([]);
  const [isAPIQueryInitialized, setIsAPIQueryInitialized] = React.useState<boolean>(false);
  /* Constants */
  const crewPlanningStatus =
    selectedFilters?.[activeKey]?.crew_planning_status?.[0]?.split(',')?.[0];
  const isClubbedOrUnclubbed = [STATUS.club_confirmed, STATUS.clubbed, STATUS.unclubbed].includes(
    crewPlanningStatus,
  );
  const relieverSeafarerRank =
    activeKey === SEAFARERS_TO_RELIEVE
      ? getSeafarerRank(selectedSeafarer)
      : selectedSeafarer?.seafarer_rank?.value ?? '';

  const memoizedPagination = React.useMemo(
    () => ({ ...(pagination ?? {}), sortBy: sortBy[activeKey] }),
    [JSON.stringify({ pagination, sortBy }), activeKey],
  );
  const memoizedReliverSectionPagination = React.useMemo(
    () => reliverSectionPagination[activeKey],
    [JSON.stringify(reliverSectionPagination), activeKey],
  );
  const [dueIn30Days, setDueIn30Days] = useStatusState(0, 'Due in 30 Days');
  const [overdueSeafarers, setOverdueSeafarers] = useStatusState(0, 'Overdue Seafarers');
  const [missingRanks, setMissingRanks] = useStatusState(0, 'Missing Ranks');
  const [notContacted, setNotContacted] = useStatusState(0, 'Not Contacted (Last 15 Days)');
  const [availableNotPlanned, setAvailableNotPlanned] = useStatusState(
    0,
    'Available (Not Planned)',
  );
  const [kpiQueryHistory, setKPIQueryHistory] = useState({
    due_in_30_days: {},
    overdue_seafarer: {},
    missing_ranks: {},
    not_contacted: {},
    available: {},
  });
  /* hooks */
  useEffect(() => {
    const activeTab = tab || SEAFARERS_TO_RELIEVE;
    setActiveKey(activeTab);
  }, [activeKey, tab]);

  useEffect(() => {
    cleanupFirstTable();
    let defaultSort = getPageSort(activeKey);
    if (!defaultSort?.length) {
      defaultSort = utils.getDefaultSort()[activeKey];
    }
    setSortByWrapper(activeKey, defaultSort);
  }, [activeKey]);

  useEffect(() => {
    if (!Object.values(loadingStates).some((v) => v) && !isEmpty(vesselOwnershipData)) {
      const vesselOwnershipDataForDropDown = vesselOwnershipData.filter(
        (v) => vesselStatuses.ACTIVE === v.status,
      );
      const tempDropDownData = { ...dataStates };
      const vesselSearchDropDownData = vesselOwnershipDataForDropDown.map((v) => ({
        id: v.vessel_id,
        value: v.name,
        ownership_id: v.id,
      }));
      if (dataStates?.vessel?.owners) {
        tempDropDownData.vessel.owners = dataStates.vessel.owners.map((v) => {
          return {
            ...v,
            ownership_ids: vesselOwnershipDataForDropDown
              .filter((item) => item.owner?.id === v.id)
              .map((item) => item?.id),
          };
        });
      }
      tempDropDownData.vessels = vesselSearchDropDownData;
      if (isPlanner) {
        const currentRanks = tempDropDownData?.seafarer?.ranks;
        const filteredRanks = currentRanks?.filter((rank) => plannerRankIds.includes(rank?.id));
        tempDropDownData.seafarer.ranks = filteredRanks;
        tempDropDownData.vessel.vesselTypes = vesselTypesList;
      }
      setDropDownData(tempDropDownData);
    }
  }, [loadingStates, dataStates, vesselOwnershipData]);

  useEffect(() => {
    if (!isPageViewInvoked) {
      try {
        ga4react?.pageview(history.location.pathname, '', 'Seafarer List');
        setIsPageViewInvoked(true);
      } catch (e) {
        console.log(e);
      }
    }
  }, [isPageViewInvoked]);

  useEffect(() => {
    queryVesselOwnership(
      qs.stringify(
        {
          order: 'created_at desc',
          f: [
            'name',
            'id',
            'vessel.id',
            'owner.value',
            'owner.id',
            'vessel.status',
            'owner.ship_party_id',
            'vessel_type.id',
            'vessel_type.value',
            'vessel_type.type',
            'fleet_staff.tech_group',
          ],
          flatten: true,
        },
        qsDefaultOptions,
      ),
    )
      .then((data) => setVesselOwnershipData(data))
      .catch((error) => console.log(error));
    eventTracker('pageView', 'Page View');
  }, []);

  useEffect(() => {
    if (!vesselOwnershipData?.length || Object.values(loadingStates).some((v) => v)) return;
    cleanupFirstTable();
  }, [vesselOwnershipData, activeKey]);

  useEffect(() => {
    // Fetch data for listing and searching
    if (
      !vesselOwnershipData?.length ||
      !memoizedPagination?.pageSize ||
      !isInteger(memoizedPagination?.pageIndex) ||
      Object.values(loadingStates).some((v) => v)
    )
      return;
    queryListData({ ...pagination, sortBy: sortBy?.[activeKey] ?? [] }, activeKey);
  }, [vesselOwnershipData, memoizedPagination, activeKey, loadingStates]);

  const handleSearch = () => {
    if (
      !vesselOwnershipData?.length ||
      !memoizedPagination?.pageSize ||
      !isInteger(memoizedPagination?.pageIndex) ||
      Object.values(loadingStates).some((v) => v)
    )
      return;

    cleanupFirstTable();
    cleanUpSecondTable();
    queryListData({ ...pagination, sortBy: sortBy?.[activeKey] ?? [] }, activeKey);
  };
  useEffect(() => {
    handleSearch();
  }, [activeKey]);

  useEffect(() => {
    let filters;
    if (!activeKey) return;
    if (activeKey === SEAFARERS_TO_RELIEVE) {
      filters = getAvailableSeafarerFilter([], isClubbedOrUnclubbed);
    }
    if (activeKey === ON_LEAVE_SEAFARER) {
      filters = getSeafarerToRelieveFilter([], isClubbedOrUnclubbed);
    }
    setSecondTableAPIQuery(
      processApiQuery({
        lookupData: dropDownData,
        filters,
        values: selectedFilters[activeKey] ?? {},
        onGlobal: true,
      }),
    );
  }, [selectedFilters]);
  useEffect(() => {
    // reset second table
  }, [relieverSeafarerRank, secondTableAPIQuery]);

  useEffect(() => {
    if (isClubbedOrUnclubbed) return;
    if (activeKey === SEAFARERS_TO_RELIEVE && selectedSeafarer) {
      const pattern = /target_rank\.value=([^&]*)(?:&)?/;
      const finalSecondTableQuery = secondTableAPIQuery.replace(pattern, '');
      handleSeafarerToRelieverData(finalSecondTableQuery);
    }
    if (activeKey === ON_LEAVE_SEAFARER && selectedSeafarer) {
      handleSeafarerToRelieveData(secondTableAPIQuery);
    }
  }, [
    relieverSeafarerRank,
    // secondTableAPIQuery,
    memoizedReliverSectionPagination,
    isClubbedOrUnclubbed,
    secondTableLoadType,
  ]);

  useEffect(() => {
    if (
      activeKey === SEAFARERS_TO_RELIEVE &&
      secondTableLoadType !== Object.values(SECOND_TABLE_FILTERS)[0]
    )
      return;
    if (activeKey === SEAFARERS_TO_RELIEVE && selectedSeafarer) {
      setRelieverSectionData({
        data: [],
        total: 0,
      });
      setSelectedSeafarerToBeReliever(null);
      const pattern = /target_rank\.value=([^&]*)(?:&)?/;
      const finalSecondTableQuery = secondTableAPIQuery.replace(pattern, '');
      handleSeafarerToRelieverData(finalSecondTableQuery);
    }
  }, [selectedSeafarer?.id]);

  useEffect(() => {
    alertRef?.current?.hide?.();
    if (hasEditAccess) {
      checkRingfencing(selectedSeafarer, selectedSeafarerToBeReliever);
    }
  }, [selectedSeafarer?.id, selectedSeafarerToBeReliever?.id]);

  /* Handlers */
  const onFilterChange = (name, value) => {
    setSelectedFilters((prev) => ({ ...prev, [name]: value }));
  };

  const handleLoadersState = (key: keyof LoaderState, value) => {
    setLoaders((prevState) => ({ ...prevState, [key]: value }));
  };

  const setSortByWrapper = (key, value) => {
    setSortBy((prev) => ({ ...prev, [key]: value }));
  };

  const navigateTab = (tab) => {
    history.push(`/seafarer/crew-planner/planner/${tab}`);
  };
  const handleTabSwitch = (tab) => {
    navigateTab(tab);
    contractExpEventTracker('tabSwitching', tab);
  };
  const kpiQueryHistoryHandler = (key, val) => {
    setKPIQueryHistory((prev) => ({ ...prev, [key]: val }));
  };

  const getTabSpecificEventTracker = (activeKey) => {
    switch (activeKey) {
      case SEAFARERS_TO_RELIEVE:
        return contractExpEventTracker;
      case ON_LEAVE_SEAFARER:
        return avlSeafarerEventTracker;
      default:
        return eventTracker;
    }
  };

  const handlePagination = useCallback(
    ({ fetchMore, sortBy }: { fetchMore: boolean; sortBy: SortBy }) => {
      if (!fetchMore) cleanupFirstTable();
      setPagination((prev) => {
        return {
          ...prev,
          ...initialPagination,
          pageIndex: fetchMore ? prev?.pageIndex + 1 : 0,
        };
      });
      setSortByWrapper(activeKey, sortBy);
    },
    [activeKey],
  );

  const checkAndResetSecondTable = () => {
    if (isSecondTableLoadTypeChanged) {
      setRelieverSectionData({
        data: [],
        total: 0,
      });
      setIsSecondTableLoadTypeChanged(false);
    }
  };

  const setSecondTableDataAvailableSeafarers = (response: any) => {
    const ownershipDataById = keyBy(vesselOwnershipData, 'id');
    response.data.results = response.data.results?.map((seafarer) => {
      seafarer.last_vessel_owner =
        ownershipDataById[seafarer?.latest_experience?.vessel_ownership_id]?.owner?.value;
      return seafarer;
    });
    setRelieverSectionData((prev) => ({
      data: [...(prev.data ?? []), ...(response.data.results ?? [])],
      total: response.data.pagination.totalCount,
    }));
  };

  const handleSeafarerToRelieverData = useDebouncedCallback(async (apiQuery) => {
    try {
      handleLoadersState('seafarers_to_be_relievers', true);
      const isSuggestedSelected = secondTableLoadType === Object.values(SECOND_TABLE_FILTERS)[0];
      checkAndResetSecondTable();
      let queryParams = addSortOrPaginateParams(
        {
          ...memoizedReliverSectionPagination,
          pageSize: isSuggestedSelected ? 50 : memoizedReliverSectionPagination?.pageSize,
        },
        apiQuery,
      );
      queryParams = `${queryParams}&target_rank.value=${relieverSeafarerRank}`;
      if (roleConfig.shipPartyType === shipPartyType.MANNING_AGENT) {
        queryParams += `&seafarer_reporting_office.ship_party_id=${roleConfig.shipPartyId}`;
      }
      const dateRange = `${moment().subtract(3, 'months').format('YYYY-MM-DD')},${moment()
        .add(2, 'months')
        .format('YYYY-MM-DD')}`;
      const office = dataStates?.reporting_office?.find((r) => r.value === keycloak?.office)?.value;
      if (office) {
        queryParams += `&seafarer_reporting_office.value=${office}`;
      }
      queryParams += `&seafarer_person.current_account_status=active&seafarer_person.current_journey_status=on_leave&seafarer_contact_log.availability_date=${dateRange}`;
      let isSuggestedResponseReceived = false;
      if (isSuggestedSelected) {
        const availableSeafarersQuery =
          seafarerService.getRelieverSeafarersWithPagination(queryParams);
        const vesselType = getVesselType(selectedSeafarer);
        const vesselId = getSeafarerVesselId(selectedSeafarer);
        const vesselOwnershipId = getSeafarerVesselOwnershipId(selectedSeafarer);
        queryParams += `&target_vessel_type.value=${vesselType}&vessel_id=${vesselId}&vessel_ownership_id=${vesselOwnershipId}&exclude_total_count=true`;
        setSuggestedSeafarersLoading(true);
        const suggestedSeafarersQuery = seafarerService.getSuggestedSeafarers(queryParams);
        availableSeafarersQuery?.then((response) => {
          if (!isSuggestedResponseReceived) {
            setSecondTableDataAvailableSeafarers(response);
            handleLoadersState('seafarers_to_be_relievers', false);
          }
        });
        suggestedSeafarersQuery
          ?.then((response) => {
            setRelieverSectionData({
              data: [],
              total: 0,
            });
            setSecondTableDataAvailableSeafarers(response);
            isSuggestedResponseReceived = true;
            handleLoadersState('seafarers_to_be_relievers', false);
          })
          ?.catch((e) => {
            if (e?.message !== OPERATION_CANCELLED_ERROR_TEXT) {
              setTimeoutError({
                message:
                  'Error 504. Failed to get seafarer scores, please refresh the page and try again.',
                variant: 'danger',
              });
            }
          })
          ?.finally(() => {
            setSuggestedSeafarersLoading(false);
          });
      } else {
        const availableSeafarersData = await seafarerService.getRelieverSeafarersWithPagination(
          queryParams,
        );
        setSecondTableDataAvailableSeafarers(availableSeafarersData);
        handleLoadersState('seafarers_to_be_relievers', false);
      }
    } catch (e) {
      handleLoadersState('seafarers_to_be_relievers', false);
      if (e?.message !== OPERATION_CANCELLED_ERROR_TEXT) {
        setAlertMessage({ message: 'Something went wrong', variant: 'danger' });
      }
    }
  }, 50);

  const handleSeafarerToRelieveData = useDebouncedCallback(async (apiQuery) => {
    try {
      handleLoadersState('seafarers_to_be_relievers', true);
      let queryParams = addSortOrPaginateParams(memoizedReliverSectionPagination, apiQuery);
      if (!queryParams.includes('vessel_type')) {
        const filteredVesselList = vesselTypesList
          ?.map((vessel) => encodeURIComponent(vessel?.value))
          .join('%7C');
        queryParams += `&vessel_type=${filteredVesselList}`;
      }
      queryParams = `${queryParams}&seafarer_person:seafarer_status_history:seafarer_rank.value=${relieverSeafarerRank}`;
      if (roleConfig.shipPartyType === shipPartyType.MANNING_AGENT) {
        queryParams += `&seafarer_reporting_office.ship_party_id=${roleConfig.shipPartyId}`;
      }
      const response = await seafarerService.getContractExpirySeafarers(queryParams);
      const ownershipDataById = keyBy(vesselOwnershipData, 'id');
      response.data.results = prepareContractExpirySeafarers(
        response.data.results,
        ownershipDataById,
      );

      setRelieverSectionData((prev) => ({
        data: [...(prev.data ?? []), ...(response.data.results ?? [])],
        total: response.data.pagination.totalCount,
      }));
    } catch (error) {
      if (error?.message !== OPERATION_CANCELLED_ERROR_TEXT) {
        setAlertMessage({ message: 'Something went wrong', variant: 'danger' });
      }
    } finally {
      handleLoadersState('seafarers_to_be_relievers', false);
    }
  }, 50);

  // handle both listing and searching
  const queryListData = useDebouncedCallback(async (sortPaginateData, activeKey) => {
    try {
      const ownershipDataById = keyBy(vesselOwnershipData, 'id');
      const apiQueryObj = qs.parse(apiQuery);
      const isManningAgent = roleConfig.shipPartyType === shipPartyType.MANNING_AGENT;
      const { pageSize, pageIndex, sortBy = [], offset } = sortPaginateData;
      const currentFilters = selectedFilters[activeKey] ?? {};
      const ownerList = dataStates?.vessel?.owners ?? [];
      const ownersMap = new Map(ownerList?.map((owner) => [owner.id, owner?.value]) ?? []);
      const includeMissingRanks =
        pageIndex === 0 && shouldIncludeMissingRanks(apiQueryObj, activeKey);
      let results = [];
      const requestPromises = [];
      let pagination;
      let response;
      const selectedRanks = utils.getSelelectRanks(apiQueryObj);
      if (selectedRanks?.length === 0) {
        return;
      }
      setSearchNotClicked(false);
      setLoading(true);
      let queryObj: QueryParams = {
        ...apiQueryObj,
        'seafarer_reporting_office.ship_party_id': isManningAgent ? roleConfig.shipPartyId : null,
        limit: pageSize,
        offset: pageIndex ?? offset ?? 0,
        orderBy: sortBy.map(({ id, desc }: SortBy) => `${id} ${desc ? 'desc' : 'asc'}`),
      };
      let nonFilteredCount = 0;
      if (activeKey === SEAFARERS_TO_RELIEVE) {
        if (!apiQuery?.includes('no-data')) {
          queryObj[utils.historyRankKey] = selectedRanks?.join('|');
          queryObj.vessel_type =
            queryObj.vessel_type ?? vesselTypesList?.map((vessel) => vessel?.value).join('|');
          requestPromises.push(
            seafarerService.getContractExpirySeafarers(qs.stringify(queryObj, qsDefaultOptions)),
          );
          if (queryObj?.offset === 0) {
            fetchDueIn30Days(queryObj);
            fetchOverdueSeafarers(queryObj);
          }
        }
        if (apiQuery?.includes('no-data')) {
          setDueIn30Days((prevState) => ({
            ...prevState,
            number: 0,
          }));
          setOverdueSeafarers((prevState) => ({
            ...prevState,
            number: 0,
          }));
        }
        let shouldSetMissingRank = false;
        if (includeMissingRanks) {
          const vesselOwnershipIdFromFilter = utils.getVesselIdsForQuery(
            apiQueryObj,
            dropDownData.vessels,
          );

          const ownerNames = currentFilters?.owner?.map((id) => ownersMap.get(id))?.filter(Boolean);
          const missingRanksParams = {
            owner_name: ownerNames,
            crew_planning_status: apiQueryObj.crew_planning_status?.split(','),
            rank: selectedRanks?.join('|'),
            tech_group: queryObj['seafarer_person:seafarer_status_history.vessel_tech_group'],
            vessel_type:
              queryObj.vessel_type ?? vesselTypesList?.map((vessel) => vessel?.value).join('|'),
            vessel_ownership_id: vesselOwnershipIdFromFilter,
          };
          const missingRanksParamsForKPI = {
            ...missingRanksParams,
            crew_planning_status: ['unplanned'],
          };

          if (
            !utils.compareKPIPayload(
              missingRanksParamsForKPI,
              missingRanksParams,
              REMOVE_KEYS_FROM_COMPARE_FOR_KPI,
            )
          ) {
            fetchMissingRanks(missingRanksParamsForKPI);
          } else if (
            !utils.compareKPIPayload(
              kpiQueryHistory.missing_ranks,
              missingRanksParamsForKPI,
              REMOVE_KEYS_FROM_COMPARE_FOR_KPI,
            )
          ) {
            kpiQueryHistoryHandler('missing_ranks', missingRanksParamsForKPI);
            shouldSetMissingRank = true;
            setMissingRanks((prevState) => ({
              ...prevState,
              isLoading: true,
            }));
          }

          requestPromises.push(seafarerService.getMissingRanks(missingRanksParams));
          const ranks = selectedFilters[activeKey]?.rank;
          const additionalRequestParams = {
            rank_id: ranks.join(','),
            crew_planning_status: apiQueryObj.crew_planning_status,
            tech_group: queryObj['seafarer_person:seafarer_status_history.vessel_tech_group'],
            vessel_type:
              queryObj.vessel_type ?? vesselTypesList?.map((vessel) => vessel?.value).join('|'),
            vessel_ownership_id: vesselOwnershipIdFromFilter,
          };
          requestPromises.push(seafarerService.getAdditionalCrewRequest(additionalRequestParams));
        } else if (queryObj?.offset === 0) {
          setMissingRanks((prevState) => ({ ...prevState, number: 0 }));
        }
        const [contractExpiryResp, missingPersonnelsResp, additionalRequestResp] =
          await Promise.all(requestPromises);

        const missingPersonnelsData =
          missingPersonnelsResp?.data?.results?.map((s) => ({ ...s, isMissingPersonnel: true })) ??
          [];
        if (includeMissingRanks && shouldSetMissingRank) {
          setMissingRanks((prevState) => ({
            ...prevState,
            isLoading: false,
            number: missingPersonnelsData.length,
          }));
        }
        const contractExpiryData = contractExpiryResp?.data?.results ?? [];
        const additionalRequestData =
          additionalRequestResp?.data?.results?.map((s) => ({ ...s, isAdditionalRequest: true })) ??
          [];
        results = prepareContractExpirySeafarers(
          [...missingPersonnelsData, ...additionalRequestData, ...contractExpiryData],
          ownershipDataById,
        );
        pagination = contractExpiryResp.data?.pagination;
        if (includeMissingRanks) {
          setExtraSeafarerCount(
            (missingPersonnelsResp?.data?.results?.length || 0) +
              (additionalRequestResp?.data?.results?.length || 0),
          );
        }
      } else if (activeKey === ON_LEAVE_SEAFARER) {
        const office = dataStates.reporting_office?.find(
          (r) => r.value === keycloak?.office,
        )?.value;
        queryObj = {
          ...queryObj,
          'target_rank.value': selectedRanks?.join('|'),
          'seafarer_reporting_office.value': office,
          'seafarer_person.current_account_status': 'active',
          'seafarer_person.current_journey_status': ['on_leave', 'travelling'],
        };
        fetchNotContacted(queryObj);
        fetchAvailableNotPlanned(queryObj);
        response = await seafarerService.getRelieverSeafarersWithPagination(
          `&${qs.stringify(queryObj, qsDefaultOptions)}`,
        );
        nonFilteredCount = response?.data?.results?.length ?? 0;
        const allowedTypes = Array.from(new Set(vesselTypesList?.map((type) => type?.type)));
        if (
          [
            CREW_PLANNING_STATUS_MAPPING.Unclubbed[0],
            CREW_PLANNING_STATUS_MAPPING.Clubbed[0],
          ].includes(apiQueryObj.crew_planning_status) &&
          response?.data?.results?.length
        ) {
          const filteredVesselOwnershipIds = vesselOwnershipData
            ?.filter((o) => allowedTypes.includes(o?.vessel_type?.type))
            ?.map((o) => o?.id);
          response.data.results = response?.data?.results?.filter((res) =>
            filteredVesselOwnershipIds.includes(res?.crew_planning?.ownership_id),
          );
        }
        results = response.data.results?.map((seafarer) => {
          const crewPlanning =
            seafarer?.crew_planning?.seafarer?.seafarer_person?.seafarer_status_history[0] ??
            seafarer?.crew_planning;
          const vesselOwnershipId = crewPlanning?.vessel_ownership_id ?? crewPlanning?.ownership_id;
          const vesselDetail = ownershipDataById?.[vesselOwnershipId];
          if (crewPlanning && vesselDetail) {
            crewPlanning.owner_name = vesselDetail?.owner?.value;
          }
          if (vesselDetail && !seafarer?.crew_planning?.seafarer) {
            const { owner, vessel_type, fleet_staff, name } = vesselDetail;
            seafarer.crew_planning = {
              ...crewPlanning,
              owner,
              vessel_type,
              fleet_staff,
              vessel_name: name,
            };
          }
          seafarer.last_vessel_owner =
            ownershipDataById[seafarer?.latest_experience?.vessel_ownership_id]?.owner?.value ??
            seafarer?.latest_experience?.owner_name;
          return seafarer;
        });
        pagination = response.data.pagination;
      }

      const totalCount = pagination?.totalCount
        ? pagination?.totalCount - (nonFilteredCount - results?.length)
        : 0;
      setFilteredSeafarers((prev) => [...prev, ...results]);
      setSeafarersTotalCount(totalCount);
      setLoading(false);
    } catch (error) {
      if (httpService.axios.isCancel(error)) {
        return;
      }
      setLoading(false);
      console.log('error:', error);
    }
  }, 100);

  const checkRingfencing = useDebouncedCallback(async (firstTableSeafarer, secondTableSeafarer) => {
    const relieverSeafarer =
      activeKey === SEAFARERS_TO_RELIEVE ? secondTableSeafarer : firstTableSeafarer;
    const onBoardSeafarer =
      activeKey === SEAFARERS_TO_RELIEVE ? firstTableSeafarer : secondTableSeafarer;
    try {
      const crewStatusFilter = activeKey
        ? selectedFilters[activeKey]?.crew_planning_status?.[0]
        : '';
      if (
        relieverSeafarer &&
        onBoardSeafarer &&
        crewStatusFilter === CREW_PLANNING_STATUSES.UNPLANNED
      ) {
        setRingfencingOwners([]);
        handleLoadersState('ringfencing', true);
        const ownershipId =
          onBoardSeafarer?.isMissingPersonnel || onBoardSeafarer?.isAdditionalRequest
            ? onBoardSeafarer?.vessel_ownership_id
            : onBoardSeafarer?.seafarer_person?.seafarer_status_history[0]?.vessel_ownership_id;

        const shipPartyId = vesselOwnershipData.find((ownership) => ownership.id === ownershipId)
          ?.owner?.ship_party_id;
        if (shipPartyId && relieverSeafarer.id) {
          const ringfencingOwnerNames = (
            await seafarerService.checkRingfencing(relieverSeafarer.id, shipPartyId)
          )?.data?.ownerNames;
          let jsxMessage = null;
          if (ringfencingOwnerNames?.length) {
            jsxMessage = (
              <div style={{ fontSize: '14px' }}>
                <span style={{ fontWeight: 'bold' }}>
                  {`The Selected Seafarer as Reliever is a preferred resource from ${ringfencingOwnerNames.join(
                    ', ',
                  )}. Action to be taken: Please consult with your line manager before confirmation of plan.`}
                </span>
              </div>
            );
          }
          setAlertMessage({
            message: jsxMessage,
            variant: 'danger',
          });
          setRingfencingOwners(ringfencingOwnerNames);
        }
      } else {
        setRingfencingOwners([]);
      }
    } catch (e) {
      if (e?.message !== OPERATION_CANCELLED_ERROR_TEXT) {
        setAlertMessage({ message: 'Something went wrong', variant: 'danger' });
      }
    } finally {
      handleLoadersState('ringfencing', false);
    }
  }, 50);

  const visitSeafarer = useCallback((seafarerId, activeKey = '') => {
    if (activeKey === SEAFARERS_TO_RELIEVE || activeKey === ON_LEAVE_SEAFARER) {
      activeKey === SEAFARERS_TO_RELIEVE
        ? contractExpEventTracker('crewDetailsLink', seafarerId)
        : avlSeafarerEventTracker('crewDetailsLink', seafarerId);
      window.open(`/seafarer/details/${seafarerId}/general`, '_blank');
    } else {
      window.open(`/seafarer/details/${seafarerId}/general`, '_blank');
    }
  }, []);

  const handleTabSelect = (key) => {
    setSortByWrapper(key, getPageSort(key));
    if (key === activeKey) {
      if (apiQuery) {
        // clear the current query and refresh page
        cleanupFirstTable();
        handleTabSwitch(key);
      }
      return;
    }
    getTabSpecificEventTracker(key)('tabs', key);
    handleTabSwitch(key);
    setIsPageViewInvoked(false);
    cleanupFirstTable();
  };

  const cleanupFirstTable = () => {
    setFilteredSeafarers([]);
    setSearchNotClicked(true);
    setSeafarersTotalCount(0);
    setExtraSeafarerCount(0);
    setPagination(initialPagination);
    setSelectedSeafarer(null);

    setRelieverSectionData({});
    setSelectedSeafarerToBeReliever(null);
  };
  const cleanUpSecondTable = () => {
    handleLoadersState('seafarers_to_be_relievers', true);
    setReliverSectionPagination((prevState) => ({
      ...prevState,
      [activeKey]: {
        ...prevState[activeKey],
        ...initialPagination,
      },
    }));
    setSelectedSeafarerToBeReliever(null);
    setRelieverSectionData({
      data: [],
      total: 0,
    });
  };
  const handleActionOfSeafarerRelieve = (seafarer, status) => {
    if (getStatus(status) === STATUS.unclubbed) {
      seafarer.seafarer_status_history =
        seafarer?.crew_planning?.reliever?.seafarer_person?.seafarer_status_history?.[0];
      seafarer.open_seafarer_id = seafarer?.crew_planning?.reliever?.id;
      selectedReliefSeafarer.current = seafarer;
      setIsOpenUnplanReliefModal(true);
    }
    if (getStatus(status) === STATUS.unplanned) {
      setSelectedSeafarer(seafarer);
    }
    setStartTime(moment().valueOf());
    crewPlannerEventTracker(
      'reliefSeafarer',
      `${seafarer?.seafarer_person?.first_name ?? ''} ${
        seafarer?.seafarer_person?.middle_name ?? ''
      } ${seafarer?.seafarer_person?.last_name ?? ''}
      }`,
    );
  };
  const handleActionOfAvailableSeafarer = (seafarer, status) => {
    if (getStatus(status) === STATUS.unclubbed) {
      seafarer.open_seafarer_id = seafarer?.id;
      selectedReliefSeafarer.current = seafarer;
      setIsOpenUnplanReliefModal(true);
    }
    if (getStatus(status) === STATUS.unplanned) {
      setIsOpenConfirmRecommendModal(true);
    }
  };

  const handleRemarkAction = (seafarer, remarkKey, tableName, rowId, planningType) => {
    const isMissingPersonnal = checkMissingPersonnel(seafarer);
    const isAdditionalCrewRequest = checkAdditionalRequest(seafarer);
    if (isAdditionalCrewRequest || planningType === CREW_PLANNING_TYPE.add_rank) {
      Paris2Modal.info({
        title: 'Remarks',
        message: 'Remarks cannot be added on Additional crew',
      });
      return;
    }
    if (isMissingPersonnal || planningType === CREW_PLANNING_TYPE.missing_personnel) {
      Paris2Modal.info({
        title: 'Remarks',
        message: 'Remarks cannot be added on Missing Personnel',
      });
      return;
    }
    selectedSeafarerForRemarks.current = {
      seafarer,
      tableName,
      remarkKey,
      rowId,
    };
    setIsOpenRemarkModal(true);
  };

  const handleConfirmRemark = async (seafarer, remarks, isEdit) => {
    try {
      const {
        remarkKey: keyPath,
        rowId: selectedRowId,
        tableName,
      } = selectedSeafarerForRemarks.current ?? {};
      const isFromFirstTable = tableName === activeKey;

      handleLoadersState('confirm_remark', true);

      await addRemark(seafarer, remarks, isEdit);

      const updatedSeafarerList = updateSeafarerData(
        isFromFirstTable,
        selectedRowId,
        keyPath,
        remarks,
      );
      const updatedSeafarer = updatedSeafarerList.find((item) => item?.id === selectedRowId);

      updateSelectedSeafarer(selectedRowId, updatedSeafarer);
    } catch (err) {
      handleError(err);
    } finally {
      finalizeRemark();
    }
  };

  const addRemark = async (seafarer, remarks, isEdit) => {
    await plannerRemark({ seafarer_id: seafarer.id, remarks });
    setAlertMessage({ message: 'Remark has been added successfully.' });
    crewPlannerEventTracker('remarks', remarks);

    const eventType = isEdit ? 'remarkEditedAndClickedSave' : 'remarkAddedAndClickedSave';
    activeKey === SEAFARERS_TO_RELIEVE
      ? contractExpEventTracker(eventType)
      : avlSeafarerEventTracker(eventType);
  };

  const updateSeafarerData = (isFromFirstTable, selectedRowId, keyPath, remarks) => {
    const updatedSeafarerList = updateNestedKey(
      isFromFirstTable ? filteredSeafarers : relieverSectionData.data,
      selectedRowId,
      keyPath,
      { remarks },
    );

    if (isFromFirstTable) {
      setFilteredSeafarers(updatedSeafarerList);
    } else {
      setRelieverSectionData((prevState) => ({ ...prevState, data: updatedSeafarerList }));
    }

    return updatedSeafarerList;
  };

  // Helper function to update selected seafarer if IDs match
  const updateSelectedSeafarer = (selectedRowId, updatedSeafarer) => {
    if (selectedRowId === selectedSeafarerToBeReliever?.id && updatedSeafarer) {
      setSelectedSeafarerToBeReliever(updatedSeafarer);
    } else if (selectedRowId === selectedSeafarer?.id && updatedSeafarer) {
      setSelectedSeafarer(updatedSeafarer);
    }
  };

  // Helper function to handle errors
  const handleError = (err) => {
    console.log(err);
    if (err?.message !== OPERATION_CANCELLED_ERROR_TEXT) {
      setAlertMessage({ message: 'Something went wrong', variant: 'danger' });
    }
  };

  // Helper function to finalize remark actions
  const finalizeRemark = () => {
    handleLoadersState('confirm_remark', false);
    setIsOpenRemarkModal(false);
  };

  const handleConfirmUnplan = async (replacer, crewPlanningId, seafarer) => {
    try {
      handleLoadersState('unplan_relief', true);
      activeKey === SEAFARERS_TO_RELIEVE
        ? contractExpEventTracker('clickingCancelOnThePopUp')
        : avlSeafarerEventTracker('clickingCancelOnThePopUp');
      await updateCrewPlan({
        id: crewPlanningId,
        planning_status: CREW_PLANNING_STATUS.canceled,
        ...(seafarer?.isAdditionalRequest && { additional_request_id: seafarer?.id }),
      });
      setAlertMessage({ message: 'Seafarer has been Unplanned to Relieve.' });
      if (
        [
          seafarerStatus.RECOMMENDED,
          seafarerStatus.RECOMMENDED_WITH_DEVIATION,
          seafarerStatus.CREW_ASSIGNMENT_APPROVED,
        ].includes(
          replacer?.seafarer_person.seafarer_status_history?.seafarer_journey_status ??
            replacer?.seafarer_person?.seafarer_status_history?.[0]?.seafarer_journey_status,
        )
      ) {
        visitSeafarer(replacer.id);
      }
    } catch (err) {
      if (err?.message !== OPERATION_CANCELLED_ERROR_TEXT) {
        setAlertMessage({ message: 'Something went wrong', variant: 'danger' });
      }
    } finally {
      handleLoadersState('unplan_relief', false);
      setIsOpenUnplanReliefModal(false);
      cleanupFirstTable();
    }
  };

  const constructCreatePlanPayload = (seafarer, reliever) => {
    const isMissingPersonnal = checkMissingPersonnel(seafarer);
    const isAdditionalCrewRequest = checkAdditionalRequest(seafarer);

    let planningType;
    if (isMissingPersonnal) {
      planningType = CREW_PLANNING_TYPE.missing_personnel;
    } else if (isAdditionalCrewRequest) {
      planningType = CREW_PLANNING_TYPE.add_rank;
    } else {
      planningType = CREW_PLANNING_TYPE.relieve;
    }

    return {
      vessel_id: getSeafarerVesselId(seafarer),
      ownership_id: getSeafarerVesselOwnershipId(seafarer),
      planning_type: planningType,
      seafarer_id: !isMissingPersonnal && !isAdditionalCrewRequest ? seafarer?.id : null,
      reliever_id: reliever?.id,
      planning_status: CREW_PLANNING_STATUS.unclubbed,
      score_request_id: reliever?.score_request_id,
      ...(isAdditionalCrewRequest && { additional_request_id: seafarer.id }),
    };
  };

  const handleConfirmRecommend = async (seafarer, reliever) => {
    try {
      handleLoadersState('confirm_recommend', true);
      const endTime = moment().valueOf();
      const spentTime = moment.utc(endTime - startTime).format('HH:mm:ss');
      const { data } = await createCrewPlan(constructCreatePlanPayload(seafarer, reliever));
      crewPlannerEventTracker('timeSpentToReleifSeafarer', spentTime);
      setStartTime(0);
      activeKey === SEAFARERS_TO_RELIEVE
        ? contractExpEventTracker('confirmingThePlan')
        : avlSeafarerEventTracker('confirmingThePlan');
      if (ringfencingOwners?.length > 0) {
        crewPlannerEventTracker('confirmRingFencing', '');
      }
      if (data) {
        if (
          ![seafarerStatus.RECOMMENDED, seafarerStatus.RECOMMENDED_WITH_DEVIATION].includes(
            reliever.seafarer_person.seafarer_journey_status?.[0]?.seafarer_journey_status,
          )
        ) {
          setSelectedSeafarer(null);
          visitSeafarer(reliever.id);
        }
        handleLoadersState('confirm_recommend', false);
        setIsOpenConfirmRecommendModal(false);
        cleanupFirstTable();
      }
    } catch (err) {
      if (err?.message !== OPERATION_CANCELLED_ERROR_TEXT) {
        setAlertMessage({ message: 'Something went wrong', variant: 'danger' });
      }
      handleLoadersState('confirm_recommend', false);
      setIsOpenConfirmRecommendModal(false);
    }
  };
  const handlePaginationOfReliever = useCallback(
    ({ fetchMore, sortBy }) => {
      if (!fetchMore) cleanUpSecondTable();
      setReliverSectionPagination((prevState) => ({
        ...prevState,
        [activeKey]: {
          ...prevState[activeKey],
          sortBy,
          pageIndex: fetchMore ? prevState[activeKey]?.pageIndex + 1 : 0,
        },
      }));
    },
    [activeKey],
  );

  const onCrewPlanningFilterChange = (selectedStatus: string) => {
    activeKey === SEAFARERS_TO_RELIEVE
      ? contractExpEventTracker('statusTabSwitched', selectedStatus)
      : avlSeafarerEventTracker('statusTabSwitched', selectedStatus);
    const urlFilter = location.search;
    const urlFilterValues = generateFilterObjectFromApiQuery(
      urlFilter,
      activeKey === SEAFARERS_TO_RELIEVE
        ? getSeafarerToRelieveFilter()
        : getAvailableSeafarerFilter(),
    );
    const existingFilters = cleanUpFilter(urlFilterValues);
    const newFilters = {
      ...existingFilters,
      crew_planning_status: CREW_PLANNING_STATUS_MAPPING[selectedStatus],
    };
    onFilterChange(activeKey, newFilters);
    setApiQuery(
      processApiQuery({
        lookupData: dropDownData,
        values: newFilters,
        filters:
          activeKey === SEAFARERS_TO_RELIEVE
            ? getSeafarerToRelieveFilter([], CREW_PLANNING_STATUS_MAPPING[selectedStatus][0])
            : getAvailableSeafarerFilter([], CREW_PLANNING_STATUS_MAPPING[selectedStatus][0]),
      }),
    );
    window.history.replaceState(
      {},
      null,
      `${history.location.pathname}?${generateApiQueryFromFilterObject(
        newFilters,
        activeKey === SEAFARERS_TO_RELIEVE
          ? getSeafarerToRelieveFilter()
          : getAvailableSeafarerFilter(),
      )}`,
    );
    handleSearch();
  };

  const handleSecondTableLoadType = (value: string) => {
    setSecondTableLoadType(value);
    setIsSecondTableLoadTypeChanged(true);
    cleanUpSecondTable();
  };

  const getBottomActionBar = () => {
    if (!hasEditAccess) {
      return null;
    }
    const crewStatusFilter = activeKey ? selectedFilters[activeKey]?.crew_planning_status?.[0] : '';
    let onCancelClick = null;
    let onConfirmClick = null;
    let confirmButtonLabel = null;
    if (
      selectedSeafarer &&
      selectedSeafarerToBeReliever &&
      crewStatusFilter === CREW_PLANNING_STATUSES.UNPLANNED
    ) {
      onCancelClick = () => setSelectedSeafarerToBeReliever(null);
      onConfirmClick = () =>
        handleActionOfAvailableSeafarer(
          activeKey === SEAFARERS_TO_RELIEVE ? selectedSeafarer : selectedSeafarerToBeReliever,
          CREW_PLANNING_STATUSES.UNPLANNED,
        );
      confirmButtonLabel = 'Plan to Relieve';
    }
    if (selectedSeafarer && crewStatusFilter === CREW_PLANNING_STATUSES.UNCLUBBED) {
      onCancelClick = () => setSelectedSeafarer(null);
      onConfirmClick = () =>
        handleActionOfAvailableSeafarer(selectedSeafarer, CREW_PLANNING_STATUSES.UNCLUBBED);
      confirmButtonLabel = 'Unplan Relieve';
    }
    if (onCancelClick && onConfirmClick) {
      return (
        <div className="confirm-bottom-bar">
          <Button variant="primary" className="confirm-bottom-button mr-2" onClick={onCancelClick}>
            Cancel
          </Button>
          <Button
            variant="primary"
            className="confirm-bottom-button confirm-bottom-plan-to-relieve"
            onClick={onConfirmClick}
            disabled={loaders.ringfencing}
          >
            {loaders.ringfencing ? (
              <>
                <Spinner
                  as="span"
                  animation="border"
                  size="sm"
                  aria-hidden="true"
                  aria-live="polite"
                  aria-label="Loading"
                />
                <span className="visually-hidden"> Loading...</span>
              </>
            ) : (
              confirmButtonLabel
            )}
          </Button>
        </div>
      );
    }
    return '';
  };

  const today = dateAsDash(new Date());
  const monthAfterToday = dateAsDash(moment().add(1, 'month'));
  const last15Days = dateAsDash(moment().subtract(15, 'days'));
  const next30Days = dateAsDash(moment().add(30, 'days'));
  const contractExpiryQueryObj = {
    limit: 0,
    offset: 0,
    withRelations: false,
    vessel_type: vesselTypesList?.map((vessel) => vessel?.value).join('|'),
    ...(plannerRanks?.length > 0 && {
      [utils.historyRankKey]: plannerRanks.map((rank) => rank?.rank).join('|'),
    }),
  };

  const availabilityQueryObj = {
    limit: 0,
    offset: 0,
    'seafarer_person.current_account_status': 'active',
    'seafarer_person.current_journey_status': 'on_leave',
    'target_vessel_type.value': vesselTypesList?.map((vessel) => vessel?.value).join('|'),
    exclude_total_count: false,
    ...(plannerRanks?.length > 0 && {
      [utils.availabilityRankKey]: plannerRanks?.map((rank) => rank?.rank).join('|'),
    }),
  };

  // Due in 30 Days
  const fetchDueIn30Days = async (overridingQuery: any = null) => {
    setDueIn30Days((prevState) => ({
      ...prevState,
      isLoading: true,
    }));
    try {
      const dueIn30DaysQueryObj = {
        ...contractExpiryQueryObj,
        ...(overridingQuery ?? {}),
        'seafarer_person:seafarer_status_history.expected_contract_end_date': `${today},${monthAfterToday}`,
        crew_planning_status: ['unplanned'],
      };

      delete dueIn30DaysQueryObj?.keyword;
      if (
        utils.compareKPIPayload(
          kpiQueryHistory.due_in_30_days,
          dueIn30DaysQueryObj,
          REMOVE_KEYS_FROM_COMPARE_FOR_KPI,
        )
      ) {
        return;
      }
      kpiQueryHistoryHandler('due_in_30_days', dueIn30DaysQueryObj);
      const dueIn30DaysResponse = await seafarerService.getContractExpirySeafarers(
        qs.stringify(dueIn30DaysQueryObj, qsDefaultOptions),
        false,
      );
      setDueIn30Days((prevState) => ({
        ...prevState,
        number: dueIn30DaysResponse.data.pagination.totalCount,
      }));
    } catch (error) {
      if (error?.message !== OPERATION_CANCELLED_ERROR_TEXT) {
        setDueIn30Days((prevState) => ({ ...prevState, number: null }));
        console.log('failed to fetch due in 30 days data', error);
      }
    } finally {
      setDueIn30Days((prevState) => ({
        ...prevState,
        isLoading: false,
      }));
    }
  };

  const fetchOverdueSeafarers = async (overridingQuery: any = null) => {
    setOverdueSeafarers((prevState) => ({
      ...prevState,
      isLoading: true,
    }));
    try {
      const overdueSeafarersObj = {
        ...contractExpiryQueryObj,
        ...(overridingQuery ?? {}),
        'seafarer_person:seafarer_status_history.expected_contract_end_date': `,${today}`,
        crew_planning_status: ['unplanned'],
      };

      delete overdueSeafarersObj?.keyword;
      if (
        utils.compareKPIPayload(
          kpiQueryHistory.overdue_seafarer,
          overdueSeafarersObj,
          REMOVE_KEYS_FROM_COMPARE_FOR_KPI,
        )
      ) {
        return;
      }
      kpiQueryHistoryHandler('overdue_seafarer', overdueSeafarersObj);
      const overdueSeafarersResponse = await seafarerService.getContractExpirySeafarers(
        qs.stringify(overdueSeafarersObj, qsDefaultOptions),
        false,
      );
      setOverdueSeafarers((prevState) => ({
        ...prevState,
        number: overdueSeafarersResponse.data.pagination.totalCount,
      }));
    } catch (error) {
      if (error?.message !== OPERATION_CANCELLED_ERROR_TEXT) {
        setOverdueSeafarers((prevState) => ({ ...prevState, number: null }));
        console.log('failed to fetch overdue seafarers data', error);
      }
    } finally {
      setOverdueSeafarers((prevState) => ({
        ...prevState,
        isLoading: false,
      }));
    }
  };

  const fetchMissingRanks = async (overridingQuery: any = null) => {
    setMissingRanks((prevState) => ({
      ...prevState,
      isLoading: true,
    }));
    try {
      let missingRankCountQuery = {
        rank: plannerRanks?.map((rank) => rank?.rank).join('|'),
      };
      missingRankCountQuery = {
        ...missingRankCountQuery,
        ...overridingQuery,
        crew_planning_status: ['unplanned'],
      };
      if (
        utils.compareKPIPayload(
          kpiQueryHistory.missing_ranks,
          missingRankCountQuery,
          REMOVE_KEYS_FROM_COMPARE_FOR_KPI,
        )
      ) {
        return;
      }
      kpiQueryHistoryHandler('missing_ranks', missingRankCountQuery);
      const missingRanksCount = await seafarerService.getMissingRanksCount(missingRankCountQuery);
      setMissingRanks((prevState) => ({
        ...prevState,
        number: missingRanksCount,
      }));
    } catch (error) {
      if (error?.message !== OPERATION_CANCELLED_ERROR_TEXT) {
        setMissingRanks((prevState) => ({ ...prevState, number: null }));
        console.log('failed to fetch missing ranks data', error);
      }
    } finally {
      setMissingRanks((prevState) => ({
        ...prevState,
        isLoading: false,
      }));
    }
  };

  const fetchNotContacted = async (overridingQuery: any = null) => {
    setNotContacted((prevState) => ({
      ...prevState,
      isLoading: true,
    }));
    try {
      const notContactedObj = {
        ...availabilityQueryObj,
        ...(overridingQuery ?? {}),
        'seafarer_contact_log.contact_date': `,${last15Days}`,
        crew_planning_status: ['unplanned'],
      };
      let modifiedNotContactedObj = { ...notContactedObj };
      if (modifiedNotContactedObj?.['target_vessel_type.value']) {
        delete modifiedNotContactedObj?.['target_vessel_type.value'];
      }
      if (
        modifiedNotContactedObj['seafarer_experience.owner_name'] ||
        modifiedNotContactedObj['target_vessel_type.value']
      ) {
        modifiedNotContactedObj.past_years = 10;
      }
      if (overridingQuery?.['target_vessel_type.value']) {
        modifiedNotContactedObj['target_vessel_type.value'] = overridingQuery['target_vessel_type.value'];
      }
      delete modifiedNotContactedObj?.keyword;
      if (
        utils.compareKPIPayload(
          kpiQueryHistory.not_contacted,
          modifiedNotContactedObj,
          REMOVE_KEYS_FROM_COMPARE_FOR_KPI,
        )
      ) {
        return;
      }
      kpiQueryHistoryHandler('not_contacted', modifiedNotContactedObj);
      const notContactedResponse = await seafarerService.getRelieverSeafarers(
        `&${qs.stringify({...modifiedNotContactedObj, limit: 0}, qsDefaultOptions)}`,
        false,
      );
      setNotContacted((prevState) => ({
        ...prevState,
        number: notContactedResponse.data.pagination.totalCount,
      }));
    } catch (error) {
      if (error?.message !== OPERATION_CANCELLED_ERROR_TEXT) {
        setNotContacted((prevState) => ({ ...prevState, number: null }));
        console.log('failed to fetch not contacted data', error);
      }
    } finally {
      setNotContacted((prevState) => ({
        ...prevState,
        isLoading: false,
      }));
    }
  };

  const fetchAvailableNotPlanned = async (overridingQuery: any = null) => {
    setAvailableNotPlanned((prevState) => ({
      ...prevState,
      isLoading: true,
    }));
    try {
      const availableNotPlannedObj = {
        ...availabilityQueryObj,
        ...(overridingQuery ?? {}),
        'seafarer_contact_log.availability_date': `${today},${next30Days}`,
        crew_planning_status: ['unplanned'],
      };
      let modifiedAvailableNotPlannedObj = { ...availableNotPlannedObj };
      if (modifiedAvailableNotPlannedObj?.['target_vessel_type.value']) {
        delete modifiedAvailableNotPlannedObj['target_vessel_type.value'];
      }
      if (
        modifiedAvailableNotPlannedObj['seafarer_experience.owner_name'] ||
        modifiedAvailableNotPlannedObj['target_vessel_type.value']
      ) {
        modifiedAvailableNotPlannedObj.past_years = 10;
      }
      if (overridingQuery?.['target_vessel_type.value']) {
        modifiedAvailableNotPlannedObj['target_vessel_type.value'] = overridingQuery?.['target_vessel_type.value'];
      }
      delete modifiedAvailableNotPlannedObj?.keyword;
      if (
        utils.compareKPIPayload(
          kpiQueryHistory.available,
          modifiedAvailableNotPlannedObj,
          REMOVE_KEYS_FROM_COMPARE_FOR_KPI,
        )
      ) {
        return;
      }
      kpiQueryHistoryHandler('available', modifiedAvailableNotPlannedObj);
      const availableNotPlannedResponse = await seafarerService.getRelieverSeafarers(
        `&${qs.stringify({...modifiedAvailableNotPlannedObj, limit: 0}, qsDefaultOptions)}`,
        false,
      );
      setAvailableNotPlanned((prevState) => ({
        ...prevState,
        number: availableNotPlannedResponse.data.pagination.totalCount,
      }));
    } catch (error) {
      if (error?.message !== OPERATION_CANCELLED_ERROR_TEXT) {
        setAvailableNotPlanned((prevState) => ({ ...prevState, number: null }));
        console.log('failed to fetch available not planned data', error);
      }
    } finally {
      setAvailableNotPlanned((prevState) => ({
        ...prevState,
        isLoading: false,
      }));
    }
  };

  useEffect(() => {
    if (
      isAPIQueryInitialized &&
      plannerRanks &&
      ((activeKey === SEAFARERS_TO_RELIEVE && !apiQuery.includes('seafarer_rank')) ||
        (activeKey === ON_LEAVE_SEAFARER && !apiQuery.includes('target_rank.value'))) &&
      !Object.values(loadingStates).some((v) => v)
    ) {
      fetchDueIn30Days();
      fetchOverdueSeafarers();
      fetchMissingRanks();
      fetchNotContacted();
      fetchAvailableNotPlanned();
    } else if (
      isAPIQueryInitialized &&
      activeKey === SEAFARERS_TO_RELIEVE &&
      apiQuery.includes('seafarer_rank')
    ) {
      fetchNotContacted();
      fetchAvailableNotPlanned();
    } else if (activeKey === ON_LEAVE_SEAFARER && apiQuery.includes('target_rank.value')) {
      fetchDueIn30Days();
      fetchOverdueSeafarers();
      fetchMissingRanks();
    }
  }, [isAPIQueryInitialized, plannerRanks, activeKey, apiQuery]);

  if (!isVisible) return null;
  return (
    <AccessHandlerWrapper hasRoleAccess={roleConfig.seafarer.view.general}>
      <div className="crew-planner">
        <TabWrapper
          isFixedWidth
          activeKey={activeKey}
          handleTabSelect={handleTabSelect}
          data={ListTabData}
        />
        <div className="crew-planner-table-wrapper">
          {activeKey === SEAFARERS_TO_RELIEVE && (
            <SeafarerToRelieve
              KPI={(
                <PlannerKPI
                  kpiData={[
                    dueIn30Days,
                    overdueSeafarers,
                    missingRanks,
                    notContacted,
                    availableNotPlanned,
                  ]}
                />
              )}
              setIsAPIQueryInitialized={setIsAPIQueryInitialized}
              selectedFilters={selectedFilters[activeKey]}
              onFilterChange={(v) => onFilterChange(activeKey, v)}
              setApiQuery={setApiQuery}
              tabName={activeKey}
              fetchData={handlePagination}
              hasMoreData={filteredSeafarers.length < seafarersTotalCount + extraSeafarerCount}
              data={filteredSeafarers}
              eventTracker={contractExpEventTracker}
              loading={loading}
              init_sort={memoizedPagination.sortBy}
              handleAction={handleActionOfSeafarerRelieve}
              handleRemark={handleRemarkAction}
              selectedSeafarer={selectedSeafarer}
              relieverSectionData={relieverSectionData}
              handleSelectSeafarerToRelieve={setSelectedSeafarer}
              handlePaginationOfReliever={handlePaginationOfReliever}
              reliverSectionLoading={loaders.seafarers_to_be_relievers}
              selectedSeafarerToBeReliever={selectedSeafarerToBeReliever}
              setSelectedSeafarerToBeReliever={setSelectedSeafarerToBeReliever}
              relieverSectionPagination={memoizedReliverSectionPagination}
              filterData={dropDownData}
              onCrewPlanningFilterChange={onCrewPlanningFilterChange}
              selectedRanks={[]}
              secondTableLoadType={secondTableLoadType}
              handleSecondTableLoadType={handleSecondTableLoadType}
              seafarersTotalCount={seafarersTotalCount + extraSeafarerCount}
              suggestedSeafarersLoading={suggestedSeafarersLoading}
              handleSearch={handleSearch}
              searchNotClicked={searchNotClicked}
              hasEditAccess={hasEditAccess}
            />
          )}
          {activeKey === ON_LEAVE_SEAFARER && (
            <AvailableSeafarer
              KPI={(
                <PlannerKPI
                  kpiData={[
                    dueIn30Days,
                    overdueSeafarers,
                    missingRanks,
                    notContacted,
                    availableNotPlanned,
                  ]}
                />
              )}
              setIsAPIQueryInitialized={setIsAPIQueryInitialized}
              selectedFilters={selectedFilters[activeKey]}
              onFilterChange={(v) => onFilterChange(activeKey, v)}
              fetchData={handlePagination}
              data={filteredSeafarers}
              hasMoreData={filteredSeafarers.length < seafarersTotalCount}
              eventTracker={avlSeafarerEventTracker}
              loading={loading}
              init_sort={memoizedPagination.sortBy}
              handleAction={handleActionOfAvailableSeafarer}
              handleRemark={handleRemarkAction}
              setApiQuery={setApiQuery}
              selectedSeafarer={selectedSeafarer}
              relieverSectionData={relieverSectionData}
              handleSelectSeafarerToRelieve={setSelectedSeafarer}
              handlePaginationOfReliever={handlePaginationOfReliever}
              reliverSectionLoading={loaders.seafarers_to_be_relievers}
              selectedSeafarerToBeReliever={selectedSeafarerToBeReliever}
              setSelectedSeafarerToBeReliever={setSelectedSeafarerToBeReliever}
              relieverSectionPagination={memoizedReliverSectionPagination}
              filterData={dropDownData}
              onCrewPlanningFilterChange={onCrewPlanningFilterChange}
              selectedRanks={[]}
              seafarersTotalCount={seafarersTotalCount}
              tabName={activeKey}
              handleSearch={handleSearch}
              searchNotClicked={searchNotClicked}
              hasEditAccess={hasEditAccess}
            />
          )}
        </div>

        <UnplanRelief
          loadingConfirm={loaders.unplan_relief}
          show={isOpenUnplanReliefModal}
          seafarer={selectedReliefSeafarer.current}
          handleCloseUnplanRelief={() => {
            setIsOpenUnplanReliefModal(false);
          }}
          handleConfirmUnplan={handleConfirmUnplan}
          activeKey={activeKey}
        />
        {isOpenConfirmRecommendModal && (
          <ConfirmRecommendModal
            ringfencingOwners={ringfencingOwners}
            loadingConfirm={loaders.confirm_recommend}
            show={isOpenConfirmRecommendModal}
            seafarer={
              activeKey === SEAFARERS_TO_RELIEVE ? selectedSeafarer : selectedSeafarerToBeReliever
            }
            reliever={
              activeKey === SEAFARERS_TO_RELIEVE ? selectedSeafarerToBeReliever : selectedSeafarer
            }
            handleCloseRecommend={() => {
              setIsOpenConfirmRecommendModal(false);
              crewPlannerEventTracker('cancelRingFencingFirst', '');
            }}
            handleConfirmRecommend={handleConfirmRecommend}
          />
        )}
        {isOpenRemarkModal && (
          <PlannerRemarkModal
            loadingConfirm={loaders.confirm_remark}
            show={isOpenRemarkModal}
            seafarer={selectedSeafarerForRemarks.current.seafarer}
            handleCloseRemark={() => {
              setIsOpenRemarkModal(false);
            }}
            handleConfirmRemark={handleConfirmRemark}
            isEdit={!!getRemarks(selectedSeafarerForRemarks.current.seafarer, isClubbedOrUnclubbed)}
            getRemarks={(seafarer: any) => getRemarks(seafarer, isClubbedOrUnclubbed)}
          />
        )}
        <ScrollArrow eventTracker={getTabSpecificEventTracker(activeKey)} />
        {getBottomActionBar()}
      </div>
    </AccessHandlerWrapper>
  );
};

export default CrewPlanner;
