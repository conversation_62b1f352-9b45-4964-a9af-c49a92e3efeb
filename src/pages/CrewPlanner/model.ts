import {
  ListTabData,
  ON_LEAVE_SEAFARER,
  SEAFARERS_TO_RELIEVE,
} from '@src/constants/crewPlanner';
import seafarerService from '@src/service/seafarer-service';
import _ from 'lodash';

export const getTabName = (key) => {
  return ListTabData.find((e) => e.eventKey === key).tabName;
};

export const getRemarks = (seafarer: any, isClubbedOrUnclubbed: boolean) => {
  if (seafarer?.tableName === ON_LEAVE_SEAFARER && isClubbedOrUnclubbed) {
    return seafarer?.crew_planning?.seafarer?.crew_planning_remarks?.[0]?.remarks;
  }
  return seafarer?.crew_planning_remarks?.[0]?.remarks;
};

export const shouldIncludeMissingRanks = (apiQueryObj: any, activeKey: string) => {
  const { keyword } = apiQueryObj;
  return !(activeKey !== SEAFARERS_TO_RELIEVE || keyword);
};

export const filterDataAPI = {
  seafarer: { call: seafarerService.getSeafarerDropDownData, args: ['?values=ranks'] },
  tech_group: {
    call: seafarerService.getTechGroupDropDown,
    responseHandler: (res) => {
      const techGroup = res.response?.tech_group.map((v) => ({ id: v, value: v }));
      return { tech_group: techGroup };
    },
  },
  vessel: {
    call: seafarerService.getDropDownDataFromVessel,
    args: ['owners  { id, value }'],
    responseHandler: (res) => ({
      ...(res?.data ?? {}),
      vesselTypes: res.data?.vesselTypes?.map((v) => ({ id: String(v.id), value: v.value })),
    }),
  },
  reporting_office: {
    call: seafarerService.getSeafarerReportingOfficeDropDownData,
  },
};

export const updateNestedKey = <T>(
  list: T[],
  rowId: number,
  keyPath: string | (string | number)[],
  value: any,
): T[] => {
  const index = list.findIndex((row) => row.id === rowId);

  if (index !== -1) {
    const updatedList = [...list];
    const row = _.cloneDeep(updatedList[index]);
    _.set(row, keyPath, value);
    updatedList[index] = row;

    return updatedList;
  }

  return list;
};

export const prepareContractExpirySeafarers = (seafarers, ownershipDataById) => {
  return seafarers?.map((seafarer) => {
    const seafarerStatusHistory = seafarer?.seafarer_person?.seafarer_status_history?.[0];
    if (seafarerStatusHistory?.vessel_ownership_id) {
      seafarer.seafarer_person.seafarer_status_history[0].vessel_name =
        ownershipDataById[seafarerStatusHistory.vessel_ownership_id]?.name ??
        seafarerStatusHistory.vessel_name;
    }
    const vesselOwnershipId =
      seafarer?.seafarer_person?.seafarer_status_history?.[0]?.vessel_ownership_id ??
      seafarer?.vessel_ownership_id;
    seafarer.vessel_owner = ownershipDataById[vesselOwnershipId]?.owner?.value;
    return seafarer;
  });
};
