import _ from 'lodash';

const getEventTracker = ({ ga4react, getTabName, activeKey }) => {
  const CREW_PLANNER = 'Crew Planner';
  const CREW_PLANNER_ADVANCE_FILTER = 'Crew Planner - Advance Filtering';
  const CREW_PLANNER_DUE_RELIEVE = 'Crew Planner - Due Relieve';
  const CREW_PLANNER_ON_LEAVE = 'Crew Planner - On Leave';

  const ga4EventTrigger = (action, category, label) => {
    try {
      ga4react?.event(action, _.toString(label), category, false);
    } catch (error) {
      console.log(error);
    }
  };
  const eventTracker = (type, value) => {
    switch (type) {
      case 'addFilter':
        ga4EventTrigger('Filter Added', CREW_PLANNER_ADVANCE_FILTER, value);
        break;
      case 'filterTypeChange':
        ga4EventTrigger('Category Selection', CREW_PLANNER_ADVANCE_FILTER, value);
        break;
      case 'filterSubTypeChange':
        ga4EventTrigger('SubTypeChange', CREW_PLANNER_ADVANCE_FILTER, value);
        break;
      case 'sortBy':
        ga4EventTrigger('Sorting', 'List', `Seafarer List Page: Sort By - ${value}`);
        break;
      case 'yearsOfLongService':
        ga4EventTrigger('Years of Long Service', 'Seafarer list - filter', `${value}`);
        break;
      default:
        ga4EventTrigger('Click', value, 'Crew Planner List Page');
        break;
    }
  };

  const contractExpEventTracker = (type, value) => {
    switch (type) {
      case 'rankSelection':
        ga4EventTrigger('Rank Selection', CREW_PLANNER_DUE_RELIEVE, value);
        break;
      case 'filter':
        ga4EventTrigger('Filter', CREW_PLANNER_DUE_RELIEVE, value);
        break;
      case 'tabSwitching':
        ga4EventTrigger('Tab Switching', CREW_PLANNER_DUE_RELIEVE, value);
        break;
      case 'vesselLink':
        ga4EventTrigger('Vessel Link', CREW_PLANNER_DUE_RELIEVE, value);
        break;
      case 'statusTabSwitched':
        ga4EventTrigger('Status Tab Switched', CREW_PLANNER_DUE_RELIEVE, value);
        break;
      case 'missingPersonnelSelected':
        ga4EventTrigger('Missing Personnel Selected', CREW_PLANNER_DUE_RELIEVE, 'Click');
        break;
      case 'additionalCrewSelected':
        ga4EventTrigger('Additional Crew Selected', CREW_PLANNER_DUE_RELIEVE, 'Click');
        break;
      case 'seafarerNameLink':
        ga4EventTrigger('Visit Seafarer Profile', CREW_PLANNER_DUE_RELIEVE, value);
        break;
      case 'onBoardSeafarerSelected':
        ga4EventTrigger(
          'On-Board Seafarer Selected',
          CREW_PLANNER_DUE_RELIEVE,
          `Onboard Seafarer Selected ${value}`,
        );
        break;
      case 'relieverSeafarerSelected':
        ga4EventTrigger(
          'Reliever Seafarer Selected',
          CREW_PLANNER_DUE_RELIEVE,
          `Reliever Seafarer Selected ${value}`,
        );
        break;
      case 'remarkAddedAndClickedSave':
        ga4EventTrigger('Remark is added and clicked save', CREW_PLANNER_DUE_RELIEVE, 'Click');
        break;
      case 'remarkEditedAndClickedSave':
        ga4EventTrigger('Remark Edited and clicked Save', CREW_PLANNER_DUE_RELIEVE, 'Click');
        break;
      case 'clickingOnTheAllAvailableSeafarerTab':
        ga4EventTrigger('Clicking on the All Available Seafarer Tab', CREW_PLANNER_DUE_RELIEVE, 'Available Seafarer List Click');
        break;
      case 'selectionOfAReplacerSeafarer':
        ga4EventTrigger('Selection of a Replacer Seafarer', CREW_PLANNER_DUE_RELIEVE, `Replacer Seafarer Selected ${value}`);
        break;
      case 'confirmingThePlan':
        ga4EventTrigger('Confirming the plan', CREW_PLANNER_DUE_RELIEVE, 'Click');
        break;
      case 'clickingCancelOnThePopUp':
        ga4EventTrigger('Clicking Cancel on the pop-up', CREW_PLANNER_DUE_RELIEVE, 'Click');
        break;
      case 'vesselSearch':
        ga4EventTrigger('Vessel Search', CREW_PLANNER_DUE_RELIEVE, '');
        break;
      default:
        eventTracker(type, value);
        break;
    }
  };

  const avlSeafarerEventTracker = (type, value) => {
    switch (type) {
      case 'rankSelection':
        ga4EventTrigger('Selection of Rank', CREW_PLANNER_ON_LEAVE, value);
        break;
      case 'filter':
        ga4EventTrigger('Filter', CREW_PLANNER_ON_LEAVE, value);
        break;
      case 'statusTabSwitched':
        ga4EventTrigger('Status Tab Switched', CREW_PLANNER_ON_LEAVE, value);
        break;
      case 'remarkAddedAndClickedSave':
        ga4EventTrigger('Remark is added and clicked save', CREW_PLANNER_ON_LEAVE, 'Click');
        break;
      case 'remarkEditedAndClickedSave':
        ga4EventTrigger('Remark Edited and clicked Save', CREW_PLANNER_ON_LEAVE, 'Click');
        break;
      case 'seafarerNameLink':
        ga4EventTrigger('Visit Seafarer Profile', CREW_PLANNER_ON_LEAVE, value);
        break;
      case 'vesselLink':
        ga4EventTrigger('Vessel Link', CREW_PLANNER_ON_LEAVE, value);
        break;
      case 'onBoardSeafarerSelected':
        ga4EventTrigger(
          'Selection of an Onboard Seafarer',
          CREW_PLANNER_ON_LEAVE,
          `Onboard Seafarer Selected ${value}`,
        );
        break;
      case 'relieverSeafarerSelected':
        ga4EventTrigger(
          'Selection of a Relieve Seafarer',
          CREW_PLANNER_ON_LEAVE,
          `Reliever Seafarer Selected ${value}`,
        );
        break;
      case 'confirmingThePlan':
        ga4EventTrigger('Confirming the plan', CREW_PLANNER_ON_LEAVE, 'Click');
        break;
      case 'clickingCancelOnThePopUp':
        ga4EventTrigger('Clicking Cancel on the pop-up', CREW_PLANNER_ON_LEAVE, 'Click');
        break;
      default:
        eventTracker(type, value);
        break;
    }
  };

  const crewPlannerEventTracker = (type, value) => {
    switch (type) {
      case 'selectTab':
        ga4EventTrigger('Tab Switching', CREW_PLANNER, value);
        break;
      case 'remarks':
        ga4EventTrigger('Adding or Editing Remarks', CREW_PLANNER, value);
        break;
      case 'reliefSeafarer':
        ga4EventTrigger('Relief Seafarer link', 'Crew Planner - Action Column', value);
        break;
      case 'cancelPlannedSeafarer':
        ga4EventTrigger('Confirm button', 'Crew Planner - Unplan Relief Modal', value);
        break;
      case 'recommendSeafarer':
        ga4EventTrigger('Recommend link', 'Crew Planner - Action Column', value);
        break;
      case 'timeSpentToReleifSeafarer':
        ga4EventTrigger('Time Spent to Relief Seafarer', CREW_PLANNER, value);
        break;
      case 'confirmRingFencing':
        ga4EventTrigger('Click the Confirm button', 'Ring Fencing Warning', 'Click');
        break;
      case 'cancelRingFencingFirst':
        ga4EventTrigger('Click the Cancel button', 'Ring Fencing warning first', 'Click');
        break;
      case 'cancelRingFencingSecond':
        ga4EventTrigger('Click the Cancel button', 'Ring Fencing Warning second', 'Click');
        break;
      default:
        eventTracker(type, value);
        break;
    }
  };
  return {
    contractExpEventTracker,
    avlSeafarerEventTracker,
    eventTracker,
    crewPlannerEventTracker,
  };
};
export default getEventTracker;
