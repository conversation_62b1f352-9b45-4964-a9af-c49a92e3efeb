import {
  DOCUMENT_SCORE_GRADE,
  ON_LEAVE_SEAFARER,
  SEAFARERS_TO_RELIEVE,
} from '@src/constants/crewPlanner';
import { getPageSort } from '@src/util/local-storage-helper';
import { isEqual, omit } from 'lodash';
import { STATUS_COLORS } from '../../model/constants';

export const getVesselType = (seafarer) => {
  if (checkMissingPersonnel(seafarer)) return seafarer?.vessel_type?.value;
  if (checkAdditionalRequest(seafarer)) return seafarer?.vessel_type;
  return seafarer?.seafarer_person?.seafarer_status_history?.[0]?.vessel_type ?? '';
};
export const getVesselIdFromSeafarer = (seafarer) => {
  if (checkMissingPersonnel(seafarer) || checkAdditionalRequest(seafarer))
    return seafarer?.vessel_id;
  return (
    seafarer?.seafarer_person?.seafarer_status_history?.[0]?.vessel_id ??
    seafarer?.crew_planning?.crew_planning_audit_log?.[0]?.vessel_id ??
    ''
  );
};
export const getSeafarerRank = (seafarer) => {
  if (checkMissingPersonnel(seafarer)) return seafarer?.rank;
  if (checkAdditionalRequest(seafarer)) return seafarer?.rank?.value;
  return seafarer?.seafarer_person?.seafarer_status_history?.[0]?.seafarer_rank?.value ?? '';
};
export const getSeafarerVesselId = (seafarer) => {
  return seafarer?.seafarer_person?.seafarer_status_history?.[0]?.vessel_id ?? seafarer?.vessel_id;
};
export const getSeafarerVesselOwnershipId = (seafarer) => {
  return (
    seafarer?.seafarer_person?.seafarer_status_history?.[0]?.vessel_ownership_id ??
    seafarer?.vessel_ownership_id
  );
};
export const checkMissingPersonnel = (seafarer) => {
  return (
    !seafarer?.isAdditionalRequest &&
    (seafarer?.isMissingPersonnel || (!seafarer?.seafarer_person && seafarer?.rank) || !seafarer)
  );
};

export const checkAdditionalRequest = (seafarer) => {
  return seafarer?.isAdditionalRequest;
};

export const getDefaultSort = () => {
  const seafarersToRelieve = getPageSort(SEAFARERS_TO_RELIEVE);
  const availableSeafarer = getPageSort(ON_LEAVE_SEAFARER);
  return {
    [SEAFARERS_TO_RELIEVE]: seafarersToRelieve?.length
      ? seafarersToRelieve
      : [
          {
            id: 'seafarer_person:seafarer_status_history.expected_contract_end_date',
            desc: false,
          },
        ],
    [ON_LEAVE_SEAFARER]: availableSeafarer?.length
      ? availableSeafarer
      : [
          {
            id: 'seafarer_contact_log.availability_date',
            desc: false,
          },
        ],
  };
};

export const getSelelectRanks = (apiQueryObj: Object) => {
  return (
    apiQueryObj[historyRankKey]?.split('|') || apiQueryObj['target_rank.value']?.split(',') || []
  );
};

export const initialSectionData = {
  data: [],
  total: 0,
};
export const initialPagination = { pageSize: 10, pageIndex: 0 };
export const historyRankKey = 'seafarer_person:seafarer_status_history:seafarer_rank.value';
export const availabilityRankKey = 'target_rank.value';
export const DEFAULT_RELIVER_SECTION_PAGINATION = {
  [SEAFARERS_TO_RELIEVE]: {
    sortBy: [
      {
        id: 'seafarer_contact_log.availability_date',
        desc: false,
      },
    ],
    ...initialPagination,
  },
  [ON_LEAVE_SEAFARER]: {
    sortBy: [
      {
        id: 'seafarer_person:seafarer_status_history.expected_contract_end_date',
        desc: false,
      },
    ],
    ...initialPagination,
  },
};

export const getScoringColor = (score: any) => {
  let scoreColor = '';
  if (score === DOCUMENT_SCORE_GRADE.Fail) return 'average-score-wrapper average-score-red';
  if (score === DOCUMENT_SCORE_GRADE.Pass) return 'average-score-wrapper average-score-green';
  if (score >= 85) {
    scoreColor = 'average-score-green';
  } else if (score < 85 && score >= 70) {
    scoreColor = 'average-score-green-light';
  } else if (score < 70 && score >= 60) {
    scoreColor = 'average-score-yellow';
  } else if (score < 60 && score >= 50) {
    scoreColor = 'average-score-orange';
  } else if (score < 50) {
    scoreColor = 'average-score-red';
  }
  return `average-score-wrapper ${scoreColor}`;
};

export function getScoreDifferenceColor(score: string) {
  let scoreColor = 'onboarding-with-joint-experience';
  switch (score) {
    case 'A':
      scoreColor = `${STATUS_COLORS.GREEN}-box-planning-relieve`;
      break;
    case 'B':
      scoreColor = `${STATUS_COLORS.GREEN}-box-planning-relieve`;
      break;
    case 'C':
      scoreColor = `${STATUS_COLORS.ORANGE}-box-planning-relieve`;
      break;
    case 'D':
      scoreColor = `${STATUS_COLORS.ORANGE}-box-planning-relieve`;
      break;
    case 'F':
      scoreColor = `${STATUS_COLORS.RED}-box-planning-relieve`;
      break;
    default:
  }
  return scoreColor;
}
export const getVesselIdsForQuery = (apiQueryObj, vessels) => {
  return apiQueryObj['seafarer_person:seafarer_status_history.vessel_id']
    ?.split('|')
    .map((vessel_id) => vessels.find((v) => v.id == vessel_id).ownership_id)
    .filter((n) => Number.isInteger(n))
    .join(',');
};
export const compareKPIPayload = (payload1, payload2, IGNORE_KEYS) => {
  return isEqual(omit(payload1, IGNORE_KEYS), omit(payload2, IGNORE_KEYS));
};
