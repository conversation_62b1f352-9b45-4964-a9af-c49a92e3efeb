import React from 'react';
import { Form } from 'react-bootstrap';
import { getVisiblePages } from '../util/pagination';

const PageNum = ({ active, disabled, children, onClick }) => {
  const className = `page-num page-num-${active ? 'active' : 'inactive'} page-num-${
    disabled ? 'disabled' : 'enabled'
  }`;
  return (
    <div className={className} onClick={onClick} aria-hidden="true">
      {children}
    </div>
  );
};

const PaginationBar = ({
  canPreviousPage,
  canNextPage,
  pageCount,
  gotoPage,
  nextPage,
  previousPage,
  setPageSize,
  pageIndex,
  pageSize,
  // sortBy,
}) => {
  const visiblePages = getVisiblePages(pageIndex, pageCount);
  const goToNextPage = () => nextPage();
  const goToPreviousPage = () => previousPage();
  return (
    <div className="d-flex justify-content-center p-4">
      <div className="page-number-border">
        <PageNum onClick={goToPreviousPage} disabled={!canPreviousPage}>
          {'<'}
        </PageNum>
        {visiblePages.map((page, index, array) => (
          <PageNum
            key={page}
            active={page - 1 === pageIndex}
            disabled={page - 1 === pageIndex}
            onClick={() => gotoPage(page - 1)}
          >
            {array[index - 1] + 2 < page ? `...${page}` : page}
          </PageNum>
        ))}
        <PageNum onClick={goToNextPage} disabled={!canNextPage}>
          {'>'}
        </PageNum>
      </div>
      <Form inline>
        <Form.Control
          as="select"
          value={pageSize}
          className="ml-3"
          onChange={(e) => {
            setPageSize(Number(e.target.value));
          }}
        >
          {[10, 50, 100, 300].map((size) => (
            <option key={size} value={size}>
              Show {size}
            </option>
          ))}
        </Form.Control>
      </Form>
    </div>
  );
};

export { PaginationBar };
