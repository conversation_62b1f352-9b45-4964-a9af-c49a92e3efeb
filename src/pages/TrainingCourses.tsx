import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useHistory, useLocation, useParams } from 'react-router-dom';
import { Container, Col, Row, Button, Form, ButtonGroup, Dropdown } from 'react-bootstrap';
import AccessHandlerWrapper from '../component/common/AccessHandlerWrapper';
import { SEAFARER_TRAINING_COURSES_PAGE } from '../model/TabData';
import { useDebounce, useDebouncedCallback } from 'use-debounce';
import SearchParametersView from '../component/SearchParametersView';
import AdvancedSearch from '../component/advanced_search/AdvancedSearch';
import AdvancedSearchMobile from '../component/advanced_search/AdvancedSearchMobile';
import {
  mapQueryStringToSearchCriteria,
  generateQuickSearch,
  addSortOrPaginateParams,
} from '../util/advance-search/search-query';
import SearchController from '../controller/search-controller';
import httpService from '../service/http-service';
import _ from 'lodash';
// import defaultFilters from '../util/advance-search/seafarer-report-default-filters';
import trainingCoursesService from '../service/training-courses-service';
import { Filter, SeafarerTrainingCourse, PaginateData } from '../types/trainingCourses';
import GA4React from 'ga-4-react';
import TrainingCoursesTable from '../component/TrainingCourses/TrainingCoursesTable';
import { storePageNumber } from '../util/local-storage-helper';
/*global Props, roleConfig, ga4react*/
/*eslint no-undef: "error"*/
interface Props {
  roleConfig: any;
  ga4react: GA4React;
  seafarerhkid: number;
  seafarerPersonId: number;
}

const DEFAULT_TOP_MARGIN = 150;
const EVERY_ITEM = 60;
const initialPagination = {
  pageSize: 10,
  pageIndex: 0,
  sortBy: [],
};

const FIELDS_MAPPING = {
  training_category: 'category',
  sub_category: 'subcategory',
  course_name: 'course',
  required_course: 'requiredcourse',
  document_type: 'documenttype',
  passed_test: 'passedtest',
  date_completed: 'datecompleted',
} as any;
const TrainingCourses = ({ roleConfig, ga4react, seafarerPersonId, seafarerhkid }: Props) => {
  const tab = 'training-courses';
  const history = useHistory();
  const activeKey = tab;
  const [pageCount, setPageCount] = useState(0);
  const initSort = initialPagination.sortBy;
  const [totalCount, setTotalCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const query = useLocation().search.substring(1);
  const [apiQuery, setApiQuery] = useState('');
  const criteria = [];
  const hasNoSearchCriteria = criteria.length === 0;
  const [showAdvSearchMobile, setShowAdvSearchMobile] = useState(false);
  const [showAdvSearch, setShowAdvSearch] = useState(false);
  const [searchedKeyword, setSearchedKeyword] = useState('');
  const [filteredRecords, setFilteredRecords] = useState([]);
  const [filteredPageCount, setFilteredPageCount] = useState(0);
  const [filters, setFilters] = useState<Filter[]>([]);
  const [debouncedFilters] = useDebounce(filters, 700);
  const searchInputRef = useRef(null);
  // eslint-disable-next-line no-unused-vars
  const hasRoleAccess = roleConfig.seafarer.training.edit || roleConfig.seafarer.training.view;
  const tableRef = useRef(null);

  useEffect(() => {
    loadDropDownDataAndDefaultFilters();
  }, []);

  useEffect(() => {
    storePageNumber(tab, 0);
    if (debouncedFilters.length) {
      let filteredList = removeEmptyFilters(debouncedFilters);
      if (filteredList.length === 0) {
        window.history.replaceState({}, '', `${history.location.pathname}`);
        setApiQuery('');
        return;
      }
      const controller = new SearchController();
      const apiFilterQuery = controller.getQuery(filteredList);
      setApiQuery(apiFilterQuery);
      const filterQuery = controller.getQuery(
        filteredList.filter((i) => i.defaultTab === undefined),
      );
      window.history.replaceState({}, '', `${history.location.pathname}?${filterQuery}`);
    } else {
      window.history.replaceState({}, '', `${history.location.pathname}`);
      setApiQuery('');
    }
  }, [debouncedFilters]);

  const ga4EventTrigger = (action: string, category: string, label: string) => {
    try {
      ga4react?.event(action, _.toString(label), category, false);
    } catch (error) {
      console.log(error);
    }
  };

  const eventTracker = (type: string, value: string) => {
    switch (type) {
      case 'pageSizeSwitch':
        ga4EventTrigger(
          'Number of Rows',
          'Seafarer Training Courses - list',
          `Seafarer Training Courses - ${value}`,
        );
        break;
      case 'KeywordSearch':
        ga4EventTrigger('Submit keyword filter', 'Seafarer Training Courses - Filter', `${value}`);
        break;
      case 'filterSubTypeChange':
        ga4EventTrigger('Value', 'Advance Search', `Seafarer Reports Page - ${value}`);
        break;
      case 'sortBy':
        ga4EventTrigger(
          'Sorting',
          'Seafarer Training Courses - list',
          `Seafarer Training Courses: Sort By - ${value}`,
        );
        break;
      default:
        break;
    }
  };

  const loadDropDownDataAndDefaultFilters = async () => {
    try {
      const searchController = new SearchController();
      const result = await searchController.onLoadPage(SEAFARER_TRAINING_COURSES_PAGE);
      /*using window location instead of active key because when user quickly switches 
          tab during initial load activekey new value is not reflected yet during this function execution*/
      setDefaultFilters(
        tab,
        mapQueryStringToSearchCriteria(
          query,
          result.dropDownData,
          tab as any,
          SEAFARER_TRAINING_COURSES_PAGE,
        ),
      );
      setShowAdvSearch(true);
    } catch (error) {
      console.log('## error', error);
    }
  };

  const handleKeywordChange = useDebouncedCallback((value) => {
    eventTracker('KeywordSearch', value);
    storePageNumber(tab, 0);
    setSearchedKeyword(value.trim().toLowerCase());
  }, 500);

  const removeEmptyFilters = (filter: Filter[]) => {
    return filter.filter(
      (item) => item.subtype !== null && item.subtype !== undefined && item.subtype !== '',
    );
  };

  const ontoggle = () => {
    eventTracker('advancedSearch', `${showAdvSearch ? 'close' : 'open'}`);
    setShowAdvSearch(!showAdvSearch);
  };

  const queryListData = async (sortPaginateData: PaginateData) => {
    try {
      setLoading(true);
      setPageCount(0);
      setFilteredRecords([]);
      setFilteredPageCount(0);
      setTotalCount(0);
      let queryParams = generateQuickSearch({ keyword: searchedKeyword });
      if (queryParams) {
        queryParams = '&' + queryParams;
      }
      queryParams = addSortOrPaginateParams(sortPaginateData, queryParams);
      if (filters.length) {
        let filteredList = removeEmptyFilters(filters);
        if (filteredList.length !== 0) {
          const controller = new SearchController();
          const filterQuery = controller.getQuery(filteredList);
          queryParams = queryParams + '&' + filterQuery;
        }
      } else {
        queryParams += `&${apiQuery}`;
      }

      const { data } = await trainingCoursesService.getTrainingCourses(seafarerhkid, queryParams);
      const seafarerRecords = data.results;
      const pagination = data.pagination[0];
      const filteredRecords = seafarerRecords.map((seafarerRecord: any) => {
        const updatedSeafarerRecord: SeafarerTrainingCourse | any = {};
        Object.keys(seafarerRecord).forEach((s: string) => {
          if (FIELDS_MAPPING[s]) {
            updatedSeafarerRecord[FIELDS_MAPPING[s]] = seafarerRecord[s];
          } else {
            updatedSeafarerRecord[s] = seafarerRecord[s];
          }
        });
        return { ...updatedSeafarerRecord, keyword: searchedKeyword };
      });

      setFilteredRecords(filteredRecords);
      setTotalCount(pagination.totalCount);
      setFilteredPageCount(Math.ceil(pagination.totalCount / sortPaginateData.pageSize));
      if (pagination) {
        setPageCount(Math.ceil(pagination.totalCount / sortPaginateData.pageSize));
      }
      setLoading(false);
    } catch (error) {
      if (httpService.axios.isCancel(error)) {
        return;
      }
      console.log('error:', error);
      setLoading(false);
    }
  };

  const getData = useCallback(
    async ({ pageSize, pageIndex, sortBy }: PaginateData) => {
      // Quick search active
      if (searchedKeyword.length && hasNoSearchCriteria) {
        handleQuickSearch({ pageSize, pageIndex, sortBy });
      } else {
        await fetchData({ pageSize, pageIndex, sortBy });
      }
      return;
    },
    [searchedKeyword, activeKey, query, apiQuery],
  );

  const handleQuickSearch = useDebouncedCallback(
    async (sortPaginateData) => {
      await queryListData(sortPaginateData);
    },
    700,
    // @ts-ignore
    [query, searchedKeyword, activeKey, apiQuery],
  );

  const fetchData = useCallback(
    async (sortPaginateData: PaginateData) => {
      await queryListData(sortPaginateData);
    },
    [query, searchedKeyword, activeKey, apiQuery],
  );

  const clearFilters = () => {
    const filteredList = removeEmptyFilters(filters);
    if (filteredList.length === 0) {
      setFilters([]);
    }
    if (searchedKeyword.length || filteredList.length) {
      if (searchInputRef?.current) {
        // @ts-ignore
        searchInputRef.current.value = '';
      }
      setSearchedKeyword('');
      setFilters([]);
      setFilteredRecords([]);
      setFilteredPageCount(0);
      setLoading(false);
      history.push(`/seafarer/details/${seafarerPersonId}/${SEAFARER_TRAINING_COURSES_PAGE}`);
    }
    eventTracker('clearAll', 'Clear All');
  };

  const setDefaultFilters = (key: string, additionalQueryFilters: Filter[]) => {
    const controller = new SearchController();
    let additionalFilters = additionalQueryFilters
      ? additionalQueryFilters.filter((i) => {
          return i.type.validTabs === undefined || i.type.validTabs.includes(key);
        })
      : undefined;
    let filterArray = [...filters].filter((i) => {
      return (
        (i.defaultTab === undefined || i.defaultTab === key) &&
        (i.type.validTabs === undefined || i.type.validTabs.includes(key))
      );
    });

    let newFilter: Filter[] = [];
    if (additionalFilters) {
      newFilter = newFilter.map(
        (i) => (i = additionalFilters?.find((item) => item?.type?.type === i?.type?.type) ?? i),
      );
      const existingFilter = newFilter.map((i) => i?.type?.type);
      additionalFilters = additionalFilters.filter((i) => !existingFilter.includes(i?.type?.type));
      setFilters([...newFilter, ...filterArray, ...additionalFilters]);
      setApiQuery(controller.getQuery([...newFilter, ...filterArray, ...additionalFilters]));
    } else {
      newFilter = newFilter.map(
        (i) => (i = filterArray.find((item) => item?.type?.type === i?.type?.type) ?? i),
      );
      const existingFilter = newFilter.map((i) => i?.type?.type);
      filterArray = filterArray.filter((i) => !existingFilter.includes(i?.type?.type));
      setFilters([...newFilter, ...filterArray]);
      setApiQuery(controller.getQuery([...newFilter, ...filterArray]));
    }
  };

  const displayQuickSearchResult = searchedKeyword.length && hasNoSearchCriteria;

  const currentPageCount = displayQuickSearchResult ? filteredPageCount : pageCount;

  return (
    <AccessHandlerWrapper hasRoleAccess={hasRoleAccess}>
      <Container>
        {showAdvSearchMobile ? (
          <div className="mobileAdvSearch">
            <AdvancedSearchMobile
              setShowAdvSearchMobile={setShowAdvSearchMobile}
              roleConfig={roleConfig}
              filters={filters}
              setFilters={setFilters}
              tab={activeKey}
              page={SEAFARER_TRAINING_COURSES_PAGE}
            />
          </div>
        ) : (
          <>
            <Row className="no-print">
              <Col xs={6} md={3}>
                <div className="mt-4 quick-filters-button">
                  <Form.Control
                    id="search-bar"
                    type="text"
                    name="keyword"
                    placeholder="Type keywords to filter"
                    ref={searchInputRef}
                    defaultValue=""
                    onClick={() => eventTracker('KeywordSearchClick', 'Keywork Search Click')}
                    onChange={(e) => handleKeywordChange(e.target.value)}
                  />
                </div>
              </Col>
              <Col xs={6} md="auto">
                <div className="mobileAdvSearch">
                  <ButtonGroup className="advanced-search-seafarer mt-4 w-100">
                    <Button
                      variant="outline-primary"
                      onClick={() => {
                        setShowAdvSearchMobile(true);
                      }}
                    >
                      Advanced Search
                    </Button>
                  </ButtonGroup>
                </div>
                <div className="desktopAdvSearch">
                  <ButtonGroup className="advanced-search-seafarer mt-4">
                    <Dropdown alignRight={false} onToggle={ontoggle} show={showAdvSearch}>
                      <Dropdown.Toggle
                        onClick={ontoggle}
                        variant="outline-primary"
                        id="dropdown-advanced-search"
                      >
                        Advanced Search{' '}
                        {removeEmptyFilters(debouncedFilters).length
                          ? `(${removeEmptyFilters(debouncedFilters).length})`
                          : ''}
                      </Dropdown.Toggle>
                      <Dropdown.Menu>
                        <div
                          className="advanced-search-seafarer-menu"
                          data-testid="advanced-search-menu"
                        >
                          <AdvancedSearch
                            roleConfig={roleConfig}
                            filters={filters}
                            setFilters={setFilters}
                            eventTracker={eventTracker}
                            tab={activeKey}
                            page={SEAFARER_TRAINING_COURSES_PAGE}
                          />
                        </div>
                      </Dropdown.Menu>
                    </Dropdown>
                  </ButtonGroup>
                </div>
              </Col>
              <Col xs="auto">
                <Button variant="outline-primary" className="mt-4" onClick={clearFilters}>
                  Clear All
                </Button>
              </Col>
            </Row>
            {filters.length > 0 && (
              <Row className="mobileAdvSearch">
                <SearchParametersView
                  criteria={filters}
                  setShowAdvSearchMobile={setShowAdvSearchMobile}
                />
              </Row>
            )}
            <Row
              className="desktopAdvSearch"
              style={{
                marginTop: showAdvSearch
                  ? DEFAULT_TOP_MARGIN +
                    (filters.length > 0 ? filters.length : 1) * EVERY_ITEM +
                    'px'
                  : '0px',
              }}
            ></Row>
            <Row>
              <Col>
                <TrainingCoursesTable
                  data={filteredRecords}
                  fetchData={getData}
                  pageCount={currentPageCount}
                  visitSeafarer={() => {}}
                  eventTracker={eventTracker}
                  loading={loading}
                  roleConfig={roleConfig}
                  quickSearchParams={searchedKeyword}
                  advancedSearchParams={apiQuery}
                  init_sort={initSort}
                  totalCount={totalCount}
                  tableRef={tableRef}
                  tabName={'training-courses'}
                />
              </Col>
            </Row>
          </>
        )}
      </Container>
    </AccessHandlerWrapper>
  );
};
export default TrainingCourses;
