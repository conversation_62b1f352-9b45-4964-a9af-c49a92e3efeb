/* eslint-disable react/prop-types */
import React, { useState, useEffect } from 'react';
import { Col, Row, Button, ButtonToolbar } from 'react-bootstrap';
import ContactDetailsSection from '../component/Details/ContactDetailsSection';
import ContactSummary from '../component/Availability/ContactSummary';
import seafarerService from '../service/seafarer-service';
import DocumentTable from '../component/OtherDocument/DocumentTable';
import { dateAsString } from '../model/utils';
import UpdateContactLog from '../component/Availability/UpdateContactLog';
import _ from 'lodash';
import AccessHandlerWrapper from '../component/common/AccessHandlerWrapper';
import { useAccess } from '@src/component/common/Access';

const generateContactLogColumns = () => {
  return [
    {
      Header: 'Contact Date',
      id: 'contact-date',
      accessor: function contactDateAccessor(row) {
        return (
          <div className="contact-log-contact-date">
            {row?.contact_date ? dateAsString(row?.contact_date) : '---'}
          </div>
        );
      },
      disableSortBy: true,
      maxWidth: 110,
    },
    {
      Header: 'Contact Mode',
      id: 'contact-mode',
      accessor: function contactModeAccessor(row) {
        return <div>{row?.contact_mode ?? '---'}</div>;
      },
      disableSortBy: true,
      maxWidth: 110,
    },
    {
      Header: 'Next Contact Date',
      id: 'next-contact-date',
      accessor: function nextContactDateAccessor(row) {
        return <div>{row?.next_contact_date ? dateAsString(row?.next_contact_date) : '---'}</div>;
      },
      disableSortBy: true,
      maxWidth: 110,
    },
    {
      Header: 'Docs in Hand',
      id: 'docs-in-hand',
      accessor: function docsInHandAccessor(row) {
        return <div>{row?.docs_in_hand ? 'Yes' : 'No'}</div>;
      },
      disableSortBy: true,
      maxWidth: 100,
    },
    {
      Header: 'Availability Date',
      id: 'availability-date',
      accessor: function availabilitydateAccessor(row) {
        return <div>{row?.availability_date ? dateAsString(row?.availability_date) : '---'}</div>;
      },
      disableSortBy: true,
      maxWidth: 100,
    },
    {
      Header: 'Availability Remark',
      id: 'availability-remark',
      accessor: function availabilityRemarkAccessor(row) {
        return <div>{row?.availability_remarks ?? '---'}</div>;
      },
      disableSortBy: true,
      maxWidth: 100,
    },
    {
      Header: 'Added by',
      id: 'added-by',
      accessor: function addedByAccessor(row) {
        return <div>{row?.created_by ?? '---'}</div>;
      },
      disableSortBy: true,
      maxWidth: 100,
    },
  ];
};

const AvailabilityPage = ({ seafarer, seafarerId, eventTracker }) => {
  const [contactsLogData, setContactsLogData] = useState([]);
  const { roleConfig } = useAccess();
  const [loading, setLoading] = useState(false);
  const [showContactLogModal, setShowContactLogModal] = useState(false);
  const [isCallContactLogData, setIsCallContactLogData] = useState(false);

  const { can_view_contacts: canViewContacts } = seafarer;

  useEffect(() => {
    (async () => {
      try {
        setLoading(true);
        const { data } = await seafarerService.getContactLog(seafarerId);
        if (data) {
          const contactsLog = _.orderBy(
            data,
            [(contactLogObj) => new Date(contactLogObj.created_at)],
            ['desc'],
          );
          setContactsLogData(contactsLog);
        }
      } catch (error) {
        console.error(`Get Contact Log by Seafarer ID: ${seafarerId} failed. Error: ${error}`);
      }
      setLoading(false);
    })();
  }, [seafarerId, isCallContactLogData]);

  const contactLogColumns = generateContactLogColumns();
  const updateContactLofBtnHandler = () => {
    setShowContactLogModal(true);
  };
  const cancelButtonHandler = () => {
    setShowContactLogModal(false);
  };

  return (
    <AccessHandlerWrapper hasRoleAccess={!roleConfig.seafarer.hidden.contactDetails}>
      {showContactLogModal && (
        <UpdateContactLog
          seafarerId={seafarerId}
          contactsLog={contactsLogData}
          cancelButtonHandler={cancelButtonHandler}
          eventTracker={eventTracker}
          setIsCallContactLogData={setIsCallContactLogData}
        />
      )}
      {canViewContacts && (
        <ContactDetailsSection
          contacts={seafarer?.seafarer_person ? seafarer?.seafarer_person?.seafarer_contacts : []}
          addresses={seafarer?.seafarer_person ? seafarer?.seafarer_person?.addresses : []}
          isAddColumnWiseCss={true}
        />
      )}
      <Row>
        <Col>
          <div className="details_page__table_head">
            <div className="font-weight-bold p-2">Contact Summary</div>
          </div>
        </Col>
      </Row>
      <ContactSummary contactsLog={contactsLogData} loading={loading} />
      <div className="details_page__table_head">
        <Row>
          <Col>
            <div className="font-weight-bold p-2">Contact Log</div>
          </Col>
          <Col>
            <ButtonToolbar className="float-right pt-2">
              {roleConfig.seafarer.edit.contactDetails && (
                <Button
                  variant="outline-primary"
                  size="sm"
                  onClick={() => {
                    updateContactLofBtnHandler();
                  }}
                >
                  Update Contact Log
                </Button>
              )}
            </ButtonToolbar>
          </Col>
        </Row>
      </div>
      <Row>
        <Col>
          <DocumentTable
            data={contactsLogData}
            loading={loading}
            columns={contactLogColumns}
            dataTestId="contact-log-table"
          />
        </Col>
      </Row>
    </AccessHandlerWrapper>
  );
};

export default AvailabilityPage;
