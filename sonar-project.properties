sonar.projectKey=paris2-web-seafarer
sonar.projectName=paris2-web-seafarer
sonar.sourceEncoding=UTF-8
sonar.sources=src
sonar.exclusions=src/component/grid/grid.tsx,src/test/pages/CrewListPage.test.tsx,src/test/pages/Details.test.tsx
sonar.cpd.exclusions=src/pages/mockResources/**/*, src/__mocks__/**/*, src/__test__/**/*, src/service/__mocks__/**/*, src/test/**/*, src/pages/PdfViewer.js
sonar.coverage.exclusions=src/pages/mockResources/**/*, src/__mocks__/**/*, src/__test__/**/*,src/constants/**/*,src/model/**/*,src/service/__mocks__/**/*,src/paris2-seafarer.js,src/root.component.js,src/seafarer.scss,src/set-public-path.js,src/setupTests.js,src/setupGuide.js,src/test/**/*,**.test.tsx,**.test.ts,**/mockResources/**,**/PdfViewer.js,**/excel-export.ts,**/src/types,**test-utils.tsx,**src/test/util**,**src/test/resources**,**src/service**
sonar.javascript.coveragePlugin=lcov
sonar.javascript.lcov.reportPaths=src/coverage/lcov.info