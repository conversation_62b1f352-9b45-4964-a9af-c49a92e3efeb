{
  "env": {
    "commonjs": true,
    "browser": true,
    "es6": true,
    "jest": true
  },
  "plugins": ["jest", "sonarjs", "prettier", "react", "@typescript-eslint"],
  "extends": [
    "airbnb",
    "airbnb-typescript",
    "airbnb/hooks",
    "plugin:react/recommended",
    "plugin:sonarjs/recommended",
    "plugin:jest/recommended",
    "plugin:prettier/recommended"
  ],
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaFeatures": {
      "jsx": true
    },
    "ecmaVersion": 2018,
    "sourceType": "module",
    "project": "./tsconfig.json"
  },
  "rules": {
    // "max-len": ["error", { "code": 120 }],
    "import/extensions": 0,
    "no-use-before-define": "off", // https://typescript-eslint.io/rules/no-use-before-define/#how-to-use
    "@typescript-eslint/no-use-before-define": "warn", // https://typescript-eslint.io/rules/no-use-before-define/#how-to-use
    "react/jsx-filename-extension": [0],
    "prefer-regex-literals": [
      "error",
      {
        "disallowRedundantWrapping": true
      }
    ],
    "import/no-extraneous-dependencies": [
      "error",
      {
        "devDependencies": [
          "test.{js,ts,tsx}", // repos with a single test file
          "test-*.{js,ts,tsx}", // repos with multiple top-level test files
          "**/*{.,_}{test,spec}.{js,ts,tsx}", // tests where the extension or filename suffix denotes that it is a test
          "**/jest.config.ts", // jest config
          "**/jest.setup.ts", // jest setup,
          "src/test/util/test-utils.tsx"
        ],
        "optionalDependencies": false
      }
    ],
    "react/function-component-definition": [
      2,
      {
        "namedComponents": ["function-declaration", "function-expression", "arrow-function"],
        "unnamedComponents": ["function-expression", "arrow-function"]
      }
    ],
    "react/require-default-props": [
      "error",
      {
        "functions": "defaultArguments"
      }
    ]
  },
  "globals": {
    "process": true
  }
}
