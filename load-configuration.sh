#!/bin/bash
# this script is mainly for <PERSON> to download the file
# You can checkout https://bitbucket.org/fleetshipteam/paris2-configuration for local development
COMMIT_ID=$(curl -s --user $BITBUCKET_USER_NAME:$BITBUCKET_APP_PASSWORD "https://api.bitbucket.org/2.0/repositories/fleetshipteam/paris2-configuration/commits/$CONFIG_BRANCH?limit=1" | jq -r '.values[0].hash')
curl -s -S --user $BITBUCKET_USER_NAME:$BITBUCKET_APP_PASSWORD -L -O "https://api.bitbucket.org/2.0/repositories/fleetshipteam/paris2-configuration/src/${COMMIT_ID}/$ENV/paris2-configuration.json"
add_env()
{
  echo $1=$(jq -r ".$2" ./paris2-configuration.json) >> paris2-configuration.env
}

add_seafarer_env()
{
  echo $1=$2 >> paris2-configuration.env
}

echo '' > paris2-configuration.env

echo "debug msg: load env start"

# PARIS 1.0
add_env PARIS_ONE_HOST paris1.host

# web_app
add_env BASE_URL web_app.base_url
add_env S3_BUCKET web_app.website_s3_bucket
add_env SITE_NAME web_app.share_point_cms_site_name

# api
add_env SEAFARER_HOST api.base_urls.seafarer
add_env CREW_ASSIGNMENT_HOST api.base_urls.crew_assignment
add_env AUTH_SERVER_URL api.base_urls.auth
add_env KEYCLOAK_API_HOST api.base_urls.keycloak
add_env REFERENCE_HOST api.base_urls.reference
add_env VESSEL_HOST api.base_urls.vessel
add_env SHIP_PARTY_HOST api.base_urls.ship_party
add_env SEAFARER_SURVEY_HOST api.base_urls.survey
add_env TABLEAU_PROXY_HOST api.base_urls.tableau_proxy
add_env LMS_HOST api.base_urls.lms_host
add_env LMS_HOST_TOKEN api.base_urls.lms_host_token
add_env NOTIFICATION_HOST api.base_urls.notification
# Seafarer const
add_seafarer_env DOC_DOWNLOAD_FAIL_MESSAGE "This feature works ONLY on Production."

# Legacy env to avoid build errors (if any)
add_seafarer_env MEDIA_HOST "NOT_EXIST"

# debug log
echo "debug msg: load env end"
cat paris2-configuration.env