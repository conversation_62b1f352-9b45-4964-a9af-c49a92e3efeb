import type { StorybookConfig } from '@storybook/react-webpack5';
import webpackConfigSingleSpaReact from '../webpack.config.js';
const config: StorybookConfig = {
  stories: ['../src/**/*.mdx', '../src/**/*.stories.@(js|jsx|mjs|ts|tsx)'],
  addons: [
    '@storybook/addon-webpack5-compiler-swc',
    '@storybook/addon-onboarding',
    '@storybook/addon-links',
    '@storybook/addon-essentials',
    '@chromatic-com/storybook',
    '@storybook/addon-interactions',
  ],
  framework: {
    name: '@storybook/react-webpack5',
    options: {},
  },
  webpackFinal: async (config) => {
    const custom = webpackConfigSingleSpaReact(config);
   
    return {
      ...config,
      resolve: { ...config.resolve, plugins: [...(config.resolve?.plugins ?? []), ...custom.resolve.plugins]},
      module: {
        ...config.module,
        rules: [
          ...config.module.rules,
          {
            test: /\.s[ac]ss$/i,
            use: [
              // Creates `style` nodes from JS strings
              'style-loader',
              // Translates CSS into CommonJS
              'css-loader',
              // Compiles Sass to CSS
              'sass-loader',
            ],
          },
        ],
      },
      externals: ['single-spa', '@paris2/styleguide'],
    };
    return;
  },
};
export default config;
