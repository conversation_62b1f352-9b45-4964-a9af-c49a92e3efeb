const { defaults } = require('jest-config');
/** @type {import('jest').Config} */

module.exports = {
  rootDir: 'src',
  testEnvironment: 'jsdom',
  modulePaths: ['<rootDir>', 'src', 'test'],
  moduleFileExtensions: [...defaults.moduleFileExtensions, 'ts', 'tsx'],
  transform: {
    '^.+\\.vue$': 'babel-jest',
    '.+\\.(css|styl|less|sass|scss|png|jpg|ttf|woff|woff2|svg|gif)$': 'jest-transform-stub',
    '^.+\\.(js|jsx)?$': 'babel-jest',
    '^.+\\.(ts|tsx)?$': 'babel-jest',
  },
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/$1',
    '^@src/(.*)$': '<rootDir>/$1',
    "\\.(css)$": "identity-obj-proxy",
    '^uuid$': require.resolve('uuid'),
    '^@src/(.*)$': '<rootDir>/$1',
  },
  moduleDirectories: [
    "node_modules",
    "src"
  ],
  transformIgnorePatterns: [
    'node_modules',
    'navbar/node_modules/',
    "/node_modules/(?![@autofiy/autofiyable|@autofiy/property]).+\\.js$",
    "/node_modules/(?![@autofiy/autofiyable|@autofiy/property]).+\\.ts$",
    "/node_modules/(?![@autofiy/autofiyable|@autofiy/property]).+\\.tsx$"],
  setupFilesAfterEnv: ['<rootDir>/setupTests.js'],
  collectCoverageFrom: ['**/*.{js,jsx,ts,tsx}', '!**/node_modules/**', '!**/vendor/**'],
  coveragePathIgnorePatterns: [
    "src/test/resources",
    "src/types",
    "src/pages/mockResources"
  ]
};
